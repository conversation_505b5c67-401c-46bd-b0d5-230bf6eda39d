{"name": "ai-hub", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build"}, "dependencies": {"@heroicons/vue": "^2.2.0", "@pdftron/webviewer": "^11.3.0", "@vueuse/components": "^12.7.0", "@vueuse/core": "^12.7.0", "ai-hub": "file:", "dompurify": "^3.2.4", "fs-extra": "^11.2.0", "highlight.js": "^11.11.1", "marked": "^15.0.7", "naive-ui": "^2.41.0", "pinia": "^3.0.1", "sass": "^1.85.1", "vue": "^3.4.15", "vue-router": "^4.2.5"}, "devDependencies": {"@tsconfig/node22": "^22.0.0", "@types/node": "^22.13.4", "@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.7.0", "npm-run-all2": "^7.0.2", "typescript": "~5.7.3", "vite": "^6.1.0", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.2"}}