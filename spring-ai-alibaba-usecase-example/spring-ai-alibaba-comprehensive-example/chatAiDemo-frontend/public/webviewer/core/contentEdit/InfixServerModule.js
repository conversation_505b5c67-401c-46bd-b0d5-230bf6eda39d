(function(){/*
 The buffer module from node.js, for the browser.

 <AUTHOR> <http://feross.org>
 @license  MIT
*/
var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.arrayIteratorImpl=function(q){var m=0;return function(){return m<q.length?{done:!1,value:q[m++]}:{done:!0}}};$jscomp.arrayIterator=function(q){return{next:$jscomp.arrayIteratorImpl(q)}};$jscomp.makeIterator=function(q){var m="undefined"!=typeof Symbol&&Symbol.iterator&&q[Symbol.iterator];if(m)return m.call(q);if("number"==typeof q.length)return $jscomp.arrayIterator(q);throw Error(String(q)+" is not an iterable or ArrayLike");};$jscomp.ASSUME_ES5=!1;
$jscomp.ASSUME_NO_NATIVE_MAP=!1;$jscomp.ASSUME_NO_NATIVE_SET=!1;$jscomp.SIMPLE_FROUND_POLYFILL=!1;$jscomp.ISOLATE_POLYFILLS=!1;$jscomp.FORCE_POLYFILL_PROMISE=!1;$jscomp.FORCE_POLYFILL_PROMISE_WHEN_NO_UNHANDLED_REJECTION=!1;$jscomp.getGlobal=function(q){q=["object"==typeof globalThis&&globalThis,q,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var m=0;m<q.length;++m){var l=q[m];if(l&&l.Math==Math)return l}throw Error("Cannot find global object");};
$jscomp.global=$jscomp.getGlobal(this);$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(q,m,l){if(q==Array.prototype||q==Object.prototype)return q;q[m]=l.value;return q};$jscomp.IS_SYMBOL_NATIVE="function"===typeof Symbol&&"symbol"===typeof Symbol("x");$jscomp.TRUST_ES6_POLYFILLS=!$jscomp.ISOLATE_POLYFILLS||$jscomp.IS_SYMBOL_NATIVE;$jscomp.polyfills={};$jscomp.propertyToPolyfillSymbol={};$jscomp.POLYFILL_PREFIX="$jscp$";
var $jscomp$lookupPolyfilledValue=function(q,m,l){if(!l||null!=q){l=$jscomp.propertyToPolyfillSymbol[m];if(null==l)return q[m];l=q[l];return void 0!==l?l:q[m]}};$jscomp.polyfill=function(q,m,l,k){m&&($jscomp.ISOLATE_POLYFILLS?$jscomp.polyfillIsolated(q,m,l,k):$jscomp.polyfillUnisolated(q,m,l,k))};
$jscomp.polyfillUnisolated=function(q,m,l,k){l=$jscomp.global;q=q.split(".");for(k=0;k<q.length-1;k++){var g=q[k];if(!(g in l))return;l=l[g]}q=q[q.length-1];k=l[q];m=m(k);m!=k&&null!=m&&$jscomp.defineProperty(l,q,{configurable:!0,writable:!0,value:m})};
$jscomp.polyfillIsolated=function(q,m,l,k){var g=q.split(".");q=1===g.length;k=g[0];k=!q&&k in $jscomp.polyfills?$jscomp.polyfills:$jscomp.global;for(var p=0;p<g.length-1;p++){var d=g[p];if(!(d in k))return;k=k[d]}g=g[g.length-1];l=$jscomp.IS_SYMBOL_NATIVE&&"es6"===l?k[g]:null;m=m(l);null!=m&&(q?$jscomp.defineProperty($jscomp.polyfills,g,{configurable:!0,writable:!0,value:m}):m!==l&&(void 0===$jscomp.propertyToPolyfillSymbol[g]&&(l=1E9*Math.random()>>>0,$jscomp.propertyToPolyfillSymbol[g]=$jscomp.IS_SYMBOL_NATIVE?
$jscomp.global.Symbol(g):$jscomp.POLYFILL_PREFIX+l+"$"+g),$jscomp.defineProperty(k,$jscomp.propertyToPolyfillSymbol[g],{configurable:!0,writable:!0,value:m})))};
$jscomp.polyfill("Promise",function(q){function m(){this.batch_=null}function l(d){return d instanceof g?d:new g(function(h,r){h(d)})}if(q&&(!($jscomp.FORCE_POLYFILL_PROMISE||$jscomp.FORCE_POLYFILL_PROMISE_WHEN_NO_UNHANDLED_REJECTION&&"undefined"===typeof $jscomp.global.PromiseRejectionEvent)||!$jscomp.global.Promise||-1===$jscomp.global.Promise.toString().indexOf("[native code]")))return q;m.prototype.asyncExecute=function(d){if(null==this.batch_){this.batch_=[];var h=this;this.asyncExecuteFunction(function(){h.executeBatch_()})}this.batch_.push(d)};
var k=$jscomp.global.setTimeout;m.prototype.asyncExecuteFunction=function(d){k(d,0)};m.prototype.executeBatch_=function(){for(;this.batch_&&this.batch_.length;){var d=this.batch_;this.batch_=[];for(var h=0;h<d.length;++h){var r=d[h];d[h]=null;try{r()}catch(t){this.asyncThrow_(t)}}}this.batch_=null};m.prototype.asyncThrow_=function(d){this.asyncExecuteFunction(function(){throw d;})};var g=function(d){this.state_=0;this.result_=void 0;this.onSettledCallbacks_=[];this.isRejectionHandled_=!1;var h=this.createResolveAndReject_();
try{d(h.resolve,h.reject)}catch(r){h.reject(r)}};g.prototype.createResolveAndReject_=function(){function d(t){return function(u){r||(r=!0,t.call(h,u))}}var h=this,r=!1;return{resolve:d(this.resolveTo_),reject:d(this.reject_)}};g.prototype.resolveTo_=function(d){if(d===this)this.reject_(new TypeError("A Promise cannot resolve to itself"));else if(d instanceof g)this.settleSameAsPromise_(d);else{a:switch(typeof d){case "object":var h=null!=d;break a;case "function":h=!0;break a;default:h=!1}h?this.resolveToNonPromiseObj_(d):
this.fulfill_(d)}};g.prototype.resolveToNonPromiseObj_=function(d){var h=void 0;try{h=d.then}catch(r){this.reject_(r);return}"function"==typeof h?this.settleSameAsThenable_(h,d):this.fulfill_(d)};g.prototype.reject_=function(d){this.settle_(2,d)};g.prototype.fulfill_=function(d){this.settle_(1,d)};g.prototype.settle_=function(d,h){if(0!=this.state_)throw Error("Cannot settle("+d+", "+h+"): Promise already settled in state"+this.state_);this.state_=d;this.result_=h;2===this.state_&&this.scheduleUnhandledRejectionCheck_();
this.executeOnSettledCallbacks_()};g.prototype.scheduleUnhandledRejectionCheck_=function(){var d=this;k(function(){if(d.notifyUnhandledRejection_()){var h=$jscomp.global.console;"undefined"!==typeof h&&h.error(d.result_)}},1)};g.prototype.notifyUnhandledRejection_=function(){if(this.isRejectionHandled_)return!1;var d=$jscomp.global.CustomEvent,h=$jscomp.global.Event,r=$jscomp.global.dispatchEvent;if("undefined"===typeof r)return!0;"function"===typeof d?d=new d("unhandledrejection",{cancelable:!0}):
"function"===typeof h?d=new h("unhandledrejection",{cancelable:!0}):(d=$jscomp.global.document.createEvent("CustomEvent"),d.initCustomEvent("unhandledrejection",!1,!0,d));d.promise=this;d.reason=this.result_;return r(d)};g.prototype.executeOnSettledCallbacks_=function(){if(null!=this.onSettledCallbacks_){for(var d=0;d<this.onSettledCallbacks_.length;++d)p.asyncExecute(this.onSettledCallbacks_[d]);this.onSettledCallbacks_=null}};var p=new m;g.prototype.settleSameAsPromise_=function(d){var h=this.createResolveAndReject_();
d.callWhenSettled_(h.resolve,h.reject)};g.prototype.settleSameAsThenable_=function(d,h){var r=this.createResolveAndReject_();try{d.call(h,r.resolve,r.reject)}catch(t){r.reject(t)}};g.prototype.then=function(d,h){function r(x,C){return"function"==typeof x?function(A){try{t(x(A))}catch(z){u(z)}}:C}var t,u,y=new g(function(x,C){t=x;u=C});this.callWhenSettled_(r(d,t),r(h,u));return y};g.prototype.catch=function(d){return this.then(void 0,d)};g.prototype.callWhenSettled_=function(d,h){function r(){switch(t.state_){case 1:d(t.result_);
break;case 2:h(t.result_);break;default:throw Error("Unexpected state: "+t.state_);}}var t=this;null==this.onSettledCallbacks_?p.asyncExecute(r):this.onSettledCallbacks_.push(r);this.isRejectionHandled_=!0};g.resolve=l;g.reject=function(d){return new g(function(h,r){r(d)})};g.race=function(d){return new g(function(h,r){for(var t=$jscomp.makeIterator(d),u=t.next();!u.done;u=t.next())l(u.value).callWhenSettled_(h,r)})};g.all=function(d){var h=$jscomp.makeIterator(d),r=h.next();return r.done?l([]):new g(function(t,
u){function y(A){return function(z){x[A]=z;C--;0==C&&t(x)}}var x=[],C=0;do x.push(void 0),C++,l(r.value).callWhenSettled_(y(x.length-1),u),r=h.next();while(!r.done)})};return g},"es6","es3");$jscomp.checkStringArgs=function(q,m,l){if(null==q)throw new TypeError("The 'this' value for String.prototype."+l+" must not be null or undefined");if(m instanceof RegExp)throw new TypeError("First argument to String.prototype."+l+" must not be a regular expression");return q+""};
$jscomp.polyfill("String.prototype.endsWith",function(q){return q?q:function(m,l){var k=$jscomp.checkStringArgs(this,m,"endsWith");m+="";void 0===l&&(l=k.length);l=Math.max(0,Math.min(l|0,k.length));for(var g=m.length;0<g&&0<l;)if(k[--l]!=m[--g])return!1;return 0>=g}},"es6","es3");$jscomp.initSymbol=function(){};
$jscomp.polyfill("Symbol",function(q){if(q)return q;var m=function(p,d){this.$jscomp$symbol$id_=p;$jscomp.defineProperty(this,"description",{configurable:!0,writable:!0,value:d})};m.prototype.toString=function(){return this.$jscomp$symbol$id_};var l="jscomp_symbol_"+(1E9*Math.random()>>>0)+"_",k=0,g=function(p){if(this instanceof g)throw new TypeError("Symbol is not a constructor");return new m(l+(p||"")+"_"+k++,p)};return g},"es6","es3");
$jscomp.polyfill("Symbol.iterator",function(q){if(q)return q;q=Symbol("Symbol.iterator");for(var m="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),l=0;l<m.length;l++){var k=$jscomp.global[m[l]];"function"===typeof k&&"function"!=typeof k.prototype[q]&&$jscomp.defineProperty(k.prototype,q,{configurable:!0,writable:!0,value:function(){return $jscomp.iteratorPrototype($jscomp.arrayIteratorImpl(this))}})}return q},"es6",
"es3");$jscomp.iteratorPrototype=function(q){q={next:q};q[Symbol.iterator]=function(){return this};return q};$jscomp.checkEs6ConformanceViaProxy=function(){try{var q={},m=Object.create(new $jscomp.global.Proxy(q,{get:function(l,k,g){return l==q&&"q"==k&&g==m}}));return!0===m.q}catch(l){return!1}};$jscomp.USE_PROXY_FOR_ES6_CONFORMANCE_CHECKS=!1;$jscomp.ES6_CONFORMANCE=$jscomp.USE_PROXY_FOR_ES6_CONFORMANCE_CHECKS&&$jscomp.checkEs6ConformanceViaProxy();
$jscomp.owns=function(q,m){return Object.prototype.hasOwnProperty.call(q,m)};$jscomp.MapEntry=function(){};$jscomp.underscoreProtoCanBeSet=function(){var q={a:!0},m={};try{return m.__proto__=q,m.a}catch(l){}return!1};$jscomp.setPrototypeOf=$jscomp.TRUST_ES6_POLYFILLS&&"function"==typeof Object.setPrototypeOf?Object.setPrototypeOf:$jscomp.underscoreProtoCanBeSet()?function(q,m){q.__proto__=m;if(q.__proto__!==m)throw new TypeError(q+" is not extensible");return q}:null;
$jscomp.assign=$jscomp.TRUST_ES6_POLYFILLS&&"function"==typeof Object.assign?Object.assign:function(q,m){for(var l=1;l<arguments.length;l++){var k=arguments[l];if(k)for(var g in k)$jscomp.owns(k,g)&&(q[g]=k[g])}return q};$jscomp.polyfill("Array.prototype.fill",function(q){return q?q:function(m,l,k){var g=this.length||0;0>l&&(l=Math.max(0,g+l));if(null==k||k>g)k=g;k=Number(k);0>k&&(k=Math.max(0,g+k));for(l=Number(l||0);l<k;l++)this[l]=m;return this}},"es6","es3");
$jscomp.typedArrayFill=function(q){return q?q:Array.prototype.fill};$jscomp.polyfill("Int8Array.prototype.fill",$jscomp.typedArrayFill,"es6","es5");$jscomp.polyfill("Uint8Array.prototype.fill",$jscomp.typedArrayFill,"es6","es5");$jscomp.polyfill("Uint8ClampedArray.prototype.fill",$jscomp.typedArrayFill,"es6","es5");$jscomp.polyfill("Int16Array.prototype.fill",$jscomp.typedArrayFill,"es6","es5");$jscomp.polyfill("Uint16Array.prototype.fill",$jscomp.typedArrayFill,"es6","es5");
$jscomp.polyfill("Int32Array.prototype.fill",$jscomp.typedArrayFill,"es6","es5");$jscomp.polyfill("Uint32Array.prototype.fill",$jscomp.typedArrayFill,"es6","es5");$jscomp.polyfill("Float32Array.prototype.fill",$jscomp.typedArrayFill,"es6","es5");$jscomp.polyfill("Float64Array.prototype.fill",$jscomp.typedArrayFill,"es6","es5");
(function(q){function m(k){if(l[k])return l[k].exports;var g=l[k]={i:k,l:!1,exports:{}};q[k].call(g.exports,g,g.exports,m);g.l=!0;return g.exports}var l={};m.m=q;m.c=l;m.d=function(k,g,p){m.o(k,g)||Object.defineProperty(k,g,{enumerable:!0,get:p})};m.r=function(k){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(k,Symbol.toStringTag,{value:"Module"});Object.defineProperty(k,"__esModule",{value:!0})};m.t=function(k,g){g&1&&(k=m(k));if(g&8||g&4&&"object"===typeof k&&k&&k.__esModule)return k;
var p=Object.create(null);m.r(p);Object.defineProperty(p,"default",{enumerable:!0,value:k});if(g&2&&"string"!=typeof k)for(var d in k)m.d(p,d,function(h){return k[h]}.bind(null,d));return p};m.n=function(k){var g=k&&k.__esModule?function(){return k["default"]}:function(){return k};m.d(g,"a",g);return g};m.o=function(k,g){return Object.prototype.hasOwnProperty.call(k,g)};m.p="/core/contentEdit";return m(m.s=9)})([function(q,m,l){l.d(m,"b",function(){return g});l.d(m,"a",function(){return p});var k=
l(2),g=function(d,h){Object(k.a)("disableLogs")||(h?console.warn("".concat(d,": ").concat(h)):console.warn(d))},p=function(d,h){}},function(q,m,l){l.d(m,"a",function(){return C});l.d(m,"b",function(){return N});var k=l(6),g=l(0),p=l(4),d=l(3),h="undefined"===typeof window?self:window,r=h.importScripts,t=!1,u=function(E,D){t||(r("".concat(h.basePath,"decode.min.js")),t=!0);E=self.BrotliDecode(Object(d.b)(E));return D?E:Object(d.a)(E)},y=function(E,D){return Object(k.a)(void 0,void 0,Promise,function(){var v;
return Object(k.b)(this,function(G){switch(G.label){case 0:return t?[3,2]:[4,Object(p.a)("".concat(self.Core.getWorkerPath(),"external/decode.min.js"),"Failed to download decode.min.js",window)];case 1:G.sent(),t=!0,G.label=2;case 2:return v=self.BrotliDecode(Object(d.b)(E)),[2,D?v:Object(d.a)(v)]}})})};(function(){function E(){this.remainingDataArrays=[]}E.prototype.processRaw=function(D){return D};E.prototype.processBrotli=function(D){this.remainingDataArrays.push(D);return null};E.prototype.GetNextChunk=
function(D){this.decodeFunction||(this.decodeFunction=0===D[0]&&97===D[1]&&115===D[2]&&109===D[3]?this.processRaw:this.processBrotli);return this.decodeFunction(D)};E.prototype.End=function(){if(this.remainingDataArrays.length){for(var D=this.arrays,v=0,G=0;G<D.length;++G)v+=D[G].length;v=new Uint8Array(v);var K=0;for(G=0;G<D.length;++G){var L=D[G];v.set(L,K);K+=L.length}return u(v,!0)}return null};return E})();var x=function(E){var D=!E.shouldOutputArray,v=new XMLHttpRequest;v.open("GET",E.url,E.isAsync);
var G=D&&v.overrideMimeType;v.responseType=G?"text":"arraybuffer";G&&v.overrideMimeType("text/plain; charset=x-user-defined");v.send();var K=function(){var w=Date.now();var f=G?v.responseText:new Uint8Array(v.response);Object(g.a)("worker","Result length is ".concat(f.length));f.length<E.compressedMaximum?(f=E.decompressFunction(f,E.shouldOutputArray),Object(g.b)("There may be some degradation of performance. Your server has not been configured to serve .gz. and .br. files with the expected Content-Encoding. See https://docs.apryse.com/documentation/web/faq/content-encoding/ for instructions on how to resolve this."),
r&&Object(g.a)("worker","Decompressed length is ".concat(f.length))):D&&(f=Object(d.a)(f));r&&Object(g.a)("worker","".concat(E.url," Decompression took ").concat(Date.now()-w));return f};if(E.isAsync)var L=new Promise(function(w,f){v.onload=function(){200===this.status||0===this.status?w(K()):f("Download Failed ".concat(E.url))};v.onerror=function(){f("Network error occurred ".concat(E.url))}});else{if(200===v.status||0===v.status)return K();throw Error("Failed to load ".concat(E.url));}return L},
C=function(E){var D=E.lastIndexOf("/");-1===D&&(D=0);var v=E.slice(D).replace(".",".br.");r||(v.endsWith(".js.mem")?v=v.replace(".js.mem",".mem"):v.endsWith(".js")&&(v=v.concat(".mem")));return E.slice(0,D)+v},A=function(E,D){D.url=C(E);D.decompressFunction=r?u:y;return x(D)},z=function(E,D,v,G){return E.catch(function(K){Object(g.b)(K);return G(D,v)})},N=function(E,D,v,G){a:{var K=[A];D={compressedMaximum:D,isAsync:v,shouldOutputArray:G};if(D.isAsync){var L=K[0](E,D);for(v=1;v<K.length;++v)L=z(L,
E,D,K[v])}else{for(v=0;v<K.length;++v)try{L=K[v](E,D);break a}catch(w){Object(g.b)(w.message)}throw Error("");}}return L}},function(q,m,l){l.d(m,"a",function(){return p});l.d(m,"b",function(){return d});var k={},g={flattenedResources:!1,CANVAS_CACHE_SIZE:void 0,maxPagesBefore:void 0,maxPagesAhead:void 0,disableLogs:!1,wvsQueryParameters:{},_trnDebugMode:!1,_logFiltersEnabled:null},p=function(h){return g[h]},d=function(h,r){var t;g[h]=r;null===(t=k[h])||void 0===t?void 0:t.forEach(function(u){u(r)})}},
function(q,m,l){l.d(m,"b",function(){return k});l.d(m,"a",function(){return g});var k=function(p){if("string"===typeof p){for(var d=new Uint8Array(p.length),h=p.length,r=0;r<h;r++)d[r]=p.charCodeAt(r);return d}return p},g=function(p){if("string"!==typeof p){for(var d="",h=0,r=p.length,t;h<r;)t=p.subarray(h,h+1024),h+=1024,d+=String.fromCharCode.apply(null,t);return d}return p}},function(q,m,l){function k(p,d,h){return new Promise(function(r){if(!p)return r();var t=h.document.createElement("script");
t.type="text/javascript";t.onload=function(){r()};t.onerror=function(){d&&Object(g.b)(d);r()};t.src=p;h.document.getElementsByTagName("head")[0].appendChild(t)})}l.d(m,"a",function(){return k});var g=l(0)},function(q,m,l){function k(h,r,t){function u(C){x=x||Date.now();return C?(Object(g.a)("load","Try instantiateStreaming"),fetch(Object(p.a)(h)).then(function(A){return WebAssembly.instantiateStreaming(A,r)}).catch(function(A){Object(g.a)("load","instantiateStreaming Failed ".concat(h," message ").concat(A.message));
return u(!1)})):Object(p.b)(h,t,!0,!0).then(function(A){y=Date.now();Object(g.a)("load","Request took ".concat(y-x," ms"));return WebAssembly.instantiate(A,r)})}var y,x;return u(!!WebAssembly.instantiateStreaming).then(function(C){Object(g.a)("load","WASM compilation took ".concat(Date.now()-(y||x)," ms"));return C})}l.d(m,"a",function(){return k});var g=l(0),p=l(1),d=l(4);l.d(m,"b",function(){return d.a})},function(q,m,l){function k(p,d,h,r){function t(u){return u instanceof h?u:new h(function(y){y(u)})}
return new (h||(h=Promise))(function(u,y){function x(z){try{A(r.next(z))}catch(N){y(N)}}function C(z){try{A(r["throw"](z))}catch(N){y(N)}}function A(z){z.done?u(z.value):t(z.value).then(x,C)}A((r=r.apply(p,d||[])).next())})}function g(p,d){function h(A){return function(z){return r([A,z])}}function r(A){if(u)throw new TypeError("Generator is already executing.");for(;C&&(C=0,A[0]&&(t=0)),t;)try{if(u=1,y&&(x=A[0]&2?y["return"]:A[0]?y["throw"]||((x=y["return"])&&x.call(y),0):y.next)&&!(x=x.call(y,A[1])).done)return x;
if(y=0,x)A=[A[0]&2,x.value];switch(A[0]){case 0:case 1:x=A;break;case 4:return t.label++,{value:A[1],done:!1};case 5:t.label++;y=A[1];A=[0];continue;case 7:A=t.ops.pop();t.trys.pop();continue;default:if(!(x=t.trys,x=0<x.length&&x[x.length-1])&&(6===A[0]||2===A[0])){t=0;continue}if(3===A[0]&&(!x||A[1]>x[0]&&A[1]<x[3]))t.label=A[1];else if(6===A[0]&&t.label<x[1])t.label=x[1],x=A;else if(x&&t.label<x[2])t.label=x[2],t.ops.push(A);else{x[2]&&t.ops.pop();t.trys.pop();continue}}A=d.call(p,t)}catch(z){A=
[6,z],y=0}finally{u=x=0}if(A[0]&5)throw A[1];return{value:A[0]?A[1]:void 0,done:!0}}var t={label:0,sent:function(){if(x[0]&1)throw x[1];return x[1]},trys:[],ops:[]},u,y,x,C=Object.create(("function"===typeof Iterator?Iterator:Object).prototype);return C.next=h(0),C["throw"]=h(1),C["return"]=h(2),"function"===typeof Symbol&&(C[Symbol.iterator]=function(){return this}),C}l.d(m,"a",function(){return k});l.d(m,"b",function(){return g})},function(q,m,l){l.d(m,"a",function(){return h});var k=l(1),g=l(5),
p=l(8),d=function(){function r(t){var u=this;this.promise=t.then(function(y){u.response=y;u.status=200})}r.prototype.addEventListener=function(t,u){this.promise.then(u)};return r}(),h=function(r,t,u){if(Object(p.a)()&&!u){self.Module.instantiateWasm=function(x,C){return Object(g.a)("".concat(r,"Wasm.wasm"),x,t["Wasm.wasm"]).then(function(A){C(A.instance)})};if(t.disableObjectURLBlobs){importScripts("".concat(r,"Wasm.js"));return}u=Object(k.b)("".concat(r,"Wasm.js.mem"),t["Wasm.js.mem"],!1,!1)}else{if(t.disableObjectURLBlobs){importScripts("".concat((self.Module.asmjsPrefix?
self.Module.asmjsPrefix:"")+r,".js"));return}u=Object(k.b)("".concat((self.Module.asmjsPrefix?self.Module.asmjsPrefix:"")+r,".js.mem"),t[".js.mem"],!1);var y=Object(k.b)("".concat((self.Module.memoryInitializerPrefixURL?self.Module.memoryInitializerPrefixURL:"")+r,".mem"),t[".mem"],!0,!0);self.Module.memoryInitializerRequest=new d(y)}u=new Blob([u],{type:"application/javascript"});importScripts(URL.createObjectURL(u))}},function(q,m,l){l.d(m,"a",function(){return A});var k,g="undefined"===typeof window?
self:window;q=function(){var z=navigator.userAgent.toLowerCase();return(z=/(msie) ([\w.]+)/.exec(z)||/(trident)(?:.*? rv:([\w.]+)|)/.exec(z))?parseInt(z[2],10):z}();var p=function(){var z=g.navigator.userAgent.match(/OPR/),N=g.navigator.userAgent.match(/Maxthon/),E=g.navigator.userAgent.match(/Edge/);return g.navigator.userAgent.match(/Chrome\/(.*?) /)&&!z&&!N&&!E}();(function(){if(!p)return null;var z=g.navigator.userAgent.match(/Chrome\/([0-9]+)\./);return z?parseInt(z[1],10):z})();var d=!!navigator.userAgent.match(/Edge/i)||
navigator.userAgent.match(/Edg\/(.*?)/)&&g.navigator.userAgent.match(/Chrome\/(.*?) /);(function(){if(!d)return null;var z=g.navigator.userAgent.match(/Edg\/([0-9]+)\./);return z?parseInt(z[1],10):z})();m=/iPad|iPhone|iPod/.test(g.navigator.platform)||"MacIntel"===navigator.platform&&1<navigator.maxTouchPoints||/iPad|iPhone|iPod/.test(g.navigator.userAgent);var h=function(){var z=g.navigator.userAgent.match(/.*\/([0-9\.]+)\s(Safari|Mobile).*/i);return z?parseFloat(z[1]):z}(),r=/^((?!chrome|android).)*safari/i.test(g.navigator.userAgent)||
/^((?!chrome|android).)*$/.test(g.navigator.userAgent)&&m;r&&/^((?!chrome|android).)*safari/i.test(navigator.userAgent)&&parseInt(null===(k=navigator.userAgent.match(/Version\/(\d+)/))||void 0===k?void 0:k[1],10);var t=g.navigator.userAgent.match(/Firefox/);(function(){if(!t)return null;var z=g.navigator.userAgent.match(/Firefox\/([0-9]+)\./);return z?parseInt(z[1],10):z})();q||/Android|webOS|Touch|IEMobile|Silk/i.test(navigator.userAgent);navigator.userAgent.match(/(iPad|iPhone|iPod)/i);g.navigator.userAgent.indexOf("Android");
var u=/Mac OS X 10_13_6.*\(KHTML, like Gecko\)$/.test(g.navigator.userAgent),y=g.navigator.userAgent.match(/(iPad|iPhone).+\sOS\s((\d+)(_\d)*)/i)?14<=parseInt(g.navigator.userAgent.match(/(iPad|iPhone).+\sOS\s((\d+)(_\d)*)/i)[3],10):!1,x=!(!self.WebAssembly||!self.WebAssembly.validate),C=-1<g.navigator.userAgent.indexOf("Edge/16")||-1<g.navigator.userAgent.indexOf("MSAppHost"),A=function(){return x&&!C&&!(!y&&(r&&14>h||u))}},function(q,m,l){q.exports=l(10)},function(q,m,l){l.r(m);(function(k){function g(w){return w.split("<File>")[1].split("</File>")[0]}
function p(w,f,H){var F=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;w="<InfixServer>".concat(w,"</InfixServer>");null==F&&(F="importCommand"+H+".xml");FS.writeFile(F,w);L.ccall("wasmRunXML","number",["string","string"],[F,f]);FS.unlink(F)}function d(w){var f=1<arguments.length&&void 0!==arguments[1]?arguments[1]:!0,H=FS.readFile(w).buffer;f&&FS.unlink(w);H||console.warn("No buffer results found for: ".concat(w));return H}function h(w){1==E?postMessage({cmd:"isReady",taskId:w}):setTimeout(function(){return h(w)},
300)}function r(w,f){var H=w.galleyId,F=w.cmd,M=w.subCmd,Q=w.caretStart,O=w.caretEnd,S=w.resultsFile,R=w.commandXML,T=w.commandFile,V=w.pageNumber;w=w.taskId;if("editText"===F)var U=M;else if("performUndoRedo"===F||"transformTextBox"===F)U=F;p(R,S,0,T);F=d(S);postMessage({cmd:"editText",subCmd:U,caretStart:Q,caretEnd:O,galleyId:H,commandXML:!1===f?null:R,resultsXML:F,pageNumber:V,taskId:w},[F])}function t(w,f,H,F){p(w,H,0,f);f=d(H);F||(w=null);postMessage({cmd:"insertTextBox",commandXML:w,resultsXML:f},
[f])}function u(w,f){var H=w.galleyId,F=w.resultsFile,M=w.willTriggerTextContentUpdated,Q=w.pageNumber;p(w.commandXML,F,0,w.commandFile);F=d(F);postMessage({pageNumber:Q,cmd:f,resultsXML:F,galleyId:H,willTriggerTextContentUpdated:M,taskId:w.taskId},[F])}function y(w,f,H,F,M,Q){if(f){H=new Uint8Array(H);f="inputFile"+w+".pdf";FS.writeFile(f,H);F=new Uint8Array(F);var O=(new TextDecoder("utf-8")).decode(F);H="objects"+w+".xml";F="results"+w+".xml";O='\n  <Commands>\n    <Command Name="LoadPDF"><File>'.concat(f,
'</File></Command>\n    <Command Name="Page BBox"><StartPage>1</StartPage><EndPage>1</EndPage></Command>\n    ').concat(O?'<Command Name="AddTableBoxes">'.concat(O,"</Command>"):"",'\n    <Command Name="Edit Page">\n      <WebFontURL>').concat(K||"https://www.pdftron.com/webfonts/v2/","</WebFontURL>\n      <Output>").concat(H,"</Output>\n      <ImagesAndText/>\n      <IgnoreRotation>false</IgnoreRotation>\n      ").concat(N?"<ForceReflow/>":"","\n    </Command>\n  </Commands>");p(O,F,1);v=w;M&&(M=
d(f,!1),f=d(H),F=d(F),postMessage({cmd:"exportFile",pageNumber:w,exportPerformed:!0,pdfBuffer:M,objectXML:f,resultsXML:F,taskId:Q},[M,F]))}else postMessage({cmd:"exportFile",pageNumber:w,exportPerformed:!1})}function x(w){var f=w.pdfFile,H=w.tableData,F=w.subCmd,M=w.pageNumber,Q=w.commandXML,O=w.objectID,S=w.isText,R=w.isUpdatingRect,T=w.oid,V=w.canUndoRedo,U=w.outputFName;w=w.taskId;M!==v&&y(M,!0,f,H,!1);var a="results"+M+".xml";f=S?"transformTextBox":"transformObject";p(Q,a,M);H=d(U);a=d(a);postMessage({cmd:f,
pageNumber:M,pdfBuffer:H,resultsXML:a,id:O,isUpdatingRect:R,isText:S,commandXML:Q,subCmd:F,canUndoRedo:V,oid:T,outputFName:U,taskId:w},[H,a])}l.d(m,"extractFileNameFromCommand",function(){return g});var C=l(7),A=l(1),z="undefined"===typeof window?self:window;z.Core=z.Core||{};var N=!0,E=!1,D=null,v=-1,G=!1,K,L={noInitialRun:!0,onRuntimeInitialized:function(){E=!0},fetchSelf:function(){Object(C.a)("InfixServer",{"Wasm.wasm":1E8,"Wasm.js.mem":1E5,".js.mem":5E6,".mem":3E6,disableObjectURLBlobs:G},!!navigator.userAgent.match(/Edge/i))},
locateFile:function(w){return w},getPreloadedPackage:function(w,f){"InfixServerWasm.br.mem"==w&&(w="InfixServerWasm.mem");return Object(A.b)("".concat(D||"").concat(w),f,!1,!0).buffer}};self.Module=L;self.basePath="../external/";onmessage=function(w){var f=w.data;switch(f.cmd){case "isReady":D=f.resourcePath;L.fetchSelf();h(f.taskId);break;case "initialiseInfixServer":var H=f.l,F=f.taskId;L.callMain([""]);L.ccall("wasmInitInfixServer","number",["string","string","string"],["infixcore.cfg",H,"results.xml"]);
var M=d("results.xml");postMessage({cmd:"initialiseInfixServer",resultsXML:M,taskId:F},[M]);break;case "disableObjectURLBlobs":G=f.disableURLBlobs;break;case "loadAvailableFonts":var Q=f.commandXML,O=f.taskId;K=f.webFontURL;p(Q,"results0.xml",0);var S=d("results0.xml");postMessage({cmd:"loadAvailableFonts",resultsXML:S,taskId:O},[S]);break;case "exportFile":y(f.pageNumber,f.performExport,f.pdfFile,f.tableData,!0,f.taskId);break;case "applyTransformMatrix":var R=f.oid,T=f.pageNumber,V=f.taskId;p(f.commandXML,
"results"+T+".xml",T);postMessage({cmd:"applyTransformMatrix",pageNumber:T,id:R,taskId:V});break;case "transformObject":x(f);break;case "deleteObject":var U=f.pdfFile,a=f.pageNumber,b=f.objectID,c=f.tableData,e=f.isUndoRedo,n=f.isPageDeleted,B=f.taskId;a!==v&&y(a,!0,U,c,!1);var I="outputFile"+a+".pdf",J="results"+a+".xml";var P='<Commands><Command Name="DeleteObject">'+"<OID>".concat(b,"</OID></Command>");P+='<Command Name="SavePDF"><File>'.concat(I,"</File>");p(P+"</Command></Commands>",J,a);var W=
d(I),Z=d(J);postMessage({cmd:"deleteObject",pageNumber:a,pdfBuffer:W,resultsXML:Z,oid:b,isUndoRedo:e,isPageDeleted:n,taskId:B},[W,Z]);break;case "insertTextBox":t(f.commandXML,f.commandFile,f.resultsFile,!0);break;case "insertNewTextBox":var Bb=f.pdfFile,X=f.pageNumber,Cb=f.topVal,Db=f.leftVal,Eb=f.bottomVal,Fb=f.rightVal,Gb=f.font,Hb=f.fontSize,Ib=f.importData,Jb=f.content,Kb=f.canUndoRedo,Lb=f.taskId,Mb=(new TextEncoder).encode("").buffer;X!=v&&y(X,!0,Bb,Mb,!1);var qa="results"+X+".xml",ra="exported"+
X+".xml",sa="outputFile"+X+".pdf";var Y='<Commands><Command Name="Insert Text Box">'+"<Rect><Top>".concat(Cb,"</Top><Left>").concat(Db,"</Left>");Y+="<Bottom>".concat(Eb,"</Bottom><Right>").concat(Fb,"</Right></Rect>");Y+="<Size>".concat(Hb,"</Size><FontName>").concat(Gb,"</FontName>");var ta="editText"+X+".xml";FS.writeFile(ta,Ib);Y+="<File>".concat(ta,"</File><TransXML>coreTransXML.cfg</TransXML>");Y+="<ExportFile>".concat(ra,"</ExportFile><TransXML>coreTransXML.cfg</TransXML>");Y=Y+'<StartPage>1</StartPage><EndPage>LastPage</EndPage><AutoSubstitute/><AutoDeleteParas/><Fitting><Shrink><FontSize Min="0.65">true</FontSize><Leading>False</Leading></Shrink><Stretch><FontSize>False</FontSize><Leading>False</Leading></Stretch></Fitting><ResetLetterSpacing/><IgnoreFlightCheck/><MissingFont>Noto Sans Regular</MissingFont><SubstituteAllChars/><TargetLang>en</TargetLang></Command>'+
'<Command Name="SavePDF"><File>'.concat(sa,"</File></Command></Commands>");p(Y,qa,X);var ua=d(sa),va=d(qa),wa=d(ra);postMessage({cmd:"insertNewTextBox",pageNumber:X,pdfBuffer:ua,exportXML:wa,resultsXML:va,contentHTML:Jb,commandXML:Y,canUndoRedo:Kb,taskId:Lb},[ua,wa,va]);break;case "AlignContentBox":var Nb=f.pdfFile,fa=f.pageNumber,Ob=f.galleyId,Pb=f.tableData,ka=f.commandXML,Qb=f.taskId;fa!=v&&y(fa,!0,Nb,Pb,!1);var Rb=g(ka);p(ka,"results.xml",fa);var xa=d("results.xml"),ya=d(Rb);postMessage({cmd:"updateContentBox",
subCmd:"Set Para Attribs",pageNumber:fa,pdfBuffer:ya,resultsXML:xa,galleyId:Ob,commandXML:ka,taskId:Qb},[ya,xa]);break;case "RenderContentBox":var Sb=f.pdfFile,ha=f.pageNumber,Tb=f.galleyId,Ub=f.tableData,za=f.commandXML,Vb=f.taskId;ha!=v&&y(ha,!0,Sb,Ub,!1);var Wb=g(za);p(za,"results.xml",ha);var Aa=d(Wb),Ba=d("results.xml");postMessage({cmd:"renderContentBox",pageNumber:ha,pdfBuffer:Aa,resultsXML:Ba,galleyId:Tb,taskId:Vb},[Aa,Ba]);break;case "AlignParagraph":var Xb=f.pdfFile,ia=f.pageNumber,Yb=f.galleyId,
Zb=f.tableData,Ca=f.commandXML,$b=f.taskId;ia!=v&&y(ia,!0,Xb,Zb,!1);p(Ca,"results.xml",ia);var Da=d("results.xml");postMessage({cmd:"editText",subCmd:"Set Para Attribs",galleyId:Yb,commandXML:Ca,pageNumber:ia,taskId:$b,resultsXML:Da},[Da]);break;case "DecorateContentBox":var la=f.commandXML,ac=f.pdfFile,ja=f.pageNumber,Ea=f.galleyId,bc=f.tableData,cc=f.taskId,dc=f.subCmd;ja!=v&&y(ja,!0,ac,bc,!1);var ec=g(la);p(la,"results.xml",ja);var Fa=d("results.xml"),Ga=d(ec);postMessage({cmd:"updateContentBox",
pageNumber:ja,pdfBuffer:Ga,commandXML:la,resultsXML:Fa,subCmd:dc,id:Ea,galleyId:Ea,taskId:cc},[Ga,Fa]);break;case "insertImage":var fc=f.pdfFile,aa=f.pageNumber,ma=f.newImage,gc=f.canUndoRedo,hc=f.taskId,Ha=f.commandXML,ic=f.imageFileName,jc=f.outputFileName,kc=(new TextEncoder).encode("").buffer;aa!=v&&y(aa,!0,fc,kc,!1);var Ia="results"+aa+".xml";FS.writeFile(ic,k.from(ma));p(Ha,Ia,aa);var Ja=d(jc),Ka=d(Ia);postMessage({cmd:"insertImage",pageNumber:aa,pdfBuffer:Ja,resultsXML:Ka,commandXML:Ha,canUndoRedo:gc,
newImage:ma,taskId:hc},[Ja,Ka,ma]);break;case "runCommand":var lc=f.subCmd,La=f.resultsFile;p(f.commandXML,La,0,f.commandFile);var Ma=d(La);postMessage({cmd:"runCommand",subCmd:lc,resultsXML:Ma},[Ma]);break;case "renderEditGalley":var Na=f.resultsFile;p(f.commandXML,Na,0,f.commandFile);var Oa=d(Na),Pa=d(f.imageFName);postMessage({cmd:"renderEditGalley",resultsXML:Oa,imageData:Pa,galleyId:f.galleyId,taskId:f.taskId},[Oa,Pa]);break;case "renderFullPage":var Qa=f.resultsFile;p(f.commandXML,Qa,0,f.commandFile);
var Ra=d(Qa),Sa=d(f.imageFName);postMessage({cmd:"renderFullPage",resultsXML:Ra,imageData:Sa,outputWidth:f.width,outputHeight:f.height},[Ra,Sa]);break;case "textAttributes":var mc=f.id,nc=f.numChars,Ta=f.resultsFile,oc=f.taskId;p(f.commandXML,Ta,0,f.commandFile);var Ua=d(Ta);postMessage({cmd:"textAttributes",id:mc,numChars:nc,resultsXML:Ua,taskId:oc},[Ua]);break;case "editText":r(f,!0);break;case "editObject":var pc=f.subCmd,qc=f.oid,Va=f.resultsFile,Wa=f.commandXML;p(Wa,Va,0,f.commandFile);var Xa=
d(Va);postMessage({cmd:"editObject",subCmd:pc,oid:qc,commandXML:Wa,resultsXML:Xa},[Xa]);break;case "performUndoRedo":switch(f.subCmd){case "editText":r(f,!1);break;case "transformObject":f.subCmd="performUndoRedo";x(f);break;case "insertTextBoxRedo":var Ya=f.commandXML,ba=f.pageNumber,rc=f.taskId,Za="results"+ba+".xml",sc="exported"+ba+".xml",tc="outputFile"+ba+".pdf";p(Ya,Za,ba);var $a=d(tc),ab=d(Za),bb=d(sc);postMessage({cmd:"insertNewTextBox",subCmd:"performUndoRedo",pageNumber:ba,pdfBuffer:$a,
exportXML:bb,resultsXML:ab,commandXML:Ya,taskId:rc},[$a,bb,ab]);break;case "insertImageRedo":var cb=f.commandXML,ca=f.pageNumber,db=f.newImage,uc="outputFile"+ca+".pdf",eb="results"+ca+".xml",fb="imageFile"+ca+".jpg";FS.writeFile(fb,k.from(db));p(cb,eb,ca);var gb=d(uc),hb=d(eb);FS.unlink(fb);postMessage({cmd:"insertImage",pageNumber:ca,pdfBuffer:gb,resultsXML:hb,commandXML:cb,newImage:db},[gb,hb])}break;case "insertTextBoxRedo":t(f.commandXML,f.commandFile,f.resultsFile,!1);break;case "copyText":u(f,
"copyText");break;case "getUpdatedText":u(f,"getUpdatedText");break;case "dumpTextBox":var vc=f.galleyId,ib=f.resultsFile,wc=f.taskId;p(f.commandXML,ib,0,f.commandFile);var jb=d(ib);postMessage({cmd:"dumpTextBox",galleyId:vc,resultsXML:jb,taskId:wc},[jb]);break;case "transformTextBox":r(f,!1);break;case "savePDF":var kb=f.resultsFile,xc=f.pdfFileName,yc=new Uint8Array(f.pdfFile);FS.writeFile(f.pdfFileName,yc);p(f.commandXML,kb,0,f.commandFile);var lb=d(xc),mb=d(kb);postMessage({cmd:"savePDF",pdfBuffer:lb,
resultsXML:mb},[lb,mb]);break;case "loadPDF":var nb=f.resultsFile,zc=new Uint8Array(f.pdfFile);FS.writeFile(f.pdfFileName,zc);p(f.commandXML,nb,0,f.commandFile);var ob=d(nb);postMessage({cmd:"loadPDF",resultsXML:ob},[ob]);break;case "loadHyperlinkURL":var Ac=f.id,na=f.resultsFile,Bc=f.taskId;p(f.commandXML,na,0,f.commandFile);var pb=FS.readFile(na).buffer;FS.unlink(na);postMessage({id:Ac,cmd:"loadHyperlinkURL",resultsXML:pb,taskId:Bc},[pb]);break;case "setTypographyContentBox":var Cc=f.pdfFile,da=
f.pageNumber,qb=f.galleyId,Dc=f.subCmd,Ec=f.tableData,Fc=f.taskId,oa=f.commandXML;da!=v&&y(da,!0,Cc,Ec,!1);var rb="results"+da+".xml";p(oa,rb,da);var Gc=g(oa),sb=d(Gc),tb=d(rb);postMessage({cmd:"setTypographyContentBox",subCmd:Dc,pageNumber:da,pdfBuffer:sb,commandXML:oa,resultsXML:tb,id:qb,galleyId:qb,taskId:Fc},[sb,tb]);break;case "updateDocumentContent":var ea=f.pageNumber,Hc=f.galleyId,Ic=f.outputFileName,ub=f.commandXML,Jc=f.isSearchReplace,Kc=f.callbackMapId,Lc=f.pdfPage,Mc=f.tableArray,Nc=f.taskId;
ea!=v&&y(ea,!0,Lc,Mc,!1);var vb="results"+ea+".xml";p(ub,vb,ea);var wb=d(Ic),xb=d(vb);postMessage({cmd:"updateContentBox",pageNumber:ea,pdfBuffer:wb,commandXML:ub,resultsXML:xb,galleyId:Hc,callbackMapId:Kc,isSearchReplace:Jc,taskId:Nc},[wb,xb]);break;case "getInfixVersion":var Oc=f.taskId,yb=f.commandXML;p(yb,"results1.xml",1);var zb=d("results1.xml");postMessage({cmd:"getInfixVersion",commandXML:yb,resultsXML:zb,taskId:Oc},[zb]);break;case "reloadPage":var pa=f.pageNumber,Pc=new Uint8Array(f.pdfFile),
Ab="inputFile"+pa+".pdf";FS.writeFile(Ab,Pc);var Qc="objects"+pa+".xml",Rc="results"+pa+".xml",Sc='\n  <Commands>\n    <Command Name="LoadPDF"><File>'.concat(Ab,'</File></Command>\n    <Command Name="Page BBox"><StartPage>1</StartPage><EndPage>1</EndPage></Command>\n    <Command Name="Edit Page">\n    <Output>').concat(Qc,"</Output><ImagesOnly/>").concat(N?"<ForceReflow/>":"","</Command>\n  </Commands>");p(Sc,Rc,1);break;case "setTextReflow":N=f.textReflow;postMessage({taskId:f.taskId});break;case "getTextReflow":postMessage({taskId:f.taskId,
textReflow:N})}}}).call(this,l(11).Buffer)},function(q,m,l){(function(k){function g(){try{var a=new Uint8Array(1);a.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}};return 42===a.foo()&&"function"===typeof a.subarray&&0===a.subarray(1,1).byteLength}catch(b){return!1}}function p(a,b){if((d.TYPED_ARRAY_SUPPORT?2147483647:1073741823)<b)throw new RangeError("Invalid typed array length");d.TYPED_ARRAY_SUPPORT?(a=new Uint8Array(b),a.__proto__=d.prototype):(null===a&&(a=new d(b)),a.length=
b);return a}function d(a,b,c){if(!(d.TYPED_ARRAY_SUPPORT||this instanceof d))return new d(a,b,c);if("number"===typeof a){if("string"===typeof b)throw Error("If encoding is specified then the first argument must be a string");return t(this,a)}return h(this,a,b,c)}function h(a,b,c,e){if("number"===typeof b)throw new TypeError('"value" argument must not be a number');if("undefined"!==typeof ArrayBuffer&&b instanceof ArrayBuffer){b.byteLength;if(0>c||b.byteLength<c)throw new RangeError("'offset' is out of bounds");
if(b.byteLength<c+(e||0))throw new RangeError("'length' is out of bounds");b=void 0===c&&void 0===e?new Uint8Array(b):void 0===e?new Uint8Array(b,c):new Uint8Array(b,c,e);d.TYPED_ARRAY_SUPPORT?(a=b,a.__proto__=d.prototype):a=u(a,b);return a}if("string"===typeof b){e=a;a=c;if("string"!==typeof a||""===a)a="utf8";if(!d.isEncoding(a))throw new TypeError('"encoding" must be a valid string encoding');c=C(b,a)|0;e=p(e,c);b=e.write(b,a);b!==c&&(e=e.slice(0,b));return e}return y(a,b)}function r(a){if("number"!==
typeof a)throw new TypeError('"size" argument must be a number');if(0>a)throw new RangeError('"size" argument must not be negative');}function t(a,b){r(b);a=p(a,0>b?0:x(b)|0);if(!d.TYPED_ARRAY_SUPPORT)for(var c=0;c<b;++c)a[c]=0;return a}function u(a,b){var c=0>b.length?0:x(b.length)|0;a=p(a,c);for(var e=0;e<c;e+=1)a[e]=b[e]&255;return a}function y(a,b){if(d.isBuffer(b)){var c=x(b.length)|0;a=p(a,c);if(0===a.length)return a;b.copy(a,0,0,c);return a}if(b){if("undefined"!==typeof ArrayBuffer&&b.buffer instanceof
ArrayBuffer||"length"in b)return(c="number"!==typeof b.length)||(c=b.length,c=c!==c),c?p(a,0):u(a,b);if("Buffer"===b.type&&T(b.data))return u(a,b.data)}throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.");}function x(a){if(a>=(d.TYPED_ARRAY_SUPPORT?2147483647:1073741823))throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+(d.TYPED_ARRAY_SUPPORT?2147483647:1073741823).toString(16)+" bytes");return a|0}function C(a,b){if(d.isBuffer(a))return a.length;
if("undefined"!==typeof ArrayBuffer&&"function"===typeof ArrayBuffer.isView&&(ArrayBuffer.isView(a)||a instanceof ArrayBuffer))return a.byteLength;"string"!==typeof a&&(a=""+a);var c=a.length;if(0===c)return 0;for(var e=!1;;)switch(b){case "ascii":case "latin1":case "binary":return c;case "utf8":case "utf-8":case void 0:return F(a).length;case "ucs2":case "ucs-2":case "utf16le":case "utf-16le":return 2*c;case "hex":return c>>>1;case "base64":return Q(a).length;default:if(e)return F(a).length;b=(""+
b).toLowerCase();e=!0}}function A(a,b,c){var e=!1;if(void 0===b||0>b)b=0;if(b>this.length)return"";if(void 0===c||c>this.length)c=this.length;if(0>=c)return"";c>>>=0;b>>>=0;if(c<=b)return"";for(a||(a="utf8");;)switch(a){case "hex":a=b;b=c;c=this.length;if(!a||0>a)a=0;if(!b||0>b||b>c)b=c;e="";for(c=a;c<b;++c)a=e,e=this[c],e=16>e?"0"+e.toString(16):e.toString(16),e=a+e;return e;case "utf8":case "utf-8":return D(this,b,c);case "ascii":a="";for(c=Math.min(this.length,c);b<c;++b)a+=String.fromCharCode(this[b]&
127);return a;case "latin1":case "binary":a="";for(c=Math.min(this.length,c);b<c;++b)a+=String.fromCharCode(this[b]);return a;case "base64":return b=0===b&&c===this.length?S.fromByteArray(this):S.fromByteArray(this.slice(b,c)),b;case "ucs2":case "ucs-2":case "utf16le":case "utf-16le":b=this.slice(b,c);c="";for(a=0;a<b.length;a+=2)c+=String.fromCharCode(b[a]+256*b[a+1]);return c;default:if(e)throw new TypeError("Unknown encoding: "+a);a=(a+"").toLowerCase();e=!0}}function z(a,b,c){var e=a[b];a[b]=
a[c];a[c]=e}function N(a,b,c,e,n){if(0===a.length)return-1;"string"===typeof c?(e=c,c=0):2147483647<c?c=2147483647:-2147483648>c&&(c=-2147483648);c=+c;isNaN(c)&&(c=n?0:a.length-1);0>c&&(c=a.length+c);if(c>=a.length){if(n)return-1;c=a.length-1}else if(0>c)if(n)c=0;else return-1;"string"===typeof b&&(b=d.from(b,e));if(d.isBuffer(b))return 0===b.length?-1:E(a,b,c,e,n);if("number"===typeof b)return b&=255,d.TYPED_ARRAY_SUPPORT&&"function"===typeof Uint8Array.prototype.indexOf?n?Uint8Array.prototype.indexOf.call(a,
b,c):Uint8Array.prototype.lastIndexOf.call(a,b,c):E(a,[b],c,e,n);throw new TypeError("val must be string, number or Buffer");}function E(a,b,c,e,n){function B(W,Z){return 1===I?W[Z]:W.readUInt16BE(Z*I)}var I=1,J=a.length,P=b.length;if(void 0!==e&&(e=String(e).toLowerCase(),"ucs2"===e||"ucs-2"===e||"utf16le"===e||"utf-16le"===e)){if(2>a.length||2>b.length)return-1;I=2;J/=2;P/=2;c/=2}if(n)for(e=-1;c<J;c++)if(B(a,c)===B(b,-1===e?0:c-e)){if(-1===e&&(e=c),c-e+1===P)return e*I}else-1!==e&&(c-=c-e),e=-1;
else for(c+P>J&&(c=J-P);0<=c;c--){J=!0;for(e=0;e<P;e++)if(B(a,c+e)!==B(b,e)){J=!1;break}if(J)return c}return-1}function D(a,b,c){c=Math.min(a.length,c);for(var e=[];b<c;){var n=a[b],B=null,I=239<n?4:223<n?3:191<n?2:1;if(b+I<=c)switch(I){case 1:128>n&&(B=n);break;case 2:var J=a[b+1];128===(J&192)&&(n=(n&31)<<6|J&63,127<n&&(B=n));break;case 3:J=a[b+1];var P=a[b+2];128===(J&192)&&128===(P&192)&&(n=(n&15)<<12|(J&63)<<6|P&63,2047<n&&(55296>n||57343<n)&&(B=n));break;case 4:J=a[b+1];P=a[b+2];var W=a[b+3];
128===(J&192)&&128===(P&192)&&128===(W&192)&&(n=(n&15)<<18|(J&63)<<12|(P&63)<<6|W&63,65535<n&&1114112>n&&(B=n))}null===B?(B=65533,I=1):65535<B&&(B-=65536,e.push(B>>>10&1023|55296),B=56320|B&1023);e.push(B);b+=I}a=e.length;if(a<=V)e=String.fromCharCode.apply(String,e);else{c="";for(b=0;b<a;)c+=String.fromCharCode.apply(String,e.slice(b,b+=V));e=c}return e}function v(a,b,c){if(0!==a%1||0>a)throw new RangeError("offset is not uint");if(a+b>c)throw new RangeError("Trying to access beyond buffer length");
}function G(a,b,c,e,n,B){if(!d.isBuffer(a))throw new TypeError('"buffer" argument must be a Buffer instance');if(b>n||b<B)throw new RangeError('"value" argument is out of bounds');if(c+e>a.length)throw new RangeError("Index out of range");}function K(a,b,c,e){0>b&&(b=65535+b+1);for(var n=0,B=Math.min(a.length-c,2);n<B;++n)a[c+n]=(b&255<<8*(e?n:1-n))>>>8*(e?n:1-n)}function L(a,b,c,e){0>b&&(b=4294967295+b+1);for(var n=0,B=Math.min(a.length-c,4);n<B;++n)a[c+n]=b>>>8*(e?n:3-n)&255}function w(a,b,c,e,
n,B){if(c+e>a.length)throw new RangeError("Index out of range");if(0>c)throw new RangeError("Index out of range");}function f(a,b,c,e,n){n||w(a,b,c,4,3.4028234663852886E38,-3.4028234663852886E38);R.write(a,b,c,e,23,4);return c+4}function H(a,b,c,e,n){n||w(a,b,c,8,1.7976931348623157E308,-1.7976931348623157E308);R.write(a,b,c,e,52,8);return c+8}function F(a,b){b=b||Infinity;for(var c,e=a.length,n=null,B=[],I=0;I<e;++I){c=a.charCodeAt(I);if(55295<c&&57344>c){if(!n){if(56319<c){-1<(b-=3)&&B.push(239,
191,189);continue}else if(I+1===e){-1<(b-=3)&&B.push(239,191,189);continue}n=c;continue}if(56320>c){-1<(b-=3)&&B.push(239,191,189);n=c;continue}c=(n-55296<<10|c-56320)+65536}else n&&-1<(b-=3)&&B.push(239,191,189);n=null;if(128>c){if(0>--b)break;B.push(c)}else if(2048>c){if(0>(b-=2))break;B.push(c>>6|192,c&63|128)}else if(65536>c){if(0>(b-=3))break;B.push(c>>12|224,c>>6&63|128,c&63|128)}else if(1114112>c){if(0>(b-=4))break;B.push(c>>18|240,c>>12&63|128,c>>6&63|128,c&63|128)}else throw Error("Invalid code point");
}return B}function M(a){for(var b=[],c=0;c<a.length;++c)b.push(a.charCodeAt(c)&255);return b}function Q(a){var b=S,c=b.toByteArray;a=(a.trim?a.trim():a.replace(/^\s+|\s+$/g,"")).replace(U,"");if(2>a.length)a="";else for(;0!==a.length%4;)a+="=";return c.call(b,a)}function O(a,b,c,e){for(var n=0;n<e&&!(n+c>=b.length||n>=a.length);++n)b[n+c]=a[n];return n}var S=l(13),R=l(14),T=l(15);m.Buffer=d;m.SlowBuffer=function(a){+a!=a&&(a=0);return d.alloc(+a)};m.INSPECT_MAX_BYTES=50;d.TYPED_ARRAY_SUPPORT=void 0!==
k.TYPED_ARRAY_SUPPORT?k.TYPED_ARRAY_SUPPORT:g();m.kMaxLength=d.TYPED_ARRAY_SUPPORT?2147483647:1073741823;d.poolSize=8192;d._augment=function(a){a.__proto__=d.prototype;return a};d.from=function(a,b,c){return h(null,a,b,c)};d.TYPED_ARRAY_SUPPORT&&(d.prototype.__proto__=Uint8Array.prototype,d.__proto__=Uint8Array,"undefined"!==typeof Symbol&&Symbol.species&&d[Symbol.species]===d&&Object.defineProperty(d,Symbol.species,{value:null,configurable:!0}));d.alloc=function(a,b,c){r(a);a=0>=a?p(null,a):void 0!==
b?"string"===typeof c?p(null,a).fill(b,c):p(null,a).fill(b):p(null,a);return a};d.allocUnsafe=function(a){return t(null,a)};d.allocUnsafeSlow=function(a){return t(null,a)};d.isBuffer=function(a){return!(null==a||!a._isBuffer)};d.compare=function(a,b){if(!d.isBuffer(a)||!d.isBuffer(b))throw new TypeError("Arguments must be Buffers");if(a===b)return 0;for(var c=a.length,e=b.length,n=0,B=Math.min(c,e);n<B;++n)if(a[n]!==b[n]){c=a[n];e=b[n];break}return c<e?-1:e<c?1:0};d.isEncoding=function(a){switch(String(a).toLowerCase()){case "hex":case "utf8":case "utf-8":case "ascii":case "latin1":case "binary":case "base64":case "ucs2":case "ucs-2":case "utf16le":case "utf-16le":return!0;
default:return!1}};d.concat=function(a,b){if(!T(a))throw new TypeError('"list" argument must be an Array of Buffers');if(0===a.length)return d.alloc(0);var c;if(void 0===b)for(c=b=0;c<a.length;++c)b+=a[c].length;b=d.allocUnsafe(b);var e=0;for(c=0;c<a.length;++c){var n=a[c];if(!d.isBuffer(n))throw new TypeError('"list" argument must be an Array of Buffers');n.copy(b,e);e+=n.length}return b};d.byteLength=C;d.prototype._isBuffer=!0;d.prototype.swap16=function(){var a=this.length;if(0!==a%2)throw new RangeError("Buffer size must be a multiple of 16-bits");
for(var b=0;b<a;b+=2)z(this,b,b+1);return this};d.prototype.swap32=function(){var a=this.length;if(0!==a%4)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var b=0;b<a;b+=4)z(this,b,b+3),z(this,b+1,b+2);return this};d.prototype.swap64=function(){var a=this.length;if(0!==a%8)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var b=0;b<a;b+=8)z(this,b,b+7),z(this,b+1,b+6),z(this,b+2,b+5),z(this,b+3,b+4);return this};d.prototype.toString=function(){var a=this.length|
0;return 0===a?"":0===arguments.length?D(this,0,a):A.apply(this,arguments)};d.prototype.equals=function(a){if(!d.isBuffer(a))throw new TypeError("Argument must be a Buffer");return this===a?!0:0===d.compare(this,a)};d.prototype.inspect=function(){var a="",b=m.INSPECT_MAX_BYTES;0<this.length&&(a=this.toString("hex",0,b).match(/.{2}/g).join(" "),this.length>b&&(a+=" ... "));return"<Buffer "+a+">"};d.prototype.compare=function(a,b,c,e,n){if(!d.isBuffer(a))throw new TypeError("Argument must be a Buffer");
void 0===b&&(b=0);void 0===c&&(c=a?a.length:0);void 0===e&&(e=0);void 0===n&&(n=this.length);if(0>b||c>a.length||0>e||n>this.length)throw new RangeError("out of range index");if(e>=n&&b>=c)return 0;if(e>=n)return-1;if(b>=c)return 1;b>>>=0;c>>>=0;e>>>=0;n>>>=0;if(this===a)return 0;var B=n-e,I=c-b,J=Math.min(B,I);e=this.slice(e,n);a=a.slice(b,c);for(b=0;b<J;++b)if(e[b]!==a[b]){B=e[b];I=a[b];break}return B<I?-1:I<B?1:0};d.prototype.includes=function(a,b,c){return-1!==this.indexOf(a,b,c)};d.prototype.indexOf=
function(a,b,c){return N(this,a,b,c,!0)};d.prototype.lastIndexOf=function(a,b,c){return N(this,a,b,c,!1)};d.prototype.write=function(a,b,c,e){if(void 0===b)e="utf8",c=this.length,b=0;else if(void 0===c&&"string"===typeof b)e=b,c=this.length,b=0;else if(isFinite(b))b|=0,isFinite(c)?(c|=0,void 0===e&&(e="utf8")):(e=c,c=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var n=this.length-b;if(void 0===c||c>n)c=n;if(0<a.length&&(0>c||0>b)||b>this.length)throw new RangeError("Attempt to write outside buffer bounds");
e||(e="utf8");for(n=!1;;)switch(e){case "hex":a:{b=Number(b)||0;e=this.length-b;c?(c=Number(c),c>e&&(c=e)):c=e;e=a.length;if(0!==e%2)throw new TypeError("Invalid hex string");c>e/2&&(c=e/2);for(e=0;e<c;++e){n=parseInt(a.substr(2*e,2),16);if(isNaN(n)){a=e;break a}this[b+e]=n}a=e}return a;case "utf8":case "utf-8":return O(F(a,this.length-b),this,b,c);case "ascii":return O(M(a),this,b,c);case "latin1":case "binary":return O(M(a),this,b,c);case "base64":return O(Q(a),this,b,c);case "ucs2":case "ucs-2":case "utf16le":case "utf-16le":e=
a;n=this.length-b;for(var B=[],I=0;I<e.length&&!(0>(n-=2));++I){var J=e.charCodeAt(I);a=J>>8;J%=256;B.push(J);B.push(a)}return O(B,this,b,c);default:if(n)throw new TypeError("Unknown encoding: "+e);e=(""+e).toLowerCase();n=!0}};d.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var V=4096;d.prototype.slice=function(a,b){var c=this.length;a=~~a;b=void 0===b?c:~~b;0>a?(a+=c,0>a&&(a=0)):a>c&&(a=c);0>b?(b+=c,0>b&&(b=0)):b>c&&(b=c);b<a&&(b=a);if(d.TYPED_ARRAY_SUPPORT)b=
this.subarray(a,b),b.__proto__=d.prototype;else{c=b-a;b=new d(c,void 0);for(var e=0;e<c;++e)b[e]=this[e+a]}return b};d.prototype.readUIntLE=function(a,b,c){a|=0;b|=0;c||v(a,b,this.length);c=this[a];for(var e=1,n=0;++n<b&&(e*=256);)c+=this[a+n]*e;return c};d.prototype.readUIntBE=function(a,b,c){a|=0;b|=0;c||v(a,b,this.length);c=this[a+--b];for(var e=1;0<b&&(e*=256);)c+=this[a+--b]*e;return c};d.prototype.readUInt8=function(a,b){b||v(a,1,this.length);return this[a]};d.prototype.readUInt16LE=function(a,
b){b||v(a,2,this.length);return this[a]|this[a+1]<<8};d.prototype.readUInt16BE=function(a,b){b||v(a,2,this.length);return this[a]<<8|this[a+1]};d.prototype.readUInt32LE=function(a,b){b||v(a,4,this.length);return(this[a]|this[a+1]<<8|this[a+2]<<16)+16777216*this[a+3]};d.prototype.readUInt32BE=function(a,b){b||v(a,4,this.length);return 16777216*this[a]+(this[a+1]<<16|this[a+2]<<8|this[a+3])};d.prototype.readIntLE=function(a,b,c){a|=0;b|=0;c||v(a,b,this.length);c=this[a];for(var e=1,n=0;++n<b&&(e*=256);)c+=
this[a+n]*e;c>=128*e&&(c-=Math.pow(2,8*b));return c};d.prototype.readIntBE=function(a,b,c){a|=0;b|=0;c||v(a,b,this.length);c=b;for(var e=1,n=this[a+--c];0<c&&(e*=256);)n+=this[a+--c]*e;n>=128*e&&(n-=Math.pow(2,8*b));return n};d.prototype.readInt8=function(a,b){b||v(a,1,this.length);return this[a]&128?-1*(255-this[a]+1):this[a]};d.prototype.readInt16LE=function(a,b){b||v(a,2,this.length);a=this[a]|this[a+1]<<8;return a&32768?a|4294901760:a};d.prototype.readInt16BE=function(a,b){b||v(a,2,this.length);
a=this[a+1]|this[a]<<8;return a&32768?a|4294901760:a};d.prototype.readInt32LE=function(a,b){b||v(a,4,this.length);return this[a]|this[a+1]<<8|this[a+2]<<16|this[a+3]<<24};d.prototype.readInt32BE=function(a,b){b||v(a,4,this.length);return this[a]<<24|this[a+1]<<16|this[a+2]<<8|this[a+3]};d.prototype.readFloatLE=function(a,b){b||v(a,4,this.length);return R.read(this,a,!0,23,4)};d.prototype.readFloatBE=function(a,b){b||v(a,4,this.length);return R.read(this,a,!1,23,4)};d.prototype.readDoubleLE=function(a,
b){b||v(a,8,this.length);return R.read(this,a,!0,52,8)};d.prototype.readDoubleBE=function(a,b){b||v(a,8,this.length);return R.read(this,a,!1,52,8)};d.prototype.writeUIntLE=function(a,b,c,e){a=+a;b|=0;c|=0;e||G(this,a,b,c,Math.pow(2,8*c)-1,0);e=1;var n=0;for(this[b]=a&255;++n<c&&(e*=256);)this[b+n]=a/e&255;return b+c};d.prototype.writeUIntBE=function(a,b,c,e){a=+a;b|=0;c|=0;e||G(this,a,b,c,Math.pow(2,8*c)-1,0);e=c-1;var n=1;for(this[b+e]=a&255;0<=--e&&(n*=256);)this[b+e]=a/n&255;return b+c};d.prototype.writeUInt8=
function(a,b,c){a=+a;b|=0;c||G(this,a,b,1,255,0);d.TYPED_ARRAY_SUPPORT||(a=Math.floor(a));this[b]=a&255;return b+1};d.prototype.writeUInt16LE=function(a,b,c){a=+a;b|=0;c||G(this,a,b,2,65535,0);d.TYPED_ARRAY_SUPPORT?(this[b]=a&255,this[b+1]=a>>>8):K(this,a,b,!0);return b+2};d.prototype.writeUInt16BE=function(a,b,c){a=+a;b|=0;c||G(this,a,b,2,65535,0);d.TYPED_ARRAY_SUPPORT?(this[b]=a>>>8,this[b+1]=a&255):K(this,a,b,!1);return b+2};d.prototype.writeUInt32LE=function(a,b,c){a=+a;b|=0;c||G(this,a,b,4,4294967295,
0);d.TYPED_ARRAY_SUPPORT?(this[b+3]=a>>>24,this[b+2]=a>>>16,this[b+1]=a>>>8,this[b]=a&255):L(this,a,b,!0);return b+4};d.prototype.writeUInt32BE=function(a,b,c){a=+a;b|=0;c||G(this,a,b,4,4294967295,0);d.TYPED_ARRAY_SUPPORT?(this[b]=a>>>24,this[b+1]=a>>>16,this[b+2]=a>>>8,this[b+3]=a&255):L(this,a,b,!1);return b+4};d.prototype.writeIntLE=function(a,b,c,e){a=+a;b|=0;e||(e=Math.pow(2,8*c-1),G(this,a,b,c,e-1,-e));e=0;var n=1,B=0;for(this[b]=a&255;++e<c&&(n*=256);)0>a&&0===B&&0!==this[b+e-1]&&(B=1),this[b+
e]=(a/n>>0)-B&255;return b+c};d.prototype.writeIntBE=function(a,b,c,e){a=+a;b|=0;e||(e=Math.pow(2,8*c-1),G(this,a,b,c,e-1,-e));e=c-1;var n=1,B=0;for(this[b+e]=a&255;0<=--e&&(n*=256);)0>a&&0===B&&0!==this[b+e+1]&&(B=1),this[b+e]=(a/n>>0)-B&255;return b+c};d.prototype.writeInt8=function(a,b,c){a=+a;b|=0;c||G(this,a,b,1,127,-128);d.TYPED_ARRAY_SUPPORT||(a=Math.floor(a));0>a&&(a=255+a+1);this[b]=a&255;return b+1};d.prototype.writeInt16LE=function(a,b,c){a=+a;b|=0;c||G(this,a,b,2,32767,-32768);d.TYPED_ARRAY_SUPPORT?
(this[b]=a&255,this[b+1]=a>>>8):K(this,a,b,!0);return b+2};d.prototype.writeInt16BE=function(a,b,c){a=+a;b|=0;c||G(this,a,b,2,32767,-32768);d.TYPED_ARRAY_SUPPORT?(this[b]=a>>>8,this[b+1]=a&255):K(this,a,b,!1);return b+2};d.prototype.writeInt32LE=function(a,b,c){a=+a;b|=0;c||G(this,a,b,4,2147483647,-2147483648);d.TYPED_ARRAY_SUPPORT?(this[b]=a&255,this[b+1]=a>>>8,this[b+2]=a>>>16,this[b+3]=a>>>24):L(this,a,b,!0);return b+4};d.prototype.writeInt32BE=function(a,b,c){a=+a;b|=0;c||G(this,a,b,4,2147483647,
-2147483648);0>a&&(a=4294967295+a+1);d.TYPED_ARRAY_SUPPORT?(this[b]=a>>>24,this[b+1]=a>>>16,this[b+2]=a>>>8,this[b+3]=a&255):L(this,a,b,!1);return b+4};d.prototype.writeFloatLE=function(a,b,c){return f(this,a,b,!0,c)};d.prototype.writeFloatBE=function(a,b,c){return f(this,a,b,!1,c)};d.prototype.writeDoubleLE=function(a,b,c){return H(this,a,b,!0,c)};d.prototype.writeDoubleBE=function(a,b,c){return H(this,a,b,!1,c)};d.prototype.copy=function(a,b,c,e){c||(c=0);e||0===e||(e=this.length);b>=a.length&&
(b=a.length);b||(b=0);0<e&&e<c&&(e=c);if(e===c||0===a.length||0===this.length)return 0;if(0>b)throw new RangeError("targetStart out of bounds");if(0>c||c>=this.length)throw new RangeError("sourceStart out of bounds");if(0>e)throw new RangeError("sourceEnd out of bounds");e>this.length&&(e=this.length);a.length-b<e-c&&(e=a.length-b+c);var n=e-c;if(this===a&&c<b&&b<e)for(e=n-1;0<=e;--e)a[e+b]=this[e+c];else if(1E3>n||!d.TYPED_ARRAY_SUPPORT)for(e=0;e<n;++e)a[e+b]=this[e+c];else Uint8Array.prototype.set.call(a,
this.subarray(c,c+n),b);return n};d.prototype.fill=function(a,b,c,e){if("string"===typeof a){"string"===typeof b?(e=b,b=0,c=this.length):"string"===typeof c&&(e=c,c=this.length);if(1===a.length){var n=a.charCodeAt(0);256>n&&(a=n)}if(void 0!==e&&"string"!==typeof e)throw new TypeError("encoding must be a string");if("string"===typeof e&&!d.isEncoding(e))throw new TypeError("Unknown encoding: "+e);}else"number"===typeof a&&(a&=255);if(0>b||this.length<b||this.length<c)throw new RangeError("Out of range index");
if(c<=b)return this;b>>>=0;c=void 0===c?this.length:c>>>0;a||(a=0);if("number"===typeof a)for(e=b;e<c;++e)this[e]=a;else for(a=d.isBuffer(a)?a:F((new d(a,e)).toString()),n=a.length,e=0;e<c-b;++e)this[e+b]=a[e%n];return this};var U=/[^+\/0-9A-Za-z-_]/g}).call(this,l(12))},function(q,m){m=function(){return this}();try{m=m||(new Function("return this"))()}catch(l){"object"===typeof window&&(m=window)}q.exports=m},function(q,m,l){function k(h){var r=h.length;if(0<r%4)throw Error("Invalid string. Length must be a multiple of 4");
h=h.indexOf("=");-1===h&&(h=r);return[h,h===r?0:4-h%4]}m.byteLength=function(h){h=k(h);var r=h[1];return 3*(h[0]+r)/4-r};m.toByteArray=function(h){var r=k(h);var t=r[0];r=r[1];var u=new d(3*(t+r)/4-r),y=0,x=0<r?t-4:t,C;for(C=0;C<x;C+=4)t=p[h.charCodeAt(C)]<<18|p[h.charCodeAt(C+1)]<<12|p[h.charCodeAt(C+2)]<<6|p[h.charCodeAt(C+3)],u[y++]=t>>16&255,u[y++]=t>>8&255,u[y++]=t&255;2===r&&(t=p[h.charCodeAt(C)]<<2|p[h.charCodeAt(C+1)]>>4,u[y++]=t&255);1===r&&(t=p[h.charCodeAt(C)]<<10|p[h.charCodeAt(C+1)]<<
4|p[h.charCodeAt(C+2)]>>2,u[y++]=t>>8&255,u[y++]=t&255);return u};m.fromByteArray=function(h){for(var r=h.length,t=r%3,u=[],y=0,x=r-t;y<x;y+=16383){for(var C=u,A=C.push,z,N=h,E=y+16383>x?x:y+16383,D=[],v=y;v<E;v+=3)z=(N[v]<<16&16711680)+(N[v+1]<<8&65280)+(N[v+2]&255),D.push(g[z>>18&63]+g[z>>12&63]+g[z>>6&63]+g[z&63]);z=D.join("");A.call(C,z)}1===t?(h=h[r-1],u.push(g[h>>2]+g[h<<4&63]+"==")):2===t&&(h=(h[r-2]<<8)+h[r-1],u.push(g[h>>10]+g[h>>4&63]+g[h<<2&63]+"="));return u.join("")};var g=[],p=[],d=
"undefined"!==typeof Uint8Array?Uint8Array:Array;for(q=0;64>q;++q)g[q]="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"[q],p["ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charCodeAt(q)]=q;p[45]=62;p[95]=63},function(q,m){m.read=function(l,k,g,p,d){var h=8*d-p-1;var r=(1<<h)-1,t=r>>1,u=-7;d=g?d-1:0;var y=g?-1:1,x=l[k+d];d+=y;g=x&(1<<-u)-1;x>>=-u;for(u+=h;0<u;g=256*g+l[k+d],d+=y,u-=8);h=g&(1<<-u)-1;g>>=-u;for(u+=p;0<u;h=256*h+l[k+d],d+=y,u-=8);if(0===g)g=1-t;else{if(g===
r)return h?NaN:Infinity*(x?-1:1);h+=Math.pow(2,p);g-=t}return(x?-1:1)*h*Math.pow(2,g-p)};m.write=function(l,k,g,p,d,h){var r,t=8*h-d-1,u=(1<<t)-1,y=u>>1,x=23===d?Math.pow(2,-24)-Math.pow(2,-77):0;h=p?0:h-1;var C=p?1:-1,A=0>k||0===k&&0>1/k?1:0;k=Math.abs(k);isNaN(k)||Infinity===k?(k=isNaN(k)?1:0,p=u):(p=Math.floor(Math.log(k)/Math.LN2),1>k*(r=Math.pow(2,-p))&&(p--,r*=2),k=1<=p+y?k+x/r:k+x*Math.pow(2,1-y),2<=k*r&&(p++,r/=2),p+y>=u?(k=0,p=u):1<=p+y?(k=(k*r-1)*Math.pow(2,d),p+=y):(k=k*Math.pow(2,y-1)*
Math.pow(2,d),p=0));for(;8<=d;l[g+h]=k&255,h+=C,k/=256,d-=8);p=p<<d|k;for(t+=d;0<t;l[g+h]=p&255,h+=C,p/=256,t-=8);l[g+h-C]|=128*A}},function(q,m){var l={}.toString;q.exports=Array.isArray||function(k){return"[object Array]"==l.call(k)}}]);}).call(this || window)
