/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[9],{614:function(ya,ua,n){function oa(ha){ha.$a();ha.advance();var fa=ha.current.textContent;ha.Lb();return fa}function ma(ha){var fa=[];for(ha.$a();ha.advance();){var pa=ha.sb();"field"===pa?fa.push(String(ha.ka("name"))):Object(h.i)("unrecognised field list element: ".concat(pa))}ha.Lb();return fa}function na(ha,fa){return fa?"false"!==ha:"true"===ha}function ka(ha,fa){var pa=ha.sb();switch(pa){case "javascript":return{name:"JavaScript",
javascript:ha.current.textContent};case "uri":return{name:"URI",uri:ha.ka("uri")};case "goto":pa=null;ha.$a();if(ha.advance()){var la=ha.ka("fit");pa={page:ha.ka("page"),fit:la};if("0"===pa.page)Object(h.i)("null page encountered in dest");else switch(fa=fa(Number(pa.page)),la){case "Fit":case "FitB":break;case "FitH":case "FitBH":pa.top=fa.Ba({x:0,y:ha.ka("top")||0}).y;break;case "FitV":case "FitBV":pa.left=fa.Ba({x:ha.ka("left")||0,y:0}).x;break;case "FitR":la=fa.Ba({x:ha.ka("left")||0,y:ha.ka("top")||
0});fa=fa.Ba({x:ha.ka("right")||0,y:ha.ka("bottom")||0});fa=new r.d(la.x,la.y,fa.x,fa.y);pa.top=fa.y1;pa.left=fa.x1;pa.bottom=fa.y2;pa.right=fa.x2;break;case "XYZ":la=fa.Ba({x:ha.ka("left")||0,y:ha.ka("top")||0});pa.top=la.y;pa.left=la.x;pa.zoom=ha.ka("zoom")||0;break;default:Object(h.i)("unknown dest fit: ".concat(la))}pa={name:"GoTo",dest:pa}}else Object(h.i)("missing dest in GoTo action");ha.Lb();return pa;case "submit-form":pa={name:"SubmitForm",url:ha.ka("url"),format:ha.ka("format"),method:ha.ka("method")||
"POST",exclude:na(ha.ka("exclude"),!1)};fa=ha.ka("flags");pa.flags=fa?fa.split(" "):[];for(ha.$a();ha.advance();)switch(fa=ha.sb(),fa){case "fields":pa.fields=ma(ha);break;default:Object(h.i)("unrecognised submit-form child: ".concat(fa))}ha.Lb();return pa;case "reset-form":pa={name:"ResetForm",exclude:na(ha.ka("exclude"),!1)};for(ha.$a();ha.advance();)switch(fa=ha.sb(),fa){case "fields":pa.fields=ma(ha);break;default:Object(h.i)("unrecognised reset-form child: ".concat(fa))}ha.Lb();return pa;case "hide":pa=
{name:"Hide",hide:na(ha.ka("hide"),!0)};for(ha.$a();ha.advance();)switch(fa=ha.sb(),fa){case "fields":pa.fields=ma(ha);break;default:Object(h.i)("unrecognised hide child: ".concat(fa))}ha.Lb();return pa;case "named":return{name:"Named",action:ha.ka("name")};default:Object(h.i)("Encountered unexpected action type: ".concat(pa))}return null}function ia(ha,fa,pa){var la={};for(ha.$a();ha.advance();){var ja=ha.sb();switch(ja){case "action":ja=ha.ka("trigger");if(fa?-1!==fa.indexOf(ja):1){la[ja]=[];for(ha.$a();ha.advance();){var ra=
ka(ha,pa);Object(f.isNull)(ra)||la[ja].push(ra)}ha.Lb()}else Object(h.i)("encountered unexpected trigger on field: ".concat(ja));break;default:Object(h.i)("encountered unknown action child: ".concat(ja))}}ha.Lb();return la}function ba(ha){return new z.a(ha.ka("r")||0,ha.ka("g")||0,ha.ka("b")||0,ha.ka("a")||1)}function x(ha,fa){var pa=ha.ka("name"),la=ha.ka("type")||"Type1",ja=ha.ka("size"),ra=fa.Ba({x:0,y:0});ja=fa.Ba({x:Number(ja),y:0});fa=ra.x-ja.x;ra=ra.y-ja.y;pa={name:pa,type:la,size:Math.sqrt(fa*
fa+ra*ra)||0,strokeColor:[0,0,0],fillColor:[0,0,0]};for(ha.$a();ha.advance();)switch(la=ha.sb(),la){case "stroke-color":pa.strokeColor=ba(ha);break;case "fill-color":pa.fillColor=ba(ha);break;default:Object(h.i)("unrecognised font child: ".concat(la))}ha.Lb();return pa}function y(ha){var fa=[];for(ha.$a();ha.advance();){var pa=ha.sb();switch(pa){case "option":pa=fa;var la=pa.push;var ja=ha;ja={value:ja.ka("value"),displayValue:ja.ka("display-value")||void 0};la.call(pa,ja);break;default:Object(h.i)("unrecognised options child: ".concat(pa))}}ha.Lb();
return fa}function w(ha,fa){var pa=ha.ka("name"),la={type:ha.ka("type"),quadding:ha.ka("quadding")||"Left-justified",maxLen:ha.ka("max-len")||-1},ja=ha.ka("flags");Object(f.isString)(ja)&&(la.flags=ja.split(" "));for(ha.$a();ha.advance();)switch(ja=ha.sb(),ja){case "actions":la.actions=ia(ha,["C","F","K","V"],function(){return fa});break;case "default-value":la.defaultValue=oa(ha);break;case "font":la.font=x(ha,fa);break;case "options":la.options=y(ha);break;default:Object(h.i)("unknown field child: ".concat(ja))}ha.Lb();
return new window.da.Annotations.la.ya(pa,la)}function b(ha,fa){switch(ha.type){case "Tx":try{if(Object(ea.c)(ha.actions))return new a.a.DatePickerWidgetAnnotation(ha,fa)}catch(pa){Object(h.i)(pa)}return new a.a.TextWidgetAnnotation(ha,fa);case "Ch":return ha.flags.get(ca.WidgetFlags.COMBO)?new a.a.ChoiceWidgetAnnotation(ha,fa):new a.a.ListWidgetAnnotation(ha,fa);case "Btn":return ha.flags.get(ca.WidgetFlags.PUSH_BUTTON)?new a.a.PushButtonWidgetAnnotation(ha,fa):ha.flags.get(ca.WidgetFlags.RADIO)?
new a.a.RadioButtonWidgetAnnotation(ha,fa):new a.a.CheckButtonWidgetAnnotation(ha,fa);case "Sig":return new a.a.SignatureWidgetAnnotation(ha,fa);default:Object(h.i)("Unrecognised field type: ".concat(ha.type))}return null}function e(ha,fa,pa,la){var ja=[],ra={};ha.$a();var qa=[],sa={},ta=[];Object(aa.a)(function(){if(ha.advance()){var wa=ha.sb();switch(wa){case "calculation-order":qa="calculation-order"===ha.sb()?ma(ha):[];break;case "document-actions":sa=ia(ha,["Init","Open"],fa);break;case "pages":wa=
[];for(ha.$a();ha.advance();){var Ba=ha.sb();switch(Ba){case "page":Ba=wa;var Ca=Ba.push,Aa=ha,Ga=fa,Ea={number:Aa.ka("number")};for(Aa.$a();Aa.advance();){var Ma=Aa.sb();switch(Ma){case "actions":Ea.actions=ia(Aa,["O","C"],Ga);break;default:Object(h.i)("unrecognised page child: ".concat(Ma))}}Aa.Lb();Ca.call(Ba,Ea);break;default:Object(h.i)("unrecognised page child: ".concat(Ba))}}ha.Lb();ta=wa;break;case "field":Ba=w(ha,fa(1));ra[Ba.name]=Ba;break;case "widget":wa={border:{style:"Solid",width:1},
backgroundColor:[],fieldName:ha.ka("field"),page:ha.ka("page"),index:ha.ka("index")||0,rotation:ha.ka("rotation")||0,flags:[],isImporting:!0};(Ba=ha.ka("appearance"))&&(wa.appearance=Ba);(Ba=ha.ka("flags"))&&(wa.flags=Ba.split(" "));for(ha.$a();ha.advance();)switch(Ba=ha.sb(),Ba){case "rect":Ca=ha;Aa=fa(Number(wa.page));Ba=Aa.Ba({x:Ca.ka("x1")||0,y:Ca.ka("y1")||0});Ca=Aa.Ba({x:Ca.ka("x2")||0,y:Ca.ka("y2")||0});Ba=new r.d(Ba.x,Ba.y,Ca.x,Ca.y);Ba.normalize();wa.rect={x1:Ba.x1,y1:Ba.y1,x2:Ba.x2,y2:Ba.y2};
break;case "border":Ba=ha;Ca={style:Ba.ka("style")||"Solid",width:Ba.ka("width")||1,color:[0,0,0]};for(Ba.$a();Ba.advance();)switch(Aa=Ba.sb(),Aa){case "color":Ca.color=ba(Ba);break;default:Object(h.i)("unrecognised border child: ".concat(Aa))}Ba.Lb();wa.border=Ca;break;case "background-color":wa.backgroundColor=ba(ha);break;case "actions":wa.actions=ia(ha,"E X D U Fo Bl PO PC PV PI".split(" "),fa);break;case "appearances":Ba=ha;Ca=Object(ea.b)(wa,"appearances");for(Ba.$a();Ba.advance();)if(Aa=Ba.sb(),
"appearance"===Aa){Aa=Ba.ka("name");Ga=Object(ea.b)(Ca,Aa);Aa=Ba;for(Aa.$a();Aa.advance();)switch(Ea=Aa.sb(),Ea){case "Normal":Object(ea.b)(Ga,"Normal").data=Aa.current.textContent;break;default:Object(h.i)("unexpected appearance state: ",Ea)}Aa.Lb()}else Object(h.i)("unexpected appearances child: ".concat(Aa));Ba.Lb();break;case "extra":Ba=ha;Ca=fa;Aa={};for(Ba.$a();Ba.advance();)switch(Ga=Ba.sb(),Ga){case "font":Aa.font=x(Ba,Ca(1));break;default:Object(h.i)("unrecognised extra child: ".concat(Ga))}Ba.Lb();
Ba=Aa;Ba.font&&(wa.font=Ba.font);break;case "captions":Ca=ha;Ba={};(Aa=Ca.ka("Normal"))&&(Ba.Normal=Aa);(Aa=Ca.ka("Rollover"))&&(Ba.Rollover=Aa);(Ca=Ca.ka("Down"))&&(Ba.Down=Ca);wa.captions=Ba;break;default:Object(h.i)("unrecognised widget child: ".concat(Ba))}ha.Lb();(Ba=ra[wa.fieldName])?(wa=b(Ba,wa),ja.push(wa)):Object(h.i)("ignoring widget with no corresponding field data: ".concat(wa.fieldName));break;default:Object(h.i)("Unknown element encountered in PDFInfo: ".concat(wa))}return!0}return!1},
function(){ha.Lb();pa({calculationOrder:qa,widgets:ja,fields:ra,documentActions:sa,pages:ta,custom:[]})},la)}n.r(ua);n.d(ua,"parse",function(){return e});var h=n(3),f=n(1);n.n(f);var a=n(157),r=n(4),z=n(11),aa=n(25),ea=n(136),ca=n(16)}}]);}).call(this || window)
