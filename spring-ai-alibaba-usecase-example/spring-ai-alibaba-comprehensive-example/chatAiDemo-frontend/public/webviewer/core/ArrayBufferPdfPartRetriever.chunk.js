/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[2],{605:function(ya,ua,n){n.r(ua);ya=n(52);n=n(523);var oa=function(){function ma(na){this.buffer=na;this.fileSize=null===na||void 0===na?void 0:na.byteLength}ma.prototype.getFileData=function(na){na(new Uint8Array(this.buffer))};ma.prototype.getFile=function(){return Promise.resolve(null)};return ma}();Object(ya.a)(oa);Object(n.a)(oa);Object(n.b)(oa);ua["default"]=oa}}]);}).call(this || window)
