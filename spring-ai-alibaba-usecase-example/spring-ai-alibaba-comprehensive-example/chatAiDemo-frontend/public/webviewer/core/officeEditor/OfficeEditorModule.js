(function(){var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.arrayIteratorImpl=function(e){var c=0;return function(){return c<e.length?{done:!1,value:e[c++]}:{done:!0}}};$jscomp.arrayIterator=function(e){return{next:$jscomp.arrayIteratorImpl(e)}};$jscomp.ASSUME_ES5=!1;$jscomp.ASSUME_NO_NATIVE_MAP=!1;$jscomp.ASSUME_NO_NATIVE_SET=!1;$jscomp.SIMPLE_FROUND_POLYFILL=!1;$jscomp.ISOLATE_POLYFILLS=!1;$jscomp.FORCE_POLYFILL_PROMISE=!1;$jscomp.FORCE_POLYFILL_PROMISE_WHEN_NO_UNHANDLED_REJECTION=!1;
$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(e,c,d){if(e==Array.prototype||e==Object.prototype)return e;e[c]=d.value;return e};$jscomp.getGlobal=function(e){e=["object"==typeof globalThis&&globalThis,e,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var c=0;c<e.length;++c){var d=e[c];if(d&&d.Math==Math)return d}throw Error("Cannot find global object");};$jscomp.global=$jscomp.getGlobal(this);
$jscomp.IS_SYMBOL_NATIVE="function"===typeof Symbol&&"symbol"===typeof Symbol("x");$jscomp.TRUST_ES6_POLYFILLS=!$jscomp.ISOLATE_POLYFILLS||$jscomp.IS_SYMBOL_NATIVE;$jscomp.polyfills={};$jscomp.propertyToPolyfillSymbol={};$jscomp.POLYFILL_PREFIX="$jscp$";var $jscomp$lookupPolyfilledValue=function(e,c,d){if(!d||null!=e){d=$jscomp.propertyToPolyfillSymbol[c];if(null==d)return e[c];d=e[d];return void 0!==d?d:e[c]}};
$jscomp.polyfill=function(e,c,d,g){c&&($jscomp.ISOLATE_POLYFILLS?$jscomp.polyfillIsolated(e,c,d,g):$jscomp.polyfillUnisolated(e,c,d,g))};$jscomp.polyfillUnisolated=function(e,c,d,g){d=$jscomp.global;e=e.split(".");for(g=0;g<e.length-1;g++){var b=e[g];if(!(b in d))return;d=d[b]}e=e[e.length-1];g=d[e];c=c(g);c!=g&&null!=c&&$jscomp.defineProperty(d,e,{configurable:!0,writable:!0,value:c})};
$jscomp.polyfillIsolated=function(e,c,d,g){var b=e.split(".");e=1===b.length;g=b[0];g=!e&&g in $jscomp.polyfills?$jscomp.polyfills:$jscomp.global;for(var k=0;k<b.length-1;k++){var a=b[k];if(!(a in g))return;g=g[a]}b=b[b.length-1];d=$jscomp.IS_SYMBOL_NATIVE&&"es6"===d?g[b]:null;c=c(d);null!=c&&(e?$jscomp.defineProperty($jscomp.polyfills,b,{configurable:!0,writable:!0,value:c}):c!==d&&(void 0===$jscomp.propertyToPolyfillSymbol[b]&&(d=1E9*Math.random()>>>0,$jscomp.propertyToPolyfillSymbol[b]=$jscomp.IS_SYMBOL_NATIVE?
$jscomp.global.Symbol(b):$jscomp.POLYFILL_PREFIX+d+"$"+b),$jscomp.defineProperty(g,$jscomp.propertyToPolyfillSymbol[b],{configurable:!0,writable:!0,value:c})))};$jscomp.initSymbol=function(){};
$jscomp.polyfill("Symbol",function(e){if(e)return e;var c=function(k,a){this.$jscomp$symbol$id_=k;$jscomp.defineProperty(this,"description",{configurable:!0,writable:!0,value:a})};c.prototype.toString=function(){return this.$jscomp$symbol$id_};var d="jscomp_symbol_"+(1E9*Math.random()>>>0)+"_",g=0,b=function(k){if(this instanceof b)throw new TypeError("Symbol is not a constructor");return new c(d+(k||"")+"_"+g++,k)};return b},"es6","es3");
$jscomp.polyfill("Symbol.iterator",function(e){if(e)return e;e=Symbol("Symbol.iterator");for(var c="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),d=0;d<c.length;d++){var g=$jscomp.global[c[d]];"function"===typeof g&&"function"!=typeof g.prototype[e]&&$jscomp.defineProperty(g.prototype,e,{configurable:!0,writable:!0,value:function(){return $jscomp.iteratorPrototype($jscomp.arrayIteratorImpl(this))}})}return e},"es6",
"es3");$jscomp.iteratorPrototype=function(e){e={next:e};e[Symbol.iterator]=function(){return this};return e};$jscomp.checkEs6ConformanceViaProxy=function(){try{var e={},c=Object.create(new $jscomp.global.Proxy(e,{get:function(d,g,b){return d==e&&"q"==g&&b==c}}));return!0===c.q}catch(d){return!1}};$jscomp.USE_PROXY_FOR_ES6_CONFORMANCE_CHECKS=!1;$jscomp.ES6_CONFORMANCE=$jscomp.USE_PROXY_FOR_ES6_CONFORMANCE_CHECKS&&$jscomp.checkEs6ConformanceViaProxy();
$jscomp.makeIterator=function(e){var c="undefined"!=typeof Symbol&&Symbol.iterator&&e[Symbol.iterator];if(c)return c.call(e);if("number"==typeof e.length)return $jscomp.arrayIterator(e);throw Error(String(e)+" is not an iterable or ArrayLike");};$jscomp.owns=function(e,c){return Object.prototype.hasOwnProperty.call(e,c)};$jscomp.MapEntry=function(){};
$jscomp.polyfill("Promise",function(e){function c(){this.batch_=null}function d(a){return a instanceof b?a:new b(function(f,h){f(a)})}if(e&&(!($jscomp.FORCE_POLYFILL_PROMISE||$jscomp.FORCE_POLYFILL_PROMISE_WHEN_NO_UNHANDLED_REJECTION&&"undefined"===typeof $jscomp.global.PromiseRejectionEvent)||!$jscomp.global.Promise||-1===$jscomp.global.Promise.toString().indexOf("[native code]")))return e;c.prototype.asyncExecute=function(a){if(null==this.batch_){this.batch_=[];var f=this;this.asyncExecuteFunction(function(){f.executeBatch_()})}this.batch_.push(a)};
var g=$jscomp.global.setTimeout;c.prototype.asyncExecuteFunction=function(a){g(a,0)};c.prototype.executeBatch_=function(){for(;this.batch_&&this.batch_.length;){var a=this.batch_;this.batch_=[];for(var f=0;f<a.length;++f){var h=a[f];a[f]=null;try{h()}catch(l){this.asyncThrow_(l)}}}this.batch_=null};c.prototype.asyncThrow_=function(a){this.asyncExecuteFunction(function(){throw a;})};var b=function(a){this.state_=0;this.result_=void 0;this.onSettledCallbacks_=[];this.isRejectionHandled_=!1;var f=this.createResolveAndReject_();
try{a(f.resolve,f.reject)}catch(h){f.reject(h)}};b.prototype.createResolveAndReject_=function(){function a(l){return function(p){h||(h=!0,l.call(f,p))}}var f=this,h=!1;return{resolve:a(this.resolveTo_),reject:a(this.reject_)}};b.prototype.resolveTo_=function(a){if(a===this)this.reject_(new TypeError("A Promise cannot resolve to itself"));else if(a instanceof b)this.settleSameAsPromise_(a);else{a:switch(typeof a){case "object":var f=null!=a;break a;case "function":f=!0;break a;default:f=!1}f?this.resolveToNonPromiseObj_(a):
this.fulfill_(a)}};b.prototype.resolveToNonPromiseObj_=function(a){var f=void 0;try{f=a.then}catch(h){this.reject_(h);return}"function"==typeof f?this.settleSameAsThenable_(f,a):this.fulfill_(a)};b.prototype.reject_=function(a){this.settle_(2,a)};b.prototype.fulfill_=function(a){this.settle_(1,a)};b.prototype.settle_=function(a,f){if(0!=this.state_)throw Error("Cannot settle("+a+", "+f+"): Promise already settled in state"+this.state_);this.state_=a;this.result_=f;2===this.state_&&this.scheduleUnhandledRejectionCheck_();
this.executeOnSettledCallbacks_()};b.prototype.scheduleUnhandledRejectionCheck_=function(){var a=this;g(function(){if(a.notifyUnhandledRejection_()){var f=$jscomp.global.console;"undefined"!==typeof f&&f.error(a.result_)}},1)};b.prototype.notifyUnhandledRejection_=function(){if(this.isRejectionHandled_)return!1;var a=$jscomp.global.CustomEvent,f=$jscomp.global.Event,h=$jscomp.global.dispatchEvent;if("undefined"===typeof h)return!0;"function"===typeof a?a=new a("unhandledrejection",{cancelable:!0}):
"function"===typeof f?a=new f("unhandledrejection",{cancelable:!0}):(a=$jscomp.global.document.createEvent("CustomEvent"),a.initCustomEvent("unhandledrejection",!1,!0,a));a.promise=this;a.reason=this.result_;return h(a)};b.prototype.executeOnSettledCallbacks_=function(){if(null!=this.onSettledCallbacks_){for(var a=0;a<this.onSettledCallbacks_.length;++a)k.asyncExecute(this.onSettledCallbacks_[a]);this.onSettledCallbacks_=null}};var k=new c;b.prototype.settleSameAsPromise_=function(a){var f=this.createResolveAndReject_();
a.callWhenSettled_(f.resolve,f.reject)};b.prototype.settleSameAsThenable_=function(a,f){var h=this.createResolveAndReject_();try{a.call(f,h.resolve,h.reject)}catch(l){h.reject(l)}};b.prototype.then=function(a,f){function h(m,t){return"function"==typeof m?function(n){try{l(m(n))}catch(r){p(r)}}:t}var l,p,q=new b(function(m,t){l=m;p=t});this.callWhenSettled_(h(a,l),h(f,p));return q};b.prototype.catch=function(a){return this.then(void 0,a)};b.prototype.callWhenSettled_=function(a,f){function h(){switch(l.state_){case 1:a(l.result_);
break;case 2:f(l.result_);break;default:throw Error("Unexpected state: "+l.state_);}}var l=this;null==this.onSettledCallbacks_?k.asyncExecute(h):this.onSettledCallbacks_.push(h);this.isRejectionHandled_=!0};b.resolve=d;b.reject=function(a){return new b(function(f,h){h(a)})};b.race=function(a){return new b(function(f,h){for(var l=$jscomp.makeIterator(a),p=l.next();!p.done;p=l.next())d(p.value).callWhenSettled_(f,h)})};b.all=function(a){var f=$jscomp.makeIterator(a),h=f.next();return h.done?d([]):new b(function(l,
p){function q(n){return function(r){m[n]=r;t--;0==t&&l(m)}}var m=[],t=0;do m.push(void 0),t++,d(h.value).callWhenSettled_(q(m.length-1),p),h=f.next();while(!h.done)})};return b},"es6","es3");$jscomp.checkStringArgs=function(e,c,d){if(null==e)throw new TypeError("The 'this' value for String.prototype."+d+" must not be null or undefined");if(c instanceof RegExp)throw new TypeError("First argument to String.prototype."+d+" must not be a regular expression");return e+""};
$jscomp.polyfill("String.prototype.endsWith",function(e){return e?e:function(c,d){var g=$jscomp.checkStringArgs(this,c,"endsWith");c+="";void 0===d&&(d=g.length);d=Math.max(0,Math.min(d|0,g.length));for(var b=c.length;0<b&&0<d;)if(g[--d]!=c[--b])return!1;return 0>=b}},"es6","es3");$jscomp.underscoreProtoCanBeSet=function(){var e={a:!0},c={};try{return c.__proto__=e,c.a}catch(d){}return!1};
$jscomp.setPrototypeOf=$jscomp.TRUST_ES6_POLYFILLS&&"function"==typeof Object.setPrototypeOf?Object.setPrototypeOf:$jscomp.underscoreProtoCanBeSet()?function(e,c){e.__proto__=c;if(e.__proto__!==c)throw new TypeError(e+" is not extensible");return e}:null;$jscomp.assign=$jscomp.TRUST_ES6_POLYFILLS&&"function"==typeof Object.assign?Object.assign:function(e,c){for(var d=1;d<arguments.length;d++){var g=arguments[d];if(g)for(var b in g)$jscomp.owns(g,b)&&(e[b]=g[b])}return e};
$jscomp.polyfill("Array.from",function(e){return e?e:function(c,d,g){d=null!=d?d:function(f){return f};var b=[],k="undefined"!=typeof Symbol&&Symbol.iterator&&c[Symbol.iterator];if("function"==typeof k){c=k.call(c);for(var a=0;!(k=c.next()).done;)b.push(d.call(g,k.value,a++))}else for(k=c.length,a=0;a<k;a++)b.push(d.call(g,c[a],a));return b}},"es6","es3");
(function(e){function c(g){if(d[g])return d[g].exports;var b=d[g]={i:g,l:!1,exports:{}};e[g].call(b.exports,b,b.exports,c);b.l=!0;return b.exports}var d={};c.m=e;c.c=d;c.d=function(g,b,k){c.o(g,b)||Object.defineProperty(g,b,{enumerable:!0,get:k})};c.r=function(g){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(g,Symbol.toStringTag,{value:"Module"});Object.defineProperty(g,"__esModule",{value:!0})};c.t=function(g,b){b&1&&(g=c(g));if(b&8||b&4&&"object"===typeof g&&g&&g.__esModule)return g;
var k=Object.create(null);c.r(k);Object.defineProperty(k,"default",{enumerable:!0,value:g});if(b&2&&"string"!=typeof g)for(var a in g)c.d(k,a,function(f){return g[f]}.bind(null,a));return k};c.n=function(g){var b=g&&g.__esModule?function(){return g["default"]}:function(){return g};c.d(b,"a",b);return b};c.o=function(g,b){return Object.prototype.hasOwnProperty.call(g,b)};c.p="/core/officeEditor";return c(c.s=9)})([function(e,c,d){d.d(c,"b",function(){return b});d.d(c,"a",function(){return k});var g=
d(1),b=function(a,f){Object(g.a)("disableLogs")||(f?console.warn("".concat(a,": ").concat(f)):console.warn(a))},k=function(a,f){}},function(e,c,d){d.d(c,"a",function(){return k});d.d(c,"b",function(){return a});var g={},b={flattenedResources:!1,CANVAS_CACHE_SIZE:void 0,maxPagesBefore:void 0,maxPagesAhead:void 0,disableLogs:!1,wvsQueryParameters:{},_trnDebugMode:!1,_logFiltersEnabled:null},k=function(f){return b[f]},a=function(f,h){var l;b[f]=h;null===(l=g[f])||void 0===l?void 0:l.forEach(function(p){p(h)})}},
function(e,c,d){d.d(c,"a",function(){return t});d.d(c,"b",function(){return B});var g=d(6),b=d(0),k=d(4),a=d(3),f="undefined"===typeof window?self:window,h=f.importScripts,l=!1,p=function(w,v){l||(h("".concat(f.basePath,"decode.min.js")),l=!0);w=self.BrotliDecode(Object(a.b)(w));return v?w:Object(a.a)(w)},q=function(w,v){return Object(g.a)(void 0,void 0,Promise,function(){var u;return Object(g.b)(this,function(x){switch(x.label){case 0:return l?[3,2]:[4,Object(k.a)("".concat(self.Core.getWorkerPath(),
"external/decode.min.js"),"Failed to download decode.min.js",window)];case 1:x.sent(),l=!0,x.label=2;case 2:return u=self.BrotliDecode(Object(a.b)(w)),[2,v?u:Object(a.a)(u)]}})})};(function(){function w(){this.remainingDataArrays=[]}w.prototype.processRaw=function(v){return v};w.prototype.processBrotli=function(v){this.remainingDataArrays.push(v);return null};w.prototype.GetNextChunk=function(v){this.decodeFunction||(this.decodeFunction=0===v[0]&&97===v[1]&&115===v[2]&&109===v[3]?this.processRaw:
this.processBrotli);return this.decodeFunction(v)};w.prototype.End=function(){if(this.remainingDataArrays.length){for(var v=this.arrays,u=0,x=0;x<v.length;++x)u+=v[x].length;u=new Uint8Array(u);var y=0;for(x=0;x<v.length;++x){var A=v[x];u.set(A,y);y+=A.length}return p(u,!0)}return null};return w})();var m=function(w){var v=!w.shouldOutputArray,u=new XMLHttpRequest;u.open("GET",w.url,w.isAsync);var x=v&&u.overrideMimeType;u.responseType=x?"text":"arraybuffer";x&&u.overrideMimeType("text/plain; charset=x-user-defined");
u.send();var y=function(){var C=Date.now();var z=x?u.responseText:new Uint8Array(u.response);Object(b.a)("worker","Result length is ".concat(z.length));z.length<w.compressedMaximum?(z=w.decompressFunction(z,w.shouldOutputArray),Object(b.b)("There may be some degradation of performance. Your server has not been configured to serve .gz. and .br. files with the expected Content-Encoding. See https://docs.apryse.com/documentation/web/faq/content-encoding/ for instructions on how to resolve this."),h&&
Object(b.a)("worker","Decompressed length is ".concat(z.length))):v&&(z=Object(a.a)(z));h&&Object(b.a)("worker","".concat(w.url," Decompression took ").concat(Date.now()-C));return z};if(w.isAsync)var A=new Promise(function(C,z){u.onload=function(){200===this.status||0===this.status?C(y()):z("Download Failed ".concat(w.url))};u.onerror=function(){z("Network error occurred ".concat(w.url))}});else{if(200===u.status||0===u.status)return y();throw Error("Failed to load ".concat(w.url));}return A},t=
function(w){var v=w.lastIndexOf("/");-1===v&&(v=0);var u=w.slice(v).replace(".",".br.");h||(u.endsWith(".js.mem")?u=u.replace(".js.mem",".mem"):u.endsWith(".js")&&(u=u.concat(".mem")));return w.slice(0,v)+u},n=function(w,v){v.url=t(w);v.decompressFunction=h?p:q;return m(v)},r=function(w,v,u,x){return w.catch(function(y){Object(b.b)(y);return x(v,u)})},B=function(w,v,u,x){a:{var y=[n];v={compressedMaximum:v,isAsync:u,shouldOutputArray:x};if(v.isAsync){var A=y[0](w,v);for(u=1;u<y.length;++u)A=r(A,w,
v,y[u])}else{for(u=0;u<y.length;++u)try{A=y[u](w,v);break a}catch(C){Object(b.b)(C.message)}throw Error("");}}return A}},function(e,c,d){d.d(c,"b",function(){return g});d.d(c,"a",function(){return b});var g=function(k){if("string"===typeof k){for(var a=new Uint8Array(k.length),f=k.length,h=0;h<f;h++)a[h]=k.charCodeAt(h);return a}return k},b=function(k){if("string"!==typeof k){for(var a="",f=0,h=k.length,l;f<h;)l=k.subarray(f,f+1024),f+=1024,a+=String.fromCharCode.apply(null,l);return a}return k}},
function(e,c,d){function g(k,a,f){return new Promise(function(h){if(!k)return h();var l=f.document.createElement("script");l.type="text/javascript";l.onload=function(){h()};l.onerror=function(){a&&Object(b.b)(a);h()};l.src=k;f.document.getElementsByTagName("head")[0].appendChild(l)})}d.d(c,"a",function(){return g});var b=d(0)},function(e,c,d){function g(f,h,l){function p(t){m=m||Date.now();return t?(Object(b.a)("load","Try instantiateStreaming"),fetch(Object(k.a)(f)).then(function(n){return WebAssembly.instantiateStreaming(n,
h)}).catch(function(n){Object(b.a)("load","instantiateStreaming Failed ".concat(f," message ").concat(n.message));return p(!1)})):Object(k.b)(f,l,!0,!0).then(function(n){q=Date.now();Object(b.a)("load","Request took ".concat(q-m," ms"));return WebAssembly.instantiate(n,h)})}var q,m;return p(!!WebAssembly.instantiateStreaming).then(function(t){Object(b.a)("load","WASM compilation took ".concat(Date.now()-(q||m)," ms"));return t})}d.d(c,"a",function(){return g});var b=d(0),k=d(2),a=d(4);d.d(c,"b",function(){return a.a})},
function(e,c,d){function g(k,a,f,h){function l(p){return p instanceof f?p:new f(function(q){q(p)})}return new (f||(f=Promise))(function(p,q){function m(r){try{n(h.next(r))}catch(B){q(B)}}function t(r){try{n(h["throw"](r))}catch(B){q(B)}}function n(r){r.done?p(r.value):l(r.value).then(m,t)}n((h=h.apply(k,a||[])).next())})}function b(k,a){function f(n){return function(r){return h([n,r])}}function h(n){if(p)throw new TypeError("Generator is already executing.");for(;t&&(t=0,n[0]&&(l=0)),l;)try{if(p=
1,q&&(m=n[0]&2?q["return"]:n[0]?q["throw"]||((m=q["return"])&&m.call(q),0):q.next)&&!(m=m.call(q,n[1])).done)return m;if(q=0,m)n=[n[0]&2,m.value];switch(n[0]){case 0:case 1:m=n;break;case 4:return l.label++,{value:n[1],done:!1};case 5:l.label++;q=n[1];n=[0];continue;case 7:n=l.ops.pop();l.trys.pop();continue;default:if(!(m=l.trys,m=0<m.length&&m[m.length-1])&&(6===n[0]||2===n[0])){l=0;continue}if(3===n[0]&&(!m||n[1]>m[0]&&n[1]<m[3]))l.label=n[1];else if(6===n[0]&&l.label<m[1])l.label=m[1],m=n;else if(m&&
l.label<m[2])l.label=m[2],l.ops.push(n);else{m[2]&&l.ops.pop();l.trys.pop();continue}}n=a.call(k,l)}catch(r){n=[6,r],q=0}finally{p=m=0}if(n[0]&5)throw n[1];return{value:n[0]?n[1]:void 0,done:!0}}var l={label:0,sent:function(){if(m[0]&1)throw m[1];return m[1]},trys:[],ops:[]},p,q,m,t=Object.create(("function"===typeof Iterator?Iterator:Object).prototype);return t.next=f(0),t["throw"]=f(1),t["return"]=f(2),"function"===typeof Symbol&&(t[Symbol.iterator]=function(){return this}),t}d.d(c,"a",function(){return g});
d.d(c,"b",function(){return b})},function(e,c,d){d.d(c,"a",function(){return f});var g=d(2),b=d(5),k=d(8),a=function(){function h(l){var p=this;this.promise=l.then(function(q){p.response=q;p.status=200})}h.prototype.addEventListener=function(l,p){this.promise.then(p)};return h}(),f=function(h,l,p){if(Object(k.a)()&&!p){self.Module.instantiateWasm=function(m,t){return Object(b.a)("".concat(h,"Wasm.wasm"),m,l["Wasm.wasm"]).then(function(n){t(n.instance)})};if(l.disableObjectURLBlobs){importScripts("".concat(h,
"Wasm.js"));return}p=Object(g.b)("".concat(h,"Wasm.js.mem"),l["Wasm.js.mem"],!1,!1)}else{if(l.disableObjectURLBlobs){importScripts("".concat((self.Module.asmjsPrefix?self.Module.asmjsPrefix:"")+h,".js"));return}p=Object(g.b)("".concat((self.Module.asmjsPrefix?self.Module.asmjsPrefix:"")+h,".js.mem"),l[".js.mem"],!1);var q=Object(g.b)("".concat((self.Module.memoryInitializerPrefixURL?self.Module.memoryInitializerPrefixURL:"")+h,".mem"),l[".mem"],!0,!0);self.Module.memoryInitializerRequest=new a(q)}p=
new Blob([p],{type:"application/javascript"});importScripts(URL.createObjectURL(p))}},function(e,c,d){d.d(c,"a",function(){return n});var g,b="undefined"===typeof window?self:window;e=function(){var r=navigator.userAgent.toLowerCase();return(r=/(msie) ([\w.]+)/.exec(r)||/(trident)(?:.*? rv:([\w.]+)|)/.exec(r))?parseInt(r[2],10):r}();var k=function(){var r=b.navigator.userAgent.match(/OPR/),B=b.navigator.userAgent.match(/Maxthon/),w=b.navigator.userAgent.match(/Edge/);return b.navigator.userAgent.match(/Chrome\/(.*?) /)&&
!r&&!B&&!w}();(function(){if(!k)return null;var r=b.navigator.userAgent.match(/Chrome\/([0-9]+)\./);return r?parseInt(r[1],10):r})();var a=!!navigator.userAgent.match(/Edge/i)||navigator.userAgent.match(/Edg\/(.*?)/)&&b.navigator.userAgent.match(/Chrome\/(.*?) /);(function(){if(!a)return null;var r=b.navigator.userAgent.match(/Edg\/([0-9]+)\./);return r?parseInt(r[1],10):r})();c=/iPad|iPhone|iPod/.test(b.navigator.platform)||"MacIntel"===navigator.platform&&1<navigator.maxTouchPoints||/iPad|iPhone|iPod/.test(b.navigator.userAgent);
var f=function(){var r=b.navigator.userAgent.match(/.*\/([0-9\.]+)\s(Safari|Mobile).*/i);return r?parseFloat(r[1]):r}(),h=/^((?!chrome|android).)*safari/i.test(b.navigator.userAgent)||/^((?!chrome|android).)*$/.test(b.navigator.userAgent)&&c;h&&/^((?!chrome|android).)*safari/i.test(navigator.userAgent)&&parseInt(null===(g=navigator.userAgent.match(/Version\/(\d+)/))||void 0===g?void 0:g[1],10);var l=b.navigator.userAgent.match(/Firefox/);(function(){if(!l)return null;var r=b.navigator.userAgent.match(/Firefox\/([0-9]+)\./);
return r?parseInt(r[1],10):r})();e||/Android|webOS|Touch|IEMobile|Silk/i.test(navigator.userAgent);navigator.userAgent.match(/(iPad|iPhone|iPod)/i);b.navigator.userAgent.indexOf("Android");var p=/Mac OS X 10_13_6.*\(KHTML, like Gecko\)$/.test(b.navigator.userAgent),q=b.navigator.userAgent.match(/(iPad|iPhone).+\sOS\s((\d+)(_\d)*)/i)?14<=parseInt(b.navigator.userAgent.match(/(iPad|iPhone).+\sOS\s((\d+)(_\d)*)/i)[3],10):!1,m=!(!self.WebAssembly||!self.WebAssembly.validate),t=-1<b.navigator.userAgent.indexOf("Edge/16")||
-1<b.navigator.userAgent.indexOf("MSAppHost"),n=function(){return m&&!t&&!(!q&&(h&&14>f||p))}},function(e,c,d){e.exports=d(10)},function(e,c,d){function g(k,a){if(null==a||a>k.length)a=k.length;for(var f=0,h=Array(a);f<a;f++)h[f]=k[f];return h}d.r(c);e=d(7);var b={};location.search.slice(1).split("&").forEach(function(k){k=k.split("=");var a;var f=Array.isArray(k)?k:void 0;if(!f)a:{var h=null==k?null:"undefined"!=typeof Symbol&&k[Symbol.iterator]||k["@@iterator"];if(null!=h){var l,p,q=[],m=!0,t=!1;
try{for(l=(h=h.call(k)).next;!(m=(a=l.call(h)).done)&&(q.push(a.value),2!==q.length);m=!0);}catch(r){t=!0;var n=r}finally{try{if(!m&&null!=h["return"]&&(p=h["return"](),Object(p)!==p)){f=void 0;break a}}finally{if(t)throw n;}}f=q}else f=void 0}if(!(a=f))a:{if(k){if("string"===typeof k){a=g(k,2);break a}a=Object.prototype.toString.call(k).slice(8,-1);"Object"===a&&k.constructor&&(a=k.constructor.name);if("Map"===a||"Set"===a){a=Array.from(k);break a}if("Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)){a=
g(k,2);break a}}a=void 0}if(!(k=a))throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");b[k[0]]=k[1]});self.Module={};self.basePath=b.externalPath;self.workerCallbackId=null;self.runtimeInitialized=!1;(function(){function k(){if(self.runtimeInitialized)try{Module._on_idle()}catch(q){postMessage({action:"responseFromOnIdle",error:q.toString()})}setTimeout(k,200)}function a(){if(h&&self.runtimeInitialized){var q=
h;h=null;q.forEach(function(m){onmessage(m)})}}function f(){a();h&&setTimeout(f,100)}var h=null,l=null,p=0;setTimeout(k,200);onmessage=function(q){if(self.runtimeInitialized){a();var m=Module["_".concat(q.data.action)];if(!m)throw Error("invalid worker function to call: ".concat(q.data.funcName));var t=q.data.data;if(t){t.byteLength||(t=new Uint8Array(t));if(!l||p<t.length)l&&Module._free(l),p=t.length,l=Module._malloc(t.length);Module.HEAP8.set(t,l)}self.workerCallbackId=q.data.callbackId;try{t?
m(l,t.length):m(0,0)}catch(n){postMessage({callbackId:self.workerCallbackId,data:{error:n.toString()}})}}else h||(h=[],setTimeout(f,100)),h.push(q)}})();Object(e.a)("OfficeEditorWorker",{"Wasm.wasm":1E8,"Wasm.js.mem":1E5,".js.mem":5E6,".mem":3E6},!!navigator.userAgent.match(/Edge/i))}]);}).call(this || window)
