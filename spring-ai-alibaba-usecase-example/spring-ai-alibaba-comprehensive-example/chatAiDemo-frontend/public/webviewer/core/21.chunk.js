/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[21],{619:function(ya,ua,n){n.r(ua);var oa=n(0),ma=n(7),na=n(3);ya=n(52);var ka=n(35),ia=n(17);n=function(){function ba(){this.init()}ba.prototype.init=function(){this.Xqa=!1;this.Tg=this.up=this.connection=null;this.Dl={};this.ga=this.bP=null};ba.prototype.VO=function(x){for(var y=this,w=0;w<x.length;++w){var b=x[w];switch(b.at){case "create":this.Dl[b.author]||(this.Dl[b.author]=b.aName);this.ZBa(b);break;case "modify":this.ga.Ls(b.xfdf).then(function(e){y.ga.Eb(e[0])});
break;case "delete":b="<delete><id>".concat(b.aId,"</id></delete>"),this.ga.Ls(b)}}};ba.prototype.ZBa=function(x){var y=this;this.ga.Ls(x.xfdf).then(function(w){w=w[0];w.authorId=x.author;y.ga.Eb(w);y.ga.trigger(ma.d.UPDATE_ANNOTATION_PERMISSION,[w])})};ba.prototype.YAa=function(x,y,w){this.up&&this.up(x,y,w)};ba.prototype.preloadAnnotations=function(x){this.addEventListener("webViewerServerAnnotationsEnabled",this.YAa.bind(this,x,"add",{imported:!1}),{once:!0})};ba.prototype.initiateCollaboration=
function(x,y,w){var b=this;if(x){b.Tg=y;b.ga=w.ia();w.addEventListener(ma.i.DOCUMENT_UNLOADED,function(){b.disableCollaboration()});b.zQa(x);var e=new XMLHttpRequest;e.addEventListener("load",function(){if(200===e.status&&0<e.responseText.length)try{var h=JSON.parse(e.responseText);b.connection=exports.da.XRa(Object(ka.k)(b.Tg,"blackbox/"),"annot");b.bP=h.id;b.Dl[h.id]=h.user_name;b.ga.rZ(h.id);b.connection.CVa(function(f){f.t&&f.t.startsWith("a_")&&f.data&&b.VO(f.data)},function(){b.connection.send({t:"a_retrieve",
dId:x});b.trigger(ba.Events.WEBVIEWER_SERVER_ANNOTATIONS_ENABLED,[b.Dl[h.id],b.bP])},function(){b.disableCollaboration()})}catch(f){Object(na.f)(f.message)}});e.open("GET",Object(ka.k)(this.Tg,"demo/SessionInfo.jsp"));e.withCredentials=!0;e.send();b.Xqa=!0;b.ga.ffa(function(h){return b.Dl[h.Author]||h.Author})}else Object(na.f)("Document ID required for collaboration")};ba.prototype.disableCollaboration=function(){this.up&&(this.ga.removeEventListener(ia.a.Events.ANNOTATION_CHANGED,this.up),this.up=
null);this.connection&&this.connection.Vu();this.ga&&this.ga.rZ("Guest");this.init();this.trigger(ba.Events.WEBVIEWER_SERVER_ANNOTATIONS_DISABLED)};ba.prototype.zQa=function(x){var y=this;this.up&&this.ga.removeEventListener(ia.a.Events.ANNOTATION_CHANGED,this.up);this.up=function(w,b,e){return Object(oa.b)(this,void 0,void 0,function(){var h,f,a,r,z,aa,ea,ca,ha;return Object(oa.d)(this,function(fa){switch(fa.label){case 0:if(e.imported)return[2];h={t:"a_".concat(b),dId:x,annots:[]};return[4,y.ga.o7()];
case 1:f=fa.aa();"delete"!==b&&(a=(new DOMParser).parseFromString(f,"text/xml"),r=new XMLSerializer);for(z=0;z<w.length;z++)aa=w[z],ca=ea=void 0,"add"===b?(ea=a.querySelector('[name="'.concat(aa.Id,'"]')),ca=r.serializeToString(ea),ha=null,aa.InReplyTo&&(ha=y.ga.Ci(aa.InReplyTo).authorId||"default"),h.annots.push({at:"create",aId:aa.Id,author:y.bP,aName:y.Dl[y.bP],parent:ha,xfdf:"<add>".concat(ca,"</add>")})):"modify"===b?(ea=a.querySelector('[name="'.concat(aa.Id,'"]')),ca=r.serializeToString(ea),
h.annots.push({at:"modify",aId:aa.Id,xfdf:"<modify>".concat(ca,"</modify>")})):"delete"===b&&h.annots.push({at:"delete",aId:aa.Id});0<h.annots.length&&y.connection.send(h);return[2]}})})}.bind(y);this.ga.addEventListener(ia.a.Events.ANNOTATION_CHANGED,this.up)};ba.Events={WEBVIEWER_SERVER_ANNOTATIONS_ENABLED:"webViewerServerAnnotationsEnabled",WEBVIEWER_SERVER_ANNOTATIONS_DISABLED:"webViewerServerAnnotationsDisabled"};return ba}();Object(ya.a)(n);ua["default"]=n}}]);}).call(this || window)
