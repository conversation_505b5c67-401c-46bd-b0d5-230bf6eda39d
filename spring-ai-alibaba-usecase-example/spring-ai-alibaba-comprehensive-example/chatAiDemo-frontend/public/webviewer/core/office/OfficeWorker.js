(function(){var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.arrayIteratorImpl=function(c){var d=0;return function(){return d<c.length?{done:!1,value:c[d++]}:{done:!0}}};$jscomp.arrayIterator=function(c){return{next:$jscomp.arrayIteratorImpl(c)}};$jscomp.ASSUME_ES5=!1;$jscomp.ASSUME_NO_NATIVE_MAP=!1;$jscomp.ASSUME_NO_NATIVE_SET=!1;$jscomp.SIMPLE_FROUND_POLYFILL=!1;$jscomp.ISOLATE_POLYFILLS=!1;$jscomp.FORCE_POLYFILL_PROMISE=!1;$jscomp.FORCE_POLYFILL_PROMISE_WHEN_NO_UNHANDLED_REJECTION=!1;
$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(c,d,f){if(c==Array.prototype||c==Object.prototype)return c;c[d]=f.value;return c};$jscomp.getGlobal=function(c){c=["object"==typeof globalThis&&globalThis,c,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var d=0;d<c.length;++d){var f=c[d];if(f&&f.Math==Math)return f}throw Error("Cannot find global object");};$jscomp.global=$jscomp.getGlobal(this);
$jscomp.IS_SYMBOL_NATIVE="function"===typeof Symbol&&"symbol"===typeof Symbol("x");$jscomp.TRUST_ES6_POLYFILLS=!$jscomp.ISOLATE_POLYFILLS||$jscomp.IS_SYMBOL_NATIVE;$jscomp.polyfills={};$jscomp.propertyToPolyfillSymbol={};$jscomp.POLYFILL_PREFIX="$jscp$";var $jscomp$lookupPolyfilledValue=function(c,d,f){if(!f||null!=c){f=$jscomp.propertyToPolyfillSymbol[d];if(null==f)return c[d];f=c[f];return void 0!==f?f:c[d]}};
$jscomp.polyfill=function(c,d,f,e){d&&($jscomp.ISOLATE_POLYFILLS?$jscomp.polyfillIsolated(c,d,f,e):$jscomp.polyfillUnisolated(c,d,f,e))};$jscomp.polyfillUnisolated=function(c,d,f,e){f=$jscomp.global;c=c.split(".");for(e=0;e<c.length-1;e++){var a=c[e];if(!(a in f))return;f=f[a]}c=c[c.length-1];e=f[c];d=d(e);d!=e&&null!=d&&$jscomp.defineProperty(f,c,{configurable:!0,writable:!0,value:d})};
$jscomp.polyfillIsolated=function(c,d,f,e){var a=c.split(".");c=1===a.length;e=a[0];e=!c&&e in $jscomp.polyfills?$jscomp.polyfills:$jscomp.global;for(var k=0;k<a.length-1;k++){var g=a[k];if(!(g in e))return;e=e[g]}a=a[a.length-1];f=$jscomp.IS_SYMBOL_NATIVE&&"es6"===f?e[a]:null;d=d(f);null!=d&&(c?$jscomp.defineProperty($jscomp.polyfills,a,{configurable:!0,writable:!0,value:d}):d!==f&&(void 0===$jscomp.propertyToPolyfillSymbol[a]&&(f=1E9*Math.random()>>>0,$jscomp.propertyToPolyfillSymbol[a]=$jscomp.IS_SYMBOL_NATIVE?
$jscomp.global.Symbol(a):$jscomp.POLYFILL_PREFIX+f+"$"+a),$jscomp.defineProperty(e,$jscomp.propertyToPolyfillSymbol[a],{configurable:!0,writable:!0,value:d})))};$jscomp.initSymbol=function(){};
$jscomp.polyfill("Symbol",function(c){if(c)return c;var d=function(k,g){this.$jscomp$symbol$id_=k;$jscomp.defineProperty(this,"description",{configurable:!0,writable:!0,value:g})};d.prototype.toString=function(){return this.$jscomp$symbol$id_};var f="jscomp_symbol_"+(1E9*Math.random()>>>0)+"_",e=0,a=function(k){if(this instanceof a)throw new TypeError("Symbol is not a constructor");return new d(f+(k||"")+"_"+e++,k)};return a},"es6","es3");
$jscomp.polyfill("Symbol.iterator",function(c){if(c)return c;c=Symbol("Symbol.iterator");for(var d="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),f=0;f<d.length;f++){var e=$jscomp.global[d[f]];"function"===typeof e&&"function"!=typeof e.prototype[c]&&$jscomp.defineProperty(e.prototype,c,{configurable:!0,writable:!0,value:function(){return $jscomp.iteratorPrototype($jscomp.arrayIteratorImpl(this))}})}return c},"es6",
"es3");$jscomp.iteratorPrototype=function(c){c={next:c};c[Symbol.iterator]=function(){return this};return c};$jscomp.checkEs6ConformanceViaProxy=function(){try{var c={},d=Object.create(new $jscomp.global.Proxy(c,{get:function(f,e,a){return f==c&&"q"==e&&a==d}}));return!0===d.q}catch(f){return!1}};$jscomp.USE_PROXY_FOR_ES6_CONFORMANCE_CHECKS=!1;$jscomp.ES6_CONFORMANCE=$jscomp.USE_PROXY_FOR_ES6_CONFORMANCE_CHECKS&&$jscomp.checkEs6ConformanceViaProxy();
$jscomp.makeIterator=function(c){var d="undefined"!=typeof Symbol&&Symbol.iterator&&c[Symbol.iterator];if(d)return d.call(c);if("number"==typeof c.length)return $jscomp.arrayIterator(c);throw Error(String(c)+" is not an iterable or ArrayLike");};$jscomp.owns=function(c,d){return Object.prototype.hasOwnProperty.call(c,d)};$jscomp.MapEntry=function(){};
$jscomp.polyfill("Promise",function(c){function d(){this.batch_=null}function f(g){return g instanceof a?g:new a(function(b,h){b(g)})}if(c&&(!($jscomp.FORCE_POLYFILL_PROMISE||$jscomp.FORCE_POLYFILL_PROMISE_WHEN_NO_UNHANDLED_REJECTION&&"undefined"===typeof $jscomp.global.PromiseRejectionEvent)||!$jscomp.global.Promise||-1===$jscomp.global.Promise.toString().indexOf("[native code]")))return c;d.prototype.asyncExecute=function(g){if(null==this.batch_){this.batch_=[];var b=this;this.asyncExecuteFunction(function(){b.executeBatch_()})}this.batch_.push(g)};
var e=$jscomp.global.setTimeout;d.prototype.asyncExecuteFunction=function(g){e(g,0)};d.prototype.executeBatch_=function(){for(;this.batch_&&this.batch_.length;){var g=this.batch_;this.batch_=[];for(var b=0;b<g.length;++b){var h=g[b];g[b]=null;try{h()}catch(l){this.asyncThrow_(l)}}}this.batch_=null};d.prototype.asyncThrow_=function(g){this.asyncExecuteFunction(function(){throw g;})};var a=function(g){this.state_=0;this.result_=void 0;this.onSettledCallbacks_=[];this.isRejectionHandled_=!1;var b=this.createResolveAndReject_();
try{g(b.resolve,b.reject)}catch(h){b.reject(h)}};a.prototype.createResolveAndReject_=function(){function g(l){return function(r){h||(h=!0,l.call(b,r))}}var b=this,h=!1;return{resolve:g(this.resolveTo_),reject:g(this.reject_)}};a.prototype.resolveTo_=function(g){if(g===this)this.reject_(new TypeError("A Promise cannot resolve to itself"));else if(g instanceof a)this.settleSameAsPromise_(g);else{a:switch(typeof g){case "object":var b=null!=g;break a;case "function":b=!0;break a;default:b=!1}b?this.resolveToNonPromiseObj_(g):
this.fulfill_(g)}};a.prototype.resolveToNonPromiseObj_=function(g){var b=void 0;try{b=g.then}catch(h){this.reject_(h);return}"function"==typeof b?this.settleSameAsThenable_(b,g):this.fulfill_(g)};a.prototype.reject_=function(g){this.settle_(2,g)};a.prototype.fulfill_=function(g){this.settle_(1,g)};a.prototype.settle_=function(g,b){if(0!=this.state_)throw Error("Cannot settle("+g+", "+b+"): Promise already settled in state"+this.state_);this.state_=g;this.result_=b;2===this.state_&&this.scheduleUnhandledRejectionCheck_();
this.executeOnSettledCallbacks_()};a.prototype.scheduleUnhandledRejectionCheck_=function(){var g=this;e(function(){if(g.notifyUnhandledRejection_()){var b=$jscomp.global.console;"undefined"!==typeof b&&b.error(g.result_)}},1)};a.prototype.notifyUnhandledRejection_=function(){if(this.isRejectionHandled_)return!1;var g=$jscomp.global.CustomEvent,b=$jscomp.global.Event,h=$jscomp.global.dispatchEvent;if("undefined"===typeof h)return!0;"function"===typeof g?g=new g("unhandledrejection",{cancelable:!0}):
"function"===typeof b?g=new b("unhandledrejection",{cancelable:!0}):(g=$jscomp.global.document.createEvent("CustomEvent"),g.initCustomEvent("unhandledrejection",!1,!0,g));g.promise=this;g.reason=this.result_;return h(g)};a.prototype.executeOnSettledCallbacks_=function(){if(null!=this.onSettledCallbacks_){for(var g=0;g<this.onSettledCallbacks_.length;++g)k.asyncExecute(this.onSettledCallbacks_[g]);this.onSettledCallbacks_=null}};var k=new d;a.prototype.settleSameAsPromise_=function(g){var b=this.createResolveAndReject_();
g.callWhenSettled_(b.resolve,b.reject)};a.prototype.settleSameAsThenable_=function(g,b){var h=this.createResolveAndReject_();try{g.call(b,h.resolve,h.reject)}catch(l){h.reject(l)}};a.prototype.then=function(g,b){function h(p,B){return"function"==typeof p?function(n){try{l(p(n))}catch(v){r(v)}}:B}var l,r,w=new a(function(p,B){l=p;r=B});this.callWhenSettled_(h(g,l),h(b,r));return w};a.prototype.catch=function(g){return this.then(void 0,g)};a.prototype.callWhenSettled_=function(g,b){function h(){switch(l.state_){case 1:g(l.result_);
break;case 2:b(l.result_);break;default:throw Error("Unexpected state: "+l.state_);}}var l=this;null==this.onSettledCallbacks_?k.asyncExecute(h):this.onSettledCallbacks_.push(h);this.isRejectionHandled_=!0};a.resolve=f;a.reject=function(g){return new a(function(b,h){h(g)})};a.race=function(g){return new a(function(b,h){for(var l=$jscomp.makeIterator(g),r=l.next();!r.done;r=l.next())f(r.value).callWhenSettled_(b,h)})};a.all=function(g){var b=$jscomp.makeIterator(g),h=b.next();return h.done?f([]):new a(function(l,
r){function w(n){return function(v){p[n]=v;B--;0==B&&l(p)}}var p=[],B=0;do p.push(void 0),B++,f(h.value).callWhenSettled_(w(p.length-1),r),h=b.next();while(!h.done)})};return a},"es6","es3");$jscomp.checkStringArgs=function(c,d,f){if(null==c)throw new TypeError("The 'this' value for String.prototype."+f+" must not be null or undefined");if(d instanceof RegExp)throw new TypeError("First argument to String.prototype."+f+" must not be a regular expression");return c+""};
$jscomp.polyfill("String.prototype.endsWith",function(c){return c?c:function(d,f){var e=$jscomp.checkStringArgs(this,d,"endsWith");d+="";void 0===f&&(f=e.length);f=Math.max(0,Math.min(f|0,e.length));for(var a=d.length;0<a&&0<f;)if(e[--f]!=d[--a])return!1;return 0>=a}},"es6","es3");$jscomp.underscoreProtoCanBeSet=function(){var c={a:!0},d={};try{return d.__proto__=c,d.a}catch(f){}return!1};
$jscomp.setPrototypeOf=$jscomp.TRUST_ES6_POLYFILLS&&"function"==typeof Object.setPrototypeOf?Object.setPrototypeOf:$jscomp.underscoreProtoCanBeSet()?function(c,d){c.__proto__=d;if(c.__proto__!==d)throw new TypeError(c+" is not extensible");return c}:null;$jscomp.assign=$jscomp.TRUST_ES6_POLYFILLS&&"function"==typeof Object.assign?Object.assign:function(c,d){for(var f=1;f<arguments.length;f++){var e=arguments[f];if(e)for(var a in e)$jscomp.owns(e,a)&&(c[a]=e[a])}return c};
(function(c){function d(e){if(f[e])return f[e].exports;var a=f[e]={i:e,l:!1,exports:{}};c[e].call(a.exports,a,a.exports,d);a.l=!0;return a.exports}var f={};d.m=c;d.c=f;d.d=function(e,a,k){d.o(e,a)||Object.defineProperty(e,a,{enumerable:!0,get:k})};d.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});Object.defineProperty(e,"__esModule",{value:!0})};d.t=function(e,a){a&1&&(e=d(e));if(a&8||a&4&&"object"===typeof e&&e&&e.__esModule)return e;
var k=Object.create(null);d.r(k);Object.defineProperty(k,"default",{enumerable:!0,value:e});if(a&2&&"string"!=typeof e)for(var g in e)d.d(k,g,function(b){return e[b]}.bind(null,g));return k};d.n=function(e){var a=e&&e.__esModule?function(){return e["default"]}:function(){return e};d.d(a,"a",a);return a};d.o=function(e,a){return Object.prototype.hasOwnProperty.call(e,a)};d.p="/core/office/";return d(d.s=13)})([function(c,d,f){f.d(d,"b",function(){return a});f.d(d,"a",function(){return k});var e=f(1),
a=function(g,b){Object(e.a)("disableLogs")||(b?console.warn("".concat(g,": ").concat(b)):console.warn(g))},k=function(g,b){}},function(c,d,f){f.d(d,"a",function(){return k});f.d(d,"b",function(){return g});var e={},a={flattenedResources:!1,CANVAS_CACHE_SIZE:void 0,maxPagesBefore:void 0,maxPagesAhead:void 0,disableLogs:!1,wvsQueryParameters:{},_trnDebugMode:!1,_logFiltersEnabled:null},k=function(b){return a[b]},g=function(b,h){var l;a[b]=h;null===(l=e[b])||void 0===l?void 0:l.forEach(function(r){r(h)})}},
function(c,d,f){f.d(d,"a",function(){return B});f.d(d,"b",function(){return x});var e=f(7),a=f(0),k=f(4),g=f(3),b="undefined"===typeof window?self:window,h=b.importScripts,l=!1,r=function(u,q){l||(h("".concat(b.basePath,"decode.min.js")),l=!0);u=self.BrotliDecode(Object(g.b)(u));return q?u:Object(g.a)(u)},w=function(u,q){return Object(e.a)(void 0,void 0,Promise,function(){var t;return Object(e.b)(this,function(E){switch(E.label){case 0:return l?[3,2]:[4,Object(k.a)("".concat(self.Core.getWorkerPath(),
"external/decode.min.js"),"Failed to download decode.min.js",window)];case 1:E.sent(),l=!0,E.label=2;case 2:return t=self.BrotliDecode(Object(g.b)(u)),[2,q?t:Object(g.a)(t)]}})})};(function(){function u(){this.remainingDataArrays=[]}u.prototype.processRaw=function(q){return q};u.prototype.processBrotli=function(q){this.remainingDataArrays.push(q);return null};u.prototype.GetNextChunk=function(q){this.decodeFunction||(this.decodeFunction=0===q[0]&&97===q[1]&&115===q[2]&&109===q[3]?this.processRaw:
this.processBrotli);return this.decodeFunction(q)};u.prototype.End=function(){if(this.remainingDataArrays.length){for(var q=this.arrays,t=0,E=0;E<q.length;++E)t+=q[E].length;t=new Uint8Array(t);var y=0;for(E=0;E<q.length;++E){var D=q[E];t.set(D,y);y+=D.length}return r(t,!0)}return null};return u})();var p=function(u){var q=!u.shouldOutputArray,t=new XMLHttpRequest;t.open("GET",u.url,u.isAsync);var E=q&&t.overrideMimeType;t.responseType=E?"text":"arraybuffer";E&&t.overrideMimeType("text/plain; charset=x-user-defined");
t.send();var y=function(){var A=Date.now();var C=E?t.responseText:new Uint8Array(t.response);Object(a.a)("worker","Result length is ".concat(C.length));C.length<u.compressedMaximum?(C=u.decompressFunction(C,u.shouldOutputArray),Object(a.b)("There may be some degradation of performance. Your server has not been configured to serve .gz. and .br. files with the expected Content-Encoding. See https://docs.apryse.com/documentation/web/faq/content-encoding/ for instructions on how to resolve this."),h&&
Object(a.a)("worker","Decompressed length is ".concat(C.length))):q&&(C=Object(g.a)(C));h&&Object(a.a)("worker","".concat(u.url," Decompression took ").concat(Date.now()-A));return C};if(u.isAsync)var D=new Promise(function(A,C){t.onload=function(){200===this.status||0===this.status?A(y()):C("Download Failed ".concat(u.url))};t.onerror=function(){C("Network error occurred ".concat(u.url))}});else{if(200===t.status||0===t.status)return y();throw Error("Failed to load ".concat(u.url));}return D},B=
function(u){var q=u.lastIndexOf("/");-1===q&&(q=0);var t=u.slice(q).replace(".",".br.");h||(t.endsWith(".js.mem")?t=t.replace(".js.mem",".mem"):t.endsWith(".js")&&(t=t.concat(".mem")));return u.slice(0,q)+t},n=function(u,q){q.url=B(u);q.decompressFunction=h?r:w;return p(q)},v=function(u,q,t,E){return u.catch(function(y){Object(a.b)(y);return E(q,t)})},x=function(u,q,t,E){a:{var y=[n];q={compressedMaximum:q,isAsync:t,shouldOutputArray:E};if(q.isAsync){var D=y[0](u,q);for(t=1;t<y.length;++t)D=v(D,u,
q,y[t])}else{for(t=0;t<y.length;++t)try{D=y[t](u,q);break a}catch(A){Object(a.b)(A.message)}throw Error("");}}return D}},function(c,d,f){f.d(d,"b",function(){return e});f.d(d,"a",function(){return a});var e=function(k){if("string"===typeof k){for(var g=new Uint8Array(k.length),b=k.length,h=0;h<b;h++)g[h]=k.charCodeAt(h);return g}return k},a=function(k){if("string"!==typeof k){for(var g="",b=0,h=k.length,l;b<h;)l=k.subarray(b,b+1024),b+=1024,g+=String.fromCharCode.apply(null,l);return g}return k}},
function(c,d,f){function e(k,g,b){return new Promise(function(h){if(!k)return h();var l=b.document.createElement("script");l.type="text/javascript";l.onload=function(){h()};l.onerror=function(){g&&Object(a.b)(g);h()};l.src=k;b.document.getElementsByTagName("head")[0].appendChild(l)})}f.d(d,"a",function(){return e});var a=f(0)},function(c,d,f){function e(b,h,l){function r(B){p=p||Date.now();return B?(Object(a.a)("load","Try instantiateStreaming"),fetch(Object(k.a)(b)).then(function(n){return WebAssembly.instantiateStreaming(n,
h)}).catch(function(n){Object(a.a)("load","instantiateStreaming Failed ".concat(b," message ").concat(n.message));return r(!1)})):Object(k.b)(b,l,!0,!0).then(function(n){w=Date.now();Object(a.a)("load","Request took ".concat(w-p," ms"));return WebAssembly.instantiate(n,h)})}var w,p;return r(!!WebAssembly.instantiateStreaming).then(function(B){Object(a.a)("load","WASM compilation took ".concat(Date.now()-(w||p)," ms"));return B})}f.d(d,"a",function(){return e});var a=f(0),k=f(2),g=f(4);f.d(d,"b",function(){return g.a})},
function(c,d){d=function(){return this}();try{d=d||(new Function("return this"))()}catch(f){"object"===typeof window&&(d=window)}c.exports=d},function(c,d,f){function e(k,g,b,h){function l(r){return r instanceof b?r:new b(function(w){w(r)})}return new (b||(b=Promise))(function(r,w){function p(v){try{n(h.next(v))}catch(x){w(x)}}function B(v){try{n(h["throw"](v))}catch(x){w(x)}}function n(v){v.done?r(v.value):l(v.value).then(p,B)}n((h=h.apply(k,g||[])).next())})}function a(k,g){function b(n){return function(v){return h([n,
v])}}function h(n){if(r)throw new TypeError("Generator is already executing.");for(;B&&(B=0,n[0]&&(l=0)),l;)try{if(r=1,w&&(p=n[0]&2?w["return"]:n[0]?w["throw"]||((p=w["return"])&&p.call(w),0):w.next)&&!(p=p.call(w,n[1])).done)return p;if(w=0,p)n=[n[0]&2,p.value];switch(n[0]){case 0:case 1:p=n;break;case 4:return l.label++,{value:n[1],done:!1};case 5:l.label++;w=n[1];n=[0];continue;case 7:n=l.ops.pop();l.trys.pop();continue;default:if(!(p=l.trys,p=0<p.length&&p[p.length-1])&&(6===n[0]||2===n[0])){l=
0;continue}if(3===n[0]&&(!p||n[1]>p[0]&&n[1]<p[3]))l.label=n[1];else if(6===n[0]&&l.label<p[1])l.label=p[1],p=n;else if(p&&l.label<p[2])l.label=p[2],l.ops.push(n);else{p[2]&&l.ops.pop();l.trys.pop();continue}}n=g.call(k,l)}catch(v){n=[6,v],w=0}finally{r=p=0}if(n[0]&5)throw n[1];return{value:n[0]?n[1]:void 0,done:!0}}var l={label:0,sent:function(){if(p[0]&1)throw p[1];return p[1]},trys:[],ops:[]},r,w,p,B=Object.create(("function"===typeof Iterator?Iterator:Object).prototype);return B.next=b(0),B["throw"]=
b(1),B["return"]=b(2),"function"===typeof Symbol&&(B[Symbol.iterator]=function(){return this}),B}f.d(d,"a",function(){return e});f.d(d,"b",function(){return a})},function(c,d,f){d.a=function(){ArrayBuffer.prototype.slice||(ArrayBuffer.prototype.slice=function(e,a){void 0===e&&(e=0);void 0===a&&(a=this.byteLength);e=Math.floor(e);a=Math.floor(a);0>e&&(e+=this.byteLength);0>a&&(a+=this.byteLength);e=Math.min(Math.max(0,e),this.byteLength);a=Math.min(Math.max(0,a),this.byteLength);if(0>=a-e)return new ArrayBuffer(0);
var k=new ArrayBuffer(a-e),g=new Uint8Array(k);e=new Uint8Array(this,e,a-e);g.set(e);return k})}},function(c,d,f){f.d(d,"a",function(){return e});f(10);var e=function(a,k){return function(){}}},function(c,d,f){d.a=function(e){var a={};decodeURIComponent(e.slice(1)).split("&").forEach(function(k){k=k.split("=",2);a[k[0]]=k[1]});return a}},function(c,d,f){f.d(d,"a",function(){return b});var e=f(2),a=f(5),k=f(12),g=function(){function h(l){var r=this;this.promise=l.then(function(w){r.response=w;r.status=
200})}h.prototype.addEventListener=function(l,r){this.promise.then(r)};return h}(),b=function(h,l,r){if(Object(k.a)()&&!r){self.Module.instantiateWasm=function(p,B){return Object(a.a)("".concat(h,"Wasm.wasm"),p,l["Wasm.wasm"]).then(function(n){B(n.instance)})};if(l.disableObjectURLBlobs){importScripts("".concat(h,"Wasm.js"));return}r=Object(e.b)("".concat(h,"Wasm.js.mem"),l["Wasm.js.mem"],!1,!1)}else{if(l.disableObjectURLBlobs){importScripts("".concat((self.Module.asmjsPrefix?self.Module.asmjsPrefix:
"")+h,".js"));return}r=Object(e.b)("".concat((self.Module.asmjsPrefix?self.Module.asmjsPrefix:"")+h,".js.mem"),l[".js.mem"],!1);var w=Object(e.b)("".concat((self.Module.memoryInitializerPrefixURL?self.Module.memoryInitializerPrefixURL:"")+h,".mem"),l[".mem"],!0,!0);self.Module.memoryInitializerRequest=new g(w)}r=new Blob([r],{type:"application/javascript"});importScripts(URL.createObjectURL(r))}},function(c,d,f){f.d(d,"a",function(){return n});var e,a="undefined"===typeof window?self:window;c=function(){var v=
navigator.userAgent.toLowerCase();return(v=/(msie) ([\w.]+)/.exec(v)||/(trident)(?:.*? rv:([\w.]+)|)/.exec(v))?parseInt(v[2],10):v}();var k=function(){var v=a.navigator.userAgent.match(/OPR/),x=a.navigator.userAgent.match(/Maxthon/),u=a.navigator.userAgent.match(/Edge/);return a.navigator.userAgent.match(/Chrome\/(.*?) /)&&!v&&!x&&!u}();(function(){if(!k)return null;var v=a.navigator.userAgent.match(/Chrome\/([0-9]+)\./);return v?parseInt(v[1],10):v})();var g=!!navigator.userAgent.match(/Edge/i)||
navigator.userAgent.match(/Edg\/(.*?)/)&&a.navigator.userAgent.match(/Chrome\/(.*?) /);(function(){if(!g)return null;var v=a.navigator.userAgent.match(/Edg\/([0-9]+)\./);return v?parseInt(v[1],10):v})();d=/iPad|iPhone|iPod/.test(a.navigator.platform)||"MacIntel"===navigator.platform&&1<navigator.maxTouchPoints||/iPad|iPhone|iPod/.test(a.navigator.userAgent);var b=function(){var v=a.navigator.userAgent.match(/.*\/([0-9\.]+)\s(Safari|Mobile).*/i);return v?parseFloat(v[1]):v}(),h=/^((?!chrome|android).)*safari/i.test(a.navigator.userAgent)||
/^((?!chrome|android).)*$/.test(a.navigator.userAgent)&&d;h&&/^((?!chrome|android).)*safari/i.test(navigator.userAgent)&&parseInt(null===(e=navigator.userAgent.match(/Version\/(\d+)/))||void 0===e?void 0:e[1],10);var l=a.navigator.userAgent.match(/Firefox/);(function(){if(!l)return null;var v=a.navigator.userAgent.match(/Firefox\/([0-9]+)\./);return v?parseInt(v[1],10):v})();c||/Android|webOS|Touch|IEMobile|Silk/i.test(navigator.userAgent);navigator.userAgent.match(/(iPad|iPhone|iPod)/i);a.navigator.userAgent.indexOf("Android");
var r=/Mac OS X 10_13_6.*\(KHTML, like Gecko\)$/.test(a.navigator.userAgent),w=a.navigator.userAgent.match(/(iPad|iPhone).+\sOS\s((\d+)(_\d)*)/i)?14<=parseInt(a.navigator.userAgent.match(/(iPad|iPhone).+\sOS\s((\d+)(_\d)*)/i)[3],10):!1,p=!(!self.WebAssembly||!self.WebAssembly.validate),B=-1<a.navigator.userAgent.indexOf("Edge/16")||-1<a.navigator.userAgent.indexOf("MSAppHost"),n=function(){return p&&!B&&!(!w&&(h&&14>b||r))}},function(c,d,f){c.exports=f(14)},function(c,d,f){f.r(d);f(15);f(20);c=f(8);
f(21);Object(c.a)()},function(c,d,f){(function(e,a){function k(g){"@babel/helpers - typeof";return k="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(b){return typeof b}:function(b){return b&&"function"==typeof Symbol&&b.constructor===Symbol&&b!==Symbol.prototype?"symbol":typeof b},k(g)}(function(g){function b(){for(var m=0;m<K.length;m++)K[m][0](K[m][1]);K=[];N=!1}function h(m,z){K.push([m,z]);N||(N=!0,H(b,0))}function l(m,z){function G(F){p(z,F)}function I(F){n(z,F)}try{m(G,
I)}catch(F){I(F)}}function r(m){var z=m.owner,G=z.state_;z=z.data_;var I=m[G];m=m.then;if("function"===typeof I){G=A;try{z=I(z)}catch(F){n(m,F)}}w(m,z)||(G===A&&p(m,z),G===C&&n(m,z))}function w(m,z){var G;try{if(m===z)throw new TypeError("A promises callback cannot return that same promise.");if(z&&("function"===typeof z||"object"===k(z))){var I=z.then;if("function"===typeof I)return I.call(z,function(F){G||(G=!0,z!==F?p(m,F):B(m,F))},function(F){G||(G=!0,n(m,F))}),!0}}catch(F){return G||n(m,F),!0}return!1}
function p(m,z){m!==z&&w(m,z)||B(m,z)}function B(m,z){m.state_===y&&(m.state_=D,m.data_=z,h(x,m))}function n(m,z){m.state_===y&&(m.state_=D,m.data_=z,h(u,m))}function v(m){var z=m.then_;m.then_=void 0;for(m=0;m<z.length;m++)r(z[m])}function x(m){m.state_=A;v(m)}function u(m){m.state_=C;v(m)}function q(m){if("function"!==typeof m)throw new TypeError("Promise constructor takes a function argument");if(!(this instanceof q))throw new TypeError("Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.");
this.then_=[];l(m,this)}g.createPromiseCapability=function(){var m={};m.promise=new q(function(z,G){m.resolve=z;m.reject=G});return m};var t=g.Promise,E=t&&"resolve"in t&&"reject"in t&&"all"in t&&"race"in t&&function(){var m;new t(function(z){m=z});return"function"===typeof m}();"undefined"!==typeof exports&&exports?(exports.Promise=E?t:q,exports.Polyfill=q):"function"===typeof define&&f(19)?define(function(){return E?t:q}):E||(g.Promise=q);var y="pending",D="sealed",A="fulfilled",C="rejected",J=
function(){},H="undefined"!==typeof a?a:setTimeout,K=[],N;q.prototype={constructor:q,state_:y,then_:null,data_:void 0,then:function(m,z){m={owner:this,then:new this.constructor(J),fulfilled:m,rejected:z};this.state_===A||this.state_===C?h(r,m):this.then_.push(m);return m.then},"catch":function(m){return this.then(null,m)}};q.all=function(m){if("[object Array]"!==Object.prototype.toString.call(m))throw new TypeError("You must pass an array to Promise.all().");return new this(function(z,G){function I(P){O++;
return function(Q){F[P]=Q;--O||z(F)}}for(var F=[],O=0,L=0,M;L<m.length;L++)(M=m[L])&&"function"===typeof M.then?M.then(I(L),G):F[L]=M;O||z(F)})};q.race=function(m){if("[object Array]"!==Object.prototype.toString.call(m))throw new TypeError("You must pass an array to Promise.race().");return new this(function(z,G){for(var I=0,F;I<m.length;I++)(F=m[I])&&"function"===typeof F.then?F.then(z,G):z(F)})};q.resolve=function(m){return m&&"object"===k(m)&&m.constructor===this?m:new this(function(z){z(m)})};
q.reject=function(m){return new this(function(z,G){G(m)})}})("undefined"!==typeof window?window:"undefined"!==typeof e?e:"undefined"!==typeof self?self:void 0)}).call(this,f(6),f(16).setImmediate)},function(c,d,f){(function(e){function a(b,h){this._id=b;this._clearFn=h}var k="undefined"!==typeof e&&e||"undefined"!==typeof self&&self||window,g=Function.prototype.apply;d.setTimeout=function(){return new a(g.call(setTimeout,k,arguments),clearTimeout)};d.setInterval=function(){return new a(g.call(setInterval,
k,arguments),clearInterval)};d.clearTimeout=d.clearInterval=function(b){b&&b.close()};a.prototype.unref=a.prototype.ref=function(){};a.prototype.close=function(){this._clearFn.call(k,this._id)};d.enroll=function(b,h){clearTimeout(b._idleTimeoutId);b._idleTimeout=h};d.unenroll=function(b){clearTimeout(b._idleTimeoutId);b._idleTimeout=-1};d._unrefActive=d.active=function(b){clearTimeout(b._idleTimeoutId);var h=b._idleTimeout;0<=h&&(b._idleTimeoutId=setTimeout(function(){b._onTimeout&&b._onTimeout()},
h))};f(17);d.setImmediate="undefined"!==typeof self&&self.setImmediate||"undefined"!==typeof e&&e.setImmediate||this&&this.setImmediate;d.clearImmediate="undefined"!==typeof self&&self.clearImmediate||"undefined"!==typeof e&&e.clearImmediate||this&&this.clearImmediate}).call(this,f(6))},function(c,d,f){(function(e,a){(function(k,g){function b(y){delete x[y]}function h(y){if(u)setTimeout(h,0,y);else{var D=x[y];if(D){u=!0;try{var A=D.callback,C=D.args;switch(C.length){case 0:A();break;case 1:A(C[0]);
break;case 2:A(C[0],C[1]);break;case 3:A(C[0],C[1],C[2]);break;default:A.apply(g,C)}}finally{b(y),u=!1}}}}function l(){t=function(y){a.nextTick(function(){h(y)})}}function r(){if(k.postMessage&&!k.importScripts){var y=!0,D=k.onmessage;k.onmessage=function(){y=!1};k.postMessage("","*");k.onmessage=D;return y}}function w(){var y="setImmediate$"+Math.random()+"$",D=function(A){A.source===k&&"string"===typeof A.data&&0===A.data.indexOf(y)&&h(+A.data.slice(y.length))};k.addEventListener?k.addEventListener("message",
D,!1):k.attachEvent("onmessage",D);t=function(A){k.postMessage(y+A,"*")}}function p(){var y=new MessageChannel;y.port1.onmessage=function(D){h(D.data)};t=function(D){y.port2.postMessage(D)}}function B(){var y=q.documentElement;t=function(D){var A=q.createElement("script");A.onreadystatechange=function(){h(D);A.onreadystatechange=null;y.removeChild(A);A=null};y.appendChild(A)}}function n(){t=function(y){setTimeout(h,0,y)}}if(!k.setImmediate){var v=1,x={},u=!1,q=k.document,t,E=Object.getPrototypeOf&&
Object.getPrototypeOf(k);E=E&&E.setTimeout?E:k;"[object process]"==={}.toString.call(k.process)?l():r()?w():k.MessageChannel?p():q&&"onreadystatechange"in q.createElement("script")?B():n();E.setImmediate=function(y){"function"!==typeof y&&(y=new Function(""+y));for(var D=Array(arguments.length-1),A=0;A<D.length;A++)D[A]=arguments[A+1];x[v]={callback:y,args:D};t(v);return v++};E.clearImmediate=b}})("undefined"===typeof self?"undefined"===typeof e?this:e:self)}).call(this,f(6),f(18))},function(c,d){function f(){throw Error("setTimeout has not been defined");
}function e(){throw Error("clearTimeout has not been defined");}function a(x){if(r===setTimeout)return setTimeout(x,0);if((r===f||!r)&&setTimeout)return r=setTimeout,setTimeout(x,0);try{return r(x,0)}catch(u){try{return r.call(null,x,0)}catch(q){return r.call(this,x,0)}}}function k(x){if(w===clearTimeout)return clearTimeout(x);if((w===e||!w)&&clearTimeout)return w=clearTimeout,clearTimeout(x);try{return w(x)}catch(u){try{return w.call(null,x)}catch(q){return w.call(this,x)}}}function g(){B&&n&&(B=
!1,n.length?p=n.concat(p):v=-1,p.length&&b())}function b(){if(!B){var x=a(g);B=!0;for(var u=p.length;u;){n=p;for(p=[];++v<u;)n&&n[v].run();v=-1;u=p.length}n=null;B=!1;k(x)}}function h(x,u){this.fun=x;this.array=u}function l(){}c=c.exports={};try{var r="function"===typeof setTimeout?setTimeout:f}catch(x){r=f}try{var w="function"===typeof clearTimeout?clearTimeout:e}catch(x){w=e}var p=[],B=!1,n,v=-1;c.nextTick=function(x){var u=Array(arguments.length-1);if(1<arguments.length)for(var q=1;q<arguments.length;q++)u[q-
1]=arguments[q];p.push(new h(x,u));1!==p.length||B||a(b)};h.prototype.run=function(){this.fun.apply(null,this.array)};c.title="browser";c.browser=!0;c.env={};c.argv=[];c.version="";c.versions={};c.on=l;c.addListener=l;c.once=l;c.off=l;c.removeListener=l;c.removeAllListeners=l;c.emit=l;c.prependListener=l;c.prependOnceListener=l;c.listeners=function(x){return[]};c.binding=function(x){throw Error("process.binding is not supported");};c.cwd=function(){return"/"};c.chdir=function(x){throw Error("process.chdir is not supported");
};c.umask=function(){return 0}},function(c,d){c.exports={}},function(c,d,f){(function(e){"undefined"===typeof e.crypto&&(e.crypto={getRandomValues:function(a){for(var k=0;k<a.length;k++)a[k]=256*Math.random()}})})("undefined"===typeof window?self:window)},function(c,d,f){function e(b){"@babel/helpers - typeof";return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(h){return typeof h}:function(h){return h&&"function"==typeof Symbol&&h.constructor===Symbol&&h!==Symbol.prototype?
"symbol":typeof h},e(b)}var a=f(9),k=f(11),g=null;(function(b){function h(x){n||(n=[]);n.push(x)}var l,r,w,p,B=!1,n=[],v=function(){function x(){l=function(){}}function u(A){var C=[];return{resource_array:C,msg:JSON.stringify(A.data,function(J,H){if("object"===e(H)&&(J=null,H instanceof Uint8Array?J=H:H instanceof ArrayBuffer&&(J=new Uint8Array(H)),J)){H=w(J.length);var K=p(H);K&&(new Uint8Array(Module.HEAPU8.buffer,K,J.length)).set(J);C.push(H);return{__trn_res_id:H}}return H})}}function q(){B=!0;
postMessage({type:"abort",data:{error:"Office worker has terminated unexpectedly"}})}function t(A){if(!B)try{var C=u(A);r(C.msg)}catch(J){q(J)}}b.basePath="../";var E=b.officeWorkerPath||"";b.workerBasePath&&(b.basePath=b.workerBasePath);b.basePath=b.externalPath?b.externalPath:b.basePath+"external/";importScripts("".concat(b.basePath,"Promise.js"));b.ContinueFunc=function(A){l("ContinueFunc called");setTimeout(function(){onmessage({data:{action:"continue"}})},A)};if(b.pdfWorkerPath)var y=b.pdfWorkerPath;
if(b.officeAsmPath)var D=b.officeAsmPath;b.Module={memoryInitializerPrefixURL:y,asmjsPrefix:D,onRuntimeInitialized:function(){l||x();var A=Date.now()-g;Object(a.a)("load","time duration from start to ready: ".concat(JSON.stringify(A)));r=function(C){if(null!==C&&void 0!==C&&0!==C&&!B){var J=(C.length<<2)+1,H=Module._malloc(J);0<stringToUTF8(C,H,J)&&Module._TRN_OnMessage(H)}};w=function(C){return Module._TRN_CreateBufferResource(C)};p=function(C){return Module._TRN_GetResourcePointer(C)};l("OnReady called");
onmessage=t;Module._TRN_InitWorker();for(A=0;A<n.length;++A)onmessage(n[A]);n=null},fetchSelf:function(){g=Date.now();Object(k.a)("".concat(E,"WebOfficeWorker"),{"Wasm.wasm":5E6,"Wasm.js.mem":1E5,".js.mem":5E6,".mem":3E6,disableObjectURLBlobs:b.disableObjectURLBlobs},!!navigator.userAgent.match(/Edge/i)||b.wasmDisabled)},onAbort:q,noExitRuntime:!0}};b.onmessage=function(x){"init"===x.data.action&&(b.wasmDisabled=!x.data.wasm,b.externalPath=x.data.externalPath,b.officeAsmPath=x.data.officeAsmPath,
b.pdfWorkerPath=x.data.pdfWorkerPath,b.disableObjectURLBlobs=x.data.disableObjectURLBlobs,b.onmessage=h,v(),b.Module.fetchSelf())}})("undefined"===typeof window?self:window)}]);}).call(this || window)
