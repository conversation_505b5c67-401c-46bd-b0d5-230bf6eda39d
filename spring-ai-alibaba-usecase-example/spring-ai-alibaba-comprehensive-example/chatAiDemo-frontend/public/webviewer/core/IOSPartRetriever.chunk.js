/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[6],{608:function(ya,ua,n){n.r(ua);var oa=n(0),ma=n(344);ya=n(600);n(35);n=n(523);var na=function(ka){function ia(ba,x){var y=ka.call(this,ba,x)||this;y.url=ba;y.range=x;y.status=ma.a.NOT_STARTED;return y}Object(oa.c)(ia,ka);ia.prototype.start=function(){var ba=document.createElement("IFRAME");ba.setAttribute("src",this.url);document.documentElement.appendChild(ba);ba.parentNode.removeChild(ba);this.status=ma.a.STARTED;this.qL()};
return ia}(ya.ByteRangeRequest);ya=function(ka){function ia(ba,x,y,w){ba=ka.call(this,ba,x,y,w)||this;ba.GF=na;return ba}Object(oa.c)(ia,ka);ia.prototype.KC=function(ba,x){return"".concat(ba,"#").concat(x.start,"&").concat(x.stop?x.stop:"")};return ia}(ya["default"]);Object(n.a)(ya);Object(n.b)(ya);ua["default"]=ya}}]);}).call(this || window)
