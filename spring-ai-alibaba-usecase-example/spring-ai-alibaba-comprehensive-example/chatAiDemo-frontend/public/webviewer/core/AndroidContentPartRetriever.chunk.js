/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[1],{604:function(ya,ua,n){n.r(ua);var oa=n(0),ma=n(344);ya=n(600);n=n(523);var na=window,ka=function(ia){function ba(x,y){var w=ia.call(this,x,y)||this;w.url=x;w.range=y;w.request=new XMLHttpRequest;w.request.open("GET",w.url,!0);na.Uint8Array&&(w.request.responseType="arraybuffer");w.request.setRequestHeader("X-Requested-With","XMLHttpRequest");w.status=ma.a.NOT_STARTED;return w}Object(oa.c)(ba,ia);return ba}(ya.ByteRangeRequest);
ya=function(ia){function ba(x,y,w,b){x=ia.call(this,x,y,w,b)||this;x.GF=ka;return x}Object(oa.c)(ba,ia);ba.prototype.KC=function(x,y){return"".concat(x,"/bytes=").concat(y.start,",").concat(y.stop?y.stop:"")};return ba}(ya["default"]);Object(n.a)(ya);Object(n.b)(ya);ua["default"]=ya}}]);}).call(this || window)
