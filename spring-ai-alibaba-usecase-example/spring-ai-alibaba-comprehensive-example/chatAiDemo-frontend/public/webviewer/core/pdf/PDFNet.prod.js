!function(){var n,i,a=a||{};a.scope={},a.arrayIteratorImpl=function(t){var e=0;return function(){return e<t.length?{done:!1,value:t[e++]}:{done:!0}}},a.arrayIterator=function(t){return{next:a.arrayIteratorImpl(t)}},a.makeIterator=function(t){var e="undefined"!=typeof Symbol&&Symbol.iterator&&t[Symbol.iterator];if(e)return e.call(t);if("number"==typeof t.length)return a.arrayIterator(t);throw Error(String(t)+" is not an iterable or ArrayLike")},a.ASSUME_ES5=!1,a.ASSUME_NO_NATIVE_MAP=!1,a.ASSUME_NO_NATIVE_SET=!1,a.SIMPLE_FROUND_POLYFILL=!1,a.ISOLATE_POLYFILLS=!1,a.FORCE_POLYFILL_PROMISE=!1,a.FORCE_POLYFILL_PROMISE_WHEN_NO_UNHANDLED_REJECTION=!1,a.getGlobal=function(t){t=["object"==typeof globalThis&&globalThis,t,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var e=0;e<t.length;++e){var n=t[e];if(n&&n.Math==Math)return n}throw Error("Cannot find global object")},a.global=a.getGlobal(this),a.defineProperty=a.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(t,e,n){return t!=Array.prototype&&t!=Object.prototype&&(t[e]=n.value),t},a.IS_SYMBOL_NATIVE="function"==typeof Symbol&&"symbol"==typeof Symbol("x"),a.TRUST_ES6_POLYFILLS=!a.ISOLATE_POLYFILLS||a.IS_SYMBOL_NATIVE,a.polyfills={},a.propertyToPolyfillSymbol={},a.POLYFILL_PREFIX="$jscp$";function r(t){var e;return(i[t]||(e=i[t]={i:t,l:!1,exports:{}},n[t].call(e.exports,e,e.exports,r),e.l=!0,e)).exports}a.polyfill=function(t,e,n,i){e&&(a.ISOLATE_POLYFILLS?a.polyfillIsolated(t,e,n,i):a.polyfillUnisolated(t,e,n,i))},a.polyfillUnisolated=function(t,e,n,i){for(n=a.global,t=t.split("."),i=0;i<t.length-1;i++){var r=t[i];if(!(r in n))return;n=n[r]}(e=e(i=n[t=t[t.length-1]]))!=i&&null!=e&&a.defineProperty(n,t,{configurable:!0,writable:!0,value:e})},a.polyfillIsolated=function(t,e,n,i){var r=t.split(".");t=1===r.length,i=r[0],i=!t&&i in a.polyfills?a.polyfills:a.global;for(var o=0;o<r.length-1;o++){var s=r[o];if(!(s in i))return;i=i[s]}r=r[r.length-1],null!=(e=e(n=a.IS_SYMBOL_NATIVE&&"es6"===n?i[r]:null))&&(t?a.defineProperty(a.polyfills,r,{configurable:!0,writable:!0,value:e}):e!==n&&(void 0===a.propertyToPolyfillSymbol[r]&&(n=1e9*Math.random()>>>0,a.propertyToPolyfillSymbol[r]=a.IS_SYMBOL_NATIVE?a.global.Symbol(r):a.POLYFILL_PREFIX+n+"$"+r),a.defineProperty(i,a.propertyToPolyfillSymbol[r],{configurable:!0,writable:!0,value:e})))},a.polyfill("Promise",function(t){function e(){this.batch_=null}function s(n){return n instanceof u?n:new u(function(t,e){t(n)})}if(t&&(!(a.FORCE_POLYFILL_PROMISE||a.FORCE_POLYFILL_PROMISE_WHEN_NO_UNHANDLED_REJECTION&&void 0===a.global.PromiseRejectionEvent)||!a.global.Promise||-1===a.global.Promise.toString().indexOf("[native code]")))return t;e.prototype.asyncExecute=function(t){var e;null==this.batch_&&(this.batch_=[],(e=this).asyncExecuteFunction(function(){e.executeBatch_()})),this.batch_.push(t)};function u(t){this.state_=0,this.result_=void 0,this.onSettledCallbacks_=[],this.isRejectionHandled_=!1;var e=this.createResolveAndReject_();try{t(e.resolve,e.reject)}catch(t){e.reject(t)}}var n=a.global.setTimeout,r=(e.prototype.asyncExecuteFunction=function(t){n(t,0)},e.prototype.executeBatch_=function(){for(;this.batch_&&this.batch_.length;){var t=this.batch_;this.batch_=[];for(var e=0;e<t.length;++e){var n=t[e];t[e]=null;try{n()}catch(t){this.asyncThrow_(t)}}}this.batch_=null},e.prototype.asyncThrow_=function(t){this.asyncExecuteFunction(function(){throw t})},u.prototype.createResolveAndReject_=function(){function t(e){return function(t){i||(i=!0,e.call(n,t))}}var n=this,i=!1;return{resolve:t(this.resolveTo_),reject:t(this.reject_)}},u.prototype.resolveTo_=function(t){if(t===this)this.reject_(new TypeError("A Promise cannot resolve to itself"));else if(t instanceof u)this.settleSameAsPromise_(t);else{switch(typeof t){case"object":var e=null!=t;break;case"function":e=!0;break;default:e=!1}e?this.resolveToNonPromiseObj_(t):this.fulfill_(t)}},u.prototype.resolveToNonPromiseObj_=function(t){var e=void 0;try{e=t.then}catch(t){return void this.reject_(t)}"function"==typeof e?this.settleSameAsThenable_(e,t):this.fulfill_(t)},u.prototype.reject_=function(t){this.settle_(2,t)},u.prototype.fulfill_=function(t){this.settle_(1,t)},u.prototype.settle_=function(t,e){if(0!=this.state_)throw Error("Cannot settle("+t+", "+e+"): Promise already settled in state"+this.state_);this.state_=t,this.result_=e,2===this.state_&&this.scheduleUnhandledRejectionCheck_(),this.executeOnSettledCallbacks_()},u.prototype.scheduleUnhandledRejectionCheck_=function(){var e=this;n(function(){var t;e.notifyUnhandledRejection_()&&void 0!==(t=a.global.console)&&t.error(e.result_)},1)},u.prototype.notifyUnhandledRejection_=function(){var t,e,n;return!this.isRejectionHandled_&&(t=a.global.CustomEvent,e=a.global.Event,void 0===(n=a.global.dispatchEvent)||("function"==typeof t?t=new t("unhandledrejection",{cancelable:!0}):"function"==typeof e?t=new e("unhandledrejection",{cancelable:!0}):(t=a.global.document.createEvent("CustomEvent")).initCustomEvent("unhandledrejection",!1,!0,t),t.promise=this,t.reason=this.result_,n(t)))},u.prototype.executeOnSettledCallbacks_=function(){if(null!=this.onSettledCallbacks_){for(var t=0;t<this.onSettledCallbacks_.length;++t)r.asyncExecute(this.onSettledCallbacks_[t]);this.onSettledCallbacks_=null}},new e);return u.prototype.settleSameAsPromise_=function(t){var e=this.createResolveAndReject_();t.callWhenSettled_(e.resolve,e.reject)},u.prototype.settleSameAsThenable_=function(t,e){var n=this.createResolveAndReject_();try{t.call(e,n.resolve,n.reject)}catch(t){n.reject(t)}},u.prototype.then=function(t,e){function n(e,t){return"function"==typeof e?function(t){try{i(e(t))}catch(t){r(t)}}:t}var i,r,o=new u(function(t,e){i=t,r=e});return this.callWhenSettled_(n(t,i),n(e,r)),o},u.prototype.catch=function(t){return this.then(void 0,t)},u.prototype.callWhenSettled_=function(t,e){function n(){switch(i.state_){case 1:t(i.result_);break;case 2:e(i.result_);break;default:throw Error("Unexpected state: "+i.state_)}}var i=this;null==this.onSettledCallbacks_?r.asyncExecute(n):this.onSettledCallbacks_.push(n),this.isRejectionHandled_=!0},u.resolve=s,u.reject=function(n){return new u(function(t,e){e(n)})},u.race=function(r){return new u(function(t,e){for(var n=a.makeIterator(r),i=n.next();!i.done;i=n.next())s(i.value).callWhenSettled_(t,e)})},u.all=function(t){var e=a.makeIterator(t),o=e.next();return o.done?s([]):new u(function(n,t){for(var i=[],r=0;i.push(void 0),r++,s(o.value).callWhenSettled_(function t(e){return function(t){i[e]=t,0==--r&&n(i)}}(i.length-1),t),!(o=e.next()).done;);})},u},"es6","es3"),a.checkStringArgs=function(t,e,n){if(null==t)throw new TypeError("The 'this' value for String.prototype."+n+" must not be null or undefined");if(e instanceof RegExp)throw new TypeError("First argument to String.prototype."+n+" must not be a regular expression");return t+""},a.polyfill("String.prototype.startsWith",function(t){return t||function(t,e){var n=a.checkStringArgs(this,t,"startsWith"),i=n.length,r=(t+="").length;e=Math.max(0,Math.min(0|e,n.length));for(var o=0;o<r&&e<i;)if(n[e++]!=t[o++])return!1;return r<=o}},"es6","es3"),a.polyfill("Array.from",function(t){return t||function(t,e,n){e=null!=e?e:function(t){return t};var i=[],r="undefined"!=typeof Symbol&&Symbol.iterator&&t[Symbol.iterator];if("function"==typeof r){t=r.call(t);for(var o=0;!(r=t.next()).done;)i.push(e.call(n,r.value,o++))}else for(r=t.length,o=0;o<r;o++)i.push(e.call(n,t[o],o));return i}},"es6","es3"),n=[function(t,e,n){t.exports=n(1)},function(t,e){function d(t){return(d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var o=[],s=[],u=0,n=0,a=[],c=[],l="undefined"==typeof window?this:window;function i(){return{putBool:function(t,e,n){if(!1!==n&&!0!==n)throw new TypeError("An boolean value is expected for putBool");t[e]=n},putNumber:function(t,e,n){t[e]=0+n},putString:function(t,e,n){t[e]=""+n},jsColorToNumber:function(t){return 4278190080+65536*Math.floor(t.R)+256*Math.floor(t.G)+Math.floor(t.B)},jsColorFromNumber:function(t){return{A:5.960464477539063e-8*t&255,R:(16711680&(0|t))>>>16,G:(65280&(0|t))>>>8,B:255&(0|t)}},pushBackString:function(t,e,n){e in t||(t[e]=[]),t[e].push(n)}}}function r(i){return Promise.resolve().then(function t(e){var n=(e=i.next(e)).value;return e.done?e.value:n.then(t)})}function h(t,e,n){if(174774839!==e&&"0"!==t)throw Error("Non-null object of PDFNet."+n+" is not supposed to be created with its constructor.")}var p=l.Core.PDFNet||{};p.Convert=l.Core.PDFNet&&l.Core.PDFNet.Convert?l.Core.PDFNet.Convert:{},p.Optimizer={},l.Core&&l.Core.enableFullPDF(),l.isArrayBuffer=function(t){return t instanceof ArrayBuffer||null!=t&&null!=t.constructor&&"ArrayBuffer"===t.constructor.name&&"number"==typeof t.byteLength},p.Destroyable=function(){if(this.constructor===p.Destroyable)throw Error("Can't instantiate abstract class!")},p.Destroyable.prototype.takeOwnership=function(){A(this.id)},p.Destroyable.prototype.destroy=function(){return this.takeOwnership(),p.sendWithPromise(this.name+".destroy",{auto_dealloc_obj:this.id})},p.Action=function(t,e){this.name="Action",h(t,e,this.name),this.id=t},p.ActionParameter=function(t,e){this.name="ActionParameter",h(t,e,this.name),this.id=t},p.ActionParameter.prototype=Object.create(p.Destroyable.prototype),p.AdvancedImagingModule=function(t,e){this.name="AdvancedImagingModule",h(t,e,this.name),this.id=t},p.AlgorithmIdentifier=function(t,e){this.name="AlgorithmIdentifier",h(t,e,this.name),this.id=t},p.AlgorithmIdentifier.prototype=Object.create(p.Destroyable.prototype),p.AlgorithmParams=function(t,e){this.name="AlgorithmParams",h(t,e,this.name),this.id=t},p.AlgorithmParams.prototype=Object.create(p.Destroyable.prototype),p.Annot=function(t,e){this.name="Annot",h(t,e,this.name),this.id=t},p.AnnotBorderStyle=function(t,e){this.name="AnnotBorderStyle",h(t,e,this.name),this.id=t},p.AnnotBorderStyle.prototype=Object.create(p.Destroyable.prototype),p.AttrObj=function(t,e){this.name="AttrObj",h(t,e,this.name),this.id=t},p.Bookmark=function(t,e){this.name="Bookmark",h(t,e,this.name),this.id=t},p.ByteRange=function(t,e){if(this.name="ByteRange",!t||void 0!==e)return new p.ByteRange({m_offset:t=void 0===t?0:t,m_size:e=void 0===e?0:e});W(t,this)},p.CMSSignatureOptions=function(t,e){this.name="CMSSignatureOptions",h(t,e,this.name),this.id=t},p.CMSSignatureOptions.prototype=Object.create(p.Destroyable.prototype),p.CaretAnnot=function(t,e){this.name="CaretAnnot",h(t,e,this.name),this.id=t},p.CheckBoxWidget=function(t,e){this.name="CheckBoxWidget",h(t,e,this.name),this.id=t},p.ChunkRenderer=function(t,e){this.name="ChunkRenderer",h(t,e,this.name),this.id=t},p.CircleAnnot=function(t,e){this.name="CircleAnnot",h(t,e,this.name),this.id=t},p.ClassMap=function(t,e){this.name="ClassMap",h(t,e,this.name),this.id=t},p.ColorPt=function(t,e){this.name="ColorPt",h(t,e,this.name),this.id=t},p.ColorPt.prototype=Object.create(p.Destroyable.prototype),p.ColorSpace=function(t,e){this.name="ColorSpace",h(t,e,this.name),this.id=t},p.ColorSpace.prototype=Object.create(p.Destroyable.prototype),p.ComboBoxWidget=function(t,e){this.name="ComboBoxWidget",h(t,e,this.name),this.id=t},p.ContentElement=function(t,e){this.name="ContentElement",h(t,e,this.name),this.id=t},p.ContentItem=function(t,e){if(this.name="ContentItem",!t||void 0!==e)return new p.ContentItem({o:t=void 0===t?"0":t,p:e=void 0===e?"0":e});W(t,this)},p.ContentNode=function(t,e){this.name="ContentNode",h(t,e,this.name),this.id=t},p.ContentReplacer=function(t,e){this.name="ContentReplacer",h(t,e,this.name),this.id=t},p.ContentReplacer.prototype=Object.create(p.Destroyable.prototype),p.ConversionMonitor=function(t,e){this.name="ConversionMonitor",h(t,e,this.name),this.id=t},p.ConversionMonitor.prototype=Object.create(p.Destroyable.prototype),p.Date=function(t,e,n,i,r,o,s,u,a,c){if(this.name="Date",!t||void 0!==e)return new p.Date({year:t=void 0===t?0:t,month:e=void 0===e?0:e,day:n=void 0===n?0:n,hour:i=void 0===i?0:i,minute:r=void 0===r?0:r,second:o=void 0===o?0:o,UT:s=void 0===s?0:s,UT_hour:u=void 0===u?0:u,UT_minutes:a=void 0===a?0:a,mp_obj:c=void 0===c?"0":c});W(t,this)},p.Destination=function(t,e){this.name="Destination",h(t,e,this.name),this.id=t},p.DictIterator=function(t,e){this.name="DictIterator",h(t,e,this.name),this.id=t},p.DictIterator.prototype=Object.create(p.Destroyable.prototype),p.DigestAlgorithm=function(t,e){this.name="DigestAlgorithm",h(t,e,this.name),this.id=t},p.DigitalSignatureField=function(t){if(this.name="DigitalSignatureField","object"===d(t))W(t,this);else if(void 0!==t)return new p.DigitalSignatureField({mp_field_dict_obj:t})},p.DisallowedChange=function(t,e){this.name="DisallowedChange",h(t,e,this.name),this.id=t},p.DisallowedChange.prototype=Object.create(p.Destroyable.prototype),p.DocSnapshot=function(t,e){this.name="DocSnapshot",h(t,e,this.name),this.id=t},p.DocSnapshot.prototype=Object.create(p.Destroyable.prototype),p.Element=function(t,e){this.name="Element",h(t,e,this.name),this.id=t},p.ElementBuilder=function(t,e){this.name="ElementBuilder",h(t,e,this.name),this.id=t},p.ElementBuilder.prototype=Object.create(p.Destroyable.prototype),p.ElementReader=function(t,e){this.name="ElementReader",h(t,e,this.name),this.id=t},p.ElementReader.prototype=Object.create(p.Destroyable.prototype),p.ElementWriter=function(t,e){this.name="ElementWriter",h(t,e,this.name),this.id=t},p.ElementWriter.prototype=Object.create(p.Destroyable.prototype),p.EmbeddedTimestampVerificationResult=function(t,e){this.name="EmbeddedTimestampVerificationResult",h(t,e,this.name),this.id=t},p.EmbeddedTimestampVerificationResult.prototype=Object.create(p.Destroyable.prototype),p.FDFDoc=function(t,e){this.name="FDFDoc",h(t,e,this.name),this.id=t},p.FDFDoc.prototype=Object.create(p.Destroyable.prototype),p.FDFField=function(t,e){if(this.name="FDFField",!t||void 0!==e)return new p.FDFField({mp_leaf_node:t=void 0===t?"0":t,mp_root_array:e=void 0===e?"0":e});W(t,this)},p.Field=function(t,e){if(this.name="Field",!t||void 0!==e)return new p.Field({leaf_node:t=void 0===t?"0":t,builder:e=void 0===e?"0":e});W(t,this)},p.FileAttachmentAnnot=function(t,e){this.name="FileAttachmentAnnot",h(t,e,this.name),this.id=t},p.FileSpec=function(t,e){this.name="FileSpec",h(t,e,this.name),this.id=t},p.Filter=function(t,e){this.name="Filter",h(t,e,this.name),this.id=t},p.Filter.prototype=Object.create(p.Destroyable.prototype),p.FilterReader=function(t,e){this.name="FilterReader",h(t,e,this.name),this.id=t},p.FilterReader.prototype=Object.create(p.Destroyable.prototype),p.FilterWriter=function(t,e){this.name="FilterWriter",h(t,e,this.name),this.id=t},p.FilterWriter.prototype=Object.create(p.Destroyable.prototype),p.Flattener=function(t,e){this.name="Flattener",h(t,e,this.name),this.id=t},p.Flattener.prototype=Object.create(p.Destroyable.prototype),p.FlowDocument=function(t,e){this.name="FlowDocument",h(t,e,this.name),this.id=t},p.FlowDocument.prototype=Object.create(p.Destroyable.prototype),p.Font=function(t,e){this.name="Font",h(t,e,this.name),this.id=t},p.Font.prototype=Object.create(p.Destroyable.prototype),p.FreeTextAnnot=function(t,e){this.name="FreeTextAnnot",h(t,e,this.name),this.id=t},p.Function=function(t,e){this.name="Function",h(t,e,this.name),this.id=t},p.Function.prototype=Object.create(p.Destroyable.prototype),p.GState=function(t,e){this.name="GState",h(t,e,this.name),this.id=t},p.GeometryCollection=function(t,e){this.name="GeometryCollection",h(t,e,this.name),this.id=t},p.GeometryCollection.prototype=Object.create(p.Destroyable.prototype),p.HighlightAnnot=function(t,e){this.name="HighlightAnnot",h(t,e,this.name),this.id=t},p.Highlights=function(t,e){this.name="Highlights",h(t,e,this.name),this.id=t},p.Highlights.prototype=Object.create(p.Destroyable.prototype),p.Image=function(t,e){this.name="Image",h(t,e,this.name),this.id=t},p.InkAnnot=function(t,e){this.name="InkAnnot",h(t,e,this.name),this.id=t},p.Iterator=function(t,e,n){this.name="Iterator",h(t,e,this.name),this.id=t,this.type=n},p.Iterator.prototype=Object.create(p.Destroyable.prototype),p.KeyStrokeActionResult=function(t,e){this.name="KeyStrokeActionResult",h(t,e,this.name),this.id=t},p.KeyStrokeActionResult.prototype=Object.create(p.Destroyable.prototype),p.KeyStrokeEventData=function(t,e){this.name="KeyStrokeEventData",h(t,e,this.name),this.id=t},p.KeyStrokeEventData.prototype=Object.create(p.Destroyable.prototype),p.LineAnnot=function(t,e){this.name="LineAnnot",h(t,e,this.name),this.id=t},p.LinkAnnot=function(t,e){this.name="LinkAnnot",h(t,e,this.name),this.id=t},p.List=function(t,e){this.name="List",h(t,e,this.name),this.id=t},p.ListBoxWidget=function(t,e){this.name="ListBoxWidget",h(t,e,this.name),this.id=t},p.ListItem=function(t,e){this.name="ListItem",h(t,e,this.name),this.id=t},p.MarkupAnnot=function(t,e){this.name="MarkupAnnot",h(t,e,this.name),this.id=t},p.Matrix2D=function(t,e,n,i,r,o){if(this.name="Matrix2D",!t||void 0!==e)return new p.Matrix2D({m_a:t=void 0===t?0:t,m_b:e=void 0===e?0:e,m_c:n=void 0===n?0:n,m_d:i=void 0===i?0:i,m_h:r=void 0===r?0:r,m_v:o=void 0===o?0:o});W(t,this)},p.MovieAnnot=function(t,e){this.name="MovieAnnot",h(t,e,this.name),this.id=t},p.NameTree=function(t,e){this.name="NameTree",h(t,e,this.name),this.id=t},p.NumberTree=function(t,e){this.name="NumberTree",h(t,e,this.name),this.id=t},p.OCG=function(t,e){this.name="OCG",h(t,e,this.name),this.id=t},p.OCGConfig=function(t,e){this.name="OCGConfig",h(t,e,this.name),this.id=t},p.OCGContext=function(t,e){this.name="OCGContext",h(t,e,this.name),this.id=t},p.OCGContext.prototype=Object.create(p.Destroyable.prototype),p.OCMD=function(t,e){this.name="OCMD",h(t,e,this.name),this.id=t},p.OCRModule=function(t,e){this.name="OCRModule",h(t,e,this.name),this.id=t},p.Obj=function(t,e){this.name="Obj",h(t,e,this.name),this.id=t},p.ObjSet=function(t,e){this.name="ObjSet",h(t,e,this.name),this.id=t},p.ObjSet.prototype=Object.create(p.Destroyable.prototype),p.ObjectIdentifier=function(t,e){this.name="ObjectIdentifier",h(t,e,this.name),this.id=t},p.ObjectIdentifier.prototype=Object.create(p.Destroyable.prototype),p.OwnedBitmap=function(t,e){this.name="OwnedBitmap",h(t,e,this.name),this.id=t},p.PDFACompliance=function(t,e){this.name="PDFACompliance",h(t,e,this.name),this.id=t},p.PDFACompliance.prototype=Object.create(p.Destroyable.prototype),p.PDFAOptions=function(t,e){this.name="PDFAOptions",h(t,e,this.name),this.id=t},p.PDFDC=function(t,e){this.name="PDFDC",h(t,e,this.name),this.id=t},p.PDFDCEX=function(t,e){this.name="PDFDCEX",h(t,e,this.name),this.id=t},p.PDFDoc=function(t,e){this.name="PDFDoc",h(t,e,this.name),this.id=t},p.PDFDoc.prototype=Object.create(p.Destroyable.prototype),p.PDFDocInfo=function(t,e){this.name="PDFDocInfo",h(t,e,this.name),this.id=t},p.PDFDocViewPrefs=function(t,e){this.name="PDFDocViewPrefs",h(t,e,this.name),this.id=t},p.PDFDraw=function(t,e){this.name="PDFDraw",h(t,e,this.name),this.id=t},p.PDFDraw.prototype=Object.create(p.Destroyable.prototype),p.PDFRasterizer=function(t,e){this.name="PDFRasterizer",h(t,e,this.name),this.id=t},p.PDFRasterizer.prototype=Object.create(p.Destroyable.prototype),p.PDFTronCustomSecurityHandler=function(t,e){this.name="PDFTronCustomSecurityHandler",h(t,e,this.name),this.id=t},p.Page=function(t,e){this.name="Page",h(t,e,this.name),this.id=t},p.PageLabel=function(t,e,n){if(this.name="PageLabel",!t||void 0!==e)return new p.PageLabel({mp_obj:t=void 0===t?"0":t,m_first_page:e=void 0===e?0:e,m_last_page:n=void 0===n?0:n});W(t,this)},p.PageSet=function(t,e){this.name="PageSet",h(t,e,this.name),this.id=t},p.PageSet.prototype=Object.create(p.Destroyable.prototype),p.Paragraph=function(t,e){this.name="Paragraph",h(t,e,this.name),this.id=t},p.PatternColor=function(t,e){this.name="PatternColor",h(t,e,this.name),this.id=t},p.PatternColor.prototype=Object.create(p.Destroyable.prototype),p.PolyLineAnnot=function(t,e){this.name="PolyLineAnnot",h(t,e,this.name),this.id=t},p.PolygonAnnot=function(t,e){this.name="PolygonAnnot",h(t,e,this.name),this.id=t},p.PopupAnnot=function(t,e){this.name="PopupAnnot",h(t,e,this.name),this.id=t},p.PrinterMode=function(t,e){this.name="PrinterMode",h(t,e,this.name),this.id=t},p.PushButtonWidget=function(t,e){this.name="PushButtonWidget",h(t,e,this.name),this.id=t},p.RSASSAPSSParams=function(t,e){this.name="RSASSAPSSParams",h(t,e,this.name),this.id=t},p.RadioButtonGroup=function(t,e){this.name="RadioButtonGroup",h(t,e,this.name),this.id=t},p.RadioButtonGroup.prototype=Object.create(p.Destroyable.prototype),p.RadioButtonWidget=function(t,e){this.name="RadioButtonWidget",h(t,e,this.name),this.id=t},p.Rect=function(t,e,n,i,r){if(this.name="Rect",!t||void 0!==e)return new p.Rect({x1:t=void 0===t?0:t,y1:e=void 0===e?0:e,x2:n=void 0===n?0:n,y2:i=void 0===i?0:i,mp_rect:r=void 0===r?"0":r});W(t,this)},p.Redaction=function(t,e){this.name="Redaction",h(t,e,this.name),this.id=t},p.RedactionAnnot=function(t,e){this.name="RedactionAnnot",h(t,e,this.name),this.id=t},p.Redactor=function(t,e){this.name="Redactor",h(t,e,this.name),this.id=t},p.Reflow=function(t,e){this.name="Reflow",h(t,e,this.name),this.id=t},p.Reflow.prototype=Object.create(p.Destroyable.prototype),p.ResultSnapshot=function(t,e){this.name="ResultSnapshot",h(t,e,this.name),this.id=t},p.ResultSnapshot.prototype=Object.create(p.Destroyable.prototype),p.RoleMap=function(t,e){this.name="RoleMap",h(t,e,this.name),this.id=t},p.RubberStampAnnot=function(t,e){this.name="RubberStampAnnot",h(t,e,this.name),this.id=t},p.SDFDoc=function(t,e){this.name="SDFDoc",h(t,e,this.name),this.id=t},p.SElement=function(t,e){if(this.name="SElement",!t||void 0!==e)return new p.SElement({obj:t=void 0===t?"0":t,k:e=void 0===e?"0":e});W(t,this)},p.STree=function(t,e){this.name="STree",h(t,e,this.name),this.id=t},p.ScreenAnnot=function(t,e){this.name="ScreenAnnot",h(t,e,this.name),this.id=t},p.SecurityHandler=function(t,e){this.name="SecurityHandler",h(t,e,this.name),this.id=t},p.SecurityHandler.prototype=Object.create(p.Destroyable.prototype),p.Shading=function(t,e){this.name="Shading",h(t,e,this.name),this.id=t},p.Shading.prototype=Object.create(p.Destroyable.prototype),p.ShapedText=function(t,e){this.name="ShapedText",h(t,e,this.name),this.id=t},p.ShapedText.prototype=Object.create(p.Destroyable.prototype),p.SignatureHandler=function(t,e){this.name="SignatureHandler",h(t,e,this.name),this.id=t},p.SignatureWidget=function(t,e){this.name="SignatureWidget",h(t,e,this.name),this.id=t},p.SoundAnnot=function(t,e){this.name="SoundAnnot",h(t,e,this.name),this.id=t},p.SquareAnnot=function(t,e){this.name="SquareAnnot",h(t,e,this.name),this.id=t},p.SquigglyAnnot=function(t,e){this.name="SquigglyAnnot",h(t,e,this.name),this.id=t},p.Stamper=function(t,e){this.name="Stamper",h(t,e,this.name),this.id=t},p.Stamper.prototype=Object.create(p.Destroyable.prototype),p.StrikeOutAnnot=function(t,e){this.name="StrikeOutAnnot",h(t,e,this.name),this.id=t},p.Table=function(t,e){this.name="Table",h(t,e,this.name),this.id=t},p.TableCell=function(t,e){this.name="TableCell",h(t,e,this.name),this.id=t},p.TableRow=function(t,e){this.name="TableRow",h(t,e,this.name),this.id=t},p.TextAnnot=function(t,e){this.name="TextAnnot",h(t,e,this.name),this.id=t},p.TextExtractor=function(t,e){this.name="TextExtractor",h(t,e,this.name),this.id=t},p.TextExtractor.prototype=Object.create(p.Destroyable.prototype),p.TextExtractorLine=function(t,e,n,i,r,o){if(this.name="TextExtractorLine",!t||void 0!==e)return new p.TextExtractorLine({line:t=void 0===t?"0":t,uni:e=void 0===e?"0":e,num:n=void 0===n?0:n,cur_num:i=void 0===i?0:i,m_direction:r=void 0===r?0:r,mp_bld:o=void 0===o?"0":o});W(t,this)},p.TextExtractorStyle=function(t){if(this.name="TextExtractorStyle","object"===d(t))W(t,this);else if(void 0!==t)return new p.TextExtractorStyle({mp_imp:t})},p.TextExtractorWord=function(t,e,n,i,r,o){if(this.name="TextExtractorWord",!t||void 0!==e)return new p.TextExtractorWord({line:t=void 0===t?"0":t,word:e=void 0===e?"0":e,uni:n=void 0===n?"0":n,num:i=void 0===i?0:i,cur_num:r=void 0===r?0:r,mp_bld:o=void 0===o?"0":o});W(t,this)},p.TextMarkupAnnot=function(t,e){this.name="TextMarkupAnnot",h(t,e,this.name),this.id=t},p.TextRange=function(t,e){this.name="TextRange",h(t,e,this.name),this.id=t},p.TextRun=function(t,e){this.name="TextRun",h(t,e,this.name),this.id=t},p.TextSearch=function(t,e){this.name="TextSearch",h(t,e,this.name),this.id=t},p.TextSearch.prototype=Object.create(p.Destroyable.prototype),p.TextStyledElement=function(t,e){this.name="TextStyledElement",h(t,e,this.name),this.id=t},p.TextWidget=function(t,e){this.name="TextWidget",h(t,e,this.name),this.id=t},p.TimestampingConfiguration=function(t,e){this.name="TimestampingConfiguration",h(t,e,this.name),this.id=t},p.TimestampingConfiguration.prototype=Object.create(p.Destroyable.prototype),p.TimestampingResult=function(t,e){this.name="TimestampingResult",h(t,e,this.name),this.id=t},p.TimestampingResult.prototype=Object.create(p.Destroyable.prototype),p.TrustVerificationResult=function(t,e){this.name="TrustVerificationResult",h(t,e,this.name),this.id=t},p.TrustVerificationResult.prototype=Object.create(p.Destroyable.prototype),p.UnderlineAnnot=function(t,e){this.name="UnderlineAnnot",h(t,e,this.name),this.id=t},p.UndoManager=function(t,e){this.name="UndoManager",h(t,e,this.name),this.id=t},p.UndoManager.prototype=Object.create(p.Destroyable.prototype),p.VerificationOptions=function(t,e){this.name="VerificationOptions",h(t,e,this.name),this.id=t},p.VerificationOptions.prototype=Object.create(p.Destroyable.prototype),p.VerificationResult=function(t,e){this.name="VerificationResult",h(t,e,this.name),this.id=t},p.VerificationResult.prototype=Object.create(p.Destroyable.prototype),p.ViewChangeCollection=function(t,e){this.name="ViewChangeCollection",h(t,e,this.name),this.id=t},p.ViewChangeCollection.prototype=Object.create(p.Destroyable.prototype),p.WatermarkAnnot=function(t,e){this.name="WatermarkAnnot",h(t,e,this.name),this.id=t},p.WebFontDownloader=function(t,e){this.name="WebFontDownloader",h(t,e,this.name),this.id=t},p.WidgetAnnot=function(t,e){this.name="WidgetAnnot",h(t,e,this.name),this.id=t},p.X501AttributeTypeAndValue=function(t,e){this.name="X501AttributeTypeAndValue",h(t,e,this.name),this.id=t},p.X501AttributeTypeAndValue.prototype=Object.create(p.Destroyable.prototype),p.X501DistinguishedName=function(t,e){this.name="X501DistinguishedName",h(t,e,this.name),this.id=t},p.X501DistinguishedName.prototype=Object.create(p.Destroyable.prototype),p.X509Certificate=function(t,e){this.name="X509Certificate",h(t,e,this.name),this.id=t},p.X509Certificate.prototype=Object.create(p.Destroyable.prototype),p.X509Extension=function(t,e){this.name="X509Extension",h(t,e,this.name),this.id=t},p.X509Extension.prototype=Object.create(p.Destroyable.prototype),p.QuadPoint=function(t,e,n,i,r,o,s,u){if(this.name="QuadPoint",!t||void 0!==e)return new p.QuadPoint({p1x:t=void 0===t?0:t,p1y:e=void 0===e?0:e,p2x:n=void 0===n?0:n,p2y:i=void 0===i?0:i,p3x:r=void 0===r?0:r,p3y:o=void 0===o?0:o,p4x:s=void 0===s?0:s,p4y:u=void 0===u?0:u});W(t,this)},p.Point=function(t,e){if(this.name="Point",!t||void 0!==e)return new p.Point({x:t=void 0===t?0:t,y:e=void 0===e?0:e});W(t,this)},p.CharData=function(t){if(void 0===t)throw new TypeError("CharData requires an object to construct with.");this.name="CharData",W(t,this)},p.Separation=function(t){if(void 0===t)throw new TypeError("Separation requires an object to construct with.");this.name="Separation",W(t,this)},p.Optimizer.createImageSettings=function(){return Promise.resolve(new p.Optimizer.ImageSettings)},p.Optimizer.ImageSettings=function(){this.m_max_pixels=4294967295,this.m_max_dpi=225,this.m_resample_dpi=150,this.m_quality=5,this.m_compression_mode=p.Optimizer.ImageSettings.CompressionMode.e_retain,this.m_downsample_mode=p.Optimizer.ImageSettings.DownsampleMode.e_default,this.m_force_changes=this.m_force_recompression=!1},p.Optimizer.ImageSettings.prototype.setImageDPI=function(t,e){return this.m_max_dpi=t,this.m_resample_dpi=e,this},p.Optimizer.ImageSettings.prototype.setCompressionMode=function(t){return this.m_compression_mode=t,this},p.Optimizer.ImageSettings.prototype.setDownsampleMode=function(t){return this.m_downsample_mode=t,this},p.Optimizer.ImageSettings.prototype.setQuality=function(t){return this.m_quality=t,this},p.Optimizer.ImageSettings.prototype.forceRecompression=function(t){return this.m_force_recompression=t,this},p.Optimizer.ImageSettings.prototype.forceChanges=function(t){return this.m_force_changes=t,this},p.Optimizer.createMonoImageSettings=function(){return Promise.resolve(new p.Optimizer.MonoImageSettings)},p.Optimizer.MonoImageSettings=function(){this.m_max_pixels=4294967295,this.m_max_dpi=450,this.m_resample_dpi=300,this.m_jbig2_threshold=8.5,this.m_compression_mode=p.Optimizer.ImageSettings.CompressionMode.e_retain,this.m_downsample_mode=p.Optimizer.ImageSettings.DownsampleMode.e_default,this.m_force_changes=this.m_force_recompression=!1},p.Optimizer.MonoImageSettings.prototype.setImageDPI=function(t,e){return this.m_max_dpi=t,this.m_resample_dpi=e,this},p.Optimizer.MonoImageSettings.prototype.setCompressionMode=function(t){return this.m_compression_mode=t,this},p.Optimizer.MonoImageSettings.prototype.setDownsampleMode=function(t){return this.m_downsample_mode=t,this},p.Optimizer.MonoImageSettings.prototype.setJBIG2Threshold=function(t){return this.m_jbig2_threshold=quality,this},p.Optimizer.MonoImageSettings.prototype.forceRecompression=function(t){return this.m_force_recompression=t,this},p.Optimizer.MonoImageSettings.prototype.forceChanges=function(t){return this.m_force_changes=t,this},p.Optimizer.createTextSettings=function(){return Promise.resolve(new p.Optimizer.TextSettings)},p.Optimizer.TextSettings=function(){this.m_embed_fonts=this.m_subset_fonts=!1},p.Optimizer.TextSettings.prototype.subsetFonts=function(t){return this.m_subset_fonts=t,this},p.Optimizer.TextSettings.prototype.embedFonts=function(t){return this.m_embed_fonts=t,this},p.Optimizer.createOptimizerSettings=function(){return Promise.resolve(new p.Optimizer.OptimizerSettings)},p.Optimizer.OptimizerSettings=function(){this.color_image_settings=new p.Optimizer.ImageSettings,this.grayscale_image_settings=new p.Optimizer.ImageSettings,this.mono_image_settings=new p.Optimizer.MonoImageSettings,this.text_settings=new p.Optimizer.TextSettings,this.remove_custom=!0},p.Optimizer.OptimizerSettings.prototype.setColorImageSettings=function(t){return this.color_image_settings=t,this},p.Optimizer.OptimizerSettings.prototype.setGrayscaleImageSettings=function(t){return this.grayscale_image_settings=t,this},p.Optimizer.OptimizerSettings.prototype.setMonoImageSettings=function(t){return this.mono_image_settings=t,this},p.Optimizer.OptimizerSettings.prototype.setTextSettings=function(t){return this.text_settings=t,this},p.Optimizer.OptimizerSettings.prototype.removeCustomEntries=function(t){return this.remove_custom=t,this},p.Optimizer.ImageSettings.CompressionMode={e_retain:0,e_flate:1,e_jpeg:2,e_jpeg2000:3,e_none:4},p.Optimizer.ImageSettings.DownsampleMode={e_off:0,e_default:1},p.Optimizer.MonoImageSettings.CompressionMode={e_jbig2:0,e_flate:1,e_none:2,e_ccitt:3},p.Optimizer.MonoImageSettings.DownsampleMode={e_off:0,e_default:1},p.Convert.ConversionOptions=function(t){this.name="PDFNet.Convert.ConversionOptions",t&&W(JSON.parse(t),this)},p.Convert.ConversionOptions.prototype.setFileExtension=function(t){return this.FileExtension=t,this},p.Convert.ConversionOptions.prototype.setEnableExternalMediaDownloads=function(t){return this.EnableExternalMediaDownloads=t,this},p.Convert.OverprintPreviewMode={e_op_off:0,e_op_on:1,e_op_pdfx_on:2},p.Convert.XPSOutputCommonOptions=function(){this.name="PDFNet.Convert.XPSOutputCommonOptions",this.mImpl={}},p.Convert.XPSOutputCommonOptions.prototype.setPrintMode=function(t){return this.mImpl.PRINTMODE=t,this},p.Convert.XPSOutputCommonOptions.prototype.setDPI=function(t){return this.mImpl.DPI=t,this},p.Convert.XPSOutputCommonOptions.prototype.setRenderPages=function(t){return this.mImpl.RENDER=t,this},p.Convert.XPSOutputCommonOptions.prototype.setThickenLines=function(t){return this.mImpl.THICKENLINES=t,this},p.Convert.XPSOutputCommonOptions.prototype.generateURLLinks=function(t){return this.mImpl.URL_LINKS=t,this},p.Convert.XPSOutputCommonOptions.prototype.setOverprint=function(t){switch(t){case p.Convert.OverprintPreviewMode.e_op_off:this.mImpl.OVERPRINT_MODE="OFF";break;case p.Convert.OverprintPreviewMode.e_op_on:this.mImpl.OVERPRINT_MODE="ON";break;case p.Convert.OverprintPreviewMode.e_op_pdfx_on:this.mImpl.OVERPRINT_MODE="PDFX"}return this},p.Convert.XPSOutputCommonOptions.prototype.getJsonString=function(){return JSON.stringify(this.mImpl)},p.Convert.createXPSOutputOptions=function(){return Promise.resolve(new p.Convert.XPSOutputOptions)},p.Convert.XPSOutputOptions=function(){p.Convert.XPSOutputCommonOptions.call(this),this.name="PDFNet.Convert.XPSOutputOptions"},p.Convert.XPSOutputOptions.prototype=Object.create(p.Convert.XPSOutputCommonOptions.prototype),p.Convert.XPSOutputOptions.prototype.setOpenXps=function(t){return this.mImpl.OPENXPS=t,this},p.Convert.FlattenFlag={e_off:0,e_simple:1,e_fast:2,e_high_quality:3},p.Convert.FlattenThresholdFlag={e_very_strict:0,e_strict:1,e_default:2,e_keep_most:3,e_keep_all:4},p.Convert.AnnotationOutputFlag={e_internal_xfdf:0,e_external_xfdf:1,e_flatten:2},p.Convert.createXODOutputOptions=function(){return Promise.resolve(new p.Convert.XODOutputOptions)},p.Convert.XODOutputOptions=function(){p.Convert.XPSOutputCommonOptions.call(this),this.name="PDFNet.Convert.XODOutputOptions"},p.Convert.XODOutputOptions.prototype=Object.create(p.Convert.XPSOutputCommonOptions.prototype),p.Convert.XODOutputOptions.prototype.setExtractUsingZorder=function(t){return this.mImpl.USEZORDER=t,this},p.Convert.XODOutputOptions.prototype.setOutputThumbnails=function(t){return this.mImpl.NOTHUMBS=t,this},p.Convert.XODOutputOptions.prototype.setThumbnailSize=function(t,e){return this.mImpl.THUMB_SIZE=t,this.mImpl.LARGE_THUMB_SIZE=e||t,this},p.Convert.XODOutputOptions.prototype.setElementLimit=function(t){return this.mImpl.ELEMENTLIMIT=t,this},p.Convert.XODOutputOptions.prototype.setOpacityMaskWorkaround=function(t){return this.mImpl.MASKRENDER=t,this},p.Convert.XODOutputOptions.prototype.setMaximumImagePixels=function(t){return this.mImpl.MAX_IMAGE_PIXELS=t,this},p.Convert.XODOutputOptions.prototype.setFlattenContent=function(t){switch(t){case p.Convert.FlattenFlag.e_off:this.mImpl.FLATTEN_CONTENT="OFF";break;case p.Convert.FlattenFlag.e_simple:this.mImpl.FLATTEN_CONTENT="SIMPLE";break;case p.Convert.FlattenFlag.e_fast:this.mImpl.FLATTEN_CONTENT="FAST";break;case p.Convert.FlattenFlag.e_high_quality:this.mImpl.FLATTEN_CONTENT="HIGH_QUALITY"}return this},p.Convert.XODOutputOptions.prototype.setFlattenThreshold=function(t){switch(t){case p.Convert.FlattenThresholdFlag.e_very_strict:this.mImpl.FLATTEN_THRESHOLD="VERY_STRICT";break;case p.Convert.FlattenThresholdFlag.e_strict:this.mImpl.FLATTEN_THRESHOLD="STRICT";break;case p.Convert.FlattenThresholdFlag.e_default:this.mImpl.FLATTEN_THRESHOLD="DEFAULT";break;case p.Convert.FlattenThresholdFlag.e_keep_most:this.mImpl.FLATTEN_THRESHOLD="KEEP_MOST";break;case p.Convert.FlattenThresholdFlag.e_keep_all:this.mImpl.FLATTEN_THRESHOLD="KEEP_ALL"}return this},p.Convert.XODOutputOptions.prototype.setPreferJPG=function(t){return this.mImpl.PREFER_JPEG=t,this},p.Convert.XODOutputOptions.prototype.setJPGQuality=function(t){return this.mImpl.JPEG_QUALITY=t,this},p.Convert.XODOutputOptions.prototype.setSilverlightTextWorkaround=function(t){return this.mImpl.REMOVE_ROTATED_TEXT=t,this},p.Convert.XODOutputOptions.prototype.setAnnotationOutput=function(t){switch(t){case p.Convert.AnnotationOutputFlag.e_internal_xfdf:this.mImpl.ANNOTATION_OUTPUT="INTERNAL";break;case p.Convert.AnnotationOutputFlag.e_external_xfdf:this.mImpl.ANNOTATION_OUTPUT="EXTERNAL";break;case p.Convert.AnnotationOutputFlag.e_flatten:this.mImpl.ANNOTATION_OUTPUT="FLATTEN"}return this},p.Convert.XODOutputOptions.prototype.setExternalParts=function(t){return this.mImpl.EXTERNAL_PARTS=t,this},p.Convert.XODOutputOptions.prototype.setEncryptPassword=function(t){return this.mImpl.ENCRYPT_PASSWORD=t,this},p.Convert.XODOutputOptions.prototype.useSilverlightFlashCompatible=function(t){return this.mImpl.COMPATIBLE_XOD=t,this},p.Convert.createTiffOutputOptions=function(){return Promise.resolve(new p.Convert.TiffOutputOptions)},p.Convert.TiffOutputOptions=function(){this.name="PDFNet.Convert.TiffOutputOptions",this.mImpl={}},p.Convert.TiffOutputOptions.prototype.setBox=function(t){switch(t){case p.Page.Box.e_media:this.mImpl.BOX="media";break;case p.Page.Box.e_crop:this.mImpl.BOX="crop";break;case p.Page.Box.e_bleed:this.mImpl.BOX="bleed";break;case p.Page.Box.e_trim:this.mImpl.BOX="trim";break;case p.Page.Box.e_art:this.mImpl.BOX="art"}return this},p.Convert.TiffOutputOptions.prototype.setRotate=function(t){switch(t){case p.Page.Box.e_0:this.mImpl.ROTATE="0";break;case p.Page.Box.e_90:this.mImpl.ROTATE="90";break;case p.Page.Box.e_180:this.mImpl.ROTATE="180";break;case p.Page.Box.e_270:this.mImpl.ROTATE="270"}return this},p.Convert.TiffOutputOptions.prototype.setClip=function(t,e,n,i){return this.mImpl.CLIP_X1=t,this.mImpl.CLIP_Y1=e,this.mImpl.CLIP_X2=n,this.mImpl.CLIP_Y2=i,this},p.Convert.TiffOutputOptions.prototype.setPages=function(t){return this.mImpl.PAGES=t,this},p.Convert.TiffOutputOptions.prototype.setOverprint=function(t){switch(t){case p.PDFRasterizer.OverprintPreviewMode.e_op_off:this.mImpl.OVERPRINT_MODE="OFF";break;case p.PDFRasterizer.OverprintPreviewMode.e_op_on:this.mImpl.OVERPRINT_MODE="ON";break;case p.PDFRasterizer.OverprintPreviewMode.e_op_pdfx_on:this.mImpl.OVERPRINT_MODE="PDFX"}return this},p.Convert.TiffOutputOptions.prototype.setCMYK=function(t){return this.mImpl.CMYK=t,this},p.Convert.TiffOutputOptions.prototype.setDither=function(t){return this.mImpl.DITHER=t,this},p.Convert.TiffOutputOptions.prototype.setGray=function(t){return this.mImpl.GRAY=t,this},p.Convert.TiffOutputOptions.prototype.setMono=function(t){return this.mImpl.MONO=t,this},p.Convert.TiffOutputOptions.prototype.setAnnots=function(t){return this.mImpl.ANNOTS=t,this},p.Convert.TiffOutputOptions.prototype.setSmooth=function(t){return this.mImpl.SMOOTH=t,this},p.Convert.TiffOutputOptions.prototype.setPrintmode=function(t){return this.mImpl.PRINTMODE=t,this},p.Convert.TiffOutputOptions.prototype.setTransparentPage=function(t){return this.mImpl.TRANSPARENT_PAGE=t,this},p.Convert.TiffOutputOptions.prototype.setPalettized=function(t){return this.mImpl.PALETTIZED=t,this},p.Convert.TiffOutputOptions.prototype.setDPI=function(t){return this.mImpl.DPI=t,this},p.Convert.TiffOutputOptions.prototype.setGamma=function(t){return this.mImpl.GAMMA=t,this},p.Convert.TiffOutputOptions.prototype.setHRes=function(t){return this.mImpl.HRES=t,this},p.Convert.TiffOutputOptions.prototype.setVRes=function(t){return this.mImpl.VRES=t,this},p.Convert.TiffOutputOptions.prototype.getJsonString=function(){return JSON.stringify(this.mImpl)},p.Convert.createHTMLOutputOptions=function(){return Promise.resolve(new p.Convert.HTMLOutputOptions)},p.Convert.HTMLOutputOptions=function(){this.name="PDFNet.Convert.HTMLOutputOptions",this.mImpl={}},p.Convert.HTMLOutputOptions.prototype.setPreferJPG=function(t){return this.mImpl.PREFER_JPEG=t,this},p.Convert.HTMLOutputOptions.prototype.setJPGQuality=function(t){return this.mImpl.JPEG_QUALITY=t,this},p.Convert.HTMLOutputOptions.prototype.setDPI=function(t){return this.mImpl.DPI=t,this},p.Convert.HTMLOutputOptions.prototype.setMaximumImagePixels=function(t){return this.mImpl.MAX_IMAGE_PIXELS=t,this},p.Convert.HTMLOutputOptions.prototype.setScale=function(t){return this.mImpl.SCALE=t,this},p.Convert.HTMLOutputOptions.prototype.setExternalLinks=function(t){return this.mImpl.EXTERNAL_LINKS=t,this},p.Convert.HTMLOutputOptions.prototype.setInternalLinks=function(t){return this.mImpl.INTERNAL_LINKS=t,this},p.Convert.HTMLOutputOptions.prototype.setSimplifyText=function(t){return this.mImpl.SIMPLIFY_TEXT=t,this},p.Convert.HTMLOutputOptions.prototype.getJsonString=function(){return JSON.stringify(this.mImpl)},p.Convert.createEPUBOutputOptions=function(){return Promise.resolve(new p.Convert.EPUBOutputOptions)},p.Convert.EPUBOutputOptions=function(){this.name="PDFNet.Convert.EPUBOutputOptions",this.mImpl={}},p.Convert.EPUBOutputOptions.prototype.setExpanded=function(t){return this.mImpl.EPUB_EXPANDED=t,this},p.Convert.EPUBOutputOptions.prototype.setReuseCover=function(t){return this.mImpl.EPUB_REUSE_COVER=t,this},p.PDFDoc.createViewerOptimizedOptions=function(){return Promise.resolve(new p.PDFDoc.ViewerOptimizedOptions)},p.PDFDoc.ViewerOptimizedOptions=function(){this.name="PDFNet.PDFDoc.ViewerOptimizedOptions",this.mImpl={}},p.PDFDoc.ViewerOptimizedOptions.prototype.setThumbnailRenderingThreshold=function(t){return this.mImpl.COMPLEXITY_THRESHOLD=t,this},p.PDFDoc.ViewerOptimizedOptions.prototype.setMinimumInitialThumbnails=function(t){return this.mImpl.MINIMUM_INITIAL_THUMBNAILS=t,this},p.PDFDoc.ViewerOptimizedOptions.prototype.setThumbnailSize=function(t){return this.mImpl.THUMB_SIZE=t,this},p.PDFDoc.ViewerOptimizedOptions.prototype.setOverprint=function(t){switch(t){case p.PDFRasterizer.OverprintPreviewMode.e_op_off:this.mImpl.OVERPRINT_MODE="OFF";break;case p.PDFRasterizer.OverprintPreviewMode.e_op_on:this.mImpl.OVERPRINT_MODE="ON";break;case p.PDFRasterizer.OverprintPreviewMode.e_op_pdfx_on:this.mImpl.OVERPRINT_MODE="PDFX"}return this},p.PDFDoc.ViewerOptimizedOptions.prototype.getJsonString=function(){return JSON.stringify(this.mImpl)},p.FDFDoc.createXFDFExportOptions=function(){return Promise.resolve(new p.FDFDoc.XFDFExportOptions)},p.FDFDoc.XFDFExportOptions=function(){this.name="PDFNet.FDFDoc.XFDFExportOptions",this.mImpl={},this.mHelpers=i()},p.FDFDoc.XFDFExportOptions.prototype.getWriteAnnotationAppearance=function(){return"WriteAnnotationAppearance"in this.mImpl&&!!this.mImpl.WriteAnnotationAppearance},p.FDFDoc.XFDFExportOptions.prototype.setWriteAnnotationAppearance=function(t){return this.mHelpers.putBool(this.mImpl,"WriteAnnotationAppearance",t),this},p.FDFDoc.XFDFExportOptions.prototype.getWriteImagedata=function(){return!("WriteImagedata"in this.mImpl&&!this.mImpl.WriteImagedata)},p.FDFDoc.XFDFExportOptions.prototype.setWriteImagedata=function(t){return this.mHelpers.putBool(this.mImpl,"WriteImagedata",t),this},p.FDFDoc.XFDFExportOptions.prototype.getJsonString=function(){return JSON.stringify(this.mImpl)},p.PDFDoc.createMergeXFDFOptions=function(){return Promise.resolve(new p.PDFDoc.MergeXFDFOptions)},p.PDFDoc.MergeXFDFOptions=function(){this.name="PDFNet.PDFDoc.MergeXFDFOptions",this.mImpl={},this.mHelpers=i()},p.PDFDoc.MergeXFDFOptions.prototype.getForce=function(){return"Force"in this.mImpl&&!!this.mImpl.Force},p.PDFDoc.MergeXFDFOptions.prototype.setForce=function(t){return this.mHelpers.putBool(this.mImpl,"Force",t),this},p.PDFDoc.MergeXFDFOptions.prototype.getUseNonStandardRotation=function(){return"UseNonStandardRotation"in this.mImpl&&!!this.mImpl.UseNonStandardRotation},p.PDFDoc.MergeXFDFOptions.prototype.setUseNonStandardRotation=function(t){return this.mHelpers.putBool(this.mImpl,"UseNonStandardRotation",t),this},p.PDFDoc.MergeXFDFOptions.prototype.getJsonString=function(){return JSON.stringify(this.mImpl)},p.PDFDoc.createDiffOptions=function(){return Promise.resolve(new p.PDFDoc.DiffOptions)},p.PDFDoc.DiffOptions=function(){this.mImpl={},this.mHelpers=i()},p.PDFDoc.DiffOptions.prototype.getAddGroupAnnots=function(){return"AddGroupAnnots"in this.mImpl&&!!this.mImpl.AddGroupAnnots},p.PDFDoc.DiffOptions.prototype.setAddGroupAnnots=function(t){return this.mHelpers.putBool(this.mImpl,"AddGroupAnnots",t),this},p.PDFDoc.DiffOptions.prototype.getBlendMode=function(){return"BlendMode"in this.mImpl?this.mImpl.BlendMode:5},p.PDFDoc.DiffOptions.prototype.setBlendMode=function(t){return this.mHelpers.putNumber(this.mImpl,"BlendMode",t),this},p.PDFDoc.DiffOptions.prototype.getColorA=function(){return"ColorA"in this.mImpl?this.mHelpers.jsColorFromNumber(this.mImpl.ColorA):this.mHelpers.jsColorFromNumber(4291559424)},p.PDFDoc.DiffOptions.prototype.setColorA=function(t){return this.mHelpers.putNumber(this.mImpl,"ColorA",this.mHelpers.jsColorToNumber(t)),this},p.PDFDoc.DiffOptions.prototype.getColorB=function(){return"ColorB"in this.mImpl?this.mHelpers.jsColorFromNumber(this.mImpl.ColorB):this.mHelpers.jsColorFromNumber(4278242508)},p.PDFDoc.DiffOptions.prototype.setColorB=function(t){return this.mHelpers.putNumber(this.mImpl,"ColorB",this.mHelpers.jsColorToNumber(t)),this},p.PDFDoc.DiffOptions.prototype.getLuminosityCompression=function(){return"LuminosityCompression"in this.mImpl?this.mImpl.LuminosityCompression:10},p.PDFDoc.DiffOptions.prototype.setLuminosityCompression=function(t){return this.mHelpers.putNumber(this.mImpl,"LuminosityCompression",t),this},p.PDFDoc.DiffOptions.prototype.getJsonString=function(){return JSON.stringify(this.mImpl)},p.createDiffOptions=p.PDFDoc.createDiffOptions,p.DiffOptions=p.PDFDoc.DiffOptions,p.PDFDoc.createTextDiffOptions=function(){return Promise.resolve(new p.PDFDoc.TextDiffOptions)},p.PDFDoc.TextDiffOptions=function(){this.name="PDFNet.PDFDoc.TextDiffOptions",this.mImpl={},this.mHelpers=i()},p.PDFDoc.TextDiffOptions.prototype.getColorA=function(){return"ColorA"in this.mImpl?this.mHelpers.jsColorFromNumber(this.mImpl.ColorA):this.mHelpers.jsColorFromNumber(4293284423)},p.PDFDoc.TextDiffOptions.prototype.setColorA=function(t){return this.mHelpers.putNumber(this.mImpl,"ColorA",this.mHelpers.jsColorToNumber(t)),this},p.PDFDoc.TextDiffOptions.prototype.getOpacityA=function(){return"OpacityA"in this.mImpl?this.mImpl.OpacityA:.5},p.PDFDoc.TextDiffOptions.prototype.setOpacityA=function(t){return this.mHelpers.putNumber(this.mImpl,"OpacityA",t),this},p.PDFDoc.TextDiffOptions.prototype.getColorB=function(){return"ColorB"in this.mImpl?this.mHelpers.jsColorFromNumber(this.mImpl.ColorB):this.mHelpers.jsColorFromNumber(4284278322)},p.PDFDoc.TextDiffOptions.prototype.setColorB=function(t){return this.mHelpers.putNumber(this.mImpl,"ColorB",this.mHelpers.jsColorToNumber(t)),this},p.PDFDoc.TextDiffOptions.prototype.getCompareUsingZOrder=function(){return"CompareUsingZOrder"in this.mImpl&&!!this.mImpl.CompareUsingZOrder},p.PDFDoc.TextDiffOptions.prototype.setCompareUsingZOrder=function(t){return this.mHelpers.putBool(this.mImpl,"CompareUsingZOrder",t),this},p.PDFDoc.TextDiffOptions.prototype.getOpacityB=function(){return"OpacityB"in this.mImpl?this.mImpl.OpacityB:.5},p.PDFDoc.TextDiffOptions.prototype.setOpacityB=function(t){return this.mHelpers.putNumber(this.mImpl,"OpacityB",t),this},p.PDFDoc.TextDiffOptions.prototype.addZonesForPage=function(t,e,n){if(void 0===this.mImpl[t]&&(this.mImpl[t]=[]),this.mImpl[t].length<n)for(var i=this.mImpl[t].length;i<n;i++)this.mImpl[t].push([]);e=e.map(function(t){return[t.x1,t.y1,t.x2,t.y2]}),this.mImpl[t][n-1]=e},p.PDFDoc.TextDiffOptions.prototype.getExtraMoveColor=function(){return"ExtraMoveColor"in this.mImpl?this.mHelpers.jsColorFromNumber(this.mImpl.ExtraMoveColor):this.mHelpers.jsColorFromNumber(4294898256)},p.PDFDoc.TextDiffOptions.prototype.setExtraMoveColor=function(t){return this.mHelpers.putNumber(this.mImpl,"ExtraMoveColor",this.mHelpers.jsColorToNumber(t)),this},p.PDFDoc.TextDiffOptions.prototype.getExtraMoveOpacity=function(){return"ExtraMoveOpacity"in this.mImpl?this.mImpl.ExtraMoveOpacity:.8},p.PDFDoc.TextDiffOptions.prototype.setExtraMoveOpacity=function(t){return this.mHelpers.putNumber(this.mImpl,"ExtraMoveOpacity",t),this},p.PDFDoc.TextDiffOptions.prototype.getExtraMoveHighlight=function(){return"ExtraMoveHighlight"in this.mImpl&&!!this.mImpl.ExtraMoveHighlight},p.PDFDoc.TextDiffOptions.prototype.setExtraMoveHighlight=function(t){return this.mHelpers.putBool(this.mImpl,"ExtraMoveHighlight",t),this},p.PDFDoc.TextDiffOptions.prototype.getShowPlaceholders=function(){return"ShowPlaceholders"in this.mImpl&&!!this.mImpl.ShowPlaceholders},p.PDFDoc.TextDiffOptions.prototype.setShowPlaceholders=function(t){return this.mHelpers.putBool(this.mImpl,"ShowPlaceholders",t),this},p.PDFDoc.TextDiffOptions.prototype.getCompareStyles=function(){return"CompareStyles"in this.mImpl&&!!this.mImpl.CompareStyles},p.PDFDoc.TextDiffOptions.prototype.setCompareStyles=function(t){return this.mHelpers.putBool(this.mImpl,"CompareStyles",t),this},p.PDFDoc.TextDiffOptions.prototype.addIgnoreZonesForPage=function(t,e){return this.addZonesForPage("IgnoreZones",t,e),this},p.PDFDoc.TextDiffOptions.prototype.getJsonString=function(){return JSON.stringify(this.mImpl)},p.PDFDoc.createRefreshOptions=function(){return Promise.resolve(new p.PDFDoc.RefreshOptions)},p.PDFDoc.RefreshOptions=function(){this.mImpl={},this.mHelpers=i()},p.PDFDoc.RefreshOptions.prototype.getDrawBackgroundOnly=function(){return!("DrawBackgroundOnly"in this.mImpl&&!this.mImpl.DrawBackgroundOnly)},p.PDFDoc.RefreshOptions.prototype.setDrawBackgroundOnly=function(t){return this.mHelpers.putBool(this.mImpl,"DrawBackgroundOnly",t),this},p.PDFDoc.RefreshOptions.prototype.getRefreshExisting=function(){return!("RefreshExisting"in this.mImpl&&!this.mImpl.RefreshExisting)},p.PDFDoc.RefreshOptions.prototype.setRefreshExisting=function(t){return this.mHelpers.putBool(this.mImpl,"RefreshExisting",t),this},p.PDFDoc.RefreshOptions.prototype.getUseNonStandardRotation=function(){return"UseNonStandardRotation"in this.mImpl&&!!this.mImpl.UseNonStandardRotation},p.PDFDoc.RefreshOptions.prototype.setUseNonStandardRotation=function(t){return this.mHelpers.putBool(this.mImpl,"UseNonStandardRotation",t),this},p.PDFDoc.RefreshOptions.prototype.getUseRoundedCorners=function(){return"UseRoundedCorners"in this.mImpl&&!!this.mImpl.UseRoundedCorners},p.PDFDoc.RefreshOptions.prototype.setUseRoundedCorners=function(t){return this.mHelpers.putBool(this.mImpl,"UseRoundedCorners",t),this},p.PDFDoc.RefreshOptions.prototype.getJsonString=function(){return JSON.stringify(this.mImpl)},p.createRefreshOptions=p.PDFDoc.createRefreshOptions,p.RefreshOptions=p.PDFDoc.RefreshOptions,p.PDFACompliance.createPDFAOptions=function(t){return Promise.resolve(new p.PDFACompliance.PDFAOptions(t))},p.PDFACompliance.PDFAOptions=function(t){this.name="PDFNet.PDFACompliance.PDFAOptions",this.mImpl={},this.mHelpers=i(),this.setConformance(t)},p.PDFACompliance.PDFAOptions.prototype.getConformance=function(){return"Conformance"in this.mImpl?this.mImpl.Conformance:p.PDFACompliance.Conformance.e_Level1B},p.PDFACompliance.PDFAOptions.prototype.setConformance=function(t){return this.mHelpers.putNumber(this.mImpl,"Conformance",t),this},p.PDFACompliance.PDFAOptions.prototype.getDPI=function(){return"Dpi"in this.mImpl?0|this.mImpl.Dpi:300},p.PDFACompliance.PDFAOptions.prototype.setDPI=function(t){return this.mHelpers.putNumber(this.mImpl,"Dpi",t),this},p.PDFACompliance.PDFAOptions.prototype.getFirstStop=function(){return"FirstStop"in this.mImpl&&!!this.mImpl.FirstStop},p.PDFACompliance.PDFAOptions.prototype.setFirstStop=function(t){return this.mHelpers.putBool(this.mImpl,"FirstStop",t),this},p.PDFACompliance.PDFAOptions.prototype.getFlattenTransparency=function(){return"Flatten"in this.mImpl&&!!this.mImpl.Flatten},p.PDFACompliance.PDFAOptions.prototype.setFlattenTransparency=function(t){return this.mHelpers.putBool(this.mImpl,"Flatten",t),this},p.PDFACompliance.PDFAOptions.prototype.getMaxRefObjs=function(){return"MaxRefObjs"in this.mImpl?0|this.mImpl.MaxRefObjs:10},p.PDFACompliance.PDFAOptions.prototype.setMaxRefObjs=function(t){return this.mHelpers.putNumber(this.mImpl,"MaxRefObjs",t),this},p.PDFACompliance.PDFAOptions.prototype.getPassword=function(){return"Password"in this.mImpl?this.mImpl.Password:""},p.PDFACompliance.PDFAOptions.prototype.setPassword=function(t){return this.mHelpers.putString(this.mImpl,"Password",""+t),this},p.PDFACompliance.PDFAOptions.prototype.getJsonString=function(){return JSON.stringify(this.mImpl)},p.PDFDoc.createSnapToOptions=function(){return Promise.resolve(new p.PDFDoc.SnapToOptions)},p.PDFDoc.SnapToOptions=function(){this.name="PDFNet.PDFDoc.SnapToOptions",this.mImpl={},this.mHelpers=i()},p.PDFDoc.SnapToOptions.prototype.setShapeLimit=function(t){return this.mHelpers.putNumber(this.mImpl,"ShapeLimit",t),this},p.PDFDoc.SnapToOptions.prototype.getJsonString=function(){return JSON.stringify(this.mImpl)},p.Convert.createOfficeToPDFOptions=function(t){return Promise.resolve(new p.Convert.OfficeToPDFOptions(t))},p.Convert.OfficeToPDFOptions=function(t){this.mImpl={},this.mHelpers=i(),this.name="PDFNet.Convert.ConversionOptions",this.mImpl.name="PDFNet.Convert.ConversionOptions",p.Convert.ConversionOptions.call(this.mImpl,t)},p.Convert.OfficeToPDFOptions.StructureTagLevel={e_default:0,e_none:1},p.Convert.OfficeToPDFOptions.DisplayComments={e_off:0,e_annotations:1},p.Convert.OfficeToPDFOptions.prototype.getApplyPageBreaksToSheet=function(){return"ApplyPageBreaksToSheet"in this.mImpl&&!!this.mImpl.ApplyPageBreaksToSheet},p.Convert.OfficeToPDFOptions.prototype.setApplyPageBreaksToSheet=function(t){return this.mHelpers.putBool(this.mImpl,"ApplyPageBreaksToSheet",t),this},p.Convert.OfficeToPDFOptions.prototype.getDisplayChangeTracking=function(){return!("DisplayChangeTracking"in this.mImpl&&!this.mImpl.DisplayChangeTracking)},p.Convert.OfficeToPDFOptions.prototype.setDisplayChangeTracking=function(t){return this.mHelpers.putBool(this.mImpl,"DisplayChangeTracking",t),this},p.Convert.OfficeToPDFOptions.prototype.getDisplayComments=function(){return"DisplayComments"in this.mImpl?this.mImpl.DisplayComments:0},p.Convert.OfficeToPDFOptions.prototype.setDisplayComments=function(t){return this.mHelpers.putNumber(this.mImpl,"DisplayComments",t),this},p.Convert.OfficeToPDFOptions.prototype.getDisplayHiddenText=function(){return"DisplayHiddenText"in this.mImpl&&!!this.mImpl.DisplayHiddenText},p.Convert.OfficeToPDFOptions.prototype.setDisplayHiddenText=function(t){return this.mHelpers.putBool(this.mImpl,"DisplayHiddenText",t),this},p.Convert.OfficeToPDFOptions.prototype.getExcelDefaultCellBorderWidth=function(){return"ExcelDefaultCellBorderWidth"in this.mImpl?this.mImpl.ExcelDefaultCellBorderWidth:0},p.Convert.OfficeToPDFOptions.prototype.setExcelDefaultCellBorderWidth=function(t){return this.mHelpers.putNumber(this.mImpl,"ExcelDefaultCellBorderWidth",t),this},p.Convert.OfficeToPDFOptions.prototype.getExcelMaxAllowedCellCount=function(){return"ExcelMaxAllowedCellCount"in this.mImpl?this.mImpl.ExcelMaxAllowedCellCount:0},p.Convert.OfficeToPDFOptions.prototype.setExcelMaxAllowedCellCount=function(t){return this.mHelpers.putNumber(this.mImpl,"ExcelMaxAllowedCellCount",t),this},p.Convert.OfficeToPDFOptions.prototype.getHideTotalNumberOfPages=function(){return"HideTotalNumberOfPages"in this.mImpl&&!!this.mImpl.HideTotalNumberOfPages},p.Convert.OfficeToPDFOptions.prototype.setHideTotalNumberOfPages=function(t){return this.mHelpers.putBool(this.mImpl,"HideTotalNumberOfPages",t),this},p.Convert.OfficeToPDFOptions.prototype.getIncludeBookmarks=function(){return!("IncludeBookmarks"in this.mImpl&&!this.mImpl.IncludeBookmarks)},p.Convert.OfficeToPDFOptions.prototype.setIncludeBookmarks=function(t){return this.mHelpers.putBool(this.mImpl,"IncludeBookmarks",t),this},p.Convert.OfficeToPDFOptions.prototype.getIncrementalSave=function(){return"IncrementalSave"in this.mImpl&&!!this.mImpl.IncrementalSave},p.Convert.OfficeToPDFOptions.prototype.setIncrementalSave=function(t){return this.mHelpers.putBool(this.mImpl,"IncrementalSave",t),this},p.Convert.OfficeToPDFOptions.prototype.getLocale=function(){return"Locale"in this.mImpl?this.mImpl.Locale:""},p.Convert.OfficeToPDFOptions.prototype.setLocale=function(t){return this.mHelpers.putString(this.mImpl,"Locale",""+t),this},p.Convert.OfficeToPDFOptions.prototype.getPassword=function(){return"Password"in this.mImpl?this.mImpl.Password:""},p.Convert.OfficeToPDFOptions.prototype.setPassword=function(t){return this.mHelpers.putString(this.mImpl,"Password",""+t),this},p.Convert.OfficeToPDFOptions.prototype.getStructureTagLevel=function(){return"structure_tag_level"in this.mImpl?this.mImpl.structure_tag_level:0},p.Convert.OfficeToPDFOptions.prototype.setStructureTagLevel=function(t){return this.mHelpers.putNumber(this.mImpl,"structure_tag_level",t),this},p.Convert.OfficeToPDFOptions.prototype.getTemplateLeftDelimiter=function(){return"TemplateLeftDelimiter"in this.mImpl?this.mImpl.TemplateLeftDelimiter:"{{"},p.Convert.OfficeToPDFOptions.prototype.setTemplateLeftDelimiter=function(t){return this.mHelpers.putString(this.mImpl,"TemplateLeftDelimiter",""+t),this},p.Convert.OfficeToPDFOptions.prototype.getTemplateParamsJson=function(){return"TemplateParamsJson"in this.mImpl?this.mImpl.TemplateParamsJson:""},p.Convert.OfficeToPDFOptions.prototype.setTemplateParamsJson=function(t){return this.mHelpers.putString(this.mImpl,"TemplateParamsJson",""+t),this},p.Convert.OfficeToPDFOptions.prototype.getTemplateRightDelimiter=function(){return"TemplateRightDelimiter"in this.mImpl?this.mImpl.TemplateRightDelimiter:"}}"},p.Convert.OfficeToPDFOptions.prototype.setTemplateRightDelimiter=function(t){return this.mHelpers.putString(this.mImpl,"TemplateRightDelimiter",""+t),this},p.Convert.OfficeToPDFOptions.prototype.getTemplateStrictMode=function(){return"TemplateStrictMode"in this.mImpl&&!!this.mImpl.TemplateStrictMode},p.Convert.OfficeToPDFOptions.prototype.setTemplateStrictMode=function(t){return this.mHelpers.putBool(this.mImpl,"TemplateStrictMode",t),this},p.Convert.OfficeToPDFOptions.prototype.getUpdateTableOfContents=function(){return"UpdateTableOfContents"in this.mImpl&&!!this.mImpl.UpdateTableOfContents},p.Convert.OfficeToPDFOptions.prototype.setUpdateTableOfContents=function(t){return this.mHelpers.putBool(this.mImpl,"UpdateTableOfContents",t),this},p.Convert.OfficeToPDFOptions.prototype.getJsonString=function(){return JSON.stringify(this.mImpl)},p.MarkupAnnot.prototype=new p.Annot("0"),p.TextMarkupAnnot.prototype=new p.MarkupAnnot("0"),p.CaretAnnot.prototype=new p.MarkupAnnot("0"),p.LineAnnot.prototype=new p.MarkupAnnot("0"),p.CircleAnnot.prototype=new p.MarkupAnnot("0"),p.FileAttachmentAnnot.prototype=new p.MarkupAnnot("0"),p.FreeTextAnnot.prototype=new p.MarkupAnnot("0"),p.HighlightAnnot.prototype=new p.TextMarkupAnnot("0"),p.InkAnnot.prototype=new p.MarkupAnnot("0"),p.LinkAnnot.prototype=new p.Annot("0"),p.MovieAnnot.prototype=new p.Annot("0"),p.PolyLineAnnot.prototype=new p.LineAnnot("0"),p.PolygonAnnot.prototype=new p.PolyLineAnnot("0"),p.PopupAnnot.prototype=new p.Annot("0"),p.RedactionAnnot.prototype=new p.MarkupAnnot("0"),p.RubberStampAnnot.prototype=new p.MarkupAnnot("0"),p.ScreenAnnot.prototype=new p.Annot("0"),p.SoundAnnot.prototype=new p.MarkupAnnot("0"),p.SquareAnnot.prototype=new p.MarkupAnnot("0"),p.SquigglyAnnot.prototype=new p.TextMarkupAnnot("0"),p.StrikeOutAnnot.prototype=new p.TextMarkupAnnot("0"),p.TextAnnot.prototype=new p.MarkupAnnot("0"),p.UnderlineAnnot.prototype=new p.TextMarkupAnnot("0"),p.WatermarkAnnot.prototype=new p.Annot("0"),p.WidgetAnnot.prototype=new p.Annot("0"),p.SignatureWidget.prototype=new p.WidgetAnnot("0"),p.ComboBoxWidget.prototype=new p.WidgetAnnot("0"),p.ListBoxWidget.prototype=new p.WidgetAnnot("0"),p.TextWidget.prototype=new p.WidgetAnnot("0"),p.CheckBoxWidget.prototype=new p.WidgetAnnot("0"),p.RadioButtonWidget.prototype=new p.WidgetAnnot("0"),p.PushButtonWidget.prototype=new p.WidgetAnnot("0"),p.ContentNode.prototype=new p.ContentElement("0"),p.Paragraph.prototype=new p.ContentNode("0"),p.TextRun.prototype=new p.ContentElement("0"),p.TextStyledElement.prototype=new p.ContentElement("0"),p.Table.prototype=new p.ContentNode("0"),p.TableRow.prototype=new p.ContentNode("0"),p.TableCell.prototype=new p.ContentNode("0"),p.RSASSAPSSParams.prototype=new p.AlgorithmParams("0"),p.PrinterMode.PaperSize={e_custom:0,e_letter:1,e_letter_small:2,e_tabloid:3,e_ledger:4,e_legal:5,e_statement:6,e_executive:7,e_a3:8,e_a4:9,e_a4_mall:10,e_a5:11,e_b4_jis:12,e_b5_jis:13,e_folio:14,e_quarto:15,e_10x14:16,e_11x17:17,e_note:18,e_envelope_9:19,e_envelope_10:20,e_envelope_11:21,e_envelope_12:22,e_envelope_14:23,e_c_size_sheet:24,e_d_size_sheet:25,e_e_size_sheet:26,e_envelope_dl:27,e_envelope_c5:28,e_envelope_c3:29,e_envelope_c4:30,e_envelope_c6:31,e_envelope_c65:32,e_envelope_b4:33,e_envelope_b5:34,e_envelope_b6:35,e_envelope_italy:36,e_envelope_monarch:37,e_6_3_quarters_envelope:38,e_us_std_fanfold:39,e_german_std_fanfold:40,e_german_legal_fanfold:41,e_b4_iso:42,e_japanese_postcard:43,e_9x11:44,e_10x11:45,e_15x11:46,e_envelope_invite:47,e_reserved_48:48,e_reserved_49:49,e_letter_extra:50,e_legal_extra:51,e_tabloid_extra:52,e_a4_extra:53,e_letter_transverse:54,e_a4_transverse:55,e_letter_extra_transverse:56,e_supera_supera_a4:57,e_Superb_Superb_a3:58,e_letter_plus:59,e_a4_plus:60,e_a5_transverse:61,e_b5_jis_transverse:62,e_a3_extra:63,e_a5_extra:64,e_b5_iso_extra:65,e_a2:66,e_a3_transverse:67,e_a3_extra_transverse:68,e_japanese_double_postcard:69,e_a6:70,e_japanese_envelope_kaku_2:71,e_japanese_envelope_kaku_3:72,e_japanese_envelope_chou_3:73,e_japanese_envelope_chou_4:74,e_letter_rotated:75,e_a3_rotated:76,e_a4_rotated:77,e_a5_rotated:78,e_b4_jis_rotated:79,e_b5_jis_rotated:80,e_japanese_postcard_rotated:81,e_double_japanese_postcard_rotated:82,e_a6_rotated:83,e_japanese_envelope_kaku_2_rotated:84,e_japanese_envelope_kaku_3_rotated:85,e_japanese_envelope_chou_3_rotated:86,e_japanese_envelope_chou_4_rotated:87,e_b6_jis:88,e_b6_jis_rotated:89,e_12x11:90,e_japanese_envelope_you_4:91,e_japanese_envelope_you_4_rotated:92,e_PrinterMode_prc_16k:93,e_prc_32k:94,e_prc_32k_big:95,e_prc_envelop_1:96,e_prc_envelop_2:97,e_prc_envelop_3:98,e_prc_envelop_4:99,e_prc_envelop_5:100,e_prc_envelop_6:101,e_prc_envelop_7:102,e_prc_envelop_8:103,e_prc_envelop_9:104,e_prc_envelop_10:105,e_prc_16k_rotated:106,e_prc_32k_rotated:107,e_prc_32k_big__rotated:108,e_prc_envelop_1_rotated:109,e_prc_envelop_2_rotated:110,e_prc_envelop_3_rotated:111,e_prc_envelop_4_rotated:112,e_prc_envelop_5_rotated:113,e_prc_envelop_6_rotated:114,e_prc_envelop_7_rotated:115,e_prc_envelop_8_rotated:116,e_prc_envelop_9_rotated:117,e_prc_envelop_10_rotated:118},p.Field.EventType={e_action_trigger_keystroke:13,e_action_trigger_format:14,e_action_trigger_validate:15,e_action_trigger_calculate:16},p.Field.Type={e_button:0,e_check:1,e_radio:2,e_text:3,e_choice:4,e_signature:5,e_null:6},p.Field.Flag={e_read_only:0,e_required:1,e_no_export:2,e_pushbutton_flag:3,e_radio_flag:4,e_toggle_to_off:5,e_radios_in_unison:6,e_multiline:7,e_password:8,e_file_select:9,e_no_spellcheck:10,e_no_scroll:11,e_comb:12,e_rich_text:13,e_combo:14,e_edit:15,e_sort:16,e_multiselect:17,e_commit_on_sel_change:18},p.Field.TextJustification={e_left_justified:0,e_centered:1,e_right_justified:2},p.Filter.StdFileOpenMode={e_read_mode:0,e_write_mode:1,e_append_mode:2},p.Filter.ReferencePos={e_begin:0,e_end:2,e_cur:1},p.OCGContext.OCDrawMode={e_VisibleOC:0,e_AllOC:1,e_NoOC:2},p.OCMD.VisibilityPolicyType={e_AllOn:0,e_AnyOn:1,e_AnyOff:2,e_AllOff:3},p.PDFACompliance.Conformance={e_Level1A:1,e_Level1B:2,e_Level2A:3,e_Level2B:4,e_Level2U:5,e_Level3A:6,e_Level3B:7,e_Level3U:8,e_Level4:9,e_Level4E:10,e_Level4F:11},p.PDFACompliance.ErrorCode={e_PDFA0_1_0:10,e_PDFA0_1_1:11,e_PDFA0_1_2:12,e_PDFA0_1_3:13,e_PDFA0_1_4:14,e_PDFA0_1_5:15,e_PDFA1_2_1:121,e_PDFA1_2_2:122,e_PDFA1_3_1:131,e_PDFA1_3_2:132,e_PDFA1_3_3:133,e_PDFA1_3_4:134,e_PDFA1_4_1:141,e_PDFA1_4_2:142,e_PDFA1_6_1:161,e_PDFA1_7_1:171,e_PDFA1_7_2:172,e_PDFA1_7_3:173,e_PDFA1_7_4:174,e_PDFA1_8_1:181,e_PDFA1_8_2:182,e_PDFA1_8_3:183,e_PDFA1_8_4:184,e_PDFA1_8_5:185,e_PDFA1_8_6:186,e_PDFA1_10_1:1101,e_PDFA1_11_1:1111,e_PDFA1_11_2:1112,e_PDFA1_12_1:1121,e_PDFA1_12_2:1122,e_PDFA1_12_3:1123,e_PDFA1_12_4:1124,e_PDFA1_12_5:1125,e_PDFA1_12_6:1126,e_PDFA1_13_1:1131,e_PDFA2_2_1:221,e_PDFA2_3_2:232,e_PDFA2_3_3:233,e_PDFA2_3_3_1:2331,e_PDFA2_3_3_2:2332,e_PDFA2_3_4_1:2341,e_PDFA2_4_1:241,e_PDFA2_4_2:242,e_PDFA2_4_3:243,e_PDFA2_4_4:244,e_PDFA2_5_1:251,e_PDFA2_5_2:252,e_PDFA2_6_1:261,e_PDFA2_7_1:271,e_PDFA2_8_1:281,e_PDFA2_9_1:291,e_PDFA2_10_1:2101,e_PDFA3_2_1:321,e_PDFA3_3_1:331,e_PDFA3_3_2:332,e_PDFA3_3_3_1:3331,e_PDFA3_3_3_2:3332,e_PDFA3_4_1:341,e_PDFA3_5_1:351,e_PDFA3_5_2:352,e_PDFA3_5_3:353,e_PDFA3_5_4:354,e_PDFA3_5_5:355,e_PDFA3_5_6:356,e_PDFA3_6_1:361,e_PDFA3_7_1:371,e_PDFA3_7_2:372,e_PDFA3_7_3:373,e_PDFA4_1:41,e_PDFA4_2:42,e_PDFA4_3:43,e_PDFA4_4:44,e_PDFA4_5:45,e_PDFA4_6:46,e_PDFA5_2_1:521,e_PDFA5_2_2:522,e_PDFA5_2_3:523,e_PDFA5_2_4:524,e_PDFA5_2_5:525,e_PDFA5_2_6:526,e_PDFA5_2_7:527,e_PDFA5_2_8:528,e_PDFA5_2_9:529,e_PDFA5_2_10:5210,e_PDFA5_2_11:5211,e_PDFA5_3_1:531,e_PDFA5_3_2_1:5321,e_PDFA5_3_2_2:5322,e_PDFA5_3_2_3:5323,e_PDFA5_3_2_4:5324,e_PDFA5_3_2_5:5325,e_PDFA5_3_3_1:5331,e_PDFA5_3_3_2:5332,e_PDFA5_3_3_3:5333,e_PDFA5_3_3_4:5334,e_PDFA5_3_4_0:5340,e_PDFA5_3_4_1:5341,e_PDFA5_3_4_2:5342,e_PDFA5_3_4_3:5343,e_PDFA6_1_1:611,e_PDFA6_1_2:612,e_PDFA6_2_1:621,e_PDFA6_2_2:622,e_PDFA6_2_3:623,e_PDFA7_2_1:721,e_PDFA7_2_2:722,e_PDFA7_2_3:723,e_PDFA7_2_4:724,e_PDFA7_2_5:725,e_PDFA7_3_1:731,e_PDFA7_3_2:732,e_PDFA7_3_3:733,e_PDFA7_3_4:734,e_PDFA7_3_5:735,e_PDFA7_3_6:736,e_PDFA7_3_7:737,e_PDFA7_3_8:738,e_PDFA7_3_9:739,e_PDFA7_5_1:751,e_PDFA7_8_1:781,e_PDFA7_8_2:782,e_PDFA7_8_3:783,e_PDFA7_8_4:784,e_PDFA7_8_5:785,e_PDFA7_8_6:786,e_PDFA7_8_7:787,e_PDFA7_8_8:788,e_PDFA7_8_9:789,e_PDFA7_8_10:7810,e_PDFA7_8_11:7811,e_PDFA7_8_12:7812,e_PDFA7_8_13:7813,e_PDFA7_8_14:7814,e_PDFA7_8_15:7815,e_PDFA7_8_16:7816,e_PDFA7_8_17:7817,e_PDFA7_8_18:7818,e_PDFA7_8_19:7819,e_PDFA7_8_20:7820,e_PDFA7_8_21:7821,e_PDFA7_8_22:7822,e_PDFA7_8_23:7823,e_PDFA7_8_24:7824,e_PDFA7_8_25:7825,e_PDFA7_8_26:7826,e_PDFA7_8_27:7827,e_PDFA7_8_28:7828,e_PDFA7_8_29:7829,e_PDFA7_8_30:7830,e_PDFA7_8_31:7831,e_PDFA7_11_1:7111,e_PDFA7_11_2:7112,e_PDFA7_11_3:7113,e_PDFA7_11_4:7114,e_PDFA7_11_5:7115,e_PDFA9_1:91,e_PDFA9_2:92,e_PDFA9_3:93,e_PDFA9_4:94,e_PDFA3_8_1:381,e_PDFA8_2_2:822,e_PDFA8_3_3_1:8331,e_PDFA8_3_3_2:8332,e_PDFA8_3_4_1:8341,e_PDFA1_2_3:123,e_PDFA1_10_2:1102,e_PDFA1_10_3:1103,e_PDFA1_12_10:11210,e_PDFA1_13_5:1135,e_PDFA2_3_10:2310,e_PDFA2_4_2_10:24220,e_PDFA2_4_2_11:24221,e_PDFA2_4_2_12:24222,e_PDFA2_4_2_13:24223,e_PDFA2_5_10:2510,e_PDFA2_5_11:2511,e_PDFA2_5_12:2512,e_PDFA2_8_3_1:2831,e_PDFA2_8_3_2:2832,e_PDFA2_8_3_3:2833,e_PDFA2_8_3_4:2834,e_PDFA2_8_3_5:2835,e_PDFA2_10_20:21020,e_PDFA2_10_21:21021,e_PDFA11_0_0:11e3,e_PDFA6_2_11_8:62118,e_PDFA8_1:81,e_PDFA_3E1:1,e_PDFA_3E2:2,e_PDFA_3E3:3,e_PDFA_4_6_1_3_4:46134,e_PDFA_4_6_1_3_5:46135,e_PDFA_4_6_1_6_1_3:461613,e_PDFA_4_6_7_3_5:46735,e_PDFA_4_6_2_5_3:46253,e_PDFA_4_6_6_3_1:46631,e_PDFA_4_6_1_12_1:461121,e_PDFA_4_6_2_4_2_3:462423,e_PDFA_4_6_2_2_3:46223,e_PDFA_4_6_9_5:4695,e_PDFA_4_6_2_10_6_1:4621061,e_PDFA_4_6_2_10_6_4:4621064,e_PDFA_LAST:4621065},p.ContentItem.Type={e_MCR:0,e_MCID:1,e_OBJR:2,e_Unknown:3},p.Action.Type={e_GoTo:0,e_GoToR:1,e_GoToE:2,e_Launch:3,e_Thread:4,e_URI:5,e_Sound:6,e_Movie:7,e_Hide:8,e_Named:9,e_SubmitForm:10,e_ResetForm:11,e_ImportData:12,e_JavaScript:13,e_SetOCGState:14,e_Rendition:15,e_Trans:16,e_GoTo3DView:17,e_RichMediaExecute:18,e_Unknown:19},p.Action.FormActionFlag={e_exclude:0,e_include_no_value_fields:1,e_export_format:2,e_get_method:3,e_submit_coordinates:4,e_xfdf:5,e_include_append_saves:6,e_include_annotations:7,e_submit_pdf:8,e_canonical_format:9,e_excl_non_user_annots:10,e_excl_F_key:11,e_embed_form:13},p.Page.EventType={e_action_trigger_page_open:11,e_action_trigger_page_close:12},p.Page.Box={e_media:0,e_crop:1,e_bleed:2,e_trim:3,e_art:4,e_user_crop:5},p.Page.Rotate={e_0:0,e_90:1,e_180:2,e_270:3},p.Annot.EventType={e_action_trigger_activate:0,e_action_trigger_annot_enter:1,e_action_trigger_annot_exit:2,e_action_trigger_annot_down:3,e_action_trigger_annot_up:4,e_action_trigger_annot_focus:5,e_action_trigger_annot_blur:6,e_action_trigger_annot_page_open:7,e_action_trigger_annot_page_close:8,e_action_trigger_annot_page_visible:9,e_action_trigger_annot_page_invisible:10},p.Annot.Type={e_Text:0,e_Link:1,e_FreeText:2,e_Line:3,e_Square:4,e_Circle:5,e_Polygon:6,e_Polyline:7,e_Highlight:8,e_Underline:9,e_Squiggly:10,e_StrikeOut:11,e_Stamp:12,e_Caret:13,e_Ink:14,e_Popup:15,e_FileAttachment:16,e_Sound:17,e_Movie:18,e_Widget:19,e_Screen:20,e_PrinterMark:21,e_TrapNet:22,e_Watermark:23,e_3D:24,e_Redact:25,e_Projection:26,e_RichMedia:27,e_Unknown:28},p.Annot.Flag={e_invisible:0,e_hidden:1,e_print:2,e_no_zoom:3,e_no_rotate:4,e_no_view:5,e_annot_read_only:6,e_locked:7,e_toggle_no_view:8,e_locked_contents:9},p.AnnotBorderStyle.Style={e_solid:0,e_dashed:1,e_beveled:2,e_inset:3,e_underline:4},p.Annot.State={e_normal:0,e_rollover:1,e_down:2},p.LineAnnot.EndingStyle={e_Square:0,e_Circle:1,e_Diamond:2,e_OpenArrow:3,e_ClosedArrow:4,e_Butt:5,e_ROpenArrow:6,e_RClosedArrow:7,e_Slash:8,e_None:9,e_Unknown:10},p.LineAnnot.IntentType={e_LineArrow:0,e_LineDimension:1,e_null:2},p.LineAnnot.CapPos={e_Inline:0,e_Top:1},p.FileAttachmentAnnot.Icon={e_Graph:0,e_PushPin:1,e_Paperclip:2,e_Tag:3,e_Unknown:4},p.FreeTextAnnot.IntentName={e_FreeText:0,e_FreeTextCallout:1,e_FreeTextTypeWriter:2,e_Unknown:3},p.LinkAnnot.HighlightingMode={e_none:0,e_invert:1,e_outline:2,e_push:3},p.MarkupAnnot.BorderEffect={e_None:0,e_Cloudy:1},p.PolyLineAnnot.IntentType={e_PolygonCloud:0,e_PolyLineDimension:1,e_PolygonDimension:2,e_Unknown:3},p.RedactionAnnot.QuadForm={e_LeftJustified:0,e_Centered:1,e_RightJustified:2,e_None:3},p.RubberStampAnnot.Icon={e_Approved:0,e_Experimental:1,e_NotApproved:2,e_AsIs:3,e_Expired:4,e_NotForPublicRelease:5,e_Confidential:6,e_Final:7,e_Sold:8,e_Departmental:9,e_ForComment:10,e_TopSecret:11,e_ForPublicRelease:12,e_Draft:13,e_Unknown:14},p.ScreenAnnot.ScaleType={e_Anamorphic:0,e_Proportional:1},p.ScreenAnnot.ScaleCondition={e_Always:0,e_WhenBigger:1,e_WhenSmaller:2,e_Never:3},p.ScreenAnnot.IconCaptionRelation={e_NoIcon:0,e_NoCaption:1,e_CBelowI:2,e_CAboveI:3,e_CRightILeft:4,e_CLeftIRight:5,e_COverlayI:6},p.SoundAnnot.Icon={e_Speaker:0,e_Mic:1,e_Unknown:2},p.TextAnnot.Icon={e_Comment:0,e_Key:1,e_Help:2,e_NewParagraph:3,e_Paragraph:4,e_Insert:5,e_Note:6,e_Unknown:7},p.WidgetAnnot.HighlightingMode={e_none:0,e_invert:1,e_outline:2,e_push:3,e_toggle:4},p.WidgetAnnot.ScaleType={e_Anamorphic:0,e_Proportional:1},p.WidgetAnnot.IconCaptionRelation={e_NoIcon:0,e_NoCaption:1,e_CBelowI:2,e_CAboveI:3,e_CRightILeft:4,e_CLeftIRight:5,e_COverlayI:6},p.WidgetAnnot.ScaleCondition={e_Always:0,e_WhenBigger:1,e_WhenSmaller:2,e_Never:3},p.ColorSpace.Type={e_device_gray:0,e_device_rgb:1,e_device_cmyk:2,e_cal_gray:3,e_cal_rgb:4,e_lab:5,e_icc:6,e_indexed:7,e_pattern:8,e_separation:9,e_device_n:10,e_null:11},p.Convert.PrinterMode={e_auto:0,e_interop_only:1,e_printer_only:2,e_prefer_builtin_converter:3},p.Destination.FitType={e_XYZ:0,e_Fit:1,e_FitH:2,e_FitV:3,e_FitR:4,e_FitB:5,e_FitBH:6,e_FitBV:7},p.GState.Attribute={e_transform:0,e_rendering_intent:1,e_stroke_cs:2,e_stroke_color:3,e_fill_cs:4,e_fill_color:5,e_line_width:6,e_line_cap:7,e_line_join:8,e_flatness:9,e_miter_limit:10,e_dash_pattern:11,e_char_spacing:12,e_word_spacing:13,e_horizontal_scale:14,e_leading:15,e_font:16,e_font_size:17,e_text_render_mode:18,e_text_rise:19,e_text_knockout:20,e_text_pos_offset:21,e_blend_mode:22,e_opacity_fill:23,e_opacity_stroke:24,e_alpha_is_shape:25,e_soft_mask:26,e_smoothnes:27,e_auto_stoke_adjust:28,e_stroke_overprint:29,e_fill_overprint:30,e_overprint_mode:31,e_transfer_funct:32,e_BG_funct:33,e_UCR_funct:34,e_halftone:35,e_null:36},p.GState.LineCap={e_butt_cap:0,e_round_cap:1,e_square_cap:2},p.GState.LineJoin={e_miter_join:0,e_round_join:1,e_bevel_join:2},p.GState.TextRenderingMode={e_fill_text:0,e_stroke_text:1,e_fill_stroke_text:2,e_invisible_text:3,e_fill_clip_text:4,e_stroke_clip_text:5,e_fill_stroke_clip_text:6,e_clip_text:7},p.GState.RenderingIntent={e_absolute_colorimetric:0,e_relative_colorimetric:1,e_saturation:2,e_perceptual:3},p.GState.BlendMode={e_bl_compatible:0,e_bl_normal:1,e_bl_multiply:2,e_bl_screen:3,e_bl_difference:4,e_bl_darken:5,e_bl_lighten:6,e_bl_color_dodge:7,e_bl_color_burn:8,e_bl_exclusion:9,e_bl_hard_light:10,e_bl_overlay:11,e_bl_soft_light:12,e_bl_luminosity:13,e_bl_hue:14,e_bl_saturation:15,e_bl_color:16},p.Element.Type={e_null:0,e_path:1,e_text_begin:2,e_text:3,e_text_new_line:4,e_text_end:5,e_image:6,e_inline_image:7,e_shading:8,e_form:9,e_group_begin:10,e_group_end:11,e_marked_content_begin:12,e_marked_content_end:13,e_marked_content_point:14},p.Element.PathSegmentType={e_moveto:1,e_lineto:2,e_cubicto:3,e_conicto:4,e_rect:5,e_closepath:6},p.ShapedText.ShapingStatus={e_FullShaping:0,e_PartialShaping:1,e_NoShaping:2},p.ShapedText.FailureReason={e_NoFailure:0,e_UnsupportedFontType:1,e_NotIndexEncoded:2,e_FontDataNotFound:3},p.ElementWriter.WriteMode={e_underlay:0,e_overlay:1,e_replacement:2},p.Flattener.Threshold={e_very_strict:0,e_strict:1,e_default:2,e_keep_most:3,e_keep_all:4},p.Flattener.Mode={e_simple:0,e_fast:1},p.Font.StandardType1Font={e_times_roman:0,e_times_bold:1,e_times_italic:2,e_times_bold_italic:3,e_helvetica:4,e_helvetica_bold:5,e_helvetica_oblique:6,e_helvetica_bold_oblique:7,e_courier:8,e_courier_bold:9,e_courier_oblique:10,e_courier_bold_oblique:11,e_symbol:12,e_zapf_dingbats:13,e_null:14},p.Font.Encoding={e_IdentityH:0,e_Indices:1},p.Font.Type={e_Type1:0,e_TrueType:1,e_MMType1:2,e_Type3:3,e_Type0:4,e_CIDType0:5,e_CIDType2:6},p.Function.Type={e_sampled:0,e_exponential:2,e_stitching:3,e_postscript:4},p.Image.InputFilter={e_none:0,e_jpeg:1,e_jp2:2,e_flate:3,e_g3:4,e_g4:5,e_ascii_hex:6},p.PageLabel.Style={e_decimal:0,e_roman_uppercase:1,e_roman_lowercase:2,e_alphabetic_uppercase:3,e_alphabetic_lowercase:4,e_none:5},p.PageSet.Filter={e_all:0,e_even:1,e_odd:2},p.PatternColor.Type={e_uncolored_tiling_pattern:0,e_colored_tiling_pattern:1,e_shading:2,e_null:3},p.PatternColor.TilingType={e_constant_spacing:0,e_no_distortion:1,e_constant_spacing_fast_fill:2},p.GeometryCollection.SnappingMode={e_DefaultSnapMode:14,e_PointOnLine:1,e_LineMidpoint:2,e_LineIntersection:4,e_PathEndpoint:8},p.DigestAlgorithm.Type={e_SHA1:0,e_SHA256:1,e_SHA384:2,e_SHA512:3,e_RIPEMD160:4,e_unknown_digest_algorithm:5},p.ObjectIdentifier.Predefined={e_commonName:0,e_surname:1,e_countryName:2,e_localityName:3,e_stateOrProvinceName:4,e_streetAddress:5,e_organizationName:6,e_organizationalUnitName:7,e_SHA1:8,e_SHA256:9,e_SHA384:10,e_SHA512:11,e_RIPEMD160:12,e_RSA_encryption_PKCS1:13,e_RSASSA_PSS:14,e_MGF1:15},p.DigitalSignatureField.SubFilterType={e_adbe_x509_rsa_sha1:0,e_adbe_pkcs7_detached:1,e_adbe_pkcs7_sha1:2,e_ETSI_CAdES_detached:3,e_ETSI_RFC3161:4,e_unknown:5,e_absent:6},p.DigitalSignatureField.DocumentPermissions={e_no_changes_allowed:1,e_formfilling_signing_allowed:2,e_annotating_formfilling_signing_allowed:3,e_unrestricted:4},p.DigitalSignatureField.FieldPermissions={e_lock_all:0,e_include:1,e_exclude:2},p.PDFDoc.EventType={e_action_trigger_doc_will_close:17,e_action_trigger_doc_will_save:18,e_action_trigger_doc_did_save:19,e_action_trigger_doc_will_print:20,e_action_trigger_doc_did_print:21},p.PDFDoc.InsertFlag={e_none:0,e_insert_bookmark:1},p.PDFDoc.ExtractFlag={e_forms_only:0,e_annots_only:1,e_both:2},p.PDFDoc.SignaturesVerificationStatus={e_unsigned:0,e_failure:1,e_untrusted:2,e_unsupported:3,e_verified:4},p.PDFDocViewPrefs.PageMode={e_UseNone:0,e_UseThumbs:1,e_UseBookmarks:2,e_FullScreen:3,e_UseOC:4,e_UseAttachments:5},p.PDFDocViewPrefs.PageLayout={e_Default:0,e_SinglePage:1,e_OneColumn:2,e_TwoColumnLeft:3,e_TwoColumnRight:4,e_TwoPageLeft:5,e_TwoPageRight:6},p.PDFDocViewPrefs.ViewerPref={e_HideToolbar:0,e_HideMenubar:1,e_HideWindowUI:2,e_FitWindow:3,e_CenterWindow:4,e_DisplayDocTitle:5},p.PDFRasterizer.Type={e_BuiltIn:0,e_GDIPlus:1},p.PDFRasterizer.OverprintPreviewMode={e_op_off:0,e_op_on:1,e_op_pdfx_on:2},p.PDFRasterizer.ColorPostProcessMode={e_postprocess_none:0,e_postprocess_invert:1,e_postprocess_gradient_map:2,e_postprocess_night_mode:3},p.PDFDraw.PixelFormat={e_rgba:0,e_bgra:1,e_rgb:2,e_bgr:3,e_gray:4,e_gray_alpha:5,e_cmyk:6},p.CMSType={e_lcms:0,e_icm:1,e_no_cms:2},p.CharacterOrdering={e_Identity:0,e_Japan1:1,e_Japan2:2,e_GB1:3,e_CNS1:4,e_Korea1:5},p.LogLevel={e_LogLevel_Off:-1,e_LogLevel_Fatal:5,e_LogLevel_Error:4,e_LogLevel_Warning:3,e_LogLevel_Info:2,e_LogLevel_Trace:1,e_LogLevel_Debug:0},p.ConnectionErrorHandlingMode={e_continue:0,e_continue_unless_switching_to_demo:1,e_stop:2},p.Shading.Type={e_function_shading:0,e_axial_shading:1,e_radial_shading:2,e_free_gouraud_shading:3,e_lattice_gouraud_shading:4,e_coons_shading:5,e_tensor_shading:6,e_null:7},p.Stamper.SizeType={e_relative_scale:1,e_absolute_size:2,e_font_size:3},p.Stamper.TextAlignment={e_align_left:-1,e_align_center:0,e_align_right:1},p.Stamper.HorizontalAlignment={e_horizontal_left:-1,e_horizontal_center:0,e_horizontal_right:1},p.Stamper.VerticalAlignment={e_vertical_bottom:-1,e_vertical_center:0,e_vertical_top:1},p.TextExtractor.ProcessingFlags={e_no_ligature_exp:1,e_no_dup_remove:2,e_punct_break:4,e_remove_hidden_text:8,e_no_invisible_text:16,e_no_watermarks:128,e_extract_using_zorder:256},p.TextExtractor.XMLOutputFlags={e_words_as_elements:1,e_output_bbox:2,e_output_style_info:4},p.TextSearch.ResultCode={e_done:0,e_page:1,e_found:2},p.TextSearch.Mode={e_reg_expression:1,e_case_sensitive:2,e_whole_word:4,e_search_up:8,e_page_stop:16,e_highlight:32,e_ambient_string:64},p.Obj.Type={e_null:0,e_bool:1,e_number:2,e_name:3,e_string:4,e_dict:5,e_array:6,e_stream:7},p.SDFDoc.SaveOptions={e_incremental:1,e_remove_unused:2,e_hex_strings:4,e_omit_xref:8,e_linearized:16,e_compatibility:32},p.SecurityHandler.Permission={e_owner:1,e_doc_open:2,e_doc_modify:3,e_print:4,e_print_high:5,e_extract_content:6,e_mod_annot:7,e_fill_forms:8,e_access_support:9,e_assemble_doc:10},p.SecurityHandler.AlgorithmType={e_RC4_40:1,e_RC4_128:2,e_AES:3,e_AES_256:4},p.VerificationOptions.SecurityLevel={e_compatibility_and_archiving:0,e_maximum:1},p.VerificationOptions.TimeMode={e_signing:0,e_timestamp:1,e_current:2},p.VerificationOptions.CertificateTrustFlag={e_signing_trust:1,e_certification_trust:2,e_dynamic_content:4,e_javascript:16,e_identity:32,e_trust_anchor:64,e_default_trust:97,e_complete_trust:119},p.VerificationResult.DocumentStatus={e_no_error:0,e_corrupt_file:1,e_unsigned:2,e_bad_byteranges:3,e_corrupt_cryptographic_contents:4},p.VerificationResult.DigestStatus={e_digest_invalid:0,e_digest_verified:1,e_digest_verification_disabled:2,e_weak_digest_algorithm_but_digest_verifiable:3,e_no_digest_status:4,e_unsupported_encoding:5},p.VerificationResult.TrustStatus={e_trust_verified:0,e_untrusted:1,e_trust_verification_disabled:2,e_no_trust_status:3},p.VerificationResult.ModificationPermissionsStatus={e_invalidated_by_disallowed_changes:0,e_has_allowed_changes:1,e_unmodified:2,e_permissions_verification_disabled:3,e_no_permissions_status:4},p.DisallowedChange.Type={e_form_filled:0,e_digital_signature_signed:1,e_page_template_instantiated:2,e_annotation_created_or_updated_or_deleted:3,e_other:4,e_unknown:5},p.Paragraph.TextJustification={e_text_justification_invalid:0,e_text_justify_left:1,e_text_justify_right:2,e_text_justify_center:3},p.TableCell.AlignmentVertical={e_alignment_vert_invalid:0,e_alignment_top:1,e_alignment_center:2,e_alignment_bottom:3},p.TableCell.AlignmentHorizontal={e_alignment_horz_invalid:0,e_alignment_left:1,e_alignment_middle:2,e_alignment_right:3},p.List.NumberFormat={e_none:0,e_decimal:1,e_lower_roman:2,e_upper_roman:3,e_lower_letter:4,e_upper_letter:5,e_ordinal:6,e_ordinal_text:7,e_chinese_counting:8,e_chinese_counting_thousand:9,e_cardinal_text:10,e_decimal_zero:11},p.Iterator.prototype.hasNext=function(){return p.sendWithPromise("Iterator.hasNext",{itr:this.id})},p.Iterator.prototype.next=function(){return p.sendWithPromise("Iterator.next",{itr:this.id})},p.DictIterator.prototype.hasNext=function(){return p.sendWithPromise("DictIterator.hasNext",{itr:this.id})},p.DictIterator.prototype.key=function(){return p.sendWithPromise("DictIterator.key",{itr:this.id}).then(function(t){return _(p.Obj,t)})},p.DictIterator.prototype.value=function(){return p.sendWithPromise("DictIterator.value",{itr:this.id}).then(function(t){return _(p.Obj,t)})},p.DictIterator.prototype.next=function(){return p.sendWithPromise("DictIterator.next",{itr:this.id})},p.Matrix2D.prototype.copy=function(){return F("copy",this.yieldFunction),p.sendWithPromise("Matrix2D.copy",{m:this}).then(function(t){return new p.Matrix2D(t)})},p.Matrix2D.prototype.set=function(t,e,n,i,r,o){P(arguments.length,6,"set","(number, number, number, number, number, number)",[[t,"number"],[e,"number"],[n,"number"],[i,"number"],[r,"number"],[o,"number"]]),F("set",this.yieldFunction);var s=this;return this.yieldFunction="Matrix2D.set",p.sendWithPromise("Matrix2D.set",{matrix:this,a:t,b:e,c:n,d:i,h:r,v:o}).then(function(t){s.yieldFunction=void 0,W(t,s)})},p.Matrix2D.prototype.concat=function(t,e,n,i,r,o){P(arguments.length,6,"concat","(number, number, number, number, number, number)",[[t,"number"],[e,"number"],[n,"number"],[i,"number"],[r,"number"],[o,"number"]]),F("concat",this.yieldFunction);var s=this;return this.yieldFunction="Matrix2D.concat",p.sendWithPromise("Matrix2D.concat",{matrix:this,a:t,b:e,c:n,d:i,h:r,v:o}).then(function(t){s.yieldFunction=void 0,W(t,s)})},p.Matrix2D.prototype.equals=function(t){return P(arguments.length,1,"equals","(PDFNet.Matrix2D)",[[t,"Structure",p.Matrix2D,"Matrix2D"]]),F("equals",this.yieldFunction),b("equals",[[t,0]]),p.sendWithPromise("Matrix2D.equals",{m1:this,m2:t})},p.Matrix2D.prototype.inverse=function(){return F("inverse",this.yieldFunction),p.sendWithPromise("Matrix2D.inverse",{matrix:this}).then(function(t){return new p.Matrix2D(t)})},p.Matrix2D.prototype.translate=function(t,e){P(arguments.length,2,"translate","(number, number)",[[t,"number"],[e,"number"]]),F("translate",this.yieldFunction);var n=this;return this.yieldFunction="Matrix2D.translate",p.sendWithPromise("Matrix2D.translate",{matrix:this,h:t,v:e}).then(function(t){n.yieldFunction=void 0,W(t,n)})},p.Matrix2D.prototype.preTranslate=function(t,e){P(arguments.length,2,"preTranslate","(number, number)",[[t,"number"],[e,"number"]]),F("preTranslate",this.yieldFunction);var n=this;return this.yieldFunction="Matrix2D.preTranslate",p.sendWithPromise("Matrix2D.preTranslate",{matrix:this,h:t,v:e}).then(function(t){n.yieldFunction=void 0,W(t,n)})},p.Matrix2D.prototype.postTranslate=function(t,e){P(arguments.length,2,"postTranslate","(number, number)",[[t,"number"],[e,"number"]]),F("postTranslate",this.yieldFunction);var n=this;return this.yieldFunction="Matrix2D.postTranslate",p.sendWithPromise("Matrix2D.postTranslate",{matrix:this,h:t,v:e}).then(function(t){n.yieldFunction=void 0,W(t,n)})},p.Matrix2D.prototype.scale=function(t,e){P(arguments.length,2,"scale","(number, number)",[[t,"number"],[e,"number"]]),F("scale",this.yieldFunction);var n=this;return this.yieldFunction="Matrix2D.scale",p.sendWithPromise("Matrix2D.scale",{matrix:this,h:t,v:e}).then(function(t){n.yieldFunction=void 0,W(t,n)})},p.Matrix2D.createZeroMatrix=function(){return p.sendWithPromise("matrix2DCreateZeroMatrix",{}).then(function(t){return new p.Matrix2D(t)})},p.Matrix2D.createIdentityMatrix=function(){return p.sendWithPromise("matrix2DCreateIdentityMatrix",{}).then(function(t){return new p.Matrix2D(t)})},p.Matrix2D.createRotationMatrix=function(t){return P(arguments.length,1,"createRotationMatrix","(number)",[[t,"number"]]),p.sendWithPromise("matrix2DCreateRotationMatrix",{angle:t}).then(function(t){return new p.Matrix2D(t)})},p.Matrix2D.prototype.multiply=function(t){P(arguments.length,1,"multiply","(PDFNet.Matrix2D)",[[t,"Structure",p.Matrix2D,"Matrix2D"]]),F("multiply",this.yieldFunction),b("multiply",[[t,0]]);var e=this;return this.yieldFunction="Matrix2D.multiply",p.sendWithPromise("Matrix2D.multiply",{matrix:this,m:t}).then(function(t){e.yieldFunction=void 0,W(t,e)})},p.Field.create=function(t){return P(arguments.length,1,"create","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("fieldCreate",{field_dict:t.id}).then(function(t){return new p.Field(t)})},p.Field.prototype.isValid=function(){return F("isValid",this.yieldFunction),p.sendWithPromise("Field.isValid",{field:this})},p.Field.prototype.getType=function(){return F("getType",this.yieldFunction),p.sendWithPromise("Field.getType",{field:this})},p.Field.prototype.getValue=function(){return F("getValue",this.yieldFunction),p.sendWithPromise("Field.getValue",{field:this}).then(function(t){return _(p.Obj,t)})},p.Field.prototype.getValueAsString=function(){return F("getValueAsString",this.yieldFunction),p.sendWithPromise("Field.getValueAsString",{field:this})},p.Field.prototype.getDefaultValueAsString=function(){return F("getDefaultValueAsString",this.yieldFunction),p.sendWithPromise("Field.getDefaultValueAsString",{field:this})},p.Field.prototype.setValueAsString=function(t){P(arguments.length,1,"setValueAsString","(string)",[[t,"string"]]),F("setValueAsString",this.yieldFunction);var e=this;return this.yieldFunction="Field.setValueAsString",p.sendWithPromise("Field.setValueAsString",{field:this,value:t}).then(function(t){return e.yieldFunction=void 0,t.result=S(p.ViewChangeCollection,t.result),W(t.field,e),t.result})},p.Field.prototype.setValue=function(t){P(arguments.length,1,"setValue","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),F("setValue",this.yieldFunction);var e=this;return this.yieldFunction="Field.setValue",p.sendWithPromise("Field.setValue",{field:this,value:t.id}).then(function(t){return e.yieldFunction=void 0,t.result=S(p.ViewChangeCollection,t.result),W(t.field,e),t.result})},p.Field.prototype.setValueAsBool=function(t){P(arguments.length,1,"setValueAsBool","(boolean)",[[t,"boolean"]]),F("setValueAsBool",this.yieldFunction);var e=this;return this.yieldFunction="Field.setValueAsBool",p.sendWithPromise("Field.setValueAsBool",{field:this,value:t}).then(function(t){return e.yieldFunction=void 0,t.result=S(p.ViewChangeCollection,t.result),W(t.field,e),t.result})},p.Field.prototype.getTriggerAction=function(t){return P(arguments.length,1,"getTriggerAction","(number)",[[t,"number"]]),F("getTriggerAction",this.yieldFunction),p.sendWithPromise("Field.getTriggerAction",{field:this,trigger:t}).then(function(t){return _(p.Obj,t)})},p.Field.prototype.getValueAsBool=function(){return F("getValueAsBool",this.yieldFunction),p.sendWithPromise("Field.getValueAsBool",{field:this})},p.Field.prototype.refreshAppearance=function(){F("refreshAppearance",this.yieldFunction);var e=this;return this.yieldFunction="Field.refreshAppearance",p.sendWithPromise("Field.refreshAppearance",{field:this}).then(function(t){e.yieldFunction=void 0,W(t,e)})},p.Field.prototype.eraseAppearance=function(){F("eraseAppearance",this.yieldFunction);var e=this;return this.yieldFunction="Field.eraseAppearance",p.sendWithPromise("Field.eraseAppearance",{field:this}).then(function(t){e.yieldFunction=void 0,W(t,e)})},p.Field.prototype.getDefaultValue=function(){return F("getDefaultValue",this.yieldFunction),p.sendWithPromise("Field.getDefaultValue",{field:this}).then(function(t){return _(p.Obj,t)})},p.Field.prototype.getName=function(){return F("getName",this.yieldFunction),p.sendWithPromise("Field.getName",{field:this})},p.Field.prototype.getPartialName=function(){return F("getPartialName",this.yieldFunction),p.sendWithPromise("Field.getPartialName",{field:this})},p.Field.prototype.rename=function(t){P(arguments.length,1,"rename","(string)",[[t,"string"]]),F("rename",this.yieldFunction);var e=this;return this.yieldFunction="Field.rename",p.sendWithPromise("Field.rename",{field:this,field_name:t}).then(function(t){e.yieldFunction=void 0,W(t,e)})},p.Field.prototype.isAnnot=function(){return F("isAnnot",this.yieldFunction),p.sendWithPromise("Field.isAnnot",{field:this})},p.Field.prototype.useSignatureHandler=function(t){P(arguments.length,1,"useSignatureHandler","(number)",[[t,"number"]]),F("useSignatureHandler",this.yieldFunction);var e=this;return this.yieldFunction="Field.useSignatureHandler",p.sendWithPromise("Field.useSignatureHandler",{field:this,signature_handler_id:t}).then(function(t){return e.yieldFunction=void 0,t.result=_(p.Obj,t.result),W(t.field,e),t.result})},p.Field.prototype.getFlag=function(t){return P(arguments.length,1,"getFlag","(number)",[[t,"number"]]),F("getFlag",this.yieldFunction),p.sendWithPromise("Field.getFlag",{field:this,flag:t})},p.Field.prototype.setFlag=function(t,e){P(arguments.length,2,"setFlag","(number, boolean)",[[t,"number"],[e,"boolean"]]),F("setFlag",this.yieldFunction);var n=this;return this.yieldFunction="Field.setFlag",p.sendWithPromise("Field.setFlag",{field:this,flag:t,value:e}).then(function(t){n.yieldFunction=void 0,W(t,n)})},p.Field.prototype.getJustification=function(){F("getJustification",this.yieldFunction);var e=this;return this.yieldFunction="Field.getJustification",p.sendWithPromise("Field.getJustification",{field:this}).then(function(t){return e.yieldFunction=void 0,W(t.field,e),t.result})},p.Field.prototype.setJustification=function(t){P(arguments.length,1,"setJustification","(number)",[[t,"number"]]),F("setJustification",this.yieldFunction);var e=this;return this.yieldFunction="Field.setJustification",p.sendWithPromise("Field.setJustification",{field:this,j:t}).then(function(t){e.yieldFunction=void 0,W(t,e)})},p.Field.prototype.setMaxLen=function(t){P(arguments.length,1,"setMaxLen","(number)",[[t,"number"]]),F("setMaxLen",this.yieldFunction);var e=this;return this.yieldFunction="Field.setMaxLen",p.sendWithPromise("Field.setMaxLen",{field:this,max_len:t}).then(function(t){e.yieldFunction=void 0,W(t,e)})},p.Field.prototype.getMaxLen=function(){return F("getMaxLen",this.yieldFunction),p.sendWithPromise("Field.getMaxLen",{field:this})},p.Field.prototype.getDefaultAppearance=function(){F("getDefaultAppearance",this.yieldFunction);var e=this;return this.yieldFunction="Field.getDefaultAppearance",p.sendWithPromise("Field.getDefaultAppearance",{field:this}).then(function(t){return e.yieldFunction=void 0,t.result=_(p.GState,t.result),W(t.field,e),t.result})},p.Field.prototype.getUpdateRect=function(){return F("getUpdateRect",this.yieldFunction),p.sendWithPromise("Field.getUpdateRect",{field:this}).then(function(t){return new p.Rect(t)})},p.Field.prototype.flatten=function(t){P(arguments.length,1,"flatten","(PDFNet.Page)",[[t,"Object",p.Page,"Page"]]),F("flatten",this.yieldFunction);var e=this;return this.yieldFunction="Field.flatten",p.sendWithPromise("Field.flatten",{field:this,page:t.id}).then(function(t){e.yieldFunction=void 0,W(t,e)})},p.Field.prototype.findInheritedAttribute=function(t){return P(arguments.length,1,"findInheritedAttribute","(string)",[[t,"string"]]),F("findInheritedAttribute",this.yieldFunction),p.sendWithPromise("Field.findInheritedAttribute",{field:this,attrib:t}).then(function(t){return _(p.Obj,t)})},p.Field.prototype.getSDFObj=function(){return F("getSDFObj",this.yieldFunction),p.sendWithPromise("Field.getSDFObj",{field:this}).then(function(t){return _(p.Obj,t)})},p.Field.prototype.getOptCount=function(){return F("getOptCount",this.yieldFunction),p.sendWithPromise("Field.getOptCount",{field:this})},p.Field.prototype.getOpt=function(t){return P(arguments.length,1,"getOpt","(number)",[[t,"number"]]),F("getOpt",this.yieldFunction),p.sendWithPromise("Field.getOpt",{field:this,index:t})},p.Field.prototype.isLockedByDigitalSignature=function(){return F("isLockedByDigitalSignature",this.yieldFunction),p.sendWithPromise("Field.isLockedByDigitalSignature",{field:this})},p.FDFDoc.create=function(){return p.sendWithPromise("fdfDocCreate",{}).then(function(t){return S(p.FDFDoc,t)})},p.FDFDoc.createFromStream=function(t){return P(arguments.length,1,"createFromStream","(PDFNet.Filter)",[[t,"Object",p.Filter,"Filter"]]),0!=t.id&&A(t.id),p.sendWithPromise("fdfDocCreateFromStream",{no_own_stream:t.id}).then(function(t){return S(p.FDFDoc,t)})},p.FDFDoc.createFromMemoryBuffer=function(t){P(arguments.length,1,"createFromMemoryBuffer","(ArrayBuffer|TypedArray)",[[t,"ArrayBuffer"]]);var e=y(t,!1);return p.sendWithPromise("fdfDocCreateFromMemoryBuffer",{buf:e}).then(function(t){return S(p.FDFDoc,t)})},p.FDFDoc.prototype.isModified=function(){return p.sendWithPromise("FDFDoc.isModified",{doc:this.id})},p.FDFDoc.prototype.saveMemoryBuffer=function(){return p.sendWithPromise("FDFDoc.saveMemoryBuffer",{doc:this.id}).then(function(t){return new Uint8Array(t)})},p.FDFDoc.prototype.getTrailer=function(){return p.sendWithPromise("FDFDoc.getTrailer",{doc:this.id}).then(function(t){return _(p.Obj,t)})},p.FDFDoc.prototype.getRoot=function(){return p.sendWithPromise("FDFDoc.getRoot",{doc:this.id}).then(function(t){return _(p.Obj,t)})},p.FDFDoc.prototype.getFDF=function(){return p.sendWithPromise("FDFDoc.getFDF",{doc:this.id}).then(function(t){return _(p.Obj,t)})},p.FDFDoc.prototype.getPDFFileName=function(){return p.sendWithPromise("FDFDoc.getPDFFileName",{doc:this.id})},p.FDFDoc.prototype.setPDFFileName=function(t){return P(arguments.length,1,"setPDFFileName","(string)",[[t,"string"]]),p.sendWithPromise("FDFDoc.setPDFFileName",{doc:this.id,filepath:t})},p.FDFDoc.prototype.getID=function(){return p.sendWithPromise("FDFDoc.getID",{doc:this.id}).then(function(t){return _(p.Obj,t)})},p.FDFDoc.prototype.setID=function(t){return P(arguments.length,1,"setID","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("FDFDoc.setID",{doc:this.id,id:t.id})},p.FDFDoc.prototype.getFieldIteratorBegin=function(){return p.sendWithPromise("FDFDoc.getFieldIteratorBegin",{doc:this.id}).then(function(t){return S(p.Iterator,t,"FDFField")})},p.FDFDoc.prototype.getFieldIterator=function(t){return P(arguments.length,1,"getFieldIterator","(string)",[[t,"string"]]),p.sendWithPromise("FDFDoc.getFieldIterator",{doc:this.id,field_name:t}).then(function(t){return S(p.Iterator,t,"FDFField")})},p.FDFDoc.prototype.getField=function(t){return P(arguments.length,1,"getField","(string)",[[t,"string"]]),p.sendWithPromise("FDFDoc.getField",{doc:this.id,field_name:t}).then(function(t){return new p.FDFField(t)})},p.FDFDoc.prototype.fieldCreate=function(t,e,n){return void 0===n&&(n=new p.Obj("0")),P(arguments.length,2,"fieldCreate","(string, number, PDFNet.Obj)",[[t,"string"],[e,"number"],[n,"Object",p.Obj,"Obj"]]),p.sendWithPromise("FDFDoc.fieldCreate",{doc:this.id,field_name:t,type:e,field_value:n.id}).then(function(t){return new p.FDFField(t)})},p.FDFDoc.prototype.fieldCreateFromString=function(t,e,n){return P(arguments.length,3,"fieldCreateFromString","(string, number, string)",[[t,"string"],[e,"number"],[n,"string"]]),p.sendWithPromise("FDFDoc.fieldCreateFromString",{doc:this.id,field_name:t,type:e,field_value:n}).then(function(t){return new p.FDFField(t)})},p.FDFDoc.prototype.getSDFDoc=function(){return p.sendWithPromise("FDFDoc.getSDFDoc",{doc:this.id}).then(function(t){return _(p.SDFDoc,t)})},p.FDFDoc.createFromXFDF=function(t){return P(arguments.length,1,"createFromXFDF","(string)",[[t,"string"]]),p.sendWithPromise("fdfDocCreateFromXFDF",{file_name:t}).then(function(t){return S(p.FDFDoc,t)})},p.FDFDoc.prototype.saveAsXFDFWithOptions=function(t,e){return void 0===e&&(e=null),P(arguments.length,1,"saveAsXFDFWithOptions","(string, PDFNet.OptionBase)",[[t,"string"],[e,"OptionBase"]]),b("saveAsXFDFWithOptions",[[e,1]]),e=e?e.getJsonString():"{}",p.sendWithPromise("FDFDoc.saveAsXFDFWithOptions",{doc:this.id,filepath:t,opts:e})},p.FDFDoc.prototype.saveAsXFDFAsString=function(){return p.sendWithPromise("FDFDoc.saveAsXFDFAsString",{doc:this.id})},p.FDFDoc.prototype.saveAsXFDFAsStringWithOptions=function(t){return void 0===t&&(t=null),P(arguments.length,0,"saveAsXFDFAsStringWithOptions","(PDFNet.OptionBase)",[[t,"OptionBase"]]),b("saveAsXFDFAsStringWithOptions",[[t,0]]),t=t?t.getJsonString():"{}",p.sendWithPromise("FDFDoc.saveAsXFDFAsStringWithOptions",{doc:this.id,opts:t})},p.FDFDoc.prototype.mergeAnnots=function(t,e){return void 0===e&&(e=""),P(arguments.length,1,"mergeAnnots","(string, string)",[[t,"string"],[e,"string"]]),p.sendWithPromise("FDFDoc.mergeAnnots",{doc:this.id,command_file:t,permitted_user:e})},p.FDFField.create=function(t,e){return void 0===t&&(t=new p.Obj("0")),void 0===e&&(e=new p.Obj("0")),P(arguments.length,0,"create","(PDFNet.Obj, PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"],[e,"Object",p.Obj,"Obj"]]),p.sendWithPromise("fdfFieldCreate",{field_dict:t.id,fdf_dict:e.id}).then(function(t){return new p.FDFField(t)})},p.FDFField.prototype.getValue=function(){F("getValue",this.yieldFunction);var e=this;return this.yieldFunction="FDFField.getValue",p.sendWithPromise("FDFField.getValue",{field:this}).then(function(t){return e.yieldFunction=void 0,t.result=_(p.Obj,t.result),W(t.field,e),t.result})},p.FDFField.prototype.setValue=function(t){P(arguments.length,1,"setValue","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),F("setValue",this.yieldFunction);var e=this;return this.yieldFunction="FDFField.setValue",p.sendWithPromise("FDFField.setValue",{field:this,value:t.id}).then(function(t){e.yieldFunction=void 0,W(t,e)})},p.FDFField.prototype.getName=function(){F("getName",this.yieldFunction);var e=this;return this.yieldFunction="FDFField.getName",p.sendWithPromise("FDFField.getName",{field:this}).then(function(t){return e.yieldFunction=void 0,W(t.field,e),t.result})},p.FDFField.prototype.getPartialName=function(){F("getPartialName",this.yieldFunction);var e=this;return this.yieldFunction="FDFField.getPartialName",p.sendWithPromise("FDFField.getPartialName",{field:this}).then(function(t){return e.yieldFunction=void 0,W(t.field,e),t.result})},p.FDFField.prototype.getSDFObj=function(){return F("getSDFObj",this.yieldFunction),p.sendWithPromise("FDFField.getSDFObj",{field:this}).then(function(t){return _(p.Obj,t)})},p.FDFField.prototype.findAttribute=function(t){return P(arguments.length,1,"findAttribute","(string)",[[t,"string"]]),F("findAttribute",this.yieldFunction),p.sendWithPromise("FDFField.findAttribute",{field:this,attrib:t}).then(function(t){return _(p.Obj,t)})},p.Filter.prototype.createASCII85Encode=function(t,e){return P(arguments.length,2,"createASCII85Encode","(number, number)",[[t,"number"],[e,"number"]]),p.sendWithPromise("Filter.createASCII85Encode",{no_own_input_filter:this.id,line_width:t,buf_sz:e}).then(function(t){return S(p.Filter,t)})},p.Filter.createMemoryFilter=function(t,e){return P(arguments.length,2,"createMemoryFilter","(number, boolean)",[[t,"number"],[e,"boolean"]]),p.sendWithPromise("filterCreateMemoryFilter",{buf_sz:t,is_input:e}).then(function(t){return S(p.Filter,t)})},p.Filter.createImage2RGBFromElement=function(t){return P(arguments.length,1,"createImage2RGBFromElement","(PDFNet.Element)",[[t,"Object",p.Element,"Element"]]),p.sendWithPromise("filterCreateImage2RGBFromElement",{elem:t.id}).then(function(t){return S(p.Filter,t)})},p.Filter.createImage2RGBFromObj=function(t){return P(arguments.length,1,"createImage2RGBFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("filterCreateImage2RGBFromObj",{obj:t.id}).then(function(t){return S(p.Filter,t)})},p.Filter.createImage2RGB=function(t){return P(arguments.length,1,"createImage2RGB","(PDFNet.Image)",[[t,"Object",p.Image,"Image"]]),p.sendWithPromise("filterCreateImage2RGB",{img:t.id}).then(function(t){return S(p.Filter,t)})},p.Filter.createImage2RGBAFromElement=function(t,e){return P(arguments.length,2,"createImage2RGBAFromElement","(PDFNet.Element, boolean)",[[t,"Object",p.Element,"Element"],[e,"boolean"]]),p.sendWithPromise("filterCreateImage2RGBAFromElement",{elem:t.id,premultiply:e}).then(function(t){return S(p.Filter,t)})},p.Filter.createImage2RGBAFromObj=function(t,e){return P(arguments.length,2,"createImage2RGBAFromObj","(PDFNet.Obj, boolean)",[[t,"Object",p.Obj,"Obj"],[e,"boolean"]]),p.sendWithPromise("filterCreateImage2RGBAFromObj",{obj:t.id,premultiply:e}).then(function(t){return S(p.Filter,t)})},p.Filter.createImage2RGBA=function(t,e){return P(arguments.length,2,"createImage2RGBA","(PDFNet.Image, boolean)",[[t,"Object",p.Image,"Image"],[e,"boolean"]]),p.sendWithPromise("filterCreateImage2RGBA",{img:t.id,premultiply:e}).then(function(t){return S(p.Filter,t)})},p.Filter.prototype.attachFilter=function(t){return P(arguments.length,1,"attachFilter","(PDFNet.Filter)",[[t,"Object",p.Filter,"Filter"]]),0!=t.id&&A(t.id),p.sendWithPromise("Filter.attachFilter",{filter:this.id,no_own_attach_filter:t.id})},p.Filter.prototype.releaseAttachedFilter=function(){return p.sendWithPromise("Filter.releaseAttachedFilter",{filter:this.id}).then(function(t){return S(p.Filter,t)})},p.Filter.prototype.getAttachedFilter=function(){return p.sendWithPromise("Filter.getAttachedFilter",{filter:this.id}).then(function(t){return _(p.Filter,t)})},p.Filter.prototype.getSourceFilter=function(){return p.sendWithPromise("Filter.getSourceFilter",{filter:this.id}).then(function(t){return _(p.Filter,t)})},p.Filter.prototype.getName=function(){return p.sendWithPromise("Filter.getName",{filter:this.id})},p.Filter.prototype.getDecodeName=function(){return p.sendWithPromise("Filter.getDecodeName",{filter:this.id})},p.Filter.prototype.begin=function(){return p.sendWithPromise("Filter.begin",{filter:this.id})},p.Filter.prototype.size=function(){return p.sendWithPromise("Filter.size",{filter:this.id})},p.Filter.prototype.consume=function(t){return P(arguments.length,1,"consume","(number)",[[t,"number"]]),p.sendWithPromise("Filter.consume",{filter:this.id,num_bytes:t})},p.Filter.prototype.count=function(){return p.sendWithPromise("Filter.count",{filter:this.id})},p.Filter.prototype.setCount=function(t){return P(arguments.length,1,"setCount","(number)",[[t,"number"]]),p.sendWithPromise("Filter.setCount",{filter:this.id,new_count:t})},p.Filter.prototype.setStreamLength=function(t){return P(arguments.length,1,"setStreamLength","(number)",[[t,"number"]]),p.sendWithPromise("Filter.setStreamLength",{filter:this.id,bytes:t})},p.Filter.prototype.flush=function(){return p.sendWithPromise("Filter.flush",{filter:this.id})},p.Filter.prototype.flushAll=function(){return p.sendWithPromise("Filter.flushAll",{filter:this.id})},p.Filter.prototype.isInputFilter=function(){return p.sendWithPromise("Filter.isInputFilter",{filter:this.id})},p.Filter.prototype.canSeek=function(){return p.sendWithPromise("Filter.canSeek",{filter:this.id})},p.Filter.prototype.seek=function(t,e){return P(arguments.length,2,"seek","(number, number)",[[t,"number"],[e,"number"]]),p.sendWithPromise("Filter.seek",{filter:this.id,offset:t,origin:e})},p.Filter.prototype.tell=function(){return p.sendWithPromise("Filter.tell",{filter:this.id})},p.Filter.prototype.truncate=function(t){return P(arguments.length,1,"truncate","(number)",[[t,"number"]]),p.sendWithPromise("Filter.truncate",{filter:this.id,new_size:t})},p.Filter.prototype.createInputIterator=function(){return p.sendWithPromise("Filter.createInputIterator",{filter:this.id}).then(function(t){return S(p.Filter,t)})},p.Filter.prototype.getFilePath=function(){return p.sendWithPromise("Filter.getFilePath",{filter:this.id})},p.Filter.prototype.memoryFilterGetBuffer=function(){return p.sendWithPromise("Filter.memoryFilterGetBuffer",{filter:this.id})},p.Filter.prototype.memoryFilterSetAsInputFilter=function(){return p.sendWithPromise("Filter.memoryFilterSetAsInputFilter",{filter:this.id})},p.Filter.prototype.memoryFilterReset=function(){return p.sendWithPromise("Filter.memoryFilterReset",{filter:this.id})},p.FilterReader.create=function(t){return P(arguments.length,1,"create","(PDFNet.Filter)",[[t,"Object",p.Filter,"Filter"]]),p.sendWithPromise("filterReaderCreate",{filter:t.id}).then(function(t){return S(p.FilterReader,t)})},p.FilterReader.prototype.attachFilter=function(t){return P(arguments.length,1,"attachFilter","(PDFNet.Filter)",[[t,"Object",p.Filter,"Filter"]]),p.sendWithPromise("FilterReader.attachFilter",{reader:this.id,filter:t.id})},p.FilterReader.prototype.getAttachedFilter=function(){return p.sendWithPromise("FilterReader.getAttachedFilter",{reader:this.id}).then(function(t){return _(p.Filter,t)})},p.FilterReader.prototype.seek=function(t,e){return P(arguments.length,2,"seek","(number, number)",[[t,"number"],[e,"number"]]),p.sendWithPromise("FilterReader.seek",{reader:this.id,offset:t,origin:e})},p.FilterReader.prototype.tell=function(){return p.sendWithPromise("FilterReader.tell",{reader:this.id})},p.FilterReader.prototype.count=function(){return p.sendWithPromise("FilterReader.count",{reader:this.id})},p.FilterReader.prototype.flush=function(){return p.sendWithPromise("FilterReader.flush",{reader:this.id})},p.FilterReader.prototype.flushAll=function(){return p.sendWithPromise("FilterReader.flushAll",{reader:this.id})},p.FilterReader.prototype.get=function(){return p.sendWithPromise("FilterReader.get",{reader:this.id})},p.FilterReader.prototype.peek=function(){return p.sendWithPromise("FilterReader.peek",{reader:this.id})},p.FilterWriter.create=function(t){return P(arguments.length,1,"create","(PDFNet.Filter)",[[t,"Object",p.Filter,"Filter"]]),p.sendWithPromise("filterWriterCreate",{filter:t.id}).then(function(t){return S(p.FilterWriter,t)})},p.FilterWriter.prototype.attachFilter=function(t){return P(arguments.length,1,"attachFilter","(PDFNet.Filter)",[[t,"Object",p.Filter,"Filter"]]),p.sendWithPromise("FilterWriter.attachFilter",{writer:this.id,filter:t.id})},p.FilterWriter.prototype.getAttachedFilter=function(){return p.sendWithPromise("FilterWriter.getAttachedFilter",{writer:this.id}).then(function(t){return _(p.Filter,t)})},p.FilterWriter.prototype.seek=function(t,e){return P(arguments.length,2,"seek","(number, number)",[[t,"number"],[e,"number"]]),p.sendWithPromise("FilterWriter.seek",{writer:this.id,offset:t,origin:e})},p.FilterWriter.prototype.tell=function(){return p.sendWithPromise("FilterWriter.tell",{writer:this.id})},p.FilterWriter.prototype.count=function(){return p.sendWithPromise("FilterWriter.count",{writer:this.id})},p.FilterWriter.prototype.flush=function(){return p.sendWithPromise("FilterWriter.flush",{writer:this.id})},p.FilterWriter.prototype.flushAll=function(){return p.sendWithPromise("FilterWriter.flushAll",{writer:this.id})},p.FilterWriter.prototype.writeUChar=function(t){return P(arguments.length,1,"writeUChar","(number)",[[t,"number"]]),p.sendWithPromise("FilterWriter.writeUChar",{writer:this.id,ch:t})},p.FilterWriter.prototype.writeInt16=function(t){return P(arguments.length,1,"writeInt16","(number)",[[t,"number"]]),p.sendWithPromise("FilterWriter.writeInt16",{writer:this.id,num:t})},p.FilterWriter.prototype.writeUInt16=function(t){return P(arguments.length,1,"writeUInt16","(number)",[[t,"number"]]),p.sendWithPromise("FilterWriter.writeUInt16",{writer:this.id,num:t})},p.FilterWriter.prototype.writeInt32=function(t){return P(arguments.length,1,"writeInt32","(number)",[[t,"number"]]),p.sendWithPromise("FilterWriter.writeInt32",{writer:this.id,num:t})},p.FilterWriter.prototype.writeUInt32=function(t){return P(arguments.length,1,"writeUInt32","(number)",[[t,"number"]]),p.sendWithPromise("FilterWriter.writeUInt32",{writer:this.id,num:t})},p.FilterWriter.prototype.writeInt64=function(t){return P(arguments.length,1,"writeInt64","(number)",[[t,"number"]]),p.sendWithPromise("FilterWriter.writeInt64",{writer:this.id,num:t})},p.FilterWriter.prototype.writeUInt64=function(t){return P(arguments.length,1,"writeUInt64","(number)",[[t,"number"]]),p.sendWithPromise("FilterWriter.writeUInt64",{writer:this.id,num:t})},p.FilterWriter.prototype.writeString=function(t){return P(arguments.length,1,"writeString","(string)",[[t,"string"]]),p.sendWithPromise("FilterWriter.writeString",{writer:this.id,str:t})},p.FilterWriter.prototype.writeFilter=function(t){return P(arguments.length,1,"writeFilter","(PDFNet.FilterReader)",[[t,"Object",p.FilterReader,"FilterReader"]]),p.sendWithPromise("FilterWriter.writeFilter",{writer:this.id,reader:t.id})},p.FilterWriter.prototype.writeLine=function(t,e){return void 0===e&&(e=13),P(arguments.length,1,"writeLine","(string, number)",[[t,"const char* = 0"],[e,"number"]]),p.sendWithPromise("FilterWriter.writeLine",{writer:this.id,line:t,eol:e})},p.FilterWriter.prototype.writeBuffer=function(t){P(arguments.length,1,"writeBuffer","(ArrayBuffer|TypedArray)",[[t,"ArrayBuffer"]]);var e=y(t,!1);return p.sendWithPromise("FilterWriter.writeBuffer",{writer:this.id,buf:e})},p.OCG.create=function(t,e){return P(arguments.length,2,"create","(PDFNet.PDFDoc, string)",[[t,"PDFDoc"],[e,"string"]]),p.sendWithPromise("ocgCreate",{pdfdoc:t.id,name:e}).then(function(t){return _(p.OCG,t)})},p.OCG.createFromObj=function(t){return P(arguments.length,1,"createFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("ocgCreateFromObj",{ocg_dict:t.id}).then(function(t){return _(p.OCG,t)})},p.OCG.prototype.copy=function(){return p.sendWithPromise("OCG.copy",{ocg:this.id}).then(function(t){return _(p.OCG,t)})},p.OCG.prototype.getSDFObj=function(){return p.sendWithPromise("OCG.getSDFObj",{ocg:this.id}).then(function(t){return _(p.Obj,t)})},p.OCG.prototype.isValid=function(){return p.sendWithPromise("OCG.isValid",{ocg:this.id})},p.OCG.prototype.getName=function(){return p.sendWithPromise("OCG.getName",{c:this.id})},p.OCG.prototype.setName=function(t){return P(arguments.length,1,"setName","(string)",[[t,"string"]]),p.sendWithPromise("OCG.setName",{c:this.id,value:t})},p.OCG.prototype.getIntent=function(){return p.sendWithPromise("OCG.getIntent",{c:this.id}).then(function(t){return _(p.Obj,t)})},p.OCG.prototype.setIntent=function(t){return P(arguments.length,1,"setIntent","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("OCG.setIntent",{c:this.id,value:t.id})},p.OCG.prototype.hasUsage=function(){return p.sendWithPromise("OCG.hasUsage",{c:this.id})},p.OCG.prototype.getUsage=function(t){return P(arguments.length,1,"getUsage","(string)",[[t,"string"]]),p.sendWithPromise("OCG.getUsage",{c:this.id,key:t}).then(function(t){return _(p.Obj,t)})},p.OCG.prototype.getCurrentState=function(t){return P(arguments.length,1,"getCurrentState","(PDFNet.OCGContext)",[[t,"Object",p.OCGContext,"OCGContext"]]),p.sendWithPromise("OCG.getCurrentState",{c:this.id,ctx:t.id})},p.OCG.prototype.setCurrentState=function(t,e){return P(arguments.length,2,"setCurrentState","(PDFNet.OCGContext, boolean)",[[t,"Object",p.OCGContext,"OCGContext"],[e,"boolean"]]),p.sendWithPromise("OCG.setCurrentState",{c:this.id,ctx:t.id,state:e})},p.OCG.prototype.getInitialState=function(t){return P(arguments.length,1,"getInitialState","(PDFNet.OCGConfig)",[[t,"Object",p.OCGConfig,"OCGConfig"]]),p.sendWithPromise("OCG.getInitialState",{c:this.id,cfg:t.id})},p.OCG.prototype.setInitialState=function(t,e){return P(arguments.length,2,"setInitialState","(PDFNet.OCGConfig, boolean)",[[t,"Object",p.OCGConfig,"OCGConfig"],[e,"boolean"]]),p.sendWithPromise("OCG.setInitialState",{c:this.id,cfg:t.id,state:e})},p.OCG.prototype.isLocked=function(t){return P(arguments.length,1,"isLocked","(PDFNet.OCGConfig)",[[t,"Object",p.OCGConfig,"OCGConfig"]]),p.sendWithPromise("OCG.isLocked",{c:this.id,cfg:t.id})},p.OCG.prototype.setLocked=function(t,e){return P(arguments.length,2,"setLocked","(PDFNet.OCGConfig, boolean)",[[t,"Object",p.OCGConfig,"OCGConfig"],[e,"boolean"]]),p.sendWithPromise("OCG.setLocked",{c:this.id,cfg:t.id,state:e})},p.OCGConfig.createFromObj=function(t){return P(arguments.length,1,"createFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("ocgConfigCreateFromObj",{dict:t.id}).then(function(t){return _(p.OCGConfig,t)})},p.OCGConfig.create=function(t,e){return P(arguments.length,2,"create","(PDFNet.PDFDoc, boolean)",[[t,"PDFDoc"],[e,"boolean"]]),p.sendWithPromise("ocgConfigCreate",{pdfdoc:t.id,default_config:e}).then(function(t){return _(p.OCGConfig,t)})},p.OCGConfig.prototype.copy=function(){return p.sendWithPromise("OCGConfig.copy",{c:this.id}).then(function(t){return _(p.OCGConfig,t)})},p.OCGConfig.prototype.getSDFObj=function(){return p.sendWithPromise("OCGConfig.getSDFObj",{c:this.id}).then(function(t){return _(p.Obj,t)})},p.OCGConfig.prototype.getOrder=function(){return p.sendWithPromise("OCGConfig.getOrder",{c:this.id}).then(function(t){return _(p.Obj,t)})},p.OCGConfig.prototype.setOrder=function(t){return P(arguments.length,1,"setOrder","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("OCGConfig.setOrder",{c:this.id,value:t.id})},p.OCGConfig.prototype.getName=function(){return p.sendWithPromise("OCGConfig.getName",{c:this.id})},p.OCGConfig.prototype.setName=function(t){return P(arguments.length,1,"setName","(string)",[[t,"string"]]),p.sendWithPromise("OCGConfig.setName",{c:this.id,value:t})},p.OCGConfig.prototype.getCreator=function(){return p.sendWithPromise("OCGConfig.getCreator",{c:this.id})},p.OCGConfig.prototype.setCreator=function(t){return P(arguments.length,1,"setCreator","(string)",[[t,"string"]]),p.sendWithPromise("OCGConfig.setCreator",{c:this.id,value:t})},p.OCGConfig.prototype.getInitBaseState=function(){return p.sendWithPromise("OCGConfig.getInitBaseState",{c:this.id})},p.OCGConfig.prototype.setInitBaseState=function(t){return void 0===t&&(t="ON"),P(arguments.length,0,"setInitBaseState","(string)",[[t,"const char* = 0"]]),p.sendWithPromise("OCGConfig.setInitBaseState",{c:this.id,value:t})},p.OCGConfig.prototype.getInitOnStates=function(){return p.sendWithPromise("OCGConfig.getInitOnStates",{c:this.id}).then(function(t){return _(p.Obj,t)})},p.OCGConfig.prototype.setInitOnStates=function(t){return P(arguments.length,1,"setInitOnStates","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("OCGConfig.setInitOnStates",{c:this.id,value:t.id})},p.OCGConfig.prototype.getInitOffStates=function(){return p.sendWithPromise("OCGConfig.getInitOffStates",{c:this.id}).then(function(t){return _(p.Obj,t)})},p.OCGConfig.prototype.setInitOffStates=function(t){return P(arguments.length,1,"setInitOffStates","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("OCGConfig.setInitOffStates",{c:this.id,value:t.id})},p.OCGConfig.prototype.getIntent=function(){return p.sendWithPromise("OCGConfig.getIntent",{c:this.id}).then(function(t){return _(p.Obj,t)})},p.OCGConfig.prototype.setIntent=function(t){return P(arguments.length,1,"setIntent","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("OCGConfig.setIntent",{c:this.id,value:t.id})},p.OCGConfig.prototype.getLockedOCGs=function(){return p.sendWithPromise("OCGConfig.getLockedOCGs",{c:this.id}).then(function(t){return _(p.Obj,t)})},p.OCGConfig.prototype.setLockedOCGs=function(t){return P(arguments.length,1,"setLockedOCGs","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("OCGConfig.setLockedOCGs",{c:this.id,value:t.id})},p.OCGConfig.prototype.getRBGroups=function(){return p.sendWithPromise("OCGConfig.getRBGroups",{c:this.id}).then(function(t){return _(p.Obj,t)})},p.OCGConfig.prototype.setRBGroups=function(t){return P(arguments.length,1,"setRBGroups","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("OCGConfig.setRBGroups",{c:this.id,value:t.id})},p.OCGContext.createFromConfig=function(t){return P(arguments.length,1,"createFromConfig","(PDFNet.OCGConfig)",[[t,"Object",p.OCGConfig,"OCGConfig"]]),p.sendWithPromise("ocgContextCreateFromConfig",{cfg:t.id}).then(function(t){return S(p.OCGContext,t)})},p.OCGContext.prototype.copy=function(){return p.sendWithPromise("OCGContext.copy",{c:this.id}).then(function(t){return S(p.OCGContext,t)})},p.OCGContext.prototype.getState=function(t){return P(arguments.length,1,"getState","(PDFNet.OCG)",[[t,"Object",p.OCG,"OCG"]]),p.sendWithPromise("OCGContext.getState",{c:this.id,grp:t.id})},p.OCGContext.prototype.setState=function(t,e){return P(arguments.length,2,"setState","(PDFNet.OCG, boolean)",[[t,"Object",p.OCG,"OCG"],[e,"boolean"]]),p.sendWithPromise("OCGContext.setState",{c:this.id,grp:t.id,state:e})},p.OCGContext.prototype.resetStates=function(t){return P(arguments.length,1,"resetStates","(boolean)",[[t,"boolean"]]),p.sendWithPromise("OCGContext.resetStates",{c:this.id,all_on:t})},p.OCGContext.prototype.setNonOCDrawing=function(t){return P(arguments.length,1,"setNonOCDrawing","(boolean)",[[t,"boolean"]]),p.sendWithPromise("OCGContext.setNonOCDrawing",{c:this.id,draw_non_OC:t})},p.OCGContext.prototype.getNonOCDrawing=function(){return p.sendWithPromise("OCGContext.getNonOCDrawing",{c:this.id})},p.OCGContext.prototype.setOCDrawMode=function(t){return P(arguments.length,1,"setOCDrawMode","(number)",[[t,"number"]]),p.sendWithPromise("OCGContext.setOCDrawMode",{c:this.id,oc_draw_mode:t})},p.OCGContext.prototype.getOCMode=function(){return p.sendWithPromise("OCGContext.getOCMode",{c:this.id})},p.OCMD.createFromObj=function(t){return P(arguments.length,1,"createFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("ocmdCreateFromObj",{ocmd_dict:t.id}).then(function(t){return _(p.OCMD,t)})},p.OCMD.create=function(t,e,n){return P(arguments.length,3,"create","(PDFNet.PDFDoc, PDFNet.Obj, number)",[[t,"PDFDoc"],[e,"Object",p.Obj,"Obj"],[n,"number"]]),p.sendWithPromise("ocmdCreate",{pdfdoc:t.id,ocgs:e.id,vis_policy:n}).then(function(t){return _(p.OCMD,t)})},p.OCMD.prototype.copy=function(){return p.sendWithPromise("OCMD.copy",{ocmd:this.id}).then(function(t){return _(p.OCMD,t)})},p.OCMD.prototype.getSDFObj=function(){return p.sendWithPromise("OCMD.getSDFObj",{ocmd:this.id}).then(function(t){return _(p.Obj,t)})},p.OCMD.prototype.getOCGs=function(){return p.sendWithPromise("OCMD.getOCGs",{ocmd:this.id}).then(function(t){return _(p.Obj,t)})},p.OCMD.prototype.getVisibilityExpression=function(){return p.sendWithPromise("OCMD.getVisibilityExpression",{ocmd:this.id}).then(function(t){return _(p.Obj,t)})},p.OCMD.prototype.isValid=function(){return p.sendWithPromise("OCMD.isValid",{ocmd:this.id})},p.OCMD.prototype.isCurrentlyVisible=function(t){return P(arguments.length,1,"isCurrentlyVisible","(PDFNet.OCGContext)",[[t,"Object",p.OCGContext,"OCGContext"]]),p.sendWithPromise("OCMD.isCurrentlyVisible",{ocmd:this.id,ctx:t.id})};p.OCMD.prototype.getVisibilityPolicy=function(){return p.sendWithPromise("OCMD.getVisibilityPolicy",{ocmd:this.id})},p.OCMD.prototype.setVisibilityPolicy=function(t){return P(arguments.length,1,"setVisibilityPolicy","(number)",[[t,"number"]]),p.sendWithPromise("OCMD.setVisibilityPolicy",{ocmd:this.id,vis_policy:t})},p.PDFACompliance.createFromFileWithOptions=function(t,e,n){return void 0===n&&(n=null),P(arguments.length,2,"createFromFileWithOptions","(boolean, string, PDFNet.OptionBase)",[[t,"boolean"],[e,"string"],[n,"OptionBase"]]),b("createFromFileWithOptions",[[n,2]]),n=n?n.getJsonString():"{}",p.sendWithPromise("pdfaComplianceCreateFromFileWithOptions",{convert:t,file_path:e,in_options:n}).then(function(t){return S(p.PDFACompliance,t)})},p.PDFACompliance.createFromBufferWithOptions=function(t,e,n){void 0===n&&(n=null),P(arguments.length,2,"createFromBufferWithOptions","(boolean, ArrayBuffer|TypedArray, PDFNet.OptionBase)",[[t,"boolean"],[e,"ArrayBuffer"],[n,"OptionBase"]]),b("createFromBufferWithOptions",[[n,2]]);var i=y(e,!1);return n=n?n.getJsonString():"{}",p.sendWithPromise("pdfaComplianceCreateFromBufferWithOptions",{convert:t,buf:i,in_options:n}).then(function(t){return S(p.PDFACompliance,t)})},p.PDFACompliance.prototype.getErrorCount=function(){return p.sendWithPromise("PDFACompliance.getErrorCount",{pdfac:this.id})},p.PDFACompliance.prototype.getError=function(t){return P(arguments.length,1,"getError","(number)",[[t,"number"]]),p.sendWithPromise("PDFACompliance.getError",{pdfac:this.id,idx:t})},p.PDFACompliance.prototype.getRefObjCount=function(t){return P(arguments.length,1,"getRefObjCount","(number)",[[t,"number"]]),p.sendWithPromise("PDFACompliance.getRefObjCount",{pdfac:this.id,id:t})},p.PDFACompliance.prototype.getRefObj=function(t,e){return P(arguments.length,2,"getRefObj","(number, number)",[[t,"number"],[e,"number"]]),p.sendWithPromise("PDFACompliance.getRefObj",{pdfac:this.id,id:t,err_idx:e})},p.PDFACompliance.getPDFAErrorMessage=function(t){return P(arguments.length,1,"getPDFAErrorMessage","(number)",[[t,"number"]]),p.sendWithPromise("pdfaComplianceGetPDFAErrorMessage",{id:t})},p.PDFACompliance.getDeclaredConformance=function(t){return P(arguments.length,1,"getDeclaredConformance","(PDFNet.PDFDoc)",[[t,"PDFDoc"]]),p.sendWithPromise("pdfaComplianceGetDeclaredConformance",{doc:t.id})},p.PDFACompliance.prototype.saveAsFromBuffer=function(t){return void 0===t&&(t=!1),P(arguments.length,0,"saveAsFromBuffer","(boolean)",[[t,"boolean"]]),p.sendWithPromise("PDFACompliance.saveAsFromBuffer",{pdfac:this.id,linearized:t}).then(function(t){return new Uint8Array(t)})},p.AttrObj.create=function(t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,0,"create","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("attrObjCreate",{dict:t.id}).then(function(t){return _(p.AttrObj,t)})},p.AttrObj.prototype.copy=function(){return p.sendWithPromise("AttrObj.copy",{a:this.id}).then(function(t){return _(p.AttrObj,t)})},p.AttrObj.prototype.getOwner=function(){return p.sendWithPromise("AttrObj.getOwner",{obj:this.id})},p.AttrObj.prototype.getSDFObj=function(){return p.sendWithPromise("AttrObj.getSDFObj",{obj:this.id}).then(function(t){return _(p.Obj,t)})},p.ClassMap.create=function(t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,0,"create","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("classMapCreate",{dict:t.id}).then(function(t){return _(p.ClassMap,t)})},p.ClassMap.prototype.copy=function(){return p.sendWithPromise("ClassMap.copy",{p:this.id}).then(function(t){return _(p.ClassMap,t)})},p.ClassMap.prototype.isValid=function(){return p.sendWithPromise("ClassMap.isValid",{map:this.id})},p.ClassMap.prototype.getSDFObj=function(){return p.sendWithPromise("ClassMap.getSDFObj",{map:this.id}).then(function(t){return _(p.Obj,t)})},p.ContentItem.prototype.copy=function(){return F("copy",this.yieldFunction),p.sendWithPromise("ContentItem.copy",{c:this}).then(function(t){return new p.ContentItem(t)})},p.ContentItem.prototype.getType=function(){return F("getType",this.yieldFunction),p.sendWithPromise("ContentItem.getType",{item:this})},p.ContentItem.prototype.getParent=function(){F("getParent",this.yieldFunction);var e=this;return this.yieldFunction="ContentItem.getParent",p.sendWithPromise("ContentItem.getParent",{item:this}).then(function(t){return e.yieldFunction=void 0,t.result=new p.SElement(t.result),W(t.item,e),t.result})},p.ContentItem.prototype.getPage=function(){F("getPage",this.yieldFunction);var e=this;return this.yieldFunction="ContentItem.getPage",p.sendWithPromise("ContentItem.getPage",{item:this}).then(function(t){return e.yieldFunction=void 0,t.result=_(p.Page,t.result),W(t.item,e),t.result})},p.ContentItem.prototype.getSDFObj=function(){return F("getSDFObj",this.yieldFunction),p.sendWithPromise("ContentItem.getSDFObj",{item:this}).then(function(t){return _(p.Obj,t)})},p.ContentItem.prototype.getMCID=function(){return F("getMCID",this.yieldFunction),p.sendWithPromise("ContentItem.getMCID",{item:this})},p.ContentItem.prototype.getContainingStm=function(){return F("getContainingStm",this.yieldFunction),p.sendWithPromise("ContentItem.getContainingStm",{item:this}).then(function(t){return _(p.Obj,t)})},p.ContentItem.prototype.getStmOwner=function(){return F("getStmOwner",this.yieldFunction),p.sendWithPromise("ContentItem.getStmOwner",{item:this}).then(function(t){return _(p.Obj,t)})},p.ContentItem.prototype.getRefObj=function(){return F("getRefObj",this.yieldFunction),p.sendWithPromise("ContentItem.getRefObj",{item:this}).then(function(t){return _(p.Obj,t)})},p.RoleMap.create=function(t){return P(arguments.length,1,"create","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("roleMapCreate",{dict:t.id}).then(function(t){return _(p.RoleMap,t)})},p.RoleMap.prototype.copy=function(){return p.sendWithPromise("RoleMap.copy",{p:this.id}).then(function(t){return _(p.RoleMap,t)})},p.RoleMap.prototype.isValid=function(){return p.sendWithPromise("RoleMap.isValid",{map:this.id})},p.RoleMap.prototype.getDirectMap=function(t){return P(arguments.length,1,"getDirectMap","(string)",[[t,"string"]]),p.sendWithPromise("RoleMap.getDirectMap",{map:this.id,type:t})},p.RoleMap.prototype.getSDFObj=function(){return p.sendWithPromise("RoleMap.getSDFObj",{map:this.id}).then(function(t){return _(p.Obj,t)})},p.SElement.create=function(t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,0,"create","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("sElementCreate",{dict:t.id}).then(function(t){return new p.SElement(t)})},p.SElement.createFromPDFDoc=function(t,e){return P(arguments.length,2,"createFromPDFDoc","(PDFNet.PDFDoc, string)",[[t,"PDFDoc"],[e,"string"]]),p.sendWithPromise("sElementCreateFromPDFDoc",{doc:t.id,struct_type:e}).then(function(t){return new p.SElement(t)})},p.SElement.prototype.insert=function(e,t){P(arguments.length,2,"insert","(PDFNet.SElement, number)",[[e,"Structure",p.SElement,"SElement"],[t,"number"]]),F("insert",this.yieldFunction),b("insert",[[e,0]]);var n=this;return this.yieldFunction="SElement.insert",e.yieldFunction="SElement.insert",p.sendWithPromise("SElement.insert",{e:this,kid:e,insert_before:t}).then(function(t){n.yieldFunction=void 0,e.yieldFunction=void 0,W(t.e,n),W(t.kid,e)})},p.SElement.prototype.createContentItem=function(t,e,n){void 0===n&&(n=-1),P(arguments.length,2,"createContentItem","(PDFNet.PDFDoc, PDFNet.Page, number)",[[t,"PDFDoc"],[e,"Object",p.Page,"Page"],[n,"number"]]),F("createContentItem",this.yieldFunction);var i=this;return this.yieldFunction="SElement.createContentItem",p.sendWithPromise("SElement.createContentItem",{e:this,doc:t.id,page:e.id,insert_before:n}).then(function(t){return i.yieldFunction=void 0,W(t.e,i),t.result})},p.SElement.prototype.isValid=function(){return F("isValid",this.yieldFunction),p.sendWithPromise("SElement.isValid",{e:this})},p.SElement.prototype.getType=function(){return F("getType",this.yieldFunction),p.sendWithPromise("SElement.getType",{e:this})},p.SElement.prototype.getNumKids=function(){return F("getNumKids",this.yieldFunction),p.sendWithPromise("SElement.getNumKids",{e:this})},p.SElement.prototype.isContentItem=function(t){return P(arguments.length,1,"isContentItem","(number)",[[t,"number"]]),F("isContentItem",this.yieldFunction),p.sendWithPromise("SElement.isContentItem",{e:this,index:t})},p.SElement.prototype.getAsContentItem=function(t){return P(arguments.length,1,"getAsContentItem","(number)",[[t,"number"]]),F("getAsContentItem",this.yieldFunction),p.sendWithPromise("SElement.getAsContentItem",{e:this,index:t}).then(function(t){return new p.ContentItem(t)})},p.SElement.prototype.getAsStructElem=function(t){return P(arguments.length,1,"getAsStructElem","(number)",[[t,"number"]]),F("getAsStructElem",this.yieldFunction),p.sendWithPromise("SElement.getAsStructElem",{e:this,index:t}).then(function(t){return new p.SElement(t)})},p.SElement.prototype.getParent=function(){return F("getParent",this.yieldFunction),p.sendWithPromise("SElement.getParent",{e:this}).then(function(t){return new p.SElement(t)})},p.SElement.prototype.getStructTreeRoot=function(){return F("getStructTreeRoot",this.yieldFunction),p.sendWithPromise("SElement.getStructTreeRoot",{e:this}).then(function(t){return _(p.STree,t)})},p.SElement.prototype.hasTitle=function(){return F("hasTitle",this.yieldFunction),p.sendWithPromise("SElement.hasTitle",{e:this})},p.SElement.prototype.getTitle=function(){return F("getTitle",this.yieldFunction),p.sendWithPromise("SElement.getTitle",{e:this})},p.SElement.prototype.getID=function(){return F("getID",this.yieldFunction),p.sendWithPromise("SElement.getID",{e:this}).then(function(t){return _(p.Obj,t)})},p.SElement.prototype.hasActualText=function(){return F("hasActualText",this.yieldFunction),p.sendWithPromise("SElement.hasActualText",{e:this})},p.SElement.prototype.getActualText=function(){return F("getActualText",this.yieldFunction),p.sendWithPromise("SElement.getActualText",{e:this})},p.SElement.prototype.hasAlt=function(){return F("hasAlt",this.yieldFunction),p.sendWithPromise("SElement.hasAlt",{e:this})},p.SElement.prototype.getAlt=function(){return F("getAlt",this.yieldFunction),p.sendWithPromise("SElement.getAlt",{e:this})},p.SElement.prototype.getSDFObj=function(){return F("getSDFObj",this.yieldFunction),p.sendWithPromise("SElement.getSDFObj",{e:this}).then(function(t){return _(p.Obj,t)})},p.STree.create=function(t){return P(arguments.length,1,"create","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("sTreeCreate",{struct_dict:t.id}).then(function(t){return _(p.STree,t)})},p.STree.createFromPDFDoc=function(t){return P(arguments.length,1,"createFromPDFDoc","(PDFNet.PDFDoc)",[[t,"PDFDoc"]]),p.sendWithPromise("sTreeCreateFromPDFDoc",{doc:t.id}).then(function(t){return _(p.STree,t)})},p.STree.prototype.insert=function(e,t){return P(arguments.length,2,"insert","(PDFNet.SElement, number)",[[e,"Structure",p.SElement,"SElement"],[t,"number"]]),b("insert",[[e,0]]),e.yieldFunction="STree.insert",p.sendWithPromise("STree.insert",{tree:this.id,kid:e,insert_before:t}).then(function(t){e.yieldFunction=void 0,W(t,e)})},p.STree.prototype.copy=function(){return p.sendWithPromise("STree.copy",{c:this.id}).then(function(t){return _(p.STree,t)})},p.STree.prototype.isValid=function(){return p.sendWithPromise("STree.isValid",{tree:this.id})},p.STree.prototype.getNumKids=function(){return p.sendWithPromise("STree.getNumKids",{tree:this.id})},p.STree.prototype.getKid=function(t){return P(arguments.length,1,"getKid","(number)",[[t,"number"]]),p.sendWithPromise("STree.getKid",{tree:this.id,index:t}).then(function(t){return new p.SElement(t)})},p.STree.prototype.getRoleMap=function(){return p.sendWithPromise("STree.getRoleMap",{tree:this.id}).then(function(t){return _(p.RoleMap,t)})},p.STree.prototype.getClassMap=function(){return p.sendWithPromise("STree.getClassMap",{tree:this.id}).then(function(t){return _(p.ClassMap,t)})},p.STree.prototype.getSDFObj=function(){return p.sendWithPromise("STree.getSDFObj",{tree:this.id}).then(function(t){return _(p.Obj,t)})},p.Action.createGoto=function(t){return P(arguments.length,1,"createGoto","(PDFNet.Destination)",[[t,"Object",p.Destination,"Destination"]]),p.sendWithPromise("actionCreateGoto",{dest:t.id}).then(function(t){return _(p.Action,t)})},p.Action.createGotoWithKey=function(t,e){return P(arguments.length,2,"createGotoWithKey","(string, PDFNet.Destination)",[[t,"string"],[e,"Object",p.Destination,"Destination"]]),p.sendWithPromise("actionCreateGotoWithKey",{key:t,dest:e.id}).then(function(t){return _(p.Action,t)})},p.Action.createGotoRemote=function(t,e){return P(arguments.length,2,"createGotoRemote","(PDFNet.FileSpec, number)",[[t,"Object",p.FileSpec,"FileSpec"],[e,"number"]]),p.sendWithPromise("actionCreateGotoRemote",{file:t.id,page_num:e}).then(function(t){return _(p.Action,t)})},p.Action.createGotoRemoteSetNewWindow=function(t,e,n){return P(arguments.length,3,"createGotoRemoteSetNewWindow","(PDFNet.FileSpec, number, boolean)",[[t,"Object",p.FileSpec,"FileSpec"],[e,"number"],[n,"boolean"]]),p.sendWithPromise("actionCreateGotoRemoteSetNewWindow",{file:t.id,page_num:e,new_window:n}).then(function(t){return _(p.Action,t)})},p.Action.createURI=function(t,e){return P(arguments.length,2,"createURI","(PDFNet.SDFDoc, string)",[[t,"SDFDoc"],[e,"string"]]),p.sendWithPromise("actionCreateURI",{sdfdoc:t.id,uri:e}).then(function(t){return _(p.Action,t)})},p.Action.createURIWithUString=function(t,e){return P(arguments.length,2,"createURIWithUString","(PDFNet.SDFDoc, string)",[[t,"SDFDoc"],[e,"string"]]),p.sendWithPromise("actionCreateURIWithUString",{sdfdoc:t.id,uri:e}).then(function(t){return _(p.Action,t)})},p.Action.createSubmitForm=function(t){return P(arguments.length,1,"createSubmitForm","(PDFNet.FileSpec)",[[t,"Object",p.FileSpec,"FileSpec"]]),p.sendWithPromise("actionCreateSubmitForm",{url:t.id}).then(function(t){return _(p.Action,t)})},p.Action.createLaunch=function(t,e){return P(arguments.length,2,"createLaunch","(PDFNet.SDFDoc, string)",[[t,"SDFDoc"],[e,"string"]]),p.sendWithPromise("actionCreateLaunch",{sdfdoc:t.id,path:e}).then(function(t){return _(p.Action,t)})},p.Action.createHideField=function(t,e){return P(arguments.length,2,"createHideField","(PDFNet.SDFDoc, Array<string>)",[[t,"SDFDoc"],[e,"Array"]]),p.sendWithPromise("actionCreateHideField",{sdfdoc:t.id,field_names_list:e}).then(function(t){return _(p.Action,t)})},p.Action.createImportData=function(t,e){return P(arguments.length,2,"createImportData","(PDFNet.SDFDoc, string)",[[t,"SDFDoc"],[e,"string"]]),p.sendWithPromise("actionCreateImportData",{sdfdoc:t.id,path:e}).then(function(t){return _(p.Action,t)})},p.Action.createResetForm=function(t){return P(arguments.length,1,"createResetForm","(PDFNet.SDFDoc)",[[t,"SDFDoc"]]),p.sendWithPromise("actionCreateResetForm",{sdfdoc:t.id}).then(function(t){return _(p.Action,t)})},p.Action.createJavaScript=function(t,e){return P(arguments.length,2,"createJavaScript","(PDFNet.SDFDoc, string)",[[t,"SDFDoc"],[e,"string"]]),p.sendWithPromise("actionCreateJavaScript",{sdfdoc:t.id,script:e}).then(function(t){return _(p.Action,t)})},p.Action.create=function(t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,0,"create","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("actionCreate",{in_obj:t.id}).then(function(t){return _(p.Action,t)})},p.Action.prototype.copy=function(){return p.sendWithPromise("Action.copy",{in_action:this.id}).then(function(t){return _(p.Action,t)})},p.Action.prototype.compare=function(t){return P(arguments.length,1,"compare","(PDFNet.Action)",[[t,"Object",p.Action,"Action"]]),p.sendWithPromise("Action.compare",{action:this.id,in_action:t.id})},p.Action.prototype.isValid=function(){return p.sendWithPromise("Action.isValid",{action:this.id})},p.Action.prototype.getXFDF=function(){return p.sendWithPromise("Action.getXFDF",{action:this.id})},p.Action.prototype.getType=function(){return p.sendWithPromise("Action.getType",{action:this.id})},p.Action.prototype.getDest=function(){return p.sendWithPromise("Action.getDest",{action:this.id}).then(function(t){return _(p.Destination,t)})},p.Action.prototype.getNext=function(){return p.sendWithPromise("Action.getNext",{action:this.id}).then(function(t){return _(p.Obj,t)})},p.Action.prototype.getSDFObj=function(){return p.sendWithPromise("Action.getSDFObj",{action:this.id}).then(function(t){return _(p.Obj,t)})},p.Action.prototype.getFormActionFlag=function(t){return P(arguments.length,1,"getFormActionFlag","(number)",[[t,"number"]]),p.sendWithPromise("Action.getFormActionFlag",{action:this.id,flag:t})},p.Action.prototype.setFormActionFlag=function(t,e){return P(arguments.length,2,"setFormActionFlag","(number, boolean)",[[t,"number"],[e,"boolean"]]),p.sendWithPromise("Action.setFormActionFlag",{action:this.id,flag:t,value:e})},p.Action.prototype.needsWriteLock=function(){return p.sendWithPromise("Action.needsWriteLock",{action:this.id})},p.Action.prototype.execute=function(){return p.sendWithPromise("Action.execute",{action:this.id})},p.Action.prototype.executeKeyStrokeAction=function(t){return P(arguments.length,1,"executeKeyStrokeAction","(PDFNet.KeyStrokeEventData)",[[t,"Object",p.KeyStrokeEventData,"KeyStrokeEventData"]]),p.sendWithPromise("Action.executeKeyStrokeAction",{action:this.id,data:t.id}).then(function(t){return S(p.KeyStrokeActionResult,t)})},p.KeyStrokeActionResult.prototype.isValid=function(){return p.sendWithPromise("KeyStrokeActionResult.isValid",{action_ret:this.id})},p.KeyStrokeActionResult.prototype.getText=function(){return p.sendWithPromise("KeyStrokeActionResult.getText",{action_ret:this.id})},p.KeyStrokeActionResult.prototype.copy=function(){return p.sendWithPromise("KeyStrokeActionResult.copy",{action_ret:this.id}).then(function(t){return S(p.KeyStrokeActionResult,t)})},p.KeyStrokeEventData.create=function(t,e,n,i,r){return P(arguments.length,5,"create","(string, string, string, number, number)",[[t,"string"],[e,"string"],[n,"string"],[i,"number"],[r,"number"]]),p.sendWithPromise("keyStrokeEventDataCreate",{field_name:t,current:e,change:n,selection_start:i,selection_end:r}).then(function(t){return S(p.KeyStrokeEventData,t)})},p.KeyStrokeEventData.prototype.copy=function(){return p.sendWithPromise("KeyStrokeEventData.copy",{data:this.id}).then(function(t){return S(p.KeyStrokeEventData,t)})},p.Page.create=function(t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,0,"create","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("pageCreate",{page_dict:t.id}).then(function(t){return _(p.Page,t)})},p.Page.prototype.copy=function(){return p.sendWithPromise("Page.copy",{p:this.id}).then(function(t){return _(p.Page,t)})},p.Page.prototype.isValid=function(){return p.sendWithPromise("Page.isValid",{page:this.id})},p.Page.prototype.getIndex=function(){return p.sendWithPromise("Page.getIndex",{page:this.id})},p.Page.prototype.getTriggerAction=function(t){return P(arguments.length,1,"getTriggerAction","(number)",[[t,"number"]]),p.sendWithPromise("Page.getTriggerAction",{page:this.id,trigger:t}).then(function(t){return _(p.Obj,t)})},p.Page.prototype.getBox=function(t){return P(arguments.length,1,"getBox","(number)",[[t,"number"]]),p.sendWithPromise("Page.getBox",{page:this.id,type:t}).then(function(t){return new p.Rect(t)})},p.Page.prototype.setBox=function(t,e){return P(arguments.length,2,"setBox","(number, PDFNet.Rect)",[[t,"number"],[e,"Structure",p.Rect,"Rect"]]),b("setBox",[[e,1]]),p.sendWithPromise("Page.setBox",{page:this.id,type:t,box:e})},p.Page.prototype.getCropBox=function(){return p.sendWithPromise("Page.getCropBox",{page:this.id}).then(function(t){return new p.Rect(t)})},p.Page.prototype.setCropBox=function(t){return P(arguments.length,1,"setCropBox","(PDFNet.Rect)",[[t,"Structure",p.Rect,"Rect"]]),b("setCropBox",[[t,0]]),p.sendWithPromise("Page.setCropBox",{page:this.id,box:t})},p.Page.prototype.getMediaBox=function(){return p.sendWithPromise("Page.getMediaBox",{page:this.id}).then(function(t){return new p.Rect(t)})},p.Page.prototype.setMediaBox=function(t){return P(arguments.length,1,"setMediaBox","(PDFNet.Rect)",[[t,"Structure",p.Rect,"Rect"]]),b("setMediaBox",[[t,0]]),p.sendWithPromise("Page.setMediaBox",{page:this.id,box:t})},p.Page.prototype.getVisibleContentBox=function(){return p.sendWithPromise("Page.getVisibleContentBox",{page:this.id}).then(function(t){return new p.Rect(t)})},p.Page.prototype.getRotation=function(){return p.sendWithPromise("Page.getRotation",{page:this.id})},p.Page.prototype.setRotation=function(t){return P(arguments.length,1,"setRotation","(number)",[[t,"number"]]),p.sendWithPromise("Page.setRotation",{page:this.id,angle:t})},p.Page.addRotations=function(t,e){return P(arguments.length,2,"addRotations","(number, number)",[[t,"number"],[e,"number"]]),p.sendWithPromise("pageAddRotations",{r0:t,r1:e})},p.Page.subtractRotations=function(t,e){return P(arguments.length,2,"subtractRotations","(number, number)",[[t,"number"],[e,"number"]]),p.sendWithPromise("pageSubtractRotations",{r0:t,r1:e})},p.Page.rotationToDegree=function(t){return P(arguments.length,1,"rotationToDegree","(number)",[[t,"number"]]),p.sendWithPromise("pageRotationToDegree",{r:t})},p.Page.degreeToRotation=function(t){return P(arguments.length,1,"degreeToRotation","(number)",[[t,"number"]]),p.sendWithPromise("pageDegreeToRotation",{r:t})},p.Page.prototype.getPageWidth=function(t){return void 0===t&&(t=p.Page.Box.e_crop),P(arguments.length,0,"getPageWidth","(number)",[[t,"number"]]),p.sendWithPromise("Page.getPageWidth",{page:this.id,box_type:t})},p.Page.prototype.getPageHeight=function(t){return void 0===t&&(t=p.Page.Box.e_crop),P(arguments.length,0,"getPageHeight","(number)",[[t,"number"]]),p.sendWithPromise("Page.getPageHeight",{page:this.id,box_type:t})},p.Page.prototype.getDefaultMatrix=function(t,e,n){return void 0===t&&(t=!1),void 0===e&&(e=p.Page.Box.e_crop),void 0===n&&(n=p.Page.Rotate.e_0),P(arguments.length,0,"getDefaultMatrix","(boolean, number, number)",[[t,"boolean"],[e,"number"],[n,"number"]]),p.sendWithPromise("Page.getDefaultMatrix",{page:this.id,flip_y:t,box_type:e,angle:n}).then(function(t){return new p.Matrix2D(t)})},p.Page.prototype.getAnnots=function(){return p.sendWithPromise("Page.getAnnots",{page:this.id}).then(function(t){return _(p.Obj,t)})},p.Page.prototype.getNumAnnots=function(){return p.sendWithPromise("Page.getNumAnnots",{page:this.id})},p.Page.prototype.getAnnot=function(t){return P(arguments.length,1,"getAnnot","(number)",[[t,"number"]]),p.sendWithPromise("Page.getAnnot",{page:this.id,index:t}).then(function(t){return _(p.Annot,t)})},p.Page.prototype.annotInsert=function(t,e){return P(arguments.length,2,"annotInsert","(number, PDFNet.Annot)",[[t,"number"],[e,"Object",p.Annot,"Annot"]]),p.sendWithPromise("Page.annotInsert",{page:this.id,pos:t,annot:e.id})},p.Page.prototype.annotPushBack=function(t){return P(arguments.length,1,"annotPushBack","(PDFNet.Annot)",[[t,"Object",p.Annot,"Annot"]]),p.sendWithPromise("Page.annotPushBack",{page:this.id,annot:t.id})},p.Page.prototype.annotPushFront=function(t){return P(arguments.length,1,"annotPushFront","(PDFNet.Annot)",[[t,"Object",p.Annot,"Annot"]]),p.sendWithPromise("Page.annotPushFront",{page:this.id,annot:t.id})},p.Page.prototype.annotRemove=function(t){return P(arguments.length,1,"annotRemove","(PDFNet.Annot)",[[t,"Object",p.Annot,"Annot"]]),p.sendWithPromise("Page.annotRemove",{page:this.id,annot:t.id})},p.Page.prototype.annotRemoveByIndex=function(t){return P(arguments.length,1,"annotRemoveByIndex","(number)",[[t,"number"]]),p.sendWithPromise("Page.annotRemoveByIndex",{page:this.id,index:t})},p.Page.prototype.scale=function(t){return P(arguments.length,1,"scale","(number)",[[t,"number"]]),p.sendWithPromise("Page.scale",{page:this.id,scale:t})},p.Page.prototype.flattenField=function(e){return P(arguments.length,1,"flattenField","(PDFNet.Field)",[[e,"Structure",p.Field,"Field"]]),b("flattenField",[[e,0]]),e.yieldFunction="Page.flattenField",p.sendWithPromise("Page.flattenField",{page:this.id,field_to_flatten:e}).then(function(t){e.yieldFunction=void 0,W(t,e)})},p.Page.prototype.hasTransition=function(){return p.sendWithPromise("Page.hasTransition",{page:this.id})},p.Page.prototype.getUserUnitSize=function(){return p.sendWithPromise("Page.getUserUnitSize",{page:this.id})},p.Page.prototype.setUserUnitSize=function(t){return P(arguments.length,1,"setUserUnitSize","(number)",[[t,"number"]]),p.sendWithPromise("Page.setUserUnitSize",{page:this.id,unit_size:t})},p.Page.prototype.getResourceDict=function(){return p.sendWithPromise("Page.getResourceDict",{page:this.id}).then(function(t){return _(p.Obj,t)})},p.Page.prototype.getContents=function(){return p.sendWithPromise("Page.getContents",{page:this.id}).then(function(t){return _(p.Obj,t)})},p.Page.prototype.getThumb=function(){return p.sendWithPromise("Page.getThumb",{page:this.id}).then(function(t){return _(p.Obj,t)})},p.Page.prototype.getSDFObj=function(){return p.sendWithPromise("Page.getSDFObj",{page:this.id}).then(function(t){return _(p.Obj,t)})},p.Page.prototype.findInheritedAttribute=function(t){return P(arguments.length,1,"findInheritedAttribute","(string)",[[t,"string"]]),p.sendWithPromise("Page.findInheritedAttribute",{page:this.id,attrib:t}).then(function(t){return _(p.Obj,t)})},p.Annot.create=function(t,e,n){return P(arguments.length,3,"create","(PDFNet.SDFDoc, number, PDFNet.Rect)",[[t,"SDFDoc"],[e,"number"],[n,"Structure",p.Rect,"Rect"]]),b("create",[[n,2]]),p.sendWithPromise("annotCreate",{doc:t.id,type:e,pos:n}).then(function(t){return _(p.Annot,t)})},p.Annot.createFromObj=function(t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,0,"createFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("annotCreateFromObj",{d:t.id}).then(function(t){return _(p.Annot,t)})},p.Annot.prototype.copy=function(){return p.sendWithPromise("Annot.copy",{d:this.id}).then(function(t){return _(p.Annot,t)})},p.Annot.prototype.compare=function(t){return P(arguments.length,1,"compare","(PDFNet.Annot)",[[t,"Object",p.Annot,"Annot"]]),p.sendWithPromise("Annot.compare",{annot:this.id,d:t.id})},p.Annot.prototype.isValid=function(){return p.sendWithPromise("Annot.isValid",{annot:this.id})},p.Annot.prototype.getSDFObj=function(){return p.sendWithPromise("Annot.getSDFObj",{annot:this.id}).then(function(t){return _(p.Obj,t)})},p.Annot.prototype.getType=function(){return p.sendWithPromise("Annot.getType",{annot:this.id})},p.Annot.prototype.isMarkup=function(){return p.sendWithPromise("Annot.isMarkup",{annot:this.id})},p.Annot.prototype.getRect=function(){return p.sendWithPromise("Annot.getRect",{annot:this.id}).then(function(t){return new p.Rect(t)})},p.Annot.prototype.getVisibleContentBox=function(){return p.sendWithPromise("Annot.getVisibleContentBox",{annot:this.id}).then(function(t){return new p.Rect(t)})},p.Annot.prototype.setRect=function(t){return P(arguments.length,1,"setRect","(PDFNet.Rect)",[[t,"Structure",p.Rect,"Rect"]]),b("setRect",[[t,0]]),p.sendWithPromise("Annot.setRect",{annot:this.id,pos:t})},p.Annot.prototype.resize=function(t){return P(arguments.length,1,"resize","(PDFNet.Rect)",[[t,"Structure",p.Rect,"Rect"]]),b("resize",[[t,0]]),p.sendWithPromise("Annot.resize",{annot:this.id,newrect:t})},p.Annot.prototype.setContents=function(t){return P(arguments.length,1,"setContents","(string)",[[t,"string"]]),p.sendWithPromise("Annot.setContents",{annot:this.id,contents:t})},p.Annot.prototype.getContents=function(){return p.sendWithPromise("Annot.getContents",{annot:this.id})},p.Annot.prototype.getTriggerAction=function(t){return P(arguments.length,1,"getTriggerAction","(number)",[[t,"number"]]),p.sendWithPromise("Annot.getTriggerAction",{annot:this.id,trigger:t}).then(function(t){return _(p.Obj,t)})},p.Annot.prototype.getCustomData=function(t){return P(arguments.length,1,"getCustomData","(string)",[[t,"string"]]),p.sendWithPromise("Annot.getCustomData",{annot:this.id,key:t})},p.Annot.prototype.setCustomData=function(t,e){return P(arguments.length,2,"setCustomData","(string, string)",[[t,"string"],[e,"string"]]),p.sendWithPromise("Annot.setCustomData",{annot:this.id,key:t,value:e})},p.Annot.prototype.deleteCustomData=function(t){return P(arguments.length,1,"deleteCustomData","(string)",[[t,"string"]]),p.sendWithPromise("Annot.deleteCustomData",{annot:this.id,key:t})},p.Annot.prototype.getPage=function(){return p.sendWithPromise("Annot.getPage",{annot:this.id}).then(function(t){return _(p.Page,t)})},p.Annot.prototype.setPage=function(t){return P(arguments.length,1,"setPage","(PDFNet.Page)",[[t,"Object",p.Page,"Page"]]),p.sendWithPromise("Annot.setPage",{annot:this.id,page:t.id})},p.Annot.prototype.getUniqueID=function(){return p.sendWithPromise("Annot.getUniqueID",{annot:this.id}).then(function(t){return _(p.Obj,t)})},p.Annot.prototype.setUniqueID=function(t){P(arguments.length,1,"setUniqueID","(ArrayBuffer|TypedArray)",[[t,"ArrayBuffer"]]);var e=y(t,!1);return p.sendWithPromise("Annot.setUniqueID",{annot:this.id,id_buf:e})},p.Annot.prototype.getDate=function(){return p.sendWithPromise("Annot.getDate",{annot:this.id}).then(function(t){return new p.Date(t)})},p.Annot.prototype.setDate=function(t){return P(arguments.length,1,"setDate","(PDFNet.Date)",[[t,"Structure",p.Date,"Date"]]),b("setDate",[[t,0]]),p.sendWithPromise("Annot.setDate",{annot:this.id,date:t})},p.Annot.prototype.getFlag=function(t){return P(arguments.length,1,"getFlag","(number)",[[t,"number"]]),p.sendWithPromise("Annot.getFlag",{annot:this.id,flag:t})},p.Annot.prototype.setFlag=function(t,e){return P(arguments.length,2,"setFlag","(number, boolean)",[[t,"number"],[e,"boolean"]]),p.sendWithPromise("Annot.setFlag",{annot:this.id,flag:t,value:e})},p.AnnotBorderStyle.create=function(t,e,n,i){return void 0===n&&(n=0),void 0===i&&(i=0),P(arguments.length,2,"create","(number, number, number, number)",[[t,"number"],[e,"number"],[n,"number"],[i,"number"]]),p.sendWithPromise("annotBorderStyleCreate",{s:t,b_width:e,b_hr:n,b_vr:i}).then(function(t){return S(p.AnnotBorderStyle,t)})},p.AnnotBorderStyle.createWithDashPattern=function(t,e,n,i,r){return P(arguments.length,5,"createWithDashPattern","(number, number, number, number, Array<number>)",[[t,"number"],[e,"number"],[n,"number"],[i,"number"],[r,"Array"]]),p.sendWithPromise("annotBorderStyleCreateWithDashPattern",{s:t,b_width:e,b_hr:n,b_vr:i,b_dash_list:r}).then(function(t){return S(p.AnnotBorderStyle,t)})},p.AnnotBorderStyle.prototype.copy=function(){return p.sendWithPromise("AnnotBorderStyle.copy",{bs:this.id}).then(function(t){return S(p.AnnotBorderStyle,t)})},p.AnnotBorderStyle.prototype.getStyle=function(){return p.sendWithPromise("AnnotBorderStyle.getStyle",{bs:this.id})},p.AnnotBorderStyle.prototype.setStyle=function(t){return P(arguments.length,1,"setStyle","(number)",[[t,"number"]]),p.sendWithPromise("AnnotBorderStyle.setStyle",{bs:this.id,style:t})},p.Annot.prototype.getAppearance=function(t,e){return void 0===t&&(t=p.Annot.State.e_normal),void 0===e&&(e=null),P(arguments.length,0,"getAppearance","(number, string)",[[t,"number"],[e,"const char* = 0"]]),p.sendWithPromise("Annot.getAppearance",{annot:this.id,annot_state:t,app_state:e}).then(function(t){return _(p.Obj,t)})},p.Annot.prototype.setAppearance=function(t,e,n){return void 0===e&&(e=p.Annot.State.e_normal),void 0===n&&(n=null),P(arguments.length,1,"setAppearance","(PDFNet.Obj, number, string)",[[t,"Object",p.Obj,"Obj"],[e,"number"],[n,"const char* = 0"]]),p.sendWithPromise("Annot.setAppearance",{annot:this.id,app_stream:t.id,annot_state:e,app_state:n})},p.Annot.prototype.removeAppearance=function(t,e){return void 0===t&&(t=p.Annot.State.e_normal),void 0===e&&(e=null),P(arguments.length,0,"removeAppearance","(number, string)",[[t,"number"],[e,"const char* = 0"]]),p.sendWithPromise("Annot.removeAppearance",{annot:this.id,annot_state:t,app_state:e})},p.Annot.prototype.flatten=function(t){return P(arguments.length,1,"flatten","(PDFNet.Page)",[[t,"Object",p.Page,"Page"]]),p.sendWithPromise("Annot.flatten",{annot:this.id,page:t.id})},p.Annot.prototype.getActiveAppearanceState=function(){return p.sendWithPromise("Annot.getActiveAppearanceState",{annot:this.id})},p.Annot.prototype.setActiveAppearanceState=function(t){return P(arguments.length,1,"setActiveAppearanceState","(string)",[[t,"string"]]),p.sendWithPromise("Annot.setActiveAppearanceState",{annot:this.id,astate:t})},p.Annot.prototype.getColor=function(){return p.sendWithPromise("Annot.getColor",{annot:this.id}).then(function(t){return S(p.ColorPt,t)})},p.Annot.prototype.getColorAsRGB=function(){return p.sendWithPromise("Annot.getColorAsRGB",{annot:this.id}).then(function(t){return S(p.ColorPt,t)})},p.Annot.prototype.getColorAsCMYK=function(){return p.sendWithPromise("Annot.getColorAsCMYK",{annot:this.id}).then(function(t){return S(p.ColorPt,t)})},p.Annot.prototype.getColorAsGray=function(){return p.sendWithPromise("Annot.getColorAsGray",{annot:this.id}).then(function(t){return S(p.ColorPt,t)})},p.Annot.prototype.getColorCompNum=function(){return p.sendWithPromise("Annot.getColorCompNum",{annot:this.id})},p.Annot.prototype.setColorDefault=function(t){return P(arguments.length,1,"setColorDefault","(PDFNet.ColorPt)",[[t,"Object",p.ColorPt,"ColorPt"]]),p.sendWithPromise("Annot.setColorDefault",{annot:this.id,col:t.id})},p.Annot.prototype.setColor=function(t,e){return void 0===e&&(e=3),P(arguments.length,1,"setColor","(PDFNet.ColorPt, number)",[[t,"Object",p.ColorPt,"ColorPt"],[e,"number"]]),p.sendWithPromise("Annot.setColor",{annot:this.id,col:t.id,numcomp:e})},p.Annot.prototype.getStructParent=function(){return p.sendWithPromise("Annot.getStructParent",{annot:this.id})},p.Annot.prototype.setStructParent=function(t){return P(arguments.length,1,"setStructParent","(number)",[[t,"number"]]),p.sendWithPromise("Annot.setStructParent",{annot:this.id,parkeyval:t})},p.Annot.prototype.getOptionalContent=function(){return p.sendWithPromise("Annot.getOptionalContent",{annot:this.id}).then(function(t){return _(p.Obj,t)})},p.Annot.prototype.setOptionalContent=function(t){return P(arguments.length,1,"setOptionalContent","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("Annot.setOptionalContent",{annot:this.id,content:t.id})},p.Annot.prototype.refreshAppearance=function(){return p.sendWithPromise("Annot.refreshAppearance",{annot:this.id})},p.Annot.prototype.refreshAppearanceRefreshOptions=function(t){return void 0===t&&(t=null),P(arguments.length,0,"refreshAppearanceRefreshOptions","(PDFNet.OptionBase)",[[t,"OptionBase"]]),b("refreshAppearanceRefreshOptions",[[t,0]]),t=t?t.getJsonString():"{}",p.sendWithPromise("Annot.refreshAppearanceRefreshOptions",{annot:this.id,options:t})},p.Annot.prototype.getRotation=function(){return p.sendWithPromise("Annot.getRotation",{annot:this.id})},p.Annot.prototype.setRotation=function(t){return P(arguments.length,1,"setRotation","(number)",[[t,"number"]]),p.sendWithPromise("Annot.setRotation",{annot:this.id,angle:t})},p.AnnotBorderStyle.prototype.getWidth=function(){return p.sendWithPromise("AnnotBorderStyle.getWidth",{bs:this.id})},p.AnnotBorderStyle.prototype.setWidth=function(t){return P(arguments.length,1,"setWidth","(number)",[[t,"number"]]),p.sendWithPromise("AnnotBorderStyle.setWidth",{bs:this.id,width:t})},p.AnnotBorderStyle.prototype.getHR=function(){return p.sendWithPromise("AnnotBorderStyle.getHR",{bs:this.id})},p.AnnotBorderStyle.prototype.setHR=function(t){return P(arguments.length,1,"setHR","(number)",[[t,"number"]]),p.sendWithPromise("AnnotBorderStyle.setHR",{bs:this.id,horizontal_radius:t})},p.AnnotBorderStyle.prototype.getVR=function(){return p.sendWithPromise("AnnotBorderStyle.getVR",{bs:this.id})},p.AnnotBorderStyle.prototype.setVR=function(t){return P(arguments.length,1,"setVR","(number)",[[t,"number"]]),p.sendWithPromise("AnnotBorderStyle.setVR",{bs:this.id,vertical_radius:t})},p.AnnotBorderStyle.prototype.getDashPattern=function(){return p.sendWithPromise("AnnotBorderStyle.getDashPattern",{bs:this.id}).then(function(t){return new Float64Array(t)})},p.Annot.prototype.getBorderStyle=function(){return p.sendWithPromise("Annot.getBorderStyle",{annot:this.id}).then(function(t){return S(p.AnnotBorderStyle,t)})},p.Annot.prototype.setBorderStyle=function(t,e){return void 0===e&&(e=!1),P(arguments.length,1,"setBorderStyle","(PDFNet.AnnotBorderStyle, boolean)",[[t,"Object",p.AnnotBorderStyle,"AnnotBorderStyle"],[e,"boolean"]]),p.sendWithPromise("Annot.setBorderStyle",{annot:this.id,bs:t.id,oldStyleOnly:e})},p.Annot.getBorderStyleStyle=function(t){return P(arguments.length,1,"getBorderStyleStyle","(PDFNet.AnnotBorderStyle)",[[t,"Object",p.AnnotBorderStyle,"AnnotBorderStyle"]]),p.sendWithPromise("annotGetBorderStyleStyle",{bs:t.id})},p.Annot.setBorderStyleStyle=function(t,e){return P(arguments.length,2,"setBorderStyleStyle","(PDFNet.AnnotBorderStyle, number)",[[t,"Object",p.AnnotBorderStyle,"AnnotBorderStyle"],[e,"number"]]),p.sendWithPromise("annotSetBorderStyleStyle",{bs:t.id,bst:e})},p.AnnotBorderStyle.prototype.compare=function(t){return P(arguments.length,1,"compare","(PDFNet.AnnotBorderStyle)",[[t,"Object",p.AnnotBorderStyle,"AnnotBorderStyle"]]),p.sendWithPromise("AnnotBorderStyle.compare",{a:this.id,b:t.id})},p.CaretAnnot.createFromObj=function(t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,0,"createFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("caretAnnotCreateFromObj",{d:t.id}).then(function(t){return _(p.CaretAnnot,t)})},p.CaretAnnot.createFromAnnot=function(t){return P(arguments.length,1,"createFromAnnot","(PDFNet.Annot)",[[t,"Object",p.Annot,"Annot"]]),p.sendWithPromise("caretAnnotCreateFromAnnot",{ann:t.id}).then(function(t){return _(p.CaretAnnot,t)})},p.CaretAnnot.create=function(t,e){return P(arguments.length,2,"create","(PDFNet.SDFDoc, PDFNet.Rect)",[[t,"SDFDoc"],[e,"Structure",p.Rect,"Rect"]]),b("create",[[e,1]]),p.sendWithPromise("caretAnnotCreate",{doc:t.id,pos:e}).then(function(t){return _(p.CaretAnnot,t)})},p.CaretAnnot.prototype.getSymbol=function(){return p.sendWithPromise("CaretAnnot.getSymbol",{caret:this.id})},p.CaretAnnot.prototype.setSymbol=function(t){return P(arguments.length,1,"setSymbol","(string)",[[t,"string"]]),p.sendWithPromise("CaretAnnot.setSymbol",{caret:this.id,symbol:t})},p.LineAnnot.createFromObj=function(t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,0,"createFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("lineAnnotCreateFromObj",{d:t.id}).then(function(t){return _(p.LineAnnot,t)})},p.LineAnnot.createFromAnnot=function(t){return P(arguments.length,1,"createFromAnnot","(PDFNet.Annot)",[[t,"Object",p.Annot,"Annot"]]),p.sendWithPromise("lineAnnotCreateFromAnnot",{ann:t.id}).then(function(t){return _(p.LineAnnot,t)})},p.LineAnnot.create=function(t,e){return P(arguments.length,2,"create","(PDFNet.SDFDoc, PDFNet.Rect)",[[t,"SDFDoc"],[e,"Structure",p.Rect,"Rect"]]),b("create",[[e,1]]),p.sendWithPromise("lineAnnotCreate",{doc:t.id,pos:e}).then(function(t){return _(p.LineAnnot,t)})},p.LineAnnot.prototype.getStartPoint=function(){return p.sendWithPromise("LineAnnot.getStartPoint",{line:this.id})},p.LineAnnot.prototype.setStartPoint=function(t){return P(arguments.length,1,"setStartPoint","(PDFNet.Point)",[[t,"Structure",p.Point,"Point"]]),b("setStartPoint",[[t,0]]),p.sendWithPromise("LineAnnot.setStartPoint",{line:this.id,sp:t})},p.LineAnnot.prototype.getEndPoint=function(){return p.sendWithPromise("LineAnnot.getEndPoint",{line:this.id})},p.LineAnnot.prototype.setEndPoint=function(t){return P(arguments.length,1,"setEndPoint","(PDFNet.Point)",[[t,"Structure",p.Point,"Point"]]),b("setEndPoint",[[t,0]]),p.sendWithPromise("LineAnnot.setEndPoint",{line:this.id,ep:t})},p.LineAnnot.prototype.getStartStyle=function(){return p.sendWithPromise("LineAnnot.getStartStyle",{line:this.id})},p.LineAnnot.prototype.setStartStyle=function(t){return P(arguments.length,1,"setStartStyle","(number)",[[t,"number"]]),p.sendWithPromise("LineAnnot.setStartStyle",{line:this.id,ss:t})},p.LineAnnot.prototype.getEndStyle=function(){return p.sendWithPromise("LineAnnot.getEndStyle",{line:this.id})},p.LineAnnot.prototype.setEndStyle=function(t){return P(arguments.length,1,"setEndStyle","(number)",[[t,"number"]]),p.sendWithPromise("LineAnnot.setEndStyle",{line:this.id,es:t})},p.LineAnnot.prototype.getLeaderLineLength=function(){return p.sendWithPromise("LineAnnot.getLeaderLineLength",{line:this.id})},p.LineAnnot.prototype.setLeaderLineLength=function(t){return P(arguments.length,1,"setLeaderLineLength","(number)",[[t,"number"]]),p.sendWithPromise("LineAnnot.setLeaderLineLength",{line:this.id,length:t})},p.LineAnnot.prototype.getLeaderLineExtensionLength=function(){return p.sendWithPromise("LineAnnot.getLeaderLineExtensionLength",{line:this.id})},p.LineAnnot.prototype.setLeaderLineExtensionLength=function(t){return P(arguments.length,1,"setLeaderLineExtensionLength","(number)",[[t,"number"]]),p.sendWithPromise("LineAnnot.setLeaderLineExtensionLength",{line:this.id,length:t})},p.LineAnnot.prototype.getShowCaption=function(){return p.sendWithPromise("LineAnnot.getShowCaption",{line:this.id})},p.LineAnnot.prototype.setShowCaption=function(t){return P(arguments.length,1,"setShowCaption","(boolean)",[[t,"boolean"]]),p.sendWithPromise("LineAnnot.setShowCaption",{line:this.id,showCaption:t})},p.LineAnnot.prototype.getIntentType=function(){return p.sendWithPromise("LineAnnot.getIntentType",{line:this.id})},p.LineAnnot.prototype.setIntentType=function(t){return P(arguments.length,1,"setIntentType","(number)",[[t,"number"]]),p.sendWithPromise("LineAnnot.setIntentType",{line:this.id,it:t})},p.LineAnnot.prototype.getCapPos=function(){return p.sendWithPromise("LineAnnot.getCapPos",{line:this.id})},p.LineAnnot.prototype.setCapPos=function(t){return P(arguments.length,1,"setCapPos","(number)",[[t,"number"]]),p.sendWithPromise("LineAnnot.setCapPos",{line:this.id,it:t})},p.LineAnnot.prototype.getLeaderLineOffset=function(){return p.sendWithPromise("LineAnnot.getLeaderLineOffset",{line:this.id})},p.LineAnnot.prototype.setLeaderLineOffset=function(t){return P(arguments.length,1,"setLeaderLineOffset","(number)",[[t,"number"]]),p.sendWithPromise("LineAnnot.setLeaderLineOffset",{line:this.id,length:t})},p.LineAnnot.prototype.getTextHOffset=function(){return p.sendWithPromise("LineAnnot.getTextHOffset",{line:this.id})},p.LineAnnot.prototype.setTextHOffset=function(t){return P(arguments.length,1,"setTextHOffset","(number)",[[t,"number"]]),p.sendWithPromise("LineAnnot.setTextHOffset",{line:this.id,offset:t})},p.LineAnnot.prototype.getTextVOffset=function(){return p.sendWithPromise("LineAnnot.getTextVOffset",{line:this.id})},p.LineAnnot.prototype.setTextVOffset=function(t){return P(arguments.length,1,"setTextVOffset","(number)",[[t,"number"]]),p.sendWithPromise("LineAnnot.setTextVOffset",{line:this.id,offset:t})},p.CircleAnnot.createFromObj=function(t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,0,"createFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("circleAnnotCreateFromObj",{d:t.id}).then(function(t){return _(p.CircleAnnot,t)})},p.CircleAnnot.createFromAnnot=function(t){return P(arguments.length,1,"createFromAnnot","(PDFNet.Annot)",[[t,"Object",p.Annot,"Annot"]]),p.sendWithPromise("circleAnnotCreateFromAnnot",{circle:t.id}).then(function(t){return _(p.CircleAnnot,t)})},p.CircleAnnot.create=function(t,e){return P(arguments.length,2,"create","(PDFNet.SDFDoc, PDFNet.Rect)",[[t,"SDFDoc"],[e,"Structure",p.Rect,"Rect"]]),b("create",[[e,1]]),p.sendWithPromise("circleAnnotCreate",{doc:t.id,pos:e}).then(function(t){return _(p.CircleAnnot,t)})},p.CircleAnnot.prototype.getInteriorColor=function(){return p.sendWithPromise("CircleAnnot.getInteriorColor",{circle:this.id}).then(function(t){return S(p.ColorPt,t)})},p.CircleAnnot.prototype.getInteriorColorCompNum=function(){return p.sendWithPromise("CircleAnnot.getInteriorColorCompNum",{circle:this.id})},p.CircleAnnot.prototype.setInteriorColorDefault=function(t){return P(arguments.length,1,"setInteriorColorDefault","(PDFNet.ColorPt)",[[t,"Object",p.ColorPt,"ColorPt"]]),p.sendWithPromise("CircleAnnot.setInteriorColorDefault",{circle:this.id,col:t.id})},p.CircleAnnot.prototype.setInteriorColor=function(t,e){return P(arguments.length,2,"setInteriorColor","(PDFNet.ColorPt, number)",[[t,"Object",p.ColorPt,"ColorPt"],[e,"number"]]),p.sendWithPromise("CircleAnnot.setInteriorColor",{circle:this.id,col:t.id,numcomp:e})},p.CircleAnnot.prototype.getContentRect=function(){return p.sendWithPromise("CircleAnnot.getContentRect",{circle:this.id}).then(function(t){return new p.Rect(t)})},p.CircleAnnot.prototype.setContentRect=function(t){return P(arguments.length,1,"setContentRect","(PDFNet.Rect)",[[t,"Structure",p.Rect,"Rect"]]),b("setContentRect",[[t,0]]),p.sendWithPromise("CircleAnnot.setContentRect",{circle:this.id,cr:t})},p.CircleAnnot.prototype.getPadding=function(){return p.sendWithPromise("CircleAnnot.getPadding",{circle:this.id}).then(function(t){return new p.Rect(t)})},p.CircleAnnot.prototype.setPadding=function(t){return P(arguments.length,1,"setPadding","(PDFNet.Rect)",[[t,"Structure",p.Rect,"Rect"]]),b("setPadding",[[t,0]]),p.sendWithPromise("CircleAnnot.setPadding",{circle:this.id,cr:t})},p.FileAttachmentAnnot.createFromObj=function(t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,0,"createFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("fileAttachmentAnnotCreateFromObj",{d:t.id}).then(function(t){return _(p.FileAttachmentAnnot,t)})},p.FileAttachmentAnnot.prototype.export=function(t){return void 0===t&&(t=""),P(arguments.length,0,"export","(string)",[[t,"string"]]),p.sendWithPromise("FileAttachmentAnnot.export",{fileatt:this.id,save_as:t})},p.FileAttachmentAnnot.prototype.createFromAnnot=function(){return p.sendWithPromise("FileAttachmentAnnot.createFromAnnot",{fileatt:this.id}).then(function(t){return _(p.Annot,t)})},p.FileAttachmentAnnot.createWithFileSpec=function(t,e,n,i){return void 0===i&&(i=p.FileAttachmentAnnot.Icon.e_PushPin),P(arguments.length,3,"createWithFileSpec","(PDFNet.SDFDoc, PDFNet.Rect, PDFNet.FileSpec, number)",[[t,"SDFDoc"],[e,"Structure",p.Rect,"Rect"],[n,"Object",p.FileSpec,"FileSpec"],[i,"number"]]),b("createWithFileSpec",[[e,1]]),p.sendWithPromise("fileAttachmentAnnotCreateWithFileSpec",{doc:t.id,pos:e,fs:n.id,icon_name:i}).then(function(t){return _(p.FileAttachmentAnnot,t)})},p.FileAttachmentAnnot.createDefault=function(t,e,n){return P(arguments.length,3,"createDefault","(PDFNet.SDFDoc, PDFNet.Rect, string)",[[t,"SDFDoc"],[e,"Structure",p.Rect,"Rect"],[n,"string"]]),b("createDefault",[[e,1]]),p.sendWithPromise("fileAttachmentAnnotCreateDefault",{doc:t.id,pos:e,path:n}).then(function(t){return _(p.FileAttachmentAnnot,t)})},p.FileAttachmentAnnot.prototype.getFileSpec=function(){return p.sendWithPromise("FileAttachmentAnnot.getFileSpec",{fileatt:this.id}).then(function(t){return _(p.FileSpec,t)})},p.FileAttachmentAnnot.prototype.setFileSpec=function(t){return P(arguments.length,1,"setFileSpec","(PDFNet.FileSpec)",[[t,"Object",p.FileSpec,"FileSpec"]]),p.sendWithPromise("FileAttachmentAnnot.setFileSpec",{fileatt:this.id,file:t.id})},p.FileAttachmentAnnot.prototype.getIcon=function(){return p.sendWithPromise("FileAttachmentAnnot.getIcon",{fileatt:this.id})},p.FileAttachmentAnnot.prototype.setIcon=function(t){return void 0===t&&(t=p.FileAttachmentAnnot.Icon.e_PushPin),P(arguments.length,0,"setIcon","(number)",[[t,"number"]]),p.sendWithPromise("FileAttachmentAnnot.setIcon",{fileatt:this.id,type:t})},p.FileAttachmentAnnot.prototype.getIconName=function(){return p.sendWithPromise("FileAttachmentAnnot.getIconName",{fileatt:this.id})},p.FileAttachmentAnnot.prototype.setIconName=function(t){return P(arguments.length,1,"setIconName","(string)",[[t,"string"]]),p.sendWithPromise("FileAttachmentAnnot.setIconName",{fileatt:this.id,iname:t})},p.FreeTextAnnot.createFromObj=function(t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,0,"createFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("freeTextAnnotCreateFromObj",{d:t.id}).then(function(t){return _(p.FreeTextAnnot,t)})},p.FreeTextAnnot.createFromAnnot=function(t){return P(arguments.length,1,"createFromAnnot","(PDFNet.Annot)",[[t,"Object",p.Annot,"Annot"]]),p.sendWithPromise("freeTextAnnotCreateFromAnnot",{ann:t.id}).then(function(t){return _(p.FreeTextAnnot,t)})},p.FreeTextAnnot.create=function(t,e){return P(arguments.length,2,"create","(PDFNet.SDFDoc, PDFNet.Rect)",[[t,"SDFDoc"],[e,"Structure",p.Rect,"Rect"]]),b("create",[[e,1]]),p.sendWithPromise("freeTextAnnotCreate",{doc:t.id,pos:e}).then(function(t){return _(p.FreeTextAnnot,t)})},p.FreeTextAnnot.prototype.getDefaultAppearance=function(){return p.sendWithPromise("FreeTextAnnot.getDefaultAppearance",{ft:this.id})},p.FreeTextAnnot.prototype.setDefaultAppearance=function(t){return P(arguments.length,1,"setDefaultAppearance","(string)",[[t,"string"]]),p.sendWithPromise("FreeTextAnnot.setDefaultAppearance",{ft:this.id,app_str:t})},p.FreeTextAnnot.prototype.getQuaddingFormat=function(){return p.sendWithPromise("FreeTextAnnot.getQuaddingFormat",{ft:this.id})},p.FreeTextAnnot.prototype.setQuaddingFormat=function(t){return P(arguments.length,1,"setQuaddingFormat","(number)",[[t,"number"]]),p.sendWithPromise("FreeTextAnnot.setQuaddingFormat",{ft:this.id,format:t})},p.FreeTextAnnot.prototype.getCalloutLinePoints=function(){return p.sendWithPromise("FreeTextAnnot.getCalloutLinePoints",{ft:this.id})},p.FreeTextAnnot.prototype.setCalloutLinePoints=function(t,e,n){return P(arguments.length,3,"setCalloutLinePoints","(PDFNet.Point, PDFNet.Point, PDFNet.Point)",[[t,"Structure",p.Point,"Point"],[e,"Structure",p.Point,"Point"],[n,"Structure",p.Point,"Point"]]),b("setCalloutLinePoints",[[t,0],[e,1],[n,2]]),p.sendWithPromise("FreeTextAnnot.setCalloutLinePoints",{ft:this.id,p1:t,p2:e,p3:n})},p.FreeTextAnnot.prototype.setCalloutLinePointsTwo=function(t,e){return P(arguments.length,2,"setCalloutLinePointsTwo","(PDFNet.Point, PDFNet.Point)",[[t,"Structure",p.Point,"Point"],[e,"Structure",p.Point,"Point"]]),b("setCalloutLinePointsTwo",[[t,0],[e,1]]),p.sendWithPromise("FreeTextAnnot.setCalloutLinePointsTwo",{ft:this.id,p1:t,p2:e})},p.FreeTextAnnot.prototype.getIntentName=function(){return p.sendWithPromise("FreeTextAnnot.getIntentName",{ft:this.id})},p.FreeTextAnnot.prototype.setIntentName=function(t){return void 0===t&&(t=p.FreeTextAnnot.IntentName.e_FreeText),P(arguments.length,0,"setIntentName","(number)",[[t,"number"]]),p.sendWithPromise("FreeTextAnnot.setIntentName",{ft:this.id,mode:t})},p.FreeTextAnnot.prototype.setIntentNameDefault=function(){return p.sendWithPromise("FreeTextAnnot.setIntentNameDefault",{ft:this.id})},p.FreeTextAnnot.prototype.getEndingStyle=function(){return p.sendWithPromise("FreeTextAnnot.getEndingStyle",{ft:this.id})},p.FreeTextAnnot.prototype.setEndingStyle=function(t){return P(arguments.length,1,"setEndingStyle","(number)",[[t,"number"]]),p.sendWithPromise("FreeTextAnnot.setEndingStyle",{ft:this.id,style:t})},p.FreeTextAnnot.prototype.setEndingStyleName=function(t){return P(arguments.length,1,"setEndingStyleName","(string)",[[t,"string"]]),p.sendWithPromise("FreeTextAnnot.setEndingStyleName",{ft:this.id,est:t})},p.FreeTextAnnot.prototype.setTextColor=function(t,e){return P(arguments.length,2,"setTextColor","(PDFNet.ColorPt, number)",[[t,"Object",p.ColorPt,"ColorPt"],[e,"number"]]),p.sendWithPromise("FreeTextAnnot.setTextColor",{ft:this.id,color:t.id,col_comp:e})},p.FreeTextAnnot.prototype.getTextColor=function(){return p.sendWithPromise("FreeTextAnnot.getTextColor",{ft:this.id}).then(function(t){return t.color=S(p.ColorPt,t.color),t})},p.FreeTextAnnot.prototype.setLineColor=function(t,e){return P(arguments.length,2,"setLineColor","(PDFNet.ColorPt, number)",[[t,"Object",p.ColorPt,"ColorPt"],[e,"number"]]),p.sendWithPromise("FreeTextAnnot.setLineColor",{ft:this.id,color:t.id,col_comp:e})},p.FreeTextAnnot.prototype.getLineColor=function(){return p.sendWithPromise("FreeTextAnnot.getLineColor",{ft:this.id}).then(function(t){return t.color=S(p.ColorPt,t.color),t})},p.FreeTextAnnot.prototype.setFontName=function(t){return P(arguments.length,1,"setFontName","(string)",[[t,"string"]]),p.sendWithPromise("FreeTextAnnot.setFontName",{ft:this.id,fontName:t})},p.FreeTextAnnot.prototype.setFontSize=function(t){return P(arguments.length,1,"setFontSize","(number)",[[t,"number"]]),p.sendWithPromise("FreeTextAnnot.setFontSize",{ft:this.id,font_size:t})},p.FreeTextAnnot.prototype.getFontSize=function(){return p.sendWithPromise("FreeTextAnnot.getFontSize",{ft:this.id})},p.HighlightAnnot.createFromObj=function(t){return P(arguments.length,1,"createFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("highlightAnnotCreateFromObj",{d:t.id}).then(function(t){return _(p.HighlightAnnot,t)})},p.HighlightAnnot.createFromAnnot=function(t){return P(arguments.length,1,"createFromAnnot","(PDFNet.Annot)",[[t,"Object",p.Annot,"Annot"]]),p.sendWithPromise("highlightAnnotCreateFromAnnot",{ann:t.id}).then(function(t){return _(p.HighlightAnnot,t)})},p.HighlightAnnot.create=function(t,e){return P(arguments.length,2,"create","(PDFNet.SDFDoc, PDFNet.Rect)",[[t,"SDFDoc"],[e,"Structure",p.Rect,"Rect"]]),b("create",[[e,1]]),p.sendWithPromise("highlightAnnotCreate",{doc:t.id,pos:e}).then(function(t){return _(p.HighlightAnnot,t)})},p.InkAnnot.createFromObj=function(t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,0,"createFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("inkAnnotCreateFromObj",{d:t.id}).then(function(t){return _(p.InkAnnot,t)})},p.InkAnnot.createFromAnnot=function(t){return P(arguments.length,1,"createFromAnnot","(PDFNet.Annot)",[[t,"Object",p.Annot,"Annot"]]),p.sendWithPromise("inkAnnotCreateFromAnnot",{ann:t.id}).then(function(t){return _(p.InkAnnot,t)})},p.InkAnnot.create=function(t,e){return P(arguments.length,2,"create","(PDFNet.SDFDoc, PDFNet.Rect)",[[t,"SDFDoc"],[e,"Structure",p.Rect,"Rect"]]),b("create",[[e,1]]),p.sendWithPromise("inkAnnotCreate",{doc:t.id,pos:e}).then(function(t){return _(p.InkAnnot,t)})},p.InkAnnot.prototype.getPathCount=function(){return p.sendWithPromise("InkAnnot.getPathCount",{ink:this.id})},p.InkAnnot.prototype.getPointCount=function(t){return P(arguments.length,1,"getPointCount","(number)",[[t,"number"]]),p.sendWithPromise("InkAnnot.getPointCount",{ink:this.id,pathindex:t})},p.InkAnnot.prototype.getPoint=function(t,e){return P(arguments.length,2,"getPoint","(number, number)",[[t,"number"],[e,"number"]]),p.sendWithPromise("InkAnnot.getPoint",{ink:this.id,pathindex:t,pointindex:e})},p.InkAnnot.prototype.setPoint=function(t,e,n){return P(arguments.length,3,"setPoint","(number, number, PDFNet.Point)",[[t,"number"],[e,"number"],[n,"Structure",p.Point,"Point"]]),b("setPoint",[[n,2]]),p.sendWithPromise("InkAnnot.setPoint",{ink:this.id,pathindex:t,pointindex:e,pt:n})},p.InkAnnot.prototype.erase=function(t,e,n){return P(arguments.length,3,"erase","(PDFNet.Point, PDFNet.Point, number)",[[t,"Structure",p.Point,"Point"],[e,"Structure",p.Point,"Point"],[n,"number"]]),b("erase",[[t,0],[e,1]]),p.sendWithPromise("InkAnnot.erase",{ink:this.id,pt1:t,pt2:e,width:n})},p.InkAnnot.prototype.getHighlightIntent=function(){return p.sendWithPromise("InkAnnot.getHighlightIntent",{ink:this.id})},p.InkAnnot.prototype.setHighlightIntent=function(t){return P(arguments.length,1,"setHighlightIntent","(boolean)",[[t,"boolean"]]),p.sendWithPromise("InkAnnot.setHighlightIntent",{ink:this.id,highlight:t})},p.LinkAnnot.createFromObj=function(t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,0,"createFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("linkAnnotCreateFromObj",{d:t.id}).then(function(t){return _(p.LinkAnnot,t)})},p.LinkAnnot.createFromAnnot=function(t){return P(arguments.length,1,"createFromAnnot","(PDFNet.Annot)",[[t,"Object",p.Annot,"Annot"]]),p.sendWithPromise("linkAnnotCreateFromAnnot",{ann:t.id}).then(function(t){return _(p.LinkAnnot,t)})},p.LinkAnnot.create=function(t,e){return P(arguments.length,2,"create","(PDFNet.SDFDoc, PDFNet.Rect)",[[t,"SDFDoc"],[e,"Structure",p.Rect,"Rect"]]),b("create",[[e,1]]),p.sendWithPromise("linkAnnotCreate",{doc:t.id,pos:e}).then(function(t){return _(p.LinkAnnot,t)})},p.LinkAnnot.prototype.removeAction=function(){return p.sendWithPromise("LinkAnnot.removeAction",{link:this.id})},p.LinkAnnot.prototype.getAction=function(){return p.sendWithPromise("LinkAnnot.getAction",{link:this.id}).then(function(t){return _(p.Action,t)})},p.LinkAnnot.prototype.setAction=function(t){return P(arguments.length,1,"setAction","(PDFNet.Action)",[[t,"Object",p.Action,"Action"]]),p.sendWithPromise("LinkAnnot.setAction",{link:this.id,action:t.id})},p.LinkAnnot.prototype.getHighlightingMode=function(){return p.sendWithPromise("LinkAnnot.getHighlightingMode",{link:this.id})},p.LinkAnnot.prototype.setHighlightingMode=function(t){return P(arguments.length,1,"setHighlightingMode","(number)",[[t,"number"]]),p.sendWithPromise("LinkAnnot.setHighlightingMode",{link:this.id,value:t})},p.LinkAnnot.prototype.getQuadPointCount=function(){return p.sendWithPromise("LinkAnnot.getQuadPointCount",{link:this.id})},p.LinkAnnot.prototype.getQuadPoint=function(t){return P(arguments.length,1,"getQuadPoint","(number)",[[t,"number"]]),p.sendWithPromise("LinkAnnot.getQuadPoint",{link:this.id,idx:t})},p.LinkAnnot.prototype.setQuadPoint=function(t,e){return P(arguments.length,2,"setQuadPoint","(number, PDFNet.QuadPoint)",[[t,"number"],[e,"Structure",p.QuadPoint,"QuadPoint"]]),b("setQuadPoint",[[e,1]]),p.sendWithPromise("LinkAnnot.setQuadPoint",{link:this.id,idx:t,qp:e})},p.getNormalizedUrl=function(t){return P(arguments.length,1,"getNormalizedUrl","(string)",[[t,"string"]]),p.sendWithPromise("getNormalizedUrl",{url:t})},p.MarkupAnnot.createFromObj=function(t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,0,"createFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("markupAnnotCreateFromObj",{d:t.id}).then(function(t){return _(p.MarkupAnnot,t)})},p.MarkupAnnot.createFromAnnot=function(t){return P(arguments.length,1,"createFromAnnot","(PDFNet.Annot)",[[t,"Object",p.Annot,"Annot"]]),p.sendWithPromise("markupAnnotCreateFromAnnot",{ann:t.id}).then(function(t){return _(p.MarkupAnnot,t)})},p.MarkupAnnot.prototype.getTitle=function(){return p.sendWithPromise("MarkupAnnot.getTitle",{markup:this.id})},p.MarkupAnnot.prototype.setTitle=function(t){return P(arguments.length,1,"setTitle","(string)",[[t,"string"]]),p.sendWithPromise("MarkupAnnot.setTitle",{markup:this.id,title:t})},p.MarkupAnnot.prototype.setTitleUString=function(t){return P(arguments.length,1,"setTitleUString","(string)",[[t,"string"]]),p.sendWithPromise("MarkupAnnot.setTitleUString",{markup:this.id,title:t})},p.MarkupAnnot.prototype.getPopup=function(){return p.sendWithPromise("MarkupAnnot.getPopup",{markup:this.id}).then(function(t){return _(p.Annot,t)})},p.MarkupAnnot.prototype.setPopup=function(t){return P(arguments.length,1,"setPopup","(PDFNet.Annot)",[[t,"Object",p.Annot,"Annot"]]),p.sendWithPromise("MarkupAnnot.setPopup",{markup:this.id,ppup:t.id})},p.MarkupAnnot.prototype.getOpacity=function(){return p.sendWithPromise("MarkupAnnot.getOpacity",{markup:this.id})},p.MarkupAnnot.prototype.setOpacity=function(t){return P(arguments.length,1,"setOpacity","(number)",[[t,"number"]]),p.sendWithPromise("MarkupAnnot.setOpacity",{markup:this.id,op:t})},p.MarkupAnnot.prototype.getSubject=function(){return p.sendWithPromise("MarkupAnnot.getSubject",{markup:this.id})},p.MarkupAnnot.prototype.setSubject=function(t){return P(arguments.length,1,"setSubject","(string)",[[t,"string"]]),p.sendWithPromise("MarkupAnnot.setSubject",{markup:this.id,contents:t})},p.MarkupAnnot.prototype.getCreationDates=function(){return p.sendWithPromise("MarkupAnnot.getCreationDates",{markup:this.id}).then(function(t){return new p.Date(t)})},p.MarkupAnnot.prototype.getBorderEffect=function(){return p.sendWithPromise("MarkupAnnot.getBorderEffect",{markup:this.id})},p.MarkupAnnot.prototype.setBorderEffect=function(t){return void 0===t&&(t=p.MarkupAnnot.BorderEffect.e_None),P(arguments.length,0,"setBorderEffect","(number)",[[t,"number"]]),p.sendWithPromise("MarkupAnnot.setBorderEffect",{markup:this.id,effect:t})},p.MarkupAnnot.prototype.getBorderEffectIntensity=function(){return p.sendWithPromise("MarkupAnnot.getBorderEffectIntensity",{markup:this.id})},p.MarkupAnnot.prototype.setBorderEffectIntensity=function(t){return void 0===t&&(t=0),P(arguments.length,0,"setBorderEffectIntensity","(number)",[[t,"number"]]),p.sendWithPromise("MarkupAnnot.setBorderEffectIntensity",{markup:this.id,intensity:t})},p.MarkupAnnot.prototype.setCreationDates=function(t){return P(arguments.length,1,"setCreationDates","(PDFNet.Date)",[[t,"Structure",p.Date,"Date"]]),b("setCreationDates",[[t,0]]),p.sendWithPromise("MarkupAnnot.setCreationDates",{markup:this.id,dt:t})},p.MarkupAnnot.prototype.getInteriorColor=function(){return p.sendWithPromise("MarkupAnnot.getInteriorColor",{markup:this.id}).then(function(t){return S(p.ColorPt,t)})},p.MarkupAnnot.prototype.getInteriorColorCompNum=function(){return p.sendWithPromise("MarkupAnnot.getInteriorColorCompNum",{markup:this.id})},p.MarkupAnnot.prototype.setInteriorColorRGB=function(t){return P(arguments.length,1,"setInteriorColorRGB","(PDFNet.ColorPt)",[[t,"Object",p.ColorPt,"ColorPt"]]),p.sendWithPromise("MarkupAnnot.setInteriorColorRGB",{markup:this.id,col:t.id})},p.MarkupAnnot.prototype.setInteriorColor=function(t,e){return P(arguments.length,2,"setInteriorColor","(PDFNet.ColorPt, number)",[[t,"Object",p.ColorPt,"ColorPt"],[e,"number"]]),p.sendWithPromise("MarkupAnnot.setInteriorColor",{markup:this.id,c:t.id,CompNum:e})},p.MarkupAnnot.prototype.getContentRect=function(){return p.sendWithPromise("MarkupAnnot.getContentRect",{markup:this.id}).then(function(t){return new p.Rect(t)})},p.MarkupAnnot.prototype.setContentRect=function(t){return P(arguments.length,1,"setContentRect","(PDFNet.Rect)",[[t,"Structure",p.Rect,"Rect"]]),b("setContentRect",[[t,0]]),p.sendWithPromise("MarkupAnnot.setContentRect",{markup:this.id,cr:t})},p.MarkupAnnot.prototype.getPadding=function(){return p.sendWithPromise("MarkupAnnot.getPadding",{markup:this.id}).then(function(t){return new p.Rect(t)})},p.MarkupAnnot.prototype.setPadding=function(t){return P(arguments.length,1,"setPadding","(PDFNet.Rect)",[[t,"Structure",p.Rect,"Rect"]]),b("setPadding",[[t,0]]),p.sendWithPromise("MarkupAnnot.setPadding",{markup:this.id,rd:t})},p.MarkupAnnot.prototype.rotateAppearance=function(t){return P(arguments.length,1,"rotateAppearance","(number)",[[t,"number"]]),p.sendWithPromise("MarkupAnnot.rotateAppearance",{markup:this.id,angle:t})},p.MovieAnnot.createFromObj=function(t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,0,"createFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("movieAnnotCreateFromObj",{d:t.id}).then(function(t){return _(p.MovieAnnot,t)})},p.MovieAnnot.createFromAnnot=function(t){return P(arguments.length,1,"createFromAnnot","(PDFNet.Annot)",[[t,"Object",p.Annot,"Annot"]]),p.sendWithPromise("movieAnnotCreateFromAnnot",{ann:t.id}).then(function(t){return _(p.MovieAnnot,t)})},p.MovieAnnot.create=function(t,e){return P(arguments.length,2,"create","(PDFNet.SDFDoc, PDFNet.Rect)",[[t,"SDFDoc"],[e,"Structure",p.Rect,"Rect"]]),b("create",[[e,1]]),p.sendWithPromise("movieAnnotCreate",{doc:t.id,pos:e}).then(function(t){return _(p.MovieAnnot,t)})},p.MovieAnnot.prototype.getTitle=function(){return p.sendWithPromise("MovieAnnot.getTitle",{movie:this.id})},p.MovieAnnot.prototype.setTitle=function(t){return P(arguments.length,1,"setTitle","(string)",[[t,"string"]]),p.sendWithPromise("MovieAnnot.setTitle",{movie:this.id,title:t})},p.MovieAnnot.prototype.isToBePlayed=function(){return p.sendWithPromise("MovieAnnot.isToBePlayed",{movie:this.id})},p.MovieAnnot.prototype.setToBePlayed=function(t){return void 0===t&&(t=!0),P(arguments.length,0,"setToBePlayed","(boolean)",[[t,"boolean"]]),p.sendWithPromise("MovieAnnot.setToBePlayed",{movie:this.id,isplay:t})},p.PolyLineAnnot.createFromObj=function(t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,0,"createFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("polyLineAnnotCreateFromObj",{d:t.id}).then(function(t){return _(p.PolyLineAnnot,t)})},p.PolyLineAnnot.createFromAnnot=function(t){return P(arguments.length,1,"createFromAnnot","(PDFNet.Annot)",[[t,"Object",p.Annot,"Annot"]]),p.sendWithPromise("polyLineAnnotCreateFromAnnot",{ann:t.id}).then(function(t){return _(p.PolyLineAnnot,t)})},p.PolyLineAnnot.create=function(t,e){return P(arguments.length,2,"create","(PDFNet.SDFDoc, PDFNet.Rect)",[[t,"SDFDoc"],[e,"Structure",p.Rect,"Rect"]]),b("create",[[e,1]]),p.sendWithPromise("polyLineAnnotCreate",{doc:t.id,pos:e}).then(function(t){return _(p.PolyLineAnnot,t)})},p.PolyLineAnnot.prototype.getVertexCount=function(){return p.sendWithPromise("PolyLineAnnot.getVertexCount",{polyline:this.id})},p.PolyLineAnnot.prototype.getVertex=function(t){return P(arguments.length,1,"getVertex","(number)",[[t,"number"]]),p.sendWithPromise("PolyLineAnnot.getVertex",{polyline:this.id,idx:t})},p.PolyLineAnnot.prototype.setVertex=function(t,e){return P(arguments.length,2,"setVertex","(number, PDFNet.Point)",[[t,"number"],[e,"Structure",p.Point,"Point"]]),b("setVertex",[[e,1]]),p.sendWithPromise("PolyLineAnnot.setVertex",{polyline:this.id,idx:t,pt:e})},p.PolyLineAnnot.prototype.getStartStyle=function(){return p.sendWithPromise("PolyLineAnnot.getStartStyle",{polyline:this.id})},p.PolyLineAnnot.prototype.setStartStyle=function(t){return P(arguments.length,1,"setStartStyle","(number)",[[t,"number"]]),p.sendWithPromise("PolyLineAnnot.setStartStyle",{polyline:this.id,style:t})},p.PolyLineAnnot.prototype.getEndStyle=function(){return p.sendWithPromise("PolyLineAnnot.getEndStyle",{polyline:this.id})},p.PolyLineAnnot.prototype.setEndStyle=function(t){return P(arguments.length,1,"setEndStyle","(number)",[[t,"number"]]),p.sendWithPromise("PolyLineAnnot.setEndStyle",{polyline:this.id,style:t})},p.PolyLineAnnot.prototype.getIntentName=function(){return p.sendWithPromise("PolyLineAnnot.getIntentName",{polyline:this.id})},p.PolyLineAnnot.prototype.setIntentName=function(t){return P(arguments.length,1,"setIntentName","(number)",[[t,"number"]]),p.sendWithPromise("PolyLineAnnot.setIntentName",{polyline:this.id,mode:t})},p.PolygonAnnot.createFromObj=function(t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,0,"createFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("polygonAnnotCreateFromObj",{d:t.id}).then(function(t){return _(p.PolygonAnnot,t)})},p.PolygonAnnot.createFromAnnot=function(t){return P(arguments.length,1,"createFromAnnot","(PDFNet.Annot)",[[t,"Object",p.Annot,"Annot"]]),p.sendWithPromise("polygonAnnotCreateFromAnnot",{ann:t.id}).then(function(t){return _(p.PolygonAnnot,t)})},p.PolygonAnnot.create=function(t,e){return P(arguments.length,2,"create","(PDFNet.SDFDoc, PDFNet.Rect)",[[t,"SDFDoc"],[e,"Structure",p.Rect,"Rect"]]),b("create",[[e,1]]),p.sendWithPromise("polygonAnnotCreate",{doc:t.id,pos:e}).then(function(t){return _(p.PolygonAnnot,t)})},p.PopupAnnot.createFromObj=function(t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,0,"createFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("popupAnnotCreateFromObj",{d:t.id}).then(function(t){return _(p.PopupAnnot,t)})},p.PopupAnnot.createFromAnnot=function(t){return P(arguments.length,1,"createFromAnnot","(PDFNet.Annot)",[[t,"Object",p.Annot,"Annot"]]),p.sendWithPromise("popupAnnotCreateFromAnnot",{ann:t.id}).then(function(t){return _(p.PopupAnnot,t)})},p.PopupAnnot.create=function(t,e){return P(arguments.length,2,"create","(PDFNet.SDFDoc, PDFNet.Rect)",[[t,"SDFDoc"],[e,"Structure",p.Rect,"Rect"]]),b("create",[[e,1]]),p.sendWithPromise("popupAnnotCreate",{doc:t.id,pos:e}).then(function(t){return _(p.PopupAnnot,t)})},p.PopupAnnot.prototype.getParent=function(){return p.sendWithPromise("PopupAnnot.getParent",{popup:this.id}).then(function(t){return _(p.Annot,t)})},p.PopupAnnot.prototype.setParent=function(t){return P(arguments.length,1,"setParent","(PDFNet.Annot)",[[t,"Object",p.Annot,"Annot"]]),p.sendWithPromise("PopupAnnot.setParent",{popup:this.id,parent:t.id})},p.PopupAnnot.prototype.isOpen=function(){return p.sendWithPromise("PopupAnnot.isOpen",{popup:this.id})},p.PopupAnnot.prototype.setOpen=function(t){return P(arguments.length,1,"setOpen","(boolean)",[[t,"boolean"]]),p.sendWithPromise("PopupAnnot.setOpen",{popup:this.id,isopen:t})},p.RedactionAnnot.createFromObj=function(t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,0,"createFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("redactionAnnotCreateFromObj",{d:t.id}).then(function(t){return _(p.RedactionAnnot,t)})},p.RedactionAnnot.createFromAnnot=function(t){return P(arguments.length,1,"createFromAnnot","(PDFNet.Annot)",[[t,"Object",p.Annot,"Annot"]]),p.sendWithPromise("redactionAnnotCreateFromAnnot",{ann:t.id}).then(function(t){return _(p.RedactionAnnot,t)})},p.RedactionAnnot.create=function(t,e){return P(arguments.length,2,"create","(PDFNet.SDFDoc, PDFNet.Rect)",[[t,"SDFDoc"],[e,"Structure",p.Rect,"Rect"]]),b("create",[[e,1]]),p.sendWithPromise("redactionAnnotCreate",{doc:t.id,pos:e}).then(function(t){return _(p.RedactionAnnot,t)})},p.RedactionAnnot.prototype.getQuadPointCount=function(){return p.sendWithPromise("RedactionAnnot.getQuadPointCount",{redaction:this.id})},p.RedactionAnnot.prototype.getQuadPoint=function(t){return P(arguments.length,1,"getQuadPoint","(number)",[[t,"number"]]),p.sendWithPromise("RedactionAnnot.getQuadPoint",{redaction:this.id,idx:t})},p.RedactionAnnot.prototype.setQuadPoint=function(t,e){return P(arguments.length,2,"setQuadPoint","(number, PDFNet.QuadPoint)",[[t,"number"],[e,"Structure",p.QuadPoint,"QuadPoint"]]),b("setQuadPoint",[[e,1]]),p.sendWithPromise("RedactionAnnot.setQuadPoint",{redaction:this.id,idx:t,qp:e})},p.RedactionAnnot.prototype.setAppFormXO=function(t){return P(arguments.length,1,"setAppFormXO","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("RedactionAnnot.setAppFormXO",{redaction:this.id,formxo:t.id})},p.RedactionAnnot.prototype.getOverlayText=function(){return p.sendWithPromise("RedactionAnnot.getOverlayText",{redaction:this.id})},p.RedactionAnnot.prototype.setOverlayText=function(t){return P(arguments.length,1,"setOverlayText","(string)",[[t,"string"]]),p.sendWithPromise("RedactionAnnot.setOverlayText",{redaction:this.id,title:t})},p.RedactionAnnot.prototype.getUseRepeat=function(){return p.sendWithPromise("RedactionAnnot.getUseRepeat",{redaction:this.id})},p.RedactionAnnot.prototype.setUseRepeat=function(t){return void 0===t&&(t=!1),P(arguments.length,0,"setUseRepeat","(boolean)",[[t,"boolean"]]),p.sendWithPromise("RedactionAnnot.setUseRepeat",{redaction:this.id,userepeat:t})},p.RedactionAnnot.prototype.getOverlayTextAppearance=function(){return p.sendWithPromise("RedactionAnnot.getOverlayTextAppearance",{redaction:this.id})},p.RedactionAnnot.prototype.setOverlayTextAppearance=function(t){return P(arguments.length,1,"setOverlayTextAppearance","(string)",[[t,"string"]]),p.sendWithPromise("RedactionAnnot.setOverlayTextAppearance",{redaction:this.id,app:t})},p.RedactionAnnot.prototype.getQuadForm=function(){return p.sendWithPromise("RedactionAnnot.getQuadForm",{redaction:this.id})},p.RedactionAnnot.prototype.setQuadForm=function(t){return void 0===t&&(t=p.RedactionAnnot.QuadForm.e_LeftJustified),P(arguments.length,0,"setQuadForm","(number)",[[t,"number"]]),p.sendWithPromise("RedactionAnnot.setQuadForm",{redaction:this.id,form:t})},p.RedactionAnnot.prototype.getAppFormXO=function(){return p.sendWithPromise("RedactionAnnot.getAppFormXO",{redaction:this.id}).then(function(t){return _(p.Obj,t)})},p.RubberStampAnnot.createFromObj=function(t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,0,"createFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("rubberStampAnnotCreateFromObj",{d:t.id}).then(function(t){return _(p.RubberStampAnnot,t)})},p.RubberStampAnnot.createFromAnnot=function(t){return P(arguments.length,1,"createFromAnnot","(PDFNet.Annot)",[[t,"Object",p.Annot,"Annot"]]),p.sendWithPromise("rubberStampAnnotCreateFromAnnot",{ann:t.id}).then(function(t){return _(p.RubberStampAnnot,t)})},p.RubberStampAnnot.create=function(t,e){return P(arguments.length,2,"create","(PDFNet.SDFDoc, PDFNet.Rect)",[[t,"SDFDoc"],[e,"Structure",p.Rect,"Rect"]]),b("create",[[e,1]]),p.sendWithPromise("rubberStampAnnotCreate",{doc:t.id,pos:e}).then(function(t){return _(p.RubberStampAnnot,t)})},p.RubberStampAnnot.createCustom=function(t,e,n){return P(arguments.length,3,"createCustom","(PDFNet.SDFDoc, PDFNet.Rect, PDFNet.Obj)",[[t,"SDFDoc"],[e,"Structure",p.Rect,"Rect"],[n,"Object",p.Obj,"Obj"]]),b("createCustom",[[e,1]]),p.sendWithPromise("rubberStampAnnotCreateCustom",{doc:t.id,pos:e,form_xobject:n.id}).then(function(t){return _(p.RubberStampAnnot,t)})},p.RubberStampAnnot.prototype.getIcon=function(){return p.sendWithPromise("RubberStampAnnot.getIcon",{stamp:this.id})},p.RubberStampAnnot.prototype.setIcon=function(t){return void 0===t&&(t=p.RubberStampAnnot.Icon.e_Draft),P(arguments.length,0,"setIcon","(number)",[[t,"number"]]),p.sendWithPromise("RubberStampAnnot.setIcon",{stamp:this.id,type:t})},p.RubberStampAnnot.prototype.setIconDefault=function(){return p.sendWithPromise("RubberStampAnnot.setIconDefault",{stamp:this.id})},p.RubberStampAnnot.prototype.getIconName=function(){return p.sendWithPromise("RubberStampAnnot.getIconName",{stamp:this.id})},p.RubberStampAnnot.prototype.setIconName=function(t){return P(arguments.length,1,"setIconName","(string)",[[t,"string"]]),p.sendWithPromise("RubberStampAnnot.setIconName",{stamp:this.id,iconstring:t})},p.rubberStampAnnotSetOpacity=function(t,e){return P(arguments.length,2,"rubberStampAnnotSetOpacity","(PDFNet.Annot, number)",[[t,"Object",p.Annot,"Annot"],[e,"number"]]),p.sendWithPromise("rubberStampAnnotSetOpacity",{stamp:t.id,opacity:e})},p.ScreenAnnot.createFromObj=function(t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,0,"createFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("screenAnnotCreateFromObj",{d:t.id}).then(function(t){return _(p.ScreenAnnot,t)})},p.ScreenAnnot.createFromAnnot=function(t){return P(arguments.length,1,"createFromAnnot","(PDFNet.Annot)",[[t,"Object",p.Annot,"Annot"]]),p.sendWithPromise("screenAnnotCreateFromAnnot",{ann:t.id}).then(function(t){return _(p.ScreenAnnot,t)})},p.ScreenAnnot.prototype.getTitle=function(){return p.sendWithPromise("ScreenAnnot.getTitle",{s:this.id})},p.ScreenAnnot.prototype.setTitle=function(t){return P(arguments.length,1,"setTitle","(string)",[[t,"string"]]),p.sendWithPromise("ScreenAnnot.setTitle",{s:this.id,title:t})},p.ScreenAnnot.create=function(t,e){return P(arguments.length,2,"create","(PDFNet.SDFDoc, PDFNet.Rect)",[[t,"SDFDoc"],[e,"Structure",p.Rect,"Rect"]]),b("create",[[e,1]]),p.sendWithPromise("screenAnnotCreate",{doc:t.id,pos:e}).then(function(t){return _(p.ScreenAnnot,t)})},p.ScreenAnnot.prototype.getAction=function(){return p.sendWithPromise("ScreenAnnot.getAction",{s:this.id}).then(function(t){return _(p.Action,t)})},p.ScreenAnnot.prototype.setAction=function(t){return P(arguments.length,1,"setAction","(PDFNet.Action)",[[t,"Object",p.Action,"Action"]]),p.sendWithPromise("ScreenAnnot.setAction",{s:this.id,action:t.id})},p.ScreenAnnot.prototype.getBorderColor=function(){return p.sendWithPromise("ScreenAnnot.getBorderColor",{s:this.id}).then(function(t){return S(p.ColorPt,t)})},p.ScreenAnnot.prototype.setBorderColor=function(t,e){return P(arguments.length,2,"setBorderColor","(PDFNet.ColorPt, number)",[[t,"Object",p.ColorPt,"ColorPt"],[e,"number"]]),p.sendWithPromise("ScreenAnnot.setBorderColor",{s:this.id,col:t.id,numcomp:e})},p.ScreenAnnot.prototype.getBorderColorCompNum=function(){return p.sendWithPromise("ScreenAnnot.getBorderColorCompNum",{s:this.id})},p.ScreenAnnot.prototype.getBackgroundColorCompNum=function(){return p.sendWithPromise("ScreenAnnot.getBackgroundColorCompNum",{s:this.id})},p.ScreenAnnot.prototype.getBackgroundColor=function(){return p.sendWithPromise("ScreenAnnot.getBackgroundColor",{s:this.id}).then(function(t){return S(p.ColorPt,t)})},p.ScreenAnnot.prototype.setBackgroundColor=function(t,e){return P(arguments.length,2,"setBackgroundColor","(PDFNet.ColorPt, number)",[[t,"Object",p.ColorPt,"ColorPt"],[e,"number"]]),p.sendWithPromise("ScreenAnnot.setBackgroundColor",{s:this.id,col:t.id,numcomp:e})},p.ScreenAnnot.prototype.getStaticCaptionText=function(){return p.sendWithPromise("ScreenAnnot.getStaticCaptionText",{s:this.id})},p.ScreenAnnot.prototype.setStaticCaptionText=function(t){return P(arguments.length,1,"setStaticCaptionText","(string)",[[t,"string"]]),p.sendWithPromise("ScreenAnnot.setStaticCaptionText",{s:this.id,contents:t})},p.ScreenAnnot.prototype.getRolloverCaptionText=function(){return p.sendWithPromise("ScreenAnnot.getRolloverCaptionText",{s:this.id})},p.ScreenAnnot.prototype.setRolloverCaptionText=function(t){return P(arguments.length,1,"setRolloverCaptionText","(string)",[[t,"string"]]),p.sendWithPromise("ScreenAnnot.setRolloverCaptionText",{s:this.id,contents:t})},p.ScreenAnnot.prototype.getMouseDownCaptionText=function(){return p.sendWithPromise("ScreenAnnot.getMouseDownCaptionText",{s:this.id})},p.ScreenAnnot.prototype.setMouseDownCaptionText=function(t){return P(arguments.length,1,"setMouseDownCaptionText","(string)",[[t,"string"]]),p.sendWithPromise("ScreenAnnot.setMouseDownCaptionText",{s:this.id,contents:t})},p.ScreenAnnot.prototype.getStaticIcon=function(){return p.sendWithPromise("ScreenAnnot.getStaticIcon",{s:this.id}).then(function(t){return _(p.Obj,t)})},p.ScreenAnnot.prototype.setStaticIcon=function(t){return P(arguments.length,1,"setStaticIcon","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("ScreenAnnot.setStaticIcon",{s:this.id,icon:t.id})},p.ScreenAnnot.prototype.getRolloverIcon=function(){return p.sendWithPromise("ScreenAnnot.getRolloverIcon",{s:this.id}).then(function(t){return _(p.Obj,t)})},p.ScreenAnnot.prototype.setRolloverIcon=function(t){return P(arguments.length,1,"setRolloverIcon","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("ScreenAnnot.setRolloverIcon",{s:this.id,icon:t.id})},p.ScreenAnnot.prototype.getMouseDownIcon=function(){return p.sendWithPromise("ScreenAnnot.getMouseDownIcon",{s:this.id}).then(function(t){return _(p.Obj,t)})},p.ScreenAnnot.prototype.setMouseDownIcon=function(t){return P(arguments.length,1,"setMouseDownIcon","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("ScreenAnnot.setMouseDownIcon",{s:this.id,icon:t.id})},p.ScreenAnnot.prototype.getScaleType=function(){return p.sendWithPromise("ScreenAnnot.getScaleType",{s:this.id})},p.ScreenAnnot.prototype.setScaleType=function(t){return P(arguments.length,1,"setScaleType","(number)",[[t,"number"]]),p.sendWithPromise("ScreenAnnot.setScaleType",{s:this.id,st:t})},p.ScreenAnnot.prototype.getIconCaptionRelation=function(){return p.sendWithPromise("ScreenAnnot.getIconCaptionRelation",{s:this.id})},p.ScreenAnnot.prototype.setIconCaptionRelation=function(t){return P(arguments.length,1,"setIconCaptionRelation","(number)",[[t,"number"]]),p.sendWithPromise("ScreenAnnot.setIconCaptionRelation",{s:this.id,icr:t})},p.ScreenAnnot.prototype.getScaleCondition=function(){return p.sendWithPromise("ScreenAnnot.getScaleCondition",{s:this.id})},p.ScreenAnnot.prototype.setScaleCondition=function(t){return P(arguments.length,1,"setScaleCondition","(number)",[[t,"number"]]),p.sendWithPromise("ScreenAnnot.setScaleCondition",{s:this.id,sc:t})},p.ScreenAnnot.prototype.getFitFull=function(){return p.sendWithPromise("ScreenAnnot.getFitFull",{s:this.id})},p.ScreenAnnot.prototype.setFitFull=function(t){return P(arguments.length,1,"setFitFull","(boolean)",[[t,"boolean"]]),p.sendWithPromise("ScreenAnnot.setFitFull",{s:this.id,ff:t})},p.ScreenAnnot.prototype.getHIconLeftOver=function(){return p.sendWithPromise("ScreenAnnot.getHIconLeftOver",{s:this.id})},p.ScreenAnnot.prototype.setHIconLeftOver=function(t){return P(arguments.length,1,"setHIconLeftOver","(number)",[[t,"number"]]),p.sendWithPromise("ScreenAnnot.setHIconLeftOver",{s:this.id,hl:t})},p.ScreenAnnot.prototype.getVIconLeftOver=function(){return p.sendWithPromise("ScreenAnnot.getVIconLeftOver",{s:this.id})},p.ScreenAnnot.prototype.setVIconLeftOver=function(t){return P(arguments.length,1,"setVIconLeftOver","(number)",[[t,"number"]]),p.sendWithPromise("ScreenAnnot.setVIconLeftOver",{s:this.id,vl:t})},p.SoundAnnot.createFromObj=function(t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,0,"createFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("soundAnnotCreateFromObj",{d:t.id}).then(function(t){return _(p.SoundAnnot,t)})},p.SoundAnnot.createFromAnnot=function(t){return P(arguments.length,1,"createFromAnnot","(PDFNet.Annot)",[[t,"Object",p.Annot,"Annot"]]),p.sendWithPromise("soundAnnotCreateFromAnnot",{ann:t.id}).then(function(t){return _(p.SoundAnnot,t)})},p.SoundAnnot.create=function(t,e){return P(arguments.length,2,"create","(PDFNet.SDFDoc, PDFNet.Rect)",[[t,"SDFDoc"],[e,"Structure",p.Rect,"Rect"]]),b("create",[[e,1]]),p.sendWithPromise("soundAnnotCreate",{doc:t.id,pos:e}).then(function(t){return _(p.SoundAnnot,t)})},p.SoundAnnot.createWithData=function(t,e,n,i,r,o){return P(arguments.length,6,"createWithData","(PDFNet.SDFDoc, PDFNet.Rect, PDFNet.Filter, number, number, number)",[[t,"SDFDoc"],[e,"Structure",p.Rect,"Rect"],[n,"Object",p.Filter,"Filter"],[i,"number"],[r,"number"],[o,"number"]]),b("createWithData",[[e,1]]),0!=n.id&&A(n.id),p.sendWithPromise("soundAnnotCreateWithData",{doc:t.id,pos:e,no_own_stream:n.id,sample_bits:i,sample_freq:r,num_channels:o}).then(function(t){return _(p.SoundAnnot,t)})},p.SoundAnnot.createAtPoint=function(t,e){return P(arguments.length,2,"createAtPoint","(PDFNet.SDFDoc, PDFNet.Point)",[[t,"SDFDoc"],[e,"Structure",p.Point,"Point"]]),b("createAtPoint",[[e,1]]),p.sendWithPromise("soundAnnotCreateAtPoint",{doc:t.id,pos:e}).then(function(t){return _(p.SoundAnnot,t)})},p.SoundAnnot.prototype.getSoundStream=function(){return p.sendWithPromise("SoundAnnot.getSoundStream",{sound:this.id}).then(function(t){return _(p.Obj,t)})},p.SoundAnnot.prototype.setSoundStream=function(t){return P(arguments.length,1,"setSoundStream","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("SoundAnnot.setSoundStream",{sound:this.id,icon:t.id})},p.SoundAnnot.prototype.getIcon=function(){return p.sendWithPromise("SoundAnnot.getIcon",{sound:this.id})},p.SoundAnnot.prototype.setIcon=function(t){return void 0===t&&(t=p.SoundAnnot.Icon.e_Speaker),P(arguments.length,0,"setIcon","(number)",[[t,"number"]]),p.sendWithPromise("SoundAnnot.setIcon",{sound:this.id,type:t})},p.SoundAnnot.prototype.getIconName=function(){return p.sendWithPromise("SoundAnnot.getIconName",{sound:this.id})},p.SoundAnnot.prototype.setIconName=function(t){return P(arguments.length,1,"setIconName","(string)",[[t,"string"]]),p.sendWithPromise("SoundAnnot.setIconName",{sound:this.id,type:t})},p.SquareAnnot.createFromObj=function(t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,0,"createFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("squareAnnotCreateFromObj",{d:t.id}).then(function(t){return _(p.SquareAnnot,t)})},p.SquareAnnot.createFromAnnot=function(t){return P(arguments.length,1,"createFromAnnot","(PDFNet.Annot)",[[t,"Object",p.Annot,"Annot"]]),p.sendWithPromise("squareAnnotCreateFromAnnot",{ann:t.id}).then(function(t){return _(p.SquareAnnot,t)})},p.SquareAnnot.create=function(t,e){return P(arguments.length,2,"create","(PDFNet.SDFDoc, PDFNet.Rect)",[[t,"SDFDoc"],[e,"Structure",p.Rect,"Rect"]]),b("create",[[e,1]]),p.sendWithPromise("squareAnnotCreate",{doc:t.id,pos:e}).then(function(t){return _(p.SquareAnnot,t)})},p.SquareAnnot.prototype.getInteriorColor=function(){return p.sendWithPromise("SquareAnnot.getInteriorColor",{square:this.id}).then(function(t){return S(p.ColorPt,t)})},p.SquareAnnot.prototype.getInteriorColorCompNum=function(){return p.sendWithPromise("SquareAnnot.getInteriorColorCompNum",{square:this.id})},p.SquareAnnot.prototype.setInteriorColorDefault=function(t){return P(arguments.length,1,"setInteriorColorDefault","(PDFNet.ColorPt)",[[t,"Object",p.ColorPt,"ColorPt"]]),p.sendWithPromise("SquareAnnot.setInteriorColorDefault",{square:this.id,col:t.id})},p.SquareAnnot.prototype.setInteriorColor=function(t,e){return P(arguments.length,2,"setInteriorColor","(PDFNet.ColorPt, number)",[[t,"Object",p.ColorPt,"ColorPt"],[e,"number"]]),p.sendWithPromise("SquareAnnot.setInteriorColor",{square:this.id,col:t.id,numcomp:e})},p.SquareAnnot.prototype.getContentRect=function(){return p.sendWithPromise("SquareAnnot.getContentRect",{square:this.id}).then(function(t){return new p.Rect(t)})},p.SquareAnnot.prototype.setContentRect=function(t){return P(arguments.length,1,"setContentRect","(PDFNet.Rect)",[[t,"Structure",p.Rect,"Rect"]]),b("setContentRect",[[t,0]]),p.sendWithPromise("SquareAnnot.setContentRect",{square:this.id,cr:t})},p.SquareAnnot.prototype.getPadding=function(){return p.sendWithPromise("SquareAnnot.getPadding",{square:this.id}).then(function(t){return new p.Rect(t)})},p.SquareAnnot.prototype.setPadding=function(t){return P(arguments.length,1,"setPadding","(PDFNet.Rect)",[[t,"Structure",p.Rect,"Rect"]]),b("setPadding",[[t,0]]),p.sendWithPromise("SquareAnnot.setPadding",{square:this.id,cr:t})},p.SquigglyAnnot.createFromObj=function(t){return P(arguments.length,1,"createFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("squigglyAnnotCreateFromObj",{d:t.id}).then(function(t){return _(p.SquigglyAnnot,t)})},p.SquigglyAnnot.createFromAnnot=function(t){return P(arguments.length,1,"createFromAnnot","(PDFNet.Annot)",[[t,"Object",p.Annot,"Annot"]]),p.sendWithPromise("squigglyAnnotCreateFromAnnot",{ann:t.id}).then(function(t){return _(p.SquigglyAnnot,t)})},p.SquigglyAnnot.create=function(t,e){return P(arguments.length,2,"create","(PDFNet.SDFDoc, PDFNet.Rect)",[[t,"SDFDoc"],[e,"Structure",p.Rect,"Rect"]]),b("create",[[e,1]]),p.sendWithPromise("squigglyAnnotCreate",{doc:t.id,pos:e}).then(function(t){return _(p.SquigglyAnnot,t)})},p.StrikeOutAnnot.createFromObj=function(t){return P(arguments.length,1,"createFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("strikeOutAnnotCreateFromObj",{d:t.id}).then(function(t){return _(p.StrikeOutAnnot,t)})},p.StrikeOutAnnot.createFromAnnot=function(t){return P(arguments.length,1,"createFromAnnot","(PDFNet.Annot)",[[t,"Object",p.Annot,"Annot"]]),p.sendWithPromise("strikeOutAnnotCreateFromAnnot",{ann:t.id}).then(function(t){return _(p.StrikeOutAnnot,t)})},p.StrikeOutAnnot.create=function(t,e){return P(arguments.length,2,"create","(PDFNet.SDFDoc, PDFNet.Rect)",[[t,"SDFDoc"],[e,"Structure",p.Rect,"Rect"]]),b("create",[[e,1]]),p.sendWithPromise("strikeOutAnnotCreate",{doc:t.id,pos:e}).then(function(t){return _(p.StrikeOutAnnot,t)})},p.TextAnnot.createFromObj=function(t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,0,"createFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("textAnnotCreateFromObj",{d:t.id}).then(function(t){return _(p.TextAnnot,t)})},p.TextAnnot.createFromAnnot=function(t){return P(arguments.length,1,"createFromAnnot","(PDFNet.Annot)",[[t,"Object",p.Annot,"Annot"]]),p.sendWithPromise("textAnnotCreateFromAnnot",{ann:t.id}).then(function(t){return _(p.TextAnnot,t)})},p.TextAnnot.createAtPoint=function(t,e){return P(arguments.length,2,"createAtPoint","(PDFNet.SDFDoc, PDFNet.Point)",[[t,"SDFDoc"],[e,"Structure",p.Point,"Point"]]),b("createAtPoint",[[e,1]]),p.sendWithPromise("textAnnotCreateAtPoint",{doc:t.id,pos:e}).then(function(t){return _(p.TextAnnot,t)})},p.TextAnnot.create=function(t,e){return P(arguments.length,2,"create","(PDFNet.SDFDoc, PDFNet.Rect)",[[t,"SDFDoc"],[e,"Structure",p.Rect,"Rect"]]),b("create",[[e,1]]),p.sendWithPromise("textAnnotCreate",{doc:t.id,pos:e}).then(function(t){return _(p.TextAnnot,t)})},p.TextAnnot.prototype.isOpen=function(){return p.sendWithPromise("TextAnnot.isOpen",{text:this.id})},p.TextAnnot.prototype.setOpen=function(t){return P(arguments.length,1,"setOpen","(boolean)",[[t,"boolean"]]),p.sendWithPromise("TextAnnot.setOpen",{text:this.id,isopen:t})},p.TextAnnot.prototype.getIcon=function(){return p.sendWithPromise("TextAnnot.getIcon",{text:this.id})},p.TextAnnot.prototype.setIcon=function(t){return void 0===t&&(t=p.TextAnnot.Icon.e_Note),P(arguments.length,0,"setIcon","(number)",[[t,"number"]]),p.sendWithPromise("TextAnnot.setIcon",{text:this.id,icon:t})},p.TextAnnot.prototype.setIconDefault=function(){return p.sendWithPromise("TextAnnot.setIconDefault",{text:this.id})},p.TextAnnot.prototype.getIconName=function(){return p.sendWithPromise("TextAnnot.getIconName",{text:this.id})},p.TextAnnot.prototype.setIconName=function(t){return P(arguments.length,1,"setIconName","(string)",[[t,"string"]]),p.sendWithPromise("TextAnnot.setIconName",{text:this.id,icon:t})},p.TextAnnot.prototype.getState=function(){return p.sendWithPromise("TextAnnot.getState",{text:this.id})},p.TextAnnot.prototype.setState=function(t){return void 0===t&&(t=""),P(arguments.length,0,"setState","(string)",[[t,"string"]]),p.sendWithPromise("TextAnnot.setState",{text:this.id,state:t})},p.TextAnnot.prototype.getStateModel=function(){return p.sendWithPromise("TextAnnot.getStateModel",{text:this.id})},p.TextAnnot.prototype.setStateModel=function(t){return P(arguments.length,1,"setStateModel","(string)",[[t,"string"]]),p.sendWithPromise("TextAnnot.setStateModel",{text:this.id,sm:t})},p.TextAnnot.prototype.getAnchorPosition=function(e){return P(arguments.length,1,"getAnchorPosition","(PDFNet.Point)",[[e,"Structure",p.Point,"Point"]]),b("getAnchorPosition",[[e,0]]),e.yieldFunction="TextAnnot.getAnchorPosition",p.sendWithPromise("TextAnnot.getAnchorPosition",{text:this.id,anchor:e}).then(function(t){e.yieldFunction=void 0,W(t,e)})},p.TextAnnot.prototype.setAnchorPosition=function(t){return P(arguments.length,1,"setAnchorPosition","(PDFNet.Point)",[[t,"Structure",p.Point,"Point"]]),b("setAnchorPosition",[[t,0]]),p.sendWithPromise("TextAnnot.setAnchorPosition",{text:this.id,anchor:t})},p.UnderlineAnnot.createFromObj=function(t){return P(arguments.length,1,"createFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("underlineAnnotCreateFromObj",{d:t.id}).then(function(t){return _(p.UnderlineAnnot,t)})},p.UnderlineAnnot.createFromAnnot=function(t){return P(arguments.length,1,"createFromAnnot","(PDFNet.Annot)",[[t,"Object",p.Annot,"Annot"]]),p.sendWithPromise("underlineAnnotCreateFromAnnot",{ann:t.id}).then(function(t){return _(p.UnderlineAnnot,t)})},p.UnderlineAnnot.create=function(t,e){return P(arguments.length,2,"create","(PDFNet.SDFDoc, PDFNet.Rect)",[[t,"SDFDoc"],[e,"Structure",p.Rect,"Rect"]]),b("create",[[e,1]]),p.sendWithPromise("underlineAnnotCreate",{doc:t.id,pos:e}).then(function(t){return _(p.UnderlineAnnot,t)})},p.WatermarkAnnot.createFromObj=function(t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,0,"createFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("watermarkAnnotCreateFromObj",{d:t.id}).then(function(t){return _(p.WatermarkAnnot,t)})},p.WatermarkAnnot.createFromAnnot=function(t){return P(arguments.length,1,"createFromAnnot","(PDFNet.Annot)",[[t,"Object",p.Annot,"Annot"]]),p.sendWithPromise("watermarkAnnotCreateFromAnnot",{ann:t.id}).then(function(t){return _(p.WatermarkAnnot,t)})},p.WatermarkAnnot.create=function(t,e){return P(arguments.length,2,"create","(PDFNet.SDFDoc, PDFNet.Rect)",[[t,"SDFDoc"],[e,"Structure",p.Rect,"Rect"]]),b("create",[[e,1]]),p.sendWithPromise("watermarkAnnotCreate",{doc:t.id,pos:e}).then(function(t){return _(p.WatermarkAnnot,t)})},p.TextMarkupAnnot.createFromObj=function(t){return P(arguments.length,1,"createFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("textMarkupAnnotCreateFromObj",{d:t.id}).then(function(t){return _(p.TextMarkupAnnot,t)})},p.TextMarkupAnnot.createFromAnnot=function(t){return P(arguments.length,1,"createFromAnnot","(PDFNet.Annot)",[[t,"Object",p.Annot,"Annot"]]),p.sendWithPromise("textMarkupAnnotCreateFromAnnot",{ann:t.id}).then(function(t){return _(p.TextMarkupAnnot,t)})},p.TextMarkupAnnot.prototype.getQuadPointCount=function(){return p.sendWithPromise("TextMarkupAnnot.getQuadPointCount",{textmarkup:this.id})},p.TextMarkupAnnot.prototype.getQuadPoint=function(t){return P(arguments.length,1,"getQuadPoint","(number)",[[t,"number"]]),p.sendWithPromise("TextMarkupAnnot.getQuadPoint",{textmarkup:this.id,idx:t})},p.TextMarkupAnnot.prototype.setQuadPoint=function(t,e){return P(arguments.length,2,"setQuadPoint","(number, PDFNet.QuadPoint)",[[t,"number"],[e,"Structure",p.QuadPoint,"QuadPoint"]]),b("setQuadPoint",[[e,1]]),p.sendWithPromise("TextMarkupAnnot.setQuadPoint",{textmarkup:this.id,idx:t,qp:e})},p.WidgetAnnot.create=function(t,e,n){return P(arguments.length,3,"create","(PDFNet.SDFDoc, PDFNet.Rect, PDFNet.Field)",[[t,"SDFDoc"],[e,"Structure",p.Rect,"Rect"],[n,"Structure",p.Field,"Field"]]),b("create",[[e,1],[n,2]]),n.yieldFunction="WidgetAnnot.create",p.sendWithPromise("widgetAnnotCreate",{doc:t.id,pos:e,field:n}).then(function(t){return n.yieldFunction=void 0,t.result=_(p.WidgetAnnot,t.result),W(t.field,n),t.result})},p.WidgetAnnot.createFromObj=function(t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,0,"createFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("widgetAnnotCreateFromObj",{d:t.id}).then(function(t){return _(p.WidgetAnnot,t)})},p.WidgetAnnot.createFromAnnot=function(t){return P(arguments.length,1,"createFromAnnot","(PDFNet.Annot)",[[t,"Object",p.Annot,"Annot"]]),p.sendWithPromise("widgetAnnotCreateFromAnnot",{ann:t.id}).then(function(t){return _(p.WidgetAnnot,t)})},p.WidgetAnnot.prototype.getField=function(){return p.sendWithPromise("WidgetAnnot.getField",{widget:this.id}).then(function(t){return new p.Field(t)})},p.WidgetAnnot.prototype.getHighlightingMode=function(){return p.sendWithPromise("WidgetAnnot.getHighlightingMode",{widget:this.id})},p.WidgetAnnot.prototype.setHighlightingMode=function(t){return void 0===t&&(t=p.WidgetAnnot.HighlightingMode.e_invert),P(arguments.length,0,"setHighlightingMode","(number)",[[t,"number"]]),p.sendWithPromise("WidgetAnnot.setHighlightingMode",{widget:this.id,value:t})},p.WidgetAnnot.prototype.getAction=function(){return p.sendWithPromise("WidgetAnnot.getAction",{widget:this.id}).then(function(t){return _(p.Action,t)})},p.WidgetAnnot.prototype.setAction=function(t){return P(arguments.length,1,"setAction","(PDFNet.Action)",[[t,"Object",p.Action,"Action"]]),p.sendWithPromise("WidgetAnnot.setAction",{widget:this.id,action:t.id})},p.WidgetAnnot.prototype.getBorderColor=function(){return p.sendWithPromise("WidgetAnnot.getBorderColor",{widget:this.id}).then(function(t){return S(p.ColorPt,t)})},p.WidgetAnnot.prototype.setBorderColor=function(t,e){return P(arguments.length,2,"setBorderColor","(PDFNet.ColorPt, number)",[[t,"Object",p.ColorPt,"ColorPt"],[e,"number"]]),p.sendWithPromise("WidgetAnnot.setBorderColor",{widget:this.id,col:t.id,compnum:e})},p.WidgetAnnot.prototype.getBorderColorCompNum=function(){return p.sendWithPromise("WidgetAnnot.getBorderColorCompNum",{widget:this.id})},p.WidgetAnnot.prototype.getBackgroundColorCompNum=function(){return p.sendWithPromise("WidgetAnnot.getBackgroundColorCompNum",{widget:this.id})},p.WidgetAnnot.prototype.getBackgroundColor=function(){return p.sendWithPromise("WidgetAnnot.getBackgroundColor",{widget:this.id}).then(function(t){return S(p.ColorPt,t)})},p.WidgetAnnot.prototype.setBackgroundColor=function(t,e){return P(arguments.length,2,"setBackgroundColor","(PDFNet.ColorPt, number)",[[t,"Object",p.ColorPt,"ColorPt"],[e,"number"]]),p.sendWithPromise("WidgetAnnot.setBackgroundColor",{widget:this.id,col:t.id,compnum:e})},p.WidgetAnnot.prototype.getStaticCaptionText=function(){return p.sendWithPromise("WidgetAnnot.getStaticCaptionText",{widget:this.id})},p.WidgetAnnot.prototype.setStaticCaptionText=function(t){return P(arguments.length,1,"setStaticCaptionText","(string)",[[t,"string"]]),p.sendWithPromise("WidgetAnnot.setStaticCaptionText",{widget:this.id,contents:t})},p.WidgetAnnot.prototype.getRolloverCaptionText=function(){return p.sendWithPromise("WidgetAnnot.getRolloverCaptionText",{widget:this.id})},p.WidgetAnnot.prototype.setRolloverCaptionText=function(t){return P(arguments.length,1,"setRolloverCaptionText","(string)",[[t,"string"]]),p.sendWithPromise("WidgetAnnot.setRolloverCaptionText",{widget:this.id,contents:t})},p.WidgetAnnot.prototype.getMouseDownCaptionText=function(){return p.sendWithPromise("WidgetAnnot.getMouseDownCaptionText",{widget:this.id})},p.WidgetAnnot.prototype.setMouseDownCaptionText=function(t){return P(arguments.length,1,"setMouseDownCaptionText","(string)",[[t,"string"]]),p.sendWithPromise("WidgetAnnot.setMouseDownCaptionText",{widget:this.id,contents:t})},p.WidgetAnnot.prototype.getStaticIcon=function(){return p.sendWithPromise("WidgetAnnot.getStaticIcon",{widget:this.id}).then(function(t){return _(p.Obj,t)})},p.WidgetAnnot.prototype.setStaticIcon=function(t){return P(arguments.length,1,"setStaticIcon","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("WidgetAnnot.setStaticIcon",{widget:this.id,icon:t.id})},p.WidgetAnnot.prototype.getRolloverIcon=function(){return p.sendWithPromise("WidgetAnnot.getRolloverIcon",{widget:this.id}).then(function(t){return _(p.Obj,t)})},p.WidgetAnnot.prototype.setRolloverIcon=function(t){return P(arguments.length,1,"setRolloverIcon","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("WidgetAnnot.setRolloverIcon",{widget:this.id,icon:t.id})},p.WidgetAnnot.prototype.getMouseDownIcon=function(){return p.sendWithPromise("WidgetAnnot.getMouseDownIcon",{widget:this.id}).then(function(t){return _(p.Obj,t)})},p.WidgetAnnot.prototype.setMouseDownIcon=function(t){return P(arguments.length,1,"setMouseDownIcon","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("WidgetAnnot.setMouseDownIcon",{widget:this.id,icon:t.id})},p.WidgetAnnot.prototype.getScaleType=function(){return p.sendWithPromise("WidgetAnnot.getScaleType",{widget:this.id})},p.WidgetAnnot.prototype.setScaleType=function(t){return P(arguments.length,1,"setScaleType","(number)",[[t,"number"]]),p.sendWithPromise("WidgetAnnot.setScaleType",{widget:this.id,st:t})},p.WidgetAnnot.prototype.getIconCaptionRelation=function(){return p.sendWithPromise("WidgetAnnot.getIconCaptionRelation",{widget:this.id})},p.WidgetAnnot.prototype.setIconCaptionRelation=function(t){return P(arguments.length,1,"setIconCaptionRelation","(number)",[[t,"number"]]),p.sendWithPromise("WidgetAnnot.setIconCaptionRelation",{widget:this.id,icr:t})},p.WidgetAnnot.prototype.getScaleCondition=function(){return p.sendWithPromise("WidgetAnnot.getScaleCondition",{widget:this.id})},p.WidgetAnnot.prototype.setScaleCondition=function(t){return P(arguments.length,1,"setScaleCondition","(number)",[[t,"number"]]),p.sendWithPromise("WidgetAnnot.setScaleCondition",{widget:this.id,sd:t})},p.WidgetAnnot.prototype.getFitFull=function(){return p.sendWithPromise("WidgetAnnot.getFitFull",{widget:this.id})},p.WidgetAnnot.prototype.setFitFull=function(t){return P(arguments.length,1,"setFitFull","(boolean)",[[t,"boolean"]]),p.sendWithPromise("WidgetAnnot.setFitFull",{widget:this.id,ff:t})},p.WidgetAnnot.prototype.getHIconLeftOver=function(){return p.sendWithPromise("WidgetAnnot.getHIconLeftOver",{widget:this.id})},p.WidgetAnnot.prototype.setHIconLeftOver=function(t){return P(arguments.length,1,"setHIconLeftOver","(number)",[[t,"number"]]),p.sendWithPromise("WidgetAnnot.setHIconLeftOver",{widget:this.id,hl:t})},p.WidgetAnnot.prototype.getVIconLeftOver=function(){return p.sendWithPromise("WidgetAnnot.getVIconLeftOver",{widget:this.id})},p.WidgetAnnot.prototype.setVIconLeftOver=function(t){return P(arguments.length,1,"setVIconLeftOver","(number)",[[t,"number"]]),p.sendWithPromise("WidgetAnnot.setVIconLeftOver",{widget:this.id,vl:t})},p.WidgetAnnot.prototype.setFontSize=function(t){return P(arguments.length,1,"setFontSize","(number)",[[t,"number"]]),p.sendWithPromise("WidgetAnnot.setFontSize",{widget:this.id,font_size:t})},p.WidgetAnnot.prototype.setTextColor=function(t,e){return P(arguments.length,2,"setTextColor","(PDFNet.ColorPt, number)",[[t,"Object",p.ColorPt,"ColorPt"],[e,"number"]]),p.sendWithPromise("WidgetAnnot.setTextColor",{widget:this.id,color:t.id,col_comp:e})},p.WidgetAnnot.prototype.setFont=function(t){return P(arguments.length,1,"setFont","(PDFNet.Font)",[[t,"Object",p.Font,"Font"]]),p.sendWithPromise("WidgetAnnot.setFont",{widget:this.id,font:t.id})},p.WidgetAnnot.prototype.getFontSize=function(){return p.sendWithPromise("WidgetAnnot.getFontSize",{widget:this.id})},p.WidgetAnnot.prototype.getTextColor=function(){return p.sendWithPromise("WidgetAnnot.getTextColor",{widget:this.id}).then(function(t){return t.col=S(p.ColorPt,t.col),t})},p.WidgetAnnot.prototype.getFont=function(){return p.sendWithPromise("WidgetAnnot.getFont",{widget:this.id}).then(function(t){return S(p.Font,t)})},p.SignatureWidget.create=function(t,e,n){return void 0===n&&(n=""),P(arguments.length,2,"create","(PDFNet.PDFDoc, PDFNet.Rect, string)",[[t,"PDFDoc"],[e,"Structure",p.Rect,"Rect"],[n,"string"]]),b("create",[[e,1]]),p.sendWithPromise("signatureWidgetCreate",{doc:t.id,pos:e,field_name:n}).then(function(t){return _(p.SignatureWidget,t)})},p.SignatureWidget.createWithField=function(t,e,n){return P(arguments.length,3,"createWithField","(PDFNet.PDFDoc, PDFNet.Rect, PDFNet.Field)",[[t,"PDFDoc"],[e,"Structure",p.Rect,"Rect"],[n,"Structure",p.Field,"Field"]]),b("createWithField",[[e,1],[n,2]]),p.sendWithPromise("signatureWidgetCreateWithField",{doc:t.id,pos:e,field:n}).then(function(t){return _(p.SignatureWidget,t)})},p.SignatureWidget.createWithDigitalSignatureField=function(t,e,n){return P(arguments.length,3,"createWithDigitalSignatureField","(PDFNet.PDFDoc, PDFNet.Rect, PDFNet.DigitalSignatureField)",[[t,"PDFDoc"],[e,"Structure",p.Rect,"Rect"],[n,"Structure",p.DigitalSignatureField,"DigitalSignatureField"]]),b("createWithDigitalSignatureField",[[e,1],[n,2]]),p.sendWithPromise("signatureWidgetCreateWithDigitalSignatureField",{doc:t.id,pos:e,field:n}).then(function(t){return _(p.SignatureWidget,t)})},p.SignatureWidget.createFromObj=function(t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,0,"createFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("signatureWidgetCreateFromObj",{d:t.id}).then(function(t){return _(p.SignatureWidget,t)})},p.SignatureWidget.createFromAnnot=function(t){return P(arguments.length,1,"createFromAnnot","(PDFNet.Annot)",[[t,"Object",p.Annot,"Annot"]]),p.sendWithPromise("signatureWidgetCreateFromAnnot",{annot:t.id}).then(function(t){return _(p.SignatureWidget,t)})},p.SignatureWidget.prototype.createSignatureAppearance=function(t){return P(arguments.length,1,"createSignatureAppearance","(PDFNet.Image)",[[t,"Object",p.Image,"Image"]]),p.sendWithPromise("SignatureWidget.createSignatureAppearance",{self:this.id,img:t.id})},p.SignatureWidget.prototype.getDigitalSignatureField=function(){return p.sendWithPromise("SignatureWidget.getDigitalSignatureField",{self:this.id}).then(function(t){return new p.DigitalSignatureField(t)})},p.ComboBoxWidget.create=function(t,e,n){return void 0===n&&(n=""),P(arguments.length,2,"create","(PDFNet.PDFDoc, PDFNet.Rect, string)",[[t,"PDFDoc"],[e,"Structure",p.Rect,"Rect"],[n,"string"]]),b("create",[[e,1]]),p.sendWithPromise("comboBoxWidgetCreate",{doc:t.id,pos:e,field_name:n}).then(function(t){return _(p.ComboBoxWidget,t)})},p.ComboBoxWidget.createWithField=function(t,e,n){return P(arguments.length,3,"createWithField","(PDFNet.PDFDoc, PDFNet.Rect, PDFNet.Field)",[[t,"PDFDoc"],[e,"Structure",p.Rect,"Rect"],[n,"Structure",p.Field,"Field"]]),b("createWithField",[[e,1],[n,2]]),p.sendWithPromise("comboBoxWidgetCreateWithField",{doc:t.id,pos:e,field:n}).then(function(t){return _(p.ComboBoxWidget,t)})},p.ComboBoxWidget.createFromObj=function(t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,0,"createFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("comboBoxWidgetCreateFromObj",{d:t.id}).then(function(t){return _(p.ComboBoxWidget,t)})},p.ComboBoxWidget.createFromAnnot=function(t){return P(arguments.length,1,"createFromAnnot","(PDFNet.Annot)",[[t,"Object",p.Annot,"Annot"]]),p.sendWithPromise("comboBoxWidgetCreateFromAnnot",{annot:t.id}).then(function(t){return _(p.ComboBoxWidget,t)})},p.ComboBoxWidget.prototype.addOption=function(t){return P(arguments.length,1,"addOption","(string)",[[t,"string"]]),p.sendWithPromise("ComboBoxWidget.addOption",{combobox:this.id,value:t})},p.ComboBoxWidget.prototype.addOptions=function(t){return P(arguments.length,1,"addOptions","(Array<string>)",[[t,"Array"]]),p.sendWithPromise("ComboBoxWidget.addOptions",{combobox:this.id,opts_list:t})},p.ComboBoxWidget.prototype.setSelectedOption=function(t){return P(arguments.length,1,"setSelectedOption","(string)",[[t,"string"]]),p.sendWithPromise("ComboBoxWidget.setSelectedOption",{combobox:this.id,value:t})},p.ComboBoxWidget.prototype.getSelectedOption=function(){return p.sendWithPromise("ComboBoxWidget.getSelectedOption",{combobox:this.id})},p.ComboBoxWidget.prototype.replaceOptions=function(t){return P(arguments.length,1,"replaceOptions","(Array<string>)",[[t,"Array"]]),p.sendWithPromise("ComboBoxWidget.replaceOptions",{combobox:this.id,new_opts_list:t})},p.ComboBoxWidget.prototype.removeOption=function(t){return P(arguments.length,1,"removeOption","(string)",[[t,"string"]]),p.sendWithPromise("ComboBoxWidget.removeOption",{combobox:this.id,value:t})},p.ListBoxWidget.create=function(t,e,n){return void 0===n&&(n=""),P(arguments.length,2,"create","(PDFNet.PDFDoc, PDFNet.Rect, string)",[[t,"PDFDoc"],[e,"Structure",p.Rect,"Rect"],[n,"string"]]),b("create",[[e,1]]),p.sendWithPromise("listBoxWidgetCreate",{doc:t.id,pos:e,field_name:n}).then(function(t){return _(p.ListBoxWidget,t)})},p.ListBoxWidget.createWithField=function(t,e,n){return P(arguments.length,3,"createWithField","(PDFNet.PDFDoc, PDFNet.Rect, PDFNet.Field)",[[t,"PDFDoc"],[e,"Structure",p.Rect,"Rect"],[n,"Structure",p.Field,"Field"]]),b("createWithField",[[e,1],[n,2]]),p.sendWithPromise("listBoxWidgetCreateWithField",{doc:t.id,pos:e,field:n}).then(function(t){return _(p.ListBoxWidget,t)})},p.ListBoxWidget.createFromObj=function(t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,0,"createFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("listBoxWidgetCreateFromObj",{d:t.id}).then(function(t){return _(p.ListBoxWidget,t)})},p.ListBoxWidget.createFromAnnot=function(t){return P(arguments.length,1,"createFromAnnot","(PDFNet.Annot)",[[t,"Object",p.Annot,"Annot"]]),p.sendWithPromise("listBoxWidgetCreateFromAnnot",{annot:t.id}).then(function(t){return _(p.ListBoxWidget,t)})},p.ListBoxWidget.prototype.addOption=function(t){return P(arguments.length,1,"addOption","(string)",[[t,"string"]]),p.sendWithPromise("ListBoxWidget.addOption",{listbox:this.id,value:t})},p.ListBoxWidget.prototype.addOptions=function(t){return P(arguments.length,1,"addOptions","(Array<string>)",[[t,"Array"]]),p.sendWithPromise("ListBoxWidget.addOptions",{listbox:this.id,opts_list:t})},p.ListBoxWidget.prototype.setSelectedOptions=function(t){return P(arguments.length,1,"setSelectedOptions","(Array<string>)",[[t,"Array"]]),p.sendWithPromise("ListBoxWidget.setSelectedOptions",{listbox:this.id,selected_opts_list:t})},p.ListBoxWidget.prototype.replaceOptions=function(t){return P(arguments.length,1,"replaceOptions","(Array<string>)",[[t,"Array"]]),p.sendWithPromise("ListBoxWidget.replaceOptions",{listbox:this.id,new_opts_list:t})},p.ListBoxWidget.prototype.removeOption=function(t){return P(arguments.length,1,"removeOption","(string)",[[t,"string"]]),p.sendWithPromise("ListBoxWidget.removeOption",{listbox:this.id,value:t})},p.TextWidget.create=function(t,e,n){return void 0===n&&(n=""),P(arguments.length,2,"create","(PDFNet.PDFDoc, PDFNet.Rect, string)",[[t,"PDFDoc"],[e,"Structure",p.Rect,"Rect"],[n,"string"]]),b("create",[[e,1]]),p.sendWithPromise("textWidgetCreate",{doc:t.id,pos:e,field_name:n}).then(function(t){return _(p.TextWidget,t)})},p.TextWidget.createWithField=function(t,e,n){return P(arguments.length,3,"createWithField","(PDFNet.PDFDoc, PDFNet.Rect, PDFNet.Field)",[[t,"PDFDoc"],[e,"Structure",p.Rect,"Rect"],[n,"Structure",p.Field,"Field"]]),b("createWithField",[[e,1],[n,2]]),p.sendWithPromise("textWidgetCreateWithField",{doc:t.id,pos:e,field:n}).then(function(t){return _(p.TextWidget,t)})},p.TextWidget.createFromObj=function(t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,0,"createFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("textWidgetCreateFromObj",{d:t.id}).then(function(t){return _(p.TextWidget,t)})},p.TextWidget.createFromAnnot=function(t){return P(arguments.length,1,"createFromAnnot","(PDFNet.Annot)",[[t,"Object",p.Annot,"Annot"]]),p.sendWithPromise("textWidgetCreateFromAnnot",{annot:t.id}).then(function(t){return _(p.TextWidget,t)})},p.TextWidget.prototype.setText=function(t){return P(arguments.length,1,"setText","(string)",[[t,"string"]]),p.sendWithPromise("TextWidget.setText",{widget:this.id,text:t})},p.TextWidget.prototype.getText=function(){return p.sendWithPromise("TextWidget.getText",{widget:this.id})},p.CheckBoxWidget.create=function(t,e,n){return void 0===n&&(n=""),P(arguments.length,2,"create","(PDFNet.PDFDoc, PDFNet.Rect, string)",[[t,"PDFDoc"],[e,"Structure",p.Rect,"Rect"],[n,"string"]]),b("create",[[e,1]]),p.sendWithPromise("checkBoxWidgetCreate",{doc:t.id,pos:e,field_name:n}).then(function(t){return _(p.CheckBoxWidget,t)})},p.CheckBoxWidget.createWithField=function(t,e,n){return P(arguments.length,3,"createWithField","(PDFNet.PDFDoc, PDFNet.Rect, PDFNet.Field)",[[t,"PDFDoc"],[e,"Structure",p.Rect,"Rect"],[n,"Structure",p.Field,"Field"]]),b("createWithField",[[e,1],[n,2]]),p.sendWithPromise("checkBoxWidgetCreateWithField",{doc:t.id,pos:e,field:n}).then(function(t){return _(p.CheckBoxWidget,t)})},p.CheckBoxWidget.createFromObj=function(t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,0,"createFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("checkBoxWidgetCreateFromObj",{d:t.id}).then(function(t){return _(p.CheckBoxWidget,t)})},p.CheckBoxWidget.createFromAnnot=function(t){return P(arguments.length,1,"createFromAnnot","(PDFNet.Annot)",[[t,"Object",p.Annot,"Annot"]]),p.sendWithPromise("checkBoxWidgetCreateFromAnnot",{annot:t.id}).then(function(t){return _(p.CheckBoxWidget,t)})},p.CheckBoxWidget.prototype.isChecked=function(){return p.sendWithPromise("CheckBoxWidget.isChecked",{button:this.id})},p.CheckBoxWidget.prototype.setChecked=function(t){return P(arguments.length,1,"setChecked","(boolean)",[[t,"boolean"]]),p.sendWithPromise("CheckBoxWidget.setChecked",{button:this.id,checked:t})},p.RadioButtonWidget.createFromObj=function(t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,0,"createFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("radioButtonWidgetCreateFromObj",{d:t.id}).then(function(t){return _(p.RadioButtonWidget,t)})},p.RadioButtonWidget.createFromAnnot=function(t){return P(arguments.length,1,"createFromAnnot","(PDFNet.Annot)",[[t,"Object",p.Annot,"Annot"]]),p.sendWithPromise("radioButtonWidgetCreateFromAnnot",{annot:t.id}).then(function(t){return _(p.RadioButtonWidget,t)})},p.RadioButtonWidget.prototype.isEnabled=function(){return p.sendWithPromise("RadioButtonWidget.isEnabled",{button:this.id})},p.RadioButtonWidget.prototype.enableButton=function(){return p.sendWithPromise("RadioButtonWidget.enableButton",{button:this.id})},p.RadioButtonWidget.prototype.getGroup=function(){return p.sendWithPromise("RadioButtonWidget.getGroup",{button:this.id}).then(function(t){return S(p.RadioButtonGroup,t)})},p.PushButtonWidget.create=function(t,e,n){return void 0===n&&(n=""),P(arguments.length,2,"create","(PDFNet.PDFDoc, PDFNet.Rect, string)",[[t,"PDFDoc"],[e,"Structure",p.Rect,"Rect"],[n,"string"]]),b("create",[[e,1]]),p.sendWithPromise("pushButtonWidgetCreate",{doc:t.id,pos:e,field_name:n}).then(function(t){return _(p.PushButtonWidget,t)})},p.PushButtonWidget.createWithField=function(t,e,n){return P(arguments.length,3,"createWithField","(PDFNet.PDFDoc, PDFNet.Rect, PDFNet.Field)",[[t,"PDFDoc"],[e,"Structure",p.Rect,"Rect"],[n,"Structure",p.Field,"Field"]]),b("createWithField",[[e,1],[n,2]]),p.sendWithPromise("pushButtonWidgetCreateWithField",{doc:t.id,pos:e,field:n}).then(function(t){return _(p.PushButtonWidget,t)})},p.PushButtonWidget.createFromObj=function(t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,0,"createFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("pushButtonWidgetCreateFromObj",{obj:t.id}).then(function(t){return _(p.PushButtonWidget,t)})},p.PushButtonWidget.createFromAnnot=function(t){return P(arguments.length,1,"createFromAnnot","(PDFNet.Annot)",[[t,"Object",p.Annot,"Annot"]]),p.sendWithPromise("pushButtonWidgetCreateFromAnnot",{annot:t.id}).then(function(t){return _(p.PushButtonWidget,t)})},p.Bookmark.create=function(t,e){return P(arguments.length,2,"create","(PDFNet.PDFDoc, string)",[[t,"PDFDoc"],[e,"string"]]),p.sendWithPromise("bookmarkCreate",{in_doc:t.id,in_title:e}).then(function(t){return _(p.Bookmark,t)})},p.Bookmark.createFromObj=function(t){return P(arguments.length,1,"createFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("bookmarkCreateFromObj",{in_bookmark_dict:t.id}).then(function(t){return _(p.Bookmark,t)})},p.Bookmark.prototype.copy=function(){return p.sendWithPromise("Bookmark.copy",{in_bookmark:this.id}).then(function(t){return _(p.Bookmark,t)})},p.Bookmark.prototype.compare=function(t){return P(arguments.length,1,"compare","(PDFNet.Bookmark)",[[t,"Object",p.Bookmark,"Bookmark"]]),p.sendWithPromise("Bookmark.compare",{bm:this.id,in_bookmark:t.id})},p.Bookmark.prototype.isValid=function(){return p.sendWithPromise("Bookmark.isValid",{bm:this.id})},p.Bookmark.prototype.hasChildren=function(){return p.sendWithPromise("Bookmark.hasChildren",{bm:this.id})},p.Bookmark.prototype.getNext=function(){return p.sendWithPromise("Bookmark.getNext",{bm:this.id}).then(function(t){return _(p.Bookmark,t)})},p.Bookmark.prototype.getPrev=function(){return p.sendWithPromise("Bookmark.getPrev",{bm:this.id}).then(function(t){return _(p.Bookmark,t)})},p.Bookmark.prototype.getFirstChild=function(){return p.sendWithPromise("Bookmark.getFirstChild",{bm:this.id}).then(function(t){return _(p.Bookmark,t)})},p.Bookmark.prototype.getLastChild=function(){return p.sendWithPromise("Bookmark.getLastChild",{bm:this.id}).then(function(t){return _(p.Bookmark,t)})},p.Bookmark.prototype.getParent=function(){return p.sendWithPromise("Bookmark.getParent",{bm:this.id}).then(function(t){return _(p.Bookmark,t)})},p.Bookmark.prototype.find=function(t){return P(arguments.length,1,"find","(string)",[[t,"string"]]),p.sendWithPromise("Bookmark.find",{bm:this.id,in_title:t}).then(function(t){return _(p.Bookmark,t)})},p.Bookmark.prototype.addNewChild=function(t){return P(arguments.length,1,"addNewChild","(string)",[[t,"string"]]),p.sendWithPromise("Bookmark.addNewChild",{bm:this.id,in_title:t}).then(function(t){return _(p.Bookmark,t)})},p.Bookmark.prototype.addChild=function(t){return P(arguments.length,1,"addChild","(PDFNet.Bookmark)",[[t,"Object",p.Bookmark,"Bookmark"]]),p.sendWithPromise("Bookmark.addChild",{bm:this.id,in_bookmark:t.id})},p.Bookmark.prototype.addNewNext=function(t){return P(arguments.length,1,"addNewNext","(string)",[[t,"string"]]),p.sendWithPromise("Bookmark.addNewNext",{bm:this.id,in_title:t}).then(function(t){return _(p.Bookmark,t)})},p.Bookmark.prototype.addNext=function(t){return P(arguments.length,1,"addNext","(PDFNet.Bookmark)",[[t,"Object",p.Bookmark,"Bookmark"]]),p.sendWithPromise("Bookmark.addNext",{bm:this.id,in_bookmark:t.id})},p.Bookmark.prototype.addNewPrev=function(t){return P(arguments.length,1,"addNewPrev","(string)",[[t,"string"]]),p.sendWithPromise("Bookmark.addNewPrev",{bm:this.id,in_title:t}).then(function(t){return _(p.Bookmark,t)})},p.Bookmark.prototype.addPrev=function(t){return P(arguments.length,1,"addPrev","(PDFNet.Bookmark)",[[t,"Object",p.Bookmark,"Bookmark"]]),p.sendWithPromise("Bookmark.addPrev",{bm:this.id,in_bookmark:t.id})},p.Bookmark.prototype.delete=function(){return p.sendWithPromise("Bookmark.delete",{bm:this.id})},p.Bookmark.prototype.unlink=function(){return p.sendWithPromise("Bookmark.unlink",{bm:this.id})},p.Bookmark.prototype.getIndent=function(){return p.sendWithPromise("Bookmark.getIndent",{bm:this.id})},p.Bookmark.prototype.isOpen=function(){return p.sendWithPromise("Bookmark.isOpen",{bm:this.id})},p.Bookmark.prototype.setOpen=function(t){return P(arguments.length,1,"setOpen","(boolean)",[[t,"boolean"]]),p.sendWithPromise("Bookmark.setOpen",{bm:this.id,in_open:t})},p.Bookmark.prototype.getOpenCount=function(){return p.sendWithPromise("Bookmark.getOpenCount",{bm:this.id})},p.Bookmark.prototype.getTitle=function(){return p.sendWithPromise("Bookmark.getTitle",{bm:this.id})},p.Bookmark.prototype.getTitleObj=function(){return p.sendWithPromise("Bookmark.getTitleObj",{bm:this.id}).then(function(t){return _(p.Obj,t)})},p.Bookmark.prototype.setTitle=function(t){return P(arguments.length,1,"setTitle","(string)",[[t,"string"]]),p.sendWithPromise("Bookmark.setTitle",{bm:this.id,title:t})},p.Bookmark.prototype.getAction=function(){return p.sendWithPromise("Bookmark.getAction",{bm:this.id}).then(function(t){return _(p.Action,t)})},p.Bookmark.prototype.setAction=function(t){return P(arguments.length,1,"setAction","(PDFNet.Action)",[[t,"Object",p.Action,"Action"]]),p.sendWithPromise("Bookmark.setAction",{bm:this.id,in_action:t.id})},p.Bookmark.prototype.removeAction=function(){return p.sendWithPromise("Bookmark.removeAction",{bm:this.id})},p.Bookmark.prototype.getFlags=function(){return p.sendWithPromise("Bookmark.getFlags",{bm:this.id})},p.Bookmark.prototype.setFlags=function(t){return P(arguments.length,1,"setFlags","(number)",[[t,"number"]]),p.sendWithPromise("Bookmark.setFlags",{bm:this.id,in_flags:t})},p.Bookmark.prototype.getColor=function(){return p.sendWithPromise("Bookmark.getColor",{bm:this.id})},p.Bookmark.prototype.setColor=function(t,e,n){return void 0===t&&(t=0),void 0===e&&(e=0),void 0===n&&(n=0),P(arguments.length,0,"setColor","(number, number, number)",[[t,"number"],[e,"number"],[n,"number"]]),p.sendWithPromise("Bookmark.setColor",{bm:this.id,in_r:t,in_g:e,in_b:n})},p.Bookmark.prototype.getSDFObj=function(){return p.sendWithPromise("Bookmark.getSDFObj",{bm:this.id}).then(function(t){return _(p.Obj,t)})},p.ColorPt.init=function(t,e,n,i){return void 0===t&&(t=0),void 0===e&&(e=0),void 0===n&&(n=0),void 0===i&&(i=0),P(arguments.length,0,"init","(number, number, number, number)",[[t,"number"],[e,"number"],[n,"number"],[i,"number"]]),p.sendWithPromise("colorPtInit",{x:t,y:e,z:n,w:i}).then(function(t){return S(p.ColorPt,t)})},p.ColorPt.prototype.compare=function(t){return P(arguments.length,1,"compare","(PDFNet.ColorPt)",[[t,"Object",p.ColorPt,"ColorPt"]]),p.sendWithPromise("ColorPt.compare",{left:this.id,right:t.id})},p.ColorPt.prototype.set=function(t,e,n,i){return void 0===t&&(t=0),void 0===e&&(e=0),void 0===n&&(n=0),void 0===i&&(i=0),P(arguments.length,0,"set","(number, number, number, number)",[[t,"number"],[e,"number"],[n,"number"],[i,"number"]]),p.sendWithPromise("ColorPt.set",{cp:this.id,x:t,y:e,z:n,w:i})},p.ColorPt.prototype.setByIndex=function(t,e){return P(arguments.length,2,"setByIndex","(number, number)",[[t,"number"],[e,"number"]]),p.sendWithPromise("ColorPt.setByIndex",{cp:this.id,colorant_index:t,colorant_value:e})},p.ColorPt.prototype.get=function(t){return P(arguments.length,1,"get","(number)",[[t,"number"]]),p.sendWithPromise("ColorPt.get",{cp:this.id,colorant_index:t})},p.ColorPt.prototype.setColorantNum=function(t){return P(arguments.length,1,"setColorantNum","(number)",[[t,"number"]]),p.sendWithPromise("ColorPt.setColorantNum",{cp:this.id,num:t})},p.ColorSpace.createDeviceGray=function(){return p.sendWithPromise("colorSpaceCreateDeviceGray",{}).then(function(t){return S(p.ColorSpace,t)})},p.ColorSpace.createDeviceRGB=function(){return p.sendWithPromise("colorSpaceCreateDeviceRGB",{}).then(function(t){return S(p.ColorSpace,t)})},p.ColorSpace.createDeviceCMYK=function(){return p.sendWithPromise("colorSpaceCreateDeviceCMYK",{}).then(function(t){return S(p.ColorSpace,t)})},p.ColorSpace.createPattern=function(){return p.sendWithPromise("colorSpaceCreatePattern",{}).then(function(t){return S(p.ColorSpace,t)})},p.ColorSpace.create=function(t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,0,"create","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("colorSpaceCreate",{color_space:t.id}).then(function(t){return S(p.ColorSpace,t)})},p.ColorSpace.createICCFromFilter=function(t,e){return P(arguments.length,2,"createICCFromFilter","(PDFNet.SDFDoc, PDFNet.Filter)",[[t,"SDFDoc"],[e,"Object",p.Filter,"Filter"]]),0!=e.id&&A(e.id),p.sendWithPromise("colorSpaceCreateICCFromFilter",{doc:t.id,no_own_filter:e.id}).then(function(t){return S(p.ColorSpace,t)})},p.ColorSpace.createICCFromBuffer=function(t,e){P(arguments.length,2,"createICCFromBuffer","(PDFNet.SDFDoc, ArrayBuffer|TypedArray)",[[t,"SDFDoc"],[e,"ArrayBuffer"]]);var n=y(e,!1);return p.sendWithPromise("colorSpaceCreateICCFromBuffer",{doc:t.id,buf:n}).then(function(t){return S(p.ColorSpace,t)})},p.ColorSpace.getComponentNumFromObj=function(t,e){return P(arguments.length,2,"getComponentNumFromObj","(number, PDFNet.Obj)",[[t,"number"],[e,"Object",p.Obj,"Obj"]]),p.sendWithPromise("colorSpaceGetComponentNumFromObj",{cs_type:t,cs_obj:e.id})},p.ColorSpace.getTypeFromObj=function(t){return P(arguments.length,1,"getTypeFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("colorSpaceGetTypeFromObj",{cs_obj:t.id})},p.ColorSpace.prototype.getType=function(){return p.sendWithPromise("ColorSpace.getType",{cs:this.id})},p.ColorSpace.prototype.getSDFObj=function(){return p.sendWithPromise("ColorSpace.getSDFObj",{cs:this.id}).then(function(t){return _(p.Obj,t)})},p.ColorSpace.prototype.getComponentNum=function(){return p.sendWithPromise("ColorSpace.getComponentNum",{cs:this.id})},p.ColorSpace.prototype.initColor=function(){return p.sendWithPromise("ColorSpace.initColor",{cs:this.id}).then(function(t){return S(p.ColorPt,t)})},p.ColorSpace.prototype.initComponentRanges=function(t){return P(arguments.length,1,"initComponentRanges","(number)",[[t,"number"]]),p.sendWithPromise("ColorSpace.initComponentRanges",{cs:this.id,num_comps:t})},p.ColorSpace.prototype.convert2Gray=function(t){return P(arguments.length,1,"convert2Gray","(PDFNet.ColorPt)",[[t,"Object",p.ColorPt,"ColorPt"]]),p.sendWithPromise("ColorSpace.convert2Gray",{cs:this.id,in_color:t.id}).then(function(t){return S(p.ColorPt,t)})},p.ColorSpace.prototype.convert2RGB=function(t){return P(arguments.length,1,"convert2RGB","(PDFNet.ColorPt)",[[t,"Object",p.ColorPt,"ColorPt"]]),p.sendWithPromise("ColorSpace.convert2RGB",{cs:this.id,in_color:t.id}).then(function(t){return S(p.ColorPt,t)})},p.ColorSpace.prototype.convert2CMYK=function(t){return P(arguments.length,1,"convert2CMYK","(PDFNet.ColorPt)",[[t,"Object",p.ColorPt,"ColorPt"]]),p.sendWithPromise("ColorSpace.convert2CMYK",{cs:this.id,in_color:t.id}).then(function(t){return S(p.ColorPt,t)})},p.ColorSpace.prototype.getAlternateColorSpace=function(){return p.sendWithPromise("ColorSpace.getAlternateColorSpace",{cs:this.id}).then(function(t){return S(p.ColorSpace,t)})},p.ColorSpace.prototype.getBaseColorSpace=function(){return p.sendWithPromise("ColorSpace.getBaseColorSpace",{cs:this.id}).then(function(t){return S(p.ColorSpace,t)})},p.ColorSpace.prototype.getHighVal=function(){return p.sendWithPromise("ColorSpace.getHighVal",{cs:this.id})},p.ColorSpace.prototype.getLookupTable=function(){return p.sendWithPromise("ColorSpace.getLookupTable",{cs:this.id})},p.ColorSpace.prototype.getBaseColor=function(t){return P(arguments.length,1,"getBaseColor","(number)",[[t,"number"]]),p.sendWithPromise("ColorSpace.getBaseColor",{cs:this.id,color_idx:t}).then(function(t){return S(p.ColorPt,t)})},p.ColorSpace.prototype.getTintFunction=function(){return p.sendWithPromise("ColorSpace.getTintFunction",{cs:this.id}).then(function(t){return S(p.Function,t)})},p.ColorSpace.prototype.isAll=function(){return p.sendWithPromise("ColorSpace.isAll",{cs:this.id})},p.ColorSpace.prototype.isNone=function(){return p.sendWithPromise("ColorSpace.isNone",{cs:this.id})},p.ContentReplacer.create=function(){return p.sendWithPromise("contentReplacerCreate",{}).then(function(t){return S(p.ContentReplacer,t)})},p.ContentReplacer.prototype.addImage=function(t,e){return P(arguments.length,2,"addImage","(PDFNet.Rect, PDFNet.Obj)",[[t,"Structure",p.Rect,"Rect"],[e,"Object",p.Obj,"Obj"]]),b("addImage",[[t,0]]),p.sendWithPromise("ContentReplacer.addImage",{cr:this.id,target_region:t,replacement_image:e.id})},p.ContentReplacer.prototype.addText=function(t,e){return P(arguments.length,2,"addText","(PDFNet.Rect, string)",[[t,"Structure",p.Rect,"Rect"],[e,"string"]]),b("addText",[[t,0]]),p.sendWithPromise("ContentReplacer.addText",{cr:this.id,target_region:t,replacement_text:e})},p.ContentReplacer.prototype.addString=function(t,e){return P(arguments.length,2,"addString","(string, string)",[[t,"string"],[e,"string"]]),p.sendWithPromise("ContentReplacer.addString",{cr:this.id,template_text:t,replacement_text:e})},p.ContentReplacer.prototype.setMatchStrings=function(t,e){return P(arguments.length,2,"setMatchStrings","(string, string)",[[t,"string"],[e,"string"]]),p.sendWithPromise("ContentReplacer.setMatchStrings",{cr:this.id,start_str:t,end_str:e})},p.ContentReplacer.prototype.process=function(t){return P(arguments.length,1,"process","(PDFNet.Page)",[[t,"Object",p.Page,"Page"]]),p.sendWithPromise("ContentReplacer.process",{cr:this.id,page:t.id})},p.Reflow.prototype.getHtml=function(){return p.sendWithPromise("Reflow.getHtml",{self:this.id})},p.Reflow.prototype.getAnnot=function(t){return P(arguments.length,1,"getAnnot","(string)",[[t,"string"]]),p.sendWithPromise("Reflow.getAnnot",{self:this.id,in_id:t})},p.Reflow.prototype.setAnnot=function(t){return P(arguments.length,1,"setAnnot","(string)",[[t,"string"]]),p.sendWithPromise("Reflow.setAnnot",{self:this.id,in_json:t})},p.Reflow.prototype.setIncludeImages=function(t){return P(arguments.length,1,"setIncludeImages","(boolean)",[[t,"boolean"]]),p.sendWithPromise("Reflow.setIncludeImages",{self:this.id,include:t})},p.Reflow.prototype.setHTMLOutputTextMarkup=function(t){return P(arguments.length,1,"setHTMLOutputTextMarkup","(boolean)",[[t,"boolean"]]),p.sendWithPromise("Reflow.setHTMLOutputTextMarkup",{self:this.id,include:t})},p.Reflow.prototype.setMessageWhenNoReflowContent=function(t){return P(arguments.length,1,"setMessageWhenNoReflowContent","(string)",[[t,"string"]]),p.sendWithPromise("Reflow.setMessageWhenNoReflowContent",{self:this.id,content:t})},p.Reflow.prototype.setMessageWhenReflowFailed=function(t){return P(arguments.length,1,"setMessageWhenReflowFailed","(string)",[[t,"string"]]),p.sendWithPromise("Reflow.setMessageWhenReflowFailed",{self:this.id,content:t})},p.Reflow.prototype.setHideBackgroundImages=function(t){return P(arguments.length,1,"setHideBackgroundImages","(boolean)",[[t,"boolean"]]),p.sendWithPromise("Reflow.setHideBackgroundImages",{self:this.id,hide_background_images:t})},p.Reflow.prototype.setHideImagesUnderText=function(t){return P(arguments.length,1,"setHideImagesUnderText","(boolean)",[[t,"boolean"]]),p.sendWithPromise("Reflow.setHideImagesUnderText",{self:this.id,hide_images_under_text:t})},p.Reflow.prototype.setHideImagesUnderInvisibleText=function(t){return P(arguments.length,1,"setHideImagesUnderInvisibleText","(boolean)",[[t,"boolean"]]),p.sendWithPromise("Reflow.setHideImagesUnderInvisibleText",{self:this.id,hide_images_under_invisible_text:t})},p.Reflow.prototype.setDoNotReflowTextOverImages=function(t){return P(arguments.length,1,"setDoNotReflowTextOverImages","(boolean)",[[t,"boolean"]]),p.sendWithPromise("Reflow.setDoNotReflowTextOverImages",{self:this.id,do_not_reflow_text_over_images:t})},p.Reflow.prototype.setFontOverrideName=function(t){return P(arguments.length,1,"setFontOverrideName","(string)",[[t,"string"]]),p.sendWithPromise("Reflow.setFontOverrideName",{self:this.id,font_family:t})},p.Reflow.prototype.setCustomStyles=function(t){return P(arguments.length,1,"setCustomStyles","(string)",[[t,"string"]]),p.sendWithPromise("Reflow.setCustomStyles",{self:this.id,styles:t})},p.Reflow.prototype.setIncludeBBoxForRecognizedZones=function(t){return P(arguments.length,1,"setIncludeBBoxForRecognizedZones","(boolean)",[[t,"boolean"]]),p.sendWithPromise("Reflow.setIncludeBBoxForRecognizedZones",{self:this.id,include:t})},p.Convert.fromXpsMem=function(t,e){P(arguments.length,2,"fromXpsMem","(PDFNet.PDFDoc, ArrayBuffer|TypedArray)",[[t,"PDFDoc"],[e,"ArrayBuffer"]]);var n=y(e,!1);return p.sendWithPromise("convertFromXpsMem",{in_pdfdoc:t.id,buf:n})},p.Convert.createReflow=function(t,e){return P(arguments.length,2,"createReflow","(PDFNet.Page, string)",[[t,"Object",p.Page,"Page"],[e,"string"]]),p.sendWithPromise("convertCreateReflow",{in_page:t.id,json_zones:e}).then(function(t){return S(p.Reflow,t)})},p.Convert.fromTextWithBuffer=function(t,e,n){return void 0===n&&(n=new p.Obj("0")),P(arguments.length,2,"fromTextWithBuffer","(PDFNet.PDFDoc, ArrayBuffer|TypedArray, PDFNet.Obj)",[[t,"PDFDoc"],[e,"ArrayBuffer"],[n,"Object",p.Obj,"Obj"]]),e=y(e,!1),p.sendWithPromise("convertFromTextWithBuffer",{in_pdfdoc:t.id,in_filename:e,options:n.id})},p.Convert.toXpsBuffer=function(e,t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,1,"toXpsBuffer","(PDFNet.PDFDoc, PDFNet.Obj)",[[e,"PDFDoc"],[t,"OptionObject",p.Obj,"Obj","PDFNet.Convert.XPSOutputOptions"]]),(t=D(t,"PDFNet.Convert.XPSOutputOptions")).then(function(t){return p.sendWithPromise("convertToXpsBuffer",{in_pdfdoc:e.id,options:t.id}).then(function(t){return new Uint8Array(t)})})},p.Convert.fileToXpsWithBuffer=function(e,n,t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,2,"fileToXpsWithBuffer","(ArrayBuffer|TypedArray, string, PDFNet.Obj)",[[e,"ArrayBuffer"],[n,"string"],[t,"OptionObject",p.Obj,"Obj","PDFNet.Convert.XPSOutputOptions"]]),n.startsWith(".")||(n="."+n),e=y(e,!1),(t=D(t,"PDFNet.Convert.XPSOutputOptions")).then(function(t){return p.sendWithPromise("convertFileToXpsWithBuffer",{in_inputFilename:e,in_inputFilename_extension:n,options:t.id}).then(function(t){return new Uint8Array(t)})})},p.Convert.fileToXodWithBuffer=function(e,n,t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,2,"fileToXodWithBuffer","(ArrayBuffer|TypedArray, string, PDFNet.Obj)",[[e,"ArrayBuffer"],[n,"string"],[t,"OptionObject",p.Obj,"Obj","PDFNet.Convert.XODOutputOptions"]]),n.startsWith(".")||(n="."+n),e=y(e,!1),(t=D(t,"PDFNet.Convert.XODOutputOptions")).then(function(t){return p.sendWithPromise("convertFileToXodWithBuffer",{in_filename:e,in_filename_extension:n,options:t.id}).then(function(t){return new Uint8Array(t)})})},p.Convert.toXodBuffer=function(e,t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,1,"toXodBuffer","(PDFNet.PDFDoc, PDFNet.Obj)",[[e,"PDFDoc"],[t,"OptionObject",p.Obj,"Obj","PDFNet.Convert.XODOutputOptions"]]),(t=D(t,"PDFNet.Convert.XODOutputOptions")).then(function(t){return p.sendWithPromise("convertToXodBuffer",{in_pdfdoc:e.id,options:t.id}).then(function(t){return new Uint8Array(t)})})},p.ConversionMonitor.prototype.next=function(){return p.sendWithPromise("ConversionMonitor.next",{conversionMonitor:this.id})},p.ConversionMonitor.prototype.ready=function(){return p.sendWithPromise("ConversionMonitor.ready",{conversionMonitor:this.id})},p.ConversionMonitor.prototype.progress=function(){return p.sendWithPromise("ConversionMonitor.progress",{conversionMonitor:this.id})},p.ConversionMonitor.prototype.filter=function(){return p.sendWithPromise("ConversionMonitor.filter",{conversionMonitor:this.id}).then(function(t){return S(p.Filter,t)})},p.Convert.officeToPdfWithFilter=function(e,n,t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,2,"officeToPdfWithFilter","(PDFNet.PDFDoc, PDFNet.Filter, PDFNet.Obj)",[[e,"PDFDoc"],[n,"Object",p.Filter,"Filter"],[t,"OptionObject",p.Obj,"Obj","PDFNet.Convert.ConversionOptions"]]),0!=n.id&&A(n.id),(t=D(t,"PDFNet.Convert.ConversionOptions")).then(function(t){return p.sendWithPromise("convertOfficeToPdfWithFilter",{in_pdfdoc:e.id,no_own_in_stream:n.id,options:t.id})})},p.Convert.toPdfWithBuffer=function(t,e,n){return P(arguments.length,3,"toPdfWithBuffer","(PDFNet.PDFDoc, ArrayBuffer|TypedArray, string)",[[t,"PDFDoc"],[e,"ArrayBuffer"],[n,"string"]]),n.startsWith(".")||(n="."+n),e=y(e,!1),p.sendWithPromise("convertToPdfWithBuffer",{in_pdfdoc:t.id,in_filename:e,in_filename_extension:n})},p.Convert.fromTiff=function(t,e){return P(arguments.length,2,"fromTiff","(PDFNet.PDFDoc, PDFNet.Filter)",[[t,"PDFDoc"],[e,"Object",p.Filter,"Filter"]]),p.sendWithPromise("convertFromTiff",{in_pdfdoc:t.id,in_data:e.id})},p.Convert.pageToHtml=function(t){return P(arguments.length,1,"pageToHtml","(PDFNet.Page)",[[t,"Object",p.Page,"Page"]]),p.sendWithPromise("convertPageToHtml",{page:t.id})},p.Convert.pageToHtmlZoned=function(t,e){return P(arguments.length,2,"pageToHtmlZoned","(PDFNet.Page, string)",[[t,"Object",p.Page,"Page"],[e,"string"]]),p.sendWithPromise("convertPageToHtmlZoned",{page:t.id,json_zones:e})},p.Convert.fileToTiffWithBuffer=function(e,n,t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,2,"fileToTiffWithBuffer","(ArrayBuffer|TypedArray, string, PDFNet.Obj)",[[e,"ArrayBuffer"],[n,"string"],[t,"OptionObject",p.Obj,"Obj","PDFNet.Convert.TiffOutputOptions"]]),n.startsWith(".")||(n="."+n),e=y(e,!1),(t=D(t,"PDFNet.Convert.TiffOutputOptions")).then(function(t){return p.sendWithPromise("convertFileToTiffWithBuffer",{in_filename:e,in_filename_extension:n,options:t.id}).then(function(t){return new Uint8Array(t)})})},p.Convert.toTiffBuffer=function(e,t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,1,"toTiffBuffer","(PDFNet.PDFDoc, PDFNet.Obj)",[[e,"PDFDoc"],[t,"OptionObject",p.Obj,"Obj","PDFNet.Convert.TiffOutputOptions"]]),(t=D(t,"PDFNet.Convert.TiffOutputOptions")).then(function(t){return p.sendWithPromise("convertToTiffBuffer",{in_pdfdoc:e.id,options:t.id}).then(function(t){return new Uint8Array(t)})})},p.Convert.fileToTiffWithFilterWithBuffer=function(e,t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,1,"fileToTiffWithFilterWithBuffer","(ArrayBuffer|TypedArray, PDFNet.Obj)",[[e,"ArrayBuffer"],[t,"OptionObject",p.Obj,"Obj","PDFNet.Convert.TiffOutputOptions"]]),e=y(e,!1),(t=D(t,"PDFNet.Convert.TiffOutputOptions")).then(function(t){return p.sendWithPromise("convertFileToTiffWithFilterWithBuffer",{in_filename:e,options:t.id}).then(function(t){return S(p.Filter,t)})})},p.Convert.toTiffWithFilter=function(e,t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,1,"toTiffWithFilter","(PDFNet.PDFDoc, PDFNet.Obj)",[[e,"PDFDoc"],[t,"OptionObject",p.Obj,"Obj","PDFNet.Convert.TiffOutputOptions"]]),(t=D(t,"PDFNet.Convert.TiffOutputOptions")).then(function(t){return p.sendWithPromise("convertToTiffWithFilter",{in_pdfdoc:e.id,options:t.id}).then(function(t){return S(p.Filter,t)})})},p.Date.init=function(t,e,n,i,r,o){return P(arguments.length,6,"init","(number, number, number, number, number, number)",[[t,"number"],[e,"number"],[n,"number"],[i,"number"],[r,"number"],[o,"number"]]),p.sendWithPromise("dateInit",{year:t,month:e,day:n,hour:i,minute:r,second:o}).then(function(t){return new p.Date(t)})},p.Date.prototype.isValid=function(){return F("isValid",this.yieldFunction),p.sendWithPromise("Date.isValid",{date:this})},p.Date.prototype.attach=function(t){P(arguments.length,1,"attach","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),F("attach",this.yieldFunction);var e=this;return this.yieldFunction="Date.attach",p.sendWithPromise("Date.attach",{date:this,d:t.id}).then(function(t){e.yieldFunction=void 0,W(t,e)})},p.Date.prototype.update=function(t){void 0===t&&(t=new p.Obj("0")),P(arguments.length,0,"update","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),F("update",this.yieldFunction);var e=this;return this.yieldFunction="Date.update",p.sendWithPromise("Date.update",{date:this,d:t.id}).then(function(t){return e.yieldFunction=void 0,W(t.date,e),t.result})},p.Date.prototype.setCurrentTime=function(){F("setCurrentTime",this.yieldFunction);var e=this;return this.yieldFunction="Date.setCurrentTime",p.sendWithPromise("Date.setCurrentTime",{date:this}).then(function(t){e.yieldFunction=void 0,W(t,e)})},p.Date.prototype.setUT=function(t){P(arguments.length,1,"setUT","(number)",[[t,"number"]]),F("setUT",this.yieldFunction);var e=this;return this.yieldFunction="Date.setUT",p.sendWithPromise("Date.setUT",{date:this,ut:t}).then(function(t){e.yieldFunction=void 0,W(t,e)})},p.Date.prototype.setUTHour=function(t){P(arguments.length,1,"setUTHour","(number)",[[t,"number"]]),F("setUTHour",this.yieldFunction);var e=this;return this.yieldFunction="Date.setUTHour",p.sendWithPromise("Date.setUTHour",{date:this,ut_hour:t}).then(function(t){e.yieldFunction=void 0,W(t,e)})},p.Date.prototype.setUTMinutes=function(t){P(arguments.length,1,"setUTMinutes","(number)",[[t,"number"]]),F("setUTMinutes",this.yieldFunction);var e=this;return this.yieldFunction="Date.setUTMinutes",p.sendWithPromise("Date.setUTMinutes",{date:this,ut_minutes:t}).then(function(t){e.yieldFunction=void 0,W(t,e)})},p.Destination.createXYZ=function(t,e,n,i){return P(arguments.length,4,"createXYZ","(PDFNet.Page, number, number, number)",[[t,"Object",p.Page,"Page"],[e,"number"],[n,"number"],[i,"number"]]),p.sendWithPromise("destinationCreateXYZ",{page:t.id,left:e,top:n,zoom:i}).then(function(t){return _(p.Destination,t)})},p.Destination.createFit=function(t){return P(arguments.length,1,"createFit","(PDFNet.Page)",[[t,"Object",p.Page,"Page"]]),p.sendWithPromise("destinationCreateFit",{page:t.id}).then(function(t){return _(p.Destination,t)})},p.Destination.createFitH=function(t,e){return P(arguments.length,2,"createFitH","(PDFNet.Page, number)",[[t,"Object",p.Page,"Page"],[e,"number"]]),p.sendWithPromise("destinationCreateFitH",{page:t.id,top:e}).then(function(t){return _(p.Destination,t)})},p.Destination.createFitV=function(t,e){return P(arguments.length,2,"createFitV","(PDFNet.Page, number)",[[t,"Object",p.Page,"Page"],[e,"number"]]),p.sendWithPromise("destinationCreateFitV",{page:t.id,left:e}).then(function(t){return _(p.Destination,t)})},p.Destination.createFitR=function(t,e,n,i,r){return P(arguments.length,5,"createFitR","(PDFNet.Page, number, number, number, number)",[[t,"Object",p.Page,"Page"],[e,"number"],[n,"number"],[i,"number"],[r,"number"]]),p.sendWithPromise("destinationCreateFitR",{page:t.id,left:e,bottom:n,right:i,top:r}).then(function(t){return _(p.Destination,t)})},p.Destination.createFitB=function(t){return P(arguments.length,1,"createFitB","(PDFNet.Page)",[[t,"Object",p.Page,"Page"]]),p.sendWithPromise("destinationCreateFitB",{page:t.id}).then(function(t){return _(p.Destination,t)})},p.Destination.createFitBH=function(t,e){return P(arguments.length,2,"createFitBH","(PDFNet.Page, number)",[[t,"Object",p.Page,"Page"],[e,"number"]]),p.sendWithPromise("destinationCreateFitBH",{page:t.id,top:e}).then(function(t){return _(p.Destination,t)})},p.Destination.createFitBV=function(t,e){return P(arguments.length,2,"createFitBV","(PDFNet.Page, number)",[[t,"Object",p.Page,"Page"],[e,"number"]]),p.sendWithPromise("destinationCreateFitBV",{page:t.id,left:e}).then(function(t){return _(p.Destination,t)})},p.Destination.create=function(t){return P(arguments.length,1,"create","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("destinationCreate",{dest:t.id}).then(function(t){return _(p.Destination,t)})},p.Destination.prototype.copy=function(){return p.sendWithPromise("Destination.copy",{d:this.id}).then(function(t){return _(p.Destination,t)})},p.Destination.prototype.isValid=function(){return p.sendWithPromise("Destination.isValid",{dest:this.id})},p.Destination.prototype.getFitType=function(){return p.sendWithPromise("Destination.getFitType",{dest:this.id})},p.Destination.prototype.getPage=function(){return p.sendWithPromise("Destination.getPage",{dest:this.id}).then(function(t){return _(p.Page,t)})},p.Destination.prototype.setPage=function(t){return P(arguments.length,1,"setPage","(PDFNet.Page)",[[t,"Object",p.Page,"Page"]]),p.sendWithPromise("Destination.setPage",{dest:this.id,page:t.id})},p.Destination.prototype.getSDFObj=function(){return p.sendWithPromise("Destination.getSDFObj",{dest:this.id}).then(function(t){return _(p.Obj,t)})},p.Destination.prototype.getExplicitDestObj=function(){return p.sendWithPromise("Destination.getExplicitDestObj",{dest:this.id}).then(function(t){return _(p.Obj,t)})},p.GState.prototype.getTransform=function(){return p.sendWithPromise("GState.getTransform",{gs:this.id}).then(function(t){return new p.Matrix2D(t)})},p.GState.prototype.getStrokeColorSpace=function(){return p.sendWithPromise("GState.getStrokeColorSpace",{gs:this.id}).then(function(t){return S(p.ColorSpace,t)})},p.GState.prototype.getFillColorSpace=function(){return p.sendWithPromise("GState.getFillColorSpace",{gs:this.id}).then(function(t){return S(p.ColorSpace,t)})},p.GState.prototype.getStrokeColor=function(){return p.sendWithPromise("GState.getStrokeColor",{gs:this.id}).then(function(t){return S(p.ColorPt,t)})},p.GState.prototype.getStrokePattern=function(){return p.sendWithPromise("GState.getStrokePattern",{gs:this.id}).then(function(t){return S(p.PatternColor,t)})},p.GState.prototype.getFillColor=function(){return p.sendWithPromise("GState.getFillColor",{gs:this.id}).then(function(t){return S(p.ColorPt,t)})},p.GState.prototype.getFillPattern=function(){return p.sendWithPromise("GState.getFillPattern",{gs:this.id}).then(function(t){return S(p.PatternColor,t)})},p.GState.prototype.getFlatness=function(){return p.sendWithPromise("GState.getFlatness",{gs:this.id})},p.GState.prototype.getLineCap=function(){return p.sendWithPromise("GState.getLineCap",{gs:this.id})},p.GState.prototype.getLineJoin=function(){return p.sendWithPromise("GState.getLineJoin",{gs:this.id})},p.GState.prototype.getLineWidth=function(){return p.sendWithPromise("GState.getLineWidth",{gs:this.id})},p.GState.prototype.getMiterLimit=function(){return p.sendWithPromise("GState.getMiterLimit",{gs:this.id})},p.GState.prototype.getPhase=function(){return p.sendWithPromise("GState.getPhase",{gs:this.id})},p.GState.prototype.getCharSpacing=function(){return p.sendWithPromise("GState.getCharSpacing",{gs:this.id})},p.GState.prototype.getWordSpacing=function(){return p.sendWithPromise("GState.getWordSpacing",{gs:this.id})},p.GState.prototype.getHorizontalScale=function(){return p.sendWithPromise("GState.getHorizontalScale",{gs:this.id})},p.GState.prototype.getLeading=function(){return p.sendWithPromise("GState.getLeading",{gs:this.id})},p.GState.prototype.getFont=function(){return p.sendWithPromise("GState.getFont",{gs:this.id}).then(function(t){return S(p.Font,t)})},p.GState.prototype.getFontSize=function(){return p.sendWithPromise("GState.getFontSize",{gs:this.id})},p.GState.prototype.getTextRenderMode=function(){return p.sendWithPromise("GState.getTextRenderMode",{gs:this.id})},p.GState.prototype.getTextRise=function(){return p.sendWithPromise("GState.getTextRise",{gs:this.id})},p.GState.prototype.isTextKnockout=function(){return p.sendWithPromise("GState.isTextKnockout",{gs:this.id})},p.GState.prototype.getRenderingIntent=function(){return p.sendWithPromise("GState.getRenderingIntent",{gs:this.id})},p.GState.getRenderingIntentType=function(t){return P(arguments.length,1,"getRenderingIntentType","(string)",[[t,"string"]]),p.sendWithPromise("gStateGetRenderingIntentType",{name:t})},p.GState.prototype.getBlendMode=function(){return p.sendWithPromise("GState.getBlendMode",{gs:this.id})},p.GState.prototype.getFillOpacity=function(){return p.sendWithPromise("GState.getFillOpacity",{gs:this.id})},p.GState.prototype.getStrokeOpacity=function(){return p.sendWithPromise("GState.getStrokeOpacity",{gs:this.id})},p.GState.prototype.getAISFlag=function(){return p.sendWithPromise("GState.getAISFlag",{gs:this.id})},p.GState.prototype.getSoftMask=function(){return p.sendWithPromise("GState.getSoftMask",{gs:this.id}).then(function(t){return _(p.Obj,t)})},p.GState.prototype.getSoftMaskTransform=function(){return p.sendWithPromise("GState.getSoftMaskTransform",{gs:this.id}).then(function(t){return new p.Matrix2D(t)})},p.GState.prototype.getStrokeOverprint=function(){return p.sendWithPromise("GState.getStrokeOverprint",{gs:this.id})},p.GState.prototype.getFillOverprint=function(){return p.sendWithPromise("GState.getFillOverprint",{gs:this.id})},p.GState.prototype.getOverprintMode=function(){return p.sendWithPromise("GState.getOverprintMode",{gs:this.id})},p.GState.prototype.getAutoStrokeAdjust=function(){return p.sendWithPromise("GState.getAutoStrokeAdjust",{gs:this.id})},p.GState.prototype.getSmoothnessTolerance=function(){return p.sendWithPromise("GState.getSmoothnessTolerance",{gs:this.id})},p.GState.prototype.getTransferFunct=function(){return p.sendWithPromise("GState.getTransferFunct",{gs:this.id}).then(function(t){return _(p.Obj,t)})},p.GState.prototype.getBlackGenFunct=function(){return p.sendWithPromise("GState.getBlackGenFunct",{gs:this.id}).then(function(t){return _(p.Obj,t)})},p.GState.prototype.getUCRFunct=function(){return p.sendWithPromise("GState.getUCRFunct",{gs:this.id}).then(function(t){return _(p.Obj,t)})},p.GState.prototype.getHalftone=function(){return p.sendWithPromise("GState.getHalftone",{gs:this.id}).then(function(t){return _(p.Obj,t)})},p.GState.prototype.setTransformMatrix=function(t){return P(arguments.length,1,"setTransformMatrix","(PDFNet.Matrix2D)",[[t,"Structure",p.Matrix2D,"Matrix2D"]]),b("setTransformMatrix",[[t,0]]),p.sendWithPromise("GState.setTransformMatrix",{gs:this.id,mtx:t})},p.GState.prototype.setTransform=function(t,e,n,i,r,o){return P(arguments.length,6,"setTransform","(number, number, number, number, number, number)",[[t,"number"],[e,"number"],[n,"number"],[i,"number"],[r,"number"],[o,"number"]]),p.sendWithPromise("GState.setTransform",{gs:this.id,a:t,b:e,c:n,d:i,h:r,v:o})},p.GState.prototype.concatMatrix=function(t){return P(arguments.length,1,"concatMatrix","(PDFNet.Matrix2D)",[[t,"Structure",p.Matrix2D,"Matrix2D"]]),b("concatMatrix",[[t,0]]),p.sendWithPromise("GState.concatMatrix",{gs:this.id,mtx:t})},p.GState.prototype.concat=function(t,e,n,i,r,o){return P(arguments.length,6,"concat","(number, number, number, number, number, number)",[[t,"number"],[e,"number"],[n,"number"],[i,"number"],[r,"number"],[o,"number"]]),p.sendWithPromise("GState.concat",{gs:this.id,a:t,b:e,c:n,d:i,h:r,v:o})},p.GState.prototype.setStrokeColorSpace=function(t){return P(arguments.length,1,"setStrokeColorSpace","(PDFNet.ColorSpace)",[[t,"Object",p.ColorSpace,"ColorSpace"]]),p.sendWithPromise("GState.setStrokeColorSpace",{gs:this.id,cs:t.id})},p.GState.prototype.setFillColorSpace=function(t){return P(arguments.length,1,"setFillColorSpace","(PDFNet.ColorSpace)",[[t,"Object",p.ColorSpace,"ColorSpace"]]),p.sendWithPromise("GState.setFillColorSpace",{gs:this.id,cs:t.id})},p.GState.prototype.setStrokeColorWithColorPt=function(t){return P(arguments.length,1,"setStrokeColorWithColorPt","(PDFNet.ColorPt)",[[t,"Object",p.ColorPt,"ColorPt"]]),p.sendWithPromise("GState.setStrokeColorWithColorPt",{gs:this.id,c:t.id})},p.GState.prototype.setStrokeColorWithPattern=function(t){return P(arguments.length,1,"setStrokeColorWithPattern","(PDFNet.PatternColor)",[[t,"Object",p.PatternColor,"PatternColor"]]),p.sendWithPromise("GState.setStrokeColorWithPattern",{gs:this.id,pattern:t.id})},p.GState.prototype.setStrokeColor=function(t,e){return P(arguments.length,2,"setStrokeColor","(PDFNet.PatternColor, PDFNet.ColorPt)",[[t,"Object",p.PatternColor,"PatternColor"],[e,"Object",p.ColorPt,"ColorPt"]]),p.sendWithPromise("GState.setStrokeColor",{gs:this.id,pattern:t.id,c:e.id})},p.GState.prototype.setFillColorWithColorPt=function(t){return P(arguments.length,1,"setFillColorWithColorPt","(PDFNet.ColorPt)",[[t,"Object",p.ColorPt,"ColorPt"]]),p.sendWithPromise("GState.setFillColorWithColorPt",{gs:this.id,c:t.id})},p.GState.prototype.setFillColorWithPattern=function(t){return P(arguments.length,1,"setFillColorWithPattern","(PDFNet.PatternColor)",[[t,"Object",p.PatternColor,"PatternColor"]]),p.sendWithPromise("GState.setFillColorWithPattern",{gs:this.id,pattern:t.id})},p.GState.prototype.setFillColor=function(t,e){return P(arguments.length,2,"setFillColor","(PDFNet.PatternColor, PDFNet.ColorPt)",[[t,"Object",p.PatternColor,"PatternColor"],[e,"Object",p.ColorPt,"ColorPt"]]),p.sendWithPromise("GState.setFillColor",{gs:this.id,pattern:t.id,c:e.id})},p.GState.prototype.setFlatness=function(t){return P(arguments.length,1,"setFlatness","(number)",[[t,"number"]]),p.sendWithPromise("GState.setFlatness",{gs:this.id,flatness:t})},p.GState.prototype.setLineCap=function(t){return P(arguments.length,1,"setLineCap","(number)",[[t,"number"]]),p.sendWithPromise("GState.setLineCap",{gs:this.id,cap:t})},p.GState.prototype.setLineJoin=function(t){return P(arguments.length,1,"setLineJoin","(number)",[[t,"number"]]),p.sendWithPromise("GState.setLineJoin",{gs:this.id,join:t})},p.GState.prototype.setLineWidth=function(t){return P(arguments.length,1,"setLineWidth","(number)",[[t,"number"]]),p.sendWithPromise("GState.setLineWidth",{gs:this.id,width:t})},p.GState.prototype.setMiterLimit=function(t){return P(arguments.length,1,"setMiterLimit","(number)",[[t,"number"]]),p.sendWithPromise("GState.setMiterLimit",{gs:this.id,miter_limit:t})},p.GState.prototype.setDashPattern=function(t,e){return P(arguments.length,2,"setDashPattern","(Array<number>, number)",[[t,"Array"],[e,"number"]]),p.sendWithPromise("GState.setDashPattern",{gs:this.id,dash_array:t,phase:e})},p.GState.prototype.setCharSpacing=function(t){return P(arguments.length,1,"setCharSpacing","(number)",[[t,"number"]]),p.sendWithPromise("GState.setCharSpacing",{gs:this.id,char_spacing:t})},p.GState.prototype.setWordSpacing=function(t){return P(arguments.length,1,"setWordSpacing","(number)",[[t,"number"]]),p.sendWithPromise("GState.setWordSpacing",{gs:this.id,word_spacing:t})},p.GState.prototype.setHorizontalScale=function(t){return P(arguments.length,1,"setHorizontalScale","(number)",[[t,"number"]]),p.sendWithPromise("GState.setHorizontalScale",{gs:this.id,hscale:t})},p.GState.prototype.setLeading=function(t){return P(arguments.length,1,"setLeading","(number)",[[t,"number"]]),p.sendWithPromise("GState.setLeading",{gs:this.id,leading:t})},p.GState.prototype.setFont=function(t,e){return P(arguments.length,2,"setFont","(PDFNet.Font, number)",[[t,"Object",p.Font,"Font"],[e,"number"]]),p.sendWithPromise("GState.setFont",{gs:this.id,font:t.id,font_sz:e})},p.GState.prototype.setTextRenderMode=function(t){return P(arguments.length,1,"setTextRenderMode","(number)",[[t,"number"]]),p.sendWithPromise("GState.setTextRenderMode",{gs:this.id,rmode:t})},p.GState.prototype.setTextRise=function(t){return P(arguments.length,1,"setTextRise","(number)",[[t,"number"]]),p.sendWithPromise("GState.setTextRise",{gs:this.id,rise:t})},p.GState.prototype.setTextKnockout=function(t){return P(arguments.length,1,"setTextKnockout","(boolean)",[[t,"boolean"]]),p.sendWithPromise("GState.setTextKnockout",{gs:this.id,knockout:t})},p.GState.prototype.setRenderingIntent=function(t){return P(arguments.length,1,"setRenderingIntent","(number)",[[t,"number"]]),p.sendWithPromise("GState.setRenderingIntent",{gs:this.id,intent:t})},p.GState.prototype.setBlendMode=function(t){return P(arguments.length,1,"setBlendMode","(number)",[[t,"number"]]),p.sendWithPromise("GState.setBlendMode",{gs:this.id,BM:t})},p.GState.prototype.setFillOpacity=function(t){return P(arguments.length,1,"setFillOpacity","(number)",[[t,"number"]]),p.sendWithPromise("GState.setFillOpacity",{gs:this.id,ca:t})},p.GState.prototype.setStrokeOpacity=function(t){return P(arguments.length,1,"setStrokeOpacity","(number)",[[t,"number"]]),p.sendWithPromise("GState.setStrokeOpacity",{gs:this.id,ca:t})},p.GState.prototype.setAISFlag=function(t){return P(arguments.length,1,"setAISFlag","(boolean)",[[t,"boolean"]]),p.sendWithPromise("GState.setAISFlag",{gs:this.id,AIS:t})},p.GState.prototype.setSoftMask=function(t){return P(arguments.length,1,"setSoftMask","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("GState.setSoftMask",{gs:this.id,SM:t.id})},p.GState.prototype.setStrokeOverprint=function(t){return P(arguments.length,1,"setStrokeOverprint","(boolean)",[[t,"boolean"]]),p.sendWithPromise("GState.setStrokeOverprint",{gs:this.id,OP:t})},p.GState.prototype.setFillOverprint=function(t){return P(arguments.length,1,"setFillOverprint","(boolean)",[[t,"boolean"]]),p.sendWithPromise("GState.setFillOverprint",{gs:this.id,op:t})},p.GState.prototype.setOverprintMode=function(t){return P(arguments.length,1,"setOverprintMode","(number)",[[t,"number"]]),p.sendWithPromise("GState.setOverprintMode",{gs:this.id,OPM:t})},p.GState.prototype.setAutoStrokeAdjust=function(t){return P(arguments.length,1,"setAutoStrokeAdjust","(boolean)",[[t,"boolean"]]),p.sendWithPromise("GState.setAutoStrokeAdjust",{gs:this.id,SA:t})},p.GState.prototype.setSmoothnessTolerance=function(t){return P(arguments.length,1,"setSmoothnessTolerance","(number)",[[t,"number"]]),p.sendWithPromise("GState.setSmoothnessTolerance",{gs:this.id,SM:t})},p.GState.prototype.setBlackGenFunct=function(t){return P(arguments.length,1,"setBlackGenFunct","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("GState.setBlackGenFunct",{gs:this.id,BG:t.id})},p.GState.prototype.setUCRFunct=function(t){return P(arguments.length,1,"setUCRFunct","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("GState.setUCRFunct",{gs:this.id,UCR:t.id})},p.GState.prototype.setTransferFunct=function(t){return P(arguments.length,1,"setTransferFunct","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("GState.setTransferFunct",{gs:this.id,TR:t.id})},p.GState.prototype.setHalftone=function(t){return P(arguments.length,1,"setHalftone","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("GState.setHalftone",{gs:this.id,HT:t.id})},p.Element.prototype.getType=function(){return p.sendWithPromise("Element.getType",{e:this.id})},p.Element.prototype.getGState=function(){return p.sendWithPromise("Element.getGState",{e:this.id}).then(function(t){return _(p.GState,t)})},p.Element.prototype.getCTM=function(){return p.sendWithPromise("Element.getCTM",{e:this.id}).then(function(t){return new p.Matrix2D(t)})},p.Element.prototype.getParentStructElement=function(){return p.sendWithPromise("Element.getParentStructElement",{e:this.id}).then(function(t){return new p.SElement(t)})},p.Element.prototype.getStructMCID=function(){return p.sendWithPromise("Element.getStructMCID",{e:this.id})},p.Element.prototype.isOCVisible=function(){return p.sendWithPromise("Element.isOCVisible",{e:this.id})},p.Element.prototype.isClippingPath=function(){return p.sendWithPromise("Element.isClippingPath",{e:this.id})},p.Element.prototype.isStroked=function(){return p.sendWithPromise("Element.isStroked",{e:this.id})},p.Element.prototype.isFilled=function(){return p.sendWithPromise("Element.isFilled",{e:this.id})},p.Element.prototype.isWindingFill=function(){return p.sendWithPromise("Element.isWindingFill",{e:this.id})},p.Element.prototype.isClipWindingFill=function(){return p.sendWithPromise("Element.isClipWindingFill",{e:this.id})},p.Element.prototype.setPathClip=function(t){return P(arguments.length,1,"setPathClip","(boolean)",[[t,"boolean"]]),p.sendWithPromise("Element.setPathClip",{e:this.id,clip:t})},p.Element.prototype.setPathStroke=function(t){return P(arguments.length,1,"setPathStroke","(boolean)",[[t,"boolean"]]),p.sendWithPromise("Element.setPathStroke",{e:this.id,stroke:t})},p.Element.prototype.setPathFill=function(t){return P(arguments.length,1,"setPathFill","(boolean)",[[t,"boolean"]]),p.sendWithPromise("Element.setPathFill",{e:this.id,fill:t})},p.Element.prototype.setWindingFill=function(t){return P(arguments.length,1,"setWindingFill","(boolean)",[[t,"boolean"]]),p.sendWithPromise("Element.setWindingFill",{e:this.id,winding_rule:t})},p.Element.prototype.setClipWindingFill=function(t){return P(arguments.length,1,"setClipWindingFill","(boolean)",[[t,"boolean"]]),p.sendWithPromise("Element.setClipWindingFill",{e:this.id,winding_rule:t})},p.Element.prototype.setPathTypes=function(t,e){return P(arguments.length,2,"setPathTypes","(string, number)",[[t,"string"],[e,"number"]]),p.sendWithPromise("Element.setPathTypes",{e:this.id,in_seg_types:t,count:e})},p.Element.prototype.getXObject=function(){return p.sendWithPromise("Element.getXObject",{e:this.id}).then(function(t){return _(p.Obj,t)})},p.Element.prototype.getImageData=function(){return p.sendWithPromise("Element.getImageData",{e:this.id}).then(function(t){return _(p.Filter,t)})},p.Element.prototype.getImageDataSize=function(){return p.sendWithPromise("Element.getImageDataSize",{e:this.id})},p.Element.prototype.getImageColorSpace=function(){return p.sendWithPromise("Element.getImageColorSpace",{e:this.id}).then(function(t){return S(p.ColorSpace,t)})},p.Element.prototype.getImageWidth=function(){return p.sendWithPromise("Element.getImageWidth",{e:this.id})},p.Element.prototype.getImageHeight=function(){return p.sendWithPromise("Element.getImageHeight",{e:this.id})},p.Element.prototype.getDecodeArray=function(){return p.sendWithPromise("Element.getDecodeArray",{e:this.id}).then(function(t){return _(p.Obj,t)})},p.Element.prototype.getBitsPerComponent=function(){return p.sendWithPromise("Element.getBitsPerComponent",{e:this.id})},p.Element.prototype.getComponentNum=function(){return p.sendWithPromise("Element.getComponentNum",{e:this.id})};function m(r,o){o=o||{};var s=new XMLHttpRequest;return new Promise(function(e,n){s.open("GET",r,!0),s.responseType="arraybuffer",o.withCredentials&&(s.withCredentials=o.withCredentials),s.onerror=function(){n(Error("Network error occurred"))},s.onload=function(t){200==this.status?(t=new Uint8Array(s.response),e(t)):n(Error("Download Failed"))};var t=o.customHeaders;if(t)for(var i in t)s.setRequestHeader(i,t[i]);s.send()},function(){s.abort()})}function g(t){return 0===t?"1st":1===t?"2nd":2===t?"3rd":t+1+"th"}p.Element.prototype.isImageMask=function(){return p.sendWithPromise("Element.isImageMask",{e:this.id})},p.Element.prototype.isImageInterpolate=function(){return p.sendWithPromise("Element.isImageInterpolate",{e:this.id})},p.Element.prototype.getMask=function(){return p.sendWithPromise("Element.getMask",{e:this.id}).then(function(t){return _(p.Obj,t)})},p.Element.prototype.getImageRenderingIntent=function(){return p.sendWithPromise("Element.getImageRenderingIntent",{e:this.id})},p.Element.prototype.getTextString=function(){return p.sendWithPromise("Element.getTextString",{e:this.id})},p.Element.prototype.getTextMatrix=function(){return p.sendWithPromise("Element.getTextMatrix",{e:this.id}).then(function(t){return new p.Matrix2D(t)})},p.Element.prototype.getCharIterator=function(){return p.sendWithPromise("Element.getCharIterator",{e:this.id}).then(function(t){return S(p.Iterator,t,"CharData")})},p.Element.prototype.getTextLength=function(){return p.sendWithPromise("Element.getTextLength",{e:this.id})},p.Element.prototype.getPosAdjustment=function(){return p.sendWithPromise("Element.getPosAdjustment",{e:this.id})},p.Element.prototype.getNewTextLineOffset=function(){return p.sendWithPromise("Element.getNewTextLineOffset",{e:this.id})},p.Element.prototype.hasTextMatrix=function(){return p.sendWithPromise("Element.hasTextMatrix",{e:this.id})},p.Element.prototype.setTextData=function(t){P(arguments.length,1,"setTextData","(ArrayBuffer|TypedArray)",[[t,"ArrayBuffer"]]);var e=y(t,!1);return p.sendWithPromise("Element.setTextData",{e:this.id,buf_text_data:e})},p.Element.prototype.setTextMatrix=function(t){return P(arguments.length,1,"setTextMatrix","(PDFNet.Matrix2D)",[[t,"Structure",p.Matrix2D,"Matrix2D"]]),b("setTextMatrix",[[t,0]]),p.sendWithPromise("Element.setTextMatrix",{e:this.id,mtx:t})},p.Element.prototype.setTextMatrixEntries=function(t,e,n,i,r,o){return P(arguments.length,6,"setTextMatrixEntries","(number, number, number, number, number, number)",[[t,"number"],[e,"number"],[n,"number"],[i,"number"],[r,"number"],[o,"number"]]),p.sendWithPromise("Element.setTextMatrixEntries",{e:this.id,a:t,b:e,c:n,d:i,h:r,v:o})},p.Element.prototype.setPosAdjustment=function(t){return P(arguments.length,1,"setPosAdjustment","(number)",[[t,"number"]]),p.sendWithPromise("Element.setPosAdjustment",{e:this.id,adjust:t})},p.Element.prototype.updateTextMetrics=function(){return p.sendWithPromise("Element.updateTextMetrics",{e:this.id})},p.Element.prototype.setNewTextLineOffset=function(t,e){return P(arguments.length,2,"setNewTextLineOffset","(number, number)",[[t,"number"],[e,"number"]]),p.sendWithPromise("Element.setNewTextLineOffset",{e:this.id,dx:t,dy:e})},p.Element.prototype.getShading=function(){return p.sendWithPromise("Element.getShading",{e:this.id}).then(function(t){return S(p.Shading,t)})},p.Element.prototype.getMCPropertyDict=function(){return p.sendWithPromise("Element.getMCPropertyDict",{e:this.id}).then(function(t){return _(p.Obj,t)})},p.Element.prototype.getMCTag=function(){return p.sendWithPromise("Element.getMCTag",{e:this.id}).then(function(t){return _(p.Obj,t)})},p.ShapedText.prototype.getScale=function(){return p.sendWithPromise("ShapedText.getScale",{self:this.id})},p.ShapedText.prototype.getShapingStatus=function(){return p.sendWithPromise("ShapedText.getShapingStatus",{self:this.id})},p.ShapedText.prototype.getFailureReason=function(){return p.sendWithPromise("ShapedText.getFailureReason",{self:this.id})},p.ShapedText.prototype.getText=function(){return p.sendWithPromise("ShapedText.getText",{self:this.id})},p.ShapedText.prototype.getNumGlyphs=function(){return p.sendWithPromise("ShapedText.getNumGlyphs",{self:this.id})},p.ShapedText.prototype.getGlyph=function(t){return P(arguments.length,1,"getGlyph","(number)",[[t,"number"]]),p.sendWithPromise("ShapedText.getGlyph",{self:this.id,index:t})},p.ShapedText.prototype.getGlyphXPos=function(t){return P(arguments.length,1,"getGlyphXPos","(number)",[[t,"number"]]),p.sendWithPromise("ShapedText.getGlyphXPos",{self:this.id,index:t})},p.ShapedText.prototype.getGlyphYPos=function(t){return P(arguments.length,1,"getGlyphYPos","(number)",[[t,"number"]]),p.sendWithPromise("ShapedText.getGlyphYPos",{self:this.id,index:t})},p.ElementBuilder.create=function(){return p.sendWithPromise("elementBuilderCreate",{}).then(function(t){return S(p.ElementBuilder,t)})},p.ElementBuilder.prototype.reset=function(t){return void 0===t&&(t=new p.GState("0")),P(arguments.length,0,"reset","(PDFNet.GState)",[[t,"Object",p.GState,"GState"]]),p.sendWithPromise("ElementBuilder.reset",{b:this.id,gs:t.id})},p.ElementBuilder.prototype.createImage=function(t){return P(arguments.length,1,"createImage","(PDFNet.Image)",[[t,"Object",p.Image,"Image"]]),p.sendWithPromise("ElementBuilder.createImage",{b:this.id,img:t.id}).then(function(t){return _(p.Element,t)})},p.ElementBuilder.prototype.createImageFromMatrix=function(t,e){return P(arguments.length,2,"createImageFromMatrix","(PDFNet.Image, PDFNet.Matrix2D)",[[t,"Object",p.Image,"Image"],[e,"Structure",p.Matrix2D,"Matrix2D"]]),b("createImageFromMatrix",[[e,1]]),p.sendWithPromise("ElementBuilder.createImageFromMatrix",{b:this.id,img:t.id,mtx:e}).then(function(t){return _(p.Element,t)})},p.ElementBuilder.prototype.createImageScaled=function(t,e,n,i,r){return P(arguments.length,5,"createImageScaled","(PDFNet.Image, number, number, number, number)",[[t,"Object",p.Image,"Image"],[e,"number"],[n,"number"],[i,"number"],[r,"number"]]),p.sendWithPromise("ElementBuilder.createImageScaled",{b:this.id,img:t.id,x:e,y:n,hscale:i,vscale:r}).then(function(t){return _(p.Element,t)})},p.ElementBuilder.prototype.createGroupBegin=function(){return p.sendWithPromise("ElementBuilder.createGroupBegin",{b:this.id}).then(function(t){return _(p.Element,t)})},p.ElementBuilder.prototype.createGroupEnd=function(){return p.sendWithPromise("ElementBuilder.createGroupEnd",{b:this.id}).then(function(t){return _(p.Element,t)})},p.ElementBuilder.prototype.createShading=function(t){return P(arguments.length,1,"createShading","(PDFNet.Shading)",[[t,"Object",p.Shading,"Shading"]]),p.sendWithPromise("ElementBuilder.createShading",{b:this.id,sh:t.id}).then(function(t){return _(p.Element,t)})},p.ElementBuilder.prototype.createFormFromStream=function(t){return P(arguments.length,1,"createFormFromStream","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("ElementBuilder.createFormFromStream",{b:this.id,form:t.id}).then(function(t){return _(p.Element,t)})},p.ElementBuilder.prototype.createFormFromPage=function(t){return P(arguments.length,1,"createFormFromPage","(PDFNet.Page)",[[t,"Object",p.Page,"Page"]]),p.sendWithPromise("ElementBuilder.createFormFromPage",{b:this.id,page:t.id}).then(function(t){return _(p.Element,t)})},p.ElementBuilder.prototype.createFormFromDoc=function(t,e){return P(arguments.length,2,"createFormFromDoc","(PDFNet.Page, PDFNet.PDFDoc)",[[t,"Object",p.Page,"Page"],[e,"PDFDoc"]]),p.sendWithPromise("ElementBuilder.createFormFromDoc",{b:this.id,page:t.id,doc:e.id}).then(function(t){return _(p.Element,t)})},p.ElementBuilder.prototype.createTextBeginWithFont=function(t,e){return P(arguments.length,2,"createTextBeginWithFont","(PDFNet.Font, number)",[[t,"Object",p.Font,"Font"],[e,"number"]]),p.sendWithPromise("ElementBuilder.createTextBeginWithFont",{b:this.id,font:t.id,font_sz:e}).then(function(t){return _(p.Element,t)})},p.ElementBuilder.prototype.createTextBegin=function(){return p.sendWithPromise("ElementBuilder.createTextBegin",{b:this.id}).then(function(t){return _(p.Element,t)})},p.ElementBuilder.prototype.createTextEnd=function(){return p.sendWithPromise("ElementBuilder.createTextEnd",{b:this.id}).then(function(t){return _(p.Element,t)})},p.ElementBuilder.prototype.createTextRun=function(t,e,n){return P(arguments.length,3,"createTextRun","(string, PDFNet.Font, number)",[[t,"string"],[e,"Object",p.Font,"Font"],[n,"number"]]),p.sendWithPromise("ElementBuilder.createTextRun",{b:this.id,text_data:t,font:e.id,font_sz:n}).then(function(t){return _(p.Element,t)})},p.ElementBuilder.prototype.createTextRunUnsigned=function(t,e,n){return P(arguments.length,3,"createTextRunUnsigned","(string, PDFNet.Font, number)",[[t,"string"],[e,"Object",p.Font,"Font"],[n,"number"]]),p.sendWithPromise("ElementBuilder.createTextRunUnsigned",{b:this.id,text_data:t,font:e.id,font_sz:n}).then(function(t){return _(p.Element,t)})},p.ElementBuilder.prototype.createNewTextRun=function(t){return P(arguments.length,1,"createNewTextRun","(string)",[[t,"string"]]),p.sendWithPromise("ElementBuilder.createNewTextRun",{b:this.id,text_data:t}).then(function(t){return _(p.Element,t)})},p.ElementBuilder.prototype.createNewTextRunUnsigned=function(t){return P(arguments.length,1,"createNewTextRunUnsigned","(string)",[[t,"string"]]),p.sendWithPromise("ElementBuilder.createNewTextRunUnsigned",{b:this.id,text_data:t}).then(function(t){return _(p.Element,t)})},p.ElementBuilder.prototype.createShapedTextRun=function(t){return P(arguments.length,1,"createShapedTextRun","(PDFNet.ShapedText)",[[t,"Object",p.ShapedText,"ShapedText"]]),p.sendWithPromise("ElementBuilder.createShapedTextRun",{b:this.id,text_data:t.id}).then(function(t){return _(p.Element,t)})},p.ElementBuilder.prototype.createTextNewLineWithOffset=function(t,e){return P(arguments.length,2,"createTextNewLineWithOffset","(number, number)",[[t,"number"],[e,"number"]]),p.sendWithPromise("ElementBuilder.createTextNewLineWithOffset",{b:this.id,dx:t,dy:e}).then(function(t){return _(p.Element,t)})},p.ElementBuilder.prototype.createTextNewLine=function(){return p.sendWithPromise("ElementBuilder.createTextNewLine",{b:this.id}).then(function(t){return _(p.Element,t)})},p.ElementBuilder.prototype.createPath=function(t,e){P(arguments.length,2,"createPath","(Array<number>, ArrayBuffer|TypedArray)",[[t,"Array"],[e,"ArrayBuffer"]]);var n=y(e,!1);return p.sendWithPromise("ElementBuilder.createPath",{b:this.id,points_list:t,buf_seg_types:n}).then(function(t){return _(p.Element,t)})},p.ElementBuilder.prototype.createRect=function(t,e,n,i){return P(arguments.length,4,"createRect","(number, number, number, number)",[[t,"number"],[e,"number"],[n,"number"],[i,"number"]]),p.sendWithPromise("ElementBuilder.createRect",{b:this.id,x:t,y:e,width:n,height:i}).then(function(t){return _(p.Element,t)})},p.ElementBuilder.prototype.createEllipse=function(t,e,n,i){return P(arguments.length,4,"createEllipse","(number, number, number, number)",[[t,"number"],[e,"number"],[n,"number"],[i,"number"]]),p.sendWithPromise("ElementBuilder.createEllipse",{b:this.id,x:t,y:e,width:n,height:i}).then(function(t){return _(p.Element,t)})},p.ElementBuilder.prototype.pathBegin=function(){return p.sendWithPromise("ElementBuilder.pathBegin",{b:this.id})},p.ElementBuilder.prototype.pathEnd=function(){return p.sendWithPromise("ElementBuilder.pathEnd",{b:this.id}).then(function(t){return _(p.Element,t)})},p.ElementBuilder.prototype.rect=function(t,e,n,i){return P(arguments.length,4,"rect","(number, number, number, number)",[[t,"number"],[e,"number"],[n,"number"],[i,"number"]]),p.sendWithPromise("ElementBuilder.rect",{b:this.id,x:t,y:e,width:n,height:i})},p.ElementBuilder.prototype.ellipse=function(t,e,n,i){return P(arguments.length,4,"ellipse","(number, number, number, number)",[[t,"number"],[e,"number"],[n,"number"],[i,"number"]]),p.sendWithPromise("ElementBuilder.ellipse",{b:this.id,x:t,y:e,width:n,height:i})},p.ElementBuilder.prototype.moveTo=function(t,e){return P(arguments.length,2,"moveTo","(number, number)",[[t,"number"],[e,"number"]]),p.sendWithPromise("ElementBuilder.moveTo",{b:this.id,x:t,y:e})},p.ElementBuilder.prototype.lineTo=function(t,e){return P(arguments.length,2,"lineTo","(number, number)",[[t,"number"],[e,"number"]]),p.sendWithPromise("ElementBuilder.lineTo",{b:this.id,x:t,y:e})},p.ElementBuilder.prototype.curveTo=function(t,e,n,i,r,o){return P(arguments.length,6,"curveTo","(number, number, number, number, number, number)",[[t,"number"],[e,"number"],[n,"number"],[i,"number"],[r,"number"],[o,"number"]]),p.sendWithPromise("ElementBuilder.curveTo",{b:this.id,cx1:t,cy1:e,cx2:n,cy2:i,x2:r,y2:o})},p.ElementBuilder.prototype.arcTo=function(t,e,n,i,r,o){return P(arguments.length,6,"arcTo","(number, number, number, number, number, number)",[[t,"number"],[e,"number"],[n,"number"],[i,"number"],[r,"number"],[o,"number"]]),p.sendWithPromise("ElementBuilder.arcTo",{b:this.id,x:t,y:e,width:n,height:i,start:r,extent:o})},p.ElementBuilder.prototype.arcTo2=function(t,e,n,i,r,o,s){return P(arguments.length,7,"arcTo2","(number, number, number, boolean, boolean, number, number)",[[t,"number"],[e,"number"],[n,"number"],[i,"boolean"],[r,"boolean"],[o,"number"],[s,"number"]]),p.sendWithPromise("ElementBuilder.arcTo2",{b:this.id,xr:t,yr:e,rx:n,isLargeArc:i,sweep:r,endX:o,endY:s})},p.ElementBuilder.prototype.closePath=function(){return p.sendWithPromise("ElementBuilder.closePath",{b:this.id})},p.ElementBuilder.prototype.createMarkedContentBeginInlineProperties=function(t){return P(arguments.length,1,"createMarkedContentBeginInlineProperties","(string)",[[t,"string"]]),p.sendWithPromise("ElementBuilder.createMarkedContentBeginInlineProperties",{b:this.id,tag:t}).then(function(t){return _(p.Element,t)})},p.ElementBuilder.prototype.createMarkedContentBegin=function(t,e){return P(arguments.length,2,"createMarkedContentBegin","(string, PDFNet.Obj)",[[t,"string"],[e,"Object",p.Obj,"Obj"]]),p.sendWithPromise("ElementBuilder.createMarkedContentBegin",{b:this.id,tag:t,property_dict:e.id}).then(function(t){return _(p.Element,t)})},p.ElementBuilder.prototype.createMarkedContentEnd=function(){return p.sendWithPromise("ElementBuilder.createMarkedContentEnd",{b:this.id}).then(function(t){return _(p.Element,t)})},p.ElementBuilder.prototype.createMarkedContentPointInlineProperties=function(t){return P(arguments.length,1,"createMarkedContentPointInlineProperties","(string)",[[t,"string"]]),p.sendWithPromise("ElementBuilder.createMarkedContentPointInlineProperties",{b:this.id,tag:t}).then(function(t){return _(p.Element,t)})},p.ElementBuilder.prototype.createMarkedContentPoint=function(t,e){return P(arguments.length,2,"createMarkedContentPoint","(string, PDFNet.Obj)",[[t,"string"],[e,"Object",p.Obj,"Obj"]]),p.sendWithPromise("ElementBuilder.createMarkedContentPoint",{b:this.id,tag:t,property_dict:e.id}).then(function(t){return _(p.Element,t)})},p.ElementReader.create=function(){return p.sendWithPromise("elementReaderCreate",{}).then(function(t){return S(p.ElementReader,t)})},p.ElementReader.prototype.beginOnPage=function(t,e){return void 0===e&&(e=new p.OCGContext("0")),P(arguments.length,1,"beginOnPage","(PDFNet.Page, PDFNet.OCGContext)",[[t,"Object",p.Page,"Page"],[e,"Object",p.OCGContext,"OCGContext"]]),p.sendWithPromise("ElementReader.beginOnPage",{r:this.id,page:t.id,ctx:e.id})},p.ElementReader.prototype.begin=function(t,e,n){return void 0===e&&(e=new p.Obj("0")),void 0===n&&(n=new p.OCGContext("0")),P(arguments.length,1,"begin","(PDFNet.Obj, PDFNet.Obj, PDFNet.OCGContext)",[[t,"Object",p.Obj,"Obj"],[e,"Object",p.Obj,"Obj"],[n,"Object",p.OCGContext,"OCGContext"]]),p.sendWithPromise("ElementReader.begin",{r:this.id,content_stream:t.id,resource_dict:e.id,ctx:n.id})},p.ElementReader.prototype.appendResource=function(t){return P(arguments.length,1,"appendResource","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("ElementReader.appendResource",{r:this.id,res:t.id})},p.ElementReader.prototype.next=function(){return p.sendWithPromise("ElementReader.next",{r:this.id}).then(function(t){return _(p.Element,t)})},p.ElementReader.prototype.current=function(){return p.sendWithPromise("ElementReader.current",{r:this.id}).then(function(t){return _(p.Element,t)})},p.ElementReader.prototype.formBegin=function(){return p.sendWithPromise("ElementReader.formBegin",{r:this.id})},p.ElementReader.prototype.patternBegin=function(t,e){return void 0===e&&(e=!1),P(arguments.length,1,"patternBegin","(boolean, boolean)",[[t,"boolean"],[e,"boolean"]]),p.sendWithPromise("ElementReader.patternBegin",{r:this.id,fill_pattern:t,reset_ctm_tfm:e})},p.ElementReader.prototype.type3FontBegin=function(e,t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,1,"type3FontBegin","(PDFNet.CharData, PDFNet.Obj)",[[e,"Structure",p.CharData,"CharData"],[t,"Object",p.Obj,"Obj"]]),b("type3FontBegin",[[e,0]]),e.yieldFunction="ElementReader.type3FontBegin",p.sendWithPromise("ElementReader.type3FontBegin",{r:this.id,char_data:e,resource_dict:t.id}).then(function(t){e.yieldFunction=void 0,W(t,e)})},p.ElementReader.prototype.end=function(){return p.sendWithPromise("ElementReader.end",{r:this.id})},p.ElementReader.prototype.getChangesIterator=function(){return p.sendWithPromise("ElementReader.getChangesIterator",{r:this.id}).then(function(t){return S(p.Iterator,t,"Int")})},p.ElementReader.prototype.isChanged=function(t){return P(arguments.length,1,"isChanged","(number)",[[t,"number"]]),p.sendWithPromise("ElementReader.isChanged",{r:this.id,attrib:t})},p.ElementReader.prototype.clearChangeList=function(){return p.sendWithPromise("ElementReader.clearChangeList",{r:this.id})},p.ElementReader.prototype.getFont=function(t){return P(arguments.length,1,"getFont","(string)",[[t,"string"]]),p.sendWithPromise("ElementReader.getFont",{r:this.id,name:t}).then(function(t){return _(p.Obj,t)})},p.ElementReader.prototype.getXObject=function(t){return P(arguments.length,1,"getXObject","(string)",[[t,"string"]]),p.sendWithPromise("ElementReader.getXObject",{r:this.id,name:t}).then(function(t){return _(p.Obj,t)})},p.ElementReader.prototype.getShading=function(t){return P(arguments.length,1,"getShading","(string)",[[t,"string"]]),p.sendWithPromise("ElementReader.getShading",{r:this.id,name:t}).then(function(t){return _(p.Obj,t)})},p.ElementReader.prototype.getColorSpace=function(t){return P(arguments.length,1,"getColorSpace","(string)",[[t,"string"]]),p.sendWithPromise("ElementReader.getColorSpace",{r:this.id,name:t}).then(function(t){return _(p.Obj,t)})},p.ElementReader.prototype.getPattern=function(t){return P(arguments.length,1,"getPattern","(string)",[[t,"string"]]),p.sendWithPromise("ElementReader.getPattern",{r:this.id,name:t}).then(function(t){return _(p.Obj,t)})},p.ElementReader.prototype.getExtGState=function(t){return P(arguments.length,1,"getExtGState","(string)",[[t,"string"]]),p.sendWithPromise("ElementReader.getExtGState",{r:this.id,name:t}).then(function(t){return _(p.Obj,t)})},p.ElementWriter.create=function(){return p.sendWithPromise("elementWriterCreate",{}).then(function(t){return S(p.ElementWriter,t)})},p.ElementWriter.prototype.beginOnPage=function(t,e,n,i,r){return void 0===e&&(e=p.ElementWriter.WriteMode.e_overlay),void 0===n&&(n=!0),void 0===i&&(i=!0),void 0===r&&(r=new p.Obj("0")),P(arguments.length,1,"beginOnPage","(PDFNet.Page, number, boolean, boolean, PDFNet.Obj)",[[t,"Object",p.Page,"Page"],[e,"number"],[n,"boolean"],[i,"boolean"],[r,"Object",p.Obj,"Obj"]]),p.sendWithPromise("ElementWriter.beginOnPage",{w:this.id,page:t.id,placement:e,page_coord_sys:n,compress:i,resources:r.id})},p.ElementWriter.prototype.begin=function(t,e){return void 0===e&&(e=!0),P(arguments.length,1,"begin","(PDFNet.SDFDoc, boolean)",[[t,"SDFDoc"],[e,"boolean"]]),p.sendWithPromise("ElementWriter.begin",{w:this.id,doc:t.id,compress:e})},p.ElementWriter.prototype.beginOnObj=function(t,e,n){return void 0===e&&(e=!0),void 0===n&&(n=new p.Obj("0")),P(arguments.length,1,"beginOnObj","(PDFNet.Obj, boolean, PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"],[e,"boolean"],[n,"Object",p.Obj,"Obj"]]),p.sendWithPromise("ElementWriter.beginOnObj",{w:this.id,stream_obj_to_update:t.id,compress:e,resources:n.id})},p.ElementWriter.prototype.end=function(){return p.sendWithPromise("ElementWriter.end",{w:this.id}).then(function(t){return _(p.Obj,t)})},p.ElementWriter.prototype.writeElement=function(t){return P(arguments.length,1,"writeElement","(PDFNet.Element)",[[t,"Object",p.Element,"Element"]]),p.sendWithPromise("ElementWriter.writeElement",{w:this.id,element:t.id})},p.ElementWriter.prototype.writePlacedElement=function(t){return P(arguments.length,1,"writePlacedElement","(PDFNet.Element)",[[t,"Object",p.Element,"Element"]]),p.sendWithPromise("ElementWriter.writePlacedElement",{w:this.id,element:t.id})},p.ElementWriter.prototype.flush=function(){return p.sendWithPromise("ElementWriter.flush",{w:this.id})},p.ElementWriter.prototype.writeBuffer=function(t){P(arguments.length,1,"writeBuffer","(ArrayBuffer|TypedArray)",[[t,"ArrayBuffer"]]);var e=y(t,!1);return p.sendWithPromise("ElementWriter.writeBuffer",{w:this.id,data_buf:e})},p.ElementWriter.prototype.writeString=function(t){return P(arguments.length,1,"writeString","(string)",[[t,"string"]]),p.sendWithPromise("ElementWriter.writeString",{w:this.id,str:t})},p.ElementWriter.prototype.setDefaultGState=function(t){return P(arguments.length,1,"setDefaultGState","(PDFNet.ElementReader)",[[t,"Object",p.ElementReader,"ElementReader"]]),p.sendWithPromise("ElementWriter.setDefaultGState",{w:this.id,reader:t.id})},p.ElementWriter.prototype.writeGStateChanges=function(t){return P(arguments.length,1,"writeGStateChanges","(PDFNet.Element)",[[t,"Object",p.Element,"Element"]]),p.sendWithPromise("ElementWriter.writeGStateChanges",{w:this.id,element:t.id})},p.FileSpec.create=function(t,e,n){return void 0===n&&(n=!0),P(arguments.length,2,"create","(PDFNet.SDFDoc, string, boolean)",[[t,"SDFDoc"],[e,"string"],[n,"boolean"]]),p.sendWithPromise("fileSpecCreate",{doc:t.id,path:e,embed:n}).then(function(t){return _(p.FileSpec,t)})},p.FileSpec.createURL=function(t,e){return P(arguments.length,2,"createURL","(PDFNet.SDFDoc, string)",[[t,"SDFDoc"],[e,"string"]]),p.sendWithPromise("fileSpecCreateURL",{doc:t.id,url:e}).then(function(t){return _(p.FileSpec,t)})},p.FileSpec.createFromObj=function(t){return P(arguments.length,1,"createFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("fileSpecCreateFromObj",{f:t.id}).then(function(t){return _(p.FileSpec,t)})},p.FileSpec.prototype.copy=function(){return p.sendWithPromise("FileSpec.copy",{d:this.id}).then(function(t){return _(p.FileSpec,t)})},p.FileSpec.prototype.compare=function(t){return P(arguments.length,1,"compare","(PDFNet.FileSpec)",[[t,"Object",p.FileSpec,"FileSpec"]]),p.sendWithPromise("FileSpec.compare",{fs:this.id,d:t.id})},p.FileSpec.prototype.isValid=function(){return p.sendWithPromise("FileSpec.isValid",{fs:this.id})},p.FileSpec.prototype.export=function(t){return void 0===t&&(t=""),P(arguments.length,0,"export","(string)",[[t,"string"]]),p.sendWithPromise("FileSpec.export",{fs:this.id,save_as:t})},p.FileSpec.prototype.getFileData=function(){return p.sendWithPromise("FileSpec.getFileData",{fs:this.id}).then(function(t){return _(p.Filter,t)})},p.FileSpec.prototype.getFilePath=function(){return p.sendWithPromise("FileSpec.getFilePath",{fs:this.id})},p.FileSpec.prototype.setDesc=function(t){return P(arguments.length,1,"setDesc","(string)",[[t,"string"]]),p.sendWithPromise("FileSpec.setDesc",{fs:this.id,desc:t})},p.FileSpec.prototype.getSDFObj=function(){return p.sendWithPromise("FileSpec.getSDFObj",{fs:this.id}).then(function(t){return _(p.Obj,t)})},p.Flattener.create=function(){return p.sendWithPromise("flattenerCreate",{}).then(function(t){return S(p.Flattener,t)})},p.Flattener.prototype.setDPI=function(t){return P(arguments.length,1,"setDPI","(number)",[[t,"number"]]),p.sendWithPromise("Flattener.setDPI",{flattener:this.id,dpi:t})},p.Flattener.prototype.setThreshold=function(t){return P(arguments.length,1,"setThreshold","(number)",[[t,"number"]]),p.sendWithPromise("Flattener.setThreshold",{flattener:this.id,threshold:t})},p.Flattener.prototype.setMaximumImagePixels=function(t){return P(arguments.length,1,"setMaximumImagePixels","(number)",[[t,"number"]]),p.sendWithPromise("Flattener.setMaximumImagePixels",{flattener:this.id,max_pixels:t})},p.Flattener.prototype.setPreferJPG=function(t){return P(arguments.length,1,"setPreferJPG","(boolean)",[[t,"boolean"]]),p.sendWithPromise("Flattener.setPreferJPG",{flattener:this.id,jpg:t})},p.Flattener.prototype.setJPGQuality=function(t){return P(arguments.length,1,"setJPGQuality","(number)",[[t,"number"]]),p.sendWithPromise("Flattener.setJPGQuality",{flattener:this.id,quality:t})},p.Flattener.prototype.setPathHinting=function(t){return P(arguments.length,1,"setPathHinting","(boolean)",[[t,"boolean"]]),p.sendWithPromise("Flattener.setPathHinting",{flattener:this.id,hinting:t})},p.Flattener.prototype.process=function(t,e){return P(arguments.length,2,"process","(PDFNet.PDFDoc, number)",[[t,"PDFDoc"],[e,"number"]]),p.sendWithPromise("Flattener.process",{flattener:this.id,doc:t.id,mode:e})},p.Flattener.prototype.processPage=function(t,e){return P(arguments.length,2,"processPage","(PDFNet.Page, number)",[[t,"Object",p.Page,"Page"],[e,"number"]]),p.sendWithPromise("Flattener.processPage",{flattener:this.id,page:t.id,mode:e})},p.Font.createFromObj=function(t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,0,"createFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("fontCreateFromObj",{font_dict:t.id}).then(function(t){return S(p.Font,t)})},p.Font.create=function(t,e){return P(arguments.length,2,"create","(PDFNet.SDFDoc, number)",[[t,"SDFDoc"],[e,"number"]]),p.sendWithPromise("fontCreate",{doc:t.id,type:e}).then(function(t){return S(p.Font,t)})},p.Font.createFromFontDescriptor=function(t,e,n){return P(arguments.length,3,"createFromFontDescriptor","(PDFNet.SDFDoc, PDFNet.Font, string)",[[t,"SDFDoc"],[e,"Object",p.Font,"Font"],[n,"string"]]),p.sendWithPromise("fontCreateFromFontDescriptor",{doc:t.id,from:e.id,char_set:n}).then(function(t){return S(p.Font,t)})},p.Font.createFromName=function(t,e,n){return P(arguments.length,3,"createFromName","(PDFNet.SDFDoc, string, string)",[[t,"SDFDoc"],[e,"string"],[n,"string"]]),p.sendWithPromise("fontCreateFromName",{doc:t.id,name:e,char_set:n}).then(function(t){return S(p.Font,t)})},p.Font.createAndEmbed=function(t,e){return P(arguments.length,2,"createAndEmbed","(PDFNet.SDFDoc, number)",[[t,"SDFDoc"],[e,"number"]]),p.sendWithPromise("fontCreateAndEmbed",{doc:t.id,type:e}).then(function(t){return S(p.Font,t)})},p.Font.createTrueTypeFontWithBuffer=function(t,e,n,i){return void 0===n&&(n=!0),void 0===i&&(i=!0),P(arguments.length,2,"createTrueTypeFontWithBuffer","(PDFNet.SDFDoc, ArrayBuffer|TypedArray, boolean, boolean)",[[t,"SDFDoc"],[e,"ArrayBuffer"],[n,"boolean"],[i,"boolean"]]),e=y(e,!1),p.sendWithPromise("fontCreateTrueTypeFontWithBuffer",{doc:t.id,font_path:e,embed:n,subset:i}).then(function(t){return S(p.Font,t)})},p.Font.createCIDTrueTypeFontWithBuffer=function(t,e,n,i,r,o){return void 0===n&&(n=!0),void 0===i&&(i=!0),void 0===r&&(r=p.Font.Encoding.e_IdentityH),void 0===o&&(o=0),P(arguments.length,2,"createCIDTrueTypeFontWithBuffer","(PDFNet.SDFDoc, ArrayBuffer|TypedArray, boolean, boolean, number, number)",[[t,"SDFDoc"],[e,"ArrayBuffer"],[n,"boolean"],[i,"boolean"],[r,"number"],[o,"number"]]),e=y(e,!1),p.sendWithPromise("fontCreateCIDTrueTypeFontWithBuffer",{doc:t.id,font_path:e,embed:n,subset:i,encoding:r,ttc_font_index:o}).then(function(t){return S(p.Font,t)})},p.Font.createType1FontWithBuffer=function(t,e,n){return void 0===n&&(n=!0),P(arguments.length,2,"createType1FontWithBuffer","(PDFNet.SDFDoc, ArrayBuffer|TypedArray, boolean)",[[t,"SDFDoc"],[e,"ArrayBuffer"],[n,"boolean"]]),e=y(e,!1),p.sendWithPromise("fontCreateType1FontWithBuffer",{doc:t.id,font_path:e,embed:n}).then(function(t){return S(p.Font,t)})},p.Font.prototype.getType=function(){return p.sendWithPromise("Font.getType",{font:this.id})},p.Font.prototype.isSimple=function(){return p.sendWithPromise("Font.isSimple",{font:this.id})},p.Font.getTypeFromObj=function(t){return P(arguments.length,1,"getTypeFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("fontGetTypeFromObj",{font_dict:t.id})},p.Font.prototype.getSDFObj=function(){return p.sendWithPromise("Font.getSDFObj",{font:this.id}).then(function(t){return _(p.Obj,t)})},p.Font.prototype.getDescriptor=function(){return p.sendWithPromise("Font.getDescriptor",{font:this.id}).then(function(t){return _(p.Obj,t)})},p.Font.prototype.getName=function(){return p.sendWithPromise("Font.getName",{font:this.id})},p.Font.prototype.getFamilyName=function(){return p.sendWithPromise("Font.getFamilyName",{font:this.id})},p.Font.prototype.isFixedWidth=function(){return p.sendWithPromise("Font.isFixedWidth",{font:this.id})},p.Font.prototype.isSerif=function(){return p.sendWithPromise("Font.isSerif",{font:this.id})},p.Font.prototype.isSymbolic=function(){return p.sendWithPromise("Font.isSymbolic",{font:this.id})},p.Font.prototype.isItalic=function(){return p.sendWithPromise("Font.isItalic",{font:this.id})},p.Font.prototype.isAllCap=function(){return p.sendWithPromise("Font.isAllCap",{font:this.id})},p.Font.prototype.isForceBold=function(){return p.sendWithPromise("Font.isForceBold",{font:this.id})},p.Font.prototype.isHorizontalMode=function(){return p.sendWithPromise("Font.isHorizontalMode",{font:this.id})},p.Font.prototype.getWidth=function(t){return P(arguments.length,1,"getWidth","(number)",[[t,"number"]]),p.sendWithPromise("Font.getWidth",{font:this.id,char_code:t})},p.Font.prototype.getMaxWidth=function(){return p.sendWithPromise("Font.getMaxWidth",{font:this.id})},p.Font.prototype.getMissingWidth=function(){return p.sendWithPromise("Font.getMissingWidth",{font:this.id})},p.Font.prototype.getCharCodeIterator=function(){return p.sendWithPromise("Font.getCharCodeIterator",{font:this.id}).then(function(t){return S(p.Iterator,t,"Int")})},p.Font.prototype.getShapedText=function(t){return P(arguments.length,1,"getShapedText","(string)",[[t,"string"]]),p.sendWithPromise("Font.getShapedText",{font:this.id,text_to_shape:t}).then(function(t){return S(p.ShapedText,t)})},p.Font.prototype.getEncoding=function(){return p.sendWithPromise("Font.getEncoding",{font:this.id})},p.Font.prototype.isEmbedded=function(){return p.sendWithPromise("Font.isEmbedded",{font:this.id})},p.Font.prototype.getEmbeddedFontName=function(){return p.sendWithPromise("Font.getEmbeddedFontName",{font:this.id})},p.Font.prototype.getEmbeddedFont=function(){return p.sendWithPromise("Font.getEmbeddedFont",{font:this.id}).then(function(t){return _(p.Obj,t)})},p.Font.prototype.getEmbeddedFontBufSize=function(){return p.sendWithPromise("Font.getEmbeddedFontBufSize",{font:this.id})},p.Font.prototype.getUnitsPerEm=function(){return p.sendWithPromise("Font.getUnitsPerEm",{font:this.id})},p.Font.prototype.getBBox=function(){return p.sendWithPromise("Font.getBBox",{font:this.id}).then(function(t){return new p.Rect(t)})},p.Font.prototype.getAscent=function(){return p.sendWithPromise("Font.getAscent",{font:this.id})},p.Font.prototype.getDescent=function(){return p.sendWithPromise("Font.getDescent",{font:this.id})},p.Font.prototype.getStandardType1FontType=function(){return p.sendWithPromise("Font.getStandardType1FontType",{font:this.id})},p.Font.prototype.isCFF=function(){return p.sendWithPromise("Font.isCFF",{font:this.id})},p.Font.prototype.getType3FontMatrix=function(){return p.sendWithPromise("Font.getType3FontMatrix",{font:this.id}).then(function(t){return new p.Matrix2D(t)})},p.Font.prototype.getType3GlyphStream=function(t){return P(arguments.length,1,"getType3GlyphStream","(number)",[[t,"number"]]),p.sendWithPromise("Font.getType3GlyphStream",{font:this.id,char_code:t}).then(function(t){return _(p.Obj,t)})},p.Font.prototype.getVerticalAdvance=function(t){return P(arguments.length,1,"getVerticalAdvance","(number)",[[t,"number"]]),p.sendWithPromise("Font.getVerticalAdvance",{font:this.id,char_code:t})},p.Font.prototype.getDescendant=function(){return p.sendWithPromise("Font.getDescendant",{font:this.id}).then(function(t){return S(p.Font,t)})},p.Font.prototype.mapToCID=function(t){return P(arguments.length,1,"mapToCID","(number)",[[t,"number"]]),p.sendWithPromise("Font.mapToCID",{font:this.id,char_code:t})},p.Function.create=function(t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,0,"create","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("functionCreate",{funct_dict:t.id}).then(function(t){return S(p.Function,t)})},p.Function.prototype.getType=function(){return p.sendWithPromise("Function.getType",{f:this.id})},p.Function.prototype.getInputCardinality=function(){return p.sendWithPromise("Function.getInputCardinality",{f:this.id})},p.Function.prototype.getOutputCardinality=function(){return p.sendWithPromise("Function.getOutputCardinality",{f:this.id})},p.Function.prototype.eval=function(t,e){return P(arguments.length,2,"eval","(number, number)",[[t,"number"],[e,"number"]]),p.sendWithPromise("Function.eval",{f:this.id,inval:t,outval:e})},p.Function.prototype.getSDFObj=function(){return p.sendWithPromise("Function.getSDFObj",{f:this.id}).then(function(t){return _(p.Obj,t)})},p.Highlights.create=function(){return p.sendWithPromise("highlightsCreate",{}).then(function(t){return S(p.Highlights,t)})},p.Highlights.prototype.copyCtor=function(){return p.sendWithPromise("Highlights.copyCtor",{hlts:this.id}).then(function(t){return S(p.Highlights,t)})},p.Highlights.prototype.add=function(t){return P(arguments.length,1,"add","(PDFNet.Highlights)",[[t,"Object",p.Highlights,"Highlights"]]),p.sendWithPromise("Highlights.add",{hlts2:this.id,hlts:t.id})},p.Highlights.prototype.saveToString=function(){return p.sendWithPromise("Highlights.saveToString",{hlts:this.id})},p.Highlights.prototype.clear=function(){return p.sendWithPromise("Highlights.clear",{hlts:this.id})},p.Highlights.prototype.begin=function(t){return P(arguments.length,1,"begin","(PDFNet.PDFDoc)",[[t,"PDFDoc"]]),p.sendWithPromise("Highlights.begin",{hlts:this.id,doc:t.id})},p.Highlights.prototype.hasNext=function(){return p.sendWithPromise("Highlights.hasNext",{hlts:this.id})},p.Highlights.prototype.next=function(){return p.sendWithPromise("Highlights.next",{hlts:this.id})},p.Highlights.prototype.getCurrentPageNumber=function(){return p.sendWithPromise("Highlights.getCurrentPageNumber",{hlts:this.id})},p.Highlights.prototype.getCurrentTextRange=function(){return p.sendWithPromise("Highlights.getCurrentTextRange",{hlts:this.id}).then(function(t){return _(p.TextRange,t)})},p.Image.createFromMemory=function(t,e,n,i,r,o,s){void 0===s&&(s=new p.Obj("0")),P(arguments.length,6,"createFromMemory","(PDFNet.SDFDoc, ArrayBuffer|TypedArray, number, number, number, PDFNet.ColorSpace, PDFNet.Obj)",[[t,"SDFDoc"],[e,"ArrayBuffer"],[n,"number"],[i,"number"],[r,"number"],[o,"Object",p.ColorSpace,"ColorSpace"],[s,"Object",p.Obj,"Obj"]]);var u=y(e,!1);return p.sendWithPromise("imageCreateFromMemory",{doc:t.id,buf:u,width:n,height:i,bpc:r,color_space:o.id,encoder_hints:s.id}).then(function(t){return _(p.Image,t)})},p.Image.createFromMemory2=function(t,e,n){void 0===n&&(n=new p.Obj("0")),P(arguments.length,2,"createFromMemory2","(PDFNet.SDFDoc, ArrayBuffer|TypedArray, PDFNet.Obj)",[[t,"SDFDoc"],[e,"ArrayBuffer"],[n,"Object",p.Obj,"Obj"]]);var i=y(e,!1);return p.sendWithPromise("imageCreateFromMemory2",{doc:t.id,buf:i,encoder_hints:n.id}).then(function(t){return _(p.Image,t)})},p.Image.createFromStream=function(t,e,n,i,r,o,s){return void 0===s&&(s=new p.Obj("0")),P(arguments.length,6,"createFromStream","(PDFNet.SDFDoc, PDFNet.FilterReader, number, number, number, PDFNet.ColorSpace, PDFNet.Obj)",[[t,"SDFDoc"],[e,"Object",p.FilterReader,"FilterReader"],[n,"number"],[i,"number"],[r,"number"],[o,"Object",p.ColorSpace,"ColorSpace"],[s,"Object",p.Obj,"Obj"]]),p.sendWithPromise("imageCreateFromStream",{doc:t.id,image_data:e.id,width:n,height:i,bpc:r,color_space:o.id,encoder_hints:s.id}).then(function(t){return _(p.Image,t)})},p.Image.createFromStream2=function(t,e,n){return void 0===n&&(n=new p.Obj("0")),P(arguments.length,2,"createFromStream2","(PDFNet.SDFDoc, PDFNet.Filter, PDFNet.Obj)",[[t,"SDFDoc"],[e,"Object",p.Filter,"Filter"],[n,"Object",p.Obj,"Obj"]]),0!=e.id&&A(e.id),p.sendWithPromise("imageCreateFromStream2",{doc:t.id,no_own_image_data:e.id,encoder_hints:n.id}).then(function(t){return _(p.Image,t)})},p.Image.createImageMask=function(t,e,n,i,r){void 0===r&&(r=new p.Obj("0")),P(arguments.length,4,"createImageMask","(PDFNet.SDFDoc, ArrayBuffer|TypedArray, number, number, PDFNet.Obj)",[[t,"SDFDoc"],[e,"ArrayBuffer"],[n,"number"],[i,"number"],[r,"Object",p.Obj,"Obj"]]);var o=y(e,!1);return p.sendWithPromise("imageCreateImageMask",{doc:t.id,buf:o,width:n,height:i,encoder_hints:r.id}).then(function(t){return _(p.Image,t)})},p.Image.createImageMaskFromStream=function(t,e,n,i,r){return void 0===r&&(r=new p.Obj("0")),P(arguments.length,4,"createImageMaskFromStream","(PDFNet.SDFDoc, PDFNet.FilterReader, number, number, PDFNet.Obj)",[[t,"SDFDoc"],[e,"Object",p.FilterReader,"FilterReader"],[n,"number"],[i,"number"],[r,"Object",p.Obj,"Obj"]]),p.sendWithPromise("imageCreateImageMaskFromStream",{doc:t.id,image_data:e.id,width:n,height:i,encoder_hints:r.id}).then(function(t){return _(p.Image,t)})},p.Image.createSoftMask=function(t,e,n,i,r,o){void 0===o&&(o=new p.Obj("0")),P(arguments.length,5,"createSoftMask","(PDFNet.SDFDoc, ArrayBuffer|TypedArray, number, number, number, PDFNet.Obj)",[[t,"SDFDoc"],[e,"ArrayBuffer"],[n,"number"],[i,"number"],[r,"number"],[o,"Object",p.Obj,"Obj"]]);var s=y(e,!1);return p.sendWithPromise("imageCreateSoftMask",{doc:t.id,buf:s,width:n,height:i,bpc:r,encoder_hints:o.id}).then(function(t){return _(p.Image,t)})},p.Image.createSoftMaskFromStream=function(t,e,n,i,r,o){return void 0===o&&(o=new p.Obj("0")),P(arguments.length,5,"createSoftMaskFromStream","(PDFNet.SDFDoc, PDFNet.FilterReader, number, number, number, PDFNet.Obj)",[[t,"SDFDoc"],[e,"Object",p.FilterReader,"FilterReader"],[n,"number"],[i,"number"],[r,"number"],[o,"Object",p.Obj,"Obj"]]),p.sendWithPromise("imageCreateSoftMaskFromStream",{doc:t.id,image_data:e.id,width:n,height:i,bpc:r,encoder_hints:o.id}).then(function(t){return _(p.Image,t)})},p.Image.createDirectFromMemory=function(t,e,n,i,r,o,s){P(arguments.length,7,"createDirectFromMemory","(PDFNet.SDFDoc, ArrayBuffer|TypedArray, number, number, number, PDFNet.ColorSpace, number)",[[t,"SDFDoc"],[e,"ArrayBuffer"],[n,"number"],[i,"number"],[r,"number"],[o,"Object",p.ColorSpace,"ColorSpace"],[s,"number"]]);var u=y(e,!1);return p.sendWithPromise("imageCreateDirectFromMemory",{doc:t.id,buf:u,width:n,height:i,bpc:r,color_space:o.id,input_format:s}).then(function(t){return _(p.Image,t)})},p.Image.createDirectFromStream=function(t,e,n,i,r,o,s){return P(arguments.length,7,"createDirectFromStream","(PDFNet.SDFDoc, PDFNet.FilterReader, number, number, number, PDFNet.ColorSpace, number)",[[t,"SDFDoc"],[e,"Object",p.FilterReader,"FilterReader"],[n,"number"],[i,"number"],[r,"number"],[o,"Object",p.ColorSpace,"ColorSpace"],[s,"number"]]),p.sendWithPromise("imageCreateDirectFromStream",{doc:t.id,image_data:e.id,width:n,height:i,bpc:r,color_space:o.id,input_format:s}).then(function(t){return _(p.Image,t)})},p.Image.createFromObj=function(t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,0,"createFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("imageCreateFromObj",{image_xobject:t.id}).then(function(t){return _(p.Image,t)})},p.Image.prototype.copy=function(){return p.sendWithPromise("Image.copy",{c:this.id}).then(function(t){return _(p.Image,t)})},p.Image.prototype.getSDFObj=function(){return p.sendWithPromise("Image.getSDFObj",{img:this.id}).then(function(t){return _(p.Obj,t)})},p.Image.prototype.isValid=function(){return p.sendWithPromise("Image.isValid",{img:this.id})},p.Image.prototype.getImageData=function(){return p.sendWithPromise("Image.getImageData",{img:this.id}).then(function(t){return _(p.Filter,t)})},p.Image.prototype.getImageDataSize=function(){return p.sendWithPromise("Image.getImageDataSize",{img:this.id})},p.Image.prototype.getImageColorSpace=function(){return p.sendWithPromise("Image.getImageColorSpace",{img:this.id}).then(function(t){return S(p.ColorSpace,t)})},p.Image.prototype.getImageWidth=function(){return p.sendWithPromise("Image.getImageWidth",{img:this.id})},p.Image.prototype.getImageHeight=function(){return p.sendWithPromise("Image.getImageHeight",{img:this.id})},p.Image.prototype.getDecodeArray=function(){return p.sendWithPromise("Image.getDecodeArray",{img:this.id}).then(function(t){return _(p.Obj,t)})},p.Image.prototype.getBitsPerComponent=function(){return p.sendWithPromise("Image.getBitsPerComponent",{img:this.id})},p.Image.prototype.getComponentNum=function(){return p.sendWithPromise("Image.getComponentNum",{img:this.id})},p.Image.prototype.isImageMask=function(){return p.sendWithPromise("Image.isImageMask",{img:this.id})},p.Image.prototype.isImageInterpolate=function(){return p.sendWithPromise("Image.isImageInterpolate",{img:this.id})},p.Image.prototype.getMask=function(){return p.sendWithPromise("Image.getMask",{img:this.id}).then(function(t){return _(p.Obj,t)})},p.Image.prototype.setMask=function(t){return P(arguments.length,1,"setMask","(PDFNet.Image)",[[t,"Object",p.Image,"Image"]]),p.sendWithPromise("Image.setMask",{img:this.id,image_mask:t.id})},p.Image.prototype.setMaskWithObj=function(t){return P(arguments.length,1,"setMaskWithObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("Image.setMaskWithObj",{img:this.id,mask:t.id})},p.Image.prototype.getSoftMask=function(){return p.sendWithPromise("Image.getSoftMask",{img:this.id}).then(function(t){return _(p.Obj,t)})},p.Image.prototype.setSoftMask=function(t){return P(arguments.length,1,"setSoftMask","(PDFNet.Image)",[[t,"Object",p.Image,"Image"]]),p.sendWithPromise("Image.setSoftMask",{img:this.id,soft_mask:t.id})},p.Image.prototype.getImageRenderingIntent=function(){return p.sendWithPromise("Image.getImageRenderingIntent",{img:this.id})},p.Image.prototype.exportFromStream=function(t){return P(arguments.length,1,"exportFromStream","(PDFNet.FilterWriter)",[[t,"Object",p.FilterWriter,"FilterWriter"]]),p.sendWithPromise("Image.exportFromStream",{img:this.id,writer:t.id})},p.Image.prototype.exportAsTiffFromStream=function(t){return P(arguments.length,1,"exportAsTiffFromStream","(PDFNet.FilterWriter)",[[t,"Object",p.FilterWriter,"FilterWriter"]]),p.sendWithPromise("Image.exportAsTiffFromStream",{img:this.id,writer:t.id})},p.Image.prototype.exportAsPngFromStream=function(t){return P(arguments.length,1,"exportAsPngFromStream","(PDFNet.FilterWriter)",[[t,"Object",p.FilterWriter,"FilterWriter"]]),p.sendWithPromise("Image.exportAsPngFromStream",{img:this.id,writer:t.id})},p.PageLabel.create=function(t,e,n,i){return void 0===n&&(n=""),void 0===i&&(i=1),P(arguments.length,2,"create","(PDFNet.SDFDoc, number, string, number)",[[t,"SDFDoc"],[e,"number"],[n,"string"],[i,"number"]]),p.sendWithPromise("pageLabelCreate",{doc:t.id,style:e,prefix:n,start_at:i}).then(function(t){return new p.PageLabel(t)})},p.PageLabel.createFromObj=function(t,e,n){return void 0===t&&(t=new p.Obj("0")),void 0===e&&(e=-1),void 0===n&&(n=-1),P(arguments.length,0,"createFromObj","(PDFNet.Obj, number, number)",[[t,"Object",p.Obj,"Obj"],[e,"number"],[n,"number"]]),p.sendWithPromise("pageLabelCreateFromObj",{l:t.id,first_page:e,last_page:n}).then(function(t){return new p.PageLabel(t)})},p.PageLabel.prototype.compare=function(t){P(arguments.length,1,"compare","(PDFNet.PageLabel)",[[t,"Structure",p.PageLabel,"PageLabel"]]),F("compare",this.yieldFunction),b("compare",[[t,0]]);var e=this;return this.yieldFunction="PageLabel.compare",p.sendWithPromise("PageLabel.compare",{l:this,d:t}).then(function(t){return e.yieldFunction=void 0,W(t.l,e),t.result})},p.PageLabel.prototype.isValid=function(){return F("isValid",this.yieldFunction),p.sendWithPromise("PageLabel.isValid",{l:this})},p.PageLabel.prototype.getLabelTitle=function(t){P(arguments.length,1,"getLabelTitle","(number)",[[t,"number"]]),F("getLabelTitle",this.yieldFunction);var e=this;return this.yieldFunction="PageLabel.getLabelTitle",p.sendWithPromise("PageLabel.getLabelTitle",{l:this,page_num:t}).then(function(t){return e.yieldFunction=void 0,W(t.l,e),t.result})},p.PageLabel.prototype.setStyle=function(t){P(arguments.length,1,"setStyle","(number)",[[t,"number"]]),F("setStyle",this.yieldFunction);var e=this;return this.yieldFunction="PageLabel.setStyle",p.sendWithPromise("PageLabel.setStyle",{l:this,style:t}).then(function(t){e.yieldFunction=void 0,W(t,e)})},p.PageLabel.prototype.getStyle=function(){return F("getStyle",this.yieldFunction),p.sendWithPromise("PageLabel.getStyle",{l:this})},p.PageLabel.prototype.getPrefix=function(){return F("getPrefix",this.yieldFunction),p.sendWithPromise("PageLabel.getPrefix",{l:this})},p.PageLabel.prototype.setPrefix=function(t){P(arguments.length,1,"setPrefix","(string)",[[t,"string"]]),F("setPrefix",this.yieldFunction);var e=this;return this.yieldFunction="PageLabel.setPrefix",p.sendWithPromise("PageLabel.setPrefix",{l:this,prefix:t}).then(function(t){e.yieldFunction=void 0,W(t,e)})},p.PageLabel.prototype.getStart=function(){return F("getStart",this.yieldFunction),p.sendWithPromise("PageLabel.getStart",{l:this})},p.PageLabel.prototype.setStart=function(t){P(arguments.length,1,"setStart","(number)",[[t,"number"]]),F("setStart",this.yieldFunction);var e=this;return this.yieldFunction="PageLabel.setStart",p.sendWithPromise("PageLabel.setStart",{l:this,start_at:t}).then(function(t){e.yieldFunction=void 0,W(t,e)})},p.PageLabel.prototype.getFirstPageNum=function(){F("getFirstPageNum",this.yieldFunction);var e=this;return this.yieldFunction="PageLabel.getFirstPageNum",p.sendWithPromise("PageLabel.getFirstPageNum",{l:this}).then(function(t){return e.yieldFunction=void 0,W(t.l,e),t.result})},p.PageLabel.prototype.getLastPageNum=function(){F("getLastPageNum",this.yieldFunction);var e=this;return this.yieldFunction="PageLabel.getLastPageNum",p.sendWithPromise("PageLabel.getLastPageNum",{l:this}).then(function(t){return e.yieldFunction=void 0,W(t.l,e),t.result})},p.PageLabel.prototype.getSDFObj=function(){return F("getSDFObj",this.yieldFunction),p.sendWithPromise("PageLabel.getSDFObj",{l:this}).then(function(t){return _(p.Obj,t)})},p.PageSet.create=function(){return p.sendWithPromise("pageSetCreate",{}).then(function(t){return S(p.PageSet,t)})},p.PageSet.createSinglePage=function(t){return P(arguments.length,1,"createSinglePage","(number)",[[t,"number"]]),p.sendWithPromise("pageSetCreateSinglePage",{one_page:t}).then(function(t){return S(p.PageSet,t)})},p.PageSet.createRange=function(t,e){return P(arguments.length,2,"createRange","(number, number)",[[t,"number"],[e,"number"]]),p.sendWithPromise("pageSetCreateRange",{range_start:t,range_end:e}).then(function(t){return S(p.PageSet,t)})},p.PageSet.createFilteredRange=function(t,e,n){return void 0===n&&(n=p.PageSet.Filter.e_all),P(arguments.length,2,"createFilteredRange","(number, number, number)",[[t,"number"],[e,"number"],[n,"number"]]),p.sendWithPromise("pageSetCreateFilteredRange",{range_start:t,range_end:e,filter:n}).then(function(t){return S(p.PageSet,t)})},p.PageSet.prototype.addPage=function(t){return P(arguments.length,1,"addPage","(number)",[[t,"number"]]),p.sendWithPromise("PageSet.addPage",{page_set:this.id,one_page:t})},p.PageSet.prototype.addRange=function(t,e,n){return void 0===n&&(n=p.PageSet.Filter.e_all),P(arguments.length,2,"addRange","(number, number, number)",[[t,"number"],[e,"number"],[n,"number"]]),p.sendWithPromise("PageSet.addRange",{page_set:this.id,range_start:t,range_end:e,filter:n})},p.PatternColor.create=function(t){return P(arguments.length,1,"create","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("patternColorCreate",{pattern:t.id}).then(function(t){return S(p.PatternColor,t)})},p.PatternColor.getTypeFromObj=function(t){return P(arguments.length,1,"getTypeFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("patternColorGetTypeFromObj",{pattern:t.id})},p.PatternColor.prototype.getType=function(){return p.sendWithPromise("PatternColor.getType",{pc:this.id})},p.PatternColor.prototype.getSDFObj=function(){return p.sendWithPromise("PatternColor.getSDFObj",{pc:this.id}).then(function(t){return _(p.Obj,t)})},p.PatternColor.prototype.getMatrix=function(){return p.sendWithPromise("PatternColor.getMatrix",{pc:this.id}).then(function(t){return new p.Matrix2D(t)})},p.PatternColor.prototype.getShading=function(){return p.sendWithPromise("PatternColor.getShading",{pc:this.id}).then(function(t){return S(p.Shading,t)})},p.PatternColor.prototype.getTilingType=function(){return p.sendWithPromise("PatternColor.getTilingType",{pc:this.id})},p.PatternColor.prototype.getBBox=function(){return p.sendWithPromise("PatternColor.getBBox",{pc:this.id}).then(function(t){return new p.Rect(t)})},p.PatternColor.prototype.getXStep=function(){return p.sendWithPromise("PatternColor.getXStep",{pc:this.id})},p.PatternColor.prototype.getYStep=function(){return p.sendWithPromise("PatternColor.getYStep",{pc:this.id})},p.GeometryCollection.prototype.snapToNearest=function(t,e,n){return P(arguments.length,3,"snapToNearest","(number, number, number)",[[t,"number"],[e,"number"],[n,"number"]]),p.sendWithPromise("GeometryCollection.snapToNearest",{self:this.id,x:t,y:e,mode:n})},p.GeometryCollection.prototype.snapToNearestPixel=function(t,e,n,i){return P(arguments.length,4,"snapToNearestPixel","(number, number, number, number)",[[t,"number"],[e,"number"],[n,"number"],[i,"number"]]),p.sendWithPromise("GeometryCollection.snapToNearestPixel",{self:this.id,x:t,y:e,dpi:n,mode:i})},p.DigestAlgorithm.calculateDigest=function(t,e){P(arguments.length,2,"calculateDigest","(number, ArrayBuffer|TypedArray)",[[t,"number"],[e,"ArrayBuffer"]]);var n=y(e,!1);return p.sendWithPromise("digestAlgorithmCalculateDigest",{in_algorithm:t,in_buffer:n}).then(function(t){return new Uint8Array(t)})},p.DigestAlgorithm.signDigestWithKeyfileBuffer=function(t,e,n,i){P(arguments.length,4,"signDigestWithKeyfileBuffer","(ArrayBuffer|TypedArray, number, ArrayBuffer|TypedArray, string)",[[t,"ArrayBuffer"],[e,"number"],[n,"ArrayBuffer"],[i,"string"]]);var r=y(t,!1),o=y(n,!1);return p.sendWithPromise("digestAlgorithmSignDigestWithKeyfileBuffer",{digest_buf:r,digest_algorithm_type:e,pkcs12_buf:o,pkcs12_password:i}).then(function(t){return new Uint8Array(t)})},p.ObjectIdentifier.createFromPredefined=function(t){return P(arguments.length,1,"createFromPredefined","(number)",[[t,"number"]]),p.sendWithPromise("objectIdentifierCreateFromPredefined",{in_oid_enum:t}).then(function(t){return S(p.ObjectIdentifier,t)})},p.ObjectIdentifier.createFromIntArray=function(t){return P(arguments.length,1,"createFromIntArray","(Array<number>)",[[t,"Array"]]),p.sendWithPromise("objectIdentifierCreateFromIntArray",{in_list:t}).then(function(t){return S(p.ObjectIdentifier,t)})},p.ObjectIdentifier.createFromDigestAlgorithm=function(t){return P(arguments.length,1,"createFromDigestAlgorithm","(number)",[[t,"number"]]),p.sendWithPromise("objectIdentifierCreateFromDigestAlgorithm",{in_algorithm:t}).then(function(t){return S(p.ObjectIdentifier,t)})},p.ObjectIdentifier.prototype.getRawValue=function(){return p.sendWithPromise("ObjectIdentifier.getRawValue",{self:this.id})},p.X501DistinguishedName.prototype.hasAttribute=function(t){return P(arguments.length,1,"hasAttribute","(PDFNet.ObjectIdentifier)",[[t,"Object",p.ObjectIdentifier,"ObjectIdentifier"]]),p.sendWithPromise("X501DistinguishedName.hasAttribute",{self:this.id,in_oid:t.id})},p.X501DistinguishedName.prototype.getStringValuesForAttribute=function(t){return P(arguments.length,1,"getStringValuesForAttribute","(PDFNet.ObjectIdentifier)",[[t,"Object",p.ObjectIdentifier,"ObjectIdentifier"]]),p.sendWithPromise("X501DistinguishedName.getStringValuesForAttribute",{self:this.id,in_oid:t.id})},p.X501DistinguishedName.prototype.getAllAttributesAndValues=function(){return p.sendWithPromise("X501DistinguishedName.getAllAttributesAndValues",{self:this.id}).then(function(t){return t.map(function(t){return S(p.X501AttributeTypeAndValue,t)})})},p.X509Certificate.createFromBuffer=function(t){P(arguments.length,1,"createFromBuffer","(ArrayBuffer|TypedArray)",[[t,"ArrayBuffer"]]);var e=y(t,!1);return p.sendWithPromise("x509CertificateCreateFromBuffer",{in_cert_buf:e}).then(function(t){return S(p.X509Certificate,t)})},p.X509Certificate.prototype.getIssuerField=function(){return p.sendWithPromise("X509Certificate.getIssuerField",{self:this.id}).then(function(t){return S(p.X501DistinguishedName,t)})},p.X509Certificate.prototype.getSubjectField=function(){return p.sendWithPromise("X509Certificate.getSubjectField",{self:this.id}).then(function(t){return S(p.X501DistinguishedName,t)})},p.X509Certificate.prototype.getNotBeforeEpochTime=function(){return p.sendWithPromise("X509Certificate.getNotBeforeEpochTime",{self:this.id})},p.X509Certificate.prototype.getNotAfterEpochTime=function(){return p.sendWithPromise("X509Certificate.getNotAfterEpochTime",{self:this.id})},p.X509Certificate.prototype.getRawX509VersionNumber=function(){return p.sendWithPromise("X509Certificate.getRawX509VersionNumber",{self:this.id})},p.X509Certificate.prototype.toString=function(){return p.sendWithPromise("X509Certificate.toString",{self:this.id})},p.X509Certificate.prototype.getFingerprint=function(t){return void 0===t&&(t=p.DigestAlgorithm.Type.e_SHA256),P(arguments.length,0,"getFingerprint","(number)",[[t,"number"]]),p.sendWithPromise("X509Certificate.getFingerprint",{self:this.id,in_digest_algorithm:t})},p.X509Certificate.prototype.getSerialNumber=function(){return p.sendWithPromise("X509Certificate.getSerialNumber",{self:this.id}).then(function(t){return new Uint8Array(t)})},p.X509Certificate.prototype.getExtensions=function(){return p.sendWithPromise("X509Certificate.getExtensions",{self:this.id}).then(function(t){return t.map(function(t){return S(p.X509Extension,t)})})},p.X509Certificate.prototype.getData=function(){return p.sendWithPromise("X509Certificate.getData",{self:this.id}).then(function(t){return new Uint8Array(t)})},p.TimestampingConfiguration.createFromURL=function(t){return P(arguments.length,1,"createFromURL","(string)",[[t,"string"]]),p.sendWithPromise("timestampingConfigurationCreateFromURL",{in_url:t}).then(function(t){return S(p.TimestampingConfiguration,t)})},p.TimestampingConfiguration.prototype.setTimestampAuthorityServerURL=function(t){return P(arguments.length,1,"setTimestampAuthorityServerURL","(string)",[[t,"string"]]),p.sendWithPromise("TimestampingConfiguration.setTimestampAuthorityServerURL",{self:this.id,in_url:t})},p.TimestampingConfiguration.prototype.setTimestampAuthorityServerUsername=function(t){return P(arguments.length,1,"setTimestampAuthorityServerUsername","(string)",[[t,"string"]]),p.sendWithPromise("TimestampingConfiguration.setTimestampAuthorityServerUsername",{self:this.id,in_username:t})},p.TimestampingConfiguration.prototype.setTimestampAuthorityServerPassword=function(t){return P(arguments.length,1,"setTimestampAuthorityServerPassword","(string)",[[t,"string"]]),p.sendWithPromise("TimestampingConfiguration.setTimestampAuthorityServerPassword",{self:this.id,in_password:t})},p.TimestampingConfiguration.prototype.setUseNonce=function(t){return P(arguments.length,1,"setUseNonce","(boolean)",[[t,"boolean"]]),p.sendWithPromise("TimestampingConfiguration.setUseNonce",{self:this.id,in_use_nonce:t})},p.TimestampingConfiguration.prototype.testConfiguration=function(t){return P(arguments.length,1,"testConfiguration","(PDFNet.VerificationOptions)",[[t,"Object",p.VerificationOptions,"VerificationOptions"]]),p.sendWithPromise("TimestampingConfiguration.testConfiguration",{self:this.id,in_opts:t.id}).then(function(t){return S(p.TimestampingResult,t)})},p.CMSSignatureOptions.create=function(){return p.sendWithPromise("cmsSignatureOptionsCreate",{}).then(function(t){return S(p.CMSSignatureOptions,t)})},p.CMSSignatureOptions.prototype.addTimestampToken=function(t){P(arguments.length,1,"addTimestampToken","(ArrayBuffer|TypedArray)",[[t,"ArrayBuffer"]]);var e=y(t,!1);return p.sendWithPromise("CMSSignatureOptions.addTimestampToken",{self:this.id,token_buf:e})},p.DigitalSignatureField.prototype.hasCryptographicSignature=function(){return F("hasCryptographicSignature",this.yieldFunction),p.sendWithPromise("DigitalSignatureField.hasCryptographicSignature",{self:this})},p.DigitalSignatureField.prototype.getSubFilter=function(){return F("getSubFilter",this.yieldFunction),p.sendWithPromise("DigitalSignatureField.getSubFilter",{self:this})},p.DigitalSignatureField.prototype.getSignatureName=function(){return F("getSignatureName",this.yieldFunction),p.sendWithPromise("DigitalSignatureField.getSignatureName",{self:this})},p.DigitalSignatureField.prototype.getLocation=function(){return F("getLocation",this.yieldFunction),p.sendWithPromise("DigitalSignatureField.getLocation",{self:this})},p.DigitalSignatureField.prototype.getReason=function(){return F("getReason",this.yieldFunction),p.sendWithPromise("DigitalSignatureField.getReason",{self:this})},p.DigitalSignatureField.prototype.getContactInfo=function(){return F("getContactInfo",this.yieldFunction),p.sendWithPromise("DigitalSignatureField.getContactInfo",{self:this})},p.DigitalSignatureField.prototype.getCertCount=function(){return F("getCertCount",this.yieldFunction),p.sendWithPromise("DigitalSignatureField.getCertCount",{self:this})},p.DigitalSignatureField.prototype.hasVisibleAppearance=function(){return F("hasVisibleAppearance",this.yieldFunction),p.sendWithPromise("DigitalSignatureField.hasVisibleAppearance",{self:this})},p.DigitalSignatureField.prototype.setContactInfo=function(t){P(arguments.length,1,"setContactInfo","(string)",[[t,"string"]]),F("setContactInfo",this.yieldFunction);var e=this;return this.yieldFunction="DigitalSignatureField.setContactInfo",p.sendWithPromise("DigitalSignatureField.setContactInfo",{self:this,in_contact_info:t}).then(function(t){e.yieldFunction=void 0,W(t,e)})},p.DigitalSignatureField.prototype.setLocation=function(t){P(arguments.length,1,"setLocation","(string)",[[t,"string"]]),F("setLocation",this.yieldFunction);var e=this;return this.yieldFunction="DigitalSignatureField.setLocation",p.sendWithPromise("DigitalSignatureField.setLocation",{self:this,in_location:t}).then(function(t){e.yieldFunction=void 0,W(t,e)})},p.DigitalSignatureField.prototype.setReason=function(t){P(arguments.length,1,"setReason","(string)",[[t,"string"]]),F("setReason",this.yieldFunction);var e=this;return this.yieldFunction="DigitalSignatureField.setReason",p.sendWithPromise("DigitalSignatureField.setReason",{self:this,in_reason:t}).then(function(t){e.yieldFunction=void 0,W(t,e)})},p.DigitalSignatureField.prototype.setDocumentPermissions=function(t){P(arguments.length,1,"setDocumentPermissions","(number)",[[t,"number"]]),F("setDocumentPermissions",this.yieldFunction);var e=this;return this.yieldFunction="DigitalSignatureField.setDocumentPermissions",p.sendWithPromise("DigitalSignatureField.setDocumentPermissions",{self:this,in_perms:t}).then(function(t){e.yieldFunction=void 0,W(t,e)})},p.DigitalSignatureField.prototype.signOnNextSave=function(t,e){P(arguments.length,2,"signOnNextSave","(string, string)",[[t,"string"],[e,"string"]]),F("signOnNextSave",this.yieldFunction);var n=this;return this.yieldFunction="DigitalSignatureField.signOnNextSave",p.sendWithPromise("DigitalSignatureField.signOnNextSave",{self:this,in_pkcs12_keyfile_path:t,in_password:e}).then(function(t){n.yieldFunction=void 0,W(t,n)})},p.DigitalSignatureField.prototype.certifyOnNextSave=function(t,e){P(arguments.length,2,"certifyOnNextSave","(string, string)",[[t,"string"],[e,"string"]]),F("certifyOnNextSave",this.yieldFunction);var n=this;return this.yieldFunction="DigitalSignatureField.certifyOnNextSave",p.sendWithPromise("DigitalSignatureField.certifyOnNextSave",{self:this,in_pkcs12_keyfile_path:t,in_password:e}).then(function(t){n.yieldFunction=void 0,W(t,n)})},p.DigitalSignatureField.prototype.isLockedByDigitalSignature=function(){return F("isLockedByDigitalSignature",this.yieldFunction),p.sendWithPromise("DigitalSignatureField.isLockedByDigitalSignature",{self:this})},p.DigitalSignatureField.prototype.getDocumentPermissions=function(){return F("getDocumentPermissions",this.yieldFunction),p.sendWithPromise("DigitalSignatureField.getDocumentPermissions",{self:this})},p.DigitalSignatureField.prototype.clearSignature=function(){F("clearSignature",this.yieldFunction);var e=this;return this.yieldFunction="DigitalSignatureField.clearSignature",p.sendWithPromise("DigitalSignatureField.clearSignature",{self:this}).then(function(t){e.yieldFunction=void 0,W(t,e)})},p.DigitalSignatureField.createFromField=function(t){return P(arguments.length,1,"createFromField","(PDFNet.Field)",[[t,"Structure",p.Field,"Field"]]),b("createFromField",[[t,0]]),p.sendWithPromise("digitalSignatureFieldCreateFromField",{in_field:t}).then(function(t){return new p.DigitalSignatureField(t)})},p.DigitalSignatureField.prototype.getSigningTime=function(){return F("getSigningTime",this.yieldFunction),p.sendWithPromise("DigitalSignatureField.getSigningTime",{self:this}).then(function(t){return new p.Date(t)})},p.DigitalSignatureField.prototype.getCert=function(t){return P(arguments.length,1,"getCert","(number)",[[t,"number"]]),F("getCert",this.yieldFunction),p.sendWithPromise("DigitalSignatureField.getCert",{self:this,in_index:t}).then(function(t){return new Uint8Array(t)})},p.DigitalSignatureField.prototype.setFieldPermissions=function(t,e){void 0===e&&(e=[]),P(arguments.length,1,"setFieldPermissions","(number, Array<string>)",[[t,"number"],[e,"Array"]]),F("setFieldPermissions",this.yieldFunction);var n=this;return this.yieldFunction="DigitalSignatureField.setFieldPermissions",p.sendWithPromise("DigitalSignatureField.setFieldPermissions",{self:this,in_action:t,in_field_names_list:e}).then(function(t){n.yieldFunction=void 0,W(t,n)})},p.DigitalSignatureField.prototype.signOnNextSaveFromBuffer=function(t,e){P(arguments.length,2,"signOnNextSaveFromBuffer","(ArrayBuffer|TypedArray, string)",[[t,"ArrayBuffer"],[e,"string"]]),F("signOnNextSaveFromBuffer",this.yieldFunction);var n=this,i=(this.yieldFunction="DigitalSignatureField.signOnNextSaveFromBuffer",y(t,!1));return p.sendWithPromise("DigitalSignatureField.signOnNextSaveFromBuffer",{self:this,in_pkcs12_buffer:i,in_password:e}).then(function(t){n.yieldFunction=void 0,W(t,n)})},p.DigitalSignatureField.prototype.signOnNextSaveWithCustomHandler=function(t){P(arguments.length,1,"signOnNextSaveWithCustomHandler","(number)",[[t,"number"]]),F("signOnNextSaveWithCustomHandler",this.yieldFunction);var e=this;return this.yieldFunction="DigitalSignatureField.signOnNextSaveWithCustomHandler",p.sendWithPromise("DigitalSignatureField.signOnNextSaveWithCustomHandler",{self:this,in_signature_handler_id:t}).then(function(t){e.yieldFunction=void 0,W(t,e)})},p.DigitalSignatureField.prototype.certifyOnNextSaveFromBuffer=function(t,e){P(arguments.length,2,"certifyOnNextSaveFromBuffer","(ArrayBuffer|TypedArray, string)",[[t,"ArrayBuffer"],[e,"string"]]),F("certifyOnNextSaveFromBuffer",this.yieldFunction);var n=this,i=(this.yieldFunction="DigitalSignatureField.certifyOnNextSaveFromBuffer",y(t,!1));return p.sendWithPromise("DigitalSignatureField.certifyOnNextSaveFromBuffer",{self:this,in_pkcs12_buffer:i,in_password:e}).then(function(t){n.yieldFunction=void 0,W(t,n)})},p.DigitalSignatureField.prototype.certifyOnNextSaveWithCustomHandler=function(t){P(arguments.length,1,"certifyOnNextSaveWithCustomHandler","(number)",[[t,"number"]]),F("certifyOnNextSaveWithCustomHandler",this.yieldFunction);var e=this;return this.yieldFunction="DigitalSignatureField.certifyOnNextSaveWithCustomHandler",p.sendWithPromise("DigitalSignatureField.certifyOnNextSaveWithCustomHandler",{self:this,in_signature_handler_id:t}).then(function(t){e.yieldFunction=void 0,W(t,e)})},p.DigitalSignatureField.prototype.getSDFObj=function(){return F("getSDFObj",this.yieldFunction),p.sendWithPromise("DigitalSignatureField.getSDFObj",{self:this}).then(function(t){return _(p.Obj,t)})},p.DigitalSignatureField.prototype.getLockedFields=function(){return F("getLockedFields",this.yieldFunction),p.sendWithPromise("DigitalSignatureField.getLockedFields",{self:this})},p.DigitalSignatureField.prototype.verify=function(t){return P(arguments.length,1,"verify","(PDFNet.VerificationOptions)",[[t,"Object",p.VerificationOptions,"VerificationOptions"]]),F("verify",this.yieldFunction),p.sendWithPromise("DigitalSignatureField.verify",{self:this,in_opts:t.id}).then(function(t){return S(p.VerificationResult,t)})},p.DigitalSignatureField.prototype.isCertification=function(){return F("isCertification",this.yieldFunction),p.sendWithPromise("DigitalSignatureField.isCertification",{self:this})},p.DigitalSignatureField.prototype.getSignerCertFromCMS=function(){return F("getSignerCertFromCMS",this.yieldFunction),p.sendWithPromise("DigitalSignatureField.getSignerCertFromCMS",{self:this}).then(function(t){return S(p.X509Certificate,t)})},p.DigitalSignatureField.prototype.getByteRanges=function(){return F("getByteRanges",this.yieldFunction),p.sendWithPromise("DigitalSignatureField.getByteRanges",{self:this}).then(function(t){return t.map(function(t){return _(p.ByteRange,t)})})},p.DigitalSignatureField.prototype.enableLTVOfflineVerification=function(t){return P(arguments.length,1,"enableLTVOfflineVerification","(PDFNet.VerificationResult)",[[t,"Object",p.VerificationResult,"VerificationResult"]]),F("enableLTVOfflineVerification",this.yieldFunction),p.sendWithPromise("DigitalSignatureField.enableLTVOfflineVerification",{self:this,in_verification_result:t.id})},p.DigitalSignatureField.prototype.timestampOnNextSave=function(t,e){return P(arguments.length,2,"timestampOnNextSave","(PDFNet.TimestampingConfiguration, PDFNet.VerificationOptions)",[[t,"Object",p.TimestampingConfiguration,"TimestampingConfiguration"],[e,"Object",p.VerificationOptions,"VerificationOptions"]]),F("timestampOnNextSave",this.yieldFunction),p.sendWithPromise("DigitalSignatureField.timestampOnNextSave",{self:this,in_timestamping_config:t.id,in_timestamp_response_verification_options:e.id})},p.DigitalSignatureField.prototype.generateContentsWithEmbeddedTimestamp=function(t,e){return P(arguments.length,2,"generateContentsWithEmbeddedTimestamp","(PDFNet.TimestampingConfiguration, PDFNet.VerificationOptions)",[[t,"Object",p.TimestampingConfiguration,"TimestampingConfiguration"],[e,"Object",p.VerificationOptions,"VerificationOptions"]]),F("generateContentsWithEmbeddedTimestamp",this.yieldFunction),p.sendWithPromise("DigitalSignatureField.generateContentsWithEmbeddedTimestamp",{self:this,in_timestamping_config:t.id,in_timestamp_response_verification_options:e.id}).then(function(t){return S(p.TimestampingResult,t)})},p.DigitalSignatureField.prototype.useSubFilter=function(t,e){void 0===e&&(e=!0),P(arguments.length,1,"useSubFilter","(number, boolean)",[[t,"number"],[e,"boolean"]]),F("useSubFilter",this.yieldFunction);var n=this;return this.yieldFunction="DigitalSignatureField.useSubFilter",p.sendWithPromise("DigitalSignatureField.useSubFilter",{self:this,in_subfilter_type:t,in_make_mandatory:e}).then(function(t){n.yieldFunction=void 0,W(t,n)})},p.DigitalSignatureField.prototype.calculateDigest=function(t){void 0===t&&(t=p.DigestAlgorithm.Type.e_SHA256),P(arguments.length,0,"calculateDigest","(number)",[[t,"number"]]),F("calculateDigest",this.yieldFunction);var e=this;return this.yieldFunction="DigitalSignatureField.calculateDigest",p.sendWithPromise("DigitalSignatureField.calculateDigest",{self:this,in_digest_algorithm_type:t}).then(function(t){return e.yieldFunction=void 0,t.result=new Uint8Array(t.result),W(t.self,e),t.result})},p.DigitalSignatureField.prototype.setPreferredDigestAlgorithm=function(t,e){void 0===e&&(e=!0),P(arguments.length,1,"setPreferredDigestAlgorithm","(number, boolean)",[[t,"number"],[e,"boolean"]]),F("setPreferredDigestAlgorithm",this.yieldFunction);var n=this;return this.yieldFunction="DigitalSignatureField.setPreferredDigestAlgorithm",p.sendWithPromise("DigitalSignatureField.setPreferredDigestAlgorithm",{self:this,in_digest_algorithm_type:t,in_make_mandatory:e}).then(function(t){n.yieldFunction=void 0,W(t,n)})},p.DigitalSignatureField.prototype.createSigDictForCustomCertification=function(t,e,n){P(arguments.length,3,"createSigDictForCustomCertification","(string, number, number)",[[t,"string"],[e,"number"],[n,"number"]]),F("createSigDictForCustomCertification",this.yieldFunction);var i=this;return this.yieldFunction="DigitalSignatureField.createSigDictForCustomCertification",p.sendWithPromise("DigitalSignatureField.createSigDictForCustomCertification",{self:this,in_filter_name:t,in_subfilter_type:e,in_contents_size_to_reserve:n}).then(function(t){i.yieldFunction=void 0,W(t,i)})},p.DigitalSignatureField.prototype.createSigDictForCustomSigning=function(t,e,n){P(arguments.length,3,"createSigDictForCustomSigning","(string, number, number)",[[t,"string"],[e,"number"],[n,"number"]]),F("createSigDictForCustomSigning",this.yieldFunction);var i=this;return this.yieldFunction="DigitalSignatureField.createSigDictForCustomSigning",p.sendWithPromise("DigitalSignatureField.createSigDictForCustomSigning",{self:this,in_filter_name:t,in_subfilter_type:e,in_contents_size_to_reserve:n}).then(function(t){i.yieldFunction=void 0,W(t,i)})},p.DigitalSignatureField.prototype.setSigDictTimeOfSigning=function(t){P(arguments.length,1,"setSigDictTimeOfSigning","(PDFNet.Date)",[[t,"Structure",p.Date,"Date"]]),F("setSigDictTimeOfSigning",this.yieldFunction),b("setSigDictTimeOfSigning",[[t,0]]);var e=this;return this.yieldFunction="DigitalSignatureField.setSigDictTimeOfSigning",p.sendWithPromise("DigitalSignatureField.setSigDictTimeOfSigning",{self:this,in_date:t}).then(function(t){e.yieldFunction=void 0,W(t,e)})},p.DigitalSignatureField.signDigestBuffer=function(t,e,n,i,r){P(arguments.length,5,"signDigestBuffer","(ArrayBuffer|TypedArray, ArrayBuffer|TypedArray, string, boolean, number)",[[t,"ArrayBuffer"],[e,"ArrayBuffer"],[n,"string"],[i,"boolean"],[r,"number"]]);var o=y(t,!1),s=y(e,!1);return p.sendWithPromise("digitalSignatureFieldSignDigestBuffer",{in_digest_buf:o,in_pkcs12_buffer:s,in_keyfile_password:n,in_pades_mode:i,in_digest_algorithm_type:r}).then(function(t){return new Uint8Array(t)})},p.DigitalSignatureField.generateESSSigningCertPAdESAttribute=function(t,e){return P(arguments.length,2,"generateESSSigningCertPAdESAttribute","(PDFNet.X509Certificate, number)",[[t,"Object",p.X509Certificate,"X509Certificate"],[e,"number"]]),p.sendWithPromise("digitalSignatureFieldGenerateESSSigningCertPAdESAttribute",{in_signer_cert:t.id,in_digest_algorithm_type:e}).then(function(t){return new Uint8Array(t)})},p.DigitalSignatureField.generateCMSSignedAttributes=function(t,e){void 0===e&&(e=new ArrayBuffer(0)),P(arguments.length,1,"generateCMSSignedAttributes","(ArrayBuffer|TypedArray, ArrayBuffer|TypedArray)",[[t,"ArrayBuffer"],[e,"ArrayBuffer"]]);var n=y(t,!1),i=y(e,!1);return p.sendWithPromise("digitalSignatureFieldGenerateCMSSignedAttributes",{in_digest_buf:n,in_custom_signedattributes_buf:i}).then(function(t){return new Uint8Array(t)})},p.DigitalSignatureField.generateCMSSignature=function(t,e,n,i,r,o){P(arguments.length,6,"generateCMSSignature","(PDFNet.X509Certificate, Array<Core.PDFNet.X509Certificate>, PDFNet.ObjectIdentifier, PDFNet.ObjectIdentifier, ArrayBuffer|TypedArray, ArrayBuffer|TypedArray)",[[t,"Object",p.X509Certificate,"X509Certificate"],[e,"Array"],[n,"Object",p.ObjectIdentifier,"ObjectIdentifier"],[i,"Object",p.ObjectIdentifier,"ObjectIdentifier"],[r,"ArrayBuffer"],[o,"ArrayBuffer"]]);var s=y(r,!1),u=y(o,!1);return e=Array.from(e,function(t){return t.id}),p.sendWithPromise("digitalSignatureFieldGenerateCMSSignature",{in_signer_cert:t.id,in_chain_certs_list:e,in_digest_algorithm_oid:n.id,in_signature_algorithm_oid:i.id,in_signature_value_buf:s,in_signedattributes_buf:u}).then(function(t){return new Uint8Array(t)})},p.DigitalSignatureField.generateCMSSignatureWithAlgoId=function(t,e,n,i,r,o,s){void 0===s&&(s=new p.CMSSignatureOptions("0")),P(arguments.length,6,"generateCMSSignatureWithAlgoId","(PDFNet.X509Certificate, Array<Core.PDFNet.X509Certificate>, PDFNet.AlgorithmIdentifier, PDFNet.AlgorithmIdentifier, ArrayBuffer|TypedArray, ArrayBuffer|TypedArray, PDFNet.CMSSignatureOptions)",[[t,"Object",p.X509Certificate,"X509Certificate"],[e,"Array"],[n,"Object",p.AlgorithmIdentifier,"AlgorithmIdentifier"],[i,"Object",p.AlgorithmIdentifier,"AlgorithmIdentifier"],[r,"ArrayBuffer"],[o,"ArrayBuffer"],[s,"Object",p.CMSSignatureOptions,"CMSSignatureOptions"]]);var u=y(r,!1),a=y(o,!1);return e=Array.from(e,function(t){return t.id}),p.sendWithPromise("digitalSignatureFieldGenerateCMSSignatureWithAlgoId",{signer_cert:t.id,chain_certs_list:e,digest_algorithm_id:n.id,signature_algorithm_id:i.id,signature_value_buf:u,signedattributes_buf:a,cms_options:s.id}).then(function(t){return new Uint8Array(t)})},p.PDFDoc.prototype.getTriggerAction=function(t){return P(arguments.length,1,"getTriggerAction","(number)",[[t,"number"]]),p.sendWithPromise("PDFDoc.getTriggerAction",{doc:this.id,trigger:t}).then(function(t){return _(p.Obj,t)})},p.PDFDoc.prototype.isXFA=function(){return p.sendWithPromise("PDFDoc.isXFA",{doc:this.id})},p.PDFDoc.create=function(){return p.sendWithPromise("pdfDocCreate",{}).then(function(t){return S(p.PDFDoc,t)})},p.PDFDoc.createFromFilter=function(t){return P(arguments.length,1,"createFromFilter","(PDFNet.Filter)",[[t,"Object",p.Filter,"Filter"]]),0!=t.id&&A(t.id),p.sendWithPromise("pdfDocCreateFromFilter",{no_own_stream:t.id}).then(function(t){return S(p.PDFDoc,t)})},p.PDFDoc.createFromBuffer=function(t){P(arguments.length,1,"createFromBuffer","(ArrayBuffer|TypedArray)",[[t,"ArrayBuffer"]]);var e=y(t,!1);return p.sendWithPromise("pdfDocCreateFromBuffer",{buf:e}).then(function(t){return S(p.PDFDoc,t)})},p.PDFDoc.createFromLayoutEls=function(t){P(arguments.length,1,"createFromLayoutEls","(ArrayBuffer|TypedArray)",[[t,"ArrayBuffer"]]);var e=y(t,!1);return p.sendWithPromise("pdfDocCreateFromLayoutEls",{buf:e}).then(function(t){return S(p.PDFDoc,t)})},p.PDFDoc.prototype.createShallowCopy=function(){return p.sendWithPromise("PDFDoc.createShallowCopy",{source:this.id}).then(function(t){return S(p.PDFDoc,t)})},p.PDFDoc.prototype.isEncrypted=function(){return p.sendWithPromise("PDFDoc.isEncrypted",{doc:this.id})},p.PDFDoc.prototype.initStdSecurityHandlerUString=function(t){return P(arguments.length,1,"initStdSecurityHandlerUString","(string)",[[t,"string"]]),p.sendWithPromise("PDFDoc.initStdSecurityHandlerUString",{doc:this.id,password:t})},p.PDFDoc.prototype.initStdSecurityHandlerBuffer=function(t){P(arguments.length,1,"initStdSecurityHandlerBuffer","(ArrayBuffer|TypedArray)",[[t,"ArrayBuffer"]]);var e=y(t,!1);return p.sendWithPromise("PDFDoc.initStdSecurityHandlerBuffer",{doc:this.id,password_buf:e})},p.PDFDoc.prototype.getSecurityHandler=function(){return p.sendWithPromise("PDFDoc.getSecurityHandler",{doc:this.id}).then(function(t){return _(p.SecurityHandler,t)})},p.PDFDoc.prototype.setSecurityHandler=function(t){return P(arguments.length,1,"setSecurityHandler","(PDFNet.SecurityHandler)",[[t,"Object",p.SecurityHandler,"SecurityHandler"]]),0!=t.id&&A(t.id),p.sendWithPromise("PDFDoc.setSecurityHandler",{doc:this.id,no_own_handler:t.id})},p.PDFDoc.prototype.removeSecurity=function(){return p.sendWithPromise("PDFDoc.removeSecurity",{doc:this.id})},p.PDFDoc.prototype.getDocInfo=function(){return p.sendWithPromise("PDFDoc.getDocInfo",{doc:this.id}).then(function(t){return _(p.PDFDocInfo,t)})},p.PDFDoc.prototype.getViewPrefs=function(){return p.sendWithPromise("PDFDoc.getViewPrefs",{doc:this.id}).then(function(t){return _(p.PDFDocViewPrefs,t)})},p.PDFDoc.prototype.isModified=function(){return p.sendWithPromise("PDFDoc.isModified",{doc:this.id})},p.PDFDoc.prototype.hasRepairedXRef=function(){return p.sendWithPromise("PDFDoc.hasRepairedXRef",{doc:this.id})},p.PDFDoc.prototype.isLinearized=function(){return p.sendWithPromise("PDFDoc.isLinearized",{doc:this.id})},p.PDFDoc.prototype.saveMemoryBuffer=function(t){return P(arguments.length,1,"saveMemoryBuffer","(number)",[[t,"number"]]),p.sendWithPromise("PDFDoc.saveMemoryBuffer",{doc:this.id,flags:t}).then(function(t){return new Uint8Array(t)})},p.PDFDoc.prototype.saveStream=function(t,e){return P(arguments.length,2,"saveStream","(PDFNet.Filter, number)",[[t,"Object",p.Filter,"Filter"],[e,"number"]]),p.sendWithPromise("PDFDoc.saveStream",{doc:this.id,stream:t.id,flags:e})},p.PDFDoc.prototype.saveCustomSignatureBuffer=function(t,e){P(arguments.length,2,"saveCustomSignatureBuffer","(ArrayBuffer|TypedArray, PDFNet.DigitalSignatureField)",[[t,"ArrayBuffer"],[e,"Structure",p.DigitalSignatureField,"DigitalSignatureField"]]),b("saveCustomSignatureBuffer",[[e,1]]);var n=y(t,!1);return p.sendWithPromise("PDFDoc.saveCustomSignatureBuffer",{doc:this.id,in_signature_buf:n,in_field:e}).then(function(t){return new Uint8Array(t)})},p.PDFDoc.prototype.saveCustomSignatureStream=function(t,e){P(arguments.length,2,"saveCustomSignatureStream","(ArrayBuffer|TypedArray, PDFNet.DigitalSignatureField)",[[t,"ArrayBuffer"],[e,"Structure",p.DigitalSignatureField,"DigitalSignatureField"]]),b("saveCustomSignatureStream",[[e,1]]);var n=y(t,!1);return p.sendWithPromise("PDFDoc.saveCustomSignatureStream",{doc:this.id,in_signature_buf:n,in_field:e}).then(function(t){return S(p.Filter,t)})},p.PDFDoc.prototype.getPageIterator=function(t){return void 0===t&&(t=1),P(arguments.length,0,"getPageIterator","(number)",[[t,"number"]]),p.sendWithPromise("PDFDoc.getPageIterator",{doc:this.id,page_number:t}).then(function(t){return S(p.Iterator,t,"Page")})},p.PDFDoc.prototype.getPage=function(t){return P(arguments.length,1,"getPage","(number)",[[t,"number"]]),p.sendWithPromise("PDFDoc.getPage",{doc:this.id,page_number:t}).then(function(t){return _(p.Page,t)})},p.PDFDoc.prototype.pageRemove=function(t){return P(arguments.length,1,"pageRemove","(PDFNet.Iterator)",[[t,"Object",p.Iterator,"Iterator"]]),p.sendWithPromise("PDFDoc.pageRemove",{doc:this.id,page_itr:t.id})},p.PDFDoc.prototype.pageInsert=function(t,e){return P(arguments.length,2,"pageInsert","(PDFNet.Iterator, PDFNet.Page)",[[t,"Object",p.Iterator,"Iterator"],[e,"Object",p.Page,"Page"]]),p.sendWithPromise("PDFDoc.pageInsert",{doc:this.id,where:t.id,page:e.id})},p.PDFDoc.prototype.insertPages=function(t,e,n,i,r){return P(arguments.length,5,"insertPages","(number, PDFNet.PDFDoc, number, number, number)",[[t,"number"],[e,"PDFDoc"],[n,"number"],[i,"number"],[r,"number"]]),p.sendWithPromise("PDFDoc.insertPages",{dest_doc:this.id,insert_before_page_number:t,src_doc:e.id,start_page:n,end_page:i,flag:r})},p.PDFDoc.prototype.insertPageSet=function(t,e,n,i){return P(arguments.length,4,"insertPageSet","(number, PDFNet.PDFDoc, PDFNet.PageSet, number)",[[t,"number"],[e,"PDFDoc"],[n,"Object",p.PageSet,"PageSet"],[i,"number"]]),p.sendWithPromise("PDFDoc.insertPageSet",{dest_doc:this.id,insert_before_page_number:t,src_doc:e.id,source_page_set:n.id,flag:i})},p.PDFDoc.prototype.movePages=function(t,e,n,i,r){return P(arguments.length,5,"movePages","(number, PDFNet.PDFDoc, number, number, number)",[[t,"number"],[e,"PDFDoc"],[n,"number"],[i,"number"],[r,"number"]]),p.sendWithPromise("PDFDoc.movePages",{dest_doc:this.id,move_before_page_number:t,src_doc:e.id,start_page:n,end_page:i,flag:r})},p.PDFDoc.prototype.movePageSet=function(t,e,n,i){return P(arguments.length,4,"movePageSet","(number, PDFNet.PDFDoc, PDFNet.PageSet, number)",[[t,"number"],[e,"PDFDoc"],[n,"Object",p.PageSet,"PageSet"],[i,"number"]]),p.sendWithPromise("PDFDoc.movePageSet",{dest_doc:this.id,move_before_page_number:t,src_doc:e.id,source_page_set:n.id,flag:i})},p.PDFDoc.prototype.pagePushFront=function(t){return P(arguments.length,1,"pagePushFront","(PDFNet.Page)",[[t,"Object",p.Page,"Page"]]),p.sendWithPromise("PDFDoc.pagePushFront",{doc:this.id,page:t.id})},p.PDFDoc.prototype.pagePushBack=function(t){return P(arguments.length,1,"pagePushBack","(PDFNet.Page)",[[t,"Object",p.Page,"Page"]]),p.sendWithPromise("PDFDoc.pagePushBack",{doc:this.id,page:t.id})},p.PDFDoc.prototype.pageCreate=function(t){return void 0===t&&(t=new p.Rect(0,0,612,792)),P(arguments.length,0,"pageCreate","(PDFNet.Rect)",[[t,"Structure",p.Rect,"Rect"]]),b("pageCreate",[[t,0]]),p.sendWithPromise("PDFDoc.pageCreate",{doc:this.id,media_box:t}).then(function(t){return _(p.Page,t)})},p.PDFDoc.prototype.appendTextDiffPage=function(t,e){return P(arguments.length,2,"appendTextDiffPage","(PDFNet.Page, PDFNet.Page)",[[t,"Object",p.Page,"Page"],[e,"Object",p.Page,"Page"]]),p.sendWithPromise("PDFDoc.appendTextDiffPage",{doc:this.id,page1:t.id,page2:e.id})},p.PDFDoc.prototype.appendTextDiffDoc=function(t,e,n){return void 0===n&&(n=null),P(arguments.length,2,"appendTextDiffDoc","(PDFNet.PDFDoc, PDFNet.PDFDoc, PDFNet.OptionBase)",[[t,"PDFDoc"],[e,"PDFDoc"],[n,"OptionBase"]]),b("appendTextDiffDoc",[[n,2]]),n=n?n.getJsonString():"{}",p.sendWithPromise("PDFDoc.appendTextDiffDoc",{doc:this.id,doc1:t.id,doc2:e.id,options:n})},p.PDFDoc.highlightTextDiff=function(t,e,n){return void 0===n&&(n=null),P(arguments.length,2,"highlightTextDiff","(PDFNet.PDFDoc, PDFNet.PDFDoc, PDFNet.OptionBase)",[[t,"PDFDoc"],[e,"PDFDoc"],[n,"OptionBase"]]),b("highlightTextDiff",[[n,2]]),n=n?n.getJsonString():"{}",p.sendWithPromise("pdfDocHighlightTextDiff",{doc1:t.id,doc2:e.id,options:n})},p.PDFDoc.prototype.getFirstBookmark=function(){return p.sendWithPromise("PDFDoc.getFirstBookmark",{doc:this.id}).then(function(t){return _(p.Bookmark,t)})},p.PDFDoc.prototype.addRootBookmark=function(t){return P(arguments.length,1,"addRootBookmark","(PDFNet.Bookmark)",[[t,"Object",p.Bookmark,"Bookmark"]]),p.sendWithPromise("PDFDoc.addRootBookmark",{doc:this.id,root_bookmark:t.id})},p.PDFDoc.prototype.getTrailer=function(){return p.sendWithPromise("PDFDoc.getTrailer",{doc:this.id}).then(function(t){return _(p.Obj,t)})},p.PDFDoc.prototype.getRoot=function(){return p.sendWithPromise("PDFDoc.getRoot",{doc:this.id}).then(function(t){return _(p.Obj,t)})},p.PDFDoc.prototype.jsContextInitialize=function(){return p.sendWithPromise("PDFDoc.jsContextInitialize",{doc:this.id})},p.PDFDoc.prototype.getPages=function(){return p.sendWithPromise("PDFDoc.getPages",{doc:this.id}).then(function(t){return _(p.Obj,t)})},p.PDFDoc.prototype.getPageCount=function(){return p.sendWithPromise("PDFDoc.getPageCount",{doc:this.id})},p.PDFDoc.prototype.getDownloadedByteCount=function(){return p.sendWithPromise("PDFDoc.getDownloadedByteCount",{doc:this.id})},p.PDFDoc.prototype.getTotalRemoteByteCount=function(){return p.sendWithPromise("PDFDoc.getTotalRemoteByteCount",{doc:this.id})},p.PDFDoc.prototype.getFieldIteratorBegin=function(){return p.sendWithPromise("PDFDoc.getFieldIteratorBegin",{doc:this.id}).then(function(t){return S(p.Iterator,t,"Field")})},p.PDFDoc.prototype.getFieldIterator=function(t){return P(arguments.length,1,"getFieldIterator","(string)",[[t,"string"]]),p.sendWithPromise("PDFDoc.getFieldIterator",{doc:this.id,field_name:t}).then(function(t){return S(p.Iterator,t,"Field")})},p.PDFDoc.prototype.getField=function(t){return P(arguments.length,1,"getField","(string)",[[t,"string"]]),p.sendWithPromise("PDFDoc.getField",{doc:this.id,field_name:t}).then(function(t){return new p.Field(t)})},p.PDFDoc.prototype.fieldCreate=function(t,e,n,i){return void 0===n&&(n=new p.Obj("0")),void 0===i&&(i=new p.Obj("0")),P(arguments.length,2,"fieldCreate","(string, number, PDFNet.Obj, PDFNet.Obj)",[[t,"string"],[e,"number"],[n,"Object",p.Obj,"Obj"],[i,"Object",p.Obj,"Obj"]]),p.sendWithPromise("PDFDoc.fieldCreate",{doc:this.id,field_name:t,type:e,field_value:n.id,def_field_value:i.id}).then(function(t){return new p.Field(t)})},p.PDFDoc.prototype.fieldCreateFromStrings=function(t,e,n,i){return void 0===i&&(i=""),P(arguments.length,3,"fieldCreateFromStrings","(string, number, string, string)",[[t,"string"],[e,"number"],[n,"string"],[i,"string"]]),p.sendWithPromise("PDFDoc.fieldCreateFromStrings",{doc:this.id,field_name:t,type:e,field_value:n,def_field_value:i}).then(function(t){return new p.Field(t)})},p.PDFDoc.prototype.refreshFieldAppearances=function(){return p.sendWithPromise("PDFDoc.refreshFieldAppearances",{doc:this.id})},p.PDFDoc.prototype.refreshAnnotAppearances=function(t){return void 0===t&&(t=null),P(arguments.length,0,"refreshAnnotAppearances","(PDFNet.OptionBase)",[[t,"OptionBase"]]),b("refreshAnnotAppearances",[[t,0]]),t=t?t.getJsonString():"{}",p.sendWithPromise("PDFDoc.refreshAnnotAppearances",{doc:this.id,options:t})},p.PDFDoc.prototype.flattenAnnotations=function(t){return void 0===t&&(t=!1),P(arguments.length,0,"flattenAnnotations","(boolean)",[[t,"boolean"]]),p.sendWithPromise("PDFDoc.flattenAnnotations",{doc:this.id,forms_only:t})},p.PDFDoc.prototype.getAcroForm=function(){return p.sendWithPromise("PDFDoc.getAcroForm",{doc:this.id}).then(function(t){return _(p.Obj,t)})},p.PDFDoc.prototype.fdfExtract=function(t){return void 0===t&&(t=p.PDFDoc.ExtractFlag.e_forms_only),P(arguments.length,0,"fdfExtract","(number)",[[t,"number"]]),p.sendWithPromise("PDFDoc.fdfExtract",{doc:this.id,flag:t}).then(function(t){return S(p.FDFDoc,t)})},p.PDFDoc.prototype.fdfExtractPageSet=function(t,e){return void 0===e&&(e=p.PDFDoc.ExtractFlag.e_forms_only),P(arguments.length,1,"fdfExtractPageSet","(PDFNet.PageSet, number)",[[t,"Object",p.PageSet,"PageSet"],[e,"number"]]),p.sendWithPromise("PDFDoc.fdfExtractPageSet",{doc:this.id,pages_to_extract:t.id,flag:e}).then(function(t){return S(p.FDFDoc,t)})},p.PDFDoc.prototype.fdfMerge=function(t){return P(arguments.length,1,"fdfMerge","(PDFNet.FDFDoc)",[[t,"FDFDoc"]]),p.sendWithPromise("PDFDoc.fdfMerge",{doc:this.id,fdf_doc:t.id})},p.PDFDoc.prototype.fdfUpdate=function(t){return P(arguments.length,1,"fdfUpdate","(PDFNet.FDFDoc)",[[t,"FDFDoc"]]),p.sendWithPromise("PDFDoc.fdfUpdate",{doc:this.id,fdf_doc:t.id})},p.PDFDoc.prototype.getOpenAction=function(){return p.sendWithPromise("PDFDoc.getOpenAction",{doc:this.id}).then(function(t){return _(p.Action,t)})},p.PDFDoc.prototype.setOpenAction=function(t){return P(arguments.length,1,"setOpenAction","(PDFNet.Action)",[[t,"Object",p.Action,"Action"]]),p.sendWithPromise("PDFDoc.setOpenAction",{doc:this.id,action:t.id})},p.PDFDoc.prototype.addFileAttachment=function(t,e){return P(arguments.length,2,"addFileAttachment","(string, PDFNet.FileSpec)",[[t,"string"],[e,"Object",p.FileSpec,"FileSpec"]]),p.sendWithPromise("PDFDoc.addFileAttachment",{doc:this.id,file_key:t,embedded_file:e.id})},p.PDFDoc.prototype.getPageLabel=function(t){return P(arguments.length,1,"getPageLabel","(number)",[[t,"number"]]),p.sendWithPromise("PDFDoc.getPageLabel",{doc:this.id,page_num:t}).then(function(t){return new p.PageLabel(t)})},p.PDFDoc.prototype.setPageLabel=function(t,e){return P(arguments.length,2,"setPageLabel","(number, PDFNet.PageLabel)",[[t,"number"],[e,"Structure",p.PageLabel,"PageLabel"]]),b("setPageLabel",[[e,1]]),p.sendWithPromise("PDFDoc.setPageLabel",{doc:this.id,page_num:t,label:e})},p.PDFDoc.prototype.removePageLabel=function(t){return P(arguments.length,1,"removePageLabel","(number)",[[t,"number"]]),p.sendWithPromise("PDFDoc.removePageLabel",{doc:this.id,page_num:t})},p.PDFDoc.prototype.getStructTree=function(){return p.sendWithPromise("PDFDoc.getStructTree",{doc:this.id}).then(function(t){return _(p.STree,t)})},p.PDFDoc.prototype.hasOC=function(){return p.sendWithPromise("PDFDoc.hasOC",{doc:this.id})},p.PDFDoc.prototype.getOCGs=function(){return p.sendWithPromise("PDFDoc.getOCGs",{doc:this.id}).then(function(t){return _(p.Obj,t)})},p.PDFDoc.prototype.getOCGConfig=function(){return p.sendWithPromise("PDFDoc.getOCGConfig",{doc:this.id}).then(function(t){return _(p.OCGConfig,t)})},p.PDFDoc.prototype.createIndirectName=function(t){return P(arguments.length,1,"createIndirectName","(string)",[[t,"string"]]),p.sendWithPromise("PDFDoc.createIndirectName",{doc:this.id,name:t}).then(function(t){return _(p.Obj,t)})},p.PDFDoc.prototype.createIndirectArray=function(){return p.sendWithPromise("PDFDoc.createIndirectArray",{doc:this.id}).then(function(t){return _(p.Obj,t)})},p.PDFDoc.prototype.createIndirectBool=function(t){return P(arguments.length,1,"createIndirectBool","(boolean)",[[t,"boolean"]]),p.sendWithPromise("PDFDoc.createIndirectBool",{doc:this.id,value:t}).then(function(t){return _(p.Obj,t)})},p.PDFDoc.prototype.createIndirectDict=function(){return p.sendWithPromise("PDFDoc.createIndirectDict",{doc:this.id}).then(function(t){return _(p.Obj,t)})},p.PDFDoc.prototype.createIndirectNull=function(){return p.sendWithPromise("PDFDoc.createIndirectNull",{doc:this.id}).then(function(t){return _(p.Obj,t)})},p.PDFDoc.prototype.createIndirectNumber=function(t){return P(arguments.length,1,"createIndirectNumber","(number)",[[t,"number"]]),p.sendWithPromise("PDFDoc.createIndirectNumber",{doc:this.id,value:t}).then(function(t){return _(p.Obj,t)})},p.PDFDoc.prototype.createIndirectString=function(t,e){return P(arguments.length,2,"createIndirectString","(number, number)",[[t,"number"],[e,"number"]]),p.sendWithPromise("PDFDoc.createIndirectString",{doc:this.id,value:t,buf_size:e}).then(function(t){return _(p.Obj,t)})},p.PDFDoc.prototype.createIndirectStringFromUString=function(t){return P(arguments.length,1,"createIndirectStringFromUString","(string)",[[t,"string"]]),p.sendWithPromise("PDFDoc.createIndirectStringFromUString",{doc:this.id,str:t}).then(function(t){return _(p.Obj,t)})},p.PDFDoc.prototype.createIndirectStreamFromFilter=function(t,e){return void 0===e&&(e=new p.Filter("0")),P(arguments.length,1,"createIndirectStreamFromFilter","(PDFNet.FilterReader, PDFNet.Filter)",[[t,"Object",p.FilterReader,"FilterReader"],[e,"Object",p.Filter,"Filter"]]),0!=e.id&&A(e.id),p.sendWithPromise("PDFDoc.createIndirectStreamFromFilter",{doc:this.id,data:t.id,no_own_filter_chain:e.id}).then(function(t){return _(p.Obj,t)})},p.PDFDoc.prototype.createIndirectStream=function(t,e){P(arguments.length,2,"createIndirectStream","(ArrayBuffer|TypedArray, PDFNet.Filter)",[[t,"ArrayBuffer"],[e,"Object",p.Filter,"Filter"]]);var n=y(t,!1);return 0!=e.id&&A(e.id),p.sendWithPromise("PDFDoc.createIndirectStream",{doc:this.id,data_buf:n,no_own_filter_chain:e.id}).then(function(t){return _(p.Obj,t)})},p.PDFDoc.prototype.getSDFDoc=function(){return p.sendWithPromise("PDFDoc.getSDFDoc",{doc:this.id}).then(function(t){return _(p.SDFDoc,t)})},p.PDFDoc.prototype.unlock=function(){var t=this;return p.sendWithPromise("PDFDoc.unlock",{doc:this.id}).then(function(){O(t)})},p.PDFDoc.prototype.unlockRead=function(){var t=this;return p.sendWithPromise("PDFDoc.unlockRead",{doc:this.id}).then(function(){O(t)})},p.PDFDoc.prototype.addHighlights=function(t){return P(arguments.length,1,"addHighlights","(string)",[[t,"string"]]),p.sendWithPromise("PDFDoc.addHighlights",{doc:this.id,hilite:t})},p.PDFDoc.prototype.isTagged=function(){return p.sendWithPromise("PDFDoc.isTagged",{doc:this.id})},p.PDFDoc.prototype.hasSignatures=function(){return p.sendWithPromise("PDFDoc.hasSignatures",{doc:this.id})},p.PDFDoc.prototype.addSignatureHandler=function(t){return P(arguments.length,1,"addSignatureHandler","(PDFNet.SignatureHandler)",[[t,"Object",p.SignatureHandler,"SignatureHandler"]]),p.sendWithPromise("PDFDoc.addSignatureHandler",{doc:this.id,signature_handler:t.id})},p.PDFDoc.prototype.addStdSignatureHandlerFromBuffer=function(t,e){P(arguments.length,2,"addStdSignatureHandlerFromBuffer","(ArrayBuffer|TypedArray, string)",[[t,"ArrayBuffer"],[e,"string"]]);var n=y(t,!1);return p.sendWithPromise("PDFDoc.addStdSignatureHandlerFromBuffer",{doc:this.id,pkcs12_buffer:n,pkcs12_pass:e})},p.PDFDoc.prototype.removeSignatureHandler=function(t){return P(arguments.length,1,"removeSignatureHandler","(number)",[[t,"number"]]),p.sendWithPromise("PDFDoc.removeSignatureHandler",{doc:this.id,signature_handler_id:t})},p.PDFDoc.prototype.getSignatureHandler=function(t){return P(arguments.length,1,"getSignatureHandler","(number)",[[t,"number"]]),p.sendWithPromise("PDFDoc.getSignatureHandler",{doc:this.id,signature_handler_id:t}).then(function(t){return _(p.SignatureHandler,t)})},p.PDFDoc.prototype.generateThumbnails=function(t){return P(arguments.length,1,"generateThumbnails","(number)",[[t,"number"]]),p.sendWithPromise("PDFDoc.generateThumbnails",{doc:this.id,size:t})},p.PDFDoc.prototype.appendVisualDiff=function(t,e,n){return void 0===n&&(n=null),P(arguments.length,2,"appendVisualDiff","(PDFNet.Page, PDFNet.Page, PDFNet.OptionBase)",[[t,"Object",p.Page,"Page"],[e,"Object",p.Page,"Page"],[n,"OptionBase"]]),b("appendVisualDiff",[[n,2]]),n=n?n.getJsonString():"{}",p.sendWithPromise("PDFDoc.appendVisualDiff",{doc:this.id,p1:t.id,p2:e.id,opts:n})},p.PDFDoc.prototype.getGeometryCollectionForPage=function(t){return P(arguments.length,1,"getGeometryCollectionForPage","(number)",[[t,"number"]]),p.sendWithPromise("PDFDoc.getGeometryCollectionForPage",{in_pdfdoc:this.id,page_num:t}).then(function(t){return S(p.GeometryCollection,t)})},p.PDFDoc.prototype.getGeometryCollectionForPageWithOptions=function(t,e){return void 0===e&&(e=null),P(arguments.length,1,"getGeometryCollectionForPageWithOptions","(number, PDFNet.OptionBase)",[[t,"number"],[e,"OptionBase"]]),b("getGeometryCollectionForPageWithOptions",[[e,1]]),e=e?e.getJsonString():"{}",p.sendWithPromise("PDFDoc.getGeometryCollectionForPageWithOptions",{in_pdfdoc:this.id,page_num:t,options:e}).then(function(t){return S(p.GeometryCollection,t)})},p.PDFDoc.prototype.getUndoManager=function(){return p.sendWithPromise("PDFDoc.getUndoManager",{doc:this.id}).then(function(t){return S(p.UndoManager,t)})},p.PDFDoc.prototype.createDigitalSignatureField=function(t){return void 0===t&&(t=""),P(arguments.length,0,"createDigitalSignatureField","(string)",[[t,"string"]]),p.sendWithPromise("PDFDoc.createDigitalSignatureField",{doc:this.id,in_sig_field_name:t}).then(function(t){return new p.DigitalSignatureField(t)})},p.PDFDoc.prototype.getDigitalSignatureFieldIteratorBegin=function(){return p.sendWithPromise("PDFDoc.getDigitalSignatureFieldIteratorBegin",{doc:this.id}).then(function(t){return S(p.Iterator,t,"DigitalSignatureField")})},p.PDFDoc.prototype.getDigitalSignaturePermissions=function(){return p.sendWithPromise("PDFDoc.getDigitalSignaturePermissions",{doc:this.id})},p.PDFDoc.prototype.saveViewerOptimizedBuffer=function(t){P(arguments.length,1,"saveViewerOptimizedBuffer","(PDFNet.Obj)",[[t,"OptionObject",p.Obj,"Obj","PDFNet.PDFDoc.ViewerOptimizedOptions"]]),t=D(t,"PDFNet.PDFDoc.ViewerOptimizedOptions");var e=this;return t.then(function(t){return p.sendWithPromise("PDFDoc.saveViewerOptimizedBuffer",{doc:e.id,opts:t.id}).then(function(t){return new Uint8Array(t)})})},p.PDFDoc.prototype.verifySignedDigitalSignatures=function(t){return P(arguments.length,1,"verifySignedDigitalSignatures","(PDFNet.VerificationOptions)",[[t,"Object",p.VerificationOptions,"VerificationOptions"]]),p.sendWithPromise("PDFDoc.verifySignedDigitalSignatures",{doc:this.id,opts:t.id})},p.PDFDoc.prototype.mergeXFDF=function(t,e){return void 0===e&&(e=null),P(arguments.length,1,"mergeXFDF","(PDFNet.Filter, PDFNet.OptionBase)",[[t,"Object",p.Filter,"Filter"],[e,"OptionBase"]]),b("mergeXFDF",[[e,1]]),e=e?e.getJsonString():"{}",p.sendWithPromise("PDFDoc.mergeXFDF",{doc:this.id,stream:t.id,opts:e})},p.PDFDoc.prototype.mergeXFDFString=function(t,e){return void 0===e&&(e=null),P(arguments.length,1,"mergeXFDFString","(string, PDFNet.OptionBase)",[[t,"string"],[e,"OptionBase"]]),b("mergeXFDFString",[[e,1]]),e=e?e.getJsonString():"{}",p.sendWithPromise("PDFDoc.mergeXFDFString",{doc:this.id,xfdf:t,opts:e})},p.PDFDocInfo.prototype.getTitle=function(){return p.sendWithPromise("PDFDocInfo.getTitle",{info:this.id})},p.PDFDocInfo.prototype.getTitleObj=function(){return p.sendWithPromise("PDFDocInfo.getTitleObj",{info:this.id}).then(function(t){return _(p.Obj,t)})},p.PDFDocInfo.prototype.setTitle=function(t){return P(arguments.length,1,"setTitle","(string)",[[t,"string"]]),p.sendWithPromise("PDFDocInfo.setTitle",{info:this.id,title:t})},p.PDFDocInfo.prototype.getAuthor=function(){return p.sendWithPromise("PDFDocInfo.getAuthor",{info:this.id})},p.PDFDocInfo.prototype.getAuthorObj=function(){return p.sendWithPromise("PDFDocInfo.getAuthorObj",{info:this.id}).then(function(t){return _(p.Obj,t)})},p.PDFDocInfo.prototype.setAuthor=function(t){return P(arguments.length,1,"setAuthor","(string)",[[t,"string"]]),p.sendWithPromise("PDFDocInfo.setAuthor",{info:this.id,author:t})},p.PDFDocInfo.prototype.getSubject=function(){return p.sendWithPromise("PDFDocInfo.getSubject",{info:this.id})},p.PDFDocInfo.prototype.getSubjectObj=function(){return p.sendWithPromise("PDFDocInfo.getSubjectObj",{info:this.id}).then(function(t){return _(p.Obj,t)})},p.PDFDocInfo.prototype.setSubject=function(t){return P(arguments.length,1,"setSubject","(string)",[[t,"string"]]),p.sendWithPromise("PDFDocInfo.setSubject",{info:this.id,subject:t})},p.PDFDocInfo.prototype.getKeywords=function(){return p.sendWithPromise("PDFDocInfo.getKeywords",{info:this.id})},p.PDFDocInfo.prototype.getKeywordsObj=function(){return p.sendWithPromise("PDFDocInfo.getKeywordsObj",{info:this.id}).then(function(t){return _(p.Obj,t)})},p.PDFDocInfo.prototype.setKeywords=function(t){return P(arguments.length,1,"setKeywords","(string)",[[t,"string"]]),p.sendWithPromise("PDFDocInfo.setKeywords",{info:this.id,keywords:t})},p.PDFDocInfo.prototype.getCreator=function(){return p.sendWithPromise("PDFDocInfo.getCreator",{info:this.id})},p.PDFDocInfo.prototype.getCreatorObj=function(){return p.sendWithPromise("PDFDocInfo.getCreatorObj",{info:this.id}).then(function(t){return _(p.Obj,t)})},p.PDFDocInfo.prototype.setCreator=function(t){return P(arguments.length,1,"setCreator","(string)",[[t,"string"]]),p.sendWithPromise("PDFDocInfo.setCreator",{info:this.id,creator:t})},p.PDFDocInfo.prototype.getProducer=function(){return p.sendWithPromise("PDFDocInfo.getProducer",{info:this.id})},p.PDFDocInfo.prototype.getProducerObj=function(){return p.sendWithPromise("PDFDocInfo.getProducerObj",{info:this.id}).then(function(t){return _(p.Obj,t)})},p.PDFDocInfo.prototype.setProducer=function(t){return P(arguments.length,1,"setProducer","(string)",[[t,"string"]]),p.sendWithPromise("PDFDocInfo.setProducer",{info:this.id,producer:t})},p.PDFDocInfo.prototype.getCreationDate=function(){return p.sendWithPromise("PDFDocInfo.getCreationDate",{info:this.id}).then(function(t){return new p.Date(t)})},p.PDFDocInfo.prototype.setCreationDate=function(t){return P(arguments.length,1,"setCreationDate","(PDFNet.Date)",[[t,"Structure",p.Date,"Date"]]),b("setCreationDate",[[t,0]]),p.sendWithPromise("PDFDocInfo.setCreationDate",{info:this.id,creation_date:t})},p.PDFDocInfo.prototype.getModDate=function(){return p.sendWithPromise("PDFDocInfo.getModDate",{info:this.id}).then(function(t){return new p.Date(t)})},p.PDFDocInfo.prototype.setModDate=function(t){return P(arguments.length,1,"setModDate","(PDFNet.Date)",[[t,"Structure",p.Date,"Date"]]),b("setModDate",[[t,0]]),p.sendWithPromise("PDFDocInfo.setModDate",{info:this.id,mod_date:t})},p.PDFDocInfo.prototype.getSDFObj=function(){return p.sendWithPromise("PDFDocInfo.getSDFObj",{info:this.id}).then(function(t){return _(p.Obj,t)})},p.PDFDocInfo.create=function(t){return P(arguments.length,1,"create","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("pdfDocInfoCreate",{tr:t.id}).then(function(t){return _(p.PDFDocInfo,t)})},p.PDFDocInfo.prototype.copy=function(){return p.sendWithPromise("PDFDocInfo.copy",{info:this.id}).then(function(t){return _(p.PDFDocInfo,t)})},p.PDFDocViewPrefs.prototype.setInitialPage=function(t){return P(arguments.length,1,"setInitialPage","(PDFNet.Destination)",[[t,"Object",p.Destination,"Destination"]]),p.sendWithPromise("PDFDocViewPrefs.setInitialPage",{p:this.id,dest:t.id})},p.PDFDocViewPrefs.prototype.setPageMode=function(t){return P(arguments.length,1,"setPageMode","(number)",[[t,"number"]]),p.sendWithPromise("PDFDocViewPrefs.setPageMode",{p:this.id,mode:t})},p.PDFDocViewPrefs.prototype.getPageMode=function(){return p.sendWithPromise("PDFDocViewPrefs.getPageMode",{p:this.id})},p.PDFDocViewPrefs.prototype.setLayoutMode=function(t){return P(arguments.length,1,"setLayoutMode","(number)",[[t,"number"]]),p.sendWithPromise("PDFDocViewPrefs.setLayoutMode",{p:this.id,mode:t})},p.PDFDocViewPrefs.prototype.getLayoutMode=function(){return p.sendWithPromise("PDFDocViewPrefs.getLayoutMode",{p:this.id})},p.PDFDocViewPrefs.prototype.setPref=function(t,e){return P(arguments.length,2,"setPref","(number, boolean)",[[t,"number"],[e,"boolean"]]),p.sendWithPromise("PDFDocViewPrefs.setPref",{p:this.id,pref:t,value:e})},p.PDFDocViewPrefs.prototype.getPref=function(t){return P(arguments.length,1,"getPref","(number)",[[t,"number"]]),p.sendWithPromise("PDFDocViewPrefs.getPref",{p:this.id,pref:t})},p.PDFDocViewPrefs.prototype.setNonFullScreenPageMode=function(t){return P(arguments.length,1,"setNonFullScreenPageMode","(number)",[[t,"number"]]),p.sendWithPromise("PDFDocViewPrefs.setNonFullScreenPageMode",{p:this.id,mode:t})},p.PDFDocViewPrefs.prototype.getNonFullScreenPageMode=function(){return p.sendWithPromise("PDFDocViewPrefs.getNonFullScreenPageMode",{p:this.id})},p.PDFDocViewPrefs.prototype.setDirection=function(t){return P(arguments.length,1,"setDirection","(boolean)",[[t,"boolean"]]),p.sendWithPromise("PDFDocViewPrefs.setDirection",{p:this.id,left_to_right:t})},p.PDFDocViewPrefs.prototype.getDirection=function(){return p.sendWithPromise("PDFDocViewPrefs.getDirection",{p:this.id})},p.PDFDocViewPrefs.prototype.setViewArea=function(t){return P(arguments.length,1,"setViewArea","(number)",[[t,"number"]]),p.sendWithPromise("PDFDocViewPrefs.setViewArea",{p:this.id,box:t})},p.PDFDocViewPrefs.prototype.getViewArea=function(){return p.sendWithPromise("PDFDocViewPrefs.getViewArea",{p:this.id})},p.PDFDocViewPrefs.prototype.setViewClip=function(t){return P(arguments.length,1,"setViewClip","(number)",[[t,"number"]]),p.sendWithPromise("PDFDocViewPrefs.setViewClip",{p:this.id,box:t})},p.PDFDocViewPrefs.prototype.getViewClip=function(){return p.sendWithPromise("PDFDocViewPrefs.getViewClip",{p:this.id})},p.PDFDocViewPrefs.prototype.setPrintArea=function(t){return P(arguments.length,1,"setPrintArea","(number)",[[t,"number"]]),p.sendWithPromise("PDFDocViewPrefs.setPrintArea",{p:this.id,box:t})},p.PDFDocViewPrefs.prototype.getPrintArea=function(){return p.sendWithPromise("PDFDocViewPrefs.getPrintArea",{p:this.id})},p.PDFDocViewPrefs.prototype.setPrintClip=function(t){return P(arguments.length,1,"setPrintClip","(number)",[[t,"number"]]),p.sendWithPromise("PDFDocViewPrefs.setPrintClip",{p:this.id,box:t})},p.PDFDocViewPrefs.prototype.getPrintClip=function(){return p.sendWithPromise("PDFDocViewPrefs.getPrintClip",{p:this.id})},p.PDFDocViewPrefs.prototype.getSDFObj=function(){return p.sendWithPromise("PDFDocViewPrefs.getSDFObj",{p:this.id}).then(function(t){return _(p.Obj,t)})},p.PDFDocViewPrefs.create=function(t){return P(arguments.length,1,"create","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("pdfDocViewPrefsCreate",{tr:t.id}).then(function(t){return _(p.PDFDocViewPrefs,t)})},p.PDFDocViewPrefs.prototype.copy=function(){return p.sendWithPromise("PDFDocViewPrefs.copy",{prefs:this.id}).then(function(t){return _(p.PDFDocViewPrefs,t)})},p.PDFRasterizer.create=function(t){return void 0===t&&(t=p.PDFRasterizer.Type.e_BuiltIn),P(arguments.length,0,"create","(number)",[[t,"number"]]),p.sendWithPromise("pdfRasterizerCreate",{type:t}).then(function(t){return S(p.PDFRasterizer,t)})},p.PDFRasterizer.prototype.setDrawAnnotations=function(t){return P(arguments.length,1,"setDrawAnnotations","(boolean)",[[t,"boolean"]]),p.sendWithPromise("PDFRasterizer.setDrawAnnotations",{r:this.id,render_annots:t})},p.PDFRasterizer.prototype.setHighlightFields=function(t){return P(arguments.length,1,"setHighlightFields","(boolean)",[[t,"boolean"]]),p.sendWithPromise("PDFRasterizer.setHighlightFields",{r:this.id,highlight:t})},p.PDFRasterizer.prototype.setDrawUIElements=function(t){return P(arguments.length,1,"setDrawUIElements","(boolean)",[[t,"boolean"]]),p.sendWithPromise("PDFRasterizer.setDrawUIElements",{r:this.id,draw_ui_elements:t})},p.PDFRasterizer.prototype.setAntiAliasing=function(t){return P(arguments.length,1,"setAntiAliasing","(boolean)",[[t,"boolean"]]),p.sendWithPromise("PDFRasterizer.setAntiAliasing",{r:this.id,enable_aa:t})},p.PDFRasterizer.prototype.setPathHinting=function(t){return P(arguments.length,1,"setPathHinting","(boolean)",[[t,"boolean"]]),p.sendWithPromise("PDFRasterizer.setPathHinting",{r:this.id,enable_hinting:t})},p.PDFRasterizer.prototype.setThinLineAdjustment=function(t,e){return P(arguments.length,2,"setThinLineAdjustment","(boolean, boolean)",[[t,"boolean"],[e,"boolean"]]),p.sendWithPromise("PDFRasterizer.setThinLineAdjustment",{r:this.id,grid_fit:t,stroke_adjust:e})},p.PDFRasterizer.prototype.setThinLineScaling=function(t){return P(arguments.length,1,"setThinLineScaling","(number)",[[t,"number"]]),p.sendWithPromise("PDFRasterizer.setThinLineScaling",{r:this.id,scaling:t})},p.PDFRasterizer.prototype.setNightModeTuning=function(t,e,n){return P(arguments.length,3,"setNightModeTuning","(number, number, number)",[[t,"number"],[e,"number"],[n,"number"]]),p.sendWithPromise("PDFRasterizer.setNightModeTuning",{r:this.id,contrast:t,saturation:e,flipness:n})},p.PDFRasterizer.prototype.setGamma=function(t){return P(arguments.length,1,"setGamma","(number)",[[t,"number"]]),p.sendWithPromise("PDFRasterizer.setGamma",{r:this.id,expgamma:t})},p.PDFRasterizer.prototype.setOCGContext=function(t){return P(arguments.length,1,"setOCGContext","(PDFNet.OCGContext)",[[t,"Object",p.OCGContext,"OCGContext"]]),p.sendWithPromise("PDFRasterizer.setOCGContext",{r:this.id,ctx:t.id})},p.PDFRasterizer.prototype.setPrintMode=function(t){return P(arguments.length,1,"setPrintMode","(boolean)",[[t,"boolean"]]),p.sendWithPromise("PDFRasterizer.setPrintMode",{r:this.id,is_printing:t})},p.PDFRasterizer.prototype.setImageSmoothing=function(t,e){return void 0===t&&(t=!0),void 0===e&&(e=!1),P(arguments.length,0,"setImageSmoothing","(boolean, boolean)",[[t,"boolean"],[e,"boolean"]]),p.sendWithPromise("PDFRasterizer.setImageSmoothing",{r:this.id,smoothing_enabled:t,hq_image_resampling:e})},p.PDFRasterizer.prototype.setOverprint=function(t){return P(arguments.length,1,"setOverprint","(number)",[[t,"number"]]),p.sendWithPromise("PDFRasterizer.setOverprint",{r:this.id,op:t})},p.PDFRasterizer.prototype.setCaching=function(t){return void 0===t&&(t=!0),P(arguments.length,0,"setCaching","(boolean)",[[t,"boolean"]]),p.sendWithPromise("PDFRasterizer.setCaching",{r:this.id,enabled:t})},p.PDFDraw.prototype.setOCGContext=function(t){return P(arguments.length,1,"setOCGContext","(PDFNet.OCGContext)",[[t,"Object",p.OCGContext,"OCGContext"]]),p.sendWithPromise("PDFDraw.setOCGContext",{r:this.id,ctx:t.id})},p.PDFRasterizer.prototype.setAnnotationState=function(t,e){return P(arguments.length,2,"setAnnotationState","(PDFNet.Annot, number)",[[t,"Object",p.Annot,"Annot"],[e,"number"]]),p.sendWithPromise("PDFRasterizer.setAnnotationState",{r:this.id,annot:t.id,new_view_state:e})},p.PDFRasterizer.prototype.setRasterizerType=function(t){return P(arguments.length,1,"setRasterizerType","(number)",[[t,"number"]]),p.sendWithPromise("PDFRasterizer.setRasterizerType",{r:this.id,type:t})},p.PDFRasterizer.prototype.getRasterizerType=function(){return p.sendWithPromise("PDFRasterizer.getRasterizerType",{r:this.id})},p.PDFRasterizer.prototype.setColorPostProcessMode=function(t){return P(arguments.length,1,"setColorPostProcessMode","(number)",[[t,"number"]]),p.sendWithPromise("PDFRasterizer.setColorPostProcessMode",{r:this.id,mode:t})},p.PDFRasterizer.prototype.getColorPostProcessMode=function(){return p.sendWithPromise("PDFRasterizer.getColorPostProcessMode",{r:this.id})},p.PDFRasterizer.prototype.enableDisplayListCaching=function(t){return P(arguments.length,1,"enableDisplayListCaching","(boolean)",[[t,"boolean"]]),p.sendWithPromise("PDFRasterizer.enableDisplayListCaching",{r:this.id,enabled:t})},p.PDFRasterizer.prototype.updateBuffer=function(){return p.sendWithPromise("PDFRasterizer.updateBuffer",{r:this.id})},p.PDFRasterizer.prototype.rasterizeAnnot=function(t,e,n,i,r){return P(arguments.length,5,"rasterizeAnnot","(PDFNet.Annot, PDFNet.Page, PDFNet.Matrix2D, boolean, boolean)",[[t,"Object",p.Annot,"Annot"],[e,"Object",p.Page,"Page"],[n,"Structure",p.Matrix2D,"Matrix2D"],[i,"boolean"],[r,"boolean"]]),b("rasterizeAnnot",[[n,2]]),p.sendWithPromise("PDFRasterizer.rasterizeAnnot",{r:this.id,annot:t.id,page:e.id,device_mtx:n,demult:i,cancel:r}).then(function(t){return _(p.OwnedBitmap,t)})},p.PDFRasterizer.prototype.rasterizeSeparations=function(t,e,n,i,r,o){return P(arguments.length,6,"rasterizeSeparations","(PDFNet.Page, number, number, PDFNet.Matrix2D, PDFNet.Rect, boolean)",[[t,"Object",p.Page,"Page"],[e,"number"],[n,"number"],[i,"Structure",p.Matrix2D,"Matrix2D"],[r,"Structure",p.Rect,"Rect"],[o,"boolean"]]),b("rasterizeSeparations",[[i,3],[r,4]]),p.sendWithPromise("PDFRasterizer.rasterizeSeparations",{r:this.id,page:t.id,width:e,height:n,mtx:i,clip:r,cancel:o}).then(function(t){return t.map(function(t){return _(p.Separation,t)})})},p.PDFDraw.create=function(t){return void 0===t&&(t=92),P(arguments.length,0,"create","(number)",[[t,"number"]]),p.sendWithPromise("pdfDrawCreate",{dpi:t}).then(function(t){return S(p.PDFDraw,t)})},p.PDFDraw.prototype.setRasterizerType=function(t){return P(arguments.length,1,"setRasterizerType","(number)",[[t,"number"]]),p.sendWithPromise("PDFDraw.setRasterizerType",{d:this.id,type:t})},p.PDFDraw.prototype.setDPI=function(t){return P(arguments.length,1,"setDPI","(number)",[[t,"number"]]),p.sendWithPromise("PDFDraw.setDPI",{d:this.id,dpi:t})},p.PDFDraw.prototype.setImageSize=function(t,e,n){return void 0===n&&(n=!0),P(arguments.length,2,"setImageSize","(number, number, boolean)",[[t,"number"],[e,"number"],[n,"boolean"]]),p.sendWithPromise("PDFDraw.setImageSize",{d:this.id,width:t,height:e,preserve_aspect_ratio:n})},p.PDFDraw.prototype.setPageBox=function(t){return P(arguments.length,1,"setPageBox","(number)",[[t,"number"]]),p.sendWithPromise("PDFDraw.setPageBox",{d:this.id,region:t})},p.PDFDraw.prototype.setClipRect=function(t){return P(arguments.length,1,"setClipRect","(PDFNet.Rect)",[[t,"Structure",p.Rect,"Rect"]]),b("setClipRect",[[t,0]]),p.sendWithPromise("PDFDraw.setClipRect",{d:this.id,rect:t})},p.PDFDraw.prototype.setFlipYAxis=function(t){return P(arguments.length,1,"setFlipYAxis","(boolean)",[[t,"boolean"]]),p.sendWithPromise("PDFDraw.setFlipYAxis",{d:this.id,flip_y:t})},p.PDFDraw.prototype.setRotate=function(t){return P(arguments.length,1,"setRotate","(number)",[[t,"number"]]),p.sendWithPromise("PDFDraw.setRotate",{d:this.id,r:t})},p.PDFDraw.prototype.setDrawAnnotations=function(t){return P(arguments.length,1,"setDrawAnnotations","(boolean)",[[t,"boolean"]]),p.sendWithPromise("PDFDraw.setDrawAnnotations",{d:this.id,render_annots:t})},p.PDFDraw.prototype.setHighlightFields=function(t){return P(arguments.length,1,"setHighlightFields","(boolean)",[[t,"boolean"]]),p.sendWithPromise("PDFDraw.setHighlightFields",{d:this.id,highlight:t})},p.PDFDraw.prototype.setDrawUIElements=function(t){return P(arguments.length,1,"setDrawUIElements","(boolean)",[[t,"boolean"]]),p.sendWithPromise("PDFDraw.setDrawUIElements",{d:this.id,draw_ui_elements:t})},p.PDFDraw.prototype.setAntiAliasing=function(t){return P(arguments.length,1,"setAntiAliasing","(boolean)",[[t,"boolean"]]),p.sendWithPromise("PDFDraw.setAntiAliasing",{d:this.id,enable_aa:t})},p.PDFDraw.prototype.setPathHinting=function(t){return P(arguments.length,1,"setPathHinting","(boolean)",[[t,"boolean"]]),p.sendWithPromise("PDFDraw.setPathHinting",{d:this.id,enable_hinting:t})},p.PDFDraw.prototype.setThinLineAdjustment=function(t,e){return P(arguments.length,2,"setThinLineAdjustment","(boolean, boolean)",[[t,"boolean"],[e,"boolean"]]),p.sendWithPromise("PDFDraw.setThinLineAdjustment",{d:this.id,grid_fit:t,stroke_adjust:e})},p.PDFDraw.prototype.setThinLineScaling=function(t){return P(arguments.length,1,"setThinLineScaling","(number)",[[t,"number"]]),p.sendWithPromise("PDFDraw.setThinLineScaling",{d:this.id,scaling:t})},p.PDFDraw.prototype.setGamma=function(t){return P(arguments.length,1,"setGamma","(number)",[[t,"number"]]),p.sendWithPromise("PDFDraw.setGamma",{d:this.id,exp:t})},p.PDFDraw.prototype.setPrintMode=function(t){return P(arguments.length,1,"setPrintMode","(boolean)",[[t,"boolean"]]),p.sendWithPromise("PDFDraw.setPrintMode",{d:this.id,is_printing:t})},p.PDFDraw.prototype.setPageTransparent=function(t){return P(arguments.length,1,"setPageTransparent","(boolean)",[[t,"boolean"]]),p.sendWithPromise("PDFDraw.setPageTransparent",{d:this.id,is_transparent:t})},p.PDFDraw.prototype.setDefaultPageColor=function(t,e,n){return P(arguments.length,3,"setDefaultPageColor","(number, number, number)",[[t,"number"],[e,"number"],[n,"number"]]),p.sendWithPromise("PDFDraw.setDefaultPageColor",{d:this.id,r:t,g:e,b:n})},p.PDFDraw.prototype.setOverprint=function(t){return P(arguments.length,1,"setOverprint","(number)",[[t,"number"]]),p.sendWithPromise("PDFDraw.setOverprint",{d:this.id,op:t})},p.PDFDraw.prototype.setImageSmoothing=function(t,e){return void 0===t&&(t=!0),void 0===e&&(e=!1),P(arguments.length,0,"setImageSmoothing","(boolean, boolean)",[[t,"boolean"],[e,"boolean"]]),p.sendWithPromise("PDFDraw.setImageSmoothing",{d:this.id,smoothing_enabled:t,hq_image_resampling:e})},p.PDFDraw.prototype.setCaching=function(t){return void 0===t&&(t=!0),P(arguments.length,0,"setCaching","(boolean)",[[t,"boolean"]]),p.sendWithPromise("PDFDraw.setCaching",{d:this.id,enabled:t})},p.PDFDraw.prototype.setColorPostProcessMode=function(t){return P(arguments.length,1,"setColorPostProcessMode","(number)",[[t,"number"]]),p.sendWithPromise("PDFDraw.setColorPostProcessMode",{d:this.id,mode:t})},p.PDFDraw.prototype.getSeparationBitmaps=function(t){return P(arguments.length,1,"getSeparationBitmaps","(PDFNet.Page)",[[t,"Object",p.Page,"Page"]]),p.sendWithPromise("PDFDraw.getSeparationBitmaps",{d:this.id,page:t.id}).then(function(t){return t.map(function(t){return _(p.Separation,t)})})},p.enableJavaScript=function(t){return P(arguments.length,1,"enableJavaScript","(boolean)",[[t,"boolean"]]),p.sendWithPromise("pdfNetEnableJavaScript",{enable:t})},p.isJavaScriptEnabled=function(){return p.sendWithPromise("pdfNetIsJavaScriptEnabled",{})},p.terminateEx=function(t){return P(arguments.length,1,"terminateEx","(number)",[[t,"number"]]),p.sendWithPromise("pdfNetTerminateEx",{termination_level:t})},p.setColorManagement=function(t){return void 0===t&&(t=p.CMSType.e_lcms),P(arguments.length,0,"setColorManagement","(number)",[[t,"number"]]),p.sendWithPromise("pdfNetSetColorManagement",{t:t})},p.setDefaultDeviceCMYKProfileFromFilter=function(t){return P(arguments.length,1,"setDefaultDeviceCMYKProfileFromFilter","(PDFNet.Filter)",[[t,"Object",p.Filter,"Filter"]]),p.sendWithPromise("pdfNetSetDefaultDeviceCMYKProfileFromFilter",{stream:t.id})},p.setDefaultDeviceRGBProfileFromFilter=function(t){return P(arguments.length,1,"setDefaultDeviceRGBProfileFromFilter","(PDFNet.Filter)",[[t,"Object",p.Filter,"Filter"]]),p.sendWithPromise("pdfNetSetDefaultDeviceRGBProfileFromFilter",{stream:t.id})},p.setDefaultFlateCompressionLevel=function(t){return P(arguments.length,1,"setDefaultFlateCompressionLevel","(number)",[[t,"number"]]),p.sendWithPromise("pdfNetSetDefaultFlateCompressionLevel",{level:t})},p.setViewerCache=function(t,e){return P(arguments.length,2,"setViewerCache","(number, boolean)",[[t,"number"],[e,"boolean"]]),p.sendWithPromise("pdfNetSetViewerCache",{max_cache_size:t,on_disk:e})},p.getVersion=function(){return p.sendWithPromise("pdfNetGetVersion",{})},p.setLogLevel=function(t){return void 0===t&&(t=p.LogLevel.e_LogLevel_Fatal),P(arguments.length,0,"setLogLevel","(number)",[[t,"number"]]),p.sendWithPromise("pdfNetSetLogLevel",{level:t})},p.getSystemFontList=function(){return p.sendWithPromise("pdfNetGetSystemFontList",{})},p.addPDFTronCustomHandler=function(t){return P(arguments.length,1,"addPDFTronCustomHandler","(number)",[[t,"number"]]),p.sendWithPromise("pdfNetAddPDFTronCustomHandler",{custom_id:t})},p.getVersionString=function(){return p.sendWithPromise("pdfNetGetVersionString",{})},p.setConnectionErrorHandlingMode=function(t){return P(arguments.length,1,"setConnectionErrorHandlingMode","(number)",[[t,"number"]]),p.sendWithPromise("pdfNetSetConnectionErrorHandlingMode",{mode:t})},p.Rect.init=function(t,e,n,i){return P(arguments.length,4,"init","(number, number, number, number)",[[t,"number"],[e,"number"],[n,"number"],[i,"number"]]),p.sendWithPromise("rectInit",{x1:t,y1:e,x2:n,y2:i}).then(function(t){return new p.Rect(t)})},p.Rect.prototype.attach=function(t){P(arguments.length,1,"attach","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),F("attach",this.yieldFunction);var e=this;return this.yieldFunction="Rect.attach",p.sendWithPromise("Rect.attach",{rect:this,obj:t.id}).then(function(t){e.yieldFunction=void 0,W(t,e)})},p.Rect.prototype.update=function(t){void 0===t&&(t=new p.Obj("0")),P(arguments.length,0,"update","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),F("update",this.yieldFunction);var e=this;return this.yieldFunction="Rect.update",p.sendWithPromise("Rect.update",{rect:this,obj:t.id}).then(function(t){return e.yieldFunction=void 0,W(t.rect,e),t.result})},p.Rect.prototype.get=function(){return F("get",this.yieldFunction),p.sendWithPromise("Rect.get",{rect:this})},p.Rect.prototype.set=function(t,e,n,i){P(arguments.length,4,"set","(number, number, number, number)",[[t,"number"],[e,"number"],[n,"number"],[i,"number"]]),F("set",this.yieldFunction);var r=this;return this.yieldFunction="Rect.set",p.sendWithPromise("Rect.set",{rect:this,x1:t,y1:e,x2:n,y2:i}).then(function(t){r.yieldFunction=void 0,W(t,r)})},p.Rect.prototype.width=function(){return F("width",this.yieldFunction),p.sendWithPromise("Rect.width",{rect:this})},p.Rect.prototype.height=function(){return F("height",this.yieldFunction),p.sendWithPromise("Rect.height",{rect:this})},p.Rect.prototype.contains=function(t,e){return P(arguments.length,2,"contains","(number, number)",[[t,"number"],[e,"number"]]),F("contains",this.yieldFunction),p.sendWithPromise("Rect.contains",{rect:this,x:t,y:e})},p.Rect.prototype.intersectRect=function(t,e){P(arguments.length,2,"intersectRect","(PDFNet.Rect, PDFNet.Rect)",[[t,"Structure",p.Rect,"Rect"],[e,"Structure",p.Rect,"Rect"]]),F("intersectRect",this.yieldFunction),b("intersectRect",[[t,0],[e,1]]);var n=this;return this.yieldFunction="Rect.intersectRect",p.sendWithPromise("Rect.intersectRect",{rect:this,rect1:t,rect2:e}).then(function(t){return n.yieldFunction=void 0,W(t.rect,n),t.result})},p.Rect.prototype.normalize=function(){F("normalize",this.yieldFunction);var e=this;return this.yieldFunction="Rect.normalize",p.sendWithPromise("Rect.normalize",{rect:this}).then(function(t){e.yieldFunction=void 0,W(t,e)})},p.Rect.prototype.inflate1=function(t){P(arguments.length,1,"inflate1","(number)",[[t,"number"]]),F("inflate1",this.yieldFunction);var e=this;return this.yieldFunction="Rect.inflate1",p.sendWithPromise("Rect.inflate1",{rect:this,amount:t}).then(function(t){e.yieldFunction=void 0,W(t,e)})},p.Rect.prototype.inflate2=function(t,e){P(arguments.length,2,"inflate2","(number, number)",[[t,"number"],[e,"number"]]),F("inflate2",this.yieldFunction);var n=this;return this.yieldFunction="Rect.inflate2",p.sendWithPromise("Rect.inflate2",{rect:this,x:t,y:e}).then(function(t){n.yieldFunction=void 0,W(t,n)})},p.Redactor.redactionCreate=function(t,e,n,i){return P(arguments.length,4,"redactionCreate","(number, PDFNet.Rect, boolean, string)",[[t,"number"],[e,"Structure",p.Rect,"Rect"],[n,"boolean"],[i,"string"]]),b("redactionCreate",[[e,1]]),p.sendWithPromise("Redactor.redactionCreate",{page_num:t,bbox:e,negative:n,text:i}).then(function(t){return _(p.Redaction,t)})},p.Redactor.redactionDestroy=function(t){return P(arguments.length,1,"redactionDestroy","(PDFNet.Redaction)",[[t,"Object",p.Redaction,"Redaction"]]),p.sendWithPromise("Redactor.redactionDestroy",{redaction:t.id})},p.Redactor.redactionCopy=function(t){return P(arguments.length,1,"redactionCopy","(PDFNet.Redaction)",[[t,"Object",p.Redaction,"Redaction"]]),p.sendWithPromise("Redactor.redactionCopy",{other:t.id}).then(function(t){return _(p.Redaction,t)})},p.Shading.create=function(t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,0,"create","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("shadingCreate",{shading_dict:t.id}).then(function(t){return S(p.Shading,t)})},p.Shading.getTypeFromObj=function(t){return P(arguments.length,1,"getTypeFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("shadingGetTypeFromObj",{shading_dict:t.id})},p.Shading.prototype.getType=function(){return p.sendWithPromise("Shading.getType",{s:this.id})},p.Shading.prototype.getSDFObj=function(){return p.sendWithPromise("Shading.getSDFObj",{s:this.id}).then(function(t){return _(p.Obj,t)})},p.Shading.prototype.getBaseColorSpace=function(){return p.sendWithPromise("Shading.getBaseColorSpace",{s:this.id}).then(function(t){return S(p.ColorSpace,t)})},p.Shading.prototype.hasBBox=function(){return p.sendWithPromise("Shading.hasBBox",{s:this.id})},p.Shading.prototype.getBBox=function(){return p.sendWithPromise("Shading.getBBox",{s:this.id}).then(function(t){return new p.Rect(t)})},p.Shading.prototype.hasBackground=function(){return p.sendWithPromise("Shading.hasBackground",{s:this.id})},p.Shading.prototype.getBackground=function(){return p.sendWithPromise("Shading.getBackground",{s:this.id}).then(function(t){return S(p.ColorPt,t)})},p.Shading.prototype.getAntialias=function(){return p.sendWithPromise("Shading.getAntialias",{s:this.id})},p.Shading.prototype.getParamStart=function(){return p.sendWithPromise("Shading.getParamStart",{s:this.id})},p.Shading.prototype.getParamEnd=function(){return p.sendWithPromise("Shading.getParamEnd",{s:this.id})},p.Shading.prototype.isExtendStart=function(){return p.sendWithPromise("Shading.isExtendStart",{s:this.id})},p.Shading.prototype.isExtendEnd=function(){return p.sendWithPromise("Shading.isExtendEnd",{s:this.id})},p.Shading.prototype.getColor=function(t){return P(arguments.length,1,"getColor","(number)",[[t,"number"]]),p.sendWithPromise("Shading.getColor",{s:this.id,t:t}).then(function(t){return S(p.ColorPt,t)})},p.Shading.prototype.getCoords=function(){return p.sendWithPromise("Shading.getCoords",{s:this.id})},p.Shading.prototype.getCoordsRadial=function(){return p.sendWithPromise("Shading.getCoordsRadial",{s:this.id})},p.Shading.prototype.getDomain=function(){return p.sendWithPromise("Shading.getDomain",{s:this.id})},p.Shading.prototype.getMatrix=function(){return p.sendWithPromise("Shading.getMatrix",{s:this.id}).then(function(t){return new p.Matrix2D(t)})},p.Shading.prototype.getColorForFunction=function(t,e){return P(arguments.length,2,"getColorForFunction","(number, number)",[[t,"number"],[e,"number"]]),p.sendWithPromise("Shading.getColorForFunction",{s:this.id,t1:t,t2:e}).then(function(t){return S(p.ColorPt,t)})},p.Stamper.create=function(t,e,n){return P(arguments.length,3,"create","(number, number, number)",[[t,"number"],[e,"number"],[n,"number"]]),p.sendWithPromise("stamperCreate",{size_type:t,a:e,b:n}).then(function(t){return S(p.Stamper,t)})},p.Stamper.prototype.stampImage=function(t,e,n){return P(arguments.length,3,"stampImage","(PDFNet.PDFDoc, PDFNet.Image, PDFNet.PageSet)",[[t,"PDFDoc"],[e,"Object",p.Image,"Image"],[n,"Object",p.PageSet,"PageSet"]]),p.sendWithPromise("Stamper.stampImage",{stamp:this.id,dest_doc:t.id,img:e.id,dest_pages:n.id})},p.Stamper.prototype.stampPage=function(t,e,n){return P(arguments.length,3,"stampPage","(PDFNet.PDFDoc, PDFNet.Page, PDFNet.PageSet)",[[t,"PDFDoc"],[e,"Object",p.Page,"Page"],[n,"Object",p.PageSet,"PageSet"]]),p.sendWithPromise("Stamper.stampPage",{stamp:this.id,dest_doc:t.id,page:e.id,dest_pages:n.id})},p.Stamper.prototype.stampText=function(t,e,n){return P(arguments.length,3,"stampText","(PDFNet.PDFDoc, string, PDFNet.PageSet)",[[t,"PDFDoc"],[e,"string"],[n,"Object",p.PageSet,"PageSet"]]),p.sendWithPromise("Stamper.stampText",{stamp:this.id,dest_doc:t.id,txt:e,dest_pages:n.id})},p.Stamper.prototype.setFont=function(t){return P(arguments.length,1,"setFont","(PDFNet.Font)",[[t,"Object",p.Font,"Font"]]),p.sendWithPromise("Stamper.setFont",{stamp:this.id,font:t.id})},p.Stamper.prototype.setFontColor=function(t){return P(arguments.length,1,"setFontColor","(PDFNet.ColorPt)",[[t,"Object",p.ColorPt,"ColorPt"]]),p.sendWithPromise("Stamper.setFontColor",{stamp:this.id,font_color:t.id})},p.Stamper.prototype.setTextAlignment=function(t){return P(arguments.length,1,"setTextAlignment","(number)",[[t,"number"]]),p.sendWithPromise("Stamper.setTextAlignment",{stamp:this.id,text_alignment:t})},p.Stamper.prototype.setOpacity=function(t){return P(arguments.length,1,"setOpacity","(number)",[[t,"number"]]),p.sendWithPromise("Stamper.setOpacity",{stamp:this.id,opacity:t})},p.Stamper.prototype.setRotation=function(t){return P(arguments.length,1,"setRotation","(number)",[[t,"number"]]),p.sendWithPromise("Stamper.setRotation",{stamp:this.id,rotation:t})},p.Stamper.prototype.setAsBackground=function(t){return P(arguments.length,1,"setAsBackground","(boolean)",[[t,"boolean"]]),p.sendWithPromise("Stamper.setAsBackground",{stamp:this.id,background:t})},p.Stamper.prototype.setAsAnnotation=function(t){return P(arguments.length,1,"setAsAnnotation","(boolean)",[[t,"boolean"]]),p.sendWithPromise("Stamper.setAsAnnotation",{stamp:this.id,annotation:t})},p.Stamper.prototype.showsOnScreen=function(t){return P(arguments.length,1,"showsOnScreen","(boolean)",[[t,"boolean"]]),p.sendWithPromise("Stamper.showsOnScreen",{stamp:this.id,on_screen:t})},p.Stamper.prototype.showsOnPrint=function(t){return P(arguments.length,1,"showsOnPrint","(boolean)",[[t,"boolean"]]),p.sendWithPromise("Stamper.showsOnPrint",{stamp:this.id,on_print:t})},p.Stamper.prototype.setAlignment=function(t,e){return P(arguments.length,2,"setAlignment","(number, number)",[[t,"number"],[e,"number"]]),p.sendWithPromise("Stamper.setAlignment",{stamp:this.id,horizontal_alignment:t,vertical_alignment:e})},p.Stamper.prototype.setPosition=function(t,e,n){return void 0===n&&(n=!1),P(arguments.length,2,"setPosition","(number, number, boolean)",[[t,"number"],[e,"number"],[n,"boolean"]]),p.sendWithPromise("Stamper.setPosition",{stamp:this.id,horizontal_distance:t,vertical_distance:e,use_percentage:n})},p.Stamper.prototype.setSize=function(t,e,n){return P(arguments.length,3,"setSize","(number, number, number)",[[t,"number"],[e,"number"],[n,"number"]]),p.sendWithPromise("Stamper.setSize",{stamp:this.id,size_type:t,a:e,b:n})},p.Stamper.deleteStamps=function(t,e){return P(arguments.length,2,"deleteStamps","(PDFNet.PDFDoc, PDFNet.PageSet)",[[t,"PDFDoc"],[e,"Object",p.PageSet,"PageSet"]]),p.sendWithPromise("stamperDeleteStamps",{doc:t.id,page_set:e.id})},p.Stamper.hasStamps=function(t,e){return P(arguments.length,2,"hasStamps","(PDFNet.PDFDoc, PDFNet.PageSet)",[[t,"PDFDoc"],[e,"Object",p.PageSet,"PageSet"]]),p.sendWithPromise("stamperHasStamps",{doc:t.id,page_set:e.id})},p.TextExtractor.create=function(){return p.sendWithPromise("textExtractorCreate",{}).then(function(t){return S(p.TextExtractor,t)})},p.TextExtractor.prototype.setOCGContext=function(t){return P(arguments.length,1,"setOCGContext","(PDFNet.OCGContext)",[[t,"Object",p.OCGContext,"OCGContext"]]),p.sendWithPromise("TextExtractor.setOCGContext",{te:this.id,ctx:t.id})},p.TextExtractor.prototype.begin=function(t,e,n){return void 0===e&&(e=null),void 0===n&&(n=0),P(arguments.length,1,"begin","(PDFNet.Page, PDFNet.Rect, number)",[[t,"Object",p.Page,"Page"],[e,"Structure",p.Rect,"Rect"],[n,"number"]]),b("begin",[[e,1]]),p.sendWithPromise("TextExtractor.begin",{te:this.id,page:t.id,clip_ptr:e,flags:n})},p.TextExtractor.prototype.getWordCount=function(){return p.sendWithPromise("TextExtractor.getWordCount",{te:this.id})},p.TextExtractor.prototype.setRightToLeftLanguage=function(t){return P(arguments.length,1,"setRightToLeftLanguage","(boolean)",[[t,"boolean"]]),p.sendWithPromise("TextExtractor.setRightToLeftLanguage",{te:this.id,rtl:t})},p.TextExtractor.prototype.getRightToLeftLanguage=function(){return p.sendWithPromise("TextExtractor.getRightToLeftLanguage",{te:this.id})},p.TextExtractor.prototype.getAsText=function(t){return void 0===t&&(t=!0),P(arguments.length,0,"getAsText","(boolean)",[[t,"boolean"]]),p.sendWithPromise("TextExtractor.getAsText",{te:this.id,dehyphen:t})},p.TextExtractor.prototype.getTextUnderAnnot=function(t){return P(arguments.length,1,"getTextUnderAnnot","(PDFNet.Annot)",[[t,"Object",p.Annot,"Annot"]]),p.sendWithPromise("TextExtractor.getTextUnderAnnot",{te:this.id,annot:t.id})},p.TextExtractor.prototype.getAsXML=function(t){return void 0===t&&(t=0),P(arguments.length,0,"getAsXML","(number)",[[t,"number"]]),p.sendWithPromise("TextExtractor.getAsXML",{te:this.id,xml_output_flags:t})},p.TextExtractorStyle.prototype.getFont=function(){F("getFont",this.yieldFunction);var e=this;return this.yieldFunction="TextExtractorStyle.getFont",p.sendWithPromise("TextExtractorStyle.getFont",{tes:this}).then(function(t){return e.yieldFunction=void 0,t.result=_(p.Obj,t.result),W(t.tes,e),t.result})},p.TextExtractorStyle.prototype.getFontName=function(){F("getFontName",this.yieldFunction);var e=this;return this.yieldFunction="TextExtractorStyle.getFontName",p.sendWithPromise("TextExtractorStyle.getFontName",{tes:this}).then(function(t){return e.yieldFunction=void 0,W(t.tes,e),t.result})},p.TextExtractorStyle.prototype.getFontSize=function(){F("getFontSize",this.yieldFunction);var e=this;return this.yieldFunction="TextExtractorStyle.getFontSize",p.sendWithPromise("TextExtractorStyle.getFontSize",{tes:this}).then(function(t){return e.yieldFunction=void 0,W(t.tes,e),t.result})},p.TextExtractorStyle.prototype.getWeight=function(){F("getWeight",this.yieldFunction);var e=this;return this.yieldFunction="TextExtractorStyle.getWeight",p.sendWithPromise("TextExtractorStyle.getWeight",{tes:this}).then(function(t){return e.yieldFunction=void 0,W(t.tes,e),t.result})},p.TextExtractorStyle.prototype.isItalic=function(){F("isItalic",this.yieldFunction);var e=this;return this.yieldFunction="TextExtractorStyle.isItalic",p.sendWithPromise("TextExtractorStyle.isItalic",{tes:this}).then(function(t){return e.yieldFunction=void 0,W(t.tes,e),t.result})},p.TextExtractorStyle.prototype.isSerif=function(){F("isSerif",this.yieldFunction);var e=this;return this.yieldFunction="TextExtractorStyle.isSerif",p.sendWithPromise("TextExtractorStyle.isSerif",{tes:this}).then(function(t){return e.yieldFunction=void 0,W(t.tes,e),t.result})},p.TextExtractorStyle.prototype.compare=function(t){return P(arguments.length,1,"compare","(PDFNet.TextExtractorStyle)",[[t,"Structure",p.TextExtractorStyle,"TextExtractorStyle"]]),F("compare",this.yieldFunction),b("compare",[[t,0]]),p.sendWithPromise("TextExtractorStyle.compare",{tes:this,s:t})},p.TextExtractorStyle.create=function(){return p.sendWithPromise("textExtractorStyleCreate",{}).then(function(t){return new p.TextExtractorStyle(t)})},p.TextExtractorStyle.prototype.copy=function(){F("copy",this.yieldFunction);var e=this;return this.yieldFunction="TextExtractorStyle.copy",p.sendWithPromise("TextExtractorStyle.copy",{s:this}).then(function(t){return e.yieldFunction=void 0,t.result=new p.TextExtractorStyle(t.result),W(t.s,e),t.result})},p.TextExtractorWord.prototype.getNumGlyphs=function(){F("getNumGlyphs",this.yieldFunction);var e=this;return this.yieldFunction="TextExtractorWord.getNumGlyphs",p.sendWithPromise("TextExtractorWord.getNumGlyphs",{tew:this}).then(function(t){return e.yieldFunction=void 0,W(t.tew,e),t.result})},p.TextExtractorWord.prototype.getCharStyle=function(t){P(arguments.length,1,"getCharStyle","(number)",[[t,"number"]]),F("getCharStyle",this.yieldFunction);var e=this;return this.yieldFunction="TextExtractorWord.getCharStyle",p.sendWithPromise("TextExtractorWord.getCharStyle",{tew:this,char_idx:t}).then(function(t){return e.yieldFunction=void 0,t.result=new p.TextExtractorStyle(t.result),W(t.tew,e),t.result})},p.TextExtractorWord.prototype.getStyle=function(){F("getStyle",this.yieldFunction);var e=this;return this.yieldFunction="TextExtractorWord.getStyle",p.sendWithPromise("TextExtractorWord.getStyle",{tew:this}).then(function(t){return e.yieldFunction=void 0,t.result=new p.TextExtractorStyle(t.result),W(t.tew,e),t.result})},p.TextExtractorWord.prototype.getStringLen=function(){F("getStringLen",this.yieldFunction);var e=this;return this.yieldFunction="TextExtractorWord.getStringLen",p.sendWithPromise("TextExtractorWord.getStringLen",{tew:this}).then(function(t){return e.yieldFunction=void 0,W(t.tew,e),t.result})},p.TextExtractorWord.prototype.getNextWord=function(){F("getNextWord",this.yieldFunction);var e=this;return this.yieldFunction="TextExtractorWord.getNextWord",p.sendWithPromise("TextExtractorWord.getNextWord",{tew:this}).then(function(t){return e.yieldFunction=void 0,t.result=new p.TextExtractorWord(t.result),W(t.tew,e),t.result})},p.TextExtractorWord.prototype.getCurrentNum=function(){F("getCurrentNum",this.yieldFunction);var e=this;return this.yieldFunction="TextExtractorWord.getCurrentNum",p.sendWithPromise("TextExtractorWord.getCurrentNum",{tew:this}).then(function(t){return e.yieldFunction=void 0,W(t.tew,e),t.result})},p.TextExtractorWord.prototype.compare=function(t){return P(arguments.length,1,"compare","(PDFNet.TextExtractorWord)",[[t,"Structure",p.TextExtractorWord,"TextExtractorWord"]]),F("compare",this.yieldFunction),b("compare",[[t,0]]),p.sendWithPromise("TextExtractorWord.compare",{tew:this,word:t})},p.TextExtractorWord.create=function(){return p.sendWithPromise("textExtractorWordCreate",{}).then(function(t){return new p.TextExtractorWord(t)})},p.TextExtractorWord.prototype.isValid=function(){F("isValid",this.yieldFunction);var e=this;return this.yieldFunction="TextExtractorWord.isValid",p.sendWithPromise("TextExtractorWord.isValid",{tew:this}).then(function(t){return e.yieldFunction=void 0,W(t.tew,e),t.result})},p.TextExtractorLine.prototype.getNumWords=function(){F("getNumWords",this.yieldFunction);var e=this;return this.yieldFunction="TextExtractorLine.getNumWords",p.sendWithPromise("TextExtractorLine.getNumWords",{line:this}).then(function(t){return e.yieldFunction=void 0,W(t.line,e),t.result})},p.TextExtractorLine.prototype.isSimpleLine=function(){F("isSimpleLine",this.yieldFunction);var e=this;return this.yieldFunction="TextExtractorLine.isSimpleLine",p.sendWithPromise("TextExtractorLine.isSimpleLine",{line:this}).then(function(t){return e.yieldFunction=void 0,W(t.line,e),t.result})},p.TextExtractorLine.prototype.getFirstWord=function(){F("getFirstWord",this.yieldFunction);var e=this;return this.yieldFunction="TextExtractorLine.getFirstWord",p.sendWithPromise("TextExtractorLine.getFirstWord",{line:this}).then(function(t){return e.yieldFunction=void 0,t.result=new p.TextExtractorWord(t.result),W(t.line,e),t.result})},p.TextExtractorLine.prototype.getWord=function(t){P(arguments.length,1,"getWord","(number)",[[t,"number"]]),F("getWord",this.yieldFunction);var e=this;return this.yieldFunction="TextExtractorLine.getWord",p.sendWithPromise("TextExtractorLine.getWord",{line:this,word_idx:t}).then(function(t){return e.yieldFunction=void 0,t.result=new p.TextExtractorWord(t.result),W(t.line,e),t.result})},p.TextExtractorLine.prototype.getNextLine=function(){F("getNextLine",this.yieldFunction);var e=this;return this.yieldFunction="TextExtractorLine.getNextLine",p.sendWithPromise("TextExtractorLine.getNextLine",{line:this}).then(function(t){return e.yieldFunction=void 0,t.result=new p.TextExtractorLine(t.result),W(t.line,e),t.result})},p.TextExtractorLine.prototype.getCurrentNum=function(){F("getCurrentNum",this.yieldFunction);var e=this;return this.yieldFunction="TextExtractorLine.getCurrentNum",p.sendWithPromise("TextExtractorLine.getCurrentNum",{line:this}).then(function(t){return e.yieldFunction=void 0,W(t.line,e),t.result})},p.TextExtractorLine.prototype.getStyle=function(){F("getStyle",this.yieldFunction);var e=this;return this.yieldFunction="TextExtractorLine.getStyle",p.sendWithPromise("TextExtractorLine.getStyle",{line:this}).then(function(t){return e.yieldFunction=void 0,t.result=new p.TextExtractorStyle(t.result),W(t.line,e),t.result})},p.TextExtractorLine.prototype.getParagraphID=function(){F("getParagraphID",this.yieldFunction);var e=this;return this.yieldFunction="TextExtractorLine.getParagraphID",p.sendWithPromise("TextExtractorLine.getParagraphID",{line:this}).then(function(t){return e.yieldFunction=void 0,W(t.line,e),t.result})},p.TextExtractorLine.prototype.getFlowID=function(){F("getFlowID",this.yieldFunction);var e=this;return this.yieldFunction="TextExtractorLine.getFlowID",p.sendWithPromise("TextExtractorLine.getFlowID",{line:this}).then(function(t){return e.yieldFunction=void 0,W(t.line,e),t.result})},p.TextExtractorLine.prototype.endsWithHyphen=function(){F("endsWithHyphen",this.yieldFunction);var e=this;return this.yieldFunction="TextExtractorLine.endsWithHyphen",p.sendWithPromise("TextExtractorLine.endsWithHyphen",{line:this}).then(function(t){return e.yieldFunction=void 0,W(t.line,e),t.result})},p.TextExtractorLine.prototype.compare=function(t){return P(arguments.length,1,"compare","(PDFNet.TextExtractorLine)",[[t,"Structure",p.TextExtractorLine,"TextExtractorLine"]]),F("compare",this.yieldFunction),b("compare",[[t,0]]),p.sendWithPromise("TextExtractorLine.compare",{line:this,line2:t})},p.TextExtractorLine.create=function(){return p.sendWithPromise("textExtractorLineCreate",{}).then(function(t){return new p.TextExtractorLine(t)})},p.TextExtractorLine.prototype.isValid=function(){F("isValid",this.yieldFunction);var e=this;return this.yieldFunction="TextExtractorLine.isValid",p.sendWithPromise("TextExtractorLine.isValid",{line:this}).then(function(t){return e.yieldFunction=void 0,W(t.line,e),t.result})},p.TextExtractor.prototype.getNumLines=function(){return p.sendWithPromise("TextExtractor.getNumLines",{te:this.id})},p.TextExtractor.prototype.getFirstLine=function(){return p.sendWithPromise("TextExtractor.getFirstLine",{te:this.id}).then(function(t){return new p.TextExtractorLine(t)})},p.TextExtractor.prototype.getQuads=function(t,e,n){return P(arguments.length,3,"getQuads","(PDFNet.Matrix2D, number, number)",[[t,"Structure",p.Matrix2D,"Matrix2D"],[e,"number"],[n,"number"]]),b("getQuads",[[t,0]]),p.sendWithPromise("TextExtractor.getQuads",{te:this.id,mtx:t,quads:e,quads_size:n})},p.TextSearch.create=function(){return p.sendWithPromise("textSearchCreate",{}).then(function(t){return S(p.TextSearch,t)})},p.TextSearch.prototype.begin=function(t,e,n,i,r){return void 0===i&&(i=-1),void 0===r&&(r=-1),P(arguments.length,3,"begin","(PDFNet.PDFDoc, string, number, number, number)",[[t,"PDFDoc"],[e,"string"],[n,"number"],[i,"number"],[r,"number"]]),p.sendWithPromise("TextSearch.begin",{ts:this.id,doc:t.id,pattern:e,mode:n,start_page:i,end_page:r})},p.TextSearch.prototype.setPattern=function(t){return P(arguments.length,1,"setPattern","(string)",[[t,"string"]]),p.sendWithPromise("TextSearch.setPattern",{ts:this.id,pattern:t})},p.TextSearch.prototype.getMode=function(){return p.sendWithPromise("TextSearch.getMode",{ts:this.id})},p.TextSearch.prototype.setMode=function(t){return P(arguments.length,1,"setMode","(number)",[[t,"number"]]),p.sendWithPromise("TextSearch.setMode",{ts:this.id,mode:t})},p.TextSearch.prototype.setRightToLeftLanguage=function(t){return P(arguments.length,1,"setRightToLeftLanguage","(boolean)",[[t,"boolean"]]),p.sendWithPromise("TextSearch.setRightToLeftLanguage",{ts:this.id,flag:t})},p.TextSearch.prototype.getCurrentPage=function(){return p.sendWithPromise("TextSearch.getCurrentPage",{ts:this.id})},p.TextSearch.prototype.setOCGContext=function(t){return P(arguments.length,1,"setOCGContext","(PDFNet.OCGContext)",[[t,"Object",p.OCGContext,"OCGContext"]]),p.sendWithPromise("TextSearch.setOCGContext",{te:this.id,ctx:t.id})},p.TextSearch.prototype.setAmbientLettersBefore=function(t){return P(arguments.length,1,"setAmbientLettersBefore","(number)",[[t,"number"]]),p.sendWithPromise("TextSearch.setAmbientLettersBefore",{self:this.id,ambient_letters_before:t})},p.TextSearch.prototype.setAmbientLettersAfter=function(t){return P(arguments.length,1,"setAmbientLettersAfter","(number)",[[t,"number"]]),p.sendWithPromise("TextSearch.setAmbientLettersAfter",{self:this.id,ambient_letters_after:t})},p.TextSearch.prototype.setAmbientWordsBefore=function(t){return P(arguments.length,1,"setAmbientWordsBefore","(number)",[[t,"number"]]),p.sendWithPromise("TextSearch.setAmbientWordsBefore",{self:this.id,ambient_words_before:t})},p.TextSearch.prototype.setAmbientWordsAfter=function(t){return P(arguments.length,1,"setAmbientWordsAfter","(number)",[[t,"number"]]),p.sendWithPromise("TextSearch.setAmbientWordsAfter",{self:this.id,ambient_words_after:t})},p.NameTree.create=function(t,e){return P(arguments.length,2,"create","(PDFNet.SDFDoc, string)",[[t,"SDFDoc"],[e,"string"]]),p.sendWithPromise("nameTreeCreate",{doc:t.id,name:e}).then(function(t){return _(p.NameTree,t)})},p.NameTree.find=function(t,e){return P(arguments.length,2,"find","(PDFNet.SDFDoc, string)",[[t,"SDFDoc"],[e,"string"]]),p.sendWithPromise("nameTreeFind",{doc:t.id,name:e}).then(function(t){return _(p.NameTree,t)})},p.NameTree.createFromObj=function(t){return P(arguments.length,1,"createFromObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("nameTreeCreateFromObj",{name_tree:t.id}).then(function(t){return _(p.NameTree,t)})},p.NameTree.prototype.copy=function(){return p.sendWithPromise("NameTree.copy",{d:this.id}).then(function(t){return _(p.NameTree,t)})},p.NameTree.prototype.isValid=function(){return p.sendWithPromise("NameTree.isValid",{tree:this.id})},p.NameTree.prototype.getIterator=function(t){return P(arguments.length,1,"getIterator","(string)",[[t,"string"]]),p.sendWithPromise("NameTree.getIterator",{tree:this.id,key:t}).then(function(t){return S(p.DictIterator,t)})},p.NameTree.prototype.getValue=function(t){return P(arguments.length,1,"getValue","(string)",[[t,"string"]]),p.sendWithPromise("NameTree.getValue",{tree:this.id,key:t}).then(function(t){return _(p.Obj,t)})},p.NameTree.prototype.getIteratorBegin=function(){return p.sendWithPromise("NameTree.getIteratorBegin",{tree:this.id}).then(function(t){return S(p.DictIterator,t)})},p.NameTree.prototype.put=function(t,e){return P(arguments.length,2,"put","(string, PDFNet.Obj)",[[t,"string"],[e,"Object",p.Obj,"Obj"]]),p.sendWithPromise("NameTree.put",{tree:this.id,key:t,value:e.id})},p.NameTree.prototype.eraseKey=function(t){return P(arguments.length,1,"eraseKey","(string)",[[t,"string"]]),p.sendWithPromise("NameTree.eraseKey",{tree:this.id,key:t})},p.NameTree.prototype.erase=function(t){return P(arguments.length,1,"erase","(PDFNet.DictIterator)",[[t,"Object",p.DictIterator,"DictIterator"]]),p.sendWithPromise("NameTree.erase",{tree:this.id,pos:t.id})},p.NameTree.prototype.getSDFObj=function(){return p.sendWithPromise("NameTree.getSDFObj",{tree:this.id}).then(function(t){return _(p.Obj,t)})},p.NumberTree.create=function(t){return P(arguments.length,1,"create","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("numberTreeCreate",{number_tree:t.id}).then(function(t){return _(p.NumberTree,t)})},p.NumberTree.prototype.copy=function(){return p.sendWithPromise("NumberTree.copy",{tree:this.id}).then(function(t){return _(p.NumberTree,t)})},p.NumberTree.prototype.isValid=function(){return p.sendWithPromise("NumberTree.isValid",{tree:this.id})},p.NumberTree.prototype.getIterator=function(t){return P(arguments.length,1,"getIterator","(number)",[[t,"number"]]),p.sendWithPromise("NumberTree.getIterator",{tree:this.id,key:t}).then(function(t){return S(p.DictIterator,t)})},p.NumberTree.prototype.getValue=function(t){return P(arguments.length,1,"getValue","(number)",[[t,"number"]]),p.sendWithPromise("NumberTree.getValue",{tree:this.id,key:t}).then(function(t){return _(p.Obj,t)})},p.NumberTree.prototype.getIteratorBegin=function(){return p.sendWithPromise("NumberTree.getIteratorBegin",{tree:this.id}).then(function(t){return S(p.DictIterator,t)})},p.NumberTree.prototype.put=function(t,e){return P(arguments.length,2,"put","(number, PDFNet.Obj)",[[t,"number"],[e,"Object",p.Obj,"Obj"]]),p.sendWithPromise("NumberTree.put",{tree:this.id,key:t,value:e.id})},p.NumberTree.prototype.eraseKey=function(t){return P(arguments.length,1,"eraseKey","(number)",[[t,"number"]]),p.sendWithPromise("NumberTree.eraseKey",{tree:this.id,key:t})},p.NumberTree.prototype.erase=function(t){return P(arguments.length,1,"erase","(PDFNet.DictIterator)",[[t,"Object",p.DictIterator,"DictIterator"]]),p.sendWithPromise("NumberTree.erase",{tree:this.id,pos:t.id})},p.NumberTree.prototype.getSDFObj=function(){return p.sendWithPromise("NumberTree.getSDFObj",{tree:this.id}).then(function(t){return _(p.Obj,t)})},p.Obj.prototype.getType=function(){return p.sendWithPromise("Obj.getType",{o:this.id})},p.Obj.prototype.getDoc=function(){return p.sendWithPromise("Obj.getDoc",{o:this.id}).then(function(t){return _(p.SDFDoc,t)})},p.Obj.prototype.write=function(t){return P(arguments.length,1,"write","(PDFNet.FilterWriter)",[[t,"Object",p.FilterWriter,"FilterWriter"]]),p.sendWithPromise("Obj.write",{o:this.id,stream:t.id})},p.Obj.prototype.isEqual=function(t){return P(arguments.length,1,"isEqual","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("Obj.isEqual",{o:this.id,to:t.id})},p.Obj.prototype.isBool=function(){return p.sendWithPromise("Obj.isBool",{o:this.id})},p.Obj.prototype.getBool=function(){return p.sendWithPromise("Obj.getBool",{o:this.id})},p.Obj.prototype.setBool=function(t){return P(arguments.length,1,"setBool","(boolean)",[[t,"boolean"]]),p.sendWithPromise("Obj.setBool",{o:this.id,b:t})},p.Obj.prototype.isNumber=function(){return p.sendWithPromise("Obj.isNumber",{o:this.id})},p.Obj.prototype.getNumber=function(){return p.sendWithPromise("Obj.getNumber",{o:this.id})},p.Obj.prototype.setNumber=function(t){return P(arguments.length,1,"setNumber","(number)",[[t,"number"]]),p.sendWithPromise("Obj.setNumber",{o:this.id,n:t})},p.Obj.prototype.isNull=function(){return p.sendWithPromise("Obj.isNull",{o:this.id})},p.Obj.prototype.isString=function(){return p.sendWithPromise("Obj.isString",{o:this.id})},p.Obj.prototype.getRawBuffer=function(){return p.sendWithPromise("Obj.getRawBuffer",{o:this.id}).then(function(t){return new Uint8Array(t)})},p.Obj.prototype.setString=function(t){return P(arguments.length,1,"setString","(string)",[[t,"string"]]),p.sendWithPromise("Obj.setString",{o:this.id,value:t})},p.Obj.prototype.setUString=function(t){return P(arguments.length,1,"setUString","(string)",[[t,"string"]]),p.sendWithPromise("Obj.setUString",{o:this.id,value:t})},p.Obj.prototype.isName=function(){return p.sendWithPromise("Obj.isName",{o:this.id})},p.Obj.prototype.getName=function(){return p.sendWithPromise("Obj.getName",{o:this.id})},p.Obj.prototype.setName=function(t){return P(arguments.length,1,"setName","(string)",[[t,"string"]]),p.sendWithPromise("Obj.setName",{o:this.id,name:t})},p.Obj.prototype.isIndirect=function(){return p.sendWithPromise("Obj.isIndirect",{o:this.id})},p.Obj.prototype.getObjNum=function(){return p.sendWithPromise("Obj.getObjNum",{o:this.id})},p.Obj.prototype.getGenNum=function(){return p.sendWithPromise("Obj.getGenNum",{o:this.id})},p.Obj.prototype.getOffset=function(){return p.sendWithPromise("Obj.getOffset",{o:this.id})},p.Obj.prototype.isFree=function(){return p.sendWithPromise("Obj.isFree",{o:this.id})},p.Obj.prototype.setMark=function(t){return P(arguments.length,1,"setMark","(boolean)",[[t,"boolean"]]),p.sendWithPromise("Obj.setMark",{o:this.id,mark:t})},p.Obj.prototype.isMarked=function(){return p.sendWithPromise("Obj.isMarked",{o:this.id})},p.Obj.prototype.isLoaded=function(){return p.sendWithPromise("Obj.isLoaded",{o:this.id})},p.Obj.prototype.isContainer=function(){return p.sendWithPromise("Obj.isContainer",{o:this.id})},p.Obj.prototype.size=function(){return p.sendWithPromise("Obj.size",{o:this.id})},p.Obj.prototype.getDictIterator=function(){return p.sendWithPromise("Obj.getDictIterator",{o:this.id}).then(function(t){return S(p.DictIterator,t)})},p.Obj.prototype.isDict=function(){return p.sendWithPromise("Obj.isDict",{o:this.id})},p.Obj.prototype.find=function(t){return P(arguments.length,1,"find","(string)",[[t,"string"]]),p.sendWithPromise("Obj.find",{o:this.id,key:t}).then(function(t){return S(p.DictIterator,t)})},p.Obj.prototype.findObj=function(t){return P(arguments.length,1,"findObj","(string)",[[t,"string"]]),p.sendWithPromise("Obj.findObj",{o:this.id,key:t}).then(function(t){return _(p.Obj,t)})},p.Obj.prototype.get=function(t){return P(arguments.length,1,"get","(string)",[[t,"string"]]),p.sendWithPromise("Obj.get",{o:this.id,key:t}).then(function(t){return S(p.DictIterator,t)})},p.Obj.prototype.putName=function(t,e){return P(arguments.length,2,"putName","(string, string)",[[t,"string"],[e,"string"]]),p.sendWithPromise("Obj.putName",{o:this.id,key:t,name:e}).then(function(t){return _(p.Obj,t)})},p.Obj.prototype.putArray=function(t){return P(arguments.length,1,"putArray","(string)",[[t,"string"]]),p.sendWithPromise("Obj.putArray",{o:this.id,key:t}).then(function(t){return _(p.Obj,t)})},p.Obj.prototype.putBool=function(t,e){return P(arguments.length,2,"putBool","(string, boolean)",[[t,"string"],[e,"boolean"]]),p.sendWithPromise("Obj.putBool",{o:this.id,key:t,value:e}).then(function(t){return _(p.Obj,t)})},p.Obj.prototype.putDict=function(t){return P(arguments.length,1,"putDict","(string)",[[t,"string"]]),p.sendWithPromise("Obj.putDict",{o:this.id,key:t}).then(function(t){return _(p.Obj,t)})},p.Obj.prototype.putNumber=function(t,e){return P(arguments.length,2,"putNumber","(string, number)",[[t,"string"],[e,"number"]]),p.sendWithPromise("Obj.putNumber",{o:this.id,key:t,value:e}).then(function(t){return _(p.Obj,t)})},p.Obj.prototype.putString=function(t,e){return P(arguments.length,2,"putString","(string, string)",[[t,"string"],[e,"string"]]),p.sendWithPromise("Obj.putString",{o:this.id,key:t,value:e}).then(function(t){return _(p.Obj,t)})},p.Obj.prototype.putText=function(t,e){return P(arguments.length,2,"putText","(string, string)",[[t,"string"],[e,"string"]]),p.sendWithPromise("Obj.putText",{o:this.id,key:t,t:e}).then(function(t){return _(p.Obj,t)})},p.Obj.prototype.putNull=function(t){return P(arguments.length,1,"putNull","(string)",[[t,"string"]]),p.sendWithPromise("Obj.putNull",{o:this.id,key:t})},p.Obj.prototype.put=function(t,e){return P(arguments.length,2,"put","(string, PDFNet.Obj)",[[t,"string"],[e,"Object",p.Obj,"Obj"]]),p.sendWithPromise("Obj.put",{o:this.id,key:t,input_obj:e.id}).then(function(t){return _(p.Obj,t)})},p.Obj.prototype.putRect=function(t,e,n,i,r){return P(arguments.length,5,"putRect","(string, number, number, number, number)",[[t,"string"],[e,"number"],[n,"number"],[i,"number"],[r,"number"]]),p.sendWithPromise("Obj.putRect",{o:this.id,key:t,x1:e,y1:n,x2:i,y2:r}).then(function(t){return _(p.Obj,t)})},p.Obj.prototype.putMatrix=function(t,e){return P(arguments.length,2,"putMatrix","(string, PDFNet.Matrix2D)",[[t,"string"],[e,"Structure",p.Matrix2D,"Matrix2D"]]),b("putMatrix",[[e,1]]),p.sendWithPromise("Obj.putMatrix",{o:this.id,key:t,mtx:e}).then(function(t){return _(p.Obj,t)})},p.Obj.prototype.eraseFromKey=function(t){return P(arguments.length,1,"eraseFromKey","(string)",[[t,"string"]]),p.sendWithPromise("Obj.eraseFromKey",{o:this.id,key:t})},p.Obj.prototype.erase=function(t){return P(arguments.length,1,"erase","(PDFNet.DictIterator)",[[t,"Object",p.DictIterator,"DictIterator"]]),p.sendWithPromise("Obj.erase",{o:this.id,pos:t.id})},p.Obj.prototype.rename=function(t,e){return P(arguments.length,2,"rename","(string, string)",[[t,"string"],[e,"string"]]),p.sendWithPromise("Obj.rename",{o:this.id,old_key:t,new_key:e})},p.Obj.prototype.isArray=function(){return p.sendWithPromise("Obj.isArray",{o:this.id})},p.Obj.prototype.getAt=function(t){return P(arguments.length,1,"getAt","(number)",[[t,"number"]]),p.sendWithPromise("Obj.getAt",{o:this.id,index:t}).then(function(t){return _(p.Obj,t)})},p.Obj.prototype.insertName=function(t,e){return P(arguments.length,2,"insertName","(number, string)",[[t,"number"],[e,"string"]]),p.sendWithPromise("Obj.insertName",{o:this.id,pos:t,name:e}).then(function(t){return _(p.Obj,t)})},p.Obj.prototype.insertArray=function(t){return P(arguments.length,1,"insertArray","(number)",[[t,"number"]]),p.sendWithPromise("Obj.insertArray",{o:this.id,pos:t}).then(function(t){return _(p.Obj,t)})},p.Obj.prototype.insertBool=function(t,e){return P(arguments.length,2,"insertBool","(number, boolean)",[[t,"number"],[e,"boolean"]]),p.sendWithPromise("Obj.insertBool",{o:this.id,pos:t,value:e}).then(function(t){return _(p.Obj,t)})},p.Obj.prototype.insertDict=function(t){return P(arguments.length,1,"insertDict","(number)",[[t,"number"]]),p.sendWithPromise("Obj.insertDict",{o:this.id,pos:t}).then(function(t){return _(p.Obj,t)})},p.Obj.prototype.insertNumber=function(t,e){return P(arguments.length,2,"insertNumber","(number, number)",[[t,"number"],[e,"number"]]),p.sendWithPromise("Obj.insertNumber",{o:this.id,pos:t,value:e}).then(function(t){return _(p.Obj,t)})},p.Obj.prototype.insertString=function(t,e){return P(arguments.length,2,"insertString","(number, string)",[[t,"number"],[e,"string"]]),p.sendWithPromise("Obj.insertString",{o:this.id,pos:t,value:e}).then(function(t){return _(p.Obj,t)})},p.Obj.prototype.insertText=function(t,e){return P(arguments.length,2,"insertText","(number, string)",[[t,"number"],[e,"string"]]),p.sendWithPromise("Obj.insertText",{o:this.id,pos:t,t:e}).then(function(t){return _(p.Obj,t)})},p.Obj.prototype.insertNull=function(t){return P(arguments.length,1,"insertNull","(number)",[[t,"number"]]),p.sendWithPromise("Obj.insertNull",{o:this.id,pos:t}).then(function(t){return _(p.Obj,t)})},p.Obj.prototype.insert=function(t,e){return P(arguments.length,2,"insert","(number, PDFNet.Obj)",[[t,"number"],[e,"Object",p.Obj,"Obj"]]),p.sendWithPromise("Obj.insert",{o:this.id,pos:t,input_obj:e.id}).then(function(t){return _(p.Obj,t)})},p.Obj.prototype.insertRect=function(t,e,n,i,r){return P(arguments.length,5,"insertRect","(number, number, number, number, number)",[[t,"number"],[e,"number"],[n,"number"],[i,"number"],[r,"number"]]),p.sendWithPromise("Obj.insertRect",{o:this.id,pos:t,x1:e,y1:n,x2:i,y2:r}).then(function(t){return _(p.Obj,t)})},p.Obj.prototype.insertMatrix=function(t,e){return P(arguments.length,2,"insertMatrix","(number, PDFNet.Matrix2D)",[[t,"number"],[e,"Structure",p.Matrix2D,"Matrix2D"]]),b("insertMatrix",[[e,1]]),p.sendWithPromise("Obj.insertMatrix",{o:this.id,pos:t,mtx:e}).then(function(t){return _(p.Obj,t)})},p.Obj.prototype.pushBackName=function(t){return P(arguments.length,1,"pushBackName","(string)",[[t,"string"]]),p.sendWithPromise("Obj.pushBackName",{o:this.id,name:t}).then(function(t){return _(p.Obj,t)})},p.Obj.prototype.pushBackArray=function(){return p.sendWithPromise("Obj.pushBackArray",{o:this.id}).then(function(t){return _(p.Obj,t)})},p.Obj.prototype.pushBackBool=function(t){return P(arguments.length,1,"pushBackBool","(boolean)",[[t,"boolean"]]),p.sendWithPromise("Obj.pushBackBool",{o:this.id,value:t}).then(function(t){return _(p.Obj,t)})},p.Obj.prototype.pushBackDict=function(){return p.sendWithPromise("Obj.pushBackDict",{o:this.id}).then(function(t){return _(p.Obj,t)})},p.Obj.prototype.pushBackNumber=function(t){return P(arguments.length,1,"pushBackNumber","(number)",[[t,"number"]]),p.sendWithPromise("Obj.pushBackNumber",{o:this.id,value:t}).then(function(t){return _(p.Obj,t)})},p.Obj.prototype.pushBackString=function(t){return P(arguments.length,1,"pushBackString","(string)",[[t,"string"]]),p.sendWithPromise("Obj.pushBackString",{o:this.id,value:t}).then(function(t){return _(p.Obj,t)})},p.Obj.prototype.pushBackText=function(t){return P(arguments.length,1,"pushBackText","(string)",[[t,"string"]]),p.sendWithPromise("Obj.pushBackText",{o:this.id,t:t}).then(function(t){return _(p.Obj,t)})},p.Obj.prototype.pushBackNull=function(){return p.sendWithPromise("Obj.pushBackNull",{o:this.id}).then(function(t){return _(p.Obj,t)})},p.Obj.prototype.pushBack=function(t){return P(arguments.length,1,"pushBack","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("Obj.pushBack",{o:this.id,input_obj:t.id}).then(function(t){return _(p.Obj,t)})},p.Obj.prototype.pushBackRect=function(t,e,n,i){return P(arguments.length,4,"pushBackRect","(number, number, number, number)",[[t,"number"],[e,"number"],[n,"number"],[i,"number"]]),p.sendWithPromise("Obj.pushBackRect",{o:this.id,x1:t,y1:e,x2:n,y2:i}).then(function(t){return _(p.Obj,t)})},p.Obj.prototype.pushBackMatrix=function(t){return P(arguments.length,1,"pushBackMatrix","(PDFNet.Matrix2D)",[[t,"Structure",p.Matrix2D,"Matrix2D"]]),b("pushBackMatrix",[[t,0]]),p.sendWithPromise("Obj.pushBackMatrix",{o:this.id,mtx:t}).then(function(t){return _(p.Obj,t)})},p.Obj.prototype.eraseAt=function(t){return P(arguments.length,1,"eraseAt","(number)",[[t,"number"]]),p.sendWithPromise("Obj.eraseAt",{o:this.id,pos:t})},p.Obj.prototype.isStream=function(){return p.sendWithPromise("Obj.isStream",{o:this.id})},p.Obj.prototype.getRawStreamLength=function(){return p.sendWithPromise("Obj.getRawStreamLength",{o:this.id})},p.Obj.prototype.setStreamData=function(t){P(arguments.length,1,"setStreamData","(ArrayBuffer|TypedArray)",[[t,"ArrayBuffer"]]);var e=y(t,!1);return p.sendWithPromise("Obj.setStreamData",{obj:this.id,data_buf:e})},p.Obj.prototype.setStreamDataWithFilter=function(t,e){P(arguments.length,2,"setStreamDataWithFilter","(ArrayBuffer|TypedArray, PDFNet.Filter)",[[t,"ArrayBuffer"],[e,"Object",p.Filter,"Filter"]]);var n=y(t,!1);return 0!=e.id&&A(e.id),p.sendWithPromise("Obj.setStreamDataWithFilter",{obj:this.id,data_buf:n,no_own_filter_chain:e.id})},p.Obj.prototype.getRawStream=function(t){return P(arguments.length,1,"getRawStream","(boolean)",[[t,"boolean"]]),p.sendWithPromise("Obj.getRawStream",{o:this.id,decrypt:t}).then(function(t){return _(p.Filter,t)})},p.Obj.prototype.getDecodedStream=function(){return p.sendWithPromise("Obj.getDecodedStream",{o:this.id}).then(function(t){return _(p.Filter,t)})},p.ObjSet.create=function(){return p.sendWithPromise("objSetCreate",{}).then(function(t){return S(p.ObjSet,t)})},p.ObjSet.prototype.createName=function(t){return P(arguments.length,1,"createName","(string)",[[t,"string"]]),p.sendWithPromise("ObjSet.createName",{set:this.id,name:t}).then(function(t){return _(p.Obj,t)})},p.ObjSet.prototype.createArray=function(){return p.sendWithPromise("ObjSet.createArray",{set:this.id}).then(function(t){return _(p.Obj,t)})},p.ObjSet.prototype.createBool=function(t){return P(arguments.length,1,"createBool","(boolean)",[[t,"boolean"]]),p.sendWithPromise("ObjSet.createBool",{set:this.id,value:t}).then(function(t){return _(p.Obj,t)})},p.ObjSet.prototype.createDict=function(){return p.sendWithPromise("ObjSet.createDict",{set:this.id}).then(function(t){return _(p.Obj,t)})},p.ObjSet.prototype.createNull=function(){return p.sendWithPromise("ObjSet.createNull",{set:this.id}).then(function(t){return _(p.Obj,t)})},p.ObjSet.prototype.createNumber=function(t){return P(arguments.length,1,"createNumber","(number)",[[t,"number"]]),p.sendWithPromise("ObjSet.createNumber",{set:this.id,value:t}).then(function(t){return _(p.Obj,t)})},p.ObjSet.prototype.createString=function(t){return P(arguments.length,1,"createString","(string)",[[t,"string"]]),p.sendWithPromise("ObjSet.createString",{set:this.id,value:t}).then(function(t){return _(p.Obj,t)})},p.ObjSet.prototype.createFromJson=function(t){return P(arguments.length,1,"createFromJson","(string)",[[t,"string"]]),p.sendWithPromise("ObjSet.createFromJson",{set:this.id,json:t}).then(function(t){return _(p.Obj,t)})},p.SDFDoc.prototype.createShallowCopy=function(){return p.sendWithPromise("SDFDoc.createShallowCopy",{source:this.id}).then(function(t){return _(p.SDFDoc,t)})},p.SDFDoc.prototype.releaseFileHandles=function(){return p.sendWithPromise("SDFDoc.releaseFileHandles",{doc:this.id})},p.SDFDoc.prototype.isEncrypted=function(){return p.sendWithPromise("SDFDoc.isEncrypted",{doc:this.id})},p.SDFDoc.prototype.initStdSecurityHandlerUString=function(t){return P(arguments.length,1,"initStdSecurityHandlerUString","(string)",[[t,"string"]]),p.sendWithPromise("SDFDoc.initStdSecurityHandlerUString",{doc:this.id,password:t})},p.SDFDoc.prototype.isModified=function(){return p.sendWithPromise("SDFDoc.isModified",{doc:this.id})},p.SDFDoc.prototype.hasRepairedXRef=function(){return p.sendWithPromise("SDFDoc.hasRepairedXRef",{doc:this.id})},p.SDFDoc.prototype.isFullSaveRequired=function(){return p.sendWithPromise("SDFDoc.isFullSaveRequired",{doc:this.id})},p.SDFDoc.prototype.getTrailer=function(){return p.sendWithPromise("SDFDoc.getTrailer",{doc:this.id}).then(function(t){return _(p.Obj,t)})},p.SDFDoc.prototype.getObj=function(t){return P(arguments.length,1,"getObj","(number)",[[t,"number"]]),p.sendWithPromise("SDFDoc.getObj",{doc:this.id,obj_num:t}).then(function(t){return _(p.Obj,t)})},p.SDFDoc.prototype.importObj=function(t,e){return P(arguments.length,2,"importObj","(PDFNet.Obj, boolean)",[[t,"Object",p.Obj,"Obj"],[e,"boolean"]]),p.sendWithPromise("SDFDoc.importObj",{doc:this.id,obj:t.id,deep_copy:e}).then(function(t){return _(p.Obj,t)})},p.SDFDoc.prototype.importObjsWithExcludeList=function(t,e){return P(arguments.length,2,"importObjsWithExcludeList","(Array<Core.PDFNet.Obj>, Array<Core.PDFNet.Obj>)",[[t,"Array"],[e,"Array"]]),t=Array.from(t,function(t){return t.id}),e=Array.from(e,function(t){return t.id}),p.sendWithPromise("SDFDoc.importObjsWithExcludeList",{doc:this.id,obj_list:t,exclude_list:e}).then(function(t){return _(p.Obj,t)})},p.SDFDoc.prototype.xRefSize=function(){return p.sendWithPromise("SDFDoc.xRefSize",{doc:this.id})},p.SDFDoc.prototype.clearMarks=function(){return p.sendWithPromise("SDFDoc.clearMarks",{doc:this.id})},p.SDFDoc.prototype.saveMemory=function(t,e){return P(arguments.length,2,"saveMemory","(number, string)",[[t,"number"],[e,"string"]]),p.sendWithPromise("SDFDoc.saveMemory",{doc:this.id,flags:t,header:e}).then(function(t){return new Uint8Array(t)})},p.SDFDoc.prototype.saveStream=function(t,e,n){return P(arguments.length,3,"saveStream","(PDFNet.Filter, number, string)",[[t,"Object",p.Filter,"Filter"],[e,"number"],[n,"string"]]),p.sendWithPromise("SDFDoc.saveStream",{doc:this.id,stream:t.id,flags:e,header:n})},p.SDFDoc.prototype.getHeader=function(){return p.sendWithPromise("SDFDoc.getHeader",{doc:this.id})},p.SDFDoc.prototype.getSecurityHandler=function(){return p.sendWithPromise("SDFDoc.getSecurityHandler",{doc:this.id}).then(function(t){return _(p.SecurityHandler,t)})},p.SDFDoc.prototype.setSecurityHandler=function(t){return P(arguments.length,1,"setSecurityHandler","(PDFNet.SecurityHandler)",[[t,"Object",p.SecurityHandler,"SecurityHandler"]]),0!=t.id&&A(t.id),p.sendWithPromise("SDFDoc.setSecurityHandler",{doc:this.id,no_own_handler:t.id})},p.SDFDoc.prototype.removeSecurity=function(){return p.sendWithPromise("SDFDoc.removeSecurity",{doc:this.id})},p.SDFDoc.prototype.swap=function(t,e){return P(arguments.length,2,"swap","(number, number)",[[t,"number"],[e,"number"]]),p.sendWithPromise("SDFDoc.swap",{doc:this.id,obj_num1:t,obj_num2:e})},p.SDFDoc.prototype.isLinearized=function(){return p.sendWithPromise("SDFDoc.isLinearized",{doc:this.id})},p.SDFDoc.prototype.getLinearizationDict=function(){return p.sendWithPromise("SDFDoc.getLinearizationDict",{doc:this.id}).then(function(t){return _(p.Obj,t)})},p.SDFDoc.prototype.getHintStream=function(){return p.sendWithPromise("SDFDoc.getHintStream",{doc:this.id}).then(function(t){return _(p.Obj,t)})},p.SDFDoc.prototype.enableDiskCaching=function(t){return P(arguments.length,1,"enableDiskCaching","(boolean)",[[t,"boolean"]]),p.sendWithPromise("SDFDoc.enableDiskCaching",{doc:this.id,use_cache_flag:t})},p.SDFDoc.prototype.lock=function(){var t=this;return p.sendWithPromise("SDFDoc.lock",{doc:this.id}).then(function(){s.push({name:"SDFDoc",id:t.id,unlocktype:"unlock"})})},p.SDFDoc.prototype.unlock=function(){var t=this;return p.sendWithPromise("SDFDoc.unlock",{doc:this.id}).then(function(){O(t)})},p.SDFDoc.prototype.lockRead=function(){var t=this;return p.sendWithPromise("SDFDoc.lockRead",{doc:this.id}).then(function(){s.push({name:"SDFDoc",id:t.id,unlocktype:"unlockRead"})})},p.SDFDoc.prototype.unlockRead=function(){var t=this;return p.sendWithPromise("SDFDoc.unlockRead",{doc:this.id}).then(function(){O(t)})},p.SDFDoc.prototype.tryLock=function(){var e=this;return p.sendWithPromise("SDFDoc.tryLock",{doc:this.id}).then(function(t){t&&s.push({name:"SDFDoc",id:e.id,unlocktype:"unlock"})})},p.SDFDoc.prototype.tryLockRead=function(){var e=this;return p.sendWithPromise("SDFDoc.tryLockRead",{doc:this.id}).then(function(t){t&&s.push({name:"SDFDoc",id:e.id,unlocktype:"unlockRead"})})},p.SDFDoc.prototype.getFileName=function(){return p.sendWithPromise("SDFDoc.getFileName",{doc:this.id})},p.SDFDoc.prototype.createIndirectName=function(t){return P(arguments.length,1,"createIndirectName","(string)",[[t,"string"]]),p.sendWithPromise("SDFDoc.createIndirectName",{doc:this.id,name:t}).then(function(t){return _(p.Obj,t)})},p.SDFDoc.prototype.createIndirectArray=function(){return p.sendWithPromise("SDFDoc.createIndirectArray",{doc:this.id}).then(function(t){return _(p.Obj,t)})},p.SDFDoc.prototype.createIndirectBool=function(t){return P(arguments.length,1,"createIndirectBool","(boolean)",[[t,"boolean"]]),p.sendWithPromise("SDFDoc.createIndirectBool",{doc:this.id,value:t}).then(function(t){return _(p.Obj,t)})},p.SDFDoc.prototype.createIndirectDict=function(){return p.sendWithPromise("SDFDoc.createIndirectDict",{doc:this.id}).then(function(t){return _(p.Obj,t)})},p.SDFDoc.prototype.createIndirectNull=function(){return p.sendWithPromise("SDFDoc.createIndirectNull",{doc:this.id}).then(function(t){return _(p.Obj,t)})},p.SDFDoc.prototype.createIndirectNumber=function(t){return P(arguments.length,1,"createIndirectNumber","(number)",[[t,"number"]]),p.sendWithPromise("SDFDoc.createIndirectNumber",{doc:this.id,value:t}).then(function(t){return _(p.Obj,t)})},p.SDFDoc.prototype.createIndirectString=function(t){P(arguments.length,1,"createIndirectString","(ArrayBuffer|TypedArray)",[[t,"ArrayBuffer"]]);var e=y(t,!1);return p.sendWithPromise("SDFDoc.createIndirectString",{doc:this.id,buf_value:e}).then(function(t){return _(p.Obj,t)})},p.SDFDoc.prototype.createIndirectStringFromUString=function(t){return P(arguments.length,1,"createIndirectStringFromUString","(string)",[[t,"string"]]),p.sendWithPromise("SDFDoc.createIndirectStringFromUString",{doc:this.id,str:t}).then(function(t){return _(p.Obj,t)})},p.SDFDoc.prototype.createIndirectStreamFromFilter=function(t,e){return void 0===e&&(e=new p.Filter("0")),P(arguments.length,1,"createIndirectStreamFromFilter","(PDFNet.FilterReader, PDFNet.Filter)",[[t,"Object",p.FilterReader,"FilterReader"],[e,"Object",p.Filter,"Filter"]]),0!=e.id&&A(e.id),p.sendWithPromise("SDFDoc.createIndirectStreamFromFilter",{doc:this.id,data:t.id,no_own_filter_chain:e.id}).then(function(t){return _(p.Obj,t)})},p.SDFDoc.prototype.createIndirectStream=function(t,e){P(arguments.length,2,"createIndirectStream","(ArrayBuffer|TypedArray, PDFNet.Filter)",[[t,"ArrayBuffer"],[e,"Object",p.Filter,"Filter"]]);var n=y(t,!1);return 0!=e.id&&A(e.id),p.sendWithPromise("SDFDoc.createIndirectStream",{doc:this.id,data_buf:n,no_own_filter_chain:e.id}).then(function(t){return _(p.Obj,t)})},p.SecurityHandler.prototype.getPermission=function(t){return P(arguments.length,1,"getPermission","(number)",[[t,"number"]]),p.sendWithPromise("SecurityHandler.getPermission",{sh:this.id,p:t})},p.SecurityHandler.prototype.getKeyLength=function(){return p.sendWithPromise("SecurityHandler.getKeyLength",{sh:this.id})},p.SecurityHandler.prototype.getEncryptionAlgorithmID=function(){return p.sendWithPromise("SecurityHandler.getEncryptionAlgorithmID",{sh:this.id})},p.SecurityHandler.prototype.getHandlerDocName=function(){return p.sendWithPromise("SecurityHandler.getHandlerDocName",{sh:this.id})},p.SecurityHandler.prototype.isModified=function(){return p.sendWithPromise("SecurityHandler.isModified",{sh:this.id})},p.SecurityHandler.prototype.setModified=function(t){return void 0===t&&(t=!0),P(arguments.length,0,"setModified","(boolean)",[[t,"boolean"]]),p.sendWithPromise("SecurityHandler.setModified",{sh:this.id,is_modified:t})},p.SecurityHandler.create=function(t){return P(arguments.length,1,"create","(number)",[[t,"number"]]),p.sendWithPromise("securityHandlerCreate",{crypt_type:t}).then(function(t){return S(p.SecurityHandler,t)})},p.SecurityHandler.createFromEncCode=function(t,e,n){return P(arguments.length,3,"createFromEncCode","(string, number, number)",[[t,"string"],[e,"number"],[n,"number"]]),p.sendWithPromise("securityHandlerCreateFromEncCode",{name:t,key_len:e,enc_code:n}).then(function(t){return S(p.SecurityHandler,t)})},p.SecurityHandler.createDefault=function(){return p.sendWithPromise("securityHandlerCreateDefault",{}).then(function(t){return S(p.SecurityHandler,t)})},p.SecurityHandler.prototype.setPermission=function(t,e){return P(arguments.length,2,"setPermission","(number, boolean)",[[t,"number"],[e,"boolean"]]),p.sendWithPromise("SecurityHandler.setPermission",{sh:this.id,perm:t,value:e})},p.SecurityHandler.prototype.changeRevisionNumber=function(t){return P(arguments.length,1,"changeRevisionNumber","(number)",[[t,"number"]]),p.sendWithPromise("SecurityHandler.changeRevisionNumber",{sh:this.id,rev_num:t})},p.SecurityHandler.prototype.setEncryptMetadata=function(t){return P(arguments.length,1,"setEncryptMetadata","(boolean)",[[t,"boolean"]]),p.sendWithPromise("SecurityHandler.setEncryptMetadata",{sh:this.id,encrypt_metadata:t})},p.SecurityHandler.prototype.getRevisionNumber=function(){return p.sendWithPromise("SecurityHandler.getRevisionNumber",{sh:this.id})},p.SecurityHandler.prototype.isUserPasswordRequired=function(){return p.sendWithPromise("SecurityHandler.isUserPasswordRequired",{sh:this.id})},p.SecurityHandler.prototype.isMasterPasswordRequired=function(){return p.sendWithPromise("SecurityHandler.isMasterPasswordRequired",{sh:this.id})},p.SecurityHandler.prototype.isAES=function(){return p.sendWithPromise("SecurityHandler.isAES",{sh:this.id})},p.SecurityHandler.prototype.isAESObj=function(t){return P(arguments.length,1,"isAESObj","(PDFNet.Obj)",[[t,"Object",p.Obj,"Obj"]]),p.sendWithPromise("SecurityHandler.isAESObj",{sh:this.id,stream:t.id})};p.SecurityHandler.prototype.isRC4=function(){return p.sendWithPromise("SecurityHandler.isRC4",{sh:this.id})},p.SecurityHandler.prototype.changeUserPasswordUString=function(t){return P(arguments.length,1,"changeUserPasswordUString","(string)",[[t,"string"]]),p.sendWithPromise("SecurityHandler.changeUserPasswordUString",{sh:this.id,password:t})},p.SecurityHandler.prototype.changeUserPasswordBuffer=function(t){P(arguments.length,1,"changeUserPasswordBuffer","(ArrayBuffer|TypedArray)",[[t,"ArrayBuffer"]]);var e=y(t,!1);return p.sendWithPromise("SecurityHandler.changeUserPasswordBuffer",{sh:this.id,password_buf:e})},p.SecurityHandler.prototype.changeMasterPasswordUString=function(t){return P(arguments.length,1,"changeMasterPasswordUString","(string)",[[t,"string"]]),p.sendWithPromise("SecurityHandler.changeMasterPasswordUString",{sh:this.id,password:t})},p.SecurityHandler.prototype.changeMasterPasswordBuffer=function(t){P(arguments.length,1,"changeMasterPasswordBuffer","(ArrayBuffer|TypedArray)",[[t,"ArrayBuffer"]]);var e=y(t,!1);return p.sendWithPromise("SecurityHandler.changeMasterPasswordBuffer",{sh:this.id,password_buf:e})},p.SecurityHandler.prototype.initPasswordUString=function(t){return P(arguments.length,1,"initPasswordUString","(string)",[[t,"string"]]),p.sendWithPromise("SecurityHandler.initPasswordUString",{sh:this.id,password:t})},p.SecurityHandler.prototype.initPasswordBuffer=function(t){P(arguments.length,1,"initPasswordBuffer","(ArrayBuffer|TypedArray)",[[t,"ArrayBuffer"]]);var e=y(t,!1);return p.sendWithPromise("SecurityHandler.initPasswordBuffer",{sh:this.id,password_buf:e})},p.SignatureHandler.prototype.getName=function(){return p.sendWithPromise("SignatureHandler.getName",{signature_handler:this.id})},p.SignatureHandler.prototype.reset=function(){return p.sendWithPromise("SignatureHandler.reset",{signature_handler:this.id})},p.SignatureHandler.prototype.destructor=function(){return p.sendWithPromise("SignatureHandler.destructor",{signature_handler:this.id})},p.UndoManager.prototype.discardAllSnapshots=function(){return p.sendWithPromise("UndoManager.discardAllSnapshots",{self:this.id}).then(function(t){return S(p.DocSnapshot,t)})},p.UndoManager.prototype.undo=function(){return p.sendWithPromise("UndoManager.undo",{self:this.id}).then(function(t){return S(p.ResultSnapshot,t)})},p.UndoManager.prototype.canUndo=function(){return p.sendWithPromise("UndoManager.canUndo",{self:this.id})},p.UndoManager.prototype.getNextUndoSnapshot=function(){return p.sendWithPromise("UndoManager.getNextUndoSnapshot",{self:this.id}).then(function(t){return S(p.DocSnapshot,t)})},p.UndoManager.prototype.redo=function(){return p.sendWithPromise("UndoManager.redo",{self:this.id}).then(function(t){return S(p.ResultSnapshot,t)})},p.UndoManager.prototype.canRedo=function(){return p.sendWithPromise("UndoManager.canRedo",{self:this.id})},p.UndoManager.prototype.getNextRedoSnapshot=function(){return p.sendWithPromise("UndoManager.getNextRedoSnapshot",{self:this.id}).then(function(t){return S(p.DocSnapshot,t)})},p.UndoManager.prototype.takeSnapshot=function(){return p.sendWithPromise("UndoManager.takeSnapshot",{self:this.id}).then(function(t){return S(p.ResultSnapshot,t)})},p.ResultSnapshot.prototype.currentState=function(){return p.sendWithPromise("ResultSnapshot.currentState",{self:this.id}).then(function(t){return S(p.DocSnapshot,t)})},p.ResultSnapshot.prototype.previousState=function(){return p.sendWithPromise("ResultSnapshot.previousState",{self:this.id}).then(function(t){return S(p.DocSnapshot,t)})},p.ResultSnapshot.prototype.isOk=function(){return p.sendWithPromise("ResultSnapshot.isOk",{self:this.id})},p.ResultSnapshot.prototype.isNullTransition=function(){return p.sendWithPromise("ResultSnapshot.isNullTransition",{self:this.id})},p.DocSnapshot.prototype.getHash=function(){return p.sendWithPromise("DocSnapshot.getHash",{self:this.id})},p.DocSnapshot.prototype.isValid=function(){return p.sendWithPromise("DocSnapshot.isValid",{self:this.id})},p.DocSnapshot.prototype.equals=function(t){return P(arguments.length,1,"equals","(PDFNet.DocSnapshot)",[[t,"Object",p.DocSnapshot,"DocSnapshot"]]),p.sendWithPromise("DocSnapshot.equals",{self:this.id,snapshot:t.id})},p.OCRModule.applyOCRJsonToPDF=function(t,e){return P(arguments.length,2,"applyOCRJsonToPDF","(PDFNet.PDFDoc, string)",[[t,"PDFDoc"],[e,"string"]]),p.sendWithPromise("ocrModuleApplyOCRJsonToPDF",{dst:t.id,json:e})},p.OCRModule.applyOCRXmlToPDF=function(t,e){return P(arguments.length,2,"applyOCRXmlToPDF","(PDFNet.PDFDoc, string)",[[t,"PDFDoc"],[e,"string"]]),p.sendWithPromise("ocrModuleApplyOCRXmlToPDF",{dst:t.id,xml:e})},p.VerificationOptions.create=function(t){return P(arguments.length,1,"create","(number)",[[t,"number"]]),p.sendWithPromise("verificationOptionsCreate",{in_level:t}).then(function(t){return S(p.VerificationOptions,t)})},p.VerificationOptions.prototype.addTrustedCertificate=function(t,e){void 0===e&&(e=p.VerificationOptions.CertificateTrustFlag.e_default_trust),P(arguments.length,1,"addTrustedCertificate","(ArrayBuffer|TypedArray, number)",[[t,"ArrayBuffer"],[e,"number"]]);var n=y(t,!1);return p.sendWithPromise("VerificationOptions.addTrustedCertificate",{self:this.id,in_certificate_buf:n,in_trust_flags:e})},p.VerificationOptions.prototype.addTrustedCertificates=function(t){P(arguments.length,1,"addTrustedCertificates","(ArrayBuffer|TypedArray)",[[t,"ArrayBuffer"]]);var e=y(t,!1);return p.sendWithPromise("VerificationOptions.addTrustedCertificates",{self:this.id,in_P7C_binary_DER_certificates_file_data_buf:e})},p.VerificationOptions.prototype.loadTrustList=function(t){return P(arguments.length,1,"loadTrustList","(PDFNet.FDFDoc)",[[t,"FDFDoc"]]),p.sendWithPromise("VerificationOptions.loadTrustList",{self:this.id,in_fdf_cert_exchange_data:t.id})},p.VerificationOptions.prototype.enableModificationVerification=function(t){return P(arguments.length,1,"enableModificationVerification","(boolean)",[[t,"boolean"]]),p.sendWithPromise("VerificationOptions.enableModificationVerification",{self:this.id,in_on_or_off:t})},p.VerificationOptions.prototype.enableDigestVerification=function(t){return P(arguments.length,1,"enableDigestVerification","(boolean)",[[t,"boolean"]]),p.sendWithPromise("VerificationOptions.enableDigestVerification",{self:this.id,in_on_or_off:t})},p.VerificationOptions.prototype.enableTrustVerification=function(t){return P(arguments.length,1,"enableTrustVerification","(boolean)",[[t,"boolean"]]),p.sendWithPromise("VerificationOptions.enableTrustVerification",{self:this.id,in_on_or_off:t})},p.VerificationOptions.prototype.setRevocationProxyPrefix=function(t){return P(arguments.length,1,"setRevocationProxyPrefix","(string)",[[t,"string"]]),p.sendWithPromise("VerificationOptions.setRevocationProxyPrefix",{self:this.id,in_str:t})},p.VerificationOptions.prototype.setRevocationTimeout=function(t){return P(arguments.length,1,"setRevocationTimeout","(number)",[[t,"number"]]),p.sendWithPromise("VerificationOptions.setRevocationTimeout",{self:this.id,in_revocation_timeout_milliseconds:t})},p.VerificationOptions.prototype.enableOnlineCRLRevocationChecking=function(t){return P(arguments.length,1,"enableOnlineCRLRevocationChecking","(boolean)",[[t,"boolean"]]),p.sendWithPromise("VerificationOptions.enableOnlineCRLRevocationChecking",{self:this.id,in_on_or_off:t})},p.VerificationOptions.prototype.enableOnlineOCSPRevocationChecking=function(t){return P(arguments.length,1,"enableOnlineOCSPRevocationChecking","(boolean)",[[t,"boolean"]]),p.sendWithPromise("VerificationOptions.enableOnlineOCSPRevocationChecking",{self:this.id,in_on_or_off:t})},p.VerificationOptions.prototype.enableOnlineRevocationChecking=function(t){return P(arguments.length,1,"enableOnlineRevocationChecking","(boolean)",[[t,"boolean"]]),p.sendWithPromise("VerificationOptions.enableOnlineRevocationChecking",{self:this.id,in_on_or_off:t})},p.VerificationOptions.prototype.getTrustedCertificateCount=function(){return p.sendWithPromise("VerificationOptions.getTrustedCertificateCount",{self:this.id})},p.VerificationOptions.prototype.getTrustedCertificate=function(t){return P(arguments.length,1,"getTrustedCertificate","(number)",[[t,"number"]]),p.sendWithPromise("VerificationOptions.getTrustedCertificate",{self:this.id,index:t}).then(function(t){return S(p.X509Certificate,t)})},p.VerificationResult.prototype.getDigitalSignatureField=function(){return p.sendWithPromise("VerificationResult.getDigitalSignatureField",{self:this.id}).then(function(t){return new p.DigitalSignatureField(t)})},p.VerificationResult.prototype.getVerificationStatus=function(){return p.sendWithPromise("VerificationResult.getVerificationStatus",{self:this.id})},p.VerificationResult.prototype.getDocumentStatus=function(){return p.sendWithPromise("VerificationResult.getDocumentStatus",{self:this.id})},p.VerificationResult.prototype.getDigestStatus=function(){return p.sendWithPromise("VerificationResult.getDigestStatus",{self:this.id})},p.VerificationResult.prototype.getTrustStatus=function(){return p.sendWithPromise("VerificationResult.getTrustStatus",{self:this.id})},p.VerificationResult.prototype.getPermissionsStatus=function(){return p.sendWithPromise("VerificationResult.getPermissionsStatus",{self:this.id})},p.VerificationResult.prototype.getTrustVerificationResult=function(){return p.sendWithPromise("VerificationResult.getTrustVerificationResult",{self:this.id}).then(function(t){return S(p.TrustVerificationResult,t)})},p.VerificationResult.prototype.hasTrustVerificationResult=function(){return p.sendWithPromise("VerificationResult.hasTrustVerificationResult",{self:this.id})},p.VerificationResult.prototype.getDisallowedChanges=function(){return p.sendWithPromise("VerificationResult.getDisallowedChanges",{self:this.id}).then(function(t){return t.map(function(t){return S(p.DisallowedChange,t)})})},p.VerificationResult.prototype.getDigestAlgorithm=function(){return p.sendWithPromise("VerificationResult.getDigestAlgorithm",{self:this.id})},p.VerificationResult.prototype.getDocumentStatusAsString=function(){return p.sendWithPromise("VerificationResult.getDocumentStatusAsString",{self:this.id})},p.VerificationResult.prototype.getDigestStatusAsString=function(){return p.sendWithPromise("VerificationResult.getDigestStatusAsString",{self:this.id})},p.VerificationResult.prototype.getTrustStatusAsString=function(){return p.sendWithPromise("VerificationResult.getTrustStatusAsString",{self:this.id})},p.VerificationResult.prototype.getPermissionsStatusAsString=function(){return p.sendWithPromise("VerificationResult.getPermissionsStatusAsString",{self:this.id})},p.VerificationResult.prototype.getUnsupportedFeatures=function(){return p.sendWithPromise("VerificationResult.getUnsupportedFeatures",{self:this.id})},p.EmbeddedTimestampVerificationResult.prototype.getVerificationStatus=function(){return p.sendWithPromise("EmbeddedTimestampVerificationResult.getVerificationStatus",{self:this.id})},p.EmbeddedTimestampVerificationResult.prototype.getCMSDigestStatus=function(){return p.sendWithPromise("EmbeddedTimestampVerificationResult.getCMSDigestStatus",{self:this.id})},p.EmbeddedTimestampVerificationResult.prototype.getMessageImprintDigestStatus=function(){return p.sendWithPromise("EmbeddedTimestampVerificationResult.getMessageImprintDigestStatus",{self:this.id})},p.EmbeddedTimestampVerificationResult.prototype.getTrustStatus=function(){return p.sendWithPromise("EmbeddedTimestampVerificationResult.getTrustStatus",{self:this.id})},p.EmbeddedTimestampVerificationResult.prototype.getCMSDigestStatusAsString=function(){return p.sendWithPromise("EmbeddedTimestampVerificationResult.getCMSDigestStatusAsString",{self:this.id})},p.EmbeddedTimestampVerificationResult.prototype.getMessageImprintDigestStatusAsString=function(){return p.sendWithPromise("EmbeddedTimestampVerificationResult.getMessageImprintDigestStatusAsString",{self:this.id})},p.EmbeddedTimestampVerificationResult.prototype.getTrustStatusAsString=function(){return p.sendWithPromise("EmbeddedTimestampVerificationResult.getTrustStatusAsString",{self:this.id})},p.EmbeddedTimestampVerificationResult.prototype.hasTrustVerificationResult=function(){return p.sendWithPromise("EmbeddedTimestampVerificationResult.hasTrustVerificationResult",{self:this.id})},p.EmbeddedTimestampVerificationResult.prototype.getTrustVerificationResult=function(){return p.sendWithPromise("EmbeddedTimestampVerificationResult.getTrustVerificationResult",{self:this.id}).then(function(t){return S(p.TrustVerificationResult,t)})},p.EmbeddedTimestampVerificationResult.prototype.getCMSSignatureDigestAlgorithm=function(){return p.sendWithPromise("EmbeddedTimestampVerificationResult.getCMSSignatureDigestAlgorithm",{self:this.id})},p.EmbeddedTimestampVerificationResult.prototype.getMessageImprintDigestAlgorithm=function(){return p.sendWithPromise("EmbeddedTimestampVerificationResult.getMessageImprintDigestAlgorithm",{self:this.id})},p.EmbeddedTimestampVerificationResult.prototype.getUnsupportedFeatures=function(){return p.sendWithPromise("EmbeddedTimestampVerificationResult.getUnsupportedFeatures",{self:this.id})},p.TrustVerificationResult.prototype.wasSuccessful=function(){return p.sendWithPromise("TrustVerificationResult.wasSuccessful",{self:this.id})},p.TrustVerificationResult.prototype.getResultString=function(){return p.sendWithPromise("TrustVerificationResult.getResultString",{self:this.id})},p.TrustVerificationResult.prototype.getTimeOfTrustVerification=function(){return p.sendWithPromise("TrustVerificationResult.getTimeOfTrustVerification",{self:this.id})},p.TrustVerificationResult.prototype.getTimeOfTrustVerificationEnum=function(){return p.sendWithPromise("TrustVerificationResult.getTimeOfTrustVerificationEnum",{self:this.id})},p.TrustVerificationResult.prototype.hasEmbeddedTimestampVerificationResult=function(){return p.sendWithPromise("TrustVerificationResult.hasEmbeddedTimestampVerificationResult",{self:this.id})},p.TrustVerificationResult.prototype.getEmbeddedTimestampVerificationResult=function(){return p.sendWithPromise("TrustVerificationResult.getEmbeddedTimestampVerificationResult",{self:this.id}).then(function(t){return S(p.EmbeddedTimestampVerificationResult,t)})},p.TrustVerificationResult.prototype.getCertPath=function(){return p.sendWithPromise("TrustVerificationResult.getCertPath",{self:this.id}).then(function(t){return t.map(function(t){return S(p.X509Certificate,t)})})},p.DisallowedChange.prototype.getObjNum=function(){return p.sendWithPromise("DisallowedChange.getObjNum",{self:this.id})},p.DisallowedChange.prototype.getType=function(){return p.sendWithPromise("DisallowedChange.getType",{self:this.id})},p.DisallowedChange.prototype.getTypeAsString=function(){return p.sendWithPromise("DisallowedChange.getTypeAsString",{self:this.id})},p.X509Extension.prototype.isCritical=function(){return p.sendWithPromise("X509Extension.isCritical",{self:this.id})},p.X509Extension.prototype.getObjectIdentifier=function(){return p.sendWithPromise("X509Extension.getObjectIdentifier",{self:this.id}).then(function(t){return S(p.ObjectIdentifier,t)})},p.X509Extension.prototype.toString=function(){return p.sendWithPromise("X509Extension.toString",{self:this.id})},p.X509Extension.prototype.getData=function(){return p.sendWithPromise("X509Extension.getData",{self:this.id}).then(function(t){return new Uint8Array(t)})},p.X501AttributeTypeAndValue.prototype.getAttributeTypeOID=function(){return p.sendWithPromise("X501AttributeTypeAndValue.getAttributeTypeOID",{self:this.id}).then(function(t){return S(p.ObjectIdentifier,t)})},p.X501AttributeTypeAndValue.prototype.getStringValue=function(){return p.sendWithPromise("X501AttributeTypeAndValue.getStringValue",{self:this.id})},p.ByteRange.prototype.getStartOffset=function(){return F("getStartOffset",this.yieldFunction),p.sendWithPromise("ByteRange.getStartOffset",{self:this})},p.ByteRange.prototype.getEndOffset=function(){return F("getEndOffset",this.yieldFunction),p.sendWithPromise("ByteRange.getEndOffset",{self:this})},p.ByteRange.prototype.getSize=function(){return F("getSize",this.yieldFunction),p.sendWithPromise("ByteRange.getSize",{self:this})},p.TimestampingResult.prototype.getStatus=function(){return p.sendWithPromise("TimestampingResult.getStatus",{self:this.id})},p.TimestampingResult.prototype.getString=function(){return p.sendWithPromise("TimestampingResult.getString",{self:this.id})},p.TimestampingResult.prototype.hasResponseVerificationResult=function(){return p.sendWithPromise("TimestampingResult.hasResponseVerificationResult",{self:this.id})},p.TimestampingResult.prototype.getResponseVerificationResult=function(){return p.sendWithPromise("TimestampingResult.getResponseVerificationResult",{self:this.id}).then(function(t){return S(p.EmbeddedTimestampVerificationResult,t)})},p.TimestampingResult.prototype.getData=function(){return p.sendWithPromise("TimestampingResult.getData",{self:this.id}).then(function(t){return new Uint8Array(t)})},p.ActionParameter.create=function(t){return P(arguments.length,1,"create","(PDFNet.Action)",[[t,"Object",p.Action,"Action"]]),p.sendWithPromise("actionParameterCreate",{action:t.id}).then(function(t){return S(p.ActionParameter,t)})},p.Action.prototype.parameterCreateWithField=function(t){return P(arguments.length,1,"parameterCreateWithField","(PDFNet.Field)",[[t,"Structure",p.Field,"Field"]]),b("parameterCreateWithField",[[t,0]]),p.sendWithPromise("Action.parameterCreateWithField",{action:this.id,field:t}).then(function(t){return S(p.ActionParameter,t)})},p.Action.prototype.parameterCreateWithAnnot=function(t){return P(arguments.length,1,"parameterCreateWithAnnot","(PDFNet.Annot)",[[t,"Object",p.Annot,"Annot"]]),p.sendWithPromise("Action.parameterCreateWithAnnot",{action:this.id,annot:t.id}).then(function(t){return S(p.ActionParameter,t)})},p.Action.prototype.parameterCreateWithPage=function(t){return P(arguments.length,1,"parameterCreateWithPage","(PDFNet.Page)",[[t,"Object",p.Page,"Page"]]),p.sendWithPromise("Action.parameterCreateWithPage",{action:this.id,page:t.id}).then(function(t){return S(p.ActionParameter,t)})},p.ActionParameter.prototype.getAction=function(){return p.sendWithPromise("ActionParameter.getAction",{ap:this.id}).then(function(t){return _(p.Action,t)})},p.ViewChangeCollection.create=function(){return p.sendWithPromise("viewChangeCollectionCreate",{}).then(function(t){return S(p.ViewChangeCollection,t)})},p.RadioButtonGroup.createFromField=function(t){return P(arguments.length,1,"createFromField","(PDFNet.Field)",[[t,"Structure",p.Field,"Field"]]),b("createFromField",[[t,0]]),p.sendWithPromise("radioButtonGroupCreateFromField",{field:t}).then(function(t){return S(p.RadioButtonGroup,t)})},p.RadioButtonGroup.create=function(t,e){return void 0===e&&(e=""),P(arguments.length,1,"create","(PDFNet.PDFDoc, string)",[[t,"PDFDoc"],[e,"string"]]),p.sendWithPromise("radioButtonGroupCreate",{doc:t.id,field_name:e}).then(function(t){return S(p.RadioButtonGroup,t)})},p.RadioButtonGroup.prototype.copy=function(){return p.sendWithPromise("RadioButtonGroup.copy",{group:this.id}).then(function(t){return S(p.RadioButtonGroup,t)})},p.RadioButtonGroup.prototype.add=function(t,e){return void 0===e&&(e=""),P(arguments.length,1,"add","(PDFNet.Rect, string)",[[t,"Structure",p.Rect,"Rect"],[e,"const char* = 0"]]),b("add",[[t,0]]),p.sendWithPromise("RadioButtonGroup.add",{group:this.id,pos:t,onstate:e}).then(function(t){return _(p.RadioButtonWidget,t)})},p.RadioButtonGroup.prototype.getNumButtons=function(){return p.sendWithPromise("RadioButtonGroup.getNumButtons",{group:this.id})},p.RadioButtonGroup.prototype.getButton=function(t){return P(arguments.length,1,"getButton","(number)",[[t,"number"]]),p.sendWithPromise("RadioButtonGroup.getButton",{group:this.id,index:t}).then(function(t){return _(p.RadioButtonWidget,t)})},p.RadioButtonGroup.prototype.getField=function(){return p.sendWithPromise("RadioButtonGroup.getField",{group:this.id}).then(function(t){return new p.Field(t)})},p.RadioButtonGroup.prototype.addGroupButtonsToPage=function(t){return P(arguments.length,1,"addGroupButtonsToPage","(PDFNet.Page)",[[t,"Object",p.Page,"Page"]]),p.sendWithPromise("RadioButtonGroup.addGroupButtonsToPage",{group:this.id,page:t.id})},p.PDFTronCustomSecurityHandler.create=function(t){return P(arguments.length,1,"create","(number)",[[t,"number"]]),p.sendWithPromise("pdfTronCustomSecurityHandlerCreate",{custom_id:t}).then(function(t){return S(p.SecurityHandler,t)})},p.WebFontDownloader.isAvailable=function(){return p.sendWithPromise("webFontDownloaderIsAvailable",{})},p.WebFontDownloader.enableDownloads=function(){return p.sendWithPromise("webFontDownloaderEnableDownloads",{})},p.WebFontDownloader.disableDownloads=function(){return p.sendWithPromise("webFontDownloaderDisableDownloads",{})},p.WebFontDownloader.preCacheAsync=function(){return p.sendWithPromise("webFontDownloaderPreCacheAsync",{})},p.WebFontDownloader.clearCache=function(){return p.sendWithPromise("webFontDownloaderClearCache",{})},p.WebFontDownloader.setCustomWebFontURL=function(t){return P(arguments.length,1,"setCustomWebFontURL","(string)",[[t,"string"]]),p.sendWithPromise("webFontDownloaderSetCustomWebFontURL",{url:t})},p.WebFontDownloader.selectWebFont=function(t,e){return P(arguments.length,2,"selectWebFont","(string, string)",[[t,"string"],[e,"string"]]),p.sendWithPromise("webFontDownloaderSelectWebFont",{font_req_string:t,charset:e})},p.FlowDocument.create=function(){return p.sendWithPromise("flowDocumentCreate",{}).then(function(t){return S(p.FlowDocument,t)})},p.FlowDocument.prototype.setDefaultPageSize=function(t,e){return P(arguments.length,2,"setDefaultPageSize","(number, number)",[[t,"number"],[e,"number"]]),p.sendWithPromise("FlowDocument.setDefaultPageSize",{self:this.id,width:t,height:e})},p.FlowDocument.prototype.setDefaultMargins=function(t,e,n,i){return P(arguments.length,4,"setDefaultMargins","(number, number, number, number)",[[t,"number"],[e,"number"],[n,"number"],[i,"number"]]),p.sendWithPromise("FlowDocument.setDefaultMargins",{self:this.id,left:t,top:e,right:n,bottom:i})},p.FlowDocument.prototype.addParagraph=function(){return p.sendWithPromise("FlowDocument.addParagraph",{self:this.id}).then(function(t){return _(p.Paragraph,t)})},p.FlowDocument.prototype.addParagraphWithText=function(t){return P(arguments.length,1,"addParagraphWithText","(string)",[[t,"string"]]),p.sendWithPromise("FlowDocument.addParagraphWithText",{self:this.id,text:t}).then(function(t){return _(p.Paragraph,t)})},p.FlowDocument.prototype.addList=function(){return p.sendWithPromise("FlowDocument.addList",{self:this.id}).then(function(t){return _(p.List,t)})},p.FlowDocument.prototype.addTable=function(){return p.sendWithPromise("FlowDocument.addTable",{self:this.id}).then(function(t){return _(p.Table,t)})},p.FlowDocument.prototype.getBody=function(){return p.sendWithPromise("FlowDocument.getBody",{self:this.id}).then(function(t){return _(p.ContentNode,t)})},p.FlowDocument.prototype.paginateToPDF=function(){return p.sendWithPromise("FlowDocument.paginateToPDF",{self:this.id}).then(function(t){return S(p.PDFDoc,t)})},p.ContentElement.prototype.asTextRun=function(){return p.sendWithPromise("ContentElement.asTextRun",{self:this.id}).then(function(t){return _(p.TextRun,t)})},p.ContentElement.prototype.asContentNode=function(){return p.sendWithPromise("ContentElement.asContentNode",{self:this.id}).then(function(t){return _(p.ContentNode,t)})},p.ContentElement.prototype.asParagraph=function(){return p.sendWithPromise("ContentElement.asParagraph",{self:this.id}).then(function(t){return _(p.Paragraph,t)})},p.ContentElement.prototype.asTable=function(){return p.sendWithPromise("ContentElement.asTable",{self:this.id}).then(function(t){return _(p.Table,t)})},p.ContentElement.prototype.asTableRow=function(){return p.sendWithPromise("ContentElement.asTableRow",{self:this.id}).then(function(t){return _(p.TableRow,t)})},p.ContentElement.prototype.asTableCell=function(){return p.sendWithPromise("ContentElement.asTableCell",{self:this.id}).then(function(t){return _(p.TableCell,t)})},p.ContentElement.prototype.asList=function(){return p.sendWithPromise("ContentElement.asList",{self:this.id}).then(function(t){return _(p.List,t)})},p.ContentElement.prototype.asListItem=function(){return p.sendWithPromise("ContentElement.asListItem",{self:this.id}).then(function(t){return _(p.ListItem,t)})},p.ContentElement.prototype.getTextStyledElement=function(){return p.sendWithPromise("ContentElement.getTextStyledElement",{self:this.id}).then(function(t){return _(p.TextStyledElement,t)})},p.ContentNode.prototype.getContentNodeIterator=function(){return p.sendWithPromise("ContentNode.getContentNodeIterator",{self:this.id}).then(function(t){return S(p.Iterator,t,"ContentElement")})},p.Paragraph.prototype.addText=function(t){return P(arguments.length,1,"addText","(string)",[[t,"string"]]),p.sendWithPromise("Paragraph.addText",{self:this.id,text:t}).then(function(t){return _(p.TextRun,t)})},p.Paragraph.prototype.setSpaceBefore=function(t){return P(arguments.length,1,"setSpaceBefore","(number)",[[t,"number"]]),p.sendWithPromise("Paragraph.setSpaceBefore",{self:this.id,val:t})},p.Paragraph.prototype.getSpaceBefore=function(){return p.sendWithPromise("Paragraph.getSpaceBefore",{self:this.id})},p.Paragraph.prototype.setSpaceAfter=function(t){return P(arguments.length,1,"setSpaceAfter","(number)",[[t,"number"]]),p.sendWithPromise("Paragraph.setSpaceAfter",{self:this.id,val:t})},p.Paragraph.prototype.getSpaceAfter=function(){return p.sendWithPromise("Paragraph.getSpaceAfter",{self:this.id})},p.Paragraph.prototype.setJustificationMode=function(t){return P(arguments.length,1,"setJustificationMode","(number)",[[t,"number"]]),p.sendWithPromise("Paragraph.setJustificationMode",{self:this.id,val:t})},p.Paragraph.prototype.getJustificationMode=function(){return p.sendWithPromise("Paragraph.getJustificationMode",{self:this.id})},p.Paragraph.prototype.setStartIndent=function(t){return P(arguments.length,1,"setStartIndent","(number)",[[t,"number"]]),p.sendWithPromise("Paragraph.setStartIndent",{self:this.id,val:t})},p.Paragraph.prototype.getStartIndent=function(){return p.sendWithPromise("Paragraph.getStartIndent",{self:this.id})},p.Paragraph.prototype.setEndIndent=function(t){return P(arguments.length,1,"setEndIndent","(number)",[[t,"number"]]),p.sendWithPromise("Paragraph.setEndIndent",{self:this.id,val:t})},p.Paragraph.prototype.getEndIndent=function(){return p.sendWithPromise("Paragraph.getEndIndent",{self:this.id})},p.Paragraph.prototype.setTextIndent=function(t){return P(arguments.length,1,"setTextIndent","(number)",[[t,"number"]]),p.sendWithPromise("Paragraph.setTextIndent",{self:this.id,val:t})},p.Paragraph.prototype.getTextIndent=function(){return p.sendWithPromise("Paragraph.getTextIndent",{self:this.id})},p.Paragraph.prototype.setBorder=function(t,e,n,i){return P(arguments.length,4,"setBorder","(number, number, number, number)",[[t,"number"],[e,"number"],[n,"number"],[i,"number"]]),p.sendWithPromise("Paragraph.setBorder",{self:this.id,thickness:t,red:e,green:n,blue:i})},p.Paragraph.prototype.getBorderThickness=function(){return p.sendWithPromise("Paragraph.getBorderThickness",{self:this.id})},p.Paragraph.prototype.addTabStop=function(t){return P(arguments.length,1,"addTabStop","(number)",[[t,"number"]]),p.sendWithPromise("Paragraph.addTabStop",{self:this.id,val:t})},p.Paragraph.prototype.getNextTabStop=function(t){return P(arguments.length,1,"getNextTabStop","(number)",[[t,"number"]]),p.sendWithPromise("Paragraph.getNextTabStop",{self:this.id,val:t})},p.Paragraph.prototype.setDefaultTabStop=function(t){return P(arguments.length,1,"setDefaultTabStop","(number)",[[t,"number"]]),p.sendWithPromise("Paragraph.setDefaultTabStop",{self:this.id,val:t})},p.Paragraph.prototype.getDefaultTabStop=function(){return p.sendWithPromise("Paragraph.getDefaultTabStop",{self:this.id})},p.Paragraph.prototype.setSpacesPerTab=function(t){return P(arguments.length,1,"setSpacesPerTab","(number)",[[t,"number"]]),p.sendWithPromise("Paragraph.setSpacesPerTab",{self:this.id,val:t})},p.Paragraph.prototype.getSpacesPerTab=function(){return p.sendWithPromise("Paragraph.getSpacesPerTab",{self:this.id})},p.Paragraph.prototype.setDisplayRtl=function(t){return P(arguments.length,1,"setDisplayRtl","(boolean)",[[t,"boolean"]]),p.sendWithPromise("Paragraph.setDisplayRtl",{self:this.id,val:t})},p.Paragraph.prototype.isDisplayRtl=function(){return p.sendWithPromise("Paragraph.isDisplayRtl",{self:this.id})},p.Table.prototype.addTableRow=function(){return p.sendWithPromise("Table.addTableRow",{self:this.id}).then(function(t){return _(p.TableRow,t)})},p.Table.prototype.getTableCell=function(t,e){return P(arguments.length,2,"getTableCell","(number, number)",[[t,"number"],[e,"number"]]),p.sendWithPromise("Table.getTableCell",{self:this.id,column:t,row:e}).then(function(t){return _(p.TableCell,t)})},p.Table.prototype.setDefaultColumnWidth=function(t){return P(arguments.length,1,"setDefaultColumnWidth","(number)",[[t,"number"]]),p.sendWithPromise("Table.setDefaultColumnWidth",{self:this.id,val:t})},p.Table.prototype.getDefaultRowHeight=function(){return p.sendWithPromise("Table.getDefaultRowHeight",{self:this.id})},p.Table.prototype.setDefaultRowHeight=function(t){return P(arguments.length,1,"setDefaultRowHeight","(number)",[[t,"number"]]),p.sendWithPromise("Table.setDefaultRowHeight",{self:this.id,val:t})},p.Table.prototype.getNumRows=function(){return p.sendWithPromise("Table.getNumRows",{self:this.id})},p.Table.prototype.getNumColumns=function(){return p.sendWithPromise("Table.getNumColumns",{self:this.id})},p.Table.prototype.setBorder=function(t,e,n,i){return P(arguments.length,4,"setBorder","(number, number, number, number)",[[t,"number"],[e,"number"],[n,"number"],[i,"number"]]),p.sendWithPromise("Table.setBorder",{self:this.id,thickness:t,red:e,green:n,blue:i})},p.Table.prototype.getBorderThickness=function(){return p.sendWithPromise("Table.getBorderThickness",{self:this.id})},p.TableCell.prototype.addParagraph=function(){return p.sendWithPromise("TableCell.addParagraph",{self:this.id}).then(function(t){return _(p.Paragraph,t)})},p.TableCell.prototype.addParagraphWithText=function(t){return P(arguments.length,1,"addParagraphWithText","(string)",[[t,"string"]]),p.sendWithPromise("TableCell.addParagraphWithText",{self:this.id,text:t}).then(function(t){return _(p.Paragraph,t)})},p.TableCell.prototype.addTable=function(){return p.sendWithPromise("TableCell.addTable",{self:this.id}).then(function(t){return _(p.Table,t)})},p.TableCell.prototype.mergeCellsRight=function(t){return P(arguments.length,1,"mergeCellsRight","(number)",[[t,"number"]]),p.sendWithPromise("TableCell.mergeCellsRight",{self:this.id,num:t}).then(function(t){return _(p.TableCell,t)})},p.TableCell.prototype.mergeCellsDown=function(t){return P(arguments.length,1,"mergeCellsDown","(number)",[[t,"number"]]),p.sendWithPromise("TableCell.mergeCellsDown",{self:this.id,num:t}).then(function(t){return _(p.TableCell,t)})},p.TableCell.prototype.setBackgroundColor=function(t,e,n){return P(arguments.length,3,"setBackgroundColor","(number, number, number)",[[t,"number"],[e,"number"],[n,"number"]]),p.sendWithPromise("TableCell.setBackgroundColor",{self:this.id,red:t,green:e,blue:n})},p.TableCell.prototype.setBorder=function(t,e,n,i){return P(arguments.length,4,"setBorder","(number, number, number, number)",[[t,"number"],[e,"number"],[n,"number"],[i,"number"]]),p.sendWithPromise("TableCell.setBorder",{self:this.id,thickness:t,red:e,green:n,blue:i})},p.TableCell.prototype.getBorderThickness=function(){return p.sendWithPromise("TableCell.getBorderThickness",{self:this.id})},p.TableCell.prototype.setVerticalAlignment=function(t){return P(arguments.length,1,"setVerticalAlignment","(number)",[[t,"number"]]),p.sendWithPromise("TableCell.setVerticalAlignment",{self:this.id,val:t})},p.TableCell.prototype.getVerticalAlignment=function(){return p.sendWithPromise("TableCell.getVerticalAlignment",{self:this.id})},p.TableCell.prototype.setHorizontalAlignment=function(t){return P(arguments.length,1,"setHorizontalAlignment","(number)",[[t,"number"]]),p.sendWithPromise("TableCell.setHorizontalAlignment",{self:this.id,val:t})},p.TableCell.prototype.getHorizontalAlignment=function(){return p.sendWithPromise("TableCell.getHorizontalAlignment",{self:this.id})},p.TableCell.prototype.setHeight=function(t){return P(arguments.length,1,"setHeight","(number)",[[t,"number"]]),p.sendWithPromise("TableCell.setHeight",{self:this.id,val:t})},p.TableCell.prototype.getHeight=function(){return p.sendWithPromise("TableCell.getHeight",{self:this.id})},p.TableCell.prototype.setWidth=function(t){return P(arguments.length,1,"setWidth","(number)",[[t,"number"]]),p.sendWithPromise("TableCell.setWidth",{self:this.id,val:t})},p.TableCell.prototype.getWidth=function(){return p.sendWithPromise("TableCell.getWidth",{self:this.id})},p.TableRow.prototype.addTableCell=function(){return p.sendWithPromise("TableRow.addTableCell",{self:this.id}).then(function(t){return _(p.TableCell,t)})},p.TableRow.prototype.setRowHeight=function(t){return P(arguments.length,1,"setRowHeight","(number)",[[t,"number"]]),p.sendWithPromise("TableRow.setRowHeight",{self:this.id,val:t})},p.TableRow.prototype.getNumColumns=function(){return p.sendWithPromise("TableRow.getNumColumns",{self:this.id})},p.TextRun.prototype.setText=function(t){return P(arguments.length,1,"setText","(string)",[[t,"string"]]),p.sendWithPromise("TextRun.setText",{self:this.id,text:t})},p.TextRun.prototype.getText=function(){return p.sendWithPromise("TextRun.getText",{self:this.id})},p.TextStyledElement.prototype.setFontFace=function(t){return P(arguments.length,1,"setFontFace","(string)",[[t,"string"]]),p.sendWithPromise("TextStyledElement.setFontFace",{impl:this.id,font_name:t})},p.TextStyledElement.prototype.getFontFace=function(){return p.sendWithPromise("TextStyledElement.getFontFace",{impl:this.id})},p.TextStyledElement.prototype.setFontSize=function(t){return P(arguments.length,1,"setFontSize","(number)",[[t,"number"]]),p.sendWithPromise("TextStyledElement.setFontSize",{impl:this.id,font_size:t})},p.TextStyledElement.prototype.getFontSize=function(){return p.sendWithPromise("TextStyledElement.getFontSize",{impl:this.id})},p.TextStyledElement.prototype.setItalic=function(t){return P(arguments.length,1,"setItalic","(boolean)",[[t,"boolean"]]),p.sendWithPromise("TextStyledElement.setItalic",{impl:this.id,val:t})},p.TextStyledElement.prototype.isItalic=function(){return p.sendWithPromise("TextStyledElement.isItalic",{impl:this.id})},p.TextStyledElement.prototype.setBold=function(t){return P(arguments.length,1,"setBold","(boolean)",[[t,"boolean"]]),p.sendWithPromise("TextStyledElement.setBold",{impl:this.id,val:t})},p.TextStyledElement.prototype.isBold=function(){return p.sendWithPromise("TextStyledElement.isBold",{impl:this.id})},p.TextStyledElement.prototype.setTextColor=function(t,e,n){return P(arguments.length,3,"setTextColor","(number, number, number)",[[t,"number"],[e,"number"],[n,"number"]]),p.sendWithPromise("TextStyledElement.setTextColor",{impl:this.id,red:t,green:e,blue:n})},p.TextStyledElement.prototype.setBackgroundColor=function(t,e,n){return P(arguments.length,3,"setBackgroundColor","(number, number, number)",[[t,"number"],[e,"number"],[n,"number"]]),p.sendWithPromise("TextStyledElement.setBackgroundColor",{impl:this.id,red:t,green:e,blue:n})},p.List.prototype.asContentElement=function(){return p.sendWithPromise("List.asContentElement",{self:this.id}).then(function(t){return _(p.ContentElement,t)})},p.List.prototype.setNumberFormat=function(t,e,n){return P(arguments.length,3,"setNumberFormat","(number, string, boolean)",[[t,"number"],[e,"string"],[n,"boolean"]]),p.sendWithPromise("List.setNumberFormat",{self:this.id,format:t,suffix:e,cascade:n})},p.List.prototype.setStartIndex=function(t){return P(arguments.length,1,"setStartIndex","(number)",[[t,"number"]]),p.sendWithPromise("List.setStartIndex",{self:this.id,idx:t})},p.List.prototype.addItem=function(){return p.sendWithPromise("List.addItem",{self:this.id}).then(function(t){return _(p.ListItem,t)})},p.List.prototype.getIndentationLevel=function(){return p.sendWithPromise("List.getIndentationLevel",{self:this.id})},p.List.prototype.getListIdentifier=function(){return p.sendWithPromise("List.getListIdentifier",{self:this.id})},p.List.prototype.continueList=function(){return p.sendWithPromise("List.continueList",{self:this.id})},p.List.prototype.getContentNodeIterator=function(){return p.sendWithPromise("List.getContentNodeIterator",{self:this.id}).then(function(t){return S(p.Iterator,t,"ContentElement")})},p.List.prototype.getTextStyledElement=function(){return p.sendWithPromise("List.getTextStyledElement",{self:this.id}).then(function(t){return _(p.TextStyledElement,t)})},p.ListItem.prototype.asContentElement=function(){return p.sendWithPromise("ListItem.asContentElement",{self:this.id}).then(function(t){return _(p.ContentElement,t)})},p.ListItem.prototype.addParagraph=function(){return p.sendWithPromise("ListItem.addParagraph",{self:this.id}).then(function(t){return _(p.Paragraph,t)})},p.ListItem.prototype.addParagraphWithText=function(t){return P(arguments.length,1,"addParagraphWithText","(string)",[[t,"string"]]),p.sendWithPromise("ListItem.addParagraphWithText",{self:this.id,text:t}).then(function(t){return _(p.Paragraph,t)})},p.ListItem.prototype.addList=function(){return p.sendWithPromise("ListItem.addList",{self:this.id}).then(function(t){return _(p.List,t)})},p.ListItem.prototype.getIndentationLevel=function(){return p.sendWithPromise("ListItem.getIndentationLevel",{self:this.id})},p.ListItem.prototype.getListIdentifier=function(){return p.sendWithPromise("ListItem.getListIdentifier",{self:this.id})},p.ListItem.prototype.getItemIndex=function(){return p.sendWithPromise("ListItem.getItemIndex",{self:this.id})},p.ListItem.prototype.getContentNodeIterator=function(){return p.sendWithPromise("ListItem.getContentNodeIterator",{self:this.id}).then(function(t){return S(p.Iterator,t,"ContentElement")})},p.ListItem.prototype.getTextStyledElement=function(){return p.sendWithPromise("ListItem.getTextStyledElement",{self:this.id}).then(function(t){return _(p.TextStyledElement,t)})},p.AlgorithmIdentifier.createFromPredefined=function(t){return P(arguments.length,1,"createFromPredefined","(number)",[[t,"number"]]),p.sendWithPromise("algorithmIdentifierCreateFromPredefined",{oid_type:t}).then(function(t){return S(p.AlgorithmIdentifier,t)})},p.AlgorithmIdentifier.createFromObjectIdentifier=function(t){return P(arguments.length,1,"createFromObjectIdentifier","(PDFNet.ObjectIdentifier)",[[t,"Object",p.ObjectIdentifier,"ObjectIdentifier"]]),p.sendWithPromise("algorithmIdentifierCreateFromObjectIdentifier",{oid:t.id}).then(function(t){return S(p.AlgorithmIdentifier,t)})},p.AlgorithmIdentifier.createFromDigestAlgorithm=function(t){return P(arguments.length,1,"createFromDigestAlgorithm","(number)",[[t,"number"]]),p.sendWithPromise("algorithmIdentifierCreateFromDigestAlgorithm",{digest_algorithm_type:t}).then(function(t){return S(p.AlgorithmIdentifier,t)})},p.AlgorithmIdentifier.createFromPredefinedAndParams=function(t,e){return P(arguments.length,2,"createFromPredefinedAndParams","(number, PDFNet.AlgorithmParams)",[[t,"number"],[e,"Object",p.AlgorithmParams,"AlgorithmParams"]]),p.sendWithPromise("algorithmIdentifierCreateFromPredefinedAndParams",{oid_type:t,params:e.id}).then(function(t){return S(p.AlgorithmIdentifier,t)})},p.AlgorithmIdentifier.createFromObjectIdentifierAndParams=function(t,e){return P(arguments.length,2,"createFromObjectIdentifierAndParams","(PDFNet.ObjectIdentifier, PDFNet.AlgorithmParams)",[[t,"Object",p.ObjectIdentifier,"ObjectIdentifier"],[e,"Object",p.AlgorithmParams,"AlgorithmParams"]]),p.sendWithPromise("algorithmIdentifierCreateFromObjectIdentifierAndParams",{oid:t.id,params:e.id}).then(function(t){return S(p.AlgorithmIdentifier,t)})},p.AlgorithmIdentifier.createFromDigestAlgorithmAndParams=function(t,e){return P(arguments.length,2,"createFromDigestAlgorithmAndParams","(number, PDFNet.AlgorithmParams)",[[t,"number"],[e,"Object",p.AlgorithmParams,"AlgorithmParams"]]),p.sendWithPromise("algorithmIdentifierCreateFromDigestAlgorithmAndParams",{digest_algorithm_type:t,params:e.id}).then(function(t){return S(p.AlgorithmIdentifier,t)})},p.RSASSAPSSParams.create=function(){return p.sendWithPromise("rsassapssParamsCreate",{}).then(function(t){return _(p.RSASSAPSSParams,t)})},p.RSASSAPSSParams.createFromAlgoIdAndSaltLen=function(t,e){return P(arguments.length,2,"createFromAlgoIdAndSaltLen","(PDFNet.AlgorithmIdentifier, number)",[[t,"Object",p.AlgorithmIdentifier,"AlgorithmIdentifier"],[e,"number"]]),p.sendWithPromise("rsassapssParamsCreateFromAlgoIdAndSaltLen",{digest_algorithm_id:t.id,salt_length:e}).then(function(t){return _(p.RSASSAPSSParams,t)})},p.RSASSAPSSParams.prototype.getDigestAlgorithm=function(){return p.sendWithPromise("RSASSAPSSParams.getDigestAlgorithm",{self:this.id}).then(function(t){return S(p.AlgorithmIdentifier,t)})},p.RSASSAPSSParams.prototype.setDigestAlgorithm=function(t){return P(arguments.length,1,"setDigestAlgorithm","(PDFNet.AlgorithmIdentifier)",[[t,"Object",p.AlgorithmIdentifier,"AlgorithmIdentifier"]]),p.sendWithPromise("RSASSAPSSParams.setDigestAlgorithm",{self:this.id,value:t.id})},p.RSASSAPSSParams.prototype.getMaskGenAlgorithm=function(){return p.sendWithPromise("RSASSAPSSParams.getMaskGenAlgorithm",{self:this.id}).then(function(t){return S(p.AlgorithmIdentifier,t)})},p.RSASSAPSSParams.prototype.setMaskGenAlgorithm=function(t){return P(arguments.length,1,"setMaskGenAlgorithm","(PDFNet.AlgorithmIdentifier)",[[t,"Object",p.AlgorithmIdentifier,"AlgorithmIdentifier"]]),p.sendWithPromise("RSASSAPSSParams.setMaskGenAlgorithm",{self:this.id,value:t.id})},p.RSASSAPSSParams.prototype.getSaltLength=function(){return p.sendWithPromise("RSASSAPSSParams.getSaltLength",{self:this.id})},p.RSASSAPSSParams.prototype.setSaltLength=function(t){return P(arguments.length,1,"setSaltLength","(number)",[[t,"number"]]),p.sendWithPromise("RSASSAPSSParams.setSaltLength",{self:this.id,value:t})},p.RSASSAPSSParams.prototype.getTrailerField=function(){return p.sendWithPromise("RSASSAPSSParams.getTrailerField",{self:this.id})},p.RSASSAPSSParams.prototype.setTrailerField=function(t){return P(arguments.length,1,"setTrailerField","(number)",[[t,"number"]]),p.sendWithPromise("RSASSAPSSParams.setTrailerField",{self:this.id,value:t})};var f,P=function(t,e,i,r,n){var o=n.length;if(e===o){if(t!==e)throw new RangeError(t+" arguments passed into function '"+i+"'. Expected "+e+" argument. Function Signature: "+i+r)}else if(e<=0){if(o<t)throw new RangeError(t+" arguments passed into function '"+i+"'. Expected at most "+o+" arguments. Function Signature: "+i+r)}else if(t<e||o<t)throw new RangeError(t+" arguments passed into function '"+i+"'. Expected "+e+" to "+o+" arguments. Function Signature: "+i+r);function s(t,e,n){throw new TypeError(g(t)+" input argument in function '"+i+"' is of type '"+e+"'. Expected type '"+n+"'. Function Signature: "+i+r)}for(t=function(t,e,n){"object"===d(t)&&t.name?s(e,t.name,n):s(e,d(t),n)},e=0;e<o;e++){var u=n[e],a=u[0],c=u[1];if(a instanceof Promise)throw new TypeError(g(e)+" input argument in function '"+i+"' is a Promise object. Promises require a 'yield' statement before being accessed.");if("OptionBase"===c){if(a)if("object"===d(a)){if("function"!=typeof a.getJsonString)throw new TypeError(g(e)+" input argument in function '"+i+"' is an 'oject' which is expected to have the 'getJsonString' function")}else s(e,a.name,"object")}else"Array"===c?Array.isArray(a)||t(a,e,"Array"):"ArrayBuffer"===c?l.isArrayBuffer(a)||l.isArrayBuffer(a.buffer)||t(a,e,"ArrayBuffer|TypedArray"):"ArrayAsBuffer"===c?Array.isArray(a)||l.isArrayBuffer(a)||l.isArrayBuffer(a.buffer)||t(a,e,"ArrayBuffer|TypedArray"):"PDFDoc"===c||"SDFDoc"===c||"FDFDoc"===c?a instanceof p.PDFDoc||a instanceof p.SDFDoc||a instanceof p.FDFDoc||t(a,e,"PDFDoc|SDFDoc|FDFDoc"):"Structure"===c?a instanceof u[2]||!a||a.name===u[3]||t(a,e,u[3]):"OptionObject"===c?a instanceof u[2]||("object"===d(a)&&a.name?a.name!==u[4]&&s(e,a.name,u[3]):s(e,d(a),u[3])):"Object"===c?a instanceof u[2]||t(a,e,u[3]):"const char* = 0"===c?"string"!=typeof a&&null!==a&&s(e,d(a),"string"):d(a)!==c&&s(e,d(a),c)}},F=function(t,e){if(void 0!==e)throw Error("Function "+e+" recently altered a struct object without yielding. That object is now being accessed by function '"+t+"'. Perhaps a yield statement is required for "+e+"?")},b=function(t,e){for(var n=0;n<e.length;n++){var i=e[n],r=i[0];if(r&&void 0!==r.yieldFunction)throw Error("Function '"+r.yieldFunction+"' recently altered a struct object without yielding. That object is now being accessed by the "+g(i[1])+" input argument in function '"+t+"'. Perhaps a yield statement is required for '"+r.yieldFunction+"'?")}},y=function(t,e){var n=t;return e&&t.constructor===Array&&(n=new Float64Array(t)),l.isArrayBuffer(n)||(n=n.buffer,t.byteLength<n.byteLength&&(n=n.slice(t.byteOffset,t.byteOffset+t.byteLength))),n},D=function(n,t){return n.name===t?p.ObjSet.create().then(function(t){var e="function"==typeof n.getJsonString?n.getJsonString():JSON.stringify(n);return t.createFromJson(e)}):Promise.resolve(n)},S=(o=[],s=[],u=n=0,a=[],c=[],l.PDFTron&&PDFTron.WebViewer&&PDFTron.WebViewer.prototype&&PDFTron.WebViewer.prototype.version&&PDFTron.skipPDFNetWebViewerWarning,function(t,e,n){return"0"===e?null:(t=new t(e,174774839,n),o.push({name:t.name,id:t.id}),t)}),_=function(t,e,n){return"0"===e?null:new t(e,174774839,n)},O=function(t){for(var e=-1,n=s.length-1;0<=n;n--)if(s[n].id==t.id){e=n;break}if(-1!=e)for(s.splice(e,1),n=c.length-1;0<=n&&e<c[n];n--)--c[n]},A=function(t){for(var e=-1,n=o.length-1;0<=n;n--)if(o[n].id==t){e=n;break}if(-1!=e)for(o.splice(e,1),n=a.length-1;0<=n&&e<a[n];n--)--a[n]},W=(p.messageHandler={sendWithPromiseReturnId:function(){throw Error("PDFNet.initialize must be called and finish resolving before any other PDFNetJS function calls.")}},p.userPriority=2,p.sendWithPromise=function(t,e){var n=this.messageHandler,i=n.sendWithPromiseReturnId(t,e,this.userPriority);return n.pdfnetCommandChain=0==n.pdfnetActiveCommands.size?i.promise:n.pdfnetCommandChain.then(function(){return i.promise}),n.pdfnetActiveCommands.add(i.callbackId),n.pdfnetCommandChain},function(t,e){for(var n in t)e[n]=t[n]}),C=(p.runGeneratorWithoutCleanup=function(t,e){return void 0===e&&(e=""),P(arguments.length,1,"runGeneratorWithoutCleanup","(object, string)",[[t,"object"],[e,"string"]]),p.runWithoutCleanup(function(){return r(t)},e)},p.runGeneratorWithCleanup=function(t,e){return void 0===e&&(e=""),P(arguments.length,1,"runGeneratorWithCleanup","(object, string)",[[t,"object"],[e,"string"]]),p.runWithCleanup(function(){return r(t)},e)},Promise.resolve()),j=(p.displayAllocatedObjects=function(){if(0!=o.length)for(var t=0;t<o.length;t++);return o.length},p.getAllocatedObjectsCount=function(){return o.length},p.startDeallocateStack=function(){return u+=1,a.push(o.length),c.push(s.length),Promise.resolve()},p.endDeallocateStack=function(){if(0===u)return Promise.resolve();var t=a.pop(),e=c.pop(),n=[],i=[];if(void 0!==e&&0!==s.length&&s.length!==e)for(;s.length>e;){var r=s.pop();r=(r=p.sendWithPromise(r.name+"."+r.unlocktype,{doc:r.id})).catch(function(t){}),n.push(r)}if(void 0!==t&&0!==o.length&&o.length!==t)for(;t<o.length;)e=o.pop(),e=(e=p.sendWithPromise(e.name+".destroy",{auto_dealloc_obj:e.id})).catch(function(t){}),i.push(e);return--u,Promise.all(n).then(function(){return Promise.all(i)})},p.getStackCount=function(){return Promise.resolve(u)},p.deallocateAllObjects=function(){var t;if(0==o.length)return(t=createPromiseCapability()).resolve(),t.promise;for(t=[],a=[];s.length;)objToUnlock=s.pop(),unlockPromise=(unlockPromise=p.sendWithPromise(objToUnlock.name+"."+objToUnlock.unlocktype,{doc:objToUnlock.id})).catch(function(t){}),t.push(unlockPromise);for(;o.length;){var e=o.pop();e=(e=p.sendWithPromise(e.name+".destroy",{auto_dealloc_obj:e.id})).catch(function(t){}),t.push(e)}return Promise.all(t)},p.Redactor.redact=function(t,e,n,i,r){return void 0===(n=void 0===n?{}:n).redaction_overlay&&(n.redaction_overlay=!0),void 0===n.positive_overlay_color?n.positive_overlay_color=void 0:void 0!==n.positive_overlay_color.id&&(n.positive_overlay_color=n.positive_overlay_color.id),void 0===n.negative_overlay_color?n.negative_overlay_color=void 0:void 0!==n.negative_overlay_color.id&&(n.negative_overlay_color=n.negative_overlay_color.id),void 0===n.border&&(n.border=!0),void 0===n.use_overlay_text&&(n.use_overlay_text=!0),void 0===n.font?n.font=void 0:void 0!==n.font.id&&(n.font=n.font.id),void 0===n.min_font_size&&(n.min_font_size=2),void 0===n.max_font_size&&(n.max_font_size=24),void 0===n.text_color?n.text_color=void 0:void 0!==n.text_color.id&&(n.text_color=n.text_color.id),void 0===n.horiz_text_alignment&&(n.horiz_text_alignment=-1),void 0===n.vert_text_alignment&&(n.vert_text_alignment=1),void 0===n.show_redacted_content_regions&&(n.show_redacted_content_regions=!1),void 0===n.redacted_content_color?n.redacted_content_color=void 0:void 0!==n.redacted_content_color.id&&(n.redacted_content_color=n.redacted_content_color.id),void 0===i&&(i=!0),void 0===r&&(r=!0),P(arguments.length,2,"redact","(PDFNet.PDFDoc, Array<Core.PDFNet.Redaction>, object, boolean, boolean)",[[t,"PDFDoc"],[e,"Array"],[n,"object"],[i,"boolean"],[r,"boolean"]]),p.sendWithPromise("redactorRedact",{doc:t.id,red_arr:e,appearance:n,ext_neg_mode:i,page_coord_sys:r})},p.Highlights.prototype.getCurrentQuads=function(){return p.sendWithPromise("Highlights.getCurrentQuads",{hlts:this.id}).then(function(t){t=new Float64Array(t);for(var e,n=[],i=0;i<t.length;i+=8)e=p.QuadPoint(t[i+0],t[i+1],t[i+2],t[i+3],t[i+4],t[i+5],t[i+6],t[i+7]),n.push(e);return n})},p.TextSearch.prototype.run=function(){if(0!=arguments.length)throw new RangeError(arguments.length+" arguments passed into function 'run'. Expected 0 arguments. Function Signature: run()");return p.sendWithPromise("TextSearch.run",{ts:this.id}).then(function(t){return t.highlights=S(p.Highlights,t.highlights),t})},p.Iterator.prototype.current=function(){if(0!=arguments.length)throw new RangeError(arguments.length+" arguments passed into function 'fillEncryptDict'. Expected 0 argument.");var e=this,t=(this.yieldFunction="Iterator.current",p.sendWithPromise("Iterator.current",{itr:this.id,type:this.type}));return e.yieldFunction=void 0,t="Int"!=this.type?t.then(function(t){return"string"==typeof t?_(p[e.type],t):new p[e.type](t)}):t},p.PDFDoc.prototype.getFileData=function(t){t({type:"id",id:this.id})},p.PDFDoc.prototype.getFile=function(t){return null},p.PDFDoc.createFromURL=function(t,e){return void 0===e&&(e={}),P(arguments.length,1,"createFromURL","(string, object)",[[t,"string"],[e,"object"]]),m(t,e).then(function(t){return p.PDFDoc.createFromBuffer(t)})},p.PDFDraw.prototype.exportBuffer=function(t,e,n){return void 0===e&&(e="PNG"),void 0===n&&(n=new p.Obj("0")),P(arguments.length,1,"exportBuffer","(PDFNet.Page, string, PDFNet.Obj)",[[t,"Object",p.Page,"Page"],[e,"string"],[n,"Object",p.Obj,"Obj"]]),p.sendWithPromise("PDFDraw.exportBuffer",{d:this.id,page:t.id,format:e,encoder_params:n.id}).then(function(t){return"0"==t?null:new Uint8Array(t)})},p.PDFDraw.prototype.exportStream=p.PDFDraw.prototype.exportBuffer,p.Element.prototype.getTextData=function(){if(0!=arguments.length)throw new RangeError(arguments.length+" arguments passed into function 'getTextData'. Expected 0 arguments. Function Signature: getTextData()");return p.sendWithPromise("Element.getTextData",{e:this.id})},p.Element.prototype.getPathData=function(){if(0!=arguments.length)throw new RangeError(arguments.length+" arguments passed into function 'getPathData'. Expected 0 arguments. Function Signature: getPathData()");return p.sendWithPromise("Element.getPathData",{e:this.id}).then(function(t){return t.operators=new Uint8Array(t.operators),t.points=new Float64Array(t.points),t})},p.PDFDoc.prototype.convertToXod=function(t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,0,"convertToXod","(PDFNet.Obj)",[[t,"OptionObject",p.Obj,"Obj","PDFNet.Convert.XODOutputOptions"]]),p.sendWithPromise("PDFDoc.convertToXod",{doc:this.id,optionsObject:t}).then(function(t){return"0"==t?null:new Uint8Array(t)})},p.PDFDoc.prototype.convertToXodStream=function(t){return void 0===t&&(t=new p.Obj("0")),P(arguments.length,0,"convertToXod","(PDFNet.Obj)",[[t,"OptionObject",p.Obj,"Obj","PDFNet.Convert.XODOutputOptions"]]),p.sendWithPromise("PDFDoc.convertToXodStream",{doc:this.id,optionsObject:t}).then(function(t){return S(p.Filter,t)})},p.FilterReader.prototype.read=function(t){return P(arguments.length,1,"read","(number)",[[t,"number"]]),p.sendWithPromise("FilterReader.read",{reader:this.id,buf_size:t}).then(function(t){return"0"==t?null:new Uint8Array(t)})},p.FilterReader.prototype.readAllIntoBuffer=function(){if(0!=arguments.length)throw new RangeError(arguments.length+" arguments passed into function 'readAllIntoBuffer'. Expected 0 arguments. Function Signature: readAllIntoBuffer()");return p.sendWithPromise("FilterReader.readAllIntoBuffer",{reader:this.id}).then(function(t){return"0"==t?null:new Uint8Array(t)})},p.bitmapInfo=function(t){W(t,this)},p.PDFDraw.prototype.getBitmap=function(t,e,n){return P(arguments.length,3,"getBitmap","(PDFNet.Page, number, boolean)",[[t,"Object",p.Page,"Page"],[e,"number"],[n,"boolean"]]),p.sendWithPromise("PDFDraw.getBitmap",{d:this.id,page:t.id,pix_fmt:e,demult:n}).then(function(t){return"0"==t?null:new p.bitmapInfo(t)})},p.Matrix2D.create=function(t,e,n,i,r,o){null==t&&(t=0),null==e&&(e=0),null==n&&(n=0),null==i&&(i=0),null==r&&(r=0),null==o&&(o=0),P(arguments.length,0,"create","(number, number, number, number, number, number)",[[t,"number"],[e,"number"],[n,"number"],[i,"number"],[r,"number"],[o,"number"]]);var s=createPromiseCapability(),u=new p.Matrix2D({m_a:t,m_b:e,m_c:n,m_d:i,m_h:r,m_v:o});return s.resolve(u),s.promise},p.PDFDoc.prototype.getPDFDoc=function(){return p.sendWithPromise("GetPDFDoc",{doc:this.id}).then(function(t){return _(p.PDFDoc,t)})},p.TextExtractorLine.prototype.getBBox=function(){if(0!=arguments.length)throw new RangeError(arguments.length+" arguments passed into function 'getBBox'. Expected 0 arguments. Function Signature: getBBox()");F("getBBox",this.yieldFunction);var e=this;return this.yieldFunction="TextExtractorLine.getBBox",p.sendWithPromise("TextExtractorLine.getBBox",{line:this}).then(function(t){return e.yieldFunction=void 0,new p.Rect(t.result.x1,t.result.y1,t.result.x2,t.result.y2,t.result.mp_rect)})},p.TextExtractorLine.prototype.getQuad=function(){if(0!=arguments.length)throw new RangeError(arguments.length+" arguments passed into function 'getQuad'. Expected 0 arguments. Function Signature: getQuad()");F("getQuad",this.yieldFunction);var e=this;return this.yieldFunction="TextExtractorLine.getQuad",p.sendWithPromise("TextExtractorLine.getQuad",{line:this}).then(function(t){return e.yieldFunction=void 0,new p.QuadPoint(t.result.p1x,t.result.p1y,t.result.p2x,t.result.p2y,t.result.p3x,t.result.p3y,t.result.p4x,t.result.p4y)})},p.TextExtractorWord.prototype.getBBox=function(){if(0!=arguments.length)throw new RangeError(arguments.length+" arguments passed into function 'getBBox'. Expected 0 arguments. Function Signature: getBBox()");F("getBBox",this.yieldFunction);var e=this;return this.yieldFunction="TextExtractorWord.getBBox",p.sendWithPromise("TextExtractorWord.getBBox",{tew:this}).then(function(t){return e.yieldFunction=void 0,new p.Rect(t.result.x1,t.result.y1,t.result.x2,t.result.y2,t.result.mp_rect)})},p.TextExtractorWord.prototype.getQuad=function(){if(0!=arguments.length)throw new RangeError(arguments.length+" arguments passed into function 'getQuad'. Expected 0 arguments. Function Signature: getQuad()");F("getQuad",this.yieldFunction);var e=this;return this.yieldFunction="TextExtractorWord.getQuad",p.sendWithPromise("TextExtractorWord.getQuad",{tew:this}).then(function(t){return e.yieldFunction=void 0,new p.QuadPoint(t.result.p1x,t.result.p1y,t.result.p2x,t.result.p2y,t.result.p3x,t.result.p3y,t.result.p4x,t.result.p4y)})},p.TextExtractorWord.prototype.getGlyphQuad=function(t){P(arguments.length,1,"getGlyphQuad","(number)",[[t,"number"]]),F("getGlyphQuad",this.yieldFunction);var e=this;return this.yieldFunction="TextExtractorWord.getGlyphQuad",p.sendWithPromise("TextExtractorWord.getGlyphQuad",{tew:this,glyph_idx:t}).then(function(t){return e.yieldFunction=void 0,new p.QuadPoint(t.result.p1x,t.result.p1y,t.result.p2x,t.result.p2y,t.result.p3x,t.result.p3y,t.result.p4x,t.result.p4y)})},p.TextExtractorStyle.prototype.getColor=function(){if(0!=arguments.length)throw new RangeError(arguments.length+" arguments passed into function 'getColor'. Expected 0 arguments. Function Signature: getColor()");F("getColor",this.yieldFunction);var e=this;return this.yieldFunction="TextExtractorStyle.getColor",p.sendWithPromise("TextExtractorStyle.getColor",{tes:this}).then(function(t){return e.yieldFunction=void 0,S(p.ColorPt,t)})},p.TextExtractorWord.prototype.getString=function(){if(0!=arguments.length)throw new RangeError(arguments.length+" arguments passed into function 'getString'. Expected 0 arguments. Function Signature: getString()");F("getString",this.yieldFunction);var e=this;return this.yieldFunction="TextExtractorWord.getString",p.sendWithPromise("TextExtractorWord.getString",{tew:this}).then(function(t){return e.yieldFunction=void 0,t})},p.TextExtractor.prototype.getHighlights=function(t){return P(arguments.length,1,"getHighlights","(Array<object>)",[[t,"Array"]]),p.sendWithPromise("TextExtractor.getHighlights",{te:this.id,char_ranges:t}).then(function(t){return S(p.Highlights,t)})},p.SecurityHandler.prototype.changeUserPasswordNonAscii=function(t){return P(arguments.length,1,"changeUserPasswordNonAscii","(string)",[[t,"string"]]),p.sendWithPromise("SecurityHandler.changeUserPasswordNonAscii",{sh:this.id,password:t,pwd_length:t.length})},p.SecurityHandler.prototype.changeMasterPasswordNonAscii=function(t){return P(arguments.length,1,"changeMasterPasswordNonAscii","(string)",[[t,"string"]]),p.sendWithPromise("SecurityHandler.changeMasterPasswordNonAscii",{sh:this.id,password:t,pwd_length:t.length})},p.SecurityHandler.prototype.initPassword=function(t){return P(arguments.length,1,"initPassword","(string)",[[t,"string"]]),p.sendWithPromise("SecurityHandler.initPassword",{sh:this.id,password:t})},p.SecurityHandler.prototype.initPasswordNonAscii=function(t){return P(arguments.length,1,"initPasswordNonAscii","(string)",[[t,"string"]]),p.sendWithPromise("SecurityHandler.initPasswordNonAscii",{sh:this.id,password:t,pwd_length:t.length})},p.Element.prototype.getBBox=function(){if(0!=arguments.length)throw new RangeError(arguments.length+" arguments passed into function 'getBBox'. Expected 0 arguments. Function Signature: getBBox()");var e=this;return this.yieldFunction="Element.getBBox",p.sendWithPromise("Element.getBBox",{e:this.id}).then(function(t){return e.yieldFunction=void 0,new p.Rect(t)})},p.Matrix2D.prototype.mult=function(t,e){return P(arguments.length,2,"create","(number, number)",[[t,"number"],[e,"number"]]),F("mult",this.yieldFunction),p.sendWithPromise("Matrix2D.mult",{matrix:this,x:t,y:e})},p.Obj.prototype.getAsPDFText=function(){if(0!=arguments.length)throw new RangeError(arguments.length+" arguments passed into function 'getAsPDFText'. Expected 0 arguments. Function Signature: getAsPDFText()");return p.sendWithPromise("Obj.getAsPDFText",{o:this.id})},p.Obj.prototype.getBuffer=function(){if(0!=arguments.length)throw new RangeError(arguments.length+" arguments passed into function 'getBuffer'. Expected 0 arguments. Function Signature: getBuffer()");return p.sendWithPromise("Obj.getBuffer",{o:this.id}).then(function(t){return"0"==t?null:new Uint8Array(t)})},p.PDFDoc.prototype.initSecurityHandler=function(t){if(void 0===t&&(t=0),1<arguments.length)throw new RangeError(arguments.length+" arguments passed into function 'initSecurityHandler'. Expected at most 1 arguments. Function Signature: initSecurityHandler(void*)");return p.sendWithPromise("PDFDoc.initSecurityHandler",{doc:this.id,custom_data:t})},p.PDFDoc.prototype.initStdSecurityHandler=p.PDFDoc.prototype.initStdSecurityHandlerUString,p.SDFDoc.prototype.initSecurityHandler=function(t){if(void 0===t&&(t=0),1<arguments.length)throw new RangeError(arguments.length+" arguments passed into function 'initSecurityHandler'. Expected at most 1 arguments. Function Signature: initSecurityHandler(void*)");return p.sendWithPromise("SDFDoc.initSecurityHandler",{doc:this.id,custom_data:t})},p.SDFDoc.prototype.initStdSecurityHandler=p.SDFDoc.prototype.initStdSecurityHandlerUString,p.Image.createFromURL=function(e,t,n,i){return void 0===n&&(n=new p.Obj("0")),void 0===i&&(i={}),P(arguments.length,2,"createFromURL","(PDFNet.PDFDoc, string, PDFNet.Obj, object)",[[e,"PDFDoc"],[t,"string"],[n,"Object",p.Obj,"Obj"],[i,"object"]]),m(t,i).then(function(t){return p.Image.createFromMemory2(e,t,n)})},p.PDFDoc.prototype.addStdSignatureHandlerFromURL=function(t,e){P(arguments.length,2,"addStdSignatureHandlerFromURL","(string, string)",[[t,"string"],[e,"string"]]);var n=this;return m(t).then(function(t){return n.addStdSignatureHandlerFromBufferWithDoc(t,e,n)})},p.PDFDoc.prototype.addStdSignatureHandlerFromBufferWithDoc=function(t,e,n){return P(arguments.length,3,"addStdSignatureHandlerFromBufferWithDoc","(ArrayBuffer|TypedArray, string, PDFNet.PDFDoc)",[[t,"ArrayBuffer"],[e,"string"],[n,"PDFDoc"]]),p.sendWithPromise("PDFDoc.addStdSignatureHandlerFromBuffer",{doc:n.id,pkcs12_buffer:t.buffer,pkcs12_pass:e})},p.Filter.createFromMemory=function(t){P(arguments.length,1,"createFromMemory","(ArrayBuffer|TypedArray)",[[t,"ArrayBuffer"]]);var e=y(t,!1);return p.sendWithPromise("filterCreateFromMemory",{buf:e}).then(function(t){return S(p.Filter,t)})},p.Filter.createURLFilter=function(t,e){return void 0===e&&(e={}),P(arguments.length,1,"createURLFilter","(string, object)",[[t,"string"],[e,"object"]]),m(t,e).then(function(t){return p.Filter.createFromMemory(t)})},p.Filter.createFlateEncode=function(t,e,n){return void 0===t&&(t=new p.Filter("0")),void 0===e&&(e=-1),void 0===n&&(n=256),P(arguments.length,0,"createFlateEncode","(PDFNet.Filter, number, number)",[[t,"Object",p.Filter,"Filter"],[e,"number"],[n,"number"]]),p.sendWithPromise("Filter.createFlateEncode",{input_filter:t.id,compression_level:e,buf_sz:n}).then(function(t){return S(p.Filter,t)})},p.PDFDoc.prototype.importPages=function(t,e){return void 0===e&&(e=!1),P(arguments.length,1,"importPages","(Array<Core.PDFNet.Page>, boolean)",[[t,"Array"],[e,"boolean"]]),t=t.map(function(t){return t.id}),p.sendWithPromise("PDFDoc.importPages",{doc:this.id,page_arr:t,import_bookmarks:e}).then(function(t){return t?t.map(function(t){return _(p.Page,t)}):null})},p.SDFDoc.prototype.applyCustomQuery=function(t){return P(arguments.length,1,"applyCustomQuery","(object)",[[t,"object"]]),p.sendWithPromise("SDFDoc.applyCustomQuery",{doc:this.id,query:JSON.stringify(t)}).then(function(t){return JSON.parse(t)})},p.PDFDoc.prototype.saveMemoryBuffer),T=p.PDFDoc.prototype.saveStream;p.PDFDoc.prototype.saveMemoryBuffer=function(t){var e=this;return Promise.resolve(e.documentCompletePromise).then(function(){return j.call(e,t)})},p.PDFDoc.prototype.saveStream=function(t){var e=this;return Promise.resolve(e.documentCompletePromise).then(function(){return T.call(e,t)})},p.PDFACompliance.createFromUrl=function(e,t,n,i,r,o,s){return void 0===n&&(n=""),void 0===i&&(i=p.PDFACompliance.Conformance.e_Level1B),void 0===r&&(r=new Int32Array(0)),void 0===o&&(o=10),void 0===s&&(s=!1),P(arguments.length,2,"createFromUrl","(boolean, string, string, number, ArrayBuffer|TypedArray, number, boolean)",[[e,"boolean"],[t,"string"],[n,"string"],[i,"number"],[r,"ArrayBuffer"],[o,"number"],[s,"boolean"]]),m(t).then(function(t){return p.PDFACompliance.createFromBuffer(e,t,n,i,r,o,s)})},p.PDFACompliance.createFromBuffer=function(t,e,n,i,r,o,s){void 0===n&&(n=""),void 0===i&&(i=p.PDFACompliance.Conformance.e_Level1B),void 0===r&&(r=new Int32Array(0)),void 0===o&&(o=10),void 0===s&&(s=!1),P(arguments.length,2,"createFromBuffer","(boolean, ArrayBuffer|TypedArray, string, number, ArrayBuffer|TypedArray, number, boolean)",[[t,"boolean"],[e,"ArrayBuffer"],[n,"string"],[i,"number"],[r,"ArrayBuffer"],[o,"number"],[s,"boolean"]]);var u=y(e,!1),a=y(r,!1);return p.sendWithPromise("pdfaComplianceCreateFromBuffer",{convert:t,buf:u,password:n,conform:i,excep:a,max_ref_objs:o,first_stop:s}).then(function(t){return S(p.PDFACompliance,t)})},p.PDFDoc.prototype.lock=function(){if(0!=arguments.length)throw new RangeError(arguments.length+" arguments passed into function 'lock'. Expected 0 arguments. Function Signature: lock()");return s.push({name:"PDFDoc",id:this.id,unlocktype:"unlock"}),p.sendWithPromise("PDFDoc.lock",{doc:this.id})},p.PDFDoc.prototype.lockRead=function(){if(0!=arguments.length)throw new RangeError(arguments.length+" arguments passed into function 'lockRead'. Expected 0 arguments. Function Signature: lockRead()");return s.push({name:"PDFDoc",id:this.id,unlocktype:"unlockRead"}),p.sendWithPromise("PDFDoc.lockRead",{doc:this.id})},p.PDFDoc.prototype.tryLock=function(){if(0!=arguments.length)throw new RangeError(arguments.length+" arguments passed into function 'tryLock'. Expected 0 arguments. Function Signature: tryLock()");var e=s.length;return s.push({name:"PDFDoc",id:this.id,unlocktype:"unlock"}),p.sendWithPromise("PDFDoc.tryLock",{doc:this.id}).then(function(t){t||s.splice(e,1)})},p.PDFDoc.prototype.timedLock=function(t){P(arguments.length,1,"timedLock","(number)",[[t,"number"]]);var e=s.length;return s.push({name:"PDFDoc",id:this.id,unlocktype:"unlock"}),p.sendWithPromise("PDFDoc.timedLock",{doc:this.id,milliseconds:t}).then(function(t){t||s.splice(e,1)})},p.PDFDoc.prototype.tryLockRead=function(){if(0!=arguments.length)throw new RangeError(arguments.length+" arguments passed into function 'tryLockRead'. Expected 0 arguments. Function Signature: tryLockRead()");var e=s.length;return s.push({name:"PDFDoc",id:this.id,unlocktype:"unlockRead"}),p.sendWithPromise("PDFDoc.tryLockRead",{doc:this.id}).then(function(t){t||s.splice(e,1)})},p.PDFDoc.prototype.timedLockRead=function(t){P(arguments.length,1,"timedLockRead","(number)",[[t,"number"]]);var e=s.length;return s.push({name:"PDFDoc",id:this.id,unlocktype:"unlockRead"}),p.sendWithPromise("PDFDoc.timedLockRead",{doc:this.id,milliseconds:t}).then(function(t){t||s.splice(e,1)})},p.hasFullApi=!0,p.Optimizer.optimize=function(t,e){return void 0===e&&(e=new p.Optimizer.OptimizerSettings),P(arguments.length,1,"optimize","(PDFNet.PDFDoc, object)",[[t,"PDFDoc"],[e,"object"]]),p.sendWithPromise("optimizerOptimize",{doc:t.id,color_image_settings:e.color_image_settings,grayscale_image_settings:e.grayscale_image_settings,mono_image_settings:e.mono_image_settings,text_settings:e.text_settings,remove_custom:e.remove_custom})},p.VerificationOptions.prototype.addTrustedCertificateFromURL=function(t,e,n){void 0===e&&(e={}),void 0===n&&(n=p.VerificationOptions.CertificateTrustFlag.e_default_trust),P(arguments.length,1,"addTrustedCertificateFromURL","(string, object, number)",[[t,"string"],[e,"object"],[n,"number"]]);var i=this;return m(t,e).then(function(t){return i.addTrustedCertificate(t,n)})},p.DigitalSignatureField.prototype.certifyOnNextSaveFromURL=function(t,e,n){void 0===n&&(n={}),P(arguments.length,2,"certifyOnNextSaveFromURL","(string, string, object)",[[t,"string"],[e,"string"],[n,"object"]]);var i=this;return m(t,n).then(function(t){return i.certifyOnNextSaveFromBuffer(t,e)})},p.DigitalSignatureField.prototype.signOnNextSaveFromURL=function(t,e,n){void 0===n&&(n={}),P(arguments.length,2,"signOnNextSaveFromURL","(string, string, object)",[[t,"string"],[e,"string"],[n,"object"]]);var i=this;return m(t,n).then(function(t){return i.signOnNextSaveFromBuffer(t,e)})},p.PDFRasterizer.prototype.rasterize=function(t,e,n,i,r,o,s,u,a){return void 0===u&&(u=null),void 0===a&&(a=null),P(arguments.length,7,"rasterize","(PDFNet.Page, number, number, number, number, boolean, PDFNet.Matrix2D, PDFNet.Rect, PDFNet.Rect)",[[t,"Object",p.Page,"Page"],[e,"number"],[n,"number"],[i,"number"],[r,"number"],[o,"boolean"],[s,"Structure",p.Matrix2D,"Matrix2D"],[u,"Structure",p.Rect,"Rect"],[a,"Structure",p.Rect,"Rect"]]),b("rasterize",[[s,6],[u,7],[a,8]]),p.sendWithPromise("PDFRasterizer.rasterize",{r:this.id,page:t.id,width:e,height:n,stride:i,num_comps:r,demult:o,device_mtx:s,clip:u,scrl_clp_regions:a})},p.ElementBuilder.prototype.createUnicodeTextRun=function(t){return P(arguments.length,1,"createUnicodeTextRun","(string)",[[t,"string"]]),p.sendWithPromise("ElementBuilder.createUnicodeTextRun",{b:this.id,text_data:t}).then(function(t){return _(p.Element,t)})},p.DigitalSignatureField.prototype.getCertPathsFromCMS=function(){return F("getCertPathsFromCMS",this.yieldFunction),p.sendWithPromise("DigitalSignatureField.getCertPathsFromCMS",{self:this}).then(function(t){return t.map(function(t){return t.map(function(t){return S(p.X509Certificate,t)})})})},p.X509Certificate.createFromURL=function(t,e){void 0===e&&(e={}),P(arguments.length,1,"createFromURL","(string, object)",[[t,"string"],[e,"object"]]);var n=this;return m(t,e).then(function(t){return n.createFromBuffer(t)})},p.DigestAlgorithm.signDigestWithKeyfileFromURL=function(e,n,t,i,r){void 0===r&&(r={}),P(arguments.length,4,"signDigestWithKeyfileFromURL","(ArrayBuffer|TypedArray, number, string, string, object)",[[e,"ArrayBuffer"],[n,"number"],[t,"string"],[i,"string"],[r,"object"]]);var o=this;return m(t,r).then(function(t){return o.signDigestWithKeyfileBuffer(e,n,t,i)})},p.createPDFNetObj=_,p.Convert.office2PDF=function(t,e){return Core.officeToPDFBuffer(t,e).then(function(t){return p.PDFDoc.createFromBuffer(t).then(function(t){return t.initSecurityHandler(),t})})},p.PDFDoc.prototype.requirePage=function(t){if(P(arguments.length,1,"requirePage","(number)",[[t,"number"]]),t<=0)throw Error("1st input argument '"+t+"' in function 'requirePage' is invalid. Expected number between 1 and number of pages in the document.");return p.sendWithPromise("PDFDoc.RequirePage",{docId:this.id,pageNum:t})},p.beginOperation=function(t){if(void 0===t?t={allowMultipleInstances:!1}:t.allowMultipleInstances,0<n&&!t.allowMultipleInstances)throw Error("a previous instance of PDFNet.beginOperation() has been called without being terminated by PDFNet.finishOperation(). If this is intentional, pass in an options object with its parameter 'allowMultipleInstances' set to 'true' (ex. optObj={}; optObj.allowMultipleInstances=true; PDFNet.beginOperation(optObj));");if(n+=1,1<arguments.length)throw new RangeError(arguments.length+" arguments passed into function 'beginOperation'. Expected 0 to 1 arguments. Function Signature: beginOperation(optObj = {})");return p.sendWithPromise("BeginOperation",{})},p.finishOperation=function(){if(0<n){if(--n,0!=arguments.length)throw new RangeError(arguments.length+" arguments passed into function 'finishOperation'. Expected 0 arguments. Function Signature: finishOperation()");return p.sendWithPromise("FinishOperation",{})}},p.runWithCleanup=function(t,e){void 0===e&&(e=""),P(arguments.length,1,"runWithCleanup","(function, string)",[[t,"function"],[e,"string"]]);var n,i=!1,r=!1;return C=C.then(function(){},function(){}).then(function(){return p.initialize(e)}).then(function(){return i=!0,p.beginOperation()}).then(function(){return r=!0,p.startDeallocateStack(),t()}).then(function(t){return n=t,r=!1,p.endDeallocateStack()}).then(function(){if(i=!1,p.finishOperation(),0<u)throw Error('Detected not yet deallocated stack. You may have called "PDFNet.startDeallocateStack()" somewhere without calling "PDFNet.endDeallocateStack()" afterwards.');return n}).catch(function(t){throw r&&p.endDeallocateStack(),i&&p.finishOperation(),t})},p.runWithoutCleanup=function(t,e){void 0===e&&(e=""),P(arguments.length,1,"runWithCleanup","(function, string)",[[t,"function"],[e,"string"]]);var n=!1;return C=C.then(function(){},function(){}).then(function(){return p.initialize(e)}).then(function(){return n=!0,p.beginOperation()}).then(function(){return t()}).then(function(t){return n=!1,p.finishOperation(),t}).catch(function(t){throw n&&p.finishOperation(),t})},p.initialize=function(e,t){var n,i;return void 0===e&&(e=""),void 0===t&&(t=""),P(arguments.length,0,"initialize","(string, string)",[[e,"string"],[t,"string"]]),f||(n={emsWorkerError:function(t,e){}},f=createPromiseCapability(),i=function(t){l.Core.preloadPDFWorker(t,n),l.Core.initPDFWorkerTransports(t,n,e).then(function(t){p.messageHandler=t.messageHandler,f.resolve()},function(t){f.reject(t)})},t&&"auto"!==t?i(t):l.Core.getDefaultBackendType().then(i,function(t){f.reject(t)})),f.promise},l.Core.PDFNet=p}],i={},r.m=n,r.c=i,r.d=function(t,e,n){r.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},r.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t||4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)r.d(n,i,function(t){return e[t]}.bind(null,i));return n},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="/core/pdf/",r(r.s=0)}.call(this||window);