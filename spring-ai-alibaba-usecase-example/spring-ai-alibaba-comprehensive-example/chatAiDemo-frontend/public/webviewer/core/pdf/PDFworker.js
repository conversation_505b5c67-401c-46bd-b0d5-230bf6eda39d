(function(){var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.arrayIteratorImpl=function(u){var z=0;return function(){return z<u.length?{done:!1,value:u[z++]}:{done:!0}}};$jscomp.arrayIterator=function(u){return{next:$jscomp.arrayIteratorImpl(u)}};$jscomp.ASSUME_ES5=!1;$jscomp.ASSUME_NO_NATIVE_MAP=!1;$jscomp.ASSUME_NO_NATIVE_SET=!1;$jscomp.SIMPLE_FROUND_POLYFILL=!1;$jscomp.ISOLATE_POLYFILLS=!1;$jscomp.FORCE_POLYFILL_PROMISE=!1;$jscomp.FORCE_POLYFILL_PROMISE_WHEN_NO_UNHANDLED_REJECTION=!1;
$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(u,z,v){if(u==Array.prototype||u==Object.prototype)return u;u[z]=v.value;return u};$jscomp.getGlobal=function(u){u=["object"==typeof globalThis&&globalThis,u,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var z=0;z<u.length;++z){var v=u[z];if(v&&v.Math==Math)return v}throw Error("Cannot find global object");};$jscomp.global=$jscomp.getGlobal(this);
$jscomp.IS_SYMBOL_NATIVE="function"===typeof Symbol&&"symbol"===typeof Symbol("x");$jscomp.TRUST_ES6_POLYFILLS=!$jscomp.ISOLATE_POLYFILLS||$jscomp.IS_SYMBOL_NATIVE;$jscomp.polyfills={};$jscomp.propertyToPolyfillSymbol={};$jscomp.POLYFILL_PREFIX="$jscp$";var $jscomp$lookupPolyfilledValue=function(u,z,v){if(!v||null!=u){v=$jscomp.propertyToPolyfillSymbol[z];if(null==v)return u[z];v=u[v];return void 0!==v?v:u[z]}};
$jscomp.polyfill=function(u,z,v,t){z&&($jscomp.ISOLATE_POLYFILLS?$jscomp.polyfillIsolated(u,z,v,t):$jscomp.polyfillUnisolated(u,z,v,t))};$jscomp.polyfillUnisolated=function(u,z,v,t){v=$jscomp.global;u=u.split(".");for(t=0;t<u.length-1;t++){var r=u[t];if(!(r in v))return;v=v[r]}u=u[u.length-1];t=v[u];z=z(t);z!=t&&null!=z&&$jscomp.defineProperty(v,u,{configurable:!0,writable:!0,value:z})};
$jscomp.polyfillIsolated=function(u,z,v,t){var r=u.split(".");u=1===r.length;t=r[0];t=!u&&t in $jscomp.polyfills?$jscomp.polyfills:$jscomp.global;for(var A=0;A<r.length-1;A++){var l=r[A];if(!(l in t))return;t=t[l]}r=r[r.length-1];v=$jscomp.IS_SYMBOL_NATIVE&&"es6"===v?t[r]:null;z=z(v);null!=z&&(u?$jscomp.defineProperty($jscomp.polyfills,r,{configurable:!0,writable:!0,value:z}):z!==v&&(void 0===$jscomp.propertyToPolyfillSymbol[r]&&(v=1E9*Math.random()>>>0,$jscomp.propertyToPolyfillSymbol[r]=$jscomp.IS_SYMBOL_NATIVE?
$jscomp.global.Symbol(r):$jscomp.POLYFILL_PREFIX+v+"$"+r),$jscomp.defineProperty(t,$jscomp.propertyToPolyfillSymbol[r],{configurable:!0,writable:!0,value:z})))};$jscomp.initSymbol=function(){};
$jscomp.polyfill("Symbol",function(u){if(u)return u;var z=function(A,l){this.$jscomp$symbol$id_=A;$jscomp.defineProperty(this,"description",{configurable:!0,writable:!0,value:l})};z.prototype.toString=function(){return this.$jscomp$symbol$id_};var v="jscomp_symbol_"+(1E9*Math.random()>>>0)+"_",t=0,r=function(A){if(this instanceof r)throw new TypeError("Symbol is not a constructor");return new z(v+(A||"")+"_"+t++,A)};return r},"es6","es3");
$jscomp.polyfill("Symbol.iterator",function(u){if(u)return u;u=Symbol("Symbol.iterator");for(var z="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),v=0;v<z.length;v++){var t=$jscomp.global[z[v]];"function"===typeof t&&"function"!=typeof t.prototype[u]&&$jscomp.defineProperty(t.prototype,u,{configurable:!0,writable:!0,value:function(){return $jscomp.iteratorPrototype($jscomp.arrayIteratorImpl(this))}})}return u},"es6",
"es3");$jscomp.iteratorPrototype=function(u){u={next:u};u[Symbol.iterator]=function(){return this};return u};$jscomp.checkEs6ConformanceViaProxy=function(){try{var u={},z=Object.create(new $jscomp.global.Proxy(u,{get:function(v,t,r){return v==u&&"q"==t&&r==z}}));return!0===z.q}catch(v){return!1}};$jscomp.USE_PROXY_FOR_ES6_CONFORMANCE_CHECKS=!1;$jscomp.ES6_CONFORMANCE=$jscomp.USE_PROXY_FOR_ES6_CONFORMANCE_CHECKS&&$jscomp.checkEs6ConformanceViaProxy();
$jscomp.makeIterator=function(u){var z="undefined"!=typeof Symbol&&Symbol.iterator&&u[Symbol.iterator];if(z)return z.call(u);if("number"==typeof u.length)return $jscomp.arrayIterator(u);throw Error(String(u)+" is not an iterable or ArrayLike");};$jscomp.owns=function(u,z){return Object.prototype.hasOwnProperty.call(u,z)};
$jscomp.polyfill("WeakMap",function(u){function z(){if(!u||!Object.seal)return!1;try{var m=Object.seal({}),e=Object.seal({}),k=new u([[m,2],[e,3]]);if(2!=k.get(m)||3!=k.get(e))return!1;k.delete(m);k.set(e,4);return!k.has(m)&&4==k.get(e)}catch(g){return!1}}function v(){}function t(m){var e=typeof m;return"object"===e&&null!==m||"function"===e}function r(m){if(!$jscomp.owns(m,l)){var e=new v;$jscomp.defineProperty(m,l,{value:e})}}function A(m){if(!$jscomp.ISOLATE_POLYFILLS){var e=Object[m];e&&(Object[m]=
function(k){if(k instanceof v)return k;Object.isExtensible(k)&&r(k);return e(k)})}}if($jscomp.USE_PROXY_FOR_ES6_CONFORMANCE_CHECKS){if(u&&$jscomp.ES6_CONFORMANCE)return u}else if(z())return u;var l="$jscomp_hidden_"+Math.random();A("freeze");A("preventExtensions");A("seal");var d=0,h=function(m){this.id_=(d+=Math.random()+1).toString();if(m){m=$jscomp.makeIterator(m);for(var e;!(e=m.next()).done;)e=e.value,this.set(e[0],e[1])}};h.prototype.set=function(m,e){if(!t(m))throw Error("Invalid WeakMap key");
r(m);if(!$jscomp.owns(m,l))throw Error("WeakMap key fail: "+m);m[l][this.id_]=e;return this};h.prototype.get=function(m){return t(m)&&$jscomp.owns(m,l)?m[l][this.id_]:void 0};h.prototype.has=function(m){return t(m)&&$jscomp.owns(m,l)&&$jscomp.owns(m[l],this.id_)};h.prototype.delete=function(m){return t(m)&&$jscomp.owns(m,l)&&$jscomp.owns(m[l],this.id_)?delete m[l][this.id_]:!1};return h},"es6","es3");$jscomp.MapEntry=function(){};
$jscomp.polyfill("Map",function(u){function z(){if($jscomp.ASSUME_NO_NATIVE_MAP||!u||"function"!=typeof u||!u.prototype.entries||"function"!=typeof Object.seal)return!1;try{var h=Object.seal({x:4}),m=new u($jscomp.makeIterator([[h,"s"]]));if("s"!=m.get(h)||1!=m.size||m.get({x:4})||m.set({x:4},"t")!=m||2!=m.size)return!1;var e=m.entries(),k=e.next();if(k.done||k.value[0]!=h||"s"!=k.value[1])return!1;k=e.next();return k.done||4!=k.value[0].x||"t"!=k.value[1]||!e.next().done?!1:!0}catch(g){return!1}}
if($jscomp.USE_PROXY_FOR_ES6_CONFORMANCE_CHECKS){if(u&&$jscomp.ES6_CONFORMANCE)return u}else if(z())return u;var v=new WeakMap,t=function(h){this.data_={};this.head_=l();this.size=0;if(h){h=$jscomp.makeIterator(h);for(var m;!(m=h.next()).done;)m=m.value,this.set(m[0],m[1])}};t.prototype.set=function(h,m){h=0===h?0:h;var e=r(this,h);e.list||(e.list=this.data_[e.id]=[]);e.entry?e.entry.value=m:(e.entry={next:this.head_,previous:this.head_.previous,head:this.head_,key:h,value:m},e.list.push(e.entry),
this.head_.previous.next=e.entry,this.head_.previous=e.entry,this.size++);return this};t.prototype.delete=function(h){h=r(this,h);return h.entry&&h.list?(h.list.splice(h.index,1),h.list.length||delete this.data_[h.id],h.entry.previous.next=h.entry.next,h.entry.next.previous=h.entry.previous,h.entry.head=null,this.size--,!0):!1};t.prototype.clear=function(){this.data_={};this.head_=this.head_.previous=l();this.size=0};t.prototype.has=function(h){return!!r(this,h).entry};t.prototype.get=function(h){return(h=
r(this,h).entry)&&h.value};t.prototype.entries=function(){return A(this,function(h){return[h.key,h.value]})};t.prototype.keys=function(){return A(this,function(h){return h.key})};t.prototype.values=function(){return A(this,function(h){return h.value})};t.prototype.forEach=function(h,m){for(var e=this.entries(),k;!(k=e.next()).done;)k=k.value,h.call(m,k[1],k[0],this)};t.prototype[Symbol.iterator]=t.prototype.entries;var r=function(h,m){var e=m&&typeof m;"object"==e||"function"==e?v.has(m)?e=v.get(m):
(e=""+ ++d,v.set(m,e)):e="p_"+m;var k=h.data_[e];if(k&&$jscomp.owns(h.data_,e))for(h=0;h<k.length;h++){var g=k[h];if(m!==m&&g.key!==g.key||m===g.key)return{id:e,list:k,index:h,entry:g}}return{id:e,list:k,index:-1,entry:void 0}},A=function(h,m){var e=h.head_;return $jscomp.iteratorPrototype(function(){if(e){for(;e.head!=h.head_;)e=e.previous;for(;e.next!=e.head;)return e=e.next,{done:!1,value:m(e)};e=null}return{done:!0,value:void 0}})},l=function(){var h={};return h.previous=h.next=h.head=h},d=0;
return t},"es6","es3");
$jscomp.polyfill("Promise",function(u){function z(){this.batch_=null}function v(l){return l instanceof r?l:new r(function(d,h){d(l)})}if(u&&(!($jscomp.FORCE_POLYFILL_PROMISE||$jscomp.FORCE_POLYFILL_PROMISE_WHEN_NO_UNHANDLED_REJECTION&&"undefined"===typeof $jscomp.global.PromiseRejectionEvent)||!$jscomp.global.Promise||-1===$jscomp.global.Promise.toString().indexOf("[native code]")))return u;z.prototype.asyncExecute=function(l){if(null==this.batch_){this.batch_=[];var d=this;this.asyncExecuteFunction(function(){d.executeBatch_()})}this.batch_.push(l)};
var t=$jscomp.global.setTimeout;z.prototype.asyncExecuteFunction=function(l){t(l,0)};z.prototype.executeBatch_=function(){for(;this.batch_&&this.batch_.length;){var l=this.batch_;this.batch_=[];for(var d=0;d<l.length;++d){var h=l[d];l[d]=null;try{h()}catch(m){this.asyncThrow_(m)}}}this.batch_=null};z.prototype.asyncThrow_=function(l){this.asyncExecuteFunction(function(){throw l;})};var r=function(l){this.state_=0;this.result_=void 0;this.onSettledCallbacks_=[];this.isRejectionHandled_=!1;var d=this.createResolveAndReject_();
try{l(d.resolve,d.reject)}catch(h){d.reject(h)}};r.prototype.createResolveAndReject_=function(){function l(m){return function(e){h||(h=!0,m.call(d,e))}}var d=this,h=!1;return{resolve:l(this.resolveTo_),reject:l(this.reject_)}};r.prototype.resolveTo_=function(l){if(l===this)this.reject_(new TypeError("A Promise cannot resolve to itself"));else if(l instanceof r)this.settleSameAsPromise_(l);else{a:switch(typeof l){case "object":var d=null!=l;break a;case "function":d=!0;break a;default:d=!1}d?this.resolveToNonPromiseObj_(l):
this.fulfill_(l)}};r.prototype.resolveToNonPromiseObj_=function(l){var d=void 0;try{d=l.then}catch(h){this.reject_(h);return}"function"==typeof d?this.settleSameAsThenable_(d,l):this.fulfill_(l)};r.prototype.reject_=function(l){this.settle_(2,l)};r.prototype.fulfill_=function(l){this.settle_(1,l)};r.prototype.settle_=function(l,d){if(0!=this.state_)throw Error("Cannot settle("+l+", "+d+"): Promise already settled in state"+this.state_);this.state_=l;this.result_=d;2===this.state_&&this.scheduleUnhandledRejectionCheck_();
this.executeOnSettledCallbacks_()};r.prototype.scheduleUnhandledRejectionCheck_=function(){var l=this;t(function(){if(l.notifyUnhandledRejection_()){var d=$jscomp.global.console;"undefined"!==typeof d&&d.error(l.result_)}},1)};r.prototype.notifyUnhandledRejection_=function(){if(this.isRejectionHandled_)return!1;var l=$jscomp.global.CustomEvent,d=$jscomp.global.Event,h=$jscomp.global.dispatchEvent;if("undefined"===typeof h)return!0;"function"===typeof l?l=new l("unhandledrejection",{cancelable:!0}):
"function"===typeof d?l=new d("unhandledrejection",{cancelable:!0}):(l=$jscomp.global.document.createEvent("CustomEvent"),l.initCustomEvent("unhandledrejection",!1,!0,l));l.promise=this;l.reason=this.result_;return h(l)};r.prototype.executeOnSettledCallbacks_=function(){if(null!=this.onSettledCallbacks_){for(var l=0;l<this.onSettledCallbacks_.length;++l)A.asyncExecute(this.onSettledCallbacks_[l]);this.onSettledCallbacks_=null}};var A=new z;r.prototype.settleSameAsPromise_=function(l){var d=this.createResolveAndReject_();
l.callWhenSettled_(d.resolve,d.reject)};r.prototype.settleSameAsThenable_=function(l,d){var h=this.createResolveAndReject_();try{l.call(d,h.resolve,h.reject)}catch(m){h.reject(m)}};r.prototype.then=function(l,d){function h(g,w){return"function"==typeof g?function(n){try{m(g(n))}catch(p){e(p)}}:w}var m,e,k=new r(function(g,w){m=g;e=w});this.callWhenSettled_(h(l,m),h(d,e));return k};r.prototype.catch=function(l){return this.then(void 0,l)};r.prototype.callWhenSettled_=function(l,d){function h(){switch(m.state_){case 1:l(m.result_);
break;case 2:d(m.result_);break;default:throw Error("Unexpected state: "+m.state_);}}var m=this;null==this.onSettledCallbacks_?A.asyncExecute(h):this.onSettledCallbacks_.push(h);this.isRejectionHandled_=!0};r.resolve=v;r.reject=function(l){return new r(function(d,h){h(l)})};r.race=function(l){return new r(function(d,h){for(var m=$jscomp.makeIterator(l),e=m.next();!e.done;e=m.next())v(e.value).callWhenSettled_(d,h)})};r.all=function(l){var d=$jscomp.makeIterator(l),h=d.next();return h.done?v([]):new r(function(m,
e){function k(n){return function(p){g[n]=p;w--;0==w&&m(g)}}var g=[],w=0;do g.push(void 0),w++,v(h.value).callWhenSettled_(k(g.length-1),e),h=d.next();while(!h.done)})};return r},"es6","es3");$jscomp.checkStringArgs=function(u,z,v){if(null==u)throw new TypeError("The 'this' value for String.prototype."+v+" must not be null or undefined");if(z instanceof RegExp)throw new TypeError("First argument to String.prototype."+v+" must not be a regular expression");return u+""};
$jscomp.polyfill("String.prototype.endsWith",function(u){return u?u:function(z,v){var t=$jscomp.checkStringArgs(this,z,"endsWith");z+="";void 0===v&&(v=t.length);v=Math.max(0,Math.min(v|0,t.length));for(var r=z.length;0<r&&0<v;)if(t[--v]!=z[--r])return!1;return 0>=r}},"es6","es3");$jscomp.findInternal=function(u,z,v){u instanceof String&&(u=String(u));for(var t=u.length,r=0;r<t;r++){var A=u[r];if(z.call(v,A,r,u))return{i:r,v:A}}return{i:-1,v:void 0}};
$jscomp.polyfill("Array.prototype.find",function(u){return u?u:function(z,v){return $jscomp.findInternal(this,z,v).v}},"es6","es3");$jscomp.underscoreProtoCanBeSet=function(){var u={a:!0},z={};try{return z.__proto__=u,z.a}catch(v){}return!1};$jscomp.setPrototypeOf=$jscomp.TRUST_ES6_POLYFILLS&&"function"==typeof Object.setPrototypeOf?Object.setPrototypeOf:$jscomp.underscoreProtoCanBeSet()?function(u,z){u.__proto__=z;if(u.__proto__!==z)throw new TypeError(u+" is not extensible");return u}:null;
$jscomp.assign=$jscomp.TRUST_ES6_POLYFILLS&&"function"==typeof Object.assign?Object.assign:function(u,z){for(var v=1;v<arguments.length;v++){var t=arguments[v];if(t)for(var r in t)$jscomp.owns(t,r)&&(u[r]=t[r])}return u};
$jscomp.polyfill("Set",function(u){function z(){if($jscomp.ASSUME_NO_NATIVE_SET||!u||"function"!=typeof u||!u.prototype.entries||"function"!=typeof Object.seal)return!1;try{var t=Object.seal({x:4}),r=new u($jscomp.makeIterator([t]));if(!r.has(t)||1!=r.size||r.add(t)!=r||1!=r.size||r.add({x:4})!=r||2!=r.size)return!1;var A=r.entries(),l=A.next();if(l.done||l.value[0]!=t||l.value[1]!=t)return!1;l=A.next();return l.done||l.value[0]==t||4!=l.value[0].x||l.value[1]!=l.value[0]?!1:A.next().done}catch(d){return!1}}
if($jscomp.USE_PROXY_FOR_ES6_CONFORMANCE_CHECKS){if(u&&$jscomp.ES6_CONFORMANCE)return u}else if(z())return u;var v=function(t){this.map_=new Map;if(t){t=$jscomp.makeIterator(t);for(var r;!(r=t.next()).done;)this.add(r.value)}this.size=this.map_.size};v.prototype.add=function(t){t=0===t?0:t;this.map_.set(t,t);this.size=this.map_.size;return this};v.prototype.delete=function(t){t=this.map_.delete(t);this.size=this.map_.size;return t};v.prototype.clear=function(){this.map_.clear();this.size=0};v.prototype.has=
function(t){return this.map_.has(t)};v.prototype.entries=function(){return this.map_.entries()};v.prototype.values=function(){return this.map_.values()};v.prototype.keys=v.prototype.values;v.prototype[Symbol.iterator]=v.prototype.values;v.prototype.forEach=function(t,r){var A=this;this.map_.forEach(function(l){return t.call(r,l,l,A)})};return v},"es6","es3");
$jscomp.iteratorFromArray=function(u,z){u instanceof String&&(u+="");var v=0,t=!1,r={next:function(){if(!t&&v<u.length){var A=v++;return{value:z(A,u[A]),done:!1}}t=!0;return{done:!0,value:void 0}}};r[Symbol.iterator]=function(){return r};return r};$jscomp.polyfill("Array.prototype.keys",function(u){return u?u:function(){return $jscomp.iteratorFromArray(this,function(z){return z})}},"es6","es3");
(function(u){function z(t){if(v[t])return v[t].exports;var r=v[t]={i:t,l:!1,exports:{}};u[t].call(r.exports,r,r.exports,z);r.l=!0;return r.exports}var v={};z.m=u;z.c=v;z.d=function(t,r,A){z.o(t,r)||Object.defineProperty(t,r,{enumerable:!0,get:A})};z.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});Object.defineProperty(t,"__esModule",{value:!0})};z.t=function(t,r){r&1&&(t=z(t));if(r&8||r&4&&"object"===typeof t&&t&&t.__esModule)return t;
var A=Object.create(null);z.r(A);Object.defineProperty(A,"default",{enumerable:!0,value:t});if(r&2&&"string"!=typeof t)for(var l in t)z.d(A,l,function(d){return t[d]}.bind(null,l));return A};z.n=function(t){var r=t&&t.__esModule?function(){return t["default"]}:function(){return t};z.d(r,"a",r);return r};z.o=function(t,r){return Object.prototype.hasOwnProperty.call(t,r)};z.p="/core/pdf/";return z(z.s=19)})([function(u,z,v){v.d(z,"d",function(){return A});v.d(z,"e",function(){return r});v.d(z,"c",function(){return l});
v.d(z,"a",function(){return d});v.d(z,"b",function(){return h});var t=v(2),r=function(m,e){Object(t.a)("disableLogs")||(e?console.warn("".concat(m,": ").concat(e)):console.warn(m))},A=function(m,e){Object(t.a)("disableLogs")||(e?console.log("".concat(m,": ").concat(e)):console.log(m))},l=function(m){if(!Object(t.a)("disableLogs"))throw console.error(m),Error(m);},d=function(m,e){},h=function(m,e){}},function(u,z,v){v.d(z,"c",function(){return h});v.d(z,"a",function(){return m});v.d(z,"b",function(){return e});
v.d(z,"d",function(){return k});var t=v(14),r=console.log,A=console.warn,l=console.error,d=function(g){void 0===g&&(g=!0);g?(console.log=function(){},console.warn=function(){},console.error=function(){}):(console.log=r,console.warn=A,console.error=l)},h=function(){var g=Object(t.a)(location.search);d("1"===g.disableLogs)},m=function(g){g.on("disableLogs",function(w){d(w.disabled)})},e=function(g,w){return function(){}},k=function(g,w){w?console.warn("".concat(g,": ").concat(w)):console.warn(g)}},
function(u,z,v){v.d(z,"a",function(){return A});v.d(z,"b",function(){return l});var t={},r={flattenedResources:!1,CANVAS_CACHE_SIZE:void 0,maxPagesBefore:void 0,maxPagesAhead:void 0,disableLogs:!1,wvsQueryParameters:{},_trnDebugMode:!1,_logFiltersEnabled:null},A=function(d){return r[d]},l=function(d,h){var m;r[d]=h;null===(m=t[d])||void 0===m?void 0:m.forEach(function(e){e(h)})}},function(u,z,v){v.d(z,"a",function(){return w});v.d(z,"b",function(){return y});var t=v(11),r=v(0),A=v(8),l=v(4),d="undefined"===
typeof window?self:window,h=d.importScripts,m=!1,e=function(D,x){m||(h("".concat(d.basePath,"decode.min.js")),m=!0);D=self.BrotliDecode(Object(l.b)(D));return x?D:Object(l.a)(D)},k=function(D,x){return Object(t.a)(void 0,void 0,Promise,function(){var E;return Object(t.b)(this,function(F){switch(F.label){case 0:return m?[3,2]:[4,Object(A.a)("".concat(self.Core.getWorkerPath(),"external/decode.min.js"),"Failed to download decode.min.js",window)];case 1:F.sent(),m=!0,F.label=2;case 2:return E=self.BrotliDecode(Object(l.b)(D)),
[2,x?E:Object(l.a)(E)]}})})};(function(){function D(){this.remainingDataArrays=[]}D.prototype.processRaw=function(x){return x};D.prototype.processBrotli=function(x){this.remainingDataArrays.push(x);return null};D.prototype.GetNextChunk=function(x){this.decodeFunction||(this.decodeFunction=0===x[0]&&97===x[1]&&115===x[2]&&109===x[3]?this.processRaw:this.processBrotli);return this.decodeFunction(x)};D.prototype.End=function(){if(this.remainingDataArrays.length){for(var x=this.arrays,E=0,F=0;F<x.length;++F)E+=
x[F].length;E=new Uint8Array(E);var H=0;for(F=0;F<x.length;++F){var a=x[F];E.set(a,H);H+=a.length}return e(E,!0)}return null};return D})();var g=function(D){var x=!D.shouldOutputArray,E=new XMLHttpRequest;E.open("GET",D.url,D.isAsync);var F=x&&E.overrideMimeType;E.responseType=F?"text":"arraybuffer";F&&E.overrideMimeType("text/plain; charset=x-user-defined");E.send();var H=function(){var b=Date.now();var c=F?E.responseText:new Uint8Array(E.response);Object(r.a)("worker","Result length is ".concat(c.length));
c.length<D.compressedMaximum?(c=D.decompressFunction(c,D.shouldOutputArray),Object(r.e)("There may be some degradation of performance. Your server has not been configured to serve .gz. and .br. files with the expected Content-Encoding. See https://docs.apryse.com/documentation/web/faq/content-encoding/ for instructions on how to resolve this."),h&&Object(r.a)("worker","Decompressed length is ".concat(c.length))):x&&(c=Object(l.a)(c));h&&Object(r.a)("worker","".concat(D.url," Decompression took ").concat(Date.now()-
b));return c};if(D.isAsync)var a=new Promise(function(b,c){E.onload=function(){200===this.status||0===this.status?b(H()):c("Download Failed ".concat(D.url))};E.onerror=function(){c("Network error occurred ".concat(D.url))}});else{if(200===E.status||0===E.status)return H();throw Error("Failed to load ".concat(D.url));}return a},w=function(D){var x=D.lastIndexOf("/");-1===x&&(x=0);var E=D.slice(x).replace(".",".br.");h||(E.endsWith(".js.mem")?E=E.replace(".js.mem",".mem"):E.endsWith(".js")&&(E=E.concat(".mem")));
return D.slice(0,x)+E},n=function(D,x){x.url=w(D);x.decompressFunction=h?e:k;return g(x)},p=function(D,x,E,F){return D.catch(function(H){Object(r.e)(H);return F(x,E)})},y=function(D,x,E,F){a:{var H=[n];x={compressedMaximum:x,isAsync:E,shouldOutputArray:F};if(x.isAsync){var a=H[0](D,x);for(E=1;E<H.length;++E)a=p(a,D,x,H[E])}else{for(E=0;E<H.length;++E)try{a=H[E](D,x);break a}catch(b){Object(r.e)(b.message)}throw Error("");}}return a}},function(u,z,v){v.d(z,"b",function(){return t});v.d(z,"a",function(){return r});
var t=function(A){if("string"===typeof A){for(var l=new Uint8Array(A.length),d=A.length,h=0;h<d;h++)l[h]=A.charCodeAt(h);return l}return A},r=function(A){if("string"!==typeof A){for(var l="",d=0,h=A.length,m;d<h;)m=A.subarray(d,d+1024),d+=1024,l+=String.fromCharCode.apply(null,m);return l}return A}},function(u,z,v){v.d(z,"a",function(){return n});var t,r="undefined"===typeof window?self:window;u=function(){var p=navigator.userAgent.toLowerCase();return(p=/(msie) ([\w.]+)/.exec(p)||/(trident)(?:.*? rv:([\w.]+)|)/.exec(p))?
parseInt(p[2],10):p}();var A=function(){var p=r.navigator.userAgent.match(/OPR/),y=r.navigator.userAgent.match(/Maxthon/),D=r.navigator.userAgent.match(/Edge/);return r.navigator.userAgent.match(/Chrome\/(.*?) /)&&!p&&!y&&!D}();(function(){if(!A)return null;var p=r.navigator.userAgent.match(/Chrome\/([0-9]+)\./);return p?parseInt(p[1],10):p})();var l=!!navigator.userAgent.match(/Edge/i)||navigator.userAgent.match(/Edg\/(.*?)/)&&r.navigator.userAgent.match(/Chrome\/(.*?) /);(function(){if(!l)return null;
var p=r.navigator.userAgent.match(/Edg\/([0-9]+)\./);return p?parseInt(p[1],10):p})();z=/iPad|iPhone|iPod/.test(r.navigator.platform)||"MacIntel"===navigator.platform&&1<navigator.maxTouchPoints||/iPad|iPhone|iPod/.test(r.navigator.userAgent);var d=function(){var p=r.navigator.userAgent.match(/.*\/([0-9\.]+)\s(Safari|Mobile).*/i);return p?parseFloat(p[1]):p}(),h=/^((?!chrome|android).)*safari/i.test(r.navigator.userAgent)||/^((?!chrome|android).)*$/.test(r.navigator.userAgent)&&z;h&&/^((?!chrome|android).)*safari/i.test(navigator.userAgent)&&
parseInt(null===(t=navigator.userAgent.match(/Version\/(\d+)/))||void 0===t?void 0:t[1],10);var m=r.navigator.userAgent.match(/Firefox/);(function(){if(!m)return null;var p=r.navigator.userAgent.match(/Firefox\/([0-9]+)\./);return p?parseInt(p[1],10):p})();u||/Android|webOS|Touch|IEMobile|Silk/i.test(navigator.userAgent);navigator.userAgent.match(/(iPad|iPhone|iPod)/i);r.navigator.userAgent.indexOf("Android");var e=/Mac OS X 10_13_6.*\(KHTML, like Gecko\)$/.test(r.navigator.userAgent),k=r.navigator.userAgent.match(/(iPad|iPhone).+\sOS\s((\d+)(_\d)*)/i)?
14<=parseInt(r.navigator.userAgent.match(/(iPad|iPhone).+\sOS\s((\d+)(_\d)*)/i)[3],10):!1,g=!(!self.WebAssembly||!self.WebAssembly.validate),w=-1<r.navigator.userAgent.indexOf("Edge/16")||-1<r.navigator.userAgent.indexOf("MSAppHost"),n=function(){return g&&!w&&!(!k&&(h&&14>d||e))}},function(u,z,v){function t(d){"@babel/helpers - typeof";return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(h){return typeof h}:function(h){return h&&"function"==typeof Symbol&&h.constructor===
Symbol&&h!==Symbol.prototype?"symbol":typeof h},t(d)}var r,A,l;!function(d){"object"===t(z)&&"undefined"!==typeof u?u.exports=d():!(A=[],r=d,l="function"===typeof r?r.apply(z,A):r,void 0!==l&&(u.exports=l))}(function(){return function k(h,m,e){function g(p,y){if(!m[p]){if(!h[p]){if(w)return w(p,!0);y=Error("Cannot find module '".concat(p,"'"));throw y.code="MODULE_NOT_FOUND",y;}y=m[p]={exports:{}};h[p][0].call(y.exports,function(D){return g(h[p][1][D]||D)},y,y.exports,k,h,m,e)}return m[p].exports}
for(var w=!1,n=0;n<e.length;n++)g(e[n]);return g}({1:[function(h,m,e){var k={}.hasOwnProperty,g=function(w,n){function p(){this.constructor=w}for(var y in n)k.call(n,y)&&(w[y]=n[y]);p.prototype=n.prototype;w.prototype=new p;w.__super__=n.prototype;return w};e=h("./PriorityQueue/AbstractPriorityQueue");h=h("./PriorityQueue/ArrayStrategy");e=function(w){function n(p){p||(p={});p.strategy||(p.strategy=BinaryHeapStrategy);p.comparator||(p.comparator=function(y,D){return(y||0)-(D||0)});n.__super__.constructor.call(this,
p)}g(n,w);return n}(e);e.ArrayStrategy=h;m.exports=e},{"./PriorityQueue/AbstractPriorityQueue":2,"./PriorityQueue/ArrayStrategy":3}],2:[function(h,m,e){m.exports=function(){function k(g){if(null==(null!=g?g.strategy:void 0))throw"Must pass options.strategy, a strategy";if(null==(null!=g?g.comparator:void 0))throw"Must pass options.comparator, a comparator";this.priv=new g.strategy(g);this.length=0}k.prototype.queue=function(g){this.length++;this.priv.queue(g)};k.prototype.dequeue=function(g){if(!this.length)throw"Empty queue";
this.length--;return this.priv.dequeue()};k.prototype.peek=function(g){if(!this.length)throw"Empty queue";return this.priv.peek()};k.prototype.remove=function(g){this.priv.remove(g)&&--this.length};k.prototype.find=function(g){return 0<=this.priv.find(g)};k.prototype.removeAllMatching=function(g,w){g=this.priv.removeAllMatching(g,w);this.length-=g};return k}()},{}],3:[function(h,m,e){var k=function(g,w,n){var p;var y=0;for(p=g.length;y<p;){var D=y+p>>>1;0<=n(g[D],w)?y=D+1:p=D}return y};m.exports=
function(){function g(w){var n;this.options=w;this.comparator=this.options.comparator;this.data=(null!=(n=this.options.initialValues)?n.slice(0):void 0)||[];this.data.sort(this.comparator).reverse()}g.prototype.queue=function(w){var n=k(this.data,w,this.comparator);this.data.splice(n,0,w)};g.prototype.dequeue=function(){return this.data.pop()};g.prototype.peek=function(){return this.data[this.data.length-1]};g.prototype.find=function(w){var n=k(this.data,w,this.comparator)-1;return 0<=n&&!this.comparator(this.data[n],
w)?n:-1};g.prototype.remove=function(w){w=this.find(w);return 0<=w?(this.data.splice(w,1),!0):!1};g.prototype.removeAllMatching=function(w,n){for(var p=0,y=this.data.length-1;0<=y;--y)if(w(this.data[y])){var D=this.data.splice(y,1)[0];n&&n(D);++p}return p};return g}()},{}]},{},[1])(1)})},function(u,z,v){(function(t){function r(d,h){this._id=d;this._clearFn=h}var A="undefined"!==typeof t&&t||"undefined"!==typeof self&&self||window,l=Function.prototype.apply;z.setTimeout=function(){return new r(l.call(setTimeout,
A,arguments),clearTimeout)};z.setInterval=function(){return new r(l.call(setInterval,A,arguments),clearInterval)};z.clearTimeout=z.clearInterval=function(d){d&&d.close()};r.prototype.unref=r.prototype.ref=function(){};r.prototype.close=function(){this._clearFn.call(A,this._id)};z.enroll=function(d,h){clearTimeout(d._idleTimeoutId);d._idleTimeout=h};z.unenroll=function(d){clearTimeout(d._idleTimeoutId);d._idleTimeout=-1};z._unrefActive=z.active=function(d){clearTimeout(d._idleTimeoutId);var h=d._idleTimeout;
0<=h&&(d._idleTimeoutId=setTimeout(function(){d._onTimeout&&d._onTimeout()},h))};v(23);z.setImmediate="undefined"!==typeof self&&self.setImmediate||"undefined"!==typeof t&&t.setImmediate||this&&this.setImmediate;z.clearImmediate="undefined"!==typeof self&&self.clearImmediate||"undefined"!==typeof t&&t.clearImmediate||this&&this.clearImmediate}).call(this,v(10))},function(u,z,v){function t(A,l,d){return new Promise(function(h){if(!A)return h();var m=d.document.createElement("script");m.type="text/javascript";
m.onload=function(){h()};m.onerror=function(){l&&Object(r.e)(l);h()};m.src=A;d.document.getElementsByTagName("head")[0].appendChild(m)})}v.d(z,"a",function(){return t});var r=v(0)},function(u,z,v){function t(d,h,m){function e(w){g=g||Date.now();return w?(Object(r.a)("load","Try instantiateStreaming"),fetch(Object(A.a)(d)).then(function(n){return WebAssembly.instantiateStreaming(n,h)}).catch(function(n){Object(r.a)("load","instantiateStreaming Failed ".concat(d," message ").concat(n.message));return e(!1)})):
Object(A.b)(d,m,!0,!0).then(function(n){k=Date.now();Object(r.a)("load","Request took ".concat(k-g," ms"));return WebAssembly.instantiate(n,h)})}var k,g;return e(!!WebAssembly.instantiateStreaming).then(function(w){Object(r.a)("load","WASM compilation took ".concat(Date.now()-(k||g)," ms"));return w})}v.d(z,"a",function(){return t});var r=v(0),A=v(3),l=v(8);v.d(z,"b",function(){return l.a})},function(u,z){z=function(){return this}();try{z=z||(new Function("return this"))()}catch(v){"object"===typeof window&&
(z=window)}u.exports=z},function(u,z,v){function t(A,l,d,h){function m(e){return e instanceof d?e:new d(function(k){k(e)})}return new (d||(d=Promise))(function(e,k){function g(p){try{n(h.next(p))}catch(y){k(y)}}function w(p){try{n(h["throw"](p))}catch(y){k(y)}}function n(p){p.done?e(p.value):m(p.value).then(g,w)}n((h=h.apply(A,l||[])).next())})}function r(A,l){function d(n){return function(p){return h([n,p])}}function h(n){if(e)throw new TypeError("Generator is already executing.");for(;w&&(w=0,n[0]&&
(m=0)),m;)try{if(e=1,k&&(g=n[0]&2?k["return"]:n[0]?k["throw"]||((g=k["return"])&&g.call(k),0):k.next)&&!(g=g.call(k,n[1])).done)return g;if(k=0,g)n=[n[0]&2,g.value];switch(n[0]){case 0:case 1:g=n;break;case 4:return m.label++,{value:n[1],done:!1};case 5:m.label++;k=n[1];n=[0];continue;case 7:n=m.ops.pop();m.trys.pop();continue;default:if(!(g=m.trys,g=0<g.length&&g[g.length-1])&&(6===n[0]||2===n[0])){m=0;continue}if(3===n[0]&&(!g||n[1]>g[0]&&n[1]<g[3]))m.label=n[1];else if(6===n[0]&&m.label<g[1])m.label=
g[1],g=n;else if(g&&m.label<g[2])m.label=g[2],m.ops.push(n);else{g[2]&&m.ops.pop();m.trys.pop();continue}}n=l.call(A,m)}catch(p){n=[6,p],k=0}finally{e=g=0}if(n[0]&5)throw n[1];return{value:n[0]?n[1]:void 0,done:!0}}var m={label:0,sent:function(){if(g[0]&1)throw g[1];return g[1]},trys:[],ops:[]},e,k,g,w=Object.create(("function"===typeof Iterator?Iterator:Object).prototype);return w.next=d(0),w["throw"]=d(1),w["return"]=d(2),"function"===typeof Symbol&&(w[Symbol.iterator]=function(){return this}),
w}v.d(z,"a",function(){return t});v.d(z,"b",function(){return r})},function(u,z){function v(){throw Error("setTimeout has not been defined");}function t(){throw Error("clearTimeout has not been defined");}function r(y){if(e===setTimeout)return setTimeout(y,0);if((e===v||!e)&&setTimeout)return e=setTimeout,setTimeout(y,0);try{return e(y,0)}catch(D){try{return e.call(null,y,0)}catch(x){return e.call(this,y,0)}}}function A(y){if(k===clearTimeout)return clearTimeout(y);if((k===t||!k)&&clearTimeout)return k=
clearTimeout,clearTimeout(y);try{return k(y)}catch(D){try{return k.call(null,y)}catch(x){return k.call(this,y)}}}function l(){w&&n&&(w=!1,n.length?g=n.concat(g):p=-1,g.length&&d())}function d(){if(!w){var y=r(l);w=!0;for(var D=g.length;D;){n=g;for(g=[];++p<D;)n&&n[p].run();p=-1;D=g.length}n=null;w=!1;A(y)}}function h(y,D){this.fun=y;this.array=D}function m(){}u=u.exports={};try{var e="function"===typeof setTimeout?setTimeout:v}catch(y){e=v}try{var k="function"===typeof clearTimeout?clearTimeout:t}catch(y){k=
t}var g=[],w=!1,n,p=-1;u.nextTick=function(y){var D=Array(arguments.length-1);if(1<arguments.length)for(var x=1;x<arguments.length;x++)D[x-1]=arguments[x];g.push(new h(y,D));1!==g.length||w||r(d)};h.prototype.run=function(){this.fun.apply(null,this.array)};u.title="browser";u.browser=!0;u.env={};u.argv=[];u.version="";u.versions={};u.on=m;u.addListener=m;u.once=m;u.off=m;u.removeListener=m;u.removeAllListeners=m;u.emit=m;u.prependListener=m;u.prependOnceListener=m;u.listeners=function(y){return[]};
u.binding=function(y){throw Error("process.binding is not supported");};u.cwd=function(){return"/"};u.chdir=function(y){throw Error("process.chdir is not supported");};u.umask=function(){return 0}},function(u,z,v){z.a=function(){ArrayBuffer.prototype.slice||(ArrayBuffer.prototype.slice=function(t,r){void 0===t&&(t=0);void 0===r&&(r=this.byteLength);t=Math.floor(t);r=Math.floor(r);0>t&&(t+=this.byteLength);0>r&&(r+=this.byteLength);t=Math.min(Math.max(0,t),this.byteLength);r=Math.min(Math.max(0,r),
this.byteLength);if(0>=r-t)return new ArrayBuffer(0);var A=new ArrayBuffer(r-t),l=new Uint8Array(A);t=new Uint8Array(this,t,r-t);l.set(t);return A})}},function(u,z,v){z.a=function(t){var r={};decodeURIComponent(t.slice(1)).split("&").forEach(function(A){A=A.split("=",2);r[A[0]]=A[1]});return r}},function(u,z,v){(function(t){function r(E){"function"!==typeof E&&(E=new Function("".concat(E)));for(var F=Array(arguments.length-1),H=0;H<F.length;H++)F[H]=arguments[H+1];n[w]={callback:E,args:F};D(w);return w++}
function A(E){delete n[E]}function l(E){if(p)setTimeout(l,0,E);else{var F=n[E];if(F){p=!0;try{var H=F.callback,a=F.args;switch(a.length){case 0:H();break;case 1:H(a[0]);break;case 2:H(a[0],a[1]);break;case 3:H(a[0],a[1],a[2]);break;default:H.apply(void 0,a)}}finally{A(E),p=!1}}}}function d(){D=function(E){t.nextTick(function(){l(E)})}}function h(){if(g.postMessage&&!g.importScripts){var E=!0,F=g.onmessage;g.onmessage=function(){E=!1};g.postMessage("","*");g.onmessage=F;return E}}function m(){var E=
"setImmediate$".concat(Math.random(),"$"),F=function(H){H.source!==g&&H.source!==g.parent||"string"!==typeof H.data||0!==H.data.indexOf(E)||l(+H.data.slice(E.length))};g.addEventListener?g.addEventListener("message",F,!1):g.attachEvent("onmessage",F);D=function(H){g.postMessage(E+H,"*")}}function e(){var E=y.documentElement;D=function(F){var H=y.createElement("script");H.onreadystatechange=function(){l(F);H.onreadystatechange=null;E.removeChild(H);H=null};E.appendChild(H)}}function k(){D=function(E){setTimeout(l,
0,E)}}var g="undefined"===typeof window?self:window,w=1,n={},p=!1,y=g.document,D,x=Object.getPrototypeOf&&Object.getPrototypeOf(g);x=x&&x.setTimeout?x:g;"[object process]"==={}.toString.call(g.process)?d():h()?m():y&&"onreadystatechange"in y.createElement("script")?e():k();x.setImmediate=r;x.clearImmediate=A;z.a={setImmediate:r,clearImmediate:A}}).call(this,v(12))},function(u,z,v){var t=v(0),r=v(2);u=function(){function A(l,d){this.name=l;this.comObj=d;this.callbackIndex=1;this.postMessageTransfers=
!0;this.callbacksCapabilities={};this.actionHandler={};this.actionHandlerAsync={};this.pdfnetCommandChain=this.nextAsync=null;this.pdfnetActiveCommands=new Set;this.actionHandler.console_log=[function(h){Object(t.d)(h)}];this.actionHandler.console_error=[function(h){Object(t.c)(h)}];this.actionHandler.workerLoaded=[function(){}];this.msgHandler=this.handleMessage.bind(this);d.addEventListener("message",this.msgHandler)}A.prototype.on=function(l,d,h){var m=this.actionHandler;m[l]&&Object(t.c)('There is already an actionName called "'.concat(l,
'"'));m[l]=[d,h]};A.prototype.clearActionHandlers=function(){this.actionHandler={};this.comObj.removeEventListener("message",this.msgHandler)};A.prototype.reset=function(){this.clearActionHandlers();this.comObj.reset&&this.comObj.reset()};A.prototype.replace=function(l,d,h){this.actionHandler[l]=[d,h]};A.prototype.onAsync=function(l,d,h){var m=this.actionHandlerAsync;m[l]&&Object(t.c)('There is already an actionName called "'.concat(l,'"'));m[l]=[d,h]};A.prototype.replaceAsync=function(l,d,h){var m=
this.actionHandlerAsync,e=this.actionHandler;e[l]&&delete e[l];m[l]=[d,h]};A.prototype.onNextAsync=function(l){this.nextAsync=l};A.prototype.send=function(l,d){this.postMessage({action:l,data:d})};A.prototype.getNextId=function(){return this.callbackIndex++};A.prototype.sendWithPromise=function(l,d,h){var m=this.getNextId();l={action:l,data:d,callbackId:m,priority:h};d=window.createPromiseCapability();this.callbacksCapabilities[m]=d;try{this.postMessage(l)}catch(e){d.reject(e)}return d.promise};A.prototype.sendWithPromiseReturnId=
function(l,d,h){var m=this.getNextId();l={action:l,data:d,callbackId:m,priority:h};d=window.createPromiseCapability();this.callbacksCapabilities[m]=d;try{this.postMessage(l)}catch(e){d.reject(e)}return{promise:d.promise,callbackId:m}};A.prototype.sendWithPromiseWithId=function(l,d,h){d>this.callbackIndex&&Object(t.c)("Can't reuse callbackId ".concat(d," lesser than callbackIndex ").concat(this.callbackIndex));d in this.callbacksCapabilities&&Object(t.c)("Can't reuse callbackId ".concat(d,". There is a capability waiting to be resolved. "));
l={action:l,data:h,callbackId:d};h=window.createPromiseCapability();this.callbacksCapabilities[d]=h;try{this.postMessage(l)}catch(m){h.reject(m)}return h.promise};A.prototype.sendError=function(l,d){if(l.message||l.errorData){l.message&&l.message.message&&(l.message=l.message.message);var h=l.errorData;l={type:l.type?l.type:"JavascriptError",message:l.message};h&&Object.keys(h).forEach(function(m){h.hasOwnProperty(m)&&(l[m]=h[m])})}this.postMessage({isReply:!0,callbackId:d,error:l})};A.prototype.getPromise=
function(l){if(l in this.callbacksCapabilities)return this.callbacksCapabilities[l];Object(t.c)("Cannot get promise for callback ".concat(l))};A.prototype.cancelPromise=function(l){if(l in this.callbacksCapabilities){var d=this.callbacksCapabilities[l];delete this.callbacksCapabilities[l];this.pdfnetActiveCommands.has(l)&&this.pdfnetActiveCommands.delete(l);d.reject({type:"Cancelled",message:"Request has been cancelled."});this.postMessage({action:"actionCancel",data:{callbackId:l}})}else Object(t.b)("Cannot cancel callback ".concat(l))};
A.prototype.postMessage=function(l){"officeeditor"!==this.name&&Object(r.a)("enableWorkerLogs")&&Object(t.d)("PDFWorker","".concat(performance.now()," Sent ").concat(JSON.stringify(l)));if(this.postMessageTransfers){var d=this.getTransfersArray(l);this.comObj.postMessage(l,d)}else this.comObj.postMessage(l)};A.prototype.getObjectTransfers=function(l,d){var h=this;null!==l&&"object"===typeof l&&(l instanceof Uint8Array?d.push(l.buffer):l instanceof ArrayBuffer?d.push(l):Object.keys(l).forEach(function(m){l.hasOwnProperty(m)&&
h.getObjectTransfers(l[m],d)}))};A.prototype.getTransfersArray=function(l){var d=[];this.getObjectTransfers(l,d);return 0===d.length?void 0:d};A.prototype.handleMessage=function(l){var d=this,h=l.data;"officeeditor"!==this.name&&Object(r.a)("enableWorkerLogs")&&Object(t.d)("PDFWorker","".concat(performance.now()," Received ").concat(JSON.stringify(h)));var m=this.actionHandler,e=this.actionHandlerAsync;l=this.callbacksCapabilities;var k=this.pdfnetActiveCommands;if(h.isReply)m=h.callbackId,m in l?
(e=l[m],delete l[m],k.has(m)&&k.delete(m),"error"in h?e.reject(h.error):e.resolve(h.data)):Object(t.a)("Cannot resolve callback ".concat(m));else if(h.action in m){var g=m[h.action];h.callbackId?Promise.resolve().then(function(){return g[0].call(g[1],h.data)}).then(function(w){d.postMessage({isReply:!0,callbackId:h.callbackId,data:w})},function(w){d.sendError(w,h.callbackId)}):g[0].call(g[1],h.data)}else h.action in e?(g=e[h.action],h.callbackId?g[0].call(g[1],h).then(function(w){d.postMessage({isReply:!0,
callbackId:h.callbackId,data:w});d.nextAsync()},function(w){d.sendError(w,h.callbackId);d.nextAsync()}):g[0].call(g[1],h).then(function(){d.nextAsync()},function(){d.nextAsync()})):Object(t.c)("Unknown action from worker: ".concat(h.action))};return A}();z.a=u},function(u,z,v){v.d(z,"a",function(){return d});var t=v(3),r=v(9),A=v(5),l=function(){function h(m){var e=this;this.promise=m.then(function(k){e.response=k;e.status=200})}h.prototype.addEventListener=function(m,e){this.promise.then(e)};return h}(),
d=function(h,m,e){if(Object(A.a)()&&!e){self.Module.instantiateWasm=function(g,w){return Object(r.a)("".concat(h,"Wasm.wasm"),g,m["Wasm.wasm"]).then(function(n){w(n.instance)})};if(m.disableObjectURLBlobs){importScripts("".concat(h,"Wasm.js"));return}e=Object(t.b)("".concat(h,"Wasm.js.mem"),m["Wasm.js.mem"],!1,!1)}else{if(m.disableObjectURLBlobs){importScripts("".concat((self.Module.asmjsPrefix?self.Module.asmjsPrefix:"")+h,".js"));return}e=Object(t.b)("".concat((self.Module.asmjsPrefix?self.Module.asmjsPrefix:
"")+h,".js.mem"),m[".js.mem"],!1);var k=Object(t.b)("".concat((self.Module.memoryInitializerPrefixURL?self.Module.memoryInitializerPrefixURL:"")+h,".mem"),m[".mem"],!0,!0);self.Module.memoryInitializerRequest=new l(k)}e=new Blob([e],{type:"application/javascript"});importScripts(URL.createObjectURL(e))}},function(u,z,v){v.d(z,"a",function(){return t});var t="optimized/"},function(u,z,v){u.exports=v(20)},function(u,z,v){v.r(z);v(5);u=v(13);v(21);v(22);v(25);v(26);v(27);v(28);v(29);Object(u.a)()},function(u,
z,v){(function(t){"undefined"===typeof t.crypto&&(t.crypto={getRandomValues:function(r){for(var A=0;A<r.length;A++)r[A]=256*Math.random()}})})("undefined"===typeof window?self:window)},function(u,z,v){(function(t,r){function A(l){"@babel/helpers - typeof";return A="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(d){return typeof d}:function(d){return d&&"function"==typeof Symbol&&d.constructor===Symbol&&d!==Symbol.prototype?"symbol":typeof d},A(l)}(function(l){function d(){for(var B=
0;B<C.length;B++)C[B][0](C[B][1]);C=[];G=!1}function h(B,I){C.push([B,I]);G||(G=!0,f(d,0))}function m(B,I){function J(K){g(I,K)}function L(K){n(I,K)}try{B(J,L)}catch(K){L(K)}}function e(B){var I=B.owner,J=I.state_;I=I.data_;var L=B[J];B=B.then;if("function"===typeof L){J=b;try{I=L(I)}catch(K){n(B,K)}}k(B,I)||(J===b&&g(B,I),J===c&&n(B,I))}function k(B,I){var J;try{if(B===I)throw new TypeError("A promises callback cannot return that same promise.");if(I&&("function"===typeof I||"object"===A(I))){var L=
I.then;if("function"===typeof L)return L.call(I,function(K){J||(J=!0,I!==K?g(B,K):w(B,K))},function(K){J||(J=!0,n(B,K))}),!0}}catch(K){return J||n(B,K),!0}return!1}function g(B,I){B!==I&&k(B,I)||w(B,I)}function w(B,I){B.state_===H&&(B.state_=a,B.data_=I,h(y,B))}function n(B,I){B.state_===H&&(B.state_=a,B.data_=I,h(D,B))}function p(B){var I=B.then_;B.then_=void 0;for(B=0;B<I.length;B++)e(I[B])}function y(B){B.state_=b;p(B)}function D(B){B.state_=c;p(B)}function x(B){if("function"!==typeof B)throw new TypeError("Promise constructor takes a function argument");
if(!(this instanceof x))throw new TypeError("Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.");this.then_=[];m(B,this)}l.createPromiseCapability=function(){var B={};B.promise=new x(function(I,J){B.resolve=I;B.reject=J});return B};var E=l.Promise,F=E&&"resolve"in E&&"reject"in E&&"all"in E&&"race"in E&&function(){var B;new E(function(I){B=I});return"function"===typeof B}();"undefined"!==typeof exports&&exports?(exports.Promise=F?
E:x,exports.Polyfill=x):"function"===typeof define&&v(24)?define(function(){return F?E:x}):F||(l.Promise=x);var H="pending",a="sealed",b="fulfilled",c="rejected",q=function(){},f="undefined"!==typeof r?r:setTimeout,C=[],G;x.prototype={constructor:x,state_:H,then_:null,data_:void 0,then:function(B,I){B={owner:this,then:new this.constructor(q),fulfilled:B,rejected:I};this.state_===b||this.state_===c?h(e,B):this.then_.push(B);return B.then},"catch":function(B){return this.then(null,B)}};x.all=function(B){if("[object Array]"!==
Object.prototype.toString.call(B))throw new TypeError("You must pass an array to Promise.all().");return new this(function(I,J){function L(R){N++;return function(P){K[R]=P;--N||I(K)}}for(var K=[],N=0,M=0,Q;M<B.length;M++)(Q=B[M])&&"function"===typeof Q.then?Q.then(L(M),J):K[M]=Q;N||I(K)})};x.race=function(B){if("[object Array]"!==Object.prototype.toString.call(B))throw new TypeError("You must pass an array to Promise.race().");return new this(function(I,J){for(var L=0,K;L<B.length;L++)(K=B[L])&&"function"===
typeof K.then?K.then(I,J):I(K)})};x.resolve=function(B){return B&&"object"===A(B)&&B.constructor===this?B:new this(function(I){I(B)})};x.reject=function(B){return new this(function(I,J){J(B)})}})("undefined"!==typeof window?window:"undefined"!==typeof t?t:"undefined"!==typeof self?self:void 0)}).call(this,v(10),v(7).setImmediate)},function(u,z,v){(function(t,r){(function(A,l){function d(H){delete y[H]}function h(H){if(D)setTimeout(h,0,H);else{var a=y[H];if(a){D=!0;try{var b=a.callback,c=a.args;switch(c.length){case 0:b();
break;case 1:b(c[0]);break;case 2:b(c[0],c[1]);break;case 3:b(c[0],c[1],c[2]);break;default:b.apply(l,c)}}finally{d(H),D=!1}}}}function m(){E=function(H){r.nextTick(function(){h(H)})}}function e(){if(A.postMessage&&!A.importScripts){var H=!0,a=A.onmessage;A.onmessage=function(){H=!1};A.postMessage("","*");A.onmessage=a;return H}}function k(){var H="setImmediate$"+Math.random()+"$",a=function(b){b.source===A&&"string"===typeof b.data&&0===b.data.indexOf(H)&&h(+b.data.slice(H.length))};A.addEventListener?
A.addEventListener("message",a,!1):A.attachEvent("onmessage",a);E=function(b){A.postMessage(H+b,"*")}}function g(){var H=new MessageChannel;H.port1.onmessage=function(a){h(a.data)};E=function(a){H.port2.postMessage(a)}}function w(){var H=x.documentElement;E=function(a){var b=x.createElement("script");b.onreadystatechange=function(){h(a);b.onreadystatechange=null;H.removeChild(b);b=null};H.appendChild(b)}}function n(){E=function(H){setTimeout(h,0,H)}}if(!A.setImmediate){var p=1,y={},D=!1,x=A.document,
E,F=Object.getPrototypeOf&&Object.getPrototypeOf(A);F=F&&F.setTimeout?F:A;"[object process]"==={}.toString.call(A.process)?m():e()?k():A.MessageChannel?g():x&&"onreadystatechange"in x.createElement("script")?w():n();F.setImmediate=function(H){"function"!==typeof H&&(H=new Function(""+H));for(var a=Array(arguments.length-1),b=0;b<a.length;b++)a[b]=arguments[b+1];y[p]={callback:H,args:a};E(p);return p++};F.clearImmediate=d}})("undefined"===typeof self?"undefined"===typeof t?this:t:self)}).call(this,
v(10),v(12))},function(u,z){u.exports={}},function(u,z,v){(function(t){var r=function(A,l){var d=function k(e){e=this["catch"](e);return{cancel:l,promise:e,then:h.bind(e),"catch":k.bind(e)}},h=function w(k,g){k=this.then(k,g);return{cancel:l,promise:k,then:w.bind(k),"catch":d.bind(k)}};return{cancel:l,promise:A,then:h.bind(A),"catch":d.bind(A)}};t.CancellablePromise=function(A,l){var d=!1,h,m=new Promise(function(e,k){h=function(){d||(l(),k("cancelled"))};(new Promise(A)).then(function(g){d=!0;e(g)},
function(g){d=!0;k(g)})});return r(m,h)};t.CancellablePromise.all=function(A){var l=Promise.all(A);return r(l,function(){A.forEach(function(d){d.cancel&&d.cancel()})})}})("undefined"===typeof self?void 0:self)},function(u,z,v){(function(t,r){function A(m){"@babel/helpers - typeof";return A="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},A(m)}function l(m,
e){var k=Object.keys(m);if(Object.getOwnPropertySymbols){var g=Object.getOwnPropertySymbols(m);e&&(g=g.filter(function(w){return Object.getOwnPropertyDescriptor(m,w).enumerable}));k.push.apply(k,g)}return k}function d(m){for(var e=1;e<arguments.length;e++){var k=null!=arguments[e]?arguments[e]:{};e%2?l(Object(k),!0).forEach(function(g){var w=k[g];a:if("object"===A(g)&&null!==g){var n=g[Symbol.toPrimitive];if(void 0!==n){g=n.call(g,"string");if("object"!==A(g))break a;throw new TypeError("@@toPrimitive must return a primitive value.");
}g=String(g)}g="symbol"===A(g)?g:String(g);g in m?Object.defineProperty(m,g,{value:w,enumerable:!0,configurable:!0,writable:!0}):m[g]=w}):Object.getOwnPropertyDescriptors?Object.defineProperties(m,Object.getOwnPropertyDescriptors(k)):l(Object(k)).forEach(function(g){Object.defineProperty(m,g,Object.getOwnPropertyDescriptor(k,g))})}return m}var h=v(1);(function(m){m.Module={INITIAL_MEMORY:50331648,noExitRuntime:!0,devicePixelRatio:1,cur_doc:null,cachePtrSize:0,hasBufOwnership:!0,loaded:!1,initCb:null,
cachePtr:null,cleanupState:null,docs:{},postEvent:function(e,k,g){Module.workerMessageHandler.send("event",{docId:e,type:k,data:g})},postProgressiveRenderingStartEvent:function(e,k){Module.postEvent(e,"progressiveRenderingStart",{pageNum:k})},postPagesUpdatedEvent:function(e,k,g,w){e={pageDimensions:Module.GetPageDimensions(e)};if(g)for(var n=0;n<g.length;++n)g[n]in e.pageDimensions?(e.pageDimensions[g[n]].contentChanged=!0,w&&(e.pageDimensions[g[n]].annotationsUnchanged=!0)):console.warn("Invalid Page Number ".concat(g[n]));
Module.postEvent(k,"pagesUpdated",e);return e},postPagesRenamedEvent:function(e,k){var g={};e=Module.PDFDocGetPageIterator(e,1);for(var w=1;Module.IteratorHasNext(e);++w){var n=Module.stackSave(),p=Module.IteratorCurrent(e);g[w]=Module.PageGetId(p);Module.IteratorNext(e);Module.stackRestore(n)}Module.postEvent(k,"pagesRenamed",{pageNumToId:g})},GetIndividualPageDimensions:function(e,k,g){e=Module.PageGetPageInfo(g);e.id=Module.PageGetId(g);return e},GetPageDimensionsRange:function(e,k,g){for(var w=
{},n=Module.PDFDocGetPageIterator(e,k);k<g&&Module.IteratorHasNext(n);++k){var p=Module.stackSave(),y=Module.IteratorCurrent(n);w[k]=this.GetIndividualPageDimensions(e,k,y);Module.IteratorNext(n);Module.stackRestore(p)}return w},GetPageDimensionsContentChangedList:function(e,k){k.sort(function(E,F){return E-F});for(var g={},w=k[0],n=k[k.length-1],p=0,y=Module.PDFDocGetPageIterator(e,w);w<=n&&Module.IteratorHasNext(y);++w){if(k[p]==w){for(++p;k[p]==w;)++p;var D=Module.stackSave(),x=Module.IteratorCurrent(y);
x=this.GetIndividualPageDimensions(e,w,x);x.contentChanged=!0;g[w]=x;Module.stackRestore(D)}Module.IteratorNext(y)}return g},GetPageDimensions:function(e){try{var k=Module.stackSave();var g=Module.GetPageCount(e);if(0===g)throw"This document has no pages.";return Module.GetPageDimensionsRange(e,1,g+1)}finally{Module.stackRestore(k)}},loadDoc:function(e,k){"undefined"===typeof Module&&this._main();var g=null;try{var w=Module.stackSave();e.customHandlerId&&Module._TRN_PDFNetAddPDFTronCustomHandler(e.customHandlerId);
k=Module.CreateDoc(e,k);var n=Module.GetDoc(k);if(Module.PDFDocInitSecurityHandler(n))return{docId:k,pageDimensions:Module.GetPageDimensions(n)};g={type:"NeedsPassword",errorData:{docId:k},message:"This document requires a password"}}catch(p){g={type:"InvalidPDF",message:p}}finally{Module.stackRestore(w)}throw g;},loadCanvas:function(e,k,g,w,n,p,y,D){var x=Module.GetDoc(e),E=Module.docs[e].chunkStorage,F=Promise.resolve();if(E&&2===D.overprintMode){var H=Module.GetDownloadData(x);if(!H.docInfoRequested){var a=
Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_DownloaderPrefetchDocInfo(H.downloader,a));0==Module.getValue(a,"i8")&&(F=H.docInfoPromiseCapability.promise);H.docInfoRequested=!0}}return new Promise(function(b,c){var q=k+1,f=function(){b(F.then(function(){return Module.RasterizePage(x,q,g,w,p,n,y,D,e)}))};if(E){var C=Module.GetDownloadData(x).downloader,G=E.getRequiredChunkOffsetArrays(C,q);E.keepChunks(G.have);C=function(){var B=E.getChunks(G.missing);Module.loadPromise=B.then(function(){var I=
Module.loadPromise.cancelled;Module.loadPromise=null;I||f()})["catch"](function(I){"cancelled"!==I?c(I):Module.loadPromise=null})};Module.loadPromise?Module.loadPromise.then(C,C):C()}else f()})},loadResources:function(e,k){Module.Initialize(k);Object(h.b)("worker","PDFNet initialized!");Module._TRN_PDFNetSetDefaultDiskCachingEnabled(!1);e=new Uint8Array(e);Module.PDFNetSetResourceData(e)},onRuntimeInitialized:function(){"undefined"===typeof Module&&(("undefined"!==typeof window?window:self).Module=
{});(function(e){e.PDFDocExportXFDF=function(k,g){k=Module.GetDoc(k);var w=Module.stackSave();try{var n=g?Module.PDFDocFDFExtract(k,g):Module.PDFDocFDFExtract(k);var p=Module.FDFDocSaveAsXFDF(n);Module.stackRestore(w)}catch(y){throw Module.stackRestore(w),y;}return p};e.PageArrayToPageSet=function(k){var g=Module.stackSave();try{var w=Module.PageSetCreate();for(var n=0;n<k.length;++n)Module.PageSetAddPage(w,k[n]);Module.stackRestore(g)}catch(p){throw Module.stackRestore(g),p;}return w};e.cancelCurrent=
function(){var k=Module.loadPromise;return k?(k.cancel(),k.cancelled=!0):(k=Module.cleanupState)?(t(k.timeout),k.cleanupArr.reverse().forEach(function(g){g()}),Module.cleanupState=null,!0):!1};e.SetWorkerRestartCallback=function(k){Module.workerRestartCallback=k};e.XFDFMerge=function(k,g,w){if(g){var n=Module.GetDoc(k),p=[];try{Object(h.b)("worker","Merge XFDF of length ".concat(g.length));var y=Module.GetUStringFromJSString(g,!0);p.push(function(){Module.UStringDestroy(y)});var D=Module.allocate(4,
"i8",Module.ALLOC_STACK);REX(Module._TRN_FDFDocCreateFromXFDF(y,D));var x=Module.getValue(D,"i8*");p.push(function(){Module.FDFDocDestroy(x)});var E=Module.PDFDocFDFUpdate(n,x,w);E&&E.length&&Module.postEvent(k,"apRefChanged",{apRefChanges:E})}finally{p.reverse().forEach(function(F){F()})}}};e.MergeXFDF=function(k,g,w){return new Promise(function(n,p){var y=[];try{var D=Module.stackSave();y[y.length]=function(){Module.stackRestore(D)};Module.XFDFMerge(k,g,w);y.forEach(function(x){x()});n({})}catch(x){y.forEach(function(E){E()}),
p(x)}})};e.CreateBufferFile=function(k,g,w){Module.MakeDev(k);var n=new ArrayBuffer(g);n=new Uint8Array(n);w=w?0:255;for(var p=0;p<g;++p)n[p]=w;Module.docs[k]={buffer:n}};e.ReadBufferFile=function(k,g){var w=Module.docs[k].buffer;g&&(Module.docs[k].buffer=new Uint8Array(w.buffer.slice(0)));return w};e.RemoveBufferFile=function(k){Module.docs[k]=null};e.SaveHelper=function(k,g,w){w="undefined"===typeof w?2:w;Module.MakeDev(g);var n=Module._TRN_PDFDocSave(k,Module.GetUStringFromJSString(g),w);Module.docs[g].sink=
null;REX(n);w&16&&Module.postPagesRenamedEvent(k,g);return Module.docs[g].buffer.buffer};e.SaveDoc=function(k,g,w,n,p,y,D,x,E){return new Promise(function(F,H){var a=[];try{var b=Module.GetDoc(k),c=Module.stackSave();a[a.length]=function(){Module.stackRestore(c)};Module.XFDFMerge(k,g,D);var q=Module.allocate(8,"i8",Module.ALLOC_STACK),f=Module.allocate(Module.intArrayFromString('{"UseNonStandardRotation": true}'),"i8",Module.ALLOC_STACK);Module.setValue(q,f,"i8*");Module.setValue(q+4,0,"i32");Module._TRN_PDFDocRefreshAnnotAppearances(b,
q);if(y){q=function(O){O=new Uint8Array(O);m.FS.writeFile("watermarkFile",O);O=Module.ImageCreateFromFile(b,Module.GetUStringFromJSString("watermarkFile"));m.FS.unlink("watermarkFile");return O};var C=Module.ElementBuilderCreate();a.push(function(){Module.ElementBuilderDestroy(C)});var G=Module.ElementWriterCreate();a.push(function(){Module.ElementWriterDestroy(G)});try{if(!y.hasOwnProperty("default"))throw Error("Watermark dictionary has no 'default' key!");var B=q(y["default"]),I=Module.PDFDocGetPageIterator(b,
1);for(f=-1;Module.IteratorHasNext(I);){var J=Module.IteratorCurrent(I);Module.IteratorNext(I);f++;var L=f.toString();try{var K=void 0;if(y.hasOwnProperty(L)){var N=y[L];if(N)K=q(N);else continue}else K=B;var M=Module.PageGetPageInfo(J),Q=Module.ElementBuilderCreateImage(C,K,0,0,M.width,M.height);Module.ElementWriterBegin(G,J);Module.ElementWriterWritePlacedElement(G,Q);Module.ElementWriterEnd(G)}catch(O){console.warn("Watermark for page "+L+"can not be added due to error: "+O)}}}catch(O){console.warn("Watermarks can not be added due to error: "+
O)}}if(x){var R=Module.SecurityHandlerCreate(E);R&&(Module.SecurityHandlerChangeUserPasswordUString(R,x),Module.PDFDocSetSecurityHandler(b,R))}B=0;if(n){var P=Module.PDFDocGetRoot(b);(B=Module.ObjFindObj(P,"OpenAction"))&&Module.ObjPut(P,"__OpenActionBackup__",B);var S=Module.ObjPutDict(P,"OpenAction");Module.ObjPutName(S,"Type","Action");Module.ObjPutName(S,"S","JavaScript");Module.ObjPutString(S,"JS","this.print()")}var T=Module.SaveHelper(b,k,p);n&&(B?Module.ObjPut(P,"OpenAction",Module.ObjFindObj(P,
"__OpenActionBackup__")):Module.ObjErase(P,"OpenAction"));a.reverse().forEach(function(O){O()});if(w)F({fileData:T});else{var U=T.slice(0);F({fileData:U})}}catch(O){a.reverse().forEach(function(V){V()}),H(O)}})};e.SaveDocFromFixedElements=function(k,g,w,n,p,y){k=Module.PDFDocCreateFromLayoutEls(k);k=Module.CreateDoc({type:"ptr",value:k});return Module.SaveDoc(k,g,!0,!1,w,n,p,y)};e.GetCurrentCanvasData=function(k){var g=Module.currentRenderData;if(!g)return null;k&&REX(Module._TRN_PDFRasterizerUpdateBuffer(g.rast));
var w=Date.now();if(g.bufPtr){k=new Uint8Array(new ArrayBuffer(g.buf_size));for(var n=0,p=0;p<g.out_height;++p)for(var y=g.bufPtr+g.stride*p,D=0;D<g.out_width;++D)k[n++]=Module.HEAPU8[y+2],k[n++]=Module.HEAPU8[y+1],k[n++]=Module.HEAPU8[y],k[n++]=Module.HEAPU8[y+3],y+=4}else k=Module.ReadBufferFile("b",k);Object(h.b)("bufferTiming","Copy took ".concat(Date.now()-w));return{pageBuf:k.buffer,pageWidth:g.out_width,pageHeight:g.out_height}};e.RasterizePage=function(k,g,w,n,p,y,D,x,E){return new Promise(function(F,
H){Module.currentRenderData={};var a=Module.currentRenderData;a.out_width=parseInt(w,10);a.out_height=parseInt(n,10);var b=[];b.push(function(){Module.currentRenderData=null});try{var c=Module.stackSave();b[b.length]=function(){Module.stackRestore(c)};var q=Module.GetPage(k,g),f=Module.PageGetPageWidth(q),C=Module.PageGetPageHeight(q);a.stride=4*a.out_width;a.buf_size=a.out_width*a.out_height*4;Object(h.b)("Memory","Created rasterizer");a.rast=Module.PDFRasterizerCreate();b.push(function(){Object(h.b)("Memory",
"Destroyed rasterizer");Module._TRN_PDFRasterizerDestroy(a.rast)});if(D){var G=Module.EMSCreateUpdatedLayersContext(k,D);0!==G&&(REX(Module._TRN_PDFRasterizerSetOCGContext(a.rast,G)),b.push(function(){Module._TRN_OCGContextDestroy(G)}))}var B=!1;x.hasOwnProperty("renderAnnots")?(x.renderAnnots&&(B=!0),REX(Module._TRN_PDFRasterizerSetDrawAnnotations(a.rast,x.renderAnnots?1:0))):REX(Module._TRN_PDFRasterizerSetDrawAnnotations(a.rast,0));x.hasOwnProperty("highlightFields")&&(x.highlightFields&&(B=!0),
REX(Module._TRN_PDFRasterizerSetHighlightFields(a.rast,x.highlightFields)));x.hasOwnProperty("antiAliasing")&&REX(Module._TRN_PDFRasterizerSetAntiAliasing(a.rast,x.antiAliasing));x.hasOwnProperty("pathHinting")&&REX(Module._TRN_PDFRasterizerSetPathHinting(a.rast,x.pathHinting));if(x.hasOwnProperty("thinLinePixelGridFit")){var I=!0;x.hasOwnProperty("thinLineStrokeAdjust")&&(I=x.thinLineStrokeAdjust);REX(Module._TRN_PDFRasterizerSetThinLineAdjustment(a.rast,x.thinLinePixelGridFit,I))}else x.hasOwnProperty("thinLineStrokeAdjust")&&
REX(Module._TRN_PDFRasterizerSetThinLineAdjustment(a.rast,!1,x.thinLineStrokeAdjust));x.hasOwnProperty("thinLineScaling")&&REX(Module._TRN_PDFRasterizerSetThinLineScaling(a.rast,x.thinLineScaling));if(x.hasOwnProperty("nightModeTuningContrast")||x.hasOwnProperty("nightModeTuningSaturation")||x.hasOwnProperty("nightModeTuningFlipness")){var J=x.hasOwnProperty("nightModeTuningContrast")?x.nightModeTuningContrast:.9,L=x.hasOwnProperty("nightModeTuningSaturation")?x.nightModeTuningSaturation:.8,K=x.hasOwnProperty("nightModeTuningFlipness")?
x.nightModeTuningFlipness:1;REX(Module._TRN_PDFRasterizerSetNightModeTuning(a.rast,J,L,K))}x.hasOwnProperty("imageSmoothing")?(I=!1,x.hasOwnProperty("hqImageResampling")&&(I=x.hqImageResampling),REX(Module._TRN_PDFRasterizerSetImageSmoothing(a.rast,x.imageSmoothing,I))):x.hasOwnProperty("hqImageResampling")&&REX(Module._TRN_PDFRasterizerSetImageSmoothing(a.rast,!0,x.hqImageResampling));x.hasOwnProperty("caching")&&REX(Module._TRN_PDFRasterizerSetCaching(a.rast,x.caching));x.hasOwnProperty("expGamma")&&
REX(Module._TRN_PDFRasterizerSetGamma(a.rast,x.expGamma));x.hasOwnProperty("isPrinting")&&(x.isPrinting&&(B=!0),REX(Module._TRN_PDFRasterizerSetPrintMode(a.rast,x.isPrinting)));x.hasOwnProperty("colorPostProcessMode")&&(x.colorPostProcessMode&&(B=!0),REX(Module._TRN_PDFRasterizerSetColorPostProcessMode(a.rast,x.colorPostProcessMode)));var N=Module.PageGetRotation(q);I=1===y||3===y;N=(1===N||3===N)!==I;var M=Module.allocate(48,"i8",Module.ALLOC_STACK);if(p){p.x1=p[0];p.y1=p[1];p.x2=p[2];p.y2=p[3];
var Q=Module.PageGetDefaultMatrix(q,0),R=Module.Matrix2DInverse(Q);p=Module.Matrix2DMultBBox(R,p);if(p.x2<p.x1){var P=p.x1;p.x1=p.x2;p.x2=P}p.y2<p.y1&&(P=p.y1,p.y1=p.y2,p.y2=P);var S=a.out_width/(N?p.y2-p.y1:p.x2-p.x1);var T=Module.GetDefaultMatrixBox(q,p,y)}else T=Module.PageGetDefaultMatrix(q,y),S=a.out_width/(I?C:f);Module.Matrix2DSet(M,S,0,0,S,0,0);Module.Matrix2DConcat(M,T);var U=Module.allocate(4,"i8",Module.ALLOC_STACK),O=Module.allocate(4,"i8",Module.ALLOC_STACK);B?(a.bufPtr=Module._malloc(a.buf_size),
Module._memset(a.bufPtr,x.pageTransparent?0:255,a.buf_size),b.push(function(){Module._free(a.bufPtr)})):(Module.CreateBufferFile("b",a.buf_size,x.pageTransparent),b.push(function(){Module.RemoveBufferFile("b")}));var V=x.overprintMode;if(10===V){REX(Module._TRN_PDFRasterizerSetOverprint(a.rast,1));var Z=Module.PDFRasterizerRasterizeSeparations(a.rast,q,a.out_width,a.out_height,M,0,0);F({pageBuf:Z,pageWidth:a.out_width,pageHeight:a.out_height})}else{REX(Module._TRN_PDFRasterizerSetOverprint(a.rast,
V));B?REX(Module._TRN_PDFRasterizerGetChunkRenderer(a.rast,q,a.bufPtr,a.out_width,a.out_height,a.stride,4,!0,M,0,0,0,U)):REX(Module._TRN_PDFRasterizerGetChunkRendererPath(a.rast,q,Module.GetUStringFromJSString("b"),a.out_width,a.out_height,!0,M,0,0,0,U));var Y=Module.getValue(U,"i8*");b.push(function(){REX(Module._TRN_ChunkRendererDestroy(Y))})}var aa=(new Date).getTime();x.useProgress&&Module.postProgressiveRenderingStartEvent(E,g);var da=r(function W(){try{if(REX(Module._TRN_ChunkRendererRenderForTimePeriod(Y,
200,O)),Module.getValue(O,"i8"))Module.cleanupState.timeout=r(W);else{var ba=Module.GetCurrentCanvasData(!1);Object(h.b)("worker","Total Page Time ".concat((new Date).getTime()-aa));b.reverse().forEach(function(X){X()});F(ba)}}catch(X){b.reverse().forEach(function(ca){ca()}),H(X)}});Module.cleanupState={cleanupArr:b,timeout:da};b.push(function(){Module.cleanupState=null})}catch(ea){b.reverse().forEach(function(W){W()}),H(ea)}})};e.UpdatePassword=function(k,g){try{var w=Module.stackSave();var n=Module.GetDoc(k);
return Module.PDFDocInitStdSecurityHandler(n,g)?(n in downloadDataMap&&REX(Module._TRN_PDFDocDownloaderInitialize(n,downloadDataMap[n].downloader)),{success:!0,pageDimensions:Module.GetPageDimensions(n)}):{success:!1}}finally{Module.stackRestore(w)}};e.UpdateCustomHeader=function(k,g){Module.customHeadersMap[k]=k in Module.customHeadersMap?d(d({},Module.customHeadersMap[k]),g):g;Module.docs[k].chunkStorage&&(Module.docs[k].chunkStorage.customHeaders=Module.customHeadersMap[k])};e.TriggerFullDownload=
function(k){return new Promise(function(g,w){var n=Module.GetDoc(k);try{n in downloadDataMap&&REX(Module.PDFDocDownloaderTriggerFullDownload(n,downloadDataMap[n].downloader)),g({})}catch(p){w(p)}})};e.InsertBlankPages=function(k,g,w,n){return new Promise(function(p,y){var D=[],x=Module.GetDoc(k);try{var E=Module.stackSave();D[D.length]=function(){Module.stackRestore(E)};for(var F=g.length-1;0<=F;--F){var H=Module.PDFDocGetPageIterator(x,g[F]),a=Module.PDFDocPageCreate(x,w,n);Module.PDFDocPageInsert(x,
H,a)}var b=Module.postPagesUpdatedEvent(x,k);D.forEach(function(c){c()});p(b)}catch(c){D.forEach(function(q){q()}),y(c)}})};e.InsertPages=function(k,g,w,n,p,y){return new Promise(function(D,x){var E=[],F=Module.GetDoc(k);try{var H=Module.stackSave();E[E.length]=function(){Module.stackRestore(H)};if(g instanceof ArrayBuffer){var a=Module.CreateDoc(g);var b=Module.GetDoc(a);E[E.length]=function(){Module.DeleteDoc(a)}}else b=Module.GetDoc(g);for(var c=w.length,q=Module.PageSetCreate(),f=0;f<c;++f)Module.PageSetAddPage(q,
w[f]);y?Module.PDFDocInsertPages2(F,n,b,q,p):Module.PDFDocInsertPages(F,n,b,q,p);var C;y||(C=Module.postPagesUpdatedEvent(F,k));E.reverse().forEach(function(G){G()});D(C)}catch(G){E.reverse().forEach(function(B){B()}),x(G)}})};e.MovePages=function(k,g,w){return new Promise(function(n,p){var y=[],D=Module.GetDoc(k);try{var x=Module.stackSave();y[y.length]=function(){Module.stackRestore(x)};for(var E=g.length,F=Module.PageSetCreate(),H=0;H<E;++H)Module.PageSetAddPage(F,g[H]);Module.PDFDocMovePages(D,
w,F);var a=Module.postPagesUpdatedEvent(D,k);y.forEach(function(b){b()});n(a)}catch(b){y.forEach(function(c){c()}),p(b)}})};e.RemovePages=function(k,g,w){return new Promise(function(n,p){var y=Module.GetDoc(k),D=[];try{var x=Module.stackSave();D[D.length]=function(){Module.stackRestore(x)};for(var E=g.length-1;0<=E;--E){var F=Module.PDFDocGetPageIterator(y,g[E]);Module.IteratorHasNext(F)&&(w?Module.PDFDocPageRemove2(y,F):Module.PDFDocPageRemove(y,F))}var H;w||(H=Module.postPagesUpdatedEvent(y,k));
D.forEach(function(a){a()});n(H)}catch(a){D.forEach(function(b){b()}),p(a)}})};e.RotatePages=function(k,g,w){return new Promise(function(n,p){var y=Module.GetDoc(k),D=[];try{var x=Module.stackSave();D[D.length]=function(){Module.stackRestore(x)};var E=g.length,F=0,H=Module.PDFDocGetPageIterator(y,g[0]),a=[];D.push(function(){Module._TRN_IteratorDestroy(H)});for(var b=g[0];Module.IteratorHasNext(H)&&F<g[E-1];++b){if(b===g[F]){var c=Module.IteratorCurrent(H),q=(Module.PageGetRotation(c)+w)%4;Module.PageSetRotation(c,
q);a.push(b);F++}Module.IteratorNext(H)}var f=Module.postPagesUpdatedEvent(y,k,a,!0);D.reverse().forEach(function(C){C()});n(f)}catch(C){D.reverse().forEach(function(G){G()}),p(C)}})};e.ExtractPages=function(k,g,w,n,p,y){return new Promise(function(D,x){var E=[];try{var F=Module.stackSave();E[E.length]=function(){Module.stackRestore(F)};var H=function(c){E.reverse().forEach(function(q){q()});x(c)};Module.XFDFMerge(k,w,p);var a=Module.CreateEmptyDoc();E[E.length]=function(){Module.DeleteDoc(a)};var b=
Module.InsertPages(a,k,g,1,!0,y).then(function(){return Module.SaveDoc(a,void 0,!0,!1,void 0,n)}).then(function(c){E.reverse().forEach(function(q){q()});return c});D(b)}catch(c){H(c)}})};e.CropPages=function(k,g,w,n,p,y){return new Promise(function(D,x){var E=Module.GetDoc(k),F=[];try{var H=Module.stackSave();F[F.length]=function(){Module.stackRestore(H)};var a=g.length,b=0,c=Module.PDFDocGetPageIterator(E,g[0]);F.push(function(){Module._TRN_IteratorDestroy(c)});for(var q=[],f=g[0];Module.IteratorHasNext(c)&&
b<g[a-1];++f){if(f===g[b]){var C=Module.IteratorCurrent(c),G=Module.allocate(8,"i8",Module.ALLOC_STACK);REX(Module._TRN_PageGetCropBox(C,G));var B=Module.PageGetRotation(C),I=Module.getValue(G,"double"),J=Module.getValue(G+8,"double"),L=Module.getValue(G+16,"double"),K=Module.getValue(G+24,"double");0===B%4?(Module.setValue(G,I+p,"double"),Module.setValue(G+8,J+n,"double"),Module.setValue(G+16,L-y,"double"),Module.setValue(G+24,K-w,"double")):1===B%4?(Module.setValue(G,I+w,"double"),Module.setValue(G+
8,J+p,"double"),Module.setValue(G+16,L-n,"double"),Module.setValue(G+24,K-y,"double")):2===B%4?(Module.setValue(G,I+y,"double"),Module.setValue(G+8,J+w,"double"),Module.setValue(G+16,L-p,"double"),Module.setValue(G+24,K-n,"double")):3===B%4&&(Module.setValue(G,I+n,"double"),Module.setValue(G+8,J+y,"double"),Module.setValue(G+16,L-w,"double"),Module.setValue(G+24,K-p,"double"));Module.setValue(G+32,0,"double");REX(Module._TRN_PageSetBox(C,0,G));REX(Module._TRN_PageSetBox(C,1,G));q.push(f);b++}Module.IteratorNext(c)}var N=
Module.postPagesUpdatedEvent(E,k,q,!0);F.reverse().forEach(function(M){M()});D(N)}catch(M){F.reverse().forEach(function(Q){Q()}),x(M)}})}})("undefined"===typeof self?this.Module:self.Module);this.loaded=!0;Module.initCb&&Module.initCb()}}})(self)}).call(this,v(7).clearImmediate,v(7).setImmediate)},function(u,z,v){(function(t){function r(d){"@babel/helpers - typeof";return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(h){return typeof h}:function(h){return h&&"function"==typeof Symbol&&
h.constructor===Symbol&&h!==Symbol.prototype?"symbol":typeof h},r(d)}var A=v(1),l="undefined"!==typeof window?window:self;l.global=l;(function(d){d.currentFileString="/current";var h=0,m=0,e={},k=null;Module.chunkMax=200;var g=function(a,b,c,q,f){var C=new XMLHttpRequest;return CancellablePromise(function(G,B){C.open("GET",a,!0);C.responseType="arraybuffer";C.onerror=function(){B("Network error occurred")};C.onload=function(){if(206===this.status&&C.response.byteLength===c){var J=new Int8Array(C.response);
G(J)}else B("Download Failed")};var I=["bytes=",b,"-",b+c-1].join("");C.setRequestHeader("Range",I);f&&(C.withCredentials=f);q&&Object.keys(q).forEach(function(J){C.setRequestHeader(J,q[J])});C.send()},function(){C.abort()})},w=function(a){this.maxChunkNum=a;this.lruList=[];this.chunkMap={}};w.prototype={has:function(a){return a in this.chunkMap},insert:function(a,b){this.lruList.length>=this.maxChunkNum&&(delete this.chunkMap[this.lruList[0]],this.lruList.shift());this.lruList.push(b);this.chunkMap[b]=
a},get:function(a){var b=this.lruList.lastIndexOf(a);0<=b&&this.lruList.splice(b,1);this.lruList.push(a);return this.chunkMap[a]}};var n=function(a){this.file=a;this.filePosition=0;this.fileLength=a.size;this.chunkSize=1048576;this.chunkCache=new w(8);this.reader=new FileReaderSync};n.prototype={read:function(a,b,c){c=this.filePosition+c<=this.fileLength?c:this.fileLength-this.filePosition;a=a.subarray(b,b+c);b=c;for(var q=this.filePosition%this.chunkSize,f=this.filePosition-q,C=0;0<c;){if(this.chunkCache.has(f))var G=
this.chunkCache.get(f);else G=new Int8Array(this.reader.readAsArrayBuffer(this.file.slice(f,f+this.chunkSize))),this.chunkCache.insert(G,f);var B=G.length,I=q+c;I<=B?(a.set(G.subarray(q,I),C),this.filePosition+=c,c=0):(a.set(G.subarray(q),C),this.filePosition+=B-q,q=0,f=this.filePosition,c=I-B,C=b-c)}return b},seek:function(a){this.filePosition=a},close:function(){this.reader=this.file=null},getPos:function(){return this.filePosition},getTotalSize:function(){return this.fileLength}};var p=function(a){this.data=
a;this.position=0;this.length=this.data.length};p.prototype={read:function(a,b,c){c=this.position+c<=this.length?c:this.length-this.position;a=a.subarray(b,b+c);b=this.data.subarray(this.position,this.position+c);a.set(b);this.position+=c;return c},write:function(a,b,c){c=this.position+c<=this.length?c:this.length-this.position;a=a.subarray(b,b+c);this.data.subarray(this.position,this.position+c).set(a);this.position+=c;return c},seek:function(a){this.position=a},close:function(){this.data=null},
getPos:function(){return this.position},getTotalSize:function(){return this.length}};var y=function(a,b,c,q,f){"object"===r(a)?(this.lruList=a.lruList,this.chunkMap=a.chunkMap,this.length=a.length,this.url=a.url,this.customHeaders=a.customHeaders,this.withCredentials=a.withCredentials):(this.lruList=[],this.chunkMap={},this.chunkMap[b]=f,this.length=b,this.url=a,this.customHeaders=c,this.withCredentials=q)};y.prototype={lruUpdate:function(a){var b=this.lruList.lastIndexOf(a);0<=b&&this.lruList.splice(b,
1);this.lruList.push(a)},getChunk:function(a){var b=this;if(this.chunkMap[a])this.lruUpdate(a);else{var c=Math.min(a+1048576,this.length)-1,q=new XMLHttpRequest;q.open("GET",this.url,!1);q.responseType="arraybuffer";q.setRequestHeader("Range",["bytes=",a,"-",c].join(""));this.withCredentials&&(q.withCredentials=this.withCredentials);this.customHeaders&&Object.keys(this.customHeaders).forEach(function(f){q.setRequestHeader(f,b.customHeaders[f])});q.send();if(200===q.status||206===q.status)this.writeChunk(new Int8Array(q.response),
a);else throw Error("Failed to load data from");}return this.chunkMap[a]},hadChunk:function(a){return a in this.chunkMap},hasChunk:function(a){return this.chunkMap[a]},getCacheData:function(){return this.chunkMap[this.length]},getRequiredChunkOffsetArrays:function(a,b){var c={have:[],downloading:[],missing:[]};try{var q=Module.stackSave();var f=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_DownloaderGetRequiredChunksSize(a,b,f));var C=Module.getValue(f,"i8*");if(C){var G=Module._malloc(8*
C);REX(Module._TRN_DownloaderGetRequiredChunks(a,b,G,C));for(a=0;a<C;++a){var B=Module.getValue(G+8*a,"double");this.hasChunk(B)?c.have.push(B):this.hadChunk(B)?c.missing.push(B):c.downloading.push(B)}}}finally{G&&Module._free(G),Module.stackRestore(q)}return c},keepVisibleChunks:function(a,b){for(var c=b.length,q=Module.chunkMax/2,f=0,C=0;C<c;++C){var G=this.getRequiredChunkOffsetArrays(a,b[C]),B=G.have,I=B.length;f+=I;if(f>q){this.keepChunks(B.slice(0,I-f+q));break}this.keepChunks(G.have)}},getChunkAsync:function(a){var b=
this,c=a+1048576,q=1048576;c>this.length&&(q-=c-this.length);return g(this.url,a,q,this.customHeaders,this.withCredentials).then(function(f){b.writeChunk(f,a)})},getChunks:function(a){for(var b=a.length,c=Array(b),q=0;q<b;++q)c[q]=this.getChunkAsync(a[q]);return CancellablePromise.all(c)},keepChunks:function(a){for(var b=a.length,c=0;c<b;++c)this.lruUpdate(a[c])},writeChunk:function(a,b,c){c=c||0;var q=this.chunkMap[b],f=a.length,C=this.lruList.length>=Module.chunkMax&&!q;1048576!==f||a.buffer.byteLength!==
f?(C?(q=this.lruList.shift(),C=this.chunkMap[q],1048576>C.length&&(C=new Int8Array(1048576)),this.chunkMap[q]=null):C=q?this.chunkMap[b]:new Int8Array(1048576),C.subarray(c,c+f).set(a),a=C):C&&(q=this.lruList.shift(),this.chunkMap[q]=null);this.lruUpdate(b);this.chunkMap[b]=a}};var D=function(a){this.chunkStorage=a;this.position=0;this.length=this.chunkStorage.length};D.prototype={read:function(a,b,c){var q=this.position+c<=this.length,f=q?c:this.length-this.position;if(this.position<this.length){var C;
for(C=0;C<f;){var G=this.position%1048576;var B=this.position-G;var I=f-C,J=a.subarray(b+C,b+C+I);if(this.chunkStorage.hadChunk(B))B=this.chunkStorage.getChunk(B).subarray(G,G+I),J.set(B),J=B.length,C+=J,this.position+=J;else for(this.position+=I;C<f;++C)J[C]=0}}if(!q){b+=f;if(c-=f)q=this.chunkStorage.getCacheData(),c>q.length&&(c=q.length),C=this.position-this.length,a=a.subarray(b,b+c),B=q.subarray(C,C+c),a.set(B);this.position+=c;return f+c}return f},write:function(a,b,c){var q=this.position+c<=
this.length,f=this.position+c<=this.length?c:this.length-this.position,C=a.subarray(b,b+f),G=this.position%1048576;this.chunkStorage.writeChunk(C,this.position-G,G);this.position+=f;if(!q){C=b+f;if(c-=f)b=this.chunkStorage.getCacheData(),c>b.length&&(c=b.length),q=this.position-this.length,C=a.subarray(C,C+c),b.subarray(q,q+c).set(C);this.position+=c;return f+c}return f},seek:function(a){this.position=a},close:function(){this.chunkStorage=null},getPos:function(){return this.position},getTotalSize:function(){return this.length}};
var x=function(a){this.docId=a;this.length=0;this.data=new Int8Array(8192);this.position=0};x.prototype={seek:function(a){this.position=a},close:function(){var a=new Int8Array(this.data.buffer.slice(0,this.length));Module.ChangeDocBackend(this.docId,{ptr:Module.GetDoc(this.docId),buffer:a});this.data=null},getPos:function(){return this.position},getTotalSize:function(){return this.length},read:function(a,b,c){var q=this.data.length;c=c+b<q?c:q-b;a=a.subarray(b,b+c);b=this.data.subarray(this.position,
this.position+c);a.set(b);this.position+=c;return c},write:function(a,b,c){for(var q=this.position+c,f=this.data.length;q>f;){f=Math.max(f*(16777216<f?1.5:2),q);var C=new Int8Array(f);C.set(this.data.subarray(0,this.length),0);this.data=C}a=a.subarray(b,b+c);this.data.set(a,this.position);this.position+=c;this.position>this.length&&(this.length=this.position);return c}};var E={IsSink:function(a){return 66===(a.flags&255)},open:function(a){var b=a.path.slice(1);this.IsSink(a)?(a.provider=new x(b),
Module.docs[b].sink=a.provider):a.provider=Module.docs[b].sink?new p(Module.docs[b].sink.data):Module.docs[b].chunkStorage?new D(Module.docs[b].chunkStorage):Module.docs[b].buffer?new p(Module.docs[b].buffer):new n(Module.docs[b].file)},close:function(a){a.provider.close()},read:function(a,b,c,q,f){return a.provider.read(b,c,q)},llseek:function(a,b,c){a=a.provider;1===c?b+=a.getPos():2===c&&(b=a.getTotalSize()+b);if(0>b)throw new FS.ErrnoError(l.ERRNO_CODES.EINVAL);a.seek(b);return b},write:function(a,
b,c,q,f){return q?a.provider.write(b,c,q):0}};l.THROW=function(a){throw{type:"PDFWorkerError",message:a};};var F=function(a){return"Exception: \n\t Message: ".concat(d.GetJSStringFromCString(Module._TRN_GetMessage(a)),"\n\t Filename: ").concat(d.GetJSStringFromCString(Module._TRN_GetFileName(a)),"\n\t Function: ").concat(d.GetJSStringFromCString(Module._TRN_GetFunction(a)),"\n\t Linenumber: ").concat(d.GetJSStringFromCString(Module._TRN_GetLineNum(a)))};d.GetErrToString=F;l.REX=function(a){a&&THROW(F(a))};
d.Initialize=function(a){var b=Module.stackSave();a=a?Module.allocate(Module.intArrayFromString(a),"i8",Module.ALLOC_STACK):0;REX(Module._TRN_PDFNetInitialize(a));Module.stackRestore(b)};d.GetDoc=function(a){if(a in Module.docs)return Module.docs[a].ptr;throw{type:"InvalidDocReference",message:"Unable to access Document id=".concat(a,". The document appears to be invalid or was deleted.")};};d.clearDocBackend=function(){null!==Module.cachePtr?(Module.hasBufOwnership&&Module._free(Module.cachePtr),
Module.cachePtr=null):Module.docs[d.currentFileString]&&delete Module.docs[d.currentFileString]};d.MakeDev=function(a){if(!e[a]){var b=FS.makedev(3,5);FS.registerDevice(b,E);FS.mkdev(a,511,b);e[a]=!0}};d.CreateDocFileBackend=function(a,b){Module.MakeDev(b);var c=Module.allocate(4,"i8",Module.ALLOC_STACK);Module.docs[b]={file:a};a=Module.allocate(Module.intArrayFromString(b),"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFDocCreateFromFilePath(a,c));c=Module.getValue(c,"i8*");Module.docs[b].ptr=c};d.InsertImageIntoDoc=
function(a,b,c){var q=[];try{var f=Module.ElementBuilderCreate();q.push(function(){Module.ElementBuilderDestroy(f)});var C=Module.ElementWriterCreate();q.push(function(){Module.ElementWriterDestroy(C)});if(c){var G=c.width;var B=c.height}else G=Module.ImageGetImageWidth(b),B=Module.ImageGetImageHeight(b),c=G/B,c>612/792?(G=612,B=parseInt(G/c,10)):(B=792,G=parseInt(B*c,10));var I=Module.ElementBuilderCreateImage(f,b,0,0,G,B),J=Module.PDFDocPageCreate(a,G,B);Module.ElementWriterBegin(C,J);Module.ElementWriterWritePlacedElement(C,
I);Module.ElementWriterEnd(C);Module.PDFDocPagePushBack(a,J)}finally{q.reverse().forEach(function(L){L()})}};var H=function(a,b,c){"object"===r(a)?(this.m_pages=a.m_pages,this.m_has_named_dests=a.m_has_named_dests,this.m_finished_download=a.m_finished_download,this.m_has_outline=a.m_has_outline,this.m_current_page=a.m_current_page,this.m_id=a.m_id,this.size=a.size,this.timeout=a.timeout,this.eventPageArray=a.eventPageArray,this.requirePageCallbacks=a.requirePageCallbacks):(this.m_pages=[],this.m_has_outline=
this.m_finished_download=this.m_has_named_dests=!1,this.m_current_page=1,this.m_id=c,this.size=a,this.timeout=null,this.eventPageArray=[],this.requirePageCallbacks={});this.downloadUserData=Module.createDownloadUserData(b,c)};H.prototype={getJSUrl:function(){return Module.extractDownloadUserData(this.downloadUserData).url},getDocId:function(){return Module.extractDownloadUserData(this.downloadUserData).docId},destroyUserData:function(){this.m_id in Module.withCredentials&&delete Module.withCredentials[this.m_id];
this.m_id in Module.customHeadersMap&&delete Module.customHeadersMap[this.m_id];Module.destroyDownloadUserData(this.downloadUserData)}};d.createDownloadUserData=function(a,b){a=Module.allocate(Module.intArrayFromString(a),"i8",Module.ALLOC_NORMAL);var c=Module.allocate(8,"i8",Module.ALLOC_NORMAL);Module.setValue(c,a,"i8*");Module.setValue(c+4,parseInt(b,10),"i32");return this.downloadUserData=c};d.extractDownloadUserData=function(a){var b=Module.getValue(a,"i8*");b=d.GetJSStringFromCString(b);a=Module.getValue(a+
4,"i32").toString();return{url:b,docId:a}};d.destroyDownloadUserData=function(a){Module._free(Module.getValue(a,"i8*"));Module._free(a)};l.downloadDataMap={};Module.customHeadersMap={};Module.withCredentials={};d.GetDownloadData=function(a){if(a in downloadDataMap)return downloadDataMap[a]};d.DownloaderHint=function(a,b){var c=Module.GetDoc(a),q=downloadDataMap[c];b.currentPage&&(q.m_current_page=b.currentPage);if(b.visiblePages){var f=b.visiblePages;for(b=0;b<f.length;++b)++f[b];Object.keys(q.requirePageCallbacks).forEach(function(G){q.requirePageCallbacks.hasOwnProperty(G)&&
f.push(parseInt(G,10))});(b=Module.docs[a].chunkStorage)&&b.keepVisibleChunks(q.downloader,f);a=f.length;var C=Module.allocate(4*a,"i8",Module.ALLOC_STACK);for(b=0;b<a;++b)Module.setValue(C+4*b,f[b],"i32");REX(Module._TRN_PDFDocDownloadPages(c,C,a,1,0))}};d.RequirePage=function(a,b){return new Promise(function(c,q){q=Module.GetDoc(a);var f=downloadDataMap[q];!f||f.m_finished_download||f.m_pages[b]?c():(b in f.requirePageCallbacks?f.requirePageCallbacks[b].push(c):f.requirePageCallbacks[b]=[c],c=Module.allocate(4,
"i8",Module.ALLOC_STACK),Module.setValue(c,b,"i32"),Module._TRN_PDFDocDownloadPages(q,c,1,0,0))})};d.IsLinearizationValid=function(a){a=Module.GetDoc(a);if(a=downloadDataMap[a]){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_DownloaderIsLinearizationValid(a.downloader,b));return 0!==Module.getValue(b,"i8")}return!1};d.ShouldRunRender=function(a,b){a=Module.GetDoc(a);return(a=downloadDataMap[a])?a.m_finished_download?!0:a.m_pages[b]:!0};d.postPagesDownloadedEvent=function(a,b,c){a=
{pageDimensions:Module.GetPageDimensionsContentChangedList(a,c),pageNumbers:c};Module.postEvent(b,"pagesDownloaded",a);return a};d.createCallbacksStruct=function(a){if(!k){var b=function(c){return function(q){var f=arguments;q in downloadDataMap?c.apply(this,f):t(function(){q in downloadDataMap&&c.apply(this,f)},0)}};k={downloadProc:Module.addFunction(function(c,q,f,C,G){C=Module.extractDownloadUserData(C);var B=C.docId;g(C.url,q,f,Module.customHeadersMap[B],Module.withCredentials[B]).then(function(I){B in
Module.docs&&Module.docs[B].chunkStorage&&Module.docs[B].chunkStorage.writeChunk(I,q);Module._TRN_DownloadComplete(0,q,f,c)})},"vidiii"),notifyUpdatePage:Module.addFunction(b(function(c,q,f,C){var G=downloadDataMap[c];G.m_pages[q]=!0;var B=G.eventPageArray;if(q in G.requirePageCallbacks)for(f=G.requirePageCallbacks[q],C=0;C<f.length;++C)f[C]();G.timeout?B.push(q):(B=G.eventPageArray=[q],G.timeout=setTimeout(function(){Module.postPagesDownloadedEvent(c,G.m_id,B);G.timeout=null},100))}),"viiii"),notifyUpdateOutline:Module.addFunction(b(function(c,
q){c=downloadDataMap[c];c.m_has_outline||(c.m_has_outline=!0,Module.postEvent(c.m_id,"bookmarksUpdated",{}))}),"vii"),notifyUpdateNamedDests:Module.addFunction(b(function(c,q){c=downloadDataMap[c];c.m_has_named_dests||(c.m_has_named_dests=!0)}),"vii"),notifyDocInfoDownloaded:Module.addFunction(b(function(c,q){downloadDataMap[c].docInfoPromiseCapability.resolve()}),"vii"),notifyUpdateThumb:Module.addFunction(b(function(c,q){}),"viiii"),notifyFinishedDownload:Module.addFunction(b(function(c,q){c=downloadDataMap[c];
c.m_finished_download||(c.m_finished_download=!0,Module.postEvent(c.m_id,"documentComplete",{}))}),"vii"),notifyDocumentError:Module.addFunction(function(c,q){},"viii"),getCurrentPage:Module.addFunction(function(c,q){return downloadDataMap[c].m_current_page},"iii")}}b=Module.allocate(44,"i8",Module.ALLOC_STACK);Module.setValue(b,k.downloadProc,"i8*");Module.setValue(b+4,a,"i8*");Module.setValue(b+8,k.notifyUpdatePage,"i8*");Module.setValue(b+12,k.notifyUpdateOutline,"i8*");Module.setValue(b+16,k.notifyUpdateNamedDests,
"i8*");Module.setValue(b+20,k.notifyDocInfoDownloaded,"i8*");Module.setValue(b+24,k.notifyUpdateThumb,"i8*");Module.setValue(b+28,k.notifyFinishedDownload,"i8*");Module.setValue(b+32,k.notifyDocumentError,"i8*");Module.setValue(b+36,k.getCurrentPage,"i8*");Module.setValue(b+40,0,"i8*");return b};d.CreateDocDownloaderBackend=function(a,b,c){var q=a.url,f=a.size,C=a.customHeaders,G=a.withCredentials,B=a.shouldUseMinimumDownloads;C&&(Module.customHeadersMap[c]=C);G&&(Module.withCredentials[c]=G);var I=
a.downloadData?new H(a.downloadData,q,c):new H(a.size,q,c);var J=Module.createCallbacksStruct(I.downloadUserData),L=Module.allocate(4,"i8",Module.ALLOC_STACK);Module.MakeDev(c);a.chunkStorage?q=new y(a.chunkStorage):(a=new Int8Array(new ArrayBuffer(Math.ceil((a.size+1048576-1)/1048576/8))),q=new y(q,f,C,G,a));Module.docs[c]={chunkStorage:q};REX(Module._TRN_DownloaderCreate(J,f,Module.GetUStringFromJSString(c),B,L));I.downloader=Module.getValue(L,"i8*");I.docInfoPromiseCapability=createPromiseCapability();
I.docInfoRequested=!1;if(f=Module._TRN_PDFDocCreateFromFilter(I.downloader,b))Module._TRN_FilterDestroy(I.downloader),REX(f);b=Module.getValue(b,"i8*");Module.docs[c].ptr=b;Module.PDFDocInitSecurityHandler(b)&&REX(Module._TRN_PDFDocDownloaderInitialize(b,I.downloader));downloadDataMap[b]=I};d.CreateDocBackend=function(a,b){var c=a.value,q=a.extension,f=a.type,C=Module.allocate(4,"i8",Module.ALLOC_STACK),G=Module.stackSave();try{if(c)if("ptr"===f)Module.docs[b]={ptr:c};else{c.shouldUseMinimumDownloads=
a.shouldUseMinimumDownloads;var B="object"===r(c)&&c.url;f=q&&"pdf"!==q;if(B)d.CreateDocDownloaderBackend(c,C,b);else{var I=c instanceof ArrayBuffer;B=I?"buffer":"file";if(I&&(c=new Uint8Array(c),10485760>c.length+h&&!f)){h+=c.length;var J=c.length,L=Module._malloc(c.length);Module.HEAPU8.set(c,L);REX(Module._TRN_PDFDocCreateFromBuffer(L,J,C));var K=Module.getValue(C,"i8*");Module.docs[b]={ptr:K,bufPtr:L,bufPtrSize:J,ownership:!0};Module.docs[b].extension=q;return}Module.MakeDev(b);I={};I[B]=c;Module.docs[b]=
I;if(f){if(a.pageSizes&&a.pageSizes.length)var N=a.pageSizes[0];else a.defaultPageSize&&(N=a.defaultPageSize);var M=Module.GetUStringFromJSString(b);REX(Module._TRN_PDFDocCreate(C));K=Module.getValue(C,"i8*");var Q=Module.ImageCreateFromFile(K,M);Module.InsertImageIntoDoc(K,Q,N)}else{var R=Module.allocate(Module.intArrayFromString(b),"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFDocCreateFromFilePath(R,C));K=Module.getValue(C,"i8*")}Module.docs[b].extension=q;Module.docs[b].ptr=K}}else REX(Module._TRN_PDFDocCreate(C)),
K=Module.getValue(C,"i8*"),Module.docs[b]={ptr:K},Module.docs[b].extension=q}finally{Module.stackRestore(G)}};d.ChangeDocBackend=function(a,b){var c=Module.docs[a];c?(c.bufPtr&&c.ownership&&(Module._free(c.bufPtr),h-=c.bufPtrSize),delete Module.docs[a]):Object(A.d)("Trying to delete document ".concat(a," that does not exist."));Module.docs[a]=b};d.DeleteDoc=function(a){var b=Module.docs[a];b?(b.ptr&&(b.ptr in downloadDataMap&&(clearTimeout(downloadDataMap[b.ptr].timeout),downloadDataMap[b.ptr].destroyUserData(),
delete downloadDataMap[b.ptr]),Module.PDFDocDestroy(b.ptr)),b.bufPtr&&b.ownership&&(Module._free(b.bufPtr),h-=b.bufPtrSize),delete Module.docs[a]):Object(A.d)("Trying to delete document ".concat(a," that does not exist."))};d.CreateDoc=function(a,b){if("id"===a.type){if(Module.docPtrStringToIdMap&&a.value in Module.docPtrStringToIdMap)return Module.docPtrStringToIdMap[a.value];a.type="ptr";a.value=Number("0x".concat(a.value))}if(!b){do b=(++m).toString();while(b in Module.docs)}Module.hasBufOwnership=
!0;d.CreateDocBackend(a,b);return b};d.CreateEmptyDoc=function(){var a=(++m).toString(),b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFDocCreate(b));b=Module.getValue(b,"i8*");Module.docs[a]={ptr:b};return a};d.PDFDocCreateFromLayoutEls=function(a){var b=new Uint8Array(a);a=Module._malloc(b.length);var c=Module.stackSave(),q=Module.allocate(4,"i8",Module.ALLOC_STACK);Module.HEAPU8.set(b,a);b=Module._TRN_PDFDocCreateFromLayoutEls(a,b.length,q);q=Module.getValue(q,"i8*");Module._free(a);
Module.stackRestore(c);REX(b);return q};d.GetPageCount=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFDocGetPageCount(a,b));return Module.getValue(b,"i8*")};d.GetPage=function(a,b){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFDocGetPage(a,b,c));a=Module.getValue(c,"i8*");Module.PageIsValid(a)||THROW("Trying to access page that doesn't exist at index ".concat(b));return a};d.PageGetPageWidth=function(a){var b=Module.allocate(8,"i8",Module.ALLOC_STACK);
REX(Module._TRN_PageGetPageWidth(a,1,b));return Module.getValue(b,"double")};d.PageGetPageHeight=function(a){var b=Module.allocate(8,"i8",Module.ALLOC_STACK);REX(Module._TRN_PageGetPageHeight(a,1,b));return Module.getValue(b,"double")};d.PageGetDefaultMatrix=function(a,b){var c=Module.allocate(48,"i8",Module.ALLOC_STACK);b||(b=0);REX(Module._TRN_PageGetDefaultMatrix(a,!0,1,b,c));return c};d.GetMatrixAsArray=function(a){for(var b=[],c=0;6>c;++c)b[c]=Module.getValue(a+8*c,"double");return b};d.PageGetPageInfo=
function(a){var b=Module.allocate(48,"i8",Module.ALLOC_STACK),c=Module.allocate(8,"i8",Module.ALLOC_STACK),q=Module.allocate(8,"i8",Module.ALLOC_STACK),f=Module.allocate(4,"i8",Module.ALLOC_STACK),C=Module.allocate(4,"i8",Module.ALLOC_STACK),G=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PageGetPageInfo(a,!0,1,0,c,q,b,f,C,G));return{rotation:Module.getValue(f,"i8*"),width:Module.getValue(c,"double"),height:Module.getValue(q,"double"),matrix:Module.GetMatrixAsArray(b),linkAnnotCount:Module.getValue(C,
"i8*"),otherExceptPopupAnnotCount:Module.getValue(G,"i8*")}};d.GetUStringFromJSString=function(a,b){var c=Module.allocate(4,"i8",Module.ALLOC_STACK),q=2*(a.length+1),f=Module.allocate(q,"i8",b?Module.ALLOC_NORMAL:Module.ALLOC_STACK);Module.stringToUTF16(a,f,q);a=Module._TRN_UStringCreateFromString(f,c);b&&Module._free(f);REX(a);return Module.getValue(c,"i8*")};d.GetJSStringFromUString=function(a){var b=Module.allocate(4,"i16*",Module.ALLOC_STACK);REX(Module._TRN_UStringCStr(a,b));return Module.UTF16ToString(Module.getValue(b,
"i16*"))};d.GetJSStringFromCString=function(a){return Module.UTF8ToString(a)};d.PDFNetSetResourceData=function(a){Module.res_ptr=Module._malloc(a.length);Module.HEAPU8.set(a,Module.res_ptr);REX(Module._TRN_PDFNetSetResourceData(Module.res_ptr,a.length));Module.res_ptr_size=a.length};d.PDFDocDestroy=function(a){REX(Module._TRN_PDFDocDestroy(a))};d.VectorGetSize=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_VectorGetSize(a,b));return Module.getValue(b,"i32")};d.VectorGetAt=
function(a,b){var c=Module.allocate(1,"i8*",Module.ALLOC_STACK);REX(Module._TRN_VectorGetAt(a,b,c));return Module.getValue(c,"i8*")};d.VectorDestroy=function(a){REX(Module._TRN_VectorDestroy(a))};d.PDFRasterizerCreate=function(){var a=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFRasterizerCreate(0,a));return Module.getValue(a,"i8*")};d.ExtractSeparationData=function(a){var b=Module.getValue(a,"i8*"),c=Module.getValue(a+4,"i32"),q=Module.getValue(a+8,"i8*"),f=Module.HEAPU8[a+12],C=
Module.HEAPU8[a+13],G=Module.HEAPU8[a+14];a=Module.HEAPU8[a+15];var B=new Uint8Array(c);B.set(Module.HEAPU8.subarray(b,b+c));b=Module.GetJSStringFromUString(q);return{color:[f,C,G,a],data:B.buffer,name:b}};d.ExtractApRefChangeData=function(a){var b=Module.getValue(a,"i32"),c=Module.getValue(a+4,"i32"),q=Module.getValue(a+8,"i32"),f=Module.getValue(a+12,"i32");a=0!==Module.getValue(a+16,"i8");return{oldObjNum:b,discardAppearance:a,newObjNum:c,genNum:q,pageNum:f}};d.PDFRasterizerRasterizeSeparations=
function(a,b,c,q,f,C,G){var B=Module.allocate(8,"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFRasterizerRasterizeSeparations(a,b,c,q,f,C,G,B));a=Module.getValue(B,"i8*");b=Module.VectorGetSize(a);c=Array(b);for(q=0;q<b;++q)f=Module.VectorGetAt(a,q),c[q]=Module.ExtractSeparationData(f);Module.VectorDestroy(a);return c};d.PageGetRotation=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PageGetRotation(a,b));return Module.getValue(b,"i8*")};d.PageGetId=function(a){var b=Module.allocate(4,
"i8",Module.ALLOC_STACK);REX(Module._TRN_PageGetSDFObj(a,b));b=Module.getValue(b,"i8*");a=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ObjGetObjNum(b,a));a=Module.getValue(a,"i32");var c=Module.allocate(2,"i8",Module.ALLOC_STACK);REX(Module._TRN_ObjGetGenNum(b,c));c=Module.getValue(c,"i16");return"".concat(a,"-").concat(c)};d.PageSetRotation=function(a,b){REX(Module._TRN_PageSetRotation(a,b))};d.GetDefaultMatrixBox=function(a,b,c){var q=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PageGetRotation(a,
q));a=(Module.getValue(q,"i32")+c)%4;c=Module.allocate(48,"i8",Module.ALLOC_STACK);switch(a){case 0:return REX(Module._TRN_Matrix2DSet(c,1,0,0,-1,-b.x1,b.y2)),c;case 1:return REX(Module._TRN_Matrix2DSet(c,0,1,1,0,-b.y1,-b.x1)),c;case 2:return REX(Module._TRN_Matrix2DSet(c,-1,0,0,1,b.x2,-b.y1)),c;case 3:return REX(Module._TRN_Matrix2DSet(c,0,-1,-1,0,b.y2,b.x2)),c}throw Error("Yikes, we don't support that rotation type");};d.Matrix2DMultBBox=function(a,b){var c=Module.allocate(8,"i8",Module.ALLOC_STACK),
q=Module.allocate(8,"i8",Module.ALLOC_STACK);Module.setValue(c,b.x1,"double");Module.setValue(q,b.y1,"double");REX(Module._TRN_Matrix2DMult(a,c,q));b.x1=Module.getValue(c,"double");b.y1=Module.getValue(q,"double");Module.setValue(c,b.x2,"double");Module.setValue(q,b.y2,"double");REX(Module._TRN_Matrix2DMult(a,c,q));b.x2=Module.getValue(c,"double");b.y2=Module.getValue(q,"double");return b};d.Matrix2DMult=function(a,b){var c=Module.allocate(8,"i8",Module.ALLOC_STACK),q=Module.allocate(8,"i8",Module.ALLOC_STACK);
Module.setValue(c,b.x,"double");Module.setValue(q,b.y,"double");REX(Module._TRN_Matrix2DMult(a,c,q));b.x=Module.getValue(c,"double");b.y=Module.getValue(q,"double");return b};d.Matrix2DConcat=function(a,b){var c=Module.getValue(b,"double"),q=Module.getValue(b+8,"double"),f=Module.getValue(b+16,"double"),C=Module.getValue(b+24,"double"),G=Module.getValue(b+32,"double");b=Module.getValue(b+40,"double");REX(Module._TRN_Matrix2DConcat(a,c,q,f,C,G,b))};d.Matrix2DSet=function(a,b,c,q,f,C,G){REX(Module._TRN_Matrix2DSet(a,
b,c,q,f,C,G))};d.IteratorHasNext=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_IteratorHasNext(a,b));return 0!==Module.getValue(b,"i8")};d.IteratorCurrent=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_IteratorCurrent(a,b));return Module.getValue(Module.getValue(b,"i8*"),"i8*")};d.IteratorNext=function(a){REX(Module._TRN_IteratorNext(a))};d.PageGetNumAnnots=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PageGetNumAnnots(a,
b));return Module.getValue(b,"i32")};d.PageGetAnnot=function(a,b){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PageGetAnnot(a,b,c));return Module.getValue(c,"i8*")};d.PageAnnotRemove=function(a,b){REX(Module._TRN_PageAnnotRemoveByIndex(a,b))};d.AnnotGetType=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_AnnotGetType(a,b));return Module.getValue(b,"i32")};d.AnnotHasAppearance=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_AnnotGetAppearance(a,
0,0,b));return 0!==Module.getValue(b,"i8")};d.AnnotRefreshAppearance=function(a){REX(Module._TRN_AnnotRefreshAppearance(a))};d.ObjErase=function(a,b){b=Module.allocate(Module.intArrayFromString(b),"i8",Module.ALLOC_STACK);REX(Module._TRN_ObjEraseFromKey(a,b))};d.GetJSDoubleArrFromCore=function(a,b){for(var c=Array(b),q=0;q<b;++q)c[q]=Module.getValue(a,"double"),a+=8;return c};d.GetJSIntArrayFromCore=function(a,b){for(var c=Array(b),q=0;q<b;++q)c[q]=Module.getValue(a,"i32"),a+=4;return c};d.BookmarkIsValid=
function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_BookmarkIsValid(a,b));return 0!==Module.getValue(b,"i8")};d.BookmarkGetNext=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_BookmarkGetNext(a,b));return Module.getValue(b,"i8*")};d.BookmarkGetFirstChild=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_BookmarkGetFirstChild(a,b));return Module.getValue(b,"i8*")};d.BookmarkHasChildren=function(a){var b=Module.allocate(4,
"i8",Module.ALLOC_STACK);REX(Module._TRN_BookmarkHasChildren(a,b));return 0!==Module.getValue(b,"i8")};d.BookmarkGetAction=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_BookmarkGetAction(a,b));return Module.getValue(b,"i8*")};d.BookmarkGetTitle=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_BookmarkGetTitle(a,b));a=Module.getValue(b,"i8*");return Module.GetJSStringFromUString(a)};d.ActionIsValid=function(a){var b=Module.allocate(4,"i8",
Module.ALLOC_STACK);REX(Module._TRN_ActionIsValid(a,b));return 0!==Module.getValue(b,"i8")};d.ActionGetType=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ActionGetType(a,b));return Module.getValue(b,"i32")};d.ActionGetDest=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ActionGetDest(a,b));return Module.getValue(b,"i8*")};d.DestinationIsValid=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_DestinationIsValid(a,
b));return 0!==Module.getValue(b,"i8")};d.DestinationGetPage=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_DestinationGetPage(a,b));return Module.getValue(b,"i8*")};d.PageIsValid=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PageIsValid(a,b));return 0!==Module.getValue(b,"i8")};d.PageGetIndex=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PageGetIndex(a,b));return Module.getValue(b,"i32")};d.ObjGetAsPDFText=
function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ObjGetAsPDFText(a,b));a=Module.getValue(b,"i8*");return Module.GetJSStringFromUString(a)};d.ObjFindObj=function(a,b){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);b=Module.allocate(Module.intArrayFromString(b),"i8",Module.ALLOC_STACK);REX(Module._TRN_ObjFindObj(a,b,c));return Module.getValue(c,"i8*")};d.PDFDocGetFirstBookmark=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFDocGetFirstBookmark(a,
b));return Module.getValue(b,"i8*")};d.DestinationGetExplicitDestObj=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_DestinationGetExplicitDestObj(a,b));return Module.getValue(b,"i8*")};d.DestinationGetFitType=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_DestinationGetFitType(a,b));return Module.getValue(b,"i32")};d.ObjIsNumber=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ObjIsNumber(a,b));return 0!==Module.getValue(b,
"i8")};d.ObjGetNumber=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ObjGetNumber(a,b));return Module.getValue(b,"double")};d.PDFDocGetRoot=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFDocGetRoot(a,b));return Module.getValue(b,"i8*")};d.ObjPutName=function(a,b,c){b=Module.allocate(Module.intArrayFromString(b),"i8",Module.ALLOC_STACK);c=Module.allocate(Module.intArrayFromString(c),"i8",Module.ALLOC_STACK);var q=Module.allocate(4,"i8",
Module.ALLOC_STACK);REX(Module._TRN_ObjPutName(a,b,c,q));return Module.getValue(q,"i8*")};d.ObjPutDict=function(a,b){b=Module.allocate(Module.intArrayFromString(b),"i8",Module.ALLOC_STACK);var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ObjPutDict(a,b,c));return Module.getValue(c,"i8*")};d.ObjPutString=function(a,b,c){b=Module.allocate(Module.intArrayFromString(b),"i8",Module.ALLOC_STACK);c=Module.allocate(Module.intArrayFromString(c),"i8",Module.ALLOC_STACK);var q=Module.allocate(4,
"i8",Module.ALLOC_STACK);REX(Module._TRN_ObjPutString(a,b,c,q));return Module.getValue(q,"i8*")};d.ObjPut=function(a,b,c){b=Module.allocate(Module.intArrayFromString(b),"i8",Module.ALLOC_STACK);var q=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ObjPut(a,b,c,q));return Module.getValue(q,"i8*")};d.ObjGetAt=function(a,b){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ObjGetAt(a,b,c));return Module.getValue(c,"i8*")};d.Matrix2DInverse=function(a){var b=Module.allocate(48,
"i8",Module.ALLOC_STACK);REX(Module._TRN_Matrix2DInverse(a,b));return b};d.PDFDocInitSecurityHandler=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFDocInitSecurityHandler(a,0,b));return 0!==Module.getValue(b,"i8")};d.PDFDocSetSecurityHandler=function(a,b){REX(Module._TRN_PDFDocSetSecurityHandler(a,b))};d.SecurityHandlerCreate=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_SecurityHandlerCreate(a,b));return Module.getValue(b,"i8*")};d.SecurityHandlerChangeUserPasswordUString=
function(a,b){REX(Module._TRN_SecurityHandlerChangeUserPasswordUString(a,Module.GetUStringFromJSString(b)))};d.PDFDocInitStdSecurityHandler=function(a,b){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFDocInitStdSecurityHandlerUString(a,Module.GetUStringFromJSString(b),c));return 0!==Module.getValue(c,"i8")};d.PDFDocDownloaderTriggerFullDownload=function(a,b){REX(Module._TRN_PDFDocDownloaderTriggerFullDownload(a,b))};d.PDFDocInsertPages=function(a,b,c,q,f){REX(Module._TRN_PDFDocInsertPageSet(a,
b,c,q,f?1:0,0))};d.PDFDocInsertPages2=function(a,b,c,q,f){REX(Module._TRN_PDFDocInsertPageSet2(a,b,c,q,f?1:0,0))};d.PDFDocMovePages=function(a,b,c){REX(Module._TRN_PDFDocMovePageSet(a,b,a,c,0,0))};d.PDFDocGetPageIterator=function(a,b){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFDocGetPageIterator(a,b,c));return Module.getValue(c,"i8*")};d.PDFDocPageRemove=function(a,b){REX(Module._TRN_PDFDocPageRemove(a,b))};d.PDFDocPageRemove2=function(a,b){REX(Module._TRN_PDFDocPageRemove2(a,
b))};d.PDFDocPageCreate=function(a,b,c){var q=Module.allocate(40,"i8",Module.ALLOC_STACK);Module.setValue(q,0,"double");Module.setValue(q+8,0,"double");Module.setValue(q+16,b,"double");Module.setValue(q+24,c,"double");Module.setValue(q+32,0,"double");b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFDocPageCreate(a,q,b));return Module.getValue(b,"i8*")};d.PDFDocPageInsert=function(a,b,c){REX(Module._TRN_PDFDocPageInsert(a,b,c))};d.PageSetCreate=function(){var a=Module.allocate(4,"i8",
Module.ALLOC_STACK);REX(Module._TRN_PageSetCreate(a));return Module.getValue(a,"i8*")};d.PageSetCreateRange=function(a,b){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PageSetCreateRange(c,a,b));return Module.getValue(c,"i8*")};d.PageSetAddPage=function(a,b){REX(Module._TRN_PageSetAddPage(a,b))};d.ElementBuilderCreate=function(){var a=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ElementBuilderCreate(a));return Module.getValue(a,"i8*")};d.ElementBuilderDestroy=function(a){REX(Module._TRN_ElementBuilderDestroy(a))};
d.ElementBuilderCreateImage=function(a,b,c,q,f,C){var G=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ElementBuilderCreateImageScaled(a,b,c,q,f,C,G));return Module.getValue(G,"i8*")};d.ElementWriterCreate=function(){var a=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ElementWriterCreate(a));return Module.getValue(a,"i8*")};d.ElementWriterDestroy=function(a){REX(Module._TRN_ElementWriterDestroy(a))};d.ElementWriterBegin=function(a,b){REX(Module._TRN_ElementWriterBeginOnPage(a,
b,1,1,1,0))};d.ElementWriterWritePlacedElement=function(a,b){REX(Module._TRN_ElementWriterWritePlacedElement(a,b))};d.ElementWriterEnd=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ElementWriterEnd(a,b))};d.ImageGetImageWidth=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ImageGetImageWidth(a,b));return Module.getValue(b,"i32")};d.ImageGetImageHeight=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ImageGetImageHeight(a,
b));return Module.getValue(b,"i32")};d.ImageCreateFromMemory2=function(a,b,c){var q=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ImageCreateFromMemory2(a,b,c,0,q));return Module.getValue(q,"i8*")};d.ImageCreateFromFile=function(a,b){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ImageCreateFromFile(a,b,0,c));return Module.getValue(c,"i8*")};d.PDFDocPagePushBack=function(a,b){REX(Module._TRN_PDFDocPagePushBack(a,b))};d.PDFDocHasOC=function(a){var b=Module.allocate(4,
"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFDocHasOC(a,b));return 0!==Module.getValue(b,"i8")};d.PDFDocGetOCGConfig=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFDocGetOCGConfig(a,b));return Module.getValue(b,"i8*")};d.OCGContextCreate=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_OCGContextCreateFromConfig(a,b));return Module.getValue(b,"i8*")};d.UStringDestroy=function(a){REX(Module._TRN_UStringDestroy(a))};d.PDFDocFDFUpdate=function(a,
b,c){if(c){for(var q=Object.keys(c),f=q.length,C=Module._malloc(8*f),G=0;G<f;++G){var B=8*G,I=q[G],J=Module.GetDoc(c[I]);I=Module.GetUStringFromJSString(I);Module.setValue(C+B,J,"i8*");Module.setValue(C+B+4,I,"i8*")}c=Module.allocate(8,"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFDocFDFUpdateAppearanceDocs(a,b,C,f,c));a=Module.getValue(c,"i8*");b=Module.VectorGetSize(a);f=Array(b);for(C=0;C<b;++C)c=Module.VectorGetAt(a,C),f[C]=Module.ExtractApRefChangeData(c);Module.VectorDestroy(a);if(b)return f}else REX(Module._TRN_PDFDocFDFUpdate(a,
b))};d.FDFDocDestroy=function(a){REX(Module._TRN_FDFDocDestroy(a))}})(l.Module)}).call(this,v(7).setImmediate)},function(u,z,v){function t(r){"@babel/helpers - typeof";return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(A){return typeof A}:function(A){return A&&"function"==typeof Symbol&&A.constructor===Symbol&&A!==Symbol.prototype?"symbol":typeof A},t(r)}(function(r){r.SetupPDFNetFunctions=function(A){Module._IB_=[];for(var l=function D(y){if("object"===t(y)&&null!==y)if("undefined"!==
typeof y.byteLength){var x=Module._IB_.length;Module._IB_[x]=new Uint8Array(y);y={handle:x,isArrayBufferRef:!0}}else Object.keys(y).forEach(function(E){y.hasOwnProperty(E)&&(y[E]=D(y[E]))});return y},d=function x(D){"object"===t(D)&&null!==D&&(D.buffer?D=D.buffer.slice(D.byteOffset,D.byteOffset+D.length):D.isArrayBufferRef?D=Module._IB_[D.handle].buffer:Object.keys(D).forEach(function(E){D.hasOwnProperty(E)&&(D[E]=x(D[E]))}));return D},h=Module._TRN_EMSCreateSharedWorkerInstance(),m,e=Module._TRN_EMSWorkerInstanceGetFunctionIterator(h),
k=function(D,x){return new Promise(function(E,F){D=l(D);var H=D.docId;H=H?Module.GetDoc(H):0;(H=Module.EMSCallSharedFunction(h,x,H))?F({type:"PDFWorkerError",message:Module.GetErrToString(H)}):(F=Module.EMSGetLastResponse(h),F=d(F),E(F))})};m=Module._TRN_EMSFunctionIteratorGetNextCommandName(e);)m=Module.GetJSStringFromCString(m),r.queue.onAsync(m,k);Module._TRN_EMSFunctionIteratorDestroy(e);if(Module._TRN_EMSCreatePDFNetWorkerInstance){var g={};e=function(p,y){A.on(p,y);g[p]=!0};Module.docPtrStringToIdMap=
{};var w=function(p){if(p in Module.docPtrStringToIdMap)return Module.docPtrStringToIdMap[p];throw Error("Couldn't find document ".concat(p));};r.queue.onAsync("PDFDoc.RequirePage",function(p){return Module.RequirePage(w(p.docId),p.pageNum)});e("pdfDocCreateFromBuffer",function(p){p=Module.CreateDoc({type:"array",value:p.buf});var y=Module.GetDoc(p).toString(16);Module.docPtrStringToIdMap[y]=p;return y});e("PDFDoc.destroy",function(p){p=w(p.auto_dealloc_obj);Module.DeleteDoc(p)});e("PDFDoc.saveMemoryBuffer",
function(p){var y=w(p.doc);return Module.SaveHelper(Module.GetDoc(y),y,p.flags).slice(0)});e("pdfDocCreate",function(){var p=Module.CreateDoc({type:"new"}),y=Module.GetDoc(p).toString(16);Module.docPtrStringToIdMap[y]=p;return y});e("GetPDFDoc",function(p){p=p.docId;var y=Module.GetDoc(p).toString(16);Module.docPtrStringToIdMap[y]=p;return y});e("ExtractPDFNetLayersContext",function(p){var y=p.layers;p=Module.GetDoc(p.docId);var D=0;y?D=Module.EMSCreateUpdatedLayersContext(p,y):Module.PDFDocHasOC(p)&&
(y=Module.PDFDocGetOCGConfig(p),D=Module.OCGContextCreate(y));return D.toString(16)});var n=Module._TRN_EMSCreatePDFNetWorkerInstance();e=Module._TRN_EMSWorkerInstanceGetFunctionIterator(n);for(k=function(p){return new Promise(function(y,D){p=l(p);var x=Module.EMSCallPDFNetFunction(n,p);x?D(Module.GetErrToString(x)):(D=Module.EMSGetLastResponse(n),D=d(D),y(D))})};m=Module._TRN_EMSFunctionIteratorGetNextCommandName(e);)if(m=Module.GetJSStringFromCString(m),!g[m])A.onAsync(m,k);Module._TRN_EMSFunctionIteratorDestroy(e)}}})(self)},
function(u,z,v){u=v(6);var t=v.n(u),r=v(15),A=v(16),l=v(5),d=v(17),h=v(1),m=v(18);(function(e){var k=null;e.basePath="../";var g=function(){var n=e.pdfWorkerPath||"";e.workerBasePath&&(e.basePath=e.workerBasePath);var p=e.isFull,y=p?"full/":"lean/";e.useOptimizedWorker&&(y+=m.a);var D=e.wasmDisabled,x=e.disableObjectURLBlobs;Object(h.c)();e.overriddenPdfWorkerPath&&(n=e.overriddenPdfWorkerPath,e.basePath="../",!Object(l.a)()||D)&&(n="");e.basePath=e.externalPath?e.externalPath:e.basePath+"external/";
Object(d.a)("".concat(n+y,"PDFNetC"),{"Wasm.wasm":p?1E7:4E6,"Wasm.js.mem":1E5,".js.mem":12E6,".mem":p?2E6:6E5,disableObjectURLBlobs:x},D)};e.EmscriptenPDFManager=function(){};e.EmscriptenPDFManager.prototype={OnInitialized:function(n){Module.loaded?n():(Module.initCb=function(){n()},Object(h.b)("worker","PDFNet is not initialized yet!"))},NewDoc:function(n,p){return new Promise(function(y,D){try{y(Module.loadDoc(n,p))}catch(x){D(x)}})},Initialize:function(n,p,y,D){n&&(Module.TOTAL_MEMORY=n);Module.memoryInitializerPrefixURL=
p;Module.asmjsPrefix=y;e.basePath=D;g()},shouldRunRender:function(n){return Module.ShouldRunRender(n.docId,n.pageIndex+1)}};var w={setup:function(n){function p(f){var C=f.data,G=f.action;var B="GetCanvas"===G||"GetCanvasPartial"===G?E.shouldRunRender(C):!0;if(B){k=f;var I=f.asyncCallCapability;Object(h.b)("Memory","Worker running command: ".concat(G));F.actionMap[G](C,f).then(function(J){"BeginOperation"!==k.action&&(k=null);I.resolve(J)},function(J){k=null;I.reject(J)})}else e.deferredQueue.queue(f),
x()}function y(f){f.asyncCallCapability=createPromiseCapability();k||F.length?F.queue(f):p(f);return f.asyncCallCapability.promise}function D(f){self.shouldResize&&E.Initialize(f.options.workerHeapSize,f.options.pdfResourcePath,f.options.pdfAsmPath,f.options.parentUrl);Module.chunkMax=f.options.chunkMax;if(f.array instanceof Uint8Array){var C=255===f.array[0];n.postMessageTransfers=C;"response"in new XMLHttpRequest?E.OnInitialized(function(){e.SetupPDFNetFunctions(n);b();n.send("test",{supportTypedArray:!0,
supportTransfers:C})}):n.send("test",!1)}else n.send("test",!1)}function x(){r.a.setImmediate(function(){if((!k||"BeginOperation"!==k.action)&&F.length&&!k){var f=F.dequeue();p(f)}})}var E=new e.EmscriptenPDFManager,F,H=!1,a=!1;Module.workerMessageHandler=n;var b=function(){H?a||(n.send("workerLoaded",{}),a=!0):H=!0};E.OnInitialized(b);(function(){e.queue=F=new t.a({strategy:t.a.ArrayStrategy,comparator:function(f,C){return f.priority===C.priority?f.callbackId-C.callbackId:C.priority-f.priority}});
e.deferredQueue=new t.a({strategy:t.a.ArrayStrategy,comparator:function(f,C){return f.priority===C.priority?f.callbackId-C.callbackId:C.priority-f.priority}});F.actionMap={};F.onAsync=function(f,C){n.onAsync(f,y);F.actionMap[f]=C}})();n.on("test",D);n.on("InitWorker",D);var c=function(f){k&&f(k)&&(Module.cancelCurrent(),k=null);F.removeAllMatching(f,function(C){C.asyncCallCapability.reject({type:"Cancelled",message:"Operation was cancelled due to a change affecting the loaded document."})})},q=function(f){c(function(C){return C.data&&
C.data.docId===f})};n.on("UpdatePassword",function(f){return Module.UpdatePassword(f.docId,f.password)});n.on("UpdateCustomHeader",function(f){return Module.UpdateCustomHeader(f.docId,f.customHeader)});n.on("LoadRes",function(f){Module.loadResources(f.array,f.l);return{}});n.on("DownloaderHint",function(f){Module.DownloaderHint(f.docId,f.hint)});n.on("IsLinearized",function(f){return Module.IsLinearizationValid(f.docId)});n.onNextAsync(x);F.onAsync("NewDoc",function(f){return E.NewDoc(f)});F.onAsync("GetCanvas",
function(f){Object(h.b)("workerdetails","Run GetCanvas PageIdx: ".concat(f.pageIndex," Width: ").concat(f.width));Object(h.b)("Memory","loadCanvas with potential memory usage ".concat(f.width*f.height*8));return Module.loadCanvas(f.docId,f.pageIndex,f.width,f.height,f.rotation,null,f.layers,f.renderOptions)});F.onAsync("GetCanvasPartial",function(f){Object(h.b)("Memory","GetCanvasPartial with potential memory usage ".concat(f.width*f.height*8));return Module.loadCanvas(f.docId,f.pageIndex,f.width,
f.height,f.rotation,f.bbox,f.layers,f.renderOptions)});F.onAsync("SaveDoc",function(f){return Module.SaveDoc(f.docId,f.xfdfString,f.finishedWithDocument,f.printDocument,f.flags,f.watermarks,f.apdocs,f.password,f.encryptionAlgorithmType)});F.onAsync("SaveDocFromFixedElements",function(f){return Module.SaveDocFromFixedElements(f.bytes,f.xfdfString,f.flags,f.watermarks,f.password,f.encryptionAlgorithmType)});F.onAsync("MergeXFDF",function(f){return Module.MergeXFDF(f.docId,f.xfdf,f.apdocs)});F.onAsync("InsertPages",
function(f){return Module.InsertPages(f.docId,f.doc,f.pageArray,f.destPos,f.insertBookmarks,f.skipUpdateEvent)});F.onAsync("MovePages",function(f){return Module.MovePages(f.docId,f.pageArray,f.destPos)});F.onAsync("RemovePages",function(f){return Module.RemovePages(f.docId,f.pageArray,f.skipUpdateEvent)});F.onAsync("RotatePages",function(f){return Module.RotatePages(f.docId,f.pageArray,f.rotation)});F.onAsync("ExtractPages",function(f){return Module.ExtractPages(f.docId,f.pageArray,f.xfdfString,f.watermarks,
f.apdocs,f.skipUpdateEvent)});F.onAsync("CropPages",function(f){return Module.CropPages(f.docId,f.pageArray,f.topMargin,f.botMargin,f.leftMargin,f.rightMargin)});F.onAsync("TriggerFullDownload",function(f){return Module.TriggerFullDownload(f.docId)});F.onAsync("InsertBlankPages",function(f){return Module.InsertBlankPages(f.docId,f.pageArray,f.width,f.height)});F.onAsync("BeginOperation",function(){return Promise.resolve()});F.onAsync("RequirePage",function(f,C){return Module.RequirePage(f.docId,f.pageNum)});
n.on("FinishOperation",function(){if(k&&"BeginOperation"===k.action)k=null,x();else throw{message:"Operation has not started."};});n.on("DeleteDocument",function(f){f=f.docId;q(f);Module.DeleteDoc(f)});n.on("GetCanvasProgressive",function(f){if(k&&k.callbackId===f.callbackId){Object(h.b)("worker","Progressive request in progress");var C=Module.GetCurrentCanvasData(!0)}else{if(F.find({priority:0,callbackId:f.callbackId}))throw Object(h.b)("worker","Progressive request Queued"),{type:"Queued",message:"Rendering has not started yet."};
if(e.deferredQueue.find({priority:0,callbackId:f.callbackId}))throw Object(h.b)("worker","Progressive request Deferred"),{type:"Queued",message:"Rendering has not started yet."};}if(!C)throw Object(h.b)("worker","Progressive request invalid (render already complete)"),{type:"Unavailable",message:"Rendering is complete or was cancelled."};return C});n.on("actionCancel",function(f){k&&k.callbackId===f.callbackId?(Object(h.b)("workerdetails","Cancelled Current Operation"),Module.cancelCurrent()&&(k=
null,x())):(Object(h.b)("workerdetails","Cancelled queued operation"),F.remove({priority:0,callbackId:f.callbackId}),e.deferredQueue.remove({priority:0,callbackId:f.callbackId}))})}};e.onmessage=function(n){if("init"===n.data.action){var p=n.data.shouldResize;e.shouldResize=p;e.isFull=n.data.isFull;e.wasmDisabled=!n.data.wasm;e.externalPath=n.data.externalPath;e.useOptimizedWorker=n.data.useOptimizedWorker;e.disableObjectURLBlobs=n.data.disableObjectURLBlobs;if(n=n.data.pdfWorkerPath)e.overriddenPdfWorkerPath=
n;p||g();p=new A.a("worker_processor",self);Object(h.a)(p);w.setup(p)}}})("undefined"===typeof window?self:window)}]);}).call(this || window)
