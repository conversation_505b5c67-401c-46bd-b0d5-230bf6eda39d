/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[19],{617:function(ya){(function(){ya.exports={Na:function(){function ua(h,f){this.scrollLeft=h;this.scrollTop=f}function n(h){if(null===h||"object"!==typeof h||void 0===h.behavior||"auto"===h.behavior||"instant"===h.behavior)return!0;if("object"===typeof h&&"smooth"===h.behavior)return!1;throw new TypeError("behavior member of ScrollOptions "+h.behavior+" is not a valid value for enumeration ScrollBehavior.");}function oa(h,f){if("Y"===
f)return h.clientHeight+e<h.scrollHeight;if("X"===f)return h.clientWidth+e<h.scrollWidth}function ma(h,f){h=ba.getComputedStyle(h,null)["overflow"+f];return"auto"===h||"scroll"===h}function na(h){var f=oa(h,"Y")&&ma(h,"Y");h=oa(h,"X")&&ma(h,"X");return f||h}function ka(h){var f=(b()-h.startTime)/468;var a=.5*(1-Math.cos(Math.PI*(1<f?1:f)));f=h.yG+(h.x-h.yG)*a;a=h.zG+(h.y-h.zG)*a;h.method.call(h.YN,f,a);f===h.x&&a===h.y||ba.requestAnimationFrame(ka.bind(ba,h))}function ia(h,f,a){var r=b();if(h===x.body){var z=
ba;var aa=ba.scrollX||ba.pageXOffset;h=ba.scrollY||ba.pageYOffset;var ea=w.scroll}else z=h,aa=h.scrollLeft,h=h.scrollTop,ea=ua;ka({YN:z,method:ea,startTime:r,yG:aa,zG:h,x:f,y:a})}var ba=window,x=document;if(!("scrollBehavior"in x.documentElement.style&&!0!==ba.xSa)){var y=ba.HTMLElement||ba.Element,w={scroll:ba.scroll||ba.scrollTo,scrollBy:ba.scrollBy,f7:y.prototype.scroll||ua,scrollIntoView:y.prototype.scrollIntoView},b=ba.performance&&ba.performance.now?ba.performance.now.bind(ba.performance):Date.now,
e=RegExp("MSIE |Trident/|Edge/").test(ba.navigator.userAgent)?1:0;ba.scroll=ba.scrollTo=function(h,f){void 0!==h&&(!0===n(h)?w.scroll.call(ba,void 0!==h.left?h.left:"object"!==typeof h?h:ba.scrollX||ba.pageXOffset,void 0!==h.top?h.top:void 0!==f?f:ba.scrollY||ba.pageYOffset):ia.call(ba,x.body,void 0!==h.left?~~h.left:ba.scrollX||ba.pageXOffset,void 0!==h.top?~~h.top:ba.scrollY||ba.pageYOffset))};ba.scrollBy=function(h,f){void 0!==h&&(n(h)?w.scrollBy.call(ba,void 0!==h.left?h.left:"object"!==typeof h?
h:0,void 0!==h.top?h.top:void 0!==f?f:0):ia.call(ba,x.body,~~h.left+(ba.scrollX||ba.pageXOffset),~~h.top+(ba.scrollY||ba.pageYOffset)))};y.prototype.scroll=y.prototype.scrollTo=function(h,f){if(void 0!==h)if(!0===n(h)){if("number"===typeof h&&void 0===f)throw new SyntaxError("Value could not be converted");w.f7.call(this,void 0!==h.left?~~h.left:"object"!==typeof h?~~h:this.scrollLeft,void 0!==h.top?~~h.top:void 0!==f?~~f:this.scrollTop)}else f=h.left,h=h.top,ia.call(this,this,"undefined"===typeof f?
this.scrollLeft:~~f,"undefined"===typeof h?this.scrollTop:~~h)};y.prototype.scrollBy=function(h,f){void 0!==h&&(!0===n(h)?w.f7.call(this,void 0!==h.left?~~h.left+this.scrollLeft:~~h+this.scrollLeft,void 0!==h.top?~~h.top+this.scrollTop:~~f+this.scrollTop):this.scroll({left:~~h.left+this.scrollLeft,top:~~h.top+this.scrollTop,behavior:h.behavior}))};y.prototype.scrollIntoView=function(h){if(!0===n(h))w.scrollIntoView.call(this,void 0===h?!0:h);else{for(h=this;h!==x.body&&!1===na(h);)h=h.parentNode||
h.host;var f=h.getBoundingClientRect(),a=this.getBoundingClientRect();h!==x.body?(ia.call(this,h,h.scrollLeft+a.left-f.left,h.scrollTop+a.top-f.top),"fixed"!==ba.getComputedStyle(h).position&&ba.scrollBy({left:f.left,top:f.top,behavior:"smooth"})):ba.scrollBy({left:a.left,top:a.top,behavior:"smooth"})}}}}}})()}}]);}).call(this || window)
