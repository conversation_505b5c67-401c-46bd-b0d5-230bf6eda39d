/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[13],{611:function(ya,ua,n){function oa(){return!1}function ma(z,aa,ea){if(!(aa in r))return!0;aa=r[aa];for(var ca=0;ca<aa.length;ca++){var ha=z;var fa=aa[ca];var pa=ea;if(fa.name in ha){var la="",ja=!1;ha=ha[fa.name];switch(fa.type){case "s":la="String";ja=Object(ia.isString)(ha);break;case "a":la="Array";ja=Object(ia.isArray)(ha);break;case "n":la="Number";ja=Object(ia.isNumber)(ha)&&Object(ia.isFinite)(ha);break;case "o":la="Object",
ja=Object(ia.isObject)(ha)&&!Object(ia.isArray)(ha)}ja||pa.reject('Expected response field "'.concat(fa.name,'" to have type ').concat(la));fa=ja}else pa.reject('Response missing field "'.concat(fa.name,'"')),fa=!1;if(!fa)return!1}return!0}function na(z){for(var aa=0,ea=["locale","excelMaxAllowedCellCount","applyPageBreaksToSheet","excelDefaultCellBorderWidth","displayChangeTracking"];aa<ea.length;aa++){var ca=ea[aa],ha=ca;ca=ca.charAt(0).toUpperCase()+ca.slice(1);z[ha]&&(Object.defineProperty(z,
ca,Object.getOwnPropertyDescriptor(z,ha)),delete z[ha])}return z}n.r(ua);var ka=n(0),ia=n(1);n.n(ia);var ba=n(3);ya=n(52);var x=n(35),y=n(629),w=n(125),b=n(523),e=n(50),h=n(231),f=function(){function z(){this.request=this.result=null;this.state=0;var aa=this;aa.promise=new Promise(function(ea,ca){aa.resolve=function(){if(0===aa.state||4===aa.state)aa.state=1,aa.result=arguments[0],ea.apply(null,arguments)};aa.reject=function(){if(0===aa.state||4===aa.state)aa.state=2,ca.apply(null,arguments)}})}z.prototype.vz=
function(){return 1===(this.state&1)};z.prototype.PDa=function(){return 2===(this.state&2)};z.prototype.hm=function(){return!this.PDa()&&!this.vz()};z.prototype.dDa=function(){return 4===(this.state&4)};z.prototype.wZ=function(){this.state|=4};return z}(),a=function(){function z(){this.gz={};this.Nc=[]}z.prototype.pop=function(){var aa=this.Nc.pop();this.gz[aa.key]=void 0;return aa};z.prototype.push=function(aa,ea){ea={key:aa,data:ea};this.Nc.push(ea);this.gz[aa]=ea.data};z.prototype.contains=function(aa){return!!this.gz[aa]};
z.prototype.get=function(aa){return this.gz[aa]};z.prototype.set=function(aa,ea){var ca=this;this.gz[aa]=ea;this.Nc.forEach(function(ha,fa){ha.key===aa&&(ca.Nc[fa]=ha)})};z.prototype.remove=function(aa){var ea=this;this.gz[aa]=void 0;this.Nc.forEach(function(ca,ha){ca.key===aa&&ea.Nc.splice(ha,1)})};z.prototype.length=function(){return this.Nc.length};return z}(),r={pages:[{name:"pages",type:"a"}],pdf:[{name:"url",type:"s"}],docmod:[{name:"url",type:"s"},{name:"rID",type:"s"}],health:[],tiles:[{name:"z",
type:"n"},{name:"rID",type:"n"},{name:"tiles",type:"a"},{name:"size",type:"n"}],cAnnots:[{name:"annots",type:"a"}],annots:[{name:"url",type:"s"},{name:"name",type:"s"}],image:[{name:"url",type:"s"},{name:"name",type:"s"},{name:"p",type:"n"}],text:[{name:"url",type:"s"},{name:"name",type:"s"},{name:"p",type:"n"}],ApString2Xod:[{name:"url",type:"s"},{name:"rID",type:"s"}]};n=function(){function z(aa,ea,ca){var ha=this;this.c_=this.K6=!1;this.Jj=this.gP=this.WA=this.vg=this.tC=this.Xs=this.sC=this.hr=
null;this.Cq=new f;this.Eu=new f;this.QJ=!1;this.Kh=this.zg=this.Ag=this.zh=null;this.ii=[];this.KK=[];this.cache={};this.timeStamp=0;this.Dj=[];this.vm=[];this.GT=null;this.w6=!1;this.cfa=this.id=null;this.QW=this.l$=oa;this.$h=0;this.xV=!1;this.Dba=1;this.pm={};this.Dx=0;this.eA=new a;ea.endsWith("/")||(ea+="/");ca=ca||{};this.K6=ca.disableWebsockets||!1;this.c_=ca.singleServerMode||!1;null!=ca.customQueryParameters&&Object(e.b)("wvsQueryParameters",ca.customQueryParameters);ea.endsWith("blackbox/")||
(ea+="blackbox/");this.hr=ca.uploadData||null;this.WA=ca.uriData||null;this.sC=ca.cacheKey||null;if(this.Xs=ca.officeOptions||null)this.Xs=na(this.Xs);this.vg=ca.rasterizerOptions||null;this.tC=ca.cadOptions||null;this.Tg=ea;this.eT=aa;this.rt(!0);this.Zx=(new y.a(ea,null,this.Dk())).cxa(!this.K6,function(fa){ha.dGa(fa)},function(){return null},function(){ha.QJ=!1},function(){ha.vKa()})}z.prototype.Ysa=function(){var aa=this;return new Promise(function(ea,ca){var ha=new XMLHttpRequest,fa="".concat(aa.Tg,
"ck");ha.open("GET",fa);ha.withCredentials=aa.Dk();ha.onreadystatechange=function(){ha.readyState===XMLHttpRequest.DONE&&(200===ha.status?ea():ca())};ha.send()})};z.prototype.wMa=function(aa){this.l$=aa||oa;this.QW=oa};z.prototype.Wqa=function(){this.uea();return this.Zx.Vu()};z.prototype.uea=function(){Object(ka.b)(this,void 0,void 0,function(){return Object(ka.d)(this,function(aa){switch(aa.label){case 0:return this.Eu=new f,this.Cq=new f,this.QJ=!1,this.id=null,this.w6=!1,[4,this.Ysa().catch(function(){})];
case 1:return aa.aa(),[2]}})})};z.prototype.vKa=function(){this.l$();this.uea();this.zh&&(this.zh.hm()?this.Oj(this.zh.request):this.zh.vz()&&this.QW(this.zh.result.url,"pdf")&&(this.zh=null,this.rea()));this.Kh&&this.Kh.hm()&&this.Oj(this.Kh.request);this.Ag&&this.Ag.hm()?this.Oj(this.Ag.request):this.zg&&this.zg.hm()&&this.O9();var aa;for(aa=0;aa<this.Dj.length;aa++)this.Dj[aa]&&(this.Dj[aa].hm()?this.Oj(this.Dj[aa].request):this.Dj[aa].vz()&&this.QW(this.Dj[aa].result.url,"image")&&(this.Dj[aa]=
null,this.PN(Object(ia.uniqueId)(),aa)));for(aa=0;aa<this.vm.length;aa++)this.vm[aa]&&this.vm[aa].hm()&&!this.vm[aa].dDa()&&this.Oj(this.vm[aa].request);for(aa=0;aa<this.ii.length;aa++)this.ii[aa]&&this.ii[aa].hm()&&this.Oj(this.ii[aa].request)};z.prototype.hCa=function(){return this.QJ?Promise.resolve():(this.QJ=!0,this.timeStamp=Date.now(),this.Zx.cM())};z.prototype.gQa=function(){var aa=this,ea,ca,ha,fa,pa;return new Promise(function(la,ja){if(aa.hr)ea=new FormData,ea.append("file",aa.hr.fileHandle,
aa.hr.fileHandle.name),aa.Xs&&ea.append("officeOptions",JSON.stringify(aa.Xs)),aa.vg&&ea.append("rasterizerOptions",JSON.stringify(aa.vg)),aa.tC&&ea.append("cadOptions",aa.tC.getJsonString()),aa.sC&&ea.append("cacheKey",aa.sC),ca=aa.hr.loadCallback,fa="upload",ha=aa.hr.extension;else if(aa.WA)ea={uri:aa.WA.uri,wVa:aa.WA.shareId},ea=Object.keys(ea).map(function(sa){return"".concat(sa,"=").concat(ea[sa]?encodeURIComponent(ea[sa]):"")}).join("&"),pa="application/x-www-form-urlencoded; charset=UTF-8",
ca=aa.WA.loadCallback,fa="url",ha=aa.WA.extension;else{la();return}var ra=new XMLHttpRequest,qa=Object(x.k)(aa.Tg,"AuxUpload");qa=Object(h.a)(qa,{type:fa,ext:ha});ra.open("POST",qa);ra.withCredentials=aa.Dk();pa&&ra.setRequestHeader("Content-Type",pa);ra.addEventListener("load",function(){if(ra.readyState===ra.DONE&&200===ra.status){var sa=JSON.parse(ra.response);aa.eT=sa.uri;ca(sa);la(sa)}});ra.addEventListener("error",function(){ja("".concat(ra.statusText," ").concat(JSON.stringify(ra)))});aa.hr&&
null!=aa.hr.onProgress&&(ra.upload.onprogress=function(sa){aa.hr.onProgress(sa)});ra.send(ea)})};z.prototype.hKa=function(aa){this.password=aa||null;this.Cq.vz()||(this.Cq=new f,this.Oj({t:"pages"}));return this.Cq.promise};z.prototype.SF=function(aa){this.GT=aa||null;this.Cq.vz()||this.Oj({t:"pages"});return this.Cq.promise};z.prototype.EC=function(aa){aa=Object.assign(aa,{uri:encodeURIComponent(this.eT)});this.GT&&(aa.ext=this.GT);this.Jj&&(aa.c=this.Jj);this.password&&(aa.pswd=this.password);this.sC&&
(aa.cacheKey=this.sC);this.Xs&&(aa.officeOptions=this.Xs);this.vg&&(aa.rastOptions=this.vg);this.tC&&(aa.cadOptions=this.tC.mImpl);return aa};z.prototype.cLa=function(){0<this.eA.length()&&10>=this.Dx&&this.dLa(this.eA.pop().data)};z.prototype.gqa=function(aa){0<this.eA.length()&&this.eA.contains(aa)&&this.eA.remove(aa)};z.prototype.Oj=function(aa){aa=this.EC(aa);this.Zx.send(aa)};z.prototype.Yea=function(aa,ea){10<this.Dx?this.eA.push(aa,ea):(this.Dx++,aa=this.EC(ea),this.Zx.send(aa))};z.prototype.dLa=
function(aa){this.Dx++;aa=this.EC(aa);this.Zx.send(aa)};z.prototype.Bp=function(aa){return aa};z.prototype.k$=function(aa){this.c_&&aa?Object(ba.i)("Server failed health check. Single server mode ignoring check."):!this.GTa&&aa&&3>=this.$h?(this.xV=!0,this.Zx.Vu()):3<this.$h&&(this.c_=!0)};z.prototype.dGa=function(aa){var ea=this,ca=aa.data,ha=aa.err,fa=aa.t;switch(fa){case "upload":ha?this.hQa.reject(ha):this.hQa.resolve("Success");break;case "pages":ha?this.Cq.reject(ha):ma(ca,fa,this.Cq)&&this.Cq.resolve(ca);
break;case "config":if(ha)this.Eu.reject(ha);else if(ma(ca,fa,this.Eu)){this.k$(ca.unhealthy);ca.id&&(this.id=ca.id);if(ca.auth){var pa=Object(e.a)("wvsQueryParameters");pa.auth=ca.auth;Object(e.b)("wvsQueryParameters",pa)}ca.serverVersion&&(this.gP=ca.serverVersion,Object(ba.g)("[WebViewer Server] server version: ".concat(this.gP)));ca.serverID?(this.$h=ca.serverID===this.cfa&&this.xV?this.$h+1:0,this.cfa=ca.serverID):this.$h=0;this.xV=!1;this.Eu.resolve(ca)}break;case "health":ha?this.Eu.reject(ha):
ma(ca,fa,this.Eu)&&this.k$(ca.unhealthy);break;case "pdf":ca.url=Object(h.a)("".concat(this.Tg,"../").concat(encodeURI(ca.url)));ha?this.zh.reject(ha):ma(ca,fa,this.zh)&&this.zh.resolve(ca);break;case "ApString2Xod":ca.url=Object(h.a)("".concat(this.Tg,"../data/").concat(encodeURI(ca.url)));ha?this.pm[ca.rID].reject(ha):ma(ca,fa,this.pm[ca.rID])&&this.pm[ca.rID].resolve(ca);break;case "docmod":ca.url=Object(h.a)("".concat(this.Tg,"../").concat(encodeURI(ca.url)));ha?this.pm[ca.rID].reject(ha):ma(ca,
fa,this.zh)&&this.pm[ca.rID].resolve(ca);break;case "xod":if(ha)this.Ag&&this.Ag.hm()&&this.Ag.reject(ha),this.zg&&this.zg.hm()&&this.zg.reject(ha);else if(ca.notFound)ca.noCreate||this.Ag&&this.Ag.hm()&&this.Ag.resolve(ca),this.zg&&this.zg.hm()&&this.zg.resolve(ca);else{ca.url&&(ca.url=Object(h.a)("".concat(this.Tg,"../").concat(encodeURI(ca.url))));if(!this.zg||this.zg.vz())this.zg=new f,this.zg.request={t:"xod",noCreate:!0};this.Ag||(this.Ag=new f,this.Ag.request={t:"xod"});this.zg.resolve(ca);
this.Ag.resolve(ca)}break;case "cAnnots":pa=this.Kh;if(ha)pa.reject(ha);else if(ma(ca,fa,pa)){pa.wZ();var la=[],ja=ca.annots;ca=function(Ba){var Ca=ja[Ba].s,Aa=ja[Ba].e,Ga="".concat(ra.Tg,"../").concat(encodeURI(ja[Ba].xfdf)),Ea="true"===ja[Ba].hasAppearance?Object(h.a)("".concat(Ga,".xodapp")):null,Ma=Object(ia.range)(Ca,Aa);la[Ba]={range:Ma,promise:new Promise(function(Oa,Na){var Qa=new XMLHttpRequest;Qa.open("GET",Object(h.a)(Ga));Qa.responseType="text";Qa.withCredentials=ea.Dk();Qa.addEventListener("load",
function(){Qa.readyState===Qa.DONE&&200===Qa.status&&Oa({Lw:Qa.response,pp:Ea,range:Ma})});Qa.addEventListener("error",function(){Na("".concat(Qa.statusText," ").concat(JSON.stringify(Qa)))});Qa.send()})}};var ra=this;for(ha=0;ha<ja.length;ha++)ca(ha);pa.resolve(la)}break;case "annots":if(ha)this.Kh.reject(ha);else if(ma(ca,fa,this.Kh)){this.Kh.wZ();var qa=new XMLHttpRequest;pa="".concat(this.Tg,"../").concat(encodeURI(ca.url));var sa=ca.hasAppearance?Object(h.a)("".concat(pa,".xodapp")):null;qa.open("GET",
Object(h.a)(pa));qa.responseType="text";qa.withCredentials=this.Dk();qa.addEventListener("load",function(){qa.readyState===qa.DONE&&200===qa.status&&ea.Kh.resolve({Lw:qa.response,pp:sa})});qa.addEventListener("error",function(){ea.Kh.reject("".concat(qa.statusText," ").concat(JSON.stringify(qa)))});qa.send()}break;case "image":this.Dx--;var ta=this.Dj[ca.p];ha?ta.promise.reject(ha):ma(ca,fa,ta)&&(ta.result=ca,ta.result.url=Object(h.a)("".concat(this.Tg,"../").concat(encodeURI(ta.result.url))),ta.resolve(ta.result));
break;case "tiles":this.Dx--;ta=ca.rID;pa=this.ii[ta];this.ii[ta]=null;this.KK.push(ta);if(ha)pa.reject(ha);else if(ma(ca,fa,pa)){for(ha=0;ha<ca.tiles.length;ha++)ca.tiles[ha]=Object(h.a)("".concat(this.Tg,"../").concat(encodeURI(ca.tiles[ha])));pa.resolve(ca)}break;case "text":ta=this.vm[ca.p];if(ha)ta.reject(ha);else if(ma(ca,fa,ta)){ta.wZ();var wa=new XMLHttpRequest;ca=Object(h.a)("".concat(this.Tg,"../").concat(encodeURI(ca.url)));wa.open("GET",ca);wa.withCredentials=this.Dk();wa.addEventListener("load",
function(){wa.readyState===wa.DONE&&200===wa.status&&(ta.result=JSON.parse(wa.response),ta.resolve(ta.result))});wa.addEventListener("error",function(Ba){ta.reject("".concat(wa.statusText," ").concat(JSON.stringify(Ba)))});wa.send()}break;case "progress":"loading"===ca.t&&this.trigger(w.a.Events.DOCUMENT_LOADING_PROGRESS,[ca.bytes,ca.total])}this.cLa();!fa&&aa.echo&&aa&&"apstring2xod"===aa.echo.t&&(aa=aa.echo.reqID)&&(2<=parseInt(this.gP,10)?this.pm[aa].reject("Message unhandled by server"):this.pm[aa].reject())};
z.prototype.Oxa=function(){return Object(ka.b)(this,void 0,void 0,function(){return Object(ka.d)(this,function(aa){switch(aa.label){case 0:return[4,this.hCa()];case 1:return aa.aa(),[2,this.Eu.promise]}})})};z.prototype.rxa=function(aa){for(var ea=this,ca=new XMLHttpRequest,ha=Object(h.a)("".concat(this.Tg,"aul"),{id:this.id}),fa=new FormData,pa={},la=0;la<aa.body.length;la++){var ja=aa.body[la];pa[ja.id]="".concat(ja.FR.w,";").concat(ja.FR.h);fa.append(ja.id,ja.FR.dataString)}aa={t:"apstring2xod",
reqID:this.Dba++,parts:pa};var ra=this.EC(aa);fa.append("msg",JSON.stringify(ra));this.pm[ra.reqID]=new f;ca.open("POST",ha);ca.withCredentials=this.Dk;ha=new Promise(function(qa,sa){ca.onreadystatechange=function(){4===ca.readyState&&(200===ca.status?qa():sa("An error occurred while sending down appearance strings to the server"))}});ca.send(fa);return ha.then(function(){return ea.pm[ra.reqID].promise})};z.prototype.$qa=function(){var aa=this.gP.split("-")[0].split("."),ea=["1","5","9"];if(3!==aa.length)throw Error("Invalid WVS version length.");
if(3!==ea.length)throw Error("Invalid version length.");for(var ca=0;ca<aa.length;++ca){if(ea.length===ca||aa[ca]>ea[ca])return-1;if(aa[ca]!==ea[ca])return 1}return 0};z.prototype.zu=function(){return 0>=this.$qa()};z.prototype.$T=function(){this.Kh||(this.Kh=new f,this.zu()?this.Kh.request={t:"cAnnots"}:this.Kh.request={t:"annots"},this.Oj(this.Kh.request));return this.Kh.promise};z.prototype.PN=function(aa,ea){this.Dj[ea]||(this.Dj[ea]=new f,this.Dj[ea].request={t:"image",p:ea},this.Yea(aa,this.Dj[ea].request));
return this.Dj[ea].promise};z.prototype.iKa=function(aa){this.vm[aa]||(this.vm[aa]=new f,this.vm[aa].request={t:"text",p:aa},this.Oj(this.vm[aa].request));return this.vm[aa].promise};z.prototype.jKa=function(aa,ea,ca,ha,fa){var pa=this.ii.length;this.KK.length&&(pa=this.KK.pop());this.ii[pa]=new f;this.ii[pa].request={t:"tiles",p:ea,z:ca,r:ha,size:fa,rID:pa};this.Yea(aa,this.ii[pa].request);return this.ii[pa].promise};z.prototype.rea=function(){this.zh||(this.zh=new f,this.zh.request={t:"pdf"},this.w6?
this.zh.resolve({url:this.eT}):this.Oj(this.zh.request));return this.zh.promise};z.prototype.O8=function(aa){var ea=this,ca=new XMLHttpRequest,ha=Object(h.a)("".concat(this.Tg,"aul"),{id:this.id}),fa=new FormData,pa={};aa.annots&&(pa.annots="xfdf");aa.watermark&&(pa.watermark="png");aa.redactions&&(pa.redactions="redact");pa={t:"docmod",reqID:this.Dba++,parts:pa};aa.print&&(pa.print=!0);var la=this.EC(pa);fa.append("msg",JSON.stringify(la));return Promise.all([aa.annots,aa.watermark,aa.redactions].map(function(ja){return Promise.resolve(ja)})).then(function(ja){var ra=
ja[0],qa=ja[1];ja=ja[2];ra&&fa.append("annots",ra);qa&&fa.append("watermark",qa);ja&&fa.append("redactions",ja);ea.pm[la.reqID]=new f;ca.open("POST",ha);ca.withCredentials=ea.Dk;ra=new Promise(function(sa,ta){ca.onreadystatechange=function(){4===ca.readyState&&(200===ca.status?sa():ta("An error occurred while sending down annotation data to the server"))}});ca.send(fa);return ra.then(function(){return ea.pm[la.reqID].promise})})};z.prototype.O9=function(){this.zg||(this.zg=new f,this.zg.request={t:"xod",
noCreate:!0},this.Oj(this.zg.request));return this.zg.promise};z.prototype.kKa=function(){this.Ag||(this.Ag=new f,this.Ag.request={t:"xod"},this.Oj(this.Ag.request));return this.Ag.promise};z.prototype.ht=function(){return!0};z.prototype.request=function(){};z.prototype.Hda=function(){};z.prototype.abort=function(){for(var aa=0;aa<this.ii.length;aa++)this.ii[aa]&&(this.ii[aa].resolve(null),this.ii[aa]=null,this.KK.push(aa));this.close()};z.prototype.iO=function(aa){this.Jj=this.Jj||{};this.Jj.headers=
aa};z.prototype.rt=function(aa){this.Jj=this.Jj||{};this.Jj.internal=this.Jj.internal||{};this.Jj.internal.withCredentials=aa};z.prototype.Dk=function(){return this.Jj&&this.Jj.internal?this.Jj.internal.withCredentials:null};z.prototype.getFileData=function(){return Promise.reject()};return z}();Object(ya.a)(n);Object(b.a)(n);Object(b.b)(n);ua["default"]=n},629:function(ya,ua,n){var oa=n(0),ma=n(3),na=n(35),ka=n(50),ia=n(231),ba=n(106),x=function(){function w(b,e,h,f,a,r){void 0===h&&(h=null);void 0===
f&&(f=null);void 0===a&&(a=null);void 0===r&&(r=null);this.AV=!1;this.$h=0;this.OE=8;this.Hea=3E3;this.CO=!1;this.Q4=this.KQa(b);this.url=e?"".concat(this.Q4,"/").concat(e):"".concat(this.Q4,"/ws");this.PS=h;this.hF=f;this.CC=a;this.sea=r}w.prototype.KQa=function(b){var e=b.indexOf("://"),h="ws://";0>e?e=0:(5===e&&(h="wss://"),e+=3);var f=b.lastIndexOf("/");0>f&&(f=b.length);return h+b.slice(e,f)};w.prototype.send=function(b){this.Ft.readyState===WebSocket.CLOSED||this.AV||this.Ft.send(JSON.stringify(b))};
w.prototype.cM=function(){return Object(oa.b)(this,void 0,void 0,function(){var b,e=this;return Object(oa.d)(this,function(){b=Object(ka.a)("wvsQueryParameters");b.bcid=Object(na.l)(8);Object(ka.b)("wvsQueryParameters",b);return[2,new Promise(function(h,f){var a=Object(ia.a)(e.url);e.CO=!1;e.Ft=new WebSocket(a);e.Ft.onopen=function(){e.AV=!1;e.$h=0;e.hF&&e.hF();h()};e.Ft.onerror=function(){e.AV=!0};e.Ft.onclose=function(r){r=r.code;e.CC&&e.CC();1E3!==r&&3E3!==r&&e.vBa(r,h,f)};e.Ft.onmessage=function(r){r&&
r.data&&(r=JSON.parse(r.data),r.hb?e.send({hb:!0}):r.end?close():e.PS(r))}})]})})};w.prototype.vBa=function(b,e,h){Object(oa.b)(this,void 0,void 0,function(){var f=this;return Object(oa.d)(this,function(){if(this.CO)return e(),[2];this.$h<this.OE?setTimeout(function(){f.CO?e():(f.$h++,Object(ma.i)("Failed to connect to server with WebSocket close code ".concat(b,". Reconnecting to WebViewer Server, attempt ").concat(f.$h," of ").concat(f.OE," ...")),f.cM().then(e).catch(h))},this.Hea):h(ba.a);return[2]})})};
w.prototype.Vu=function(){var b;void 0===b&&(b=!1);this.$h=0;this.CO=!0;b?this.Ft.close(3E3):this.Ft.close();return Promise.resolve()};return w}(),y=function(){function w(b,e,h,f,a,r,z){void 0===f&&(f=null);void 0===a&&(a=null);void 0===r&&(r=null);void 0===z&&(z=null);this.$h=this.NN=this.id=0;this.dE=!1;this.request=null;this.OE=8;this.Hea=3E3;b=this.THa(b);this.url=e?"".concat(b,"/").concat(e,"pf"):"".concat(b,"/pf");this.$O=h;this.PS=f;this.hF=a;this.CC=r;this.sea=z}w.prototype.THa=function(b){var e=
b.lastIndexOf("/");0>e&&(e=b.length);return b.slice(0,e)};w.prototype.zsa=function(b){b=b.split("\n");for(b[b.length-1]&&b.pop();0<b.length&&3>b[b.length-1].length;)"]"===b.pop()&&(this.id=0);0<b.length&&3>b[0].length&&b.shift();for(var e=0;e<b.length;++e)b[e].endsWith(",")&&(b[e]=b[e].substr(0,b[e].length-1));return b};w.prototype.Gea=function(){return Object(oa.b)(this,void 0,void 0,function(){var b=this;return Object(oa.d)(this,function(e){switch(e.label){case 0:return this.$h++<this.OE?[4,new Promise(function(h){return setTimeout(function(){b.sea();
b.cM();h()},3E3)})]:[3,2];case 1:e.aa(),e.label=2;case 2:return[2]}})})};w.prototype.YHa=function(b){Object(oa.b)(this,void 0,void 0,function(){var e,h;return Object(oa.d)(this,function(f){switch(f.label){case 0:e=null,h=0,f.label=1;case 1:if(!(h<b.length))return[3,6];e=JSON.parse(b[h]);if(!e)return[3,5];if(!e.end)return[3,2];close();return[3,5];case 2:if(!e.id||Number(e.id)===this.id)return[3,4];Object(ma.i)("Reconnecting, new server detected");this.Vu();return[4,this.Gea()];case 3:return f.aa(),
[3,5];case 4:e.hb&&Number(e.id)===this.id?this.send({hb:!0}):this.dE||this.PS(e),f.label=5;case 5:return++h,[3,1];case 6:return[2]}})})};w.prototype.ZFa=function(b){Object(oa.b)(this,void 0,void 0,function(){var e,h,f;return Object(oa.d)(this,function(a){switch(a.label){case 0:if(!(3<=b.readyState))return[3,2];try{e=b.responseText.length}catch(r){return Object(ma.g)("caught exception"),[2]}if(0<e)try{h=this.zsa(b.responseText),0===this.id&&0<h.length&&(f=JSON.parse(h.shift()),this.id=f.id,this.$h=
0),this.YHa(h)}catch(r){}return this.dE?[3,2]:[4,this.z7()];case 1:a.aa(),a.label=2;case 2:return[2]}})})};w.prototype.z7=function(){return Object(oa.b)(this,void 0,void 0,function(){var b=this;return Object(oa.d)(this,function(){return[2,new Promise(function(e,h){function f(){return Object(oa.b)(b,void 0,void 0,function(){return Object(oa.d)(this,function(r){switch(r.label){case 0:h(),this.Vu(),r.label=1;case 1:return this.dE&&this.$h<this.OE?[4,this.Gea()]:[3,3];case 2:return r.aa(),[3,1];case 3:return[2]}})})}
b.request=new XMLHttpRequest;b.request.withCredentials=b.$O;var a=Object(ia.a)(b.url,0!==b.id?{id:String(b.id),uc:String(b.NN)}:{uc:String(b.NN)});b.NN++;b.request.open("GET",a,!0);b.request.setRequestHeader("Cache-Control","no-cache");b.request.setRequestHeader("X-Requested-With","XMLHttpRequest");b.request.onreadystatechange=function(){b.ZFa(b.request)};b.request.addEventListener("error",f);b.request.addEventListener("timeout",f);b.request.addEventListener("load",function(){b.hF&&b.hF();e()});b.request.send()})]})})};
w.prototype.cM=function(){var b=Object(ka.a)("wvsQueryParameters");b.bcid=Object(na.l)(8);Object(ka.b)("wvsQueryParameters",b);this.NN=this.id=0;this.dE=!1;return this.z7()};w.prototype.send=function(b){var e=this,h=new XMLHttpRequest;h.withCredentials=this.$O;var f=Object(ia.a)(this.url,{id:String(this.id)}),a=new FormData;a.append("data",JSON.stringify(b));h.addEventListener("error",function(){e.Vu()});h.open("POST",f);h.setRequestHeader("X-Requested-With","XMLHttpRequest");h.send(a)};w.prototype.Vu=
function(){this.id=0;this.dE=!0;this.CC&&this.CC();this.request.abort();return Promise.resolve()};return w}();ya=function(){function w(b,e,h){this.L5=b;this.target=e;this.$O=h}w.prototype.cxa=function(b,e,h,f,a){void 0===b&&(b=!0);void 0===e&&(e=null);void 0===h&&(h=null);void 0===f&&(f=null);void 0===a&&(a=null);return b?new x(this.L5,this.target,e,h,f,a):new y(this.L5,this.target,this.$O,e,h,f,a)};return w}();ua.a=ya}}]);}).call(this || window)
