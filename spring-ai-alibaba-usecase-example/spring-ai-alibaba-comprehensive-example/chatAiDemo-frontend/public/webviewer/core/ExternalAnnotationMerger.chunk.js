/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[4],{616:function(ya,ua,n){n.r(ua);var oa=n(0),ma=n(638),na=n(639),ka;(function(ia){ia[ia.EXTERNAL_XFDF_NOT_REQUESTED=0]="EXTERNAL_XFDF_NOT_REQUESTED";ia[ia.EXTERNAL_XFDF_NOT_AVAILABLE=1]="EXTERNAL_XFDF_NOT_AVAILABLE";ia[ia.EXTERNAL_XFDF_AVAILABLE=2]="EXTERNAL_XFDF_AVAILABLE"})(ka||(ka={}));ya=function(){function ia(ba){this.ba=ba;this.state=ka.EXTERNAL_XFDF_NOT_REQUESTED}ia.prototype.PAa=function(){var ba=this;return function(x,
y,w){return Object(oa.b)(ba,void 0,void 0,function(){var b,e,h,f,a,r,z,aa=this,ea;return Object(oa.d)(this,function(ca){switch(ca.label){case 0:if(this.state!==ka.EXTERNAL_XFDF_NOT_REQUESTED)return[3,2];b=this.ba.getDocument().My();return[4,this.rya(b)];case 1:e=ca.aa(),h=this.bsa(e),this.BT=null!==(ea=null===h||void 0===h?void 0:h.parse())&&void 0!==ea?ea:null,this.state=null===this.BT?ka.EXTERNAL_XFDF_NOT_AVAILABLE:ka.EXTERNAL_XFDF_AVAILABLE,ca.label=2;case 2:if(this.state===ka.EXTERNAL_XFDF_NOT_AVAILABLE)return w(x),
[2];f=new DOMParser;a=f.parseFromString(x,"text/xml");y.forEach(function(ha){aa.merge(a,aa.BT,ha-1)});r=new XMLSerializer;z=r.serializeToString(a);w(z);return[2]}})})}};ia.prototype.vZ=function(ba){this.rya=ba};ia.prototype.kg=function(){this.BT=void 0;this.state=ka.EXTERNAL_XFDF_NOT_REQUESTED};ia.prototype.bsa=function(ba){return ba?Array.isArray(ba)?new ma.a(ba):"string"!==typeof ba?null:(new DOMParser).parseFromString(ba,"text/xml").querySelector("xfdf > add")?new ma.a(ba):new na.a(ba):null};ia.prototype.merge=
function(ba,x,y){var w=this;0===y&&(this.eFa(ba,x.vu),this.gFa(ba,x.fT));var b=x.ea[y];b&&(this.hFa(ba,b.nu),this.jFa(ba,b.Uha,x.gD),this.iFa(ba,b.page,y),this.fFa(ba,b.C6));b=this.ba.Bb();if(y===b-1){var e=x.gD;Object.keys(e).forEach(function(h){e[h].CV||w.hba(ba,h,e[h])})}};ia.prototype.eFa=function(ba,x){null!==x&&(ba=this.iC(ba),this.ew(ba,"calculation-order",x))};ia.prototype.gFa=function(ba,x){null!==x&&(ba=this.iC(ba),this.ew(ba,"document-actions",x))};ia.prototype.hFa=function(ba,x){var y=
this,w=this.hC(ba.querySelector("xfdf"),"annots");Object.keys(x).forEach(function(b){y.ew(w,'[name="'.concat(b,'"]'),x[b])})};ia.prototype.jFa=function(ba,x,y){var w=this;if(0!==x.length){var b=this.iC(ba);x.forEach(function(e){var h=e.getAttribute("field"),f=y[h];f&&(w.hba(ba,h,f),w.ew(b,"null",e))})}};ia.prototype.hba=function(ba,x,y){var w=this.iC(ba),b=w.querySelector('ffield[name="'.concat(x,'"]'));null!==y.EK&&null===b&&this.ew(w,'ffield[name="'.concat(x,'"]'),y.EK);ba=this.hC(ba.querySelector("xfdf"),
"xfdf > fields","fields");x=x.split(".");this.qY(ba,x,0,y.value);y.CV=!0};ia.prototype.iFa=function(ba,x,y){null!==x&&(ba=this.iC(ba),ba=this.hC(ba,"pages"),this.ew(ba,'[number="'.concat(y+1,'"]'),x))};ia.prototype.fFa=function(ba,x){Object.keys(x).forEach(function(y){(y=ba.querySelector('annots [name="'.concat(y,'"]')))&&y.parentElement.removeChild(y)})};ia.prototype.qY=function(ba,x,y,w){if(y===x.length)x=document.createElementNS("","value"),x.textContent=w,this.ew(ba,"value",x);else{var b=x[y];
this.hC(ba,'[name="'.concat(b,'"]'),"field").setAttribute("name",b);ba=ba.querySelectorAll('[name="'.concat(b,'"]'));1===ba.length?this.qY(ba[0],x,y+1,w):(b=this.Cwa(ba),this.qY(y===x.length-1?b:this.bPa(ba,b),x,y+1,w))}};ia.prototype.Cwa=function(ba){for(var x=null,y=0;y<ba.length;y++){var w=ba[y];if(0===w.childElementCount||1===w.childElementCount&&"value"===w.children[0].tagName){x=w;break}}return x};ia.prototype.bPa=function(ba,x){for(var y=0;y<ba.length;y++)if(ba[y]!==x)return ba[y];return null};
ia.prototype.ew=function(ba,x,y){x=ba.querySelector(x);null!==x&&ba.removeChild(x);ba.appendChild(y)};ia.prototype.iC=function(ba){var x=ba.querySelector("pdf-info");if(null!==x)return x;x=this.hC(ba.querySelector("xfdf"),"pdf-info");x.setAttribute("xmlns","http://www.pdftron.com/pdfinfo");x.setAttribute("version","2");x.setAttribute("import-version","4");return x};ia.prototype.hC=function(ba,x,y){var w=ba.querySelector(x);if(null!==w)return w;w=document.createElementNS("",y||x);ba.appendChild(w);
return w};return ia}();ua["default"]=ya},627:function(ya,ua){ya=function(){function n(){}n.prototype.RI=function(oa){var ma={vu:null,fT:null,gD:{},ea:{}};oa=(new DOMParser).parseFromString(oa,"text/xml");ma.vu=oa.querySelector("pdf-info calculation-order");ma.fT=oa.querySelector("pdf-info document-actions");ma.gD=this.LGa(oa);ma.ea=this.ZGa(oa);return ma};n.prototype.LGa=function(oa){var ma=oa.querySelector("fields");oa=oa.querySelectorAll("pdf-info > ffield");if(null===ma&&null===oa)return{};var na=
{};this.ooa(na,ma);this.moa(na,oa);return na};n.prototype.ooa=function(oa,ma){if(null!==ma&&ma.children){for(var na=[],ka=0;ka<ma.children.length;ka++){var ia=ma.children[ka];na.push({name:ia.getAttribute("name"),element:ia})}for(;0!==na.length;)for(ma=na.shift(),ka=0;ka<ma.element.children.length;ka++)ia=ma.element.children[ka],"value"===ia.tagName?oa[ma.name]={value:ia.textContent,EK:null,CV:!1}:ia.children&&na.push({name:"".concat(ma.name,".").concat(ia.getAttribute("name")),element:ia})}};n.prototype.moa=
function(oa,ma){ma.forEach(function(na){var ka=na.getAttribute("name");oa[ka]?oa[ka].EK=na:oa[ka]={value:null,EK:na,CV:!1}})};n.prototype.ZGa=function(oa){var ma=this,na={};oa.querySelectorAll("pdf-info widget").forEach(function(ka){var ia=parseInt(ka.getAttribute("page"),10)-1;ma.bM(na,ia);na[ia].Uha.push(ka)});oa.querySelectorAll("pdf-info page").forEach(function(ka){var ia=parseInt(ka.getAttribute("number"),10)-1;ma.bM(na,ia);na[ia].page=ka});this.M8(oa).forEach(function(ka){var ia=parseInt(ka.getAttribute("page"),
10),ba=ka.getAttribute("name");ma.bM(na,ia);na[ia].nu[ba]=ka});this.u8(oa).forEach(function(ka){var ia=parseInt(ka.getAttribute("page"),10);ka=ka.textContent;ma.bM(na,ia);na[ia].C6[ka]=!0});return na};n.prototype.bM=function(oa,ma){oa[ma]||(oa[ma]={nu:{},C6:{},Uha:[],page:null})};return n}();ua.a=ya},638:function(ya,ua,n){var oa=n(0),ma=n(1);n.n(ma);ya=function(na){function ka(ia){var ba=na.call(this)||this;ba.lwa=Array.isArray(ia)?ia:[ia];return ba}Object(oa.c)(ka,na);ka.prototype.parse=function(){var ia=
this,ba={vu:null,fT:null,gD:{},ea:{}};this.lwa.forEach(function(x){ba=Object(ma.merge)(ba,ia.RI(x))});return ba};ka.prototype.M8=function(ia){var ba=[];ia.querySelectorAll("add > *").forEach(function(x){ba.push(x)});ia.querySelectorAll("modify > *").forEach(function(x){ba.push(x)});return ba};ka.prototype.u8=function(ia){return ia.querySelectorAll("delete > *")};return ka}(n(627).a);ua.a=ya},639:function(ya,ua,n){var oa=n(0);ya=function(ma){function na(ka){var ia=ma.call(this)||this;ia.mwa=ka;return ia}
Object(oa.c)(na,ma);na.prototype.parse=function(){return this.RI(this.mwa)};na.prototype.M8=function(ka){return ka.querySelectorAll("annots > *")};na.prototype.u8=function(){return[]};return na}(n(627).a);ua.a=ya}}]);}).call(this || window)
