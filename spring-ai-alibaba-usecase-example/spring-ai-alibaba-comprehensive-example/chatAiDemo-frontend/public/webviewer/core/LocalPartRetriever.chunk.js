/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[7],{602:function(ya,ua,n){n.r(ua);var oa=n(0),ma=n(3),na=n(206);ya=n(125);var ka=n(345);n=n(523);var ia=window;ya=function(ba){function x(y,w,b){w=ba.call(this,y,w,b)||this;if(y.name&&"xod"!==y.name.toLowerCase().split(".").pop())throw Error("Not an XOD file");if(!ia.FileReader||!ia.File||!ia.Blob)throw Error("File API is not supported in this browser");w.file=y;w.IJ=[];w.JS=0;return w}Object(oa.c)(x,ba);x.prototype.SW=function(y,
w,b){var e=this,h=new FileReader;h.onloadend=function(f){if(0<e.IJ.length){var a=e.IJ.shift();a.DIa.readAsBinaryString(a.file)}else e.JS--;if(h.error){f=h.error;if(f.code===f.ABORT_ERR){Object(ma.i)("Request for chunk ".concat(w.start,"-").concat(w.stop," was aborted"));return}return b(f)}if(f=h.content||f.target.result)return b(!1,f);Object(ma.i)("No data was returned from FileReader.")};w&&(y=(y.slice||y.webkitSlice||y.mozSlice||y.VTa).call(y,w.start,w.stop));0===e.IJ.length&&50>e.JS?(h.readAsBinaryString(y),
e.JS++):e.IJ.push({DIa:h,file:y});return function(){h.abort()}};x.prototype.Cz=function(y){var w=this;w.EJ=!0;var b=na.a;w.SW(w.file,{start:-b,stop:w.file.size},function(e,h){if(e)return Object(ma.i)("Error loading end header: %s ".concat(e)),y(e);if(h.length!==b)throw Error("Zip end header data is wrong size!");w.We=new ka.a(h);var f=w.We.v8();w.SW(w.file,f,function(a,r){if(a)return Object(ma.i)("Error loading central directory: %s ".concat(a)),y(a);if(r.length!==f.stop-f.start)throw Error("Zip central directory data is wrong size!");
w.We.xda(r);w.lS=!0;w.EJ=!1;return y(!1)})})};x.prototype.tY=function(y,w){var b=this,e=b.Kj[y];if(b.We.O5(y)){var h=b.We.BD(y),f=b.SW(b.file,h,function(a,r){delete b.Kj[y];if(a)return Object(ma.i)('Error loading part "%s": %s, '.concat(y,", ").concat(a)),w(a);if(r.length!==h.stop-h.start)throw Error("Part data is wrong size!");w(!1,y,r,b.We.N$(y))});e.Lga=!0;e.cancel=f}else w(Error('File not found: "'.concat(y,'"')),y)};return x}(ya.a);Object(n.a)(ya);Object(n.b)(ya);ua["default"]=ya}}]);}).call(this || window)
