/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[0],{600:function(ya,ua,n){n.r(ua);n.d(ua,"ByteRangeRequest",function(){return r});var oa=n(0),ma=n(1);n.n(ma);var na=n(3),ka=n(206);ya=n(125);var ia=n(345),ba=n(111),x=n(104),y=n(344),w=n(231);n=n(523);var b=[],e=[],h=window,f=function(){return function(){this.Lq=1}}(),a;(function(aa){aa[aa.UNSENT=0]="UNSENT";aa[aa.DONE=4]="DONE"})(a||(a={}));var r=function(){function aa(ea,ca,ha,fa){var pa=this;this.url=ea;this.range=ca;this.Tf=
ha;this.withCredentials=fa;this.wla=a;this.request=new XMLHttpRequest;this.request.open("GET",this.url,!0);h.Uint8Array&&(this.request.responseType="arraybuffer");fa&&(this.request.withCredentials=fa);z.DISABLE_RANGE_HEADER||(Object(ma.isUndefined)(ca.stop)?this.request.setRequestHeader("Range","bytes=".concat(ca.start)):this.request.setRequestHeader("Range",["bytes=",ca.start,"-",ca.stop-1].join("")));ha&&Object.keys(ha).forEach(function(la){pa.request.setRequestHeader(la,ha[la])});this.request.overrideMimeType?
this.request.overrideMimeType("text/plain; charset=x-user-defined"):this.request.setRequestHeader("Accept-Charset","x-user-defined");this.status=y.a.NOT_STARTED}aa.prototype.start=function(ea){var ca=this,ha=this.request;ha.onreadystatechange=function(){if(ca.aborted)return ca.status=y.a.ABORTED,ea({code:y.a.ABORTED});if(this.readyState===ca.wla.DONE){ca.qL();var fa=0===window.document.URL.indexOf("file:///");200===ha.status||206===ha.status||fa&&0===ha.status?(fa=h.n9(this),ca.k_(fa,ea)):(ca.status=
y.a.ERROR,ea({code:ca.status,status:ca.status}))}};this.request.send(null);this.status=y.a.STARTED};aa.prototype.k_=function(ea,ca){this.status=y.a.SUCCESS;if(ca)return ca(!1,ea)};aa.prototype.abort=function(){this.qL();this.aborted=!0;this.request.abort()};aa.prototype.qL=function(){var ea=Object(w.c)(this.url,this.range,e);-1!==ea&&e.splice(ea,1);if(0<b.length){ea=b.shift();var ca=new aa(ea.url,ea.range,this.Tf,this.withCredentials);ea.request=ca;e.push(ea);ca.start(Object(w.d)(ea))}};aa.prototype.extend=
function(ea){var ca=Object.assign({},this,ea.prototype);ca.constructor=ea;return ca};return aa}(),z=function(aa){function ea(ca,ha,fa,pa,la){fa=aa.call(this,ca,fa,pa)||this;fa.Am={};fa.vJ=ha;fa.url=ca;fa.DISABLE_RANGE_HEADER=!1;fa.GF=r;fa.G0=3;fa.Tf=la||{};return fa}Object(oa.c)(ea,aa);ea.prototype.KC=function(ca,ha,fa){var pa=-1===ca.indexOf("?")?"?":"&";switch(fa){case !1:case x.a.NEVER_CACHE:ca="".concat(ca+pa,"_=").concat(Object(ma.uniqueId)());break;case !0:case x.a.CACHE:ca="".concat(ca+pa,
"_=").concat(ha.start,",").concat(Object(ma.isUndefined)(ha.stop)?"":ha.stop)}return ca};ea.prototype.f6=function(ca,ha,fa,pa){void 0===fa&&(fa={});return new this.GF(ca,ha,fa,pa)};ea.prototype.$va=function(ca,ha,fa,pa,la){for(var ja=0;ja<b.length;ja++)if(Object(ma.isEqual)(b[ja].range,ha)&&Object(ma.isEqual)(b[ja].url,ca))return b[ja].lj.push(pa),b[ja].SM++,null;for(ja=0;ja<e.length;ja++)if(Object(ma.isEqual)(e[ja].range,ha)&&Object(ma.isEqual)(e[ja].url,ca))return e[ja].lj.push(pa),e[ja].SM++,null;
fa={url:ca,range:ha,vJ:fa,lj:[pa],SM:1};if(0===b.length&&e.length<this.G0)return e.push(fa),fa.request=this.f6(ca,ha,la,this.withCredentials),fa;b.push(fa);return null};ea.prototype.Ts=function(ca,ha,fa){var pa=this.KC(ca,ha,this.vJ);(ca=this.$va(pa,ha,this.vJ,fa,this.Tf))&&ca.request.start(Object(w.d)(ca));return function(){var la=Object(w.c)(pa,ha,e);if(-1!==la){var ja=--e[la].SM;0===ja&&e[la].request&&e[la].request.abort()}else la=Object(w.c)(pa,ha,b),-1!==la&&(ja=--b[la].SM,0===ja&&b.splice(la,
1))}};ea.prototype.y8=function(){return{start:-ka.a}};ea.prototype.bBa=function(){var ca=-(ka.a+ka.e);return{start:ca-ka.d,end:ca}};ea.prototype.Cz=function(ca){var ha=this;this.EJ=!0;var fa=ka.a;this.Ts(this.url,this.y8(),function(pa,la,ja){function ra(){var qa=ha.We.v8();ha.Ts(ha.url,qa,function(sa,ta){if(sa)return Object(na.i)("Error loading central directory: ".concat(sa)),ca(sa);ta=Object(ba.a)(ta);if(ta.length!==qa.stop-qa.start)return ca("Invalid XOD file: Zip central directory data is wrong size! Should be ".concat(qa.stop-
qa.start," but is ").concat(ta.length));ha.We.xda(ta);ha.lS=!0;ha.EJ=!1;return ca(!1)})}if(pa)return Object(na.i)("Error loading end header: ".concat(pa)),ca(pa,la,ja);la=Object(ba.a)(la);if(la.length!==fa)return ca("Invalid XOD file: Zip end header data is wrong size!");try{ha.We=new ia.a(la)}catch(qa){return ca(qa)}ha.We.mEa?ha.Ts(ha.url,ha.bBa(),function(qa,sa){if(qa)return Object(na.i)("Error loading zip64 header: ".concat(qa)),ca(qa);sa=Object(ba.a)(sa);ha.We.PEa(sa);ra()}):ra()})};ea.prototype.g9=
function(ca){ca(Object.keys(this.We.ps))};ea.prototype.tY=function(ca,ha){var fa=this;if(this.We.O5(ca)){var pa=this.We.AD(ca);if(pa in this.Am){var la=this.Kj[ca];la.Sx=this.Am[pa];la.Sx.Lq++;la.cancel=la.Sx.cancel}else{var ja=this.We.wya(ca),ra=this.Ts(this.url,ja,function(sa,ta){sa?(Object(na.i)('Error loading part "'.concat(ca,'": ').concat(sa)),fa.Ts(fa.url,ja,function(wa,Ba){if(wa)return ha(wa,ca);fa.Bda(Ba,ja,pa,ca,ha)})):fa.Bda(ta,ja,pa,ca,ha)}),qa=this.Kj[ca];qa&&(qa.Lga=!0,qa.cancel=function(){qa.Sx.Lq--;
0===qa.Sx.Lq&&(ra(),delete fa.Am[pa])},this.Am[pa]=new f(pa),qa.Sx=this.Am[pa],qa.Sx.cancel=qa.cancel)}}else delete this.Kj[ca],ha(Error('File not found: "'.concat(ca,'"')),ca)};ea.prototype.Bda=function(ca,ha,fa,pa,la){if(ca.length!==ha.stop-ha.start)la(Error("Part data is wrong size!"),pa);else{do{if(!this.Am[fa])return;pa=this.Am[fa].Lq;for(var ja=ha.fw.length,ra=0;ra<ja;++ra){var qa=ha.fw[ra];la(!1,qa.aw,ca["string"===typeof ca?"substring":"subarray"](qa.start,qa.stop),this.We.N$(qa.aw));qa.aw in
this.Kj&&delete this.Kj[qa.aw]}}while(pa!==this.Am[fa].Lq);delete this.Am[fa]}};ea.DISABLE_RANGE_HEADER=!1;ea.G0=3;return ea}(ya.a);(function(aa){function ea(ca,ha,fa){var pa=aa.call(this)||this,la;for(la in ca)pa[la]=ca[la];pa.XUa=ca;pa.startOffset=ha;pa.endOffset=fa;pa.f6=function(ja,ra,qa,sa){Object(ma.isUndefined)(ra.stop)?(ra.start+=pa.endOffset,ra.stop=pa.endOffset):(ra.start+=pa.startOffset,ra.stop+=pa.startOffset);ja=pa.KC(pa.url,ra,pa.vJ);return new ca.GF(ja,ra,qa,sa)};return pa}Object(oa.c)(ea,
aa);return ea})(z);Object(n.a)(z);Object(n.b)(z);ua["default"]=z}}]);}).call(this || window)
