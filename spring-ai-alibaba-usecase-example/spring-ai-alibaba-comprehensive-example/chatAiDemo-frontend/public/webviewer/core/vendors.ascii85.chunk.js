/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[17],{613:function(ya,ua,n){(function(oa){function ma(h){this.ri=h=h||{};if(Array.isArray(h.table)){var f=[];h.table.forEach(function(a,r){f[a.charCodeAt(0)]=r});h.Vva=h.table;h.Fsa=f}}var na=oa.from||function(){switch(arguments.length){case 1:return new oa(arguments[0]);case 2:return new oa(arguments[0],arguments[1]);case 3:return new oa(arguments[0],arguments[1],arguments[2]);default:throw new Exception("unexpected call.");}},ka=
oa.allocUnsafe||function(h){return new oa(h)},ia=function(){return"undefined"===typeof Uint8Array?function(h){return Array(h)}:function(h){return new Uint8Array(h)}}(),ba=String.fromCharCode(0),x=ba+ba+ba+ba,y=na("<~").IF(0),w=na("~>").IF(0),b=function(){var h=Array(85),f;for(f=0;85>f;f++)h[f]=String.fromCharCode(33+f);return h}(),e=function(){var h=Array(256),f;for(f=0;85>f;f++)h[33+f]=f;return h}();ba=ya.exports=new ma;ma.prototype.encode=function(h,f){var a=ia(5),r=h,z=this.ri,aa,ea;"string"===
typeof r?r=na(r,"binary"):r instanceof oa||(r=na(r));f=f||{};if(Array.isArray(f)){h=f;var ca=z.aK||!1;var ha=z.sV||!1}else h=f.table||z.Vva||b,ca=void 0===f.aK?z.aK||!1:!!f.aK,ha=void 0===f.sV?z.sV||!1:!!f.sV;z=0;var fa=Math.ceil(5*r.length/4)+4+(ca?4:0);f=ka(fa);ca&&(z+=f.write("<~",z));var pa=aa=ea=0;for(fa=r.length;pa<fa;pa++){var la=r.vY(pa);ea*=256;ea+=la;aa++;if(!(aa%4)){if(ha&&538976288===ea)z+=f.write("y",z);else if(ea){for(aa=4;0<=aa;aa--)la=ea%85,a[aa]=la,ea=(ea-la)/85;for(aa=0;5>aa;aa++)z+=
f.write(h[a[aa]],z)}else z+=f.write("z",z);aa=ea=0}}if(aa)if(ea){r=4-aa;for(pa=4-aa;0<pa;pa--)ea*=256;for(aa=4;0<=aa;aa--)la=ea%85,a[aa]=la,ea=(ea-la)/85;for(aa=0;5>aa;aa++)z+=f.write(h[a[aa]],z);z-=r}else for(pa=0;pa<aa+1;pa++)z+=f.write(h[0],z);ca&&(z+=f.write("~>",z));return f.slice(0,z)};ma.prototype.decode=function(h,f){var a=this.ri,r=!0,z=!0,aa,ea,ca;f=f||a.Fsa||e;if(!Array.isArray(f)&&(f=f.table||f,!Array.isArray(f))){var ha=[];Object.keys(f).forEach(function(ja){ha[ja.charCodeAt(0)]=f[ja]});
f=ha}r=!f[122];z=!f[121];h instanceof oa||(h=na(h));ha=0;if(r||z){var fa=0;for(ca=h.length;fa<ca;fa++){var pa=h.vY(fa);r&&122===pa&&ha++;z&&121===pa&&ha++}}var la=0;ca=Math.ceil(4*h.length/5)+4*ha+5;a=ka(ca);if(4<=h.length&&h.IF(0)===y){for(fa=h.length-2;2<fa&&h.IF(fa)!==w;fa--);if(2>=fa)throw Error("Invalid ascii85 string delimiter pair.");h=h.slice(2,fa)}fa=aa=ea=0;for(ca=h.length;fa<ca;fa++)pa=h.vY(fa),r&&122===pa?la+=a.write(x,la):z&&121===pa?la+=a.write("    ",la):void 0!==f[pa]&&(ea*=85,ea+=
f[pa],aa++,aa%5||(la=a.IQa(ea,la),aa=ea=0));if(aa){h=5-aa;for(fa=0;fa<h;fa++)ea*=85,ea+=84;fa=3;for(ca=h-1;fa>ca;fa--)la=a.JQa(ea>>>8*fa&255,la)}return a.slice(0,la)};ba.tSa=new ma({table:"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ.-:+=^!/*?&<>()[]{}@%$#".split("")});ba.ERa=new ma({aK:!0});ba.Dia=ma}).call(this,n(524).Buffer)}}]);}).call(this || window)
