/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[8],{603:function(ya,ua,n){n.r(ua);var oa=n(0);ya=n(52);var ma=n(523),na=n(290),ka=n(25),ia=window;n=function(){function ba(x){var y=this;this.vDa=function(w){return w&&("image"===w.type.split("/")[0].toLowerCase()||w.name&&!!w.name.match(/.(jpg|jpeg|png|gif)$/i))};this.file=x;this.NDa=new Promise(function(w){return Object(oa.b)(y,void 0,void 0,function(){var b;return Object(oa.d)(this,function(e){switch(e.label){case 0:return this.vDa(this.file)?
[4,Object(na.b)(x)]:[3,2];case 1:b=e.aa(),this.file=ka.q?new Blob([b],{type:x.type}):new File([b],null===x||void 0===x?void 0:x.name,{type:x.type}),e.label=2;case 2:return w(!0),[2]}})})})}ba.prototype.getFileData=function(x){var y=this,w=new FileReader;w.onload=function(b){y.trigger(ba.Events.DOCUMENT_LOADING_PROGRESS,[b.loaded,b.loaded]);x(new Uint8Array(b.target.result))};w.onprogress=function(b){b.lengthComputable&&y.trigger(ba.Events.DOCUMENT_LOADING_PROGRESS,[b.loaded,0<b.total?b.total:0])};
w.readAsArrayBuffer(this.file)};ba.prototype.getFile=function(){return Object(oa.b)(this,void 0,Promise,function(){return Object(oa.d)(this,function(x){switch(x.label){case 0:return[4,this.NDa];case 1:return x.aa(),ia.da.isJSWorker?[2,this.file.path]:[2,this.file]}})})};ba.Events={DOCUMENT_LOADING_PROGRESS:"documentLoadingProgress"};return ba}();Object(ya.a)(n);Object(ma.a)(n);Object(ma.b)(n);ua["default"]=n}}]);}).call(this || window)
