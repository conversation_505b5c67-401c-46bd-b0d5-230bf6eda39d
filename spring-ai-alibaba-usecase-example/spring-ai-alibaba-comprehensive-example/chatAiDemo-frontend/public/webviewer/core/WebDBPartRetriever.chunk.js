/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[12],{612:function(ya,ua,n){n.r(ua);var oa=n(0),ma=n(1);n.n(ma);ya=n(125);n=n(523);ya=function(na){function ka(ia,ba,x){ba=na.call(this,ia,ba,x)||this;ba.db=ia;return ba}Object(oa.c)(ka,na);ka.prototype.request=function(ia){var ba=this;Object(ma.each)(ia,function(x){ba.db.get(x,function(y,w,b){return y?ba.trigger("partReady.partRetriever",{Pb:x,error:y}):ba.trigger("partReady.partRetriever",{Pb:x,data:w,Kl:!1,wi:!1,error:null,De:b})})})};
ka.prototype.Cz=function(ia){ia()};return ka}(ya.a);Object(n.a)(ya);Object(n.b)(ya);ua["default"]=ya}}]);}).call(this || window)
