/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[11],{609:function(ya,ua,n){n.r(ua);var oa=n(0),ma=n(1);n.n(ma);var na=n(3),ka=n(206);ya=n(52);var ia=n(125),ba=n(345),x=n(104),y=n(344);n=n(523);var w=window,b=function(){function f(a,r,z){var aa=-1===a.indexOf("?")?"?":"&";switch(r){case x.a.NEVER_CACHE:this.url="".concat(a+aa,"_=").concat(Object(ma.uniqueId)());break;default:this.url=a}this.Tf=z;this.request=new XMLHttpRequest;this.request.open("GET",this.url,!0);this.request.setRequestHeader("X-Requested-With",
"XMLHttpRequest");this.request.overrideMimeType?this.request.overrideMimeType("text/plain; charset=x-user-defined"):this.request.setRequestHeader("Accept-Charset","x-user-defined");this.status=y.a.NOT_STARTED}f.prototype.start=function(a,r){var z=this,aa=this,ea=this.request,ca;aa.IE=0;a&&Object.keys(a).forEach(function(ha){z.request.setRequestHeader(ha,a[ha])});r&&(this.request.withCredentials=r);this.xN=setInterval(function(){var ha=0===window.document.URL.indexOf("file:///");ha=200===ea.status||
ha&&0===ea.status;if(ea.readyState!==y.b.DONE||ha){try{ea.responseText}catch(fa){return}aa.IE<ea.responseText.length&&(ca=aa.hIa())&&aa.trigger(f.Events.DATA,[ca]);0===ea.readyState&&(clearInterval(aa.xN),aa.trigger(f.Events.DONE))}else clearInterval(aa.xN),aa.trigger(f.Events.DONE,["Error received return status ".concat(ea.status)])},1E3);this.request.send(null);this.status=y.a.STARTED};f.prototype.hIa=function(){var a=this.request,r=a.responseText;if(0!==r.length)if(this.IE===r.length)clearInterval(this.xN),
this.trigger(f.Events.DONE);else return r=Math.min(this.IE+3E6,r.length),a=w.n9(a,this.IE,!0,r),this.IE=r,a};f.prototype.abort=function(){clearInterval(this.xN);var a=this;this.request.onreadystatechange=function(){Object(na.i)("StreamingRequest aborted");a.status=y.a.ABORTED;return a.trigger(f.Events.ABORTED)};this.request.abort()};f.prototype.finish=function(){var a=this;this.request.onreadystatechange=function(){a.status=y.a.SUCCESS;return a.trigger(f.Events.DONE)};this.request.abort()};f.Events=
{DONE:"done",DATA:"data",ABORTED:"aborted"};return f}();Object(ya.a)(b);var e;(function(f){f[f.LOCAL_HEADER=0]="LOCAL_HEADER";f[f.FILE=1]="FILE";f[f.CENTRAL_DIR=2]="CENTRAL_DIR"})(e||(e={}));var h=function(f){function a(){var r=f.call(this)||this;r.buffer="";r.state=e.LOCAL_HEADER;r.a_=4;r.tq=null;r.Hz=ka.c;r.ps={};return r}Object(oa.c)(a,f);a.prototype.VHa=function(r){var z;for(r=this.buffer+r;r.length>=this.Hz;)switch(this.state){case e.LOCAL_HEADER:this.tq=z=this.mIa(r.slice(0,this.Hz));if(z.vA!==
ka.g)throw Error("Wrong signature in local header: ".concat(z.vA));r=r.slice(this.Hz);this.state=e.FILE;this.Hz=z.$R+z.Rv+z.eD+this.a_;this.trigger(a.Events.HEADER,[z]);break;case e.FILE:this.tq.name=r.slice(0,this.tq.Rv);this.ps[this.tq.name]=this.tq;z=this.Hz-this.a_;var aa=r.slice(this.tq.Rv+this.tq.eD,z);this.trigger(a.Events.FILE,[this.tq.name,aa,this.tq.vS]);r=r.slice(z);if(r.slice(0,this.a_)===ka.h)this.state=e.LOCAL_HEADER,this.Hz=ka.c;else return this.state=e.CENTRAL_DIR,!0}this.buffer=r;
return!1};a.Events={HEADER:"header",FILE:"file"};return a}(ba.a);Object(ya.a)(h);ya=function(f){function a(r,z,aa,ea,ca){aa=f.call(this,r,aa,ea)||this;aa.url=r;aa.stream=new b(r,z);aa.We=new h;aa.Tca=window.createPromiseCapability();aa.Ida={};aa.Tf=ca;return aa}Object(oa.c)(a,f);a.prototype.SF=function(r){var z=this;this.request([this.Om,this.Xo,this.Nm]);this.stream.addEventListener(b.Events.DATA,function(aa){try{if(z.We.VHa(aa))return z.stream.finish()}catch(ea){throw z.stream.abort(),z.Zu(ea),
r(ea),ea;}});this.stream.addEventListener(b.Events.DONE,function(aa){z.qHa=!0;z.Tca.resolve();aa&&(z.Zu(aa),r(aa))});this.We.addEventListener(h.Events.HEADER,Object(ma.bind)(this.Hda,this));this.We.addEventListener(h.Events.FILE,Object(ma.bind)(this.EIa,this));return this.stream.start(this.Tf,this.withCredentials)};a.prototype.g9=function(r){var z=this;this.Tca.promise.then(function(){r(Object.keys(z.We.ps))})};a.prototype.ht=function(){return!0};a.prototype.request=function(r){var z=this;this.qHa&&
r.forEach(function(aa){z.Ida[aa]||z.sPa(aa)})};a.prototype.Hda=function(){};a.prototype.abort=function(){this.stream&&this.stream.abort()};a.prototype.sPa=function(r){this.trigger(ia.a.Events.PART_READY,[{Pb:r,error:"Requested part not found",Kl:!1,wi:!1}])};a.prototype.EIa=function(r,z,aa){this.Ida[r]=!0;this.trigger(ia.a.Events.PART_READY,[{Pb:r,data:z,Kl:!1,wi:!1,error:null,De:aa}])};return a}(ia.a);Object(n.a)(ya);Object(n.b)(ya);ua["default"]=ya}}]);}).call(this || window)
