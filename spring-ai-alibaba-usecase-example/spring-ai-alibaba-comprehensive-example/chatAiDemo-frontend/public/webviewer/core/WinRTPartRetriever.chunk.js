/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[14],{610:function(ya,ua,n){n.r(ua);var oa=n(0),ma=n(344);ya=n(600);var na=n(125);n=n(523);var ka={},ia=function(ba){function x(y,w){var b=ba.call(this,y,w)||this;b.url=y;b.range=w;b.status=ma.a.NOT_STARTED;return b}Object(oa.c)(x,ba);x.prototype.start=function(y){var w=this;"undefined"===typeof ka[this.range.start]&&(ka[this.range.start]={k_:function(b){var e=atob(b),h,f=e.length;b=new Uint8Array(f);for(h=0;h<f;++h)b[h]=e.charCodeAt(h);
e=b.length;h="";for(var a=0;a<e;)f=b.subarray(a,a+1024),a+=1024,h+=String.fromCharCode.apply(null,f);w.k_(h,y)},UTa:function(){w.status=ma.a.ERROR;y({code:w.status})}},window.external.notify(this.url),this.status=ma.a.STARTED);w.qL()};return x}(ya.ByteRangeRequest);ya=function(ba){function x(y,w,b,e){y=ba.call(this,y,b,e)||this;y.GF=ia;return y}Object(oa.c)(x,ba);x.prototype.KC=function(y,w){return"".concat(y,"?").concat(w.start,"&").concat(w.stop?w.stop:"")};return x}(na.a);Object(n.a)(ya);Object(n.b)(ya);
ua["default"]=ya}}]);}).call(this || window)
