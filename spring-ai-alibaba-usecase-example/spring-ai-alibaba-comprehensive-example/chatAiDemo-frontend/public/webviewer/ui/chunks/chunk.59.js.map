{"version": 3, "sources": ["webpack:///./src/ui/src/components/CustomModal/CustomModal.scss?65b5", "webpack:///./src/ui/src/components/CustomModal/CustomModal.scss", "webpack:///./src/ui/src/components/CustomModal/CustomModal.js", "webpack:///./src/ui/src/components/CustomModal/index.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "isDOMNode", "element", "Node", "parent", "e", "CustomModalItemPropTypes", "dataElement", "PropTypes", "string", "isRequired", "isOpen", "bool", "render", "func", "object", "close", "disableBackdropClick", "disableEscapeKeyDown", "CustomModalItem", "props", "useState", "usedElement", "setUsedElement", "modalRef", "React", "useRef", "useLayoutEffect", "current", "focus", "createTextNode", "isValidElement", "isReactElement", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "onClickOutsideModal", "useCallback", "onKeyDownOutsideModal", "event", "which", "useEffect", "addEventListener", "removeEventListener", "classNames", "role", "tabIndex", "className", "join", "data-element", "onClick", "ref", "stopPropagation", "CustomModal", "useSelector", "state", "selectors", "getCustomModals", "getOpenElements", "getDisabledElements", "shallowEqual", "customModals", "openElements", "disableElements", "dispatch", "useDispatch", "closeCustomModalElement", "actions", "closeElement", "map", "customModalItem", "isModalOpen", "disableElement", "disabled", "header", "body", "footer", "headerDiv", "bodyDiv", "footerDiv", "createElementsForModal", "children", "appendToComponent", "child", "div", "classList", "add", "title", "button", "style", "createElement", "split", "name", "innerText", "onclick", "Object", "entries", "k", "v", "Number", "isInteger", "querySelector", "<PERSON><PERSON><PERSON><PERSON>", "Fragment", "key", "filter", "Boolean", "propTypes", "memo"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,wuLAAyuL,KAGlwL0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,wwCCFvB,SAASC,EAAUC,GACjB,IACE,OACEA,aAAmB1B,OAAO2B,MAC1BD,aAAmB1B,OAAO4B,OAAOD,KAEnC,MAAOE,GACP,OAAO,GAIX,IAEMC,EAA2B,CAC/BC,YAAaC,IAAUC,OAAOC,WAC9BC,OAAQH,IAAUI,KAClBC,OAAQL,IAAUM,KAClBZ,QAASM,IAAUO,OACnBC,MAAOR,IAAUM,KACjBG,qBAAsBT,IAAUI,KAChCM,qBAAsBV,IAAUI,MAGlC,SAASO,EAAgBC,GACvB,IAAQb,EAA4Fa,EAA5Fb,YAAaI,EAA+ES,EAA/ET,OAAQT,EAAuEkB,EAAvElB,QAASW,EAA8DO,EAA9DP,OAAQG,EAAsDI,EAAtDJ,MAAOC,EAA+CG,EAA/CH,qBAAsBC,EAAyBE,EAAzBF,qBACpB,IAAjBG,mBAASnB,GAAQ,GAAhDoB,EAAW,KAAEC,EAAc,KAC5BC,EAAWC,IAAMC,SAEvBD,IAAME,iBAAgB,WACpB,GAAId,GAAUF,GAAUa,EAASI,QAAS,CACxCJ,EAASI,QAAQC,QACjB,IAAIvC,EAAKuB,IAKT,GAJkB,iBAAPvB,IACTA,EAAKZ,SAASoD,eAAexC,IAtBd,SAACY,GAAO,OAAKuB,IAAMM,eAAe7B,GAyB/C8B,CAAe1C,GACjBiC,EAAejC,QACV,GAAIW,EAAUX,GAAK,CACxB,KAAOkC,EAASI,QAAQK,YACtBT,EAASI,QAAQM,YAAYV,EAASI,QAAQO,WAEhDX,EAASI,QAAQhD,YAAYU,OAGhC,CAACqB,EAAQE,IAEZ,IAAMuB,EAAsBX,IAAMY,aAAY,WACvCpB,GACHD,EAAMT,KAEP,CAACS,EAAOT,EAAaU,IAElBqB,EAAwBb,IAAMY,aAAY,SAA6BE,GACvEA,GAAyB,KAAhBA,EAAMC,QAAiBtB,GAClCF,EAAMT,KAEP,CAACS,EAAOT,EAAaW,IAExBO,IAAMgB,WAAU,WACd,GAAI9B,EAEF,OADAjC,SAASgE,iBAAiB,UAAWJ,GAC9B,WACL5D,SAASiE,oBAAoB,UAAWL,MAG3C,CAACA,EAAuB3B,IAG3B,IAAMiC,EAAa,CAAC,qBAQpB,OAPAA,EAAWrD,KAAKgB,GACZI,EACFiC,EAAWrD,KAAK,QAEhBqD,EAAWrD,KAAK,UAKhB,yBACEsD,KAAK,SACLC,SAAS,KACTC,UAAWH,EAAWI,KAAK,KAC3BC,eAAc1C,EACd2C,QAASd,GAGT,yBAAKe,IAAK3B,EAAUuB,UAAU,wBAAwBG,QAAS,SAAC7C,GAAC,OAAKA,EAAE+C,oBAAoB9B,IAOlG,SAAS+B,IACP,IAOC,IAPqDC,aACpD,SAACC,GAAK,MAAK,CACTC,IAAUC,gBAAgBF,GAC1BC,IAAUE,gBAAgBH,GAC1BC,IAAUG,oBAAoBJ,MAEhCK,KACD,GAPMC,EAAY,KAAEC,EAAY,KAAEC,EAAe,KAS5CC,EAAWC,cACXC,EAA0BzC,IAAMY,aAAY,SAAyC9B,GACzFyD,EAASG,IAAQC,aAAa7D,MAC7B,CAACyD,IAEJ,OAAOH,EAAaQ,KAAI,SAACC,GACvB,IAAQ/D,EAA4E+D,EAA5E/D,YAAW,EAAiE+D,EAA/DrD,4BAAoB,IAAG,GAAK,IAAmCqD,EAAjCpD,4BAAoB,IAAG,GAAK,EACzEL,EAAWyD,EAAXzD,OACA0D,EAAcT,EAAavD,GAC3BiE,EAAiBT,EAAgBxD,GACvC,GAAIiE,IAA8C,IAA5BA,EAAeC,SAEnC,OAAO,KAGT,IAAQC,EAAyBJ,EAAzBI,OAAQC,EAAiBL,EAAjBK,KAAMC,EAAWN,EAAXM,OAElBC,EAAY,KACZC,EAAU,KACVC,EAAY,KAQVC,EAAyB,SAACnF,EAAWsD,EAAK8B,GAC1CpF,EAPoB,SAACP,EAAI6D,GACzBlD,EAAUX,IACZ6D,EAAIvE,YAAYU,GAMhB4F,CAAkBrF,EAAWsD,GACpB8B,GAAYA,EAASlG,QAC9BkG,EAAS5F,SAAQ,SAAC8F,EAAO9G,GACvB,IAAI+G,EACJ,GAAInF,EAAUkF,GACZA,EAAME,UAAUC,IAAI,WAAD,OAAYjH,IAC/B+G,EAAMD,MACD,OACGI,EAAyCJ,EAAzCI,MAAOC,EAAkCL,EAAlCK,OAAQC,EAA0BN,EAA1BM,MAAK,EAAqBN,EAAnBjC,eAAO,IAAG,OAAI,EACtCH,EAAcoC,EAAdpC,UACNqC,EAAOI,EAAU9G,SAASgH,cAAc,UAAYhH,SAASgH,cAAc,OAElE,QAAT,EADA3C,EAAayC,EAAM,iBAAczC,GAAcA,SACtC,OAAT,EAAW4C,MAAM,KAAKtG,SAAQ,SAACuG,GAAI,OAAKR,EAAIC,UAAUC,IAAIM,MAE1DR,EAAIC,UAAUC,IAAI,WAAD,OAAYjH,IAC7B+G,EAAIS,UAAYN,EAChBH,EAAIU,QAAU5C,EAEVuC,IACFL,EAAIK,MAASM,OAAOC,QAAQP,GAAOpB,KAAI,yBAAE4B,EAAC,KAAEC,EAAC,YAAOC,OAAOC,UAAUF,GAAK,GAAH,OAAMD,EAAC,YAAIC,EAAC,gBAAUD,EAAC,YAAIC,MAAMlD,KAAK,MAGjH,IAAM1D,EAAK6D,EAAIkD,cAAc,YAAD,OAAahI,IACrCiB,EACF6D,EAAImD,aAAalB,EAAK9F,GAEtB6D,EAAIvE,YAAYwG,OAMxB,GAAIV,EAAQ,CACV,IAAQ3B,EAAuC2B,EAAvC3B,UAAW0C,EAA4Bf,EAA5Be,MAAK,EAAuBf,EAArB7E,iBAAS,IAAG,OAAI,EACpC0F,EAAUb,EAAVa,MACNA,EAASA,IAAU1F,EAAa,2BAAI0F,GAAa,KAEjDV,EAAY,yBACV1B,IAAK,SAACA,GACJ,IAAQuB,EAAWJ,EAAXI,OACR,EAA4CA,EAApCO,gBAAQ,IAAG,KAAE,IAAuBP,EAArB7E,UACvBmF,OADgC,IAAG,OAAI,EACL7B,EAAK8B,IAEzClC,UAAS,6BAAwBA,GACjC0C,MAAOA,GACPF,GAGJ,GAAIZ,EAAM,CACR,IAAQ5B,EAAuC4B,EAAvC5B,UAAW0C,EAA4Bd,EAA5Bc,MAAK,EAAuBd,EAArB9E,iBAAS,IAAG,OAAI,EACpC0F,EAAUZ,EAAVY,MACNA,EAASA,IAAU1F,EAAa,2BAAI0F,GAAa,KAEjDT,EAAU,yBACR3B,IAAK,SAACA,GACJ,IAAQwB,EAASL,EAATK,KACR,EAA4CA,EAApCM,gBAAQ,IAAG,KAAE,IAAuBN,EAArB9E,UACvBmF,OADgC,IAAG,OAAI,EACL7B,EAAK8B,IAEzClC,UAAS,2BAAsBA,GAC/B0C,MAAOA,GACPF,GAGJ,GAAIX,EAAQ,CACV,IAAQ7B,EAAuC6B,EAAvC7B,UAAW0C,EAA4Bb,EAA5Ba,MAAK,EAAuBb,EAArB/E,iBAAS,IAAG,OAAI,EACpC0F,EAAUX,EAAVW,MACNA,EAASA,IAAU1F,EAAa,2BAAI0F,GAAa,KAEjDR,EAAY,yBACV5B,IAAK,SAACA,GACJ,IAAQyB,EAAWN,EAAXM,OACR,EAA4CA,EAApCK,gBAAQ,IAAG,KAAE,IAAuBL,EAArB/E,UACvBmF,OADgC,IAAG,OAAI,EACL7B,EAAK8B,IAEzClC,UAAS,6BAAwBA,GACjC0C,MAAOA,GACPF,GAGJ,IAAIrF,EAAU,KAMd,OALIwE,GAAUC,GAAQC,KACpB1E,EAAU,kBAAC,IAAMqG,SAAQ,KAAE1B,EAAWC,EAASC,GAC/ClE,EAAS,MAIT,kBAACM,EAAe,CACdqF,IAAKjG,EACLA,YAAaA,EACbI,OAAQ4D,EACR1D,OAAQA,EACRX,QAASA,EACTc,MAAOkD,EACPjD,qBAAsBA,EACtBC,qBAAsBA,OAGzBuF,OAAOC,SA3IZvF,EAAgBwF,UAAYrG,EA8IbmB,UAAMmF,KAAKvD,GChPXA", "file": "chunks/chunk.59.js", "sourcesContent": ["var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./CustomModal.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".open.CustomModal{visibility:visible}.closed.CustomModal{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.CustomModal .footer .modal-button.confirm:hover{background:var(--primary-button-hover);border-color:var(--primary-button-hover);color:var(--gray-0)}.CustomModal .footer .modal-button.confirm{background:var(--primary-button);border-color:var(--primary-button);color:var(--primary-button-text)}.CustomModal .footer .modal-button.confirm.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.CustomModal .footer .modal-button.confirm.disabled span{color:var(--primary-button-text)}.CustomModal .footer .modal-button.cancel:hover,.CustomModal .footer .modal-button.secondary-btn-custom:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6)}.CustomModal .footer .modal-button.cancel,.CustomModal .footer .modal-button.secondary-btn-custom{border:none;box-shadow:inset 0 0 0 1px var(--primary-button);color:var(--primary-button)}.CustomModal .footer .modal-button.cancel.disabled,.CustomModal .footer .modal-button.secondary-btn-custom.disabled{cursor:default;border:none;box-shadow:inset 0 0 0 1px rgba(43,115,171,.5);color:rgba(43,115,171,.5)}.CustomModal .footer .modal-button.cancel.disabled span,.CustomModal .footer .modal-button.secondary-btn-custom.disabled span{color:rgba(43,115,171,.5)}.CustomModal{position:fixed;left:0;bottom:0;z-index:100;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background:var(--modal-negative-space)}.CustomModal .modal-container .wrapper .modal-content{padding:10px}.CustomModal .footer{display:flex;flex-direction:row;justify-content:flex-end;width:100%;margin-top:13px}.CustomModal .footer.modal-footer{padding:16px;margin:0;border-top:1px solid var(--divider)}.CustomModal .footer .modal-button{display:flex;justify-content:center;align-items:center;padding:6px 18px;margin:8px 0 0;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;height:30px;cursor:pointer}.CustomModal .footer .modal-button.confirm{margin-left:4px}.CustomModal .footer .modal-button.secondary-btn-custom{border-radius:4px;padding:2px 20px 4px;cursor:pointer}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .CustomModal .footer .modal-button{padding:23px 8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .CustomModal .footer .modal-button{padding:23px 8px}}.CustomModal .swipe-indicator{background:var(--swipe-indicator-bg);border-radius:2px;height:4px;width:38px;position:absolute;top:12px;margin-left:auto;margin-right:auto;left:0;right:0}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .CustomModal .swipe-indicator{display:none}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .CustomModal .swipe-indicator{display:none}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .CustomModal .swipe-indicator{width:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .CustomModal .swipe-indicator{width:32px}}.CustomModal .CustomModal-container{box-shadow:0 0 3px 0 var(--document-box-shadow);overflow-y:auto;max-height:100%}@media(max-height:500px){.App:not(.is-web-component) .CustomModal .CustomModal-container,.CustomModal .App:not(.is-web-component) .CustomModal-container{overflow:auto;max-height:100%}}@container (max-height: 500px){.App.is-web-component .CustomModal .CustomModal-container,.CustomModal .App.is-web-component .CustomModal-container{overflow:auto;max-height:100%}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .CustomModal .CustomModal-container,.CustomModal .App:not(.is-in-desktop-only-mode):not(.is-web-component) .CustomModal-container{width:100%;position:fixed;left:0;bottom:0;padding-top:4px;min-width:100px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .CustomModal .CustomModal-container,.CustomModal .App.is-web-component:not(.is-in-desktop-only-mode) .CustomModal-container{width:100%;position:fixed;left:0;bottom:0;padding-top:4px;min-width:100px}}.CustomModal .CustomModal-container{display:flex;flex-direction:column;width:480px;padding:0;border-radius:4px;box-shadow:0 0 3px var(--document-box-shadow);background:var(--component-background);position:relative}.CustomModal .CustomModal-container .CustomModal-header{padding:20px 16px 16px;box-shadow:inset 0 -1px 0 var(--modal-stroke-and-border)}.CustomModal .CustomModal-container .CustomModal-header p{display:flex;align-items:center;height:24px;font-size:16px;font-weight:700;margin:0 16px 0 0}.CustomModal .CustomModal-container .CustomModal-body{padding:16px}.CustomModal .CustomModal-container .CustomModal-footer{padding:16px;box-shadow:inset 0 1px 0 var(--modal-stroke-and-border);margin:0}.CustomModal .CustomModal-container .CustomModal-footer .modal-button{margin-top:0}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React, { useState } from 'react';\nimport PropTypes from 'prop-types';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport actions from 'actions';\nimport selectors from 'selectors';\n\nimport './CustomModal.scss';\n\nfunction isDOMNode(element) {\n  try {\n    return (\n      element instanceof window.Node ||\n      element instanceof window.parent.Node\n    );\n  } catch (e) {\n    return false;\n  }\n}\n\nconst isReactElement = (element) => React.isValidElement(element);\n\nconst CustomModalItemPropTypes = {\n  dataElement: PropTypes.string.isRequired,\n  isOpen: PropTypes.bool,\n  render: PropTypes.func,\n  element: PropTypes.object,\n  close: PropTypes.func,\n  disableBackdropClick: PropTypes.bool,\n  disableEscapeKeyDown: PropTypes.bool,\n};\n\nfunction CustomModalItem(props) {\n  const { dataElement, isOpen, element, render, close, disableBackdropClick, disableEscapeKeyDown } = props;\n  const [usedElement, setUsedElement] = useState(element);\n  const modalRef = React.useRef();\n\n  React.useLayoutEffect(function renderCustomModal() {\n    if (render && isOpen && modalRef.current) {\n      modalRef.current.focus();\n      let el = render();\n      if (typeof el === 'string') {\n        el = document.createTextNode(el);\n      }\n      // Only support React components through the render function\n      if (isReactElement(el)) {\n        setUsedElement(el);\n      } else if (isDOMNode(el)) {\n        while (modalRef.current.firstChild) {\n          modalRef.current.removeChild(modalRef.current.lastChild);\n        }\n        modalRef.current.appendChild(el);\n      }\n    }\n  }, [isOpen, render]);\n\n  const onClickOutsideModal = React.useCallback(function onClickOutsideModal() {\n    if (!disableBackdropClick) {\n      close(dataElement);\n    }\n  }, [close, dataElement, disableBackdropClick]);\n\n  const onKeyDownOutsideModal = React.useCallback(function onClickOutsideModal(event) {\n    if (event && event.which === 27 && !disableEscapeKeyDown) {\n      close(dataElement);\n    }\n  }, [close, dataElement, disableEscapeKeyDown]);\n\n  React.useEffect(() => {\n    if (isOpen) {\n      document.addEventListener('keydown', onKeyDownOutsideModal);\n      return function cleanUp() {\n        document.removeEventListener('keydown', onKeyDownOutsideModal);\n      };\n    }\n  }, [onKeyDownOutsideModal, isOpen]);\n\n\n  const classNames = ['Modal CustomModal'];\n  classNames.push(dataElement);\n  if (isOpen) {\n    classNames.push('open');\n  } else {\n    classNames.push('closed');\n  }\n\n  return (\n    // Disable key-events for modal element as we attach keydown for Escape to document\n    <div\n      role=\"button\"\n      tabIndex=\"-1\"\n      className={classNames.join(' ')}\n      data-element={dataElement}\n      onClick={onClickOutsideModal}\n    >\n      {/* This element is not interactive. Reason to have onclick is to prevent propagation */}\n      <div ref={modalRef} className=\"CustomModal-container\" onClick={(e) => e.stopPropagation()}>{usedElement}</div>\n    </div>\n  );\n}\n\nCustomModalItem.propTypes = CustomModalItemPropTypes;\n\nfunction CustomModal() {\n  const [customModals, openElements, disableElements] = useSelector(\n    (state) => [\n      selectors.getCustomModals(state),\n      selectors.getOpenElements(state),\n      selectors.getDisabledElements(state),\n    ],\n    shallowEqual,\n  );\n\n  const dispatch = useDispatch();\n  const closeCustomModalElement = React.useCallback(function closeCustomModalElementCallback(dataElement) {\n    dispatch(actions.closeElement(dataElement));\n  }, [dispatch]);\n\n  return customModals.map((customModalItem) => {\n    const { dataElement, disableBackdropClick = false, disableEscapeKeyDown = false } = customModalItem;\n    let { render } = customModalItem;\n    const isModalOpen = openElements[dataElement];\n    const disableElement = disableElements[dataElement];\n    if (disableElement && disableElement.disabled === true) {\n      // avoid rendering component if it is disabled\n      return null;\n    }\n\n    const { header, body, footer } = customModalItem;\n\n    let headerDiv = null;\n    let bodyDiv = null;\n    let footerDiv = null;\n\n    const appendToComponent = (el, ref) => {\n      if (isDOMNode(el)) {\n        ref.appendChild(el);\n      }\n    };\n\n    const createElementsForModal = (innerHTML, ref, children) => {\n      if (innerHTML) {\n        appendToComponent(innerHTML, ref);\n      } else if (children && children.length) {\n        children.forEach((child, i) => {\n          let div;\n          if (isDOMNode(child)) {\n            child.classList.add(`customEl${i}`);\n            div = child;\n          } else {\n            const { title, button, style, onClick = null } = child;\n            let { className } = child;\n            div = (button) ? document.createElement('button') : document.createElement('div');\n            className = (button) ? `Button ${className}` : className;\n            className?.split(' ').forEach((name) => div.classList.add(name));\n\n            div.classList.add(`customEl${i}`);\n            div.innerText = title;\n            div.onclick = onClick;\n\n            if (style) {\n              div.style = (Object.entries(style).map(([k, v]) => (Number.isInteger(v) ? `${k}:${v}px` : `${k}:${v}`)).join(';'));\n            }\n          }\n          const el = ref.querySelector(`.customEl${i}`);\n          if (el) {\n            ref.replaceChild(div, el);\n          } else {\n            ref.appendChild(div);\n          }\n        });\n      }\n    };\n\n    if (header) {\n      const { className, style, innerHTML = null } = header;\n      let { title } = header;\n      title = (title && !innerHTML) ? <p>{title}</p> : null;\n\n      headerDiv = <div\n        ref={(ref) => {\n          const { header } = customModalItem;\n          const { children = [], innerHTML = null } = header;\n          createElementsForModal(innerHTML, ref, children);\n        }}\n        className={`CustomModal-header ${className}`}\n        style={style}\n      >{title}</div>;\n    }\n\n    if (body) {\n      const { className, style, innerHTML = null } = body;\n      let { title } = body;\n      title = (title && !innerHTML) ? <p>{title}</p> : null;\n\n      bodyDiv = <div\n        ref={(ref) => {\n          const { body } = customModalItem;\n          const { children = [], innerHTML = null } = body;\n          createElementsForModal(innerHTML, ref, children);\n        }}\n        className={`CustomModal-body ${className}`}\n        style={style}\n      >{title}</div>;\n    }\n\n    if (footer) {\n      const { className, style, innerHTML = null } = footer;\n      let { title } = footer;\n      title = (title && !innerHTML) ? <p>{title}</p> : null;\n\n      footerDiv = <div\n        ref={(ref) => {\n          const { footer } = customModalItem;\n          const { children = [], innerHTML = null } = footer;\n          createElementsForModal(innerHTML, ref, children);\n        }}\n        className={`CustomModal-footer ${className}`}\n        style={style}\n      >{title}</div>;\n    }\n\n    let element = null;\n    if (header || body || footer) {\n      element = <React.Fragment>{headerDiv}{bodyDiv}{footerDiv}</React.Fragment>;\n      render = null;\n    }\n\n    return (\n      <CustomModalItem\n        key={dataElement}\n        dataElement={dataElement}\n        isOpen={isModalOpen}\n        render={render}\n        element={element}\n        close={closeCustomModalElement}\n        disableBackdropClick={disableBackdropClick}\n        disableEscapeKeyDown={disableEscapeKeyDown}\n      />\n    );\n  }).filter(Boolean);\n}\n\nexport default React.memo(CustomModal);\n", "import CustomModal from './CustomModal';\n\nexport default CustomModal;\n"], "sourceRoot": ""}