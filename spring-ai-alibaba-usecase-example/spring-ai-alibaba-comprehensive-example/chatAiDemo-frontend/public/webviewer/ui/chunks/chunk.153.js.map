{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/fr.js"], "names": ["module", "exports", "e", "t", "default", "n", "i", "name", "weekdays", "split", "weekdaysShort", "weekdaysMin", "months", "monthsShort", "weekStart", "yearStart", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "relativeTime", "future", "past", "s", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "ordinal", "locale"], "mappings": "gFAAoEA,EAAOC,QAA6K,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAGI,EAAE,CAACC,KAAK,KAAKC,SAAS,sDAAsDC,MAAM,KAAKC,cAAc,qCAAqCD,MAAM,KAAKE,YAAY,uBAAuBF,MAAM,KAAKG,OAAO,uFAAuFH,MAAM,KAAKI,YAAY,iEAAiEJ,MAAM,KAAKK,UAAU,EAAEC,UAAU,EAAEC,QAAQ,CAACC,GAAG,QAAQC,IAAI,WAAWC,EAAE,aAAaC,GAAG,cAAcC,IAAI,oBAAoBC,KAAK,0BAA0BC,aAAa,CAACC,OAAO,UAAUC,KAAK,YAAYC,EAAE,oBAAoBC,EAAE,aAAaC,GAAG,aAAaC,EAAE,YAAYC,GAAG,YAAYC,EAAE,UAAUC,GAAG,WAAWC,EAAE,UAAUC,GAAG,UAAUC,EAAE,QAAQC,GAAG,UAAUC,QAAQ,SAASnC,GAAG,OAASA,GAAG,IAAIA,EAAE,KAAK,MAAM,OAAOC,EAAEC,QAAQkC,OAAOhC,EAAE,MAAK,GAAIA,EAAxkCD,CAAE,EAAQ", "file": "chunks/chunk.153.js", "sourcesContent": ["!function(e,n){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=n(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],n):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_fr=n(e.dayjs)}(this,(function(e){\"use strict\";function n(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var t=n(e),i={name:\"fr\",weekdays:\"dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi\".split(\"_\"),weekdaysShort:\"dim._lun._mar._mer._jeu._ven._sam.\".split(\"_\"),weekdaysMin:\"di_lu_ma_me_je_ve_sa\".split(\"_\"),months:\"janvier_février_mars_avril_mai_juin_juillet_août_septembre_octobre_novembre_décembre\".split(\"_\"),monthsShort:\"janv._févr._mars_avr._mai_juin_juil._août_sept._oct._nov._déc.\".split(\"_\"),weekStart:1,yearStart:4,formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY HH:mm\",LLLL:\"dddd D MMMM YYYY HH:mm\"},relativeTime:{future:\"dans %s\",past:\"il y a %s\",s:\"quelques secondes\",m:\"une minute\",mm:\"%d minutes\",h:\"une heure\",hh:\"%d heures\",d:\"un jour\",dd:\"%d jours\",M:\"un mois\",MM:\"%d mois\",y:\"un an\",yy:\"%d ans\"},ordinal:function(e){return\"\"+e+(1===e?\"er\":\"\")}};return t.default.locale(i,null,!0),i}));"], "sourceRoot": ""}