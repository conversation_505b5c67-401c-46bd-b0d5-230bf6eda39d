(window.webpackJsonp=window.webpackJsonp||[]).push([[62],{1944:function(e,a,l){var r=l(32),o=l(1945);"string"==typeof(o=o.__esModule?o.default:o)&&(o=[[e.i,o,""]]);var n={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let a;a=document.getElementsByTagName("apryse-webviewer"),a.length||(a=function e(a,l=document){const r=[];return l.querySelectorAll(a).forEach(e=>r.push(e)),l.querySelectorAll("*").forEach(l=>{l.shadowRoot&&r.push(...e(a,l.shadowRoot))}),r}("apryse-webviewer"));const l=[];for(let r=0;r<a.length;r++){const o=a[r];if(0===r)o.shadowRoot.appendChild(e),e.onload=function(){l.length>0&&l.forEach(a=>{a.innerHTML=e.innerHTML})};else{const a=e.cloneNode(!0);o.shadowRoot.appendChild(a),l.push(a)}}},singleton:!1};r(o,n);e.exports=o.locals||{}},1945:function(e,a,l){(e.exports=l(33)(!1)).push([e.i,".FormulaBar{display:flex;border:1px solid var(--gray-5);background-color:var(--gray-2);padding:8px;grid-gap:8px;gap:8px;border-bottom:1px solid #e0e0e0}.FormulaBar .RangeInput.focus-visible,.FormulaBar .RangeInput:focus-visible{outline:var(--focus-visible-outline)!important}.FormulaBar .Formula{display:flex;align-items:center;flex-grow:1;height:32px;position:relative;border:1px solid var(--border);border-radius:4px;background-color:var(--component-background);padding-left:30px}.FormulaBar .Formula.readOnly{border:1px solid var(--disabled-text)}.FormulaBar .Formula:not(.readOnly)[focus-within]{outline:var(--focus-visible-outline)!important}.FormulaBar .Formula:not(.readOnly):focus-within{outline:var(--focus-visible-outline)!important}.FormulaBar .FormulaIcon{position:absolute;left:8px}.FormulaBar .FormulaIcon.readOnly{color:var(--disabled-icon)}.FormulaBar .FormulaInput{flex-grow:1;width:100%;padding:4px;border:none;outline:none;font-size:14px;background-color:transparent}.FormulaBar .FormulaInput.readOnly{color:var(--disabled-text)}",""])},1996:function(e,a,l){"use strict";l.r(a);var r=l(0),o=l.n(r),n=l(6),t=l(3),i=l(428),u=l(42),s=(l(1944),l(4)),c=l.n(s),d=l(5),p=l(76),m=l(17),f=l.n(m),b=function(e){var a=e.isReadOnly,l=e.activeCellRange,r=e.cellFormula,n=e.stringCellValue,t=Object(i.a)().t,s=r||n||"";return o.a.createElement(p.a,{className:"FormulaBar",dataElement:d.a.FORMULA_BAR},o.a.createElement("input",{type:"text",className:"RangeInput",value:l,readOnly:a,"aria-label":t("formulaBar.range")}),o.a.createElement("div",{className:f()("Formula",{readOnly:a})},o.a.createElement(u.a,{glyph:"function",className:f()("FormulaIcon",{readOnly:a})}),o.a.createElement("input",{className:f()("FormulaInput",{readOnly:a}),type:"text",value:s,readOnly:a,"aria-label":t("formulaBar.label")})))};b.propTypes={isReadOnly:c.a.bool,activeCellRange:c.a.string,cellFormula:c.a.string,stringCellValue:c.a.string};var g=b,v=[{value:"=SUMIF",label:"SUMIF",description:"formulaBar.sumif"},{value:"=SUMSQ",label:"SUMSQ",description:"formulaBar.sumsq"},{value:"=SUM",label:"SUM",description:"formulaBar.sum"},{value:"=ASINH",label:"ASINH",description:"formulaBar.asinh"},{value:"=ACOS",label:"ACOS",description:"formulaBar.acos"},{value:"=COSH",label:"COSH",description:"formulaBar.cosh"},{value:"=ISEVEN",label:"ISEVEN",description:"formulaBar.iseven"},{value:"=ISODD",label:"ISODD",description:"formulaBar.isodd"}],F=function(){var e=Object(n.e)(t.a.getActiveCellRange),a=Object(n.e)(t.a.getCellFormula),l=Object(n.e)(t.a.getStringCellValue);return o.a.createElement(g,{isReadOnly:!0,formulaOptions:v,activeCellRange:e,cellFormula:a,stringCellValue:l})};a.default=F}}]);
//# sourceMappingURL=chunk.62.js.map