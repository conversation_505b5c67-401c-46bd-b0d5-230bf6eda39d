{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/sl.js"], "names": ["module", "exports", "e", "_", "default", "t", "n", "name", "weekdays", "split", "months", "weekStart", "weekdaysShort", "monthsShort", "weekdaysMin", "ordinal", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "relativeTime", "future", "past", "s", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "locale"], "mappings": "gFAAoEA,EAAOC,QAA6K,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAGI,EAAE,CAACC,KAAK,KAAKC,SAAS,sDAAsDC,MAAM,KAAKC,OAAO,wFAAwFD,MAAM,KAAKE,UAAU,EAAEC,cAAc,qCAAqCH,MAAM,KAAKI,YAAY,8DAA8DJ,MAAM,KAAKK,YAAY,uBAAuBL,MAAM,KAAKM,QAAQ,SAASb,GAAG,OAAOA,EAAE,KAAKc,QAAQ,CAACC,GAAG,OAAOC,IAAI,UAAUC,EAAE,aAAaC,GAAG,eAAeC,IAAI,oBAAoBC,KAAK,2BAA2BC,aAAa,CAACC,OAAO,SAASC,KAAK,UAAUC,EAAE,eAAeC,EAAE,SAASC,GAAG,WAAWC,EAAE,MAAMC,GAAG,QAAQC,EAAE,MAAMC,GAAG,SAASC,EAAE,QAAQC,GAAG,aAAaC,EAAE,OAAOC,GAAG,WAAW,OAAOjC,EAAEC,QAAQiC,OAAO/B,EAAE,MAAK,GAAIA,EAA9gCD,CAAE,EAAQ", "file": "chunks/chunk.211.js", "sourcesContent": ["!function(e,t){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=t(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],t):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_sl=t(e.dayjs)}(this,(function(e){\"use strict\";function t(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var _=t(e),n={name:\"sl\",weekdays:\"nedelja_ponedeljek_torek_sreda_četrtek_petek_sobota\".split(\"_\"),months:\"januar_februar_marec_april_maj_junij_julij_avgust_september_oktober_november_december\".split(\"_\"),weekStart:1,weekdaysShort:\"ned._pon._tor._sre._čet._pet._sob.\".split(\"_\"),monthsShort:\"jan._feb._mar._apr._maj._jun._jul._avg._sep._okt._nov._dec.\".split(\"_\"),weekdaysMin:\"ne_po_to_sr_če_pe_so\".split(\"_\"),ordinal:function(e){return e+\".\"},formats:{LT:\"H:mm\",LTS:\"H:mm:ss\",L:\"DD.MM.YYYY\",LL:\"D. MMMM YYYY\",LLL:\"D. MMMM YYYY H:mm\",LLLL:\"dddd, D. MMMM YYYY H:mm\"},relativeTime:{future:\"čez %s\",past:\"pred %s\",s:\"nekaj sekund\",m:\"minuta\",mm:\"%d minut\",h:\"ura\",hh:\"%d ur\",d:\"dan\",dd:\"%d dni\",M:\"mesec\",MM:\"%d mesecev\",y:\"leto\",yy:\"%d let\"}};return _.default.locale(n,null,!0),n}));"], "sourceRoot": ""}