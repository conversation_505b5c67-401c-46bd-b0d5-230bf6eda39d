{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/x-pseudo.js"], "names": ["module", "exports", "e", "_", "default", "t", "d", "name", "weekdays", "split", "months", "weekStart", "weekdaysShort", "monthsShort", "weekdaysMin", "ordinal", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "relativeTime", "future", "past", "s", "m", "mm", "h", "hh", "dd", "M", "MM", "y", "yy", "locale"], "mappings": "gFAAoEA,EAAOC,QAAmL,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAGI,EAAE,CAACC,KAAK,WAAWC,SAAS,yEAAyEC,MAAM,KAAKC,OAAO,6GAA6GD,MAAM,KAAKE,UAAU,EAAEC,cAAc,qCAAqCH,MAAM,KAAKI,YAAY,8DAA8DJ,MAAM,KAAKK,YAAY,4BAA4BL,MAAM,KAAKM,QAAQ,SAASb,GAAG,OAAOA,GAAGc,QAAQ,CAACC,GAAG,QAAQC,IAAI,WAAWC,EAAE,aAAaC,GAAG,cAAcC,IAAI,oBAAoBC,KAAK,2BAA2BC,aAAa,CAACC,OAAO,SAASC,KAAK,UAAUC,EAAE,mBAAmBC,EAAE,aAAaC,GAAG,eAAeC,EAAE,YAAYC,GAAG,YAAYxB,EAAE,SAASyB,GAAG,WAAWC,EAAE,YAAYC,GAAG,cAAcC,EAAE,UAAUC,GAAG,cAAc,OAAOhC,EAAEC,QAAQgC,OAAO9B,EAAE,MAAK,GAAIA,EAA1mCD,CAAE,EAAQ", "file": "chunks/chunk.237.js", "sourcesContent": ["!function(e,t){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=t(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],t):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_x_pseudo=t(e.dayjs)}(this,(function(e){\"use strict\";function t(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var _=t(e),d={name:\"x-pseudo\",weekdays:\"S~úñdá~ý_Mó~ñdáý~_Túé~sdáý~_Wéd~ñésd~áý_T~húrs~dáý_~Fríd~áý_S~átúr~dáý\".split(\"_\"),months:\"J~áñúá~rý_F~ébrú~árý_~Márc~h_Áp~ríl_~Máý_~Júñ<PERSON>~_<PERSON><PERSON>~ý_Áú~gúst~_Sép~témb~ér_Ó~ctób~ér_Ñ~óvém~bér_~Décé~mbér\".split(\"_\"),weekStart:1,weekdaysShort:\"S~úñ_~Móñ_~Túé_~Wéd_~Thú_~Frí_~Sát\".split(\"_\"),monthsShort:\"J~áñ_~Féb_~Már_~Ápr_~Máý_~Júñ_~Júl_~Áúg_~Sép_~Óct_~Ñóv_~Déc\".split(\"_\"),weekdaysMin:\"S~ú_Mó~_Tú_~Wé_T~h_Fr~_Sá\".split(\"_\"),ordinal:function(e){return e},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY HH:mm\",LLLL:\"dddd, D MMMM YYYY HH:mm\"},relativeTime:{future:\"í~ñ %s\",past:\"%s á~gó\",s:\"á ~féw ~sécó~ñds\",m:\"á ~míñ~úté\",mm:\"%d m~íñú~tés\",h:\"á~ñ hó~úr\",hh:\"%d h~óúrs\",d:\"á ~dáý\",dd:\"%d d~áýs\",M:\"á ~móñ~th\",MM:\"%d m~óñt~hs\",y:\"á ~ýéár\",yy:\"%d ý~éárs\"}};return _.default.locale(d,null,!0),d}));"], "sourceRoot": ""}