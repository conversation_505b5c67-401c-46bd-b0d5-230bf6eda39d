{"version": 3, "sources": ["webpack:///./src/ui/src/components/HeaderFooterControlsOverlay/HeaderFooterControlsBar/HeaderFooterControlsBar.scss?ba6e", "webpack:///./src/ui/src/components/HeaderFooterControlsOverlay/HeaderFooterControlsBar/HeaderFooterControlsBar.scss", "webpack:///./src/ui/src/components/HeaderFooterControlsOverlay/HeaderFooterControlsOverlay.scss?3684", "webpack:///./src/ui/src/components/HeaderFooterControlsOverlay/HeaderFooterControlsOverlay.scss", "webpack:///./src/ui/src/components/HeaderFooterControlsOverlay/HeaderFooterControlsBar/HeaderFooterControlsBar.js", "webpack:///./src/ui/src/components/HeaderFooterControlsOverlay/HeaderFooterControlsBar/index.js", "webpack:///./src/ui/src/components/HeaderFooterControlsOverlay/HeaderFooterControlsOverlay.js", "webpack:///./src/ui/src/components/HeaderFooterControlsOverlay/index.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "propTypes", "type", "PropTypes", "oneOf", "pageNumber", "number", "isActive", "bool", "HeaderFooterControlsBar", "t", "useTranslation", "dispatch", "useDispatch", "blockerRef", "useRef", "dropdownId", "barId", "barClassName", "classNames", "useEffect", "stopPropagation", "event", "eventType", "current", "addEventListener", "removeEventListener", "dropdownItems", "label", "key", "icon", "onClick", "actions", "openElement", "DataElements", "HEADER_FOOTER_OPTIONS_MODAL", "core", "getOfficeEditor", "removeHeaders", "removeFooters", "onClickItem", "itemKey", "item", "find", "className", "id", "ref", "Dropdown", "width", "renderItem", "Icon", "glyph", "<PERSON><PERSON><PERSON>", "items", "displayButton", "isOpen", "ActionButton", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ariaControls", "ariaExpanded", "img", "stopPropagationOnMouseDown", "React", "memo", "visiblePages", "arrayOf", "isHeaderControlsActive", "isFooterControlsActive", "HeaderFooterControlsOverlay", "portals", "map", "pageSection", "getRootNode", "getElementById", "createPortal"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAqE,IAKxFR,KAAK,CAACnB,EAAOC,EAAI,2iGAA8iG,KAGvkG0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAIhC,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEzB5B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,kGAAmG,M,8uBCJ5H,8lGAAAA,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,20BAAAA,GAAA,gEAAAA,GAAA,0JAAAA,EAAA,6FAAAA,GAAA,mIAAAA,IAAA,4SAAAA,IAAA,2OAAAA,EAAA,iBAAAA,EAAA,EAAAA,IAAA,EAAAA,GAAA,EAAAA,GAAA,SAaA,IAAM4B,EAAY,CAChBC,KAAMC,IAAUC,MAAM,CAAC,SAAU,WACjCC,WAAYF,IAAUG,OACtBC,SAAUJ,IAAUK,MAGhBC,EAA0B,SAAH,GAAuC,MAAjCP,EAAI,EAAJA,KAAMG,EAAU,EAAVA,WAAYE,EAAQ,EAARA,SAC5CG,EAAqB,EAAhBC,cAAgB,GAApB,GACFC,EAAWC,cAEXC,EAAaC,mBACbC,EAAa,GAAH,OAAMd,EAAI,6BAAqBG,GACzCY,EAAQ,GAAH,OAAMf,EAAI,oBAAYG,GAC3Ba,EAAeC,KACU,EADA,GAC7B,yBAAyB,GAAI,UACzBjB,EAAI,aAAa,GAAI,IACzB,SAAUK,GAAQ,IAIpBa,qBAAU,WAER,IAAMC,EAAkB,SAACC,GACvBA,EAAMD,mBAKR,MAHA,CAAC,QAAS,YAAa,UAAW,YAAa,aAAc,cAAchC,SAAQ,SAACkC,GAAc,MAC9E,QAAlB,EAAAT,EAAWU,eAAO,OAAlB,EAAoBC,iBAAiBF,EAAWF,MAE3C,WACL,CAAC,QAAS,YAAa,UAAW,YAAa,aAAc,cAAchC,SAAQ,SAACkC,GAAc,MAC9E,QAAlB,EAAAT,EAAWU,eAAO,OAAlB,EAAoBE,oBAAoBH,EAAWF,SAGtD,IAEH,IAwBMM,EAAgB,CACpB,CACEC,MAAOlB,EAAE,4BACTmB,IAAK,eACLC,KAAM,eACNC,QA7B2B,WAC7BnB,EAASoB,IAAQC,YAAYC,IAAaC,gCA8B1C,CACEP,MAA2BlB,EAAX,WAATR,EAAsB,4BAAiC,6BAC9D2B,IAAK,UAAF,OAAY3B,GACf4B,KAAM,iBACNC,QA/BoC,WACtC,MAAa,WAAT7B,EACKkC,IAAKC,kBAAkBC,cAAcjC,GAEjC,WAATH,EACKkC,IAAKC,kBAAkBE,cAAclC,QAD9C,KA+BImC,EAAW,eAvFnB,EAuFmB,GAvFnB,EAuFmB,UAAG,WAAOC,GAAO,uEAC+B,OAAzDC,EAAOf,EAAcgB,MAAK,SAACD,GAAI,OAAKA,EAAKb,MAAQY,KAAQ,SACzDC,aAAI,EAAJA,EAAMX,UAAS,0CAzFzB,+KA0FG,gBAHgB,sCAiBjB,OACE,yBAAKa,UAAW1B,EAAc2B,GAAI5B,GAChC,yBAAK2B,UAAU,iBAAiBE,IAAKhC,IACrC,yBAAK8B,UAAU,SAASlC,EAAE,gBAAD,OAAiBR,KAE1C,kBAAC6C,EAAA,EAAQ,CACPC,MAAM,OACNH,GAAI7B,EACJiC,WAnDqB,SAACP,GAAI,OAC9B,oCACE,kBAACQ,EAAA,EAAI,CAACC,MAAOT,EAAKZ,KAAMc,UAAU,wBAClC,yBAAKA,UAAU,2BACb,yBAAKA,UAAU,wBAAwBlC,EAAEgC,EAAKd,SAElC,iBAAbc,EAAKb,KAA0B,yBAAKe,UAAU,cA8C7CA,UAAU,6BACVQ,OAAQ,SAACV,GAAI,OAAKA,EAAKb,KACvBwB,MAAO1B,EACPa,YAAaA,EACbc,cAzBuB,SAACC,GAAM,OAClC,kBAACC,EAAA,EAAY,CACXZ,UAAU,iBACVa,eAAgBxC,EAChByC,aAAY,UAAK1C,EAAU,aAC3B2C,aAAcJ,EACdK,IAAK,6BACLhC,MAAOlB,EAAE,wBACTH,SAAUgD,KAkBRM,4BAA4B,MAMpCpD,EAAwBR,UAAYA,EAErB6D,IC7HArD,ED6HAqD,IAAMC,KAAKtD,GEvHpBR,G,QAAY,CAChB+D,aAAc7D,IAAU8D,QAAQ9D,IAAUG,QAC1C4D,uBAAwB/D,IAAUK,KAClC2D,uBAAwBhE,IAAUK,OAG9B4D,EAA8B,SAAH,GAAyE,IAAnEJ,EAAY,EAAZA,aAAcE,EAAsB,EAAtBA,uBAAwBC,EAAsB,EAAtBA,uBAErEE,EAAUL,EAAaM,KAAI,SAACjE,GAChC,IAAMkE,EAAcC,cAAcC,eAAe,cAAD,OAAepE,IAC/D,OAAKkE,EAGEG,uBACL,yBAAK7C,IAAKxB,EAAYuC,UAAU,+BAC9B,kBAAC,EAAuB,CAAC1C,KAAK,SAASG,WAAYA,EAAYE,SAAU2D,IACzE,kBAAC,EAAuB,CAAChE,KAAK,SAASG,WAAYA,EAAYE,SAAU4D,KAE3EI,GAPO,QAWX,OACE,oCACGF,IAKPD,EAA4BnE,UAAYA,EAEzBmE,QCrCAA", "file": "chunks/chunk.51.js", "sourcesContent": ["var api = require(\"!../../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../../node_modules/css-loader/index.js!../../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../../node_modules/sass-loader/dist/cjs.js!./HeaderFooterControlsBar.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.header-footer-edit-ui{position:absolute;height:46px;width:100%;align-items:center;display:none}.header-footer-edit-ui.header-edit-ui{top:143px}.header-footer-edit-ui.footer-edit-ui{bottom:143px}.header-footer-edit-ui.active{display:flex}.header-footer-edit-ui .box-shadow-div{width:100%;height:100%;overflow:hidden;position:absolute;display:flex;align-items:center}.header-footer-edit-ui .box-shadow-div:after{content:\\\"\\\";position:absolute;width:100%;height:40px;padding:3px 0;background-color:var(--gray-0);box-shadow:0 0 3px 0 var(--gray-7);z-index:-1}.header-footer-edit-ui .label{color:var(--gray-8);font-size:.8125rem;font-weight:700;margin-left:16px}.header-footer-edit-ui .options-dropdown-container{position:absolute;right:16px}.header-footer-edit-ui .options-dropdown-container .options-button{color:var(--blue-5);padding:8px 32px 8px 8px;background:transparent;width:auto;border:none;cursor:pointer}.header-footer-edit-ui .options-dropdown-container .options-button .Icon{width:16px;height:16px;position:absolute;right:9.5px;top:0;bottom:0;margin:auto;color:var(--blue-5)}.header-footer-edit-ui .options-dropdown-container .options-button.active,.header-footer-edit-ui .options-dropdown-container .options-button.active .Icon,.header-footer-edit-ui .options-dropdown-container .options-button:hover,.header-footer-edit-ui .options-dropdown-container .options-button:hover .Icon{color:var(--blue-6)}.header-footer-edit-ui .options-dropdown-container .options-button.active .Icon{transform:rotate(180deg)}.header-footer-edit-ui .options-dropdown-container .Dropdown__items{padding:4px 0;min-width:157px}.header-footer-edit-ui .options-dropdown-container .Dropdown__items .Dropdown__item{height:32px;padding:0 10px 0 40px;position:relative;width:auto}.header-footer-edit-ui .options-dropdown-container .Dropdown__items .Dropdown__item:first-child{margin-bottom:9px}.header-footer-edit-ui .options-dropdown-container .Dropdown__items .Dropdown__item .Icon{position:absolute;left:10px;fill:var(--gray-7);top:0;bottom:0;margin:auto}.header-footer-edit-ui .options-dropdown-container .Dropdown__items .Divider{flex-basis:100%;width:100%;height:1px;margin:0;position:absolute;bottom:-5px;left:0}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./HeaderFooterControlsOverlay.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".HeaderFooterControlsOverlay{position:absolute;top:0;left:0;width:100%;height:100%;z-index:501}\", \"\"]);\n\n// exports\n", "import React, { useEffect, useRef } from 'react';\nimport { useDispatch } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\nimport Icon from 'components/Icon';\nimport ActionButton from 'components/ActionButton';\nimport PropTypes from 'prop-types';\nimport actions from 'actions';\nimport DataElements from 'constants/dataElement';\nimport Dropdown from 'components/Dropdown';\nimport classNames from 'classnames';\nimport core from 'core';\n\nimport './HeaderFooterControlsBar.scss';\n\nconst propTypes = {\n  type: PropTypes.oneOf(['header', 'footer']),\n  pageNumber: PropTypes.number,\n  isActive: PropTypes.bool,\n};\n\nconst HeaderFooterControlsBar = ({ type, pageNumber, isActive }) => {\n  const [t] = useTranslation();\n  const dispatch = useDispatch();\n\n  const blockerRef = useRef();\n  const dropdownId = `${type}-options-dropdown-${pageNumber}`;\n  const barId = `${type}-edit-ui-${pageNumber}`;\n  const barClassName = classNames({\n    'header-footer-edit-ui': true,\n    [`${type}-edit-ui`]: true,\n    'active': isActive\n  });\n\n\n  useEffect(() => {\n    // This stops the cursor from moving when the user clicks on the bar\n    const stopPropagation = (event) => {\n      event.stopPropagation();\n    };\n    ['click', 'mousedown', 'mouseup', 'mousemove', 'mouseenter', 'mouseleave'].forEach((eventType) => {\n      blockerRef.current?.addEventListener(eventType, stopPropagation);\n    });\n    return () => {\n      ['click', 'mousedown', 'mouseup', 'mousemove', 'mouseenter', 'mouseleave'].forEach((eventType) => {\n        blockerRef.current?.removeEventListener(eventType, stopPropagation);\n      });\n    };\n  }, []);\n\n  const handlePageOptionsClick = () => {\n    dispatch(actions.openElement(DataElements.HEADER_FOOTER_OPTIONS_MODAL));\n  };\n\n  const handleRemoveHeaderFooterOnClick = () => {\n    if (type === 'header') {\n      return core.getOfficeEditor().removeHeaders(pageNumber);\n    }\n    if (type === 'footer') {\n      return core.getOfficeEditor().removeFooters(pageNumber);\n    }\n  };\n\n  const renderDropdownItem = (item) => (\n    <>\n      <Icon glyph={item.icon} className='Dropdown__item-icon' />\n      <div className='Dropdown__item-vertical'>\n        <div className='Dropdown__item-label'>{t(item.label)}</div>\n      </div>\n      {item.key === 'page-options' && <div className='Divider'></div>}\n    </>\n  );\n\n\n  const dropdownItems = [\n    {\n      label: t('officeEditor.pageOptions'),\n      key: 'page-options',\n      icon: 'ic-edit-page',\n      onClick: handlePageOptionsClick,\n    },\n    {\n      label: type === 'header' ? t('officeEditor.removeHeader') : t('officeEditor.removeFooter'),\n      key: `remove-${type}`,\n      icon: 'ic-delete-page',\n      onClick: handleRemoveHeaderFooterOnClick,\n    },\n  ];\n\n  const onClickItem = async (itemKey) => {\n    const item = dropdownItems.find((item) => item.key === itemKey);\n    await item?.onClick();\n  };\n\n  const renderDropdownButton = (isOpen) => (\n    <ActionButton\n      className='options-button'\n      ariaLabelledby={barId}\n      ariaControls={`${dropdownId}-dropdown`}\n      ariaExpanded={isOpen}\n      img={'ic_chevron_down_black_24px'}\n      label={t('officeEditor.options')}\n      isActive={isOpen}\n    />\n  );\n\n  return (\n    <div className={barClassName} id={barId}>\n      <div className='box-shadow-div' ref={blockerRef}></div>\n      <div className='label'>{t(`officeEditor.${type}`)}</div>\n\n      <Dropdown\n        width='auto'\n        id={dropdownId}\n        renderItem={renderDropdownItem}\n        className='options-dropdown-container'\n        getKey={(item) => item.key}\n        items={dropdownItems}\n        onClickItem={onClickItem}\n        displayButton={renderDropdownButton}\n        stopPropagationOnMouseDown={true}\n      />\n    </div>\n  );\n};\n\nHeaderFooterControlsBar.propTypes = propTypes;\n\nexport default React.memo(HeaderFooterControlsBar);\n", "import HeaderFooterControlsBar from './HeaderFooterControlsBar';\n\nexport default HeaderFooterControlsBar;\n", "import React from 'react';\nimport { createPortal } from 'react-dom';\nimport getRootNode from 'src/helpers/getRootNode';\nimport HeaderFooterControlsBar from './HeaderFooterControlsBar';\nimport PropTypes from 'prop-types';\n\nimport './HeaderFooterControlsOverlay.scss';\n\nconst propTypes = {\n  visiblePages: PropTypes.arrayOf(PropTypes.number),\n  isHeaderControlsActive: PropTypes.bool,\n  isFooterControlsActive: PropTypes.bool,\n};\n\nconst HeaderFooterControlsOverlay = ({ visiblePages, isHeaderControlsActive, isFooterControlsActive }) => {\n\n  const portals = visiblePages.map((pageNumber) => {\n    const pageSection = getRootNode().getElementById(`pageSection${pageNumber}`);\n    if (!pageSection) {\n      return null;\n    }\n    return createPortal(\n      <div key={pageNumber} className='HeaderFooterControlsOverlay'>\n        <HeaderFooterControlsBar type='header' pageNumber={pageNumber} isActive={isHeaderControlsActive} />\n        <HeaderFooterControlsBar type='footer' pageNumber={pageNumber} isActive={isFooterControlsActive} />\n      </div>,\n      pageSection\n    );\n  });\n\n  return (\n    <>\n      {portals}\n    </>\n  );\n};\n\nHeaderFooterControlsOverlay.propTypes = propTypes;\n\nexport default HeaderFooterControlsOverlay;\n", "import HeaderFooterControlsOverlay from './HeaderFooterControlsOverlay';\n\nexport default HeaderFooterControlsOverlay;\n"], "sourceRoot": ""}