{"version": 3, "sources": ["webpack:///./src/ui/src/components/DimensionInput/DimensionInput.scss?d569", "webpack:///./src/ui/src/components/DimensionInput/DimensionInput.scss", "webpack:///./src/ui/src/components/InsertPageModal/InsertBlankPagePanel/InsertBlankPagePanel.scss?a89f", "webpack:///./src/ui/src/components/InsertPageModal/InsertBlankPagePanel/InsertBlankPagePanel.scss", "webpack:///./src/ui/src/components/InsertPageModal/InsertUploadedPagePanel/InsertUploadedPagePanel.scss?82b9", "webpack:///./src/ui/src/components/InsertPageModal/InsertUploadedPagePanel/InsertUploadedPagePanel.scss", "webpack:///./src/ui/src/components/InsertPageModal/InsertPageModal.scss?b398", "webpack:///./src/ui/src/components/InsertPageModal/InsertPageModal.scss", "webpack:///./src/ui/src/components/InsertPageModal/InsertBlankPagePanel/IncrementNumberInput.js", "webpack:///./src/ui/src/components/DimensionInput/DimensionInput.js", "webpack:///./src/ui/src/components/DimensionInput/index.js", "webpack:///./src/ui/src/components/InsertPageModal/InsertBlankPagePanel/InsertBlankPagePanel.js", "webpack:///./src/ui/src/components/InsertPageModal/InsertBlankPagePanel/index.js", "webpack:///./src/ui/src/components/InsertPageModal/InsertUploadedPagePanel/InsertUploadedPagePanel.js", "webpack:///./src/ui/src/components/InsertPageModal/InsertUploadedPagePanel/InsertUploadedPagePanelContainer.js", "webpack:///./src/ui/src/components/InsertPageModal/InsertUploadedPagePanel/index.js", "webpack:///./src/ui/src/components/InsertPageModal/InsertPageModal.js", "webpack:///./src/ui/src/components/InsertPageModal/InsertPageModalContainer.js", "webpack:///./src/ui/src/components/InsertPageModal/index.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "IncrementNumberInput", "id", "className", "min", "onChange", "value", "<PERSON><PERSON><PERSON><PERSON>", "useState", "number", "setNumber", "classNames", "incrementNumberInput", "Input", "type", "e", "parseInt", "target", "onBlur", "inputValue", "onClick", "Icon", "glyph", "newNumber", "propTypes", "PropTypes", "string", "func", "isRequired", "bool", "DimensionInput", "label", "initialValue", "unit", "max<PERSON><PERSON><PERSON>", "disabled", "setValue", "dimensionInput", "step", "style", "width", "input", "toString", "decimalSize", "isIE11", "includes", "resizeInput", "InsertBlankPagePanel", "insertNewPageBelow", "insertNewPageIndexes", "numberOfBlankPagesToInsert", "pageNumberError", "setInsertNewPageBelow", "setInsertNewPageIndexes", "setNumberOfBlankPagesToInsert", "setInsertPageHeight", "setInsertPageWidth", "setPageNumberError", "loadedDocumentPageCount", "presetNewPageDimensions", "useSelector", "state", "selectors", "getPresetNewPageDimensions", "t", "useTranslation", "presetPageDimensions", "Object", "keys", "pagePlacementOptions", "supportedUnits", "unitConversions", "selectedPageDimensions", "setSelectedPageDimensions", "openCustomDimensions", "setOpenCustomDimensions", "getOwnPropertyNames", "units", "setUnits", "customWidth", "setCustomWidth", "height", "customHeight", "setCustomHeight", "useEffect", "handlePageNumbersChanged", "pageNumbers", "Choice", "name", "checked", "radio", "htmlFor", "PageNumberInput", "selectedPageNumbers", "pageCount", "onSelectedPageNumbersChange", "onBlurHandler", "amount", "Dropdown", "labelledById", "dataElement", "currentSelectionKey", "onClickItem", "dimensions", "items", "visibility", "display", "array", "InsertUploadedPagePanel", "React", "forwardRef", "ref", "sourceDocument", "closeModal", "clearLoadedFile", "insertPages", "closeModalWarning", "selectedThumbnails", "setSelectedThumbnails", "isLoading", "setIsLoading", "pageNumberToInsertAt", "setPageNumberToInsertAt", "insertAbove", "setInsertAbove", "customizableUI", "getFeatureFlags", "getPageCount", "selectedPages", "getSelectedPages", "reduce", "currentPage", "onInsertChoiceChange", "onMouseDown", "stopPropagation", "<PERSON><PERSON>", "img", "title", "onError", "pageNumber", "PageThumbnailsGrid", "onThumbnailSelected", "undefined", "onfileLoa<PERSON><PERSON><PERSON><PERSON>", "isFileSelected", "insertAtPage", "insertBeforeThisPage", "displayName", "InsertUploadedPagePanelContainer", "props", "dispatch", "useDispatch", "exitPageInsertionWarning", "loadAsPDF", "InsertPageModal", "getSelectedThumbnailPageIndexes", "getCurrentPage", "getSelectedTab", "DataElements", "INSERT_PAGE_MODAL", "selectedPageIndexes", "selectedTab", "selectedDoc", "setSelectedDoc", "insertPageHeight", "insertPageWidth", "map", "isUploadPagePanelActive", "insertBlankPageProps", "actions", "closeElement", "apply", "insertBelow", "page", "index", "fileProcessedHandler", "file", "getInstanceNode", "instance", "Core", "Document", "core", "createDocument", "console", "error", "clearDocument", "data-element", "ModalWrapper", "isOpen", "<PERSON><PERSON><PERSON><PERSON>", "onCloseClick", "swipeToClose", "INSERT_FROM_FILE_TAB", "Tabs", "INSERT_BLANK_PAGE_TAB", "INSERT_BLANK_PAGE_PANEL", "INSERT_FROM_FILE_PANEL", "FilePickerPanel", "onFileProcessed", "InsertPageModalContainer", "isElementDisabled", "isElementOpen", "isDisabled", "getDocumentViewer", "getDocument", "getTotalPages"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEzB5B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,6rBAA8rB,M,qBCLvtB,IAAIL,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAqE,IAKxFR,KAAK,CAACnB,EAAOC,EAAI,m5MAAo5M,KAG76M0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAIhC,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAqE,IAKxFR,KAAK,CAACnB,EAAOC,EAAI,2gUAA4gU,KAGriU0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAIhC,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,4wUAA6wU,KAGtyU0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,2mECJvB,IAAMC,EAAuB,SAAH,GAA2D,IAArDC,EAAE,EAAFA,GAAIC,EAAS,EAATA,UAAWC,EAAG,EAAHA,IAAKC,EAAQ,EAARA,SAAUC,EAAK,EAALA,MAAOC,EAAS,EAATA,UACxB,IAAfC,mBAASF,GAAM,GAApCG,EAAM,KAAEC,EAAS,KA8BxB,OACE,yBAAKP,UAAWQ,IAAW,EAAD,CACxBC,sBAAsB,GACrBT,IAAcA,KAEf,kBAACU,EAAA,EAAK,CAACX,GAAIA,EAAIY,KAAK,SAASV,IAAKA,EAAKC,SAnBtB,SAACU,GACpBV,EAASW,SAASD,EAAEE,OAAOX,QAC3BI,EAAUM,SAASD,EAAEE,OAAOX,SAiBqCA,MAAOG,EAAQF,UAAWA,EAAWW,OAdrF,SAACH,GAClB,IAAII,EAAaH,SAASD,EAAEE,OAAOX,OAC9Ba,IACHA,EAAaH,SAASZ,GACtBC,EAASc,GACTT,EAAUS,OAUV,yBAAKhB,UAAU,qBACb,4BAAQA,UAAU,yBAAyBiB,QAnCzB,WACtBf,EAASI,EAAS,GAClBC,EAAUD,EAAS,KAkCb,kBAACY,EAAA,EAAI,CAAClB,UAAU,WAAWmB,MAAO,qBAEpC,4BAAQnB,UAAU,yBAAyBiB,QAjCzB,WACtB,IAAMG,EAAYd,EAAS,EACvBc,EAAYnB,IAGhBC,EAASkB,GACTb,EAAUa,MA4BJ,kBAACF,EAAA,EAAI,CAAClB,UAAU,aAAamB,MAAO,0BAO9CrB,EAAqBuB,UAAY,CAC/BtB,GAAIuB,IAAUC,OACdvB,UAAWsB,IAAUC,OACrBtB,IAAKqB,IAAUC,OACfrB,SAAUoB,IAAUE,KAAKC,WACzBtB,MAAOmB,IAAUhB,OACjBF,UAAWkB,IAAUI,MAGR5B,Q,8uDCzDf,IAAM6B,EAAiB,SAAH,GAAyF,IAAnF5B,EAAE,EAAFA,GAAIC,EAAS,EAATA,UAAW4B,EAAK,EAALA,MAAOC,EAAY,EAAZA,aAAc3B,EAAQ,EAARA,SAAU4B,EAAI,EAAJA,KAAI,IAAEC,iBAAS,IAAG,KAAE,EAAEC,EAAQ,EAARA,SAC5C,IAAtB3B,mBAASwB,GAAa,GAAzC1B,EAAK,KAAE8B,EAAQ,KA4BtB,OACE,yBAAKjC,UAAWQ,IAAW,GACzB0B,gBAAgB,GACflC,IAAcA,KAEf,2BAAOA,UAAU,yBACd4B,EACD,yBAAK5B,UAAU,6BACb,2BACED,GAAIA,EACJC,UAAU,kBACVW,KAAK,SACLV,IAAI,IACJkC,KAAM,IACNjC,SAxCoB,SAACU,GAC7BqB,EAASrB,EAAEE,OAAOX,OAClBD,EAASU,EAAEE,OAAOX,QAuCV6B,SAAUA,EACVI,MAAO,CAAEC,MAAO,GAAF,OApCJ,SAACC,GACnB,IAAI1D,EAAS0D,EAAMC,WAAW3D,OAC1B4D,EAAc,GAClB,GAAIC,IAAQ,CAEV7D,GADsB,KAEtBmD,GAFsB,KAGtBS,GAHsB,KAUxB,OALIF,EAAMC,WAAWG,SAAS,KAC5B9D,GAAU4D,EAEV5D,GAAU4D,EAER5D,EAASmD,EACJA,EAEFnD,EAmBoB+D,CAAYxC,GAAM,OACrCA,MAAOA,IAET,0BAAMH,UAAU,kBACbG,EAAQ,GAAK2B,OAQ1BH,EAAeN,UAAY,CACzBtB,GAAIuB,IAAUC,OAAOE,WACrBzB,UAAWsB,IAAUC,OACrBK,MAAON,IAAUC,OACjBM,aAAcP,IAAUhB,OAAOmB,WAC/BvB,SAAUoB,IAAUE,KAAKC,WACzBK,KAAMR,IAAUC,OAAOE,WACvBM,UAAWT,IAAUhB,OACrB0B,SAAUV,IAAUI,MAGPC,ICzEAA,EDyEAA,E,uiCE9Df,IAAMiB,EAAuB,SAAH,GAYpB,IAXJC,EAAkB,EAAlBA,mBACAC,EAAoB,EAApBA,qBACAC,EAA0B,EAA1BA,2BACAC,EAAe,EAAfA,gBACAC,EAAqB,EAArBA,sBACAC,EAAuB,EAAvBA,wBACAC,EAA6B,EAA7BA,8BACAC,EAAmB,EAAnBA,oBACAC,EAAkB,EAAlBA,mBACAC,EAAkB,EAAlBA,mBACAC,EAAuB,EAAvBA,wBAEOC,EAEL,EAFgCC,aAAY,SAACC,GAAK,MAAK,CACvDC,IAAUC,2BAA2BF,OACrC,GAF4B,GAIvBG,EAAqB,EAAhBC,cAAgB,GAApB,GAEFC,EAAuBC,OAAOC,KAAKT,GAInCU,EACK,QADLA,EAEK,QAGLC,EAAiB,CACrB,cAAe,IACf,mBAAoB,KACpB,mBAAoB,MAKhBC,EAAkB,CACtB,IAAK,EACL,GAAM,KACN,GAAM,KACN,GANmB,IASwE,IAAjC/D,mBAAS0D,EAAqB,IAAG,GAAtFM,EAAsB,KAAEC,EAAyB,KACe,IAAfjE,oBAAS,GAAM,GAAhEkE,EAAoB,KAAEC,EAAuB,KAC6B,IAAvDnE,mBAAS2D,OAAOS,oBAAoBN,GAAgB,IAAG,GAA1EO,EAAK,KAAEC,EAAQ,KACgF,IAAhEtE,mBAASmD,EAAwBO,EAAqB,IAAI1B,OAAM,GAA/FuC,EAAW,KAAEC,EAAc,KACuE,IAAjExE,mBAASmD,EAAwBO,EAAqB,IAAIe,QAAO,GAAlGC,EAAY,KAAEC,EAAe,KAEpCC,qBAAU,WACR5B,EAhBmB,GAgBAG,EAAwBa,GAAwBhC,OACnEe,EAjBmB,GAiBCI,EAAwBa,GAAwBS,UACnE,IAEH,IAQMI,EAA2B,SAACC,GAC5BA,EAAYvG,OAAS,EACvB0E,EAAmB,MAEnBA,EAAmB,GAAD,OAAIO,EAAE,kCAE1BX,EAAwBiC,IAoC1B,OACE,yBAAKnF,UAAU,2BACb,yBAAKA,UAAU,mBACb,yBAAKA,UAAU,aAAa6D,EAAE,0CAC9B,yBAAK7D,UAAU,WACb,kBAACoF,EAAA,EAAM,CACLxD,MAAOiC,EAAE,kCAAD,OAAmCK,IAC3CmB,KAAK,iBACLnF,SA1D0B,WAClC+C,GAAsB,IA0DdqC,SAAUzC,EACV0C,OAAK,IAEP,kBAACH,EAAA,EAAM,CACLxD,MAAOiC,EAAE,kCAAD,OAAmCK,IAC3CmB,KAAK,iBACLnF,SA7D0B,WAClC+C,GAAsB,IA6DdqC,QAASzC,EACT0C,OAAK,KAGT,yBAAKvF,UAAU,aAAa6D,EAAE,yCAC9B,yBAAK7D,UAAU,+BACb,yBAAKA,UAAU,mBACb,2BAAOA,UAAU,wBAAwBwF,QAAQ,qBAAqB3B,EAAE,yCACtE,0BAAM7D,UAAU,kBACb6D,EAAE,uCAAuC,KAAGN,EAAyB,IACrEM,EAAE,yCAGP,kBAAC4B,EAAA,EAAe,CACd1F,GAAG,oBACH2F,oBAAqB5C,EACrB6C,UAAWpC,EACXqC,4BAA6BV,EAC7BW,cAAeX,EACflC,gBAAiBA,KAGrB,yBAAKhD,UAAU,mBACb,2BAAOwF,QAAQ,sBAAsB3B,EAAE,yCACvC,kBAAC,EAAoB,CACnB9D,GAAG,qBACHY,KAAK,SACLV,IAAI,IACJC,SA7EuB,SAAC4F,GAClC3C,EAA8B2C,IA6EpB3F,MAAO4C,EACP3C,WAAS,MAIf,yBAAKJ,UAAU,aAAa6D,EAAE,0CAC9B,yBAAK7D,UAAU,mCACb,yBAAKA,UAAU,mBACb,2BAAOD,GAAG,kCAAkCyF,QAAQ,eAAe3B,EAAE,6CACrE,kBAACkC,EAAA,EAAQ,CACPhG,GAAG,cACHiG,aAAa,kCACbC,YAAY,iBACZC,oBAAqB7B,EACrB8B,YAlFwB,SAACC,GA5DN,WA6DzBA,GACF5B,GAAwB,GACxBnB,EAAmBuB,GAlDF,GAkDgCR,EAAgBD,EAAeO,MAChFtB,EAAoB2B,GAnDH,GAmDkCX,EAAgBD,EAAeO,QAElFrB,EArDiB,GAqDEG,EAAwB4C,GAAY/D,OACvDe,EAtDiB,GAsDGI,EAAwB4C,GAAYtB,QACxDN,GAAwB,IAE1BF,EAA0B8B,IAyEhBC,MAAK,UAAMtC,EAAsB,CA/Id,cAkJvB,yBAAK/D,UAAU,kBAAkBoC,MAAO,CAAEkE,WAAY/B,EAAuB,UAAY,WACvF,2BAAOxE,GAAG,sCAAsCyF,QAAQ,sBAAsB3B,EAAE,yCAChF,kBAACkC,EAAA,EAAQ,CACPhG,GAAG,qBACHiG,aAAa,sCACbC,YAAY,eACZC,oBAAqBxB,EACrByB,YAnGe,SAACzB,GAC1BC,EAASD,GACTrB,EAAmBuB,GA3CA,GA2C8BR,EAAgBD,EAAeO,MAChFtB,EAAoB2B,GA5CD,GA4CgCX,EAAgBD,EAAeO,OAiGxE2B,MAAOrC,OAAOC,KAAKE,OAIzB,yBAAKnE,UAAU,UAAUoC,MAAO,CAAEmE,QAAShC,EAAuB,OAAS,SACzE,yBAAKvE,UAAU,mBACb,2BAAOwF,QAAQ,kBAAkB3B,EAAE,mCACnC,kBAAC,EAAc,CAAC9D,GAAG,iBACjBC,UAAU,mBACV6B,aAAc+C,EACd1E,SA3FuB,SAACmC,GAClCwC,EAAexC,GACfgB,EAAmBhB,GA9DA,GA8DwB+B,EAAgBD,EAAeO,OA0FhE5C,KAAMqC,EAAeO,MAGzB,yBAAK1E,UAAU,mBACb,2BAAOwF,QAAQ,mBAAmB3B,EAAE,oCACpC,kBAAC,EAAc,CAAC9D,GAAG,kBACjBC,UAAU,oBACV6B,aAAckD,EACd7E,SA/FwB,SAAC4E,GACnCE,EAAgBF,GAChB1B,EAAoB0B,GAnED,GAmE0BV,EAAgBD,EAAeO,OA8FlE5C,KAAMqC,EAAeO,UASnC9B,EAAqBvB,UAAY,CAC/BwB,mBAAoBvB,IAAUI,KAC9BoB,qBAAsBxB,IAAUkF,MAChCzD,2BAA4BzB,IAAUhB,OACtC0C,gBAAiB1B,IAAUC,OAC3B0B,sBAAuB3B,IAAUE,KACjC0B,wBAAyB5B,IAAUE,KACnC2B,8BAA+B7B,IAAUE,KACzC4B,oBAAqB9B,IAAUE,KAC/B6B,mBAAoB/B,IAAUE,KAC9B8B,mBAAoBhC,IAAUE,KAC9B+B,wBAAyBjC,IAAUhB,QAGtBsC,ICrOAA,EDqOAA,E,g+DE3Nf,IAAM6D,EAA0BC,IAAMC,YAAW,WAQ9CC,GAAQ,IAPTC,EAAc,EAAdA,eACAC,EAAU,EAAVA,WACAC,EAAe,EAAfA,gBACAC,EAAW,EAAXA,YACAzD,EAAuB,EAAvBA,wBACA0D,EAAiB,EAAjBA,kBAAiB,IACjBnE,4BAAoB,IAAG,GAAC,GAAE,EAEnBe,EAAqB,EAAhBC,cAAgB,GAApB,GACwD,IAAZzD,mBAAS,IAAG,GAAzD6G,EAAkB,KAAEC,EAAqB,KACA,IAAd9G,oBAAS,GAAK,GAAzC+G,EAAS,KAAEC,EAAY,KAC6D,IAAnChH,mBAAS,CAACyC,EAAqB,KAAI,GAApFwE,EAAoB,KAAEC,EAAuB,KACA,IAAdlH,oBAAS,GAAK,GAA7CmH,EAAW,KAAEC,EAAc,KACwB,IAAZpH,mBAAS,IAAG,GAAnD2C,EAAe,KAAEM,EAAkB,KACpCoE,EAAiBjE,aAAY,SAACC,GAAK,aAAqC,QAArC,EAAKC,IAAUgE,gBAAgBjE,UAAM,aAAhC,EAAkCgE,kBAEhFzC,qBAAU,WAGR,IAFA,IAAMU,EAAYkB,EAAee,eAC3BC,EAAgB,GACb3J,EAAI,EAAGA,GAAKyH,EAAWzH,IAC9B2J,EAAc3J,IAAK,EAErBiJ,EAAsBU,KACrB,CAAChB,IAGJ,IASMiB,EAAmB,WAEvB,OADoB9D,OAAOC,KAAKiD,GACba,QAAO,SAACF,EAAeG,GAIxC,OAHId,EAAmBc,IACrBH,EAAczI,KAAKyB,SAASmH,IAEvBH,IACN,KAcCI,EAAuB,WAC3BR,GAAgBD,IA4BlB,OACE,yBAAKxH,UAAU,6BAA6BkI,YAAa,SAACtH,GAAC,OAAKA,EAAEuH,mBAAmBvB,IAAKA,GACxF,yBAAK5G,UAAU,UACb,yBAAKA,UAAU,eACb,kBAACoI,EAAA,EAAM,CACLC,IAAK,kBACLpH,QAAS8F,EACTd,YAAa,2BACbqC,MAAOzE,EAAE,iBAEVA,EAAE,+BAA+B,IAAC,WAAKuD,EAAY,EAAIU,IAAmBlJ,OAAM,MAEnF,kBAACwJ,EAAA,EAAM,CAACpI,UAAU,6BAA6BqI,IAAI,aAAapH,QAtB/C,WACrBgG,KAqB6FqB,MAAM,mBAGjG,yBAAKtI,UAAU,cACb,yBAAKA,UAAU,8BACb,yBAAKA,UAAU,gCACb,0BAAMA,UAAU,uBAAuB6D,EAAE,0CACzC,yBAAK7D,UAAU,uBACb,kBAACoF,EAAA,EAAM,CAACxD,MAAOiC,EAAE,wCAAyC0B,OAAK,EAACF,KAAK,qBAAqBC,QAASkC,EAAatH,SAAU+H,IAC1H,kBAAC7C,EAAA,EAAM,CAACxD,MAAOiC,EAAE,wCAAyC0B,OAAK,EAACF,KAAK,qBAAqBC,SAAUkC,EAAatH,SAAU+H,MAG/H,yBAAKjI,UAAU,wBACb,0BAAMA,UAAU,uBAAuB6D,EAAE,kDACzC,yBAAK7D,UAAU,qBACZ6D,EAAE,wBAAwB,IAC3B,kBAAC4B,EAAA,EAAe,CACdC,oBAAqB4B,EACrB3B,UAAWpC,EACXqC,4BAvEe,SAACT,GACxBA,EAAYvG,OAAS,IACvB0E,EAAmB,MACnBiE,EAAwBpC,KAqEdU,cAAe0B,EACfgB,QAvCgB,SAACC,GACzBA,GACFlF,EAAmB,GAAD,OAAIO,EAAE,2BAA0B,YAAIN,KAsC5CP,gBAAiBA,OAMzB,yBAAKhD,UAAWQ,IAAW,iCAAkC,CAAE4G,YAAW,aAAcM,KACtF,kBAACe,EAAA,EAAkB,CACjBlK,SAAUsI,EACV6B,oBAtGkB,SAACF,QACYG,IAAnCzB,EAAmBsB,GACrBtB,EAAmBsB,IAAc,EAEjCtB,EAAmBsB,IAAetB,EAAmBsB,GAEvDrB,E,+VAAsB,CAAD,GAAMD,KAiGnBA,mBAAoBA,EACpB0B,oBAAqBvB,MAG3B,yBAAKrH,UAAWQ,IAAW,qBAAsB,CAAEqI,gBAAiBzB,KAClE,4BAAQpH,UAAWQ,IAAW,sBAAuB,CAAEwB,SAAUoF,IAAcnG,QAlFvD,WAC5BkG,EAAsB,KAiF6FnF,SAAUoF,GACtHvD,EAAE,uBAEL,kBAACuE,EAAA,EAAM,CACLpI,UAAU,YACViB,QA/EmB,WACzB,IAAI6H,EAAexB,EAAqB,GACxC,GAAIwB,EAAc,CAChB,IAAIC,EAAuBvB,EAAcsB,IAAiBA,EAEtDC,EAAuBxF,IACzBwF,EAAuB,MAGzB/B,EAAYH,EAAgBiB,IAAoBiB,GAGlDjC,KAoEMlF,MAAOiC,EAAE,0BACT7B,SAAwC,IAA9B8F,IAAmBlJ,QAAgBwI,GAA6C,IAAhCE,EAAqB1I,QAAgBoE,SAOzGyD,EAAwBuC,YAAcvC,EACvBA,Q,mOC7Jf,IAAMwC,EAAmCvC,IAAMC,YAAW,SAACuC,EAAOtC,GAEhE,IAAMuC,EAAWC,cACTtC,EAAeoC,EAAfpC,WAER,OAAQ,kBAAC,EAAuB,KAAKoC,EAAK,CAAElC,YAAaA,IAAaC,kBAD5C,WAAH,OAASoC,YAAyBvC,EAAYqC,IACuCvC,IAAKA,QAGnHqC,EAAiCD,YAAc,mCAChCC,ICZAA,EDYAA,E,0ZEbf,gmGAAA/K,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,8YAAAA,GAAA,gEAAAA,GAAA,0JAAAA,EAAA,6FAAAA,GAAA,mIAAAA,IAAA,8SAAAA,IAAA,4OAAAA,EAAA,iBAAAA,EAAA,EAAAA,IAAA,EAAAA,GAAA,EAAAA,GAAA,SAoBA,IAAMC,GAAU,CAAEmL,WAAW,GAiJdC,GA/IS,SAAH,GAAoC,IAA9BhG,EAAuB,EAAvBA,wBAKvB,KAJsDE,aAAY,SAACC,GAAK,MAAK,CAC7EC,IAAU6F,gCAAgC9F,GAC1CC,IAAU8F,eAAe/F,GACzBC,IAAU+F,eAAehG,EAAOiG,IAAaC,uBAC7C,GAJKC,EAAmB,KAAE7B,EAAW,KAAE8B,EAAW,KAMA,KAAdzJ,mBAAS,MAAK,GAA7C0J,EAAW,KAAEC,EAAc,KACiC,KAAf3J,oBAAS,GAAM,GAA5DwC,EAAkB,KAAEI,EAAqB,KACoB,KAAZ5C,mBAAS,IAAG,GAA7DyC,EAAoB,KAAEI,EAAuB,KAC2B,KAAX7C,mBAAS,GAAE,GAAxE0C,EAA0B,KAAEI,EAA6B,KACL,KAAX9C,mBAAS,GAAE,GAApD4J,EAAgB,KAAE7G,EAAmB,KACa,KAAX/C,mBAAS,GAAE,GAAlD6J,EAAe,KAAE7G,EAAkB,KACgB,KAAZhD,mBAAS,IAAG,GAAnD2C,EAAe,KAAEM,EAAkB,KAE1C2B,qBAAU,WACR,IAAME,EAAc0E,EAAoBjL,OAAS,EAAIiL,EAAoBM,KAAI,SAACjM,GAAC,OAAKA,EAAI,KAAK,CAAC8J,GAC9F9E,EAAwBiC,KACvB,CAAC0E,IAEJ,IA0DQO,EACAC,EA3DFlB,EAAWC,cACVvF,EAAqB,GAAhBC,cAAgB,GAApB,GAEFgD,EAAa,WACjBqC,EAASmB,IAAQC,aAAaZ,IAAaC,qBAOvCY,EAAQ,WACZ,GAAI3H,EACF,IADsB,kBAEpB4H,YAAY3H,EAAqBqH,KAAI,SAACO,EAAMC,GAAK,OAAKD,GAAQC,EAAQ,GAAKzM,KAAIgM,EAAiBD,IADzF/L,EAAI,EAAGA,EAAI6E,IAA8B7E,EAAC,UAInD,IADK,kBAEHsJ,YAAY1E,EAAqBqH,KAAI,SAACO,EAAMC,GAAK,OAAKD,GAAQC,EAAQ,GAAKzM,KAAIgM,EAAiBD,IADzF/L,EAAI,EAAGA,EAAI6E,IAA8B7E,EAAC,KAIrD4I,KAKI8D,EAAoB,eApE5B,EAoE4B,GApE5B,EAoE4B,WAAG,WAAOC,GAAI,6EAGlCA,aAAgBC,cAAkBC,SAASC,KAAKC,UAAQ,gBAC1D1M,EAAWsM,EAAK,gDAGGK,IAAKC,eAAeN,EAAM1M,IAAQ,OAAnDI,EAAW,EAAH,uDAER6M,QAAQC,MAAM,2BAA2B,QAG7CrB,EAAezL,GAAU,yDAhF7B,iLAiFG,gBAbyB,sCAepB+M,EAAgB,WACpBtB,EAAe,OAkEjB,OACE,yBAAKhK,UAAU,6BAA6BuL,eAAc5B,IAAaC,kBAAmB1B,YAAa6B,EAtG3E,WAC5BV,YAAyBvC,EAAYqC,IAqGwGrC,GAC3I,kBAAC0E,EAAA,EAAY,CACXlD,MAAOyB,EAAc,KAAOlG,EAAE,yBAC9B4H,QAAQ,EACRC,aAAc5E,EACd6E,aAAc7E,EACd8E,cAAY,GAEX7B,EAtEH,kBAAC,EAAuB,CACtBlD,eAAgBkD,EAChBjD,WAAYA,EACZC,gBAAiBuE,EACjB/H,wBAAyBA,EACzBT,qBAAsBA,KAMpBsH,EAA0BN,IAAgBH,IAAakC,qBACvDxB,EAAuB,CAC3BxH,qBACAC,uBACAC,6BACAC,kBACAC,wBACAC,0BACAC,gCACAC,sBACAC,qBACAC,qBACAC,2BAGA,yBAAKvD,UAAU,iBAAiBiB,QAAS,SAACL,GAAC,OAAKA,EAAEuH,mBAAmBD,YAAa,SAACtH,GAAC,OAAKA,EAAEuH,oBACzF,kBAAC2D,EAAA,EAAI,CAAC9L,UAAU,mBAAmBD,GAAI4J,IAAaC,mBAClD,yBAAK5J,UAAU,yBACb,yBAAKA,UAAU,YACb,kBAAC,IAAG,CAACiG,YAAa0D,IAAaoC,uBAC7B,4BAAQ/L,UAAU,sBAAsB6D,EAAE,gCAE5C,yBAAK7D,UAAU,wBACf,kBAAC,IAAG,CAACiG,YAAa0D,IAAakC,sBAC7B,4BAAQ7L,UAAU,sBAAsB6D,EAAE,mCAIhD,kBAAC,IAAQ,CAACoC,YAAa0D,IAAaqC,yBAClC,kBAAC,EAAyB3B,IAE5B,kBAAC,IAAQ,CAACpE,YAAa0D,IAAasC,wBAClC,yBAAKjM,UAAU,cACb,kBAACkM,EAAA,EAAe,CACdC,gBAAiBvB,OAIzB,yBAAK5K,UAAU,YACf,yBAAKA,UAAU,UACb,kBAACoI,EAAA,EAAM,CACLpI,UAAU,+BACV4B,MAAM,yBACNX,QAASuJ,EACTxI,SAAUkI,GAAmB,GAAKD,GAAoB,GAAKG,GAA2D,IAAhCtH,EAAqBlE,QAAgBoE,U,miCC1IvI,IAceoJ,GAdkB,WAC/B,IAGE,KAH2B3I,aAAY,SAACC,GAAK,MAAK,CAClDC,IAAU0I,kBAAkB3I,EAAOiG,IAAaC,mBAChDjG,IAAU2I,cAAc5I,EAAOiG,IAAaC,uBAC5C,GAHK2C,EAAU,KAAEd,EAAM,KAMzB,IAAKc,GAAcd,EAAQ,CACzB,IACMlI,EADW2H,IAAKsB,oBAAoBC,cACCvB,IAAKwB,gBAAkB,KAClE,OAAO,kBAAC,GAAe,CAACnJ,wBAAyBA,IAEnD,OAAO,MCjBMgG", "file": "chunks/chunk.42.js", "sourcesContent": ["var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./DimensionInput.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".dimension-input-container{display:flex;align-items:center;border:1px solid var(--border);background:var(--component-background);color:var(--text-color);border-radius:4px;width:100%;max-width:80px;min-width:64px;height:28px;padding:1px 2px}.dimension-input-container input::-webkit-inner-spin-button,.dimension-input-container input::-webkit-outer-spin-button{-webkit-appearance:none;margin:0}.dimension-input-container input[type=number]{-moz-appearance:textfield}.dimension-input-container input,.dimension-input-container input:focus{border:0}.dimension-input{border:0;border-radius:0;padding:0;margin-right:0;margin-left:4px;min-width:1ch}.dimension-unit{color:var(--text-color);font-size:13px}\", \"\"]);\n\n// exports\n", "var api = require(\"!../../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../../node_modules/css-loader/index.js!../../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../../node_modules/sass-loader/dist/cjs.js!./InsertBlankPagePanel.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.incrementNumberInput .increment-buttons .increment-arrow-button:hover{cursor:pointer;border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6);background-color:var(--faded-component-background)}.insert-blank-page-panel{width:100%}.insert-blank-page-panel .dimension-input-container{min-width:100%;margin:0;height:32px}.insert-blank-page-panel .dimension-input-container[focus-within]{border:1px solid var(--focus-border)}.insert-blank-page-panel .dimension-input-container:focus-within{border:1px solid var(--focus-border)}.insert-blank-page-panel .subheader{font-size:13px;font-weight:700;margin-bottom:8px}.insert-blank-page-panel .panel-container .section{display:flex;padding-bottom:16px;grid-gap:16px;gap:16px}.insert-blank-page-panel .panel-container .section.extra-space-section{height:98px}.insert-blank-page-panel .panel-container .section.page-dimensions-section,.insert-blank-page-panel .panel-container .section:last-of-type{padding-bottom:0}.insert-blank-page-panel .panel-container .section .input-container{display:flex;flex-direction:column}.insert-blank-page-panel .panel-container .section .input-container label{margin:0;padding-bottom:8px;font-size:13px}.insert-blank-page-panel .panel-container .section .input-container .page-number-input{width:100%;height:32px;margin:0}.insert-blank-page-panel .panel-container .section .input-container .customSelector{margin-left:0}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .insert-blank-page-panel .panel-container .section .input-container .customSelector ul{top:auto;bottom:calc(100% + 4px)}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .insert-blank-page-panel .panel-container .section .input-container .customSelector ul{top:auto;bottom:calc(100% + 4px)}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .insert-blank-page-panel .panel-container .section .input-container .customSelector li:first-child{display:none}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .insert-blank-page-panel .panel-container .section .input-container .customSelector li:first-child{display:none}}.insert-blank-page-panel .panel-container .section .input-container select{height:28px;width:100%}.insert-blank-page-panel .panel-container .section .input-container .Dropdown{height:32px;min-width:150px;width:100%!important}.insert-blank-page-panel .panel-container .section .input-container .Dropdown .arrow{flex:0 1 auto}.insert-blank-page-panel .panel-container .section .input-container .Dropdown .picked-option .picked-option-text{text-align:left}.insert-blank-page-panel .panel-container .section .input-container .Dropdown__items{top:-52px;z-index:80;width:100%}.insert-blank-page-panel .panel-container .section .input-container .page-number-error{margin-top:8px;font-size:13px;color:var(--color-message-error)}.insert-blank-page-panel .panel-container .section .input-container .specify-pages-wrapper{display:flex;justify-content:space-between}.insert-blank-page-panel .panel-container .section .input-container .specify-pages-wrapper .input-sub-text{color:var(--faded-text)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .insert-blank-page-panel .panel-container .section .ui__choice__label,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .insert-blank-page-panel .panel-container .section button,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .insert-blank-page-panel .panel-container .section input{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .insert-blank-page-panel .panel-container .section .ui__choice__label,.App.is-web-component:not(.is-in-desktop-only-mode) .insert-blank-page-panel .panel-container .section button,.App.is-web-component:not(.is-in-desktop-only-mode) .insert-blank-page-panel .panel-container .section input{font-size:13px}}.insert-blank-page-panel .panel-container .section>*{flex:1;margin:8px 0}.incrementNumberInput{border:1px solid var(--border);border-radius:4px;display:flex;height:32px}.incrementNumberInput input[type=number]::-webkit-inner-spin-button,.incrementNumberInput input[type=number]::-webkit-outer-spin-button{-webkit-appearance:none;margin:0}.incrementNumberInput input[type=number]{-moz-appearance:textfield}.incrementNumberInput .ui__input{border:0;height:100%}.incrementNumberInput .ui__input .ui__input__input{width:100%;height:100%;padding:6px;line-height:normal}.incrementNumberInput .ui__input--message-default.ui__input--focused{outline:none;box-shadow:none}.incrementNumberInput .increment-buttons{display:flex;flex-direction:column;grid-gap:2px;gap:2px;justify-content:center;padding:2px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .incrementNumberInput .increment-buttons{display:none}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .incrementNumberInput .increment-buttons{display:none}}.incrementNumberInput .increment-buttons .increment-arrow-button{border:0;border-radius:2px;height:10px;width:20px;display:flex;justify-content:center;align-items:center;line-height:10px;padding:0;background:none;cursor:pointer}.incrementNumberInput .increment-buttons .increment-arrow-button .Icon{height:12px;width:12px}.incrementNumberInput .increment-buttons .increment-arrow-button:active{box-shadow:0 0 1px 0 var(--document-box-shadow)}.incrementNumberInput[focus-within]{border:1px solid var(--focus-border)}.incrementNumberInput:focus-within{border:1px solid var(--focus-border)}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../../node_modules/css-loader/index.js!../../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../../node_modules/sass-loader/dist/cjs.js!./InsertUploadedPagePanel.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.insert-uploaded-page-panel .insert-page-footer .modal-btn.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.insert-uploaded-page-panel .insert-page-footer .modal-btn.disabled span{color:var(--primary-button-text)}.insert-uploaded-page-panel .modal-body .modal-body-thumbnail-container.modular-ui .thumb-card:hover{cursor:pointer;border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6);background-color:var(--faded-component-background)}.insert-uploaded-page-panel{overflow-y:auto;max-height:100%}@media(max-height:500px){.App:not(.is-web-component) .insert-uploaded-page-panel{overflow:auto;max-height:100%}}@container (max-height: 500px){.App.is-web-component .insert-uploaded-page-panel{overflow:auto;max-height:100%}}.insert-uploaded-page-panel{box-sizing:border-box;display:flex;flex-direction:column;border-radius:4px;box-shadow:0 0 3px 0 var(--document-box-shadow);width:791px;background:var(--component-background)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .insert-uploaded-page-panel{width:100%;height:100vh;left:0;bottom:0}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .insert-uploaded-page-panel{width:100%;height:100vh;left:0;bottom:0}}.insert-uploaded-page-panel .header{display:flex;justify-content:space-between;width:100%;font-size:16px;line-height:24px;color:var(--gray-9);font-weight:700;box-shadow:inset 0 -1px 0 var(--divider);padding:20px 16px}.insert-uploaded-page-panel .header .left-header{display:flex;align-items:center;grid-gap:4px;gap:4px}.insert-uploaded-page-panel .header .Button:hover{background:var(--view-header-button-hover);border-radius:4px}.insert-uploaded-page-panel .insert-page-divider{border-top:1px solid var(--divider);margin:18px -16px 16px;width:inherit}.insert-uploaded-page-panel .insert-blank-page-controls{padding-bottom:32px;display:flex;grid-gap:48px;gap:48px;height:90px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .insert-uploaded-page-panel .insert-blank-page-controls{height:auto;flex-direction:column;grid-gap:20px;gap:20px}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .insert-uploaded-page-panel .insert-blank-page-controls input,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .insert-uploaded-page-panel .insert-blank-page-controls label{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .insert-uploaded-page-panel .insert-blank-page-controls{height:auto;flex-direction:column;grid-gap:20px;gap:20px}.App.is-web-component:not(.is-in-desktop-only-mode) .insert-uploaded-page-panel .insert-blank-page-controls input,.App.is-web-component:not(.is-in-desktop-only-mode) .insert-uploaded-page-panel .insert-blank-page-controls label{font-size:13px}}.insert-uploaded-page-panel .insert-blank-page-controls .insert-page-titles{font-size:13px;font-weight:700;color:var(--text-color)}.insert-uploaded-page-panel .insert-blank-page-controls .insert-page-location-options{display:flex;flex-direction:column}.insert-uploaded-page-panel .insert-blank-page-controls .insert-page-location-options .insert-page-options{display:flex;flex-direction:row;align-items:baseline;grid-gap:20px;gap:20px;padding-top:11px}.insert-uploaded-page-panel .insert-blank-page-controls .insert-page-location{display:flex;flex-direction:column}.insert-uploaded-page-panel .insert-blank-page-controls .insert-page-location .insert-page-input{display:flex;padding-top:8px;grid-gap:8px;gap:8px;align-items:baseline;font-size:13px;font-weight:400;-webkit-font-smoothing:auto}.insert-uploaded-page-panel .insert-blank-page-controls .insert-page-location .page-number-error{color:var(--color-message-error)}.insert-uploaded-page-panel .modal-body{height:unset;display:flex;flex-direction:column;box-shadow:inset 0 -1px 0 var(--divider);padding:16px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .insert-uploaded-page-panel .modal-body{flex:1}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .insert-uploaded-page-panel .modal-body{flex:1}}.insert-uploaded-page-panel .modal-body .modal-body-thumbnail-container{height:409px;width:100%;padding:16px 0 16px 16px;border-radius:4px;overflow:auto;background-color:var(--gray-2);display:flex;flex-wrap:wrap;grid-gap:16px;gap:16px}.insert-uploaded-page-panel .modal-body .modal-body-thumbnail-container.isLoading{display:flex;justify-content:center;align-items:center}.insert-uploaded-page-panel .modal-body .modal-body-thumbnail-container.modular-ui .thumb-card{border:none;box-shadow:inset 0 0 0 1px var(--lighter-border)}.insert-uploaded-page-panel .modal-body .modal-body-thumbnail-container.modular-ui .thumb-card:hover{background:var(--gray-1)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .insert-uploaded-page-panel .modal-body .modal-body-thumbnail-container{flex-direction:row;flex:1;grid-gap:16px;gap:16px;padding:16px;max-height:calc(100vh - 328px)}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .insert-uploaded-page-panel .modal-body .modal-body-thumbnail-container .thumb-card{flex:0 0 calc(50% - 8px);box-sizing:border-box}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .insert-uploaded-page-panel .modal-body .modal-body-thumbnail-container .thumb-card-img{height:110px;width:83px}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .insert-uploaded-page-panel .modal-body .modal-body-thumbnail-container .thumb-body{align-items:center}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .insert-uploaded-page-panel .modal-body .modal-body-thumbnail-container .thumb-image{width:100%}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .insert-uploaded-page-panel .modal-body .modal-body-thumbnail-container .thumb-checkbox{position:absolute;top:2px;right:2px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .insert-uploaded-page-panel .modal-body .modal-body-thumbnail-container{flex-direction:row;flex:1;grid-gap:16px;gap:16px;padding:16px;max-height:calc(100vh - 328px)}.App.is-web-component:not(.is-in-desktop-only-mode) .insert-uploaded-page-panel .modal-body .modal-body-thumbnail-container .thumb-card{flex:0 0 calc(50% - 8px);box-sizing:border-box}.App.is-web-component:not(.is-in-desktop-only-mode) .insert-uploaded-page-panel .modal-body .modal-body-thumbnail-container .thumb-card-img{height:110px;width:83px}.App.is-web-component:not(.is-in-desktop-only-mode) .insert-uploaded-page-panel .modal-body .modal-body-thumbnail-container .thumb-body{align-items:center}.App.is-web-component:not(.is-in-desktop-only-mode) .insert-uploaded-page-panel .modal-body .modal-body-thumbnail-container .thumb-image{width:100%}.App.is-web-component:not(.is-in-desktop-only-mode) .insert-uploaded-page-panel .modal-body .modal-body-thumbnail-container .thumb-checkbox{position:absolute;top:2px;right:2px}}.insert-uploaded-page-panel .insert-page-footer{display:flex;padding:16px;justify-content:flex-end}.insert-uploaded-page-panel .insert-page-footer.isFileSelected{justify-content:space-between}.insert-uploaded-page-panel .insert-page-footer .deselect-thumbnails{border:none;background-color:transparent;color:var(--secondary-button-text);padding:8px 16px;height:32px;display:flex;align-items:center;justify-content:center;cursor:pointer}:host(:not([data-tabbing=true])) .insert-uploaded-page-panel .insert-page-footer .deselect-thumbnails,html:not([data-tabbing=true]) .insert-uploaded-page-panel .insert-page-footer .deselect-thumbnails{outline:none}.insert-uploaded-page-panel .insert-page-footer .deselect-thumbnails:hover{color:var(--secondary-button-hover)}.insert-uploaded-page-panel .insert-page-footer .deselect-thumbnails.disabled{visibility:hidden}.insert-uploaded-page-panel .insert-page-footer .modal-btn{border:none;background-color:transparent;border-radius:4px;padding:8px 16px;height:32px;width:100px;display:flex;align-items:center;justify-content:center;position:relative;font-weight:400;cursor:pointer}:host(:not([data-tabbing=true])) .insert-uploaded-page-panel .insert-page-footer .modal-btn,html:not([data-tabbing=true]) .insert-uploaded-page-panel .insert-page-footer .modal-btn{outline:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .insert-uploaded-page-panel .insert-page-footer .modal-btn{height:32px;width:100px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .insert-uploaded-page-panel .insert-page-footer .modal-btn{height:32px;width:100px}}.insert-uploaded-page-panel .insert-page-footer .modal-btn:not(:disabled):not(.disabled){background:var(--primary-button);color:var(--primary-button-text)}.insert-uploaded-page-panel .insert-page-footer .modal-btn:not(:disabled):not(.disabled):hover{background:var(--primary-button-hover)}.insert-uploaded-page-panel .insert-page-footer .modal-btn.disabled{border:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .insert-uploaded-page-panel .insert-page-footer button{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .insert-uploaded-page-panel .insert-page-footer button{font-size:13px}}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./InsertPageModal.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".open.InsertPageModal{visibility:visible}.closed.InsertPageModal{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.InsertPageModal .footer .modal-button.confirm:hover,.InsertPageModal .modal-container .footer .Button.insertPageModalConfirmButton:enabled:hover{background:var(--primary-button-hover);border-color:var(--primary-button-hover);color:var(--gray-0)}.InsertPageModal .footer .modal-button.confirm{background:var(--primary-button);border-color:var(--primary-button);color:var(--primary-button-text)}.InsertPageModal .footer .modal-button.confirm.disabled,.InsertPageModal .modal-container .footer .Button.insertPageModalConfirmButton.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.InsertPageModal .footer .modal-button.confirm.disabled span,.InsertPageModal .modal-container .footer .Button.insertPageModalConfirmButton.disabled span{color:var(--primary-button-text)}.InsertPageModal .footer .modal-button.cancel:hover,.InsertPageModal .footer .modal-button.secondary-btn-custom:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6)}.InsertPageModal .footer .modal-button.cancel,.InsertPageModal .footer .modal-button.secondary-btn-custom{border:none;box-shadow:inset 0 0 0 1px var(--primary-button);color:var(--primary-button)}.InsertPageModal .footer .modal-button.cancel.disabled,.InsertPageModal .footer .modal-button.secondary-btn-custom.disabled{cursor:default;border:none;box-shadow:inset 0 0 0 1px rgba(43,115,171,.5);color:rgba(43,115,171,.5)}.InsertPageModal .footer .modal-button.cancel.disabled span,.InsertPageModal .footer .modal-button.secondary-btn-custom.disabled span{color:rgba(43,115,171,.5)}.InsertPageModal{position:fixed;left:0;bottom:0;z-index:100;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background:var(--modal-negative-space)}.InsertPageModal .modal-container .wrapper .modal-content{padding:10px}.InsertPageModal .footer{display:flex;flex-direction:row;justify-content:flex-end;width:100%;margin-top:13px}.InsertPageModal .footer.modal-footer{padding:16px;margin:0;border-top:1px solid var(--divider)}.InsertPageModal .footer .modal-button{display:flex;justify-content:center;align-items:center;padding:6px 18px;margin:8px 0 0;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;height:30px;cursor:pointer}.InsertPageModal .footer .modal-button.confirm{margin-left:4px}.InsertPageModal .footer .modal-button.secondary-btn-custom{border-radius:4px;padding:2px 20px 4px;cursor:pointer}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InsertPageModal .footer .modal-button{padding:23px 8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InsertPageModal .footer .modal-button{padding:23px 8px}}.InsertPageModal .swipe-indicator{background:var(--swipe-indicator-bg);border-radius:2px;height:4px;width:38px;position:absolute;top:12px;margin-left:auto;margin-right:auto;left:0;right:0}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InsertPageModal .swipe-indicator{display:none}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .InsertPageModal .swipe-indicator{display:none}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InsertPageModal .swipe-indicator{width:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InsertPageModal .swipe-indicator{width:32px}}.InsertPageModal .modal-container .tab-list{width:100%;height:28px;display:flex;border-radius:4px;color:var(--text-color)}.InsertPageModal .modal-container .tab-list .tab-options-button{text-align:center;vertical-align:middle;line-height:24px;text-overflow:ellipsis;overflow:hidden;white-space:nowrap;flex:1;border-radius:0;cursor:pointer}.InsertPageModal .modal-container .tab-list .tab-options-button:first-child{border-bottom-left-radius:4px;border-top-left-radius:4px}.InsertPageModal .modal-container .tab-list .tab-options-button:last-child{border-bottom-right-radius:4px;border-top-right-radius:4px}.InsertPageModal .modal-container .tab-list .tab-options-button:hover{background:var(--popup-button-hover)}.InsertPageModal .modal-container .tab-list .tab-options-button.selected{cursor:default}.InsertPageModal .modal-container .tab-list .tab-options-button.focus-visible,.InsertPageModal .modal-container .tab-list .tab-options-button:focus-visible{outline:var(--focus-visible-outline)}.InsertPageModal .modal-container .tab-panel{width:100%;display:flex;flex-direction:column;align-items:center}.InsertPageModal .modal-container .tab-panel.focus-visible,.InsertPageModal .modal-container .tab-panel:focus-visible{outline:var(--focus-visible-outline)!important}.InsertPageModal .modal-container{overflow-y:visible;display:flex;flex-direction:column;justify-content:space-between;width:480px;padding:0;border-radius:4px;background:var(--component-background)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InsertPageModal .modal-container{border-radius:0;width:100%}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InsertPageModal .modal-container{border-radius:0;width:100%}}@media(max-height:320px){.App:not(.is-web-component) .InsertPageModal .modal-container{display:grid;height:100%;position:fixed;top:0;grid-template-rows:100px auto 70px;justify-content:normal}}@container (max-height: 320px){.App.is-web-component .InsertPageModal .modal-container{display:grid;height:100%;position:fixed;top:0;grid-template-rows:100px auto 70px;justify-content:normal}}.InsertPageModal .modal-container .tabs-header-container{padding:16px}.InsertPageModal .modal-container .header{margin:0;display:flex;align-items:center;width:100%}.InsertPageModal .modal-container .header p{font-size:16px;font-weight:700;width:89.286%;margin:0 16px 0 0}.InsertPageModal .modal-container .header .insertPageModalCloseButton{position:static;height:32px;width:32px;border-radius:4px}.InsertPageModal .modal-container .header .insertPageModalCloseButton:hover{background:var(--tools-button-hover)}.InsertPageModal .modal-container .header .insertPageModalCloseButton.selected{background:var(--view-header-button-active);cursor:default}.InsertPageModal .modal-container .tab-panel{overflow-y:visible}.InsertPageModal .modal-container .tab-panel .panel-container{padding:0 16px 16px}.InsertPageModal .modal-container .tab-panel .panel-body{width:100%;height:240px;position:relative;padding:0 16px 16px}.InsertPageModal .modal-container .tab-list{font-size:14px}.InsertPageModal .modal-container .tab-list .tab-options-button{padding:0;border:none;background-color:transparent}:host(:not([data-tabbing=true])) .InsertPageModal .modal-container .tab-list .tab-options-button,html:not([data-tabbing=true]) .InsertPageModal .modal-container .tab-list .tab-options-button{outline:none}.InsertPageModal .modal-container .footer{display:flex;padding:16px;align-items:center;justify-content:flex-end;width:100%;box-shadow:inset 0 1px 0 var(--modal-stroke-and-border);margin:0}.InsertPageModal .modal-container .footer .Button.insertPageModalConfirmButton{border:none;background-color:transparent;background:var(--primary-button);border-radius:4px;padding:0 8px;height:32px;width:92px;display:flex;align-items:center;justify-content:center;position:relative;color:var(--primary-button-text);cursor:pointer}:host(:not([data-tabbing=true])) .InsertPageModal .modal-container .footer .Button.insertPageModalConfirmButton,html:not([data-tabbing=true]) .InsertPageModal .modal-container .footer .Button.insertPageModalConfirmButton{outline:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InsertPageModal .modal-container .footer .Button.insertPageModalConfirmButton{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InsertPageModal .modal-container .footer .Button.insertPageModalConfirmButton{font-size:13px}}.InsertPageModal .modal-container .footer .Button.insertPageModalConfirmButton.disabled{border:none}.InsertPageModal .modal-container .tab-list .tab-options-divider+.tab-options-button{border-left:none!important}.InsertPageModal .modal-container .tab-list .tab-options-button{border-top:1px solid var(--tab-border-color);border-bottom:1px solid var(--tab-border-color)}.InsertPageModal .modal-container .tab-list .tab-options-button:first-child{border-left:1px solid var(--tab-border-color)}.InsertPageModal .modal-container .tab-list .tab-options-button:last-child{border-right:1px solid var(--tab-border-color)}.InsertPageModal .modal-container .tab-list .tab-options-button:hover{background:var(--tab-background-color-hover);border-top:1px solid var(--tab-border-color-hover);border-bottom:1px solid var(--tab-border-color-hover);border-right:1px solid var(--tab-border-color-hover)}.InsertPageModal .modal-container .tab-list .tab-options-button:hover+button,.InsertPageModal .modal-container .tab-list .tab-options-button:hover+div{border-left:none}.InsertPageModal .modal-container .tab-list .tab-options-button.selected{background:var(--tab-color-selected);border:1px solid var(--tab-color-selected);color:var(--tab-text-color-selected)}.InsertPageModal .modal-container .tab-list .tab-options-button.selected+button,.InsertPageModal .modal-container .tab-list .tab-options-button.selected+div{border-left:none!important}.InsertPageModal .modal-container .tab-list .tab-options-button:not(.selected){border-right:1px solid var(--tab-border-color)}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React, { useState } from 'react';\nimport { Input } from '@pdftron/webviewer-react-toolkit';\nimport Icon from 'components/Icon';\nimport classNames from 'classnames';\nimport PropTypes from 'prop-types';\n\nconst IncrementNumberInput = ({ id, className, min, onChange, value, fillWidth }) => {\n  const [number, setNumber] = useState(value);\n\n  const incrementNumber = () => {\n    onChange(number + 1);\n    setNumber(number + 1);\n  };\n\n  const decrementNumber = () => {\n    const newNumber = number - 1;\n    if (newNumber < min) {\n      return;\n    }\n    onChange(newNumber);\n    setNumber(newNumber);\n  };\n\n  const handleChange = (e) => {\n    onChange(parseInt(e.target.value));\n    setNumber(parseInt(e.target.value));\n  };\n\n  const handleBlur = (e) => {\n    let inputValue = parseInt(e.target.value);\n    if (!inputValue) {\n      inputValue = parseInt(min);\n      onChange(inputValue);\n      setNumber(inputValue);\n    }\n  };\n\n  return (\n    <div className={classNames({\n      incrementNumberInput: true,\n      [className]: !!className,\n    })}>\n      <Input id={id} type=\"number\" min={min} onChange={handleChange} value={number} fillWidth={fillWidth} onBlur={handleBlur} />\n      <div className=\"increment-buttons\">\n        <button className=\"increment-arrow-button\" onClick={incrementNumber}>\n          <Icon className=\"up-arrow\" glyph={'icon-chevron-up'} />\n        </button>\n        <button className=\"increment-arrow-button\" onClick={decrementNumber}>\n          <Icon className=\"down-arrow\" glyph={'icon-chevron-down'} />\n        </button>\n      </div>\n    </div>\n  );\n};\n\nIncrementNumberInput.propTypes = {\n  id: PropTypes.string,\n  className: PropTypes.string,\n  min: PropTypes.string,\n  onChange: PropTypes.func.isRequired,\n  value: PropTypes.number,\n  fillWidth: PropTypes.bool,\n};\n\nexport default IncrementNumberInput;\n", "import React, { useState } from 'react';\nimport { isIE11 } from 'helpers/device';\nimport classNames from 'classnames';\nimport PropTypes from 'prop-types';\n\nimport './DimensionInput.scss';\n\nconst DimensionInput = ({ id, className, label, initialValue, onChange, unit, maxLength = 10, disabled }) => {\n  const [value, setValue] = useState(initialValue);\n\n  const handleDimensionChange = (e) => {\n    setValue(e.target.value);\n    onChange(e.target.value);\n  };\n\n  // Resizes number input boxes so that units of measurement can be shown next to them as if they are also in the same box\n  const resizeInput = (input) => {\n    let length = input.toString().length;\n    let decimalSize = 0.3;\n    if (isIE11) {\n      const IE_ADJUSTMENT = 1.25;\n      length *= IE_ADJUSTMENT;\n      maxLength *= IE_ADJUSTMENT;\n      decimalSize *= IE_ADJUSTMENT;\n    }\n    if (input.toString().includes('.')) {\n      length -= decimalSize;\n    } else {\n      length += decimalSize;\n    }\n    if (length > maxLength) {\n      return maxLength;\n    }\n    return length;\n  };\n\n  return (\n    <div className={classNames({\n      dimensionInput: true,\n      [className]: !!className,\n    })}>\n      <label className=\"dimension-input-label\">\n        {label}\n        <div className=\"dimension-input-container\">\n          <input\n            id={id}\n            className=\"dimension-input\"\n            type=\"number\"\n            min=\"0\"\n            step={0.01}\n            onChange={handleDimensionChange}\n            disabled={disabled}\n            style={{ width: `${resizeInput(value)}ch` }}\n            value={value}\n          />\n          <span className=\"dimension-unit\">\n            {value > 0 && unit}\n          </span>\n        </div>\n      </label>\n    </div>\n  );\n};\n\nDimensionInput.propTypes = {\n  id: PropTypes.string.isRequired,\n  className: PropTypes.string,\n  label: PropTypes.string,\n  initialValue: PropTypes.number.isRequired,\n  onChange: PropTypes.func.isRequired,\n  unit: PropTypes.string.isRequired,\n  maxLength: PropTypes.number,\n  disabled: PropTypes.bool,\n};\n\nexport default DimensionInput;\n", "import DimensionInput from './DimensionInput';\n\nexport default DimensionInput;", "import React, { useState, useEffect } from 'react';\nimport { useSelector } from 'react-redux';\nimport PropTypes from 'prop-types';\nimport selectors from 'selectors';\nimport { Choice } from '@pdftron/webviewer-react-toolkit';\nimport PageNumberInput from 'components/PageReplacementModal/PageNumberInput';\nimport Dropdown from 'components/Dropdown';\nimport IncrementNumberInput from './IncrementNumberInput';\nimport DimensionInput from 'components/DimensionInput';\nimport { useTranslation } from 'react-i18next';\n\nimport './InsertBlankPagePanel.scss';\n\nconst InsertBlankPagePanel = ({\n  insertNewPageBelow,\n  insertNewPageIndexes,\n  numberOfBlankPagesToInsert,\n  pageNumberError,\n  setInsertNewPageBelow,\n  setInsertNewPageIndexes,\n  setNumberOfBlankPagesToInsert,\n  setInsertPageHeight,\n  setInsertPageWidth,\n  setPageNumberError,\n  loadedDocumentPageCount,\n}) => {\n  const [presetNewPageDimensions] = useSelector((state) => [\n    selectors.getPresetNewPageDimensions(state),\n  ]);\n\n  const [t] = useTranslation();\n\n  const presetPageDimensions = Object.keys(presetNewPageDimensions);\n\n  const CUSTOM_PAGE_DIMENSIONS = 'Custom';\n\n  const pagePlacementOptions = {\n    'ABOVE': 'above',\n    'BELOW': 'below',\n  };\n\n  const supportedUnits = {\n    'Inches (in)': '\"',\n    'Centimeters (cm)': 'cm',\n    'Millimeters (mm)': 'mm',\n  };\n\n  const PT_TO_INCHES = 72;\n\n  const unitConversions = {\n    '\"': 1,\n    'cm': 2.54,\n    'mm': 25.4,\n    'pt': PT_TO_INCHES,\n  };\n\n  const [selectedPageDimensions, setSelectedPageDimensions] = useState(presetPageDimensions[0]);\n  const [openCustomDimensions, setOpenCustomDimensions] = useState(false);\n  const [units, setUnits] = useState(Object.getOwnPropertyNames(supportedUnits)[0]);\n  const [customWidth, setCustomWidth] = useState(presetNewPageDimensions[presetPageDimensions[0]].width);\n  const [customHeight, setCustomHeight] = useState(presetNewPageDimensions[presetPageDimensions[0]].height);\n\n  useEffect(() => {\n    setInsertPageWidth(presetNewPageDimensions[selectedPageDimensions].width * PT_TO_INCHES);\n    setInsertPageHeight(presetNewPageDimensions[selectedPageDimensions].height * PT_TO_INCHES);\n  }, []);\n\n  const insertNewPagePlacementAbove = () => {\n    setInsertNewPageBelow(false);\n  };\n\n  const insertNewPagePlacementBelow = () => {\n    setInsertNewPageBelow(true);\n  };\n\n  const handlePageNumbersChanged = (pageNumbers) => {\n    if (pageNumbers.length > 0) {\n      setPageNumberError(null);\n    } else {\n      setPageNumberError(`${t('message.errorBlankPageNumber')}`);\n    }\n    setInsertNewPageIndexes(pageNumbers);\n  };\n\n  const handleAmountOfPagesChanged = (amount) => {\n    setNumberOfBlankPagesToInsert(amount);\n  };\n\n  const handleUnitsChanged = (units) => {\n    setUnits(units);\n    setInsertPageWidth(customWidth * (PT_TO_INCHES / unitConversions[supportedUnits[units]]));\n    setInsertPageHeight(customHeight * (PT_TO_INCHES / unitConversions[supportedUnits[units]]));\n  };\n\n  const handlePageDimensionsChanged = (dimensions) => {\n    if (dimensions === CUSTOM_PAGE_DIMENSIONS) {\n      setOpenCustomDimensions(true);\n      setInsertPageWidth(customWidth * (PT_TO_INCHES / unitConversions[supportedUnits[units]]));\n      setInsertPageHeight(customHeight * (PT_TO_INCHES / unitConversions[supportedUnits[units]]));\n    } else {\n      setInsertPageWidth(presetNewPageDimensions[dimensions].width * PT_TO_INCHES);\n      setInsertPageHeight(presetNewPageDimensions[dimensions].height * PT_TO_INCHES);\n      setOpenCustomDimensions(false);\n    }\n    setSelectedPageDimensions(dimensions);\n  };\n\n  const handleBlankPageWidthChange = (width) => {\n    setCustomWidth(width);\n    setInsertPageWidth(width * (PT_TO_INCHES / unitConversions[supportedUnits[units]]));\n  };\n\n  const handleBlankPageHeightChange = (height) => {\n    setCustomHeight(height);\n    setInsertPageHeight(height * (PT_TO_INCHES / unitConversions[supportedUnits[units]]));\n  };\n\n  return (\n    <div className=\"insert-blank-page-panel\">\n      <div className=\"panel-container\">\n        <div className=\"subheader\">{t('insertPageModal.pagePlacements.header')}</div>\n        <div className=\"section\">\n          <Choice\n            label={t(`insertPageModal.pagePlacements.${pagePlacementOptions.ABOVE}`)}\n            name=\"PAGE_PLACEMENT\"\n            onChange={insertNewPagePlacementAbove}\n            checked={!insertNewPageBelow}\n            radio\n          />\n          <Choice\n            label={t(`insertPageModal.pagePlacements.${pagePlacementOptions.BELOW}`)}\n            name=\"PAGE_PLACEMENT\"\n            onChange={insertNewPagePlacementBelow}\n            checked={insertNewPageBelow}\n            radio\n          />\n        </div>\n        <div className=\"subheader\">{t('insertPageModal.pageLocations.header')}</div>\n        <div className=\"section extra-space-section\">\n          <div className=\"input-container\">\n            <label className='specify-pages-wrapper' htmlFor='specifyPagesInput'>{t('insertPageModal.pageLocations.specify')}\n              <span className=\"input-sub-text\">\n                {t('insertPageModal.pageLocations.total')}: {loadedDocumentPageCount}{' '}\n                {t('insertPageModal.pageLocations.pages')}\n              </span>\n            </label>\n            <PageNumberInput\n              id=\"specifyPagesInput\"\n              selectedPageNumbers={insertNewPageIndexes}\n              pageCount={loadedDocumentPageCount}\n              onSelectedPageNumbersChange={handlePageNumbersChanged}\n              onBlurHandler={handlePageNumbersChanged}\n              pageNumberError={pageNumberError}\n            />\n          </div>\n          <div className=\"input-container\">\n            <label htmlFor='numberOfPagesInput'>{t('insertPageModal.pageLocations.amount')}</label>\n            <IncrementNumberInput\n              id=\"numberOfPagesInput\"\n              type=\"number\"\n              min=\"1\"\n              onChange={handleAmountOfPagesChanged}\n              value={numberOfBlankPagesToInsert}\n              fillWidth\n            />\n          </div>\n        </div>\n        <div className=\"subheader\">{t('insertPageModal.pageDimensions.header')}</div>\n        <div className=\"section page-dimensions-section\">\n          <div className=\"input-container\">\n            <label id=\"insert-blank-pages-preset-label\" htmlFor=\"pagesPreset\">{t('insertPageModal.pageDimensions.subHeader')}</label>\n            <Dropdown\n              id=\"pagesPreset\"\n              labelledById=\"insert-blank-pages-preset-label\"\n              dataElement=\"presetSelector\"\n              currentSelectionKey={selectedPageDimensions}\n              onClickItem={handlePageDimensionsChanged}\n              items={[...presetPageDimensions, CUSTOM_PAGE_DIMENSIONS]}\n            />\n          </div>\n          <div className=\"input-container\" style={{ visibility: openCustomDimensions ? 'visible' : 'hidden' }}>\n            <label id=\"insert-blank-pages-dimensions-label\" htmlFor='pageDimensionsUnit'>{t('insertPageModal.pageDimensions.units')}</label>\n            <Dropdown\n              id=\"pageDimensionsUnit\"\n              labelledById=\"insert-blank-pages-dimensions-label\"\n              dataElement=\"unitSelector\"\n              currentSelectionKey={units}\n              onClickItem={handleUnitsChanged}\n              items={Object.keys(supportedUnits)}\n            />\n          </div>\n        </div>\n        <div className=\"section\" style={{ display: openCustomDimensions ? 'flex' : 'none' }}>\n          <div className=\"input-container\">\n            <label htmlFor='pageWidthInput'>{t('formField.formFieldPopup.width')}</label>\n            <DimensionInput id='pageWidthInput'\n              className='customWidthInput'\n              initialValue={customWidth}\n              onChange={handleBlankPageWidthChange}\n              unit={supportedUnits[units]}\n            />\n          </div>\n          <div className=\"input-container\">\n            <label htmlFor='pageHeightInput'>{t('formField.formFieldPopup.height')}</label>\n            <DimensionInput id='pageHeightInput'\n              className='customHeightInput'\n              initialValue={customHeight}\n              onChange={handleBlankPageHeightChange}\n              unit={supportedUnits[units]}\n            />\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nInsertBlankPagePanel.propTypes = {\n  insertNewPageBelow: PropTypes.bool,\n  insertNewPageIndexes: PropTypes.array,\n  numberOfBlankPagesToInsert: PropTypes.number,\n  pageNumberError: PropTypes.string,\n  setInsertNewPageBelow: PropTypes.func,\n  setInsertNewPageIndexes: PropTypes.func,\n  setNumberOfBlankPagesToInsert: PropTypes.func,\n  setInsertPageHeight: PropTypes.func,\n  setInsertPageWidth: PropTypes.func,\n  setPageNumberError: PropTypes.func,\n  loadedDocumentPageCount: PropTypes.number,\n};\n\nexport default InsertBlankPagePanel;\n", "import InsertBlankPagePanel from './InsertBlankPagePanel';\n\nexport default InsertBlankPagePanel;", "import React, { useEffect, useState } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport classNames from 'classnames';\nimport Button from 'components/Button';\nimport selectors from 'selectors';\n\nimport './InsertUploadedPagePanel.scss';\nimport PageThumbnailsGrid from 'components/PageThumbnailsGrid';\nimport { Choice } from '@pdftron/webviewer-react-toolkit';\nimport PageNumberInput from 'components/PageReplacementModal/PageNumberInput';\nimport { useSelector } from 'react-redux';\n\nconst InsertUploadedPagePanel = React.forwardRef(({\n  sourceDocument,\n  closeModal,\n  clearLoadedFile,\n  insertPages,\n  loadedDocumentPageCount,\n  closeModalWarning,\n  insertNewPageIndexes = [1],\n}, ref) => {\n  const [t] = useTranslation();\n  const [selectedThumbnails, setSelectedThumbnails] = useState({});\n  const [isLoading, setIsLoading] = useState(true);\n  const [pageNumberToInsertAt, setPageNumberToInsertAt] = useState([insertNewPageIndexes[0]]);\n  const [insertAbove, setInsertAbove] = useState(true);\n  const [pageNumberError, setPageNumberError] = useState('');\n  const customizableUI = useSelector((state) => selectors.getFeatureFlags(state)?.customizableUI);\n\n  useEffect(() => {\n    const pageCount = sourceDocument.getPageCount();\n    const selectedPages = {};\n    for (let i = 1; i <= pageCount; i++) {\n      selectedPages[i] = true;\n    }\n    setSelectedThumbnails(selectedPages);\n  }, [sourceDocument]);\n\n\n  const onThumbnailSelected = (pageNumber) => {\n    if (selectedThumbnails[pageNumber] === undefined) {\n      selectedThumbnails[pageNumber] = true;\n    } else {\n      selectedThumbnails[pageNumber] = !selectedThumbnails[pageNumber];\n    }\n    setSelectedThumbnails({ ...selectedThumbnails });\n  };\n\n  const getSelectedPages = () => {\n    const pageNumbers = Object.keys(selectedThumbnails);\n    return pageNumbers.reduce((selectedPages, currentPage) => {\n      if (selectedThumbnails[currentPage]) {\n        selectedPages.push(parseInt(currentPage));\n      }\n      return selectedPages;\n    }, []);\n  };\n\n  const pageInputBlurHandler = (pageNumbers) => {\n    if (pageNumbers.length > 0) {\n      setPageNumberError(null);\n      setPageNumberToInsertAt(pageNumbers);\n    }\n  };\n\n  const deselectAllThumbnails = () => {\n    setSelectedThumbnails({});\n  };\n\n  const onInsertChoiceChange = () => {\n    setInsertAbove(!insertAbove);\n  };\n\n  const insertPagesHandler = () => {\n    let insertAtPage = pageNumberToInsertAt[0];\n    if (insertAtPage) {\n      let insertBeforeThisPage = insertAbove ? insertAtPage : ++insertAtPage;\n\n      if (insertBeforeThisPage > loadedDocumentPageCount) {\n        insertBeforeThisPage = null;\n      }\n\n      insertPages(sourceDocument, getSelectedPages(), insertBeforeThisPage);\n    }\n\n    closeModal();\n  };\n\n  const onCloseHandler = () => {\n    closeModalWarning();\n  };\n\n  const handlePageNumberError = (pageNumber) => {\n    if (pageNumber) {\n      setPageNumberError(`${t('message.errorPageNumber')} ${loadedDocumentPageCount}`);\n    }\n  };\n\n  return (\n    <div className=\"insert-uploaded-page-panel\" onMouseDown={(e) => e.stopPropagation()} ref={ref}>\n      <div className=\"header\">\n        <div className='left-header'>\n          <Button\n            img={'icon-arrow-back'}\n            onClick={clearLoadedFile}\n            dataElement={'insertFromFileBackButton'}\n            title={t('action.back')}\n          />\n          {t('insertPageModal.selectPages')} {`(${isLoading ? 0 : getSelectedPages().length})`}\n        </div>\n        <Button className=\"insertPageModalCloseButton\" img=\"icon-close\" onClick={onCloseHandler} title=\"action.cancel\" />\n\n      </div>\n      <div className=\"modal-body\">\n        <div className=\"insert-blank-page-controls\">\n          <div className='insert-page-location-options'>\n            <span className='insert-page-titles '>{t('insertPageModal.pagePlacements.header')}</span>\n            <div className='insert-page-options'>\n              <Choice label={t('insertPageModal.pagePlacements.above')} radio name='insertPagePosition' checked={insertAbove} onChange={onInsertChoiceChange} />\n              <Choice label={t('insertPageModal.pagePlacements.below')} radio name='insertPagePosition' checked={!insertAbove} onChange={onInsertChoiceChange} />\n            </div>\n          </div>\n          <div className='insert-page-location'>\n            <span className='insert-page-titles '>{t('insertPageModal.pageLocations.specifyLocation')}</span>\n            <div className='insert-page-input'>\n              {t('insertPageModal.page')}:\n              <PageNumberInput\n                selectedPageNumbers={pageNumberToInsertAt}\n                pageCount={loadedDocumentPageCount}\n                onSelectedPageNumbersChange={pageInputBlurHandler}\n                onBlurHandler={setPageNumberToInsertAt}\n                onError={handlePageNumberError}\n                pageNumberError={pageNumberError}\n              />\n            </div>\n          </div>\n\n        </div>\n        <div className={classNames('modal-body-thumbnail-container', { isLoading, 'modular-ui': customizableUI })}>\n          <PageThumbnailsGrid\n            document={sourceDocument}\n            onThumbnailSelected={onThumbnailSelected}\n            selectedThumbnails={selectedThumbnails}\n            onfileLoadedHandler={setIsLoading} />\n        </div>\n      </div>\n      <div className={classNames('insert-page-footer', { isFileSelected: !isLoading })}>\n        <button className={classNames('deselect-thumbnails', { disabled: isLoading })} onClick={deselectAllThumbnails} disabled={isLoading}>\n          {t('action.deselectAll')}\n        </button>\n        <Button\n          className=\"modal-btn\"\n          onClick={insertPagesHandler}\n          label={t('insertPageModal.button')}\n          disabled={getSelectedPages().length === 0 || isLoading || pageNumberToInsertAt.length === 0 || pageNumberError}\n        />\n      </div>\n    </div>\n  );\n});\n\nInsertUploadedPagePanel.displayName = InsertUploadedPagePanel;\nexport default InsertUploadedPagePanel;\n", "import React from 'react';\nimport InsertUploadedPagePanel from './InsertUploadedPagePanel';\nimport { insertPages, exitPageInsertionWarning } from 'helpers/pageManipulationFunctions';\nimport { useDispatch } from 'react-redux';\n\nconst InsertUploadedPagePanelContainer = React.forwardRef((props, ref) => {\n  // This makes it easier to mock the insertPages handler for testing purposes\n  const dispatch = useDispatch();\n  const { closeModal } = props;\n  const closeModalWarning = () => exitPageInsertionWarning(closeModal, dispatch);\n  return (<InsertUploadedPagePanel {...props} insertPages={insertPages} closeModalWarning={closeModalWarning} ref={ref} />);\n});\n\nInsertUploadedPagePanelContainer.displayName = 'InsertUploadedPagePanelContainer';\nexport default InsertUploadedPagePanelContainer;", "import InsertUploadedPagePanelContainer from './InsertUploadedPagePanelContainer';\n\nexport default InsertUploadedPagePanelContainer;", "import React, { useState, useEffect } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport actions from 'actions';\nimport selectors from 'selectors';\nimport { useTranslation } from 'react-i18next';\nimport DataElements from 'constants/dataElement';\nimport { Tabs, Tab, TabPanel } from 'components/Tabs';\nimport Button from 'components/Button';\nimport { getInstanceNode } from 'helpers/getRootNode';\nimport ModalWrapper from 'components/ModalWrapper';\n\nimport core from 'core';\n\nimport { insertAbove, insertBelow, exitPageInsertionWarning } from '../../helpers/pageManipulationFunctions';\nimport InsertBlankPagePanel from './InsertBlankPagePanel';\nimport InsertUploadedPagePanel from './InsertUploadedPagePanel';\n\nimport FilePickerPanel from '../PageReplacementModal/FilePickerPanel';\n\nimport './InsertPageModal.scss';\n\nconst options = { loadAsPDF: true };\n\nconst InsertPageModal = ({ loadedDocumentPageCount }) => {\n  const [selectedPageIndexes, currentPage, selectedTab] = useSelector((state) => [\n    selectors.getSelectedThumbnailPageIndexes(state),\n    selectors.getCurrentPage(state),\n    selectors.getSelectedTab(state, DataElements.INSERT_PAGE_MODAL),\n  ]);\n\n  const [selectedDoc, setSelectedDoc] = useState(null);\n  const [insertNewPageBelow, setInsertNewPageBelow] = useState(false);\n  const [insertNewPageIndexes, setInsertNewPageIndexes] = useState([]);\n  const [numberOfBlankPagesToInsert, setNumberOfBlankPagesToInsert] = useState(1);\n  const [insertPageHeight, setInsertPageHeight] = useState(0);\n  const [insertPageWidth, setInsertPageWidth] = useState(0);\n  const [pageNumberError, setPageNumberError] = useState('');\n\n  useEffect(() => {\n    const pageNumbers = selectedPageIndexes.length > 0 ? selectedPageIndexes.map((i) => i + 1) : [currentPage];\n    setInsertNewPageIndexes(pageNumbers);\n  }, [selectedPageIndexes]);\n\n  const dispatch = useDispatch();\n  const [t] = useTranslation();\n\n  const closeModal = () => {\n    dispatch(actions.closeElement(DataElements.INSERT_PAGE_MODAL));\n  };\n\n  const showCloseModalWarning = () => {\n    exitPageInsertionWarning(closeModal, dispatch);\n  };\n\n  const apply = () => {\n    if (insertNewPageBelow) {\n      for (let i = 0; i < numberOfBlankPagesToInsert; ++i) {\n        insertBelow(insertNewPageIndexes.map((page, index) => page + (index + 1) * i), insertPageWidth, insertPageHeight);\n      }\n    } else {\n      for (let i = 0; i < numberOfBlankPagesToInsert; ++i) {\n        insertAbove(insertNewPageIndexes.map((page, index) => page + (index + 1) * i), insertPageWidth, insertPageHeight);\n      }\n    }\n    closeModal();\n  };\n\n  // File picker can merge docs, in which case the callback gets\n  // executed with a Document not a file\n  const fileProcessedHandler = async (file) => {\n    let document;\n    // eslint-disable-next-line no-undef\n    if (file instanceof getInstanceNode().instance.Core.Document) {\n      document = file;\n    } else {\n      try {\n        document = await core.createDocument(file, options);\n      } catch (e) {\n        console.error('File type not supported');\n      }\n    }\n    setSelectedDoc(document);\n  };\n\n  const clearDocument = () => {\n    setSelectedDoc(null);\n  };\n\n  const renderFileSelectedPanel = () => {\n    return (\n      <InsertUploadedPagePanel\n        sourceDocument={selectedDoc}\n        closeModal={closeModal}\n        clearLoadedFile={clearDocument}\n        loadedDocumentPageCount={loadedDocumentPageCount}\n        insertNewPageIndexes={insertNewPageIndexes}\n      />\n    );\n  };\n\n  const renderSelectionTabs = () => {\n    const isUploadPagePanelActive = selectedTab === DataElements.INSERT_FROM_FILE_TAB;\n    const insertBlankPageProps = {\n      insertNewPageBelow,\n      insertNewPageIndexes,\n      numberOfBlankPagesToInsert,\n      pageNumberError,\n      setInsertNewPageBelow,\n      setInsertNewPageIndexes,\n      setNumberOfBlankPagesToInsert,\n      setInsertPageHeight,\n      setInsertPageWidth,\n      setPageNumberError,\n      loadedDocumentPageCount,\n    };\n    return (\n      <div className=\"container tabs\" onClick={(e) => e.stopPropagation()} onMouseDown={(e) => e.stopPropagation()}>\n        <Tabs className=\"insert-page-tabs\" id={DataElements.INSERT_PAGE_MODAL}>\n          <div className=\"tabs-header-container\">\n            <div className=\"tab-list\">\n              <Tab dataElement={DataElements.INSERT_BLANK_PAGE_TAB}>\n                <button className=\"tab-options-button\">{t('insertPageModal.tabs.blank')}</button>\n              </Tab>\n              <div className=\"tab-options-divider\" />\n              <Tab dataElement={DataElements.INSERT_FROM_FILE_TAB}>\n                <button className=\"tab-options-button\">{t('insertPageModal.tabs.upload')}</button>\n              </Tab>\n            </div>\n          </div>\n          <TabPanel dataElement={DataElements.INSERT_BLANK_PAGE_PANEL}>\n            <InsertBlankPagePanel {...insertBlankPageProps} />\n          </TabPanel>\n          <TabPanel dataElement={DataElements.INSERT_FROM_FILE_PANEL}>\n            <div className='panel-body'>\n              <FilePickerPanel\n                onFileProcessed={fileProcessedHandler} />\n            </div>\n          </TabPanel>\n        </Tabs>\n        <div className=\"divider\"></div>\n        <div className=\"footer\">\n          <Button\n            className=\"insertPageModalConfirmButton\"\n            label=\"insertPageModal.button\"\n            onClick={apply}\n            disabled={insertPageWidth <= 0 || insertPageHeight <= 0 || isUploadPagePanelActive || insertNewPageIndexes.length === 0 || pageNumberError} />\n        </div>\n      </div>\n    );\n  };\n\n  return (\n    <div className=\"Modal open InsertPageModal\" data-element={DataElements.INSERT_PAGE_MODAL} onMouseDown={selectedDoc ? showCloseModalWarning : closeModal}>\n      <ModalWrapper\n        title={selectedDoc ? null : t('insertPageModal.title')}\n        isOpen={true}\n        closeHandler={closeModal}\n        onCloseClick={closeModal}\n        swipeToClose\n      >\n        {selectedDoc ? renderFileSelectedPanel() : renderSelectionTabs()}\n      </ModalWrapper>\n    </div>\n  );\n};\n\nexport default InsertPageModal;\n", "import React from 'react';\nimport InsertPageModal from './InsertPageModal';\nimport { useSelector } from 'react-redux';\nimport selectors from 'selectors';\nimport DataElements from 'constants/dataElement';\nimport core from 'core';\n\nconst InsertPageModalContainer = () => {\n  const [isDisabled, isOpen] = useSelector((state) => [\n    selectors.isElementDisabled(state, DataElements.INSERT_PAGE_MODAL),\n    selectors.isElementOpen(state, DataElements.INSERT_PAGE_MODAL),\n  ]);\n\n\n  if (!isDisabled && isOpen) {\n    const document = core.getDocumentViewer().getDocument();\n    const loadedDocumentPageCount = document ? core.getTotalPages() : null;\n    return <InsertPageModal loadedDocumentPageCount={loadedDocumentPageCount} />;\n  }\n  return null;\n};\nexport default InsertPageModalContainer;", "import InsertPageModal from './InsertPageModalContainer';\n\nexport default InsertPageModal;"], "sourceRoot": ""}