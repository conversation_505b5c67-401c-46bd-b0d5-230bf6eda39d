{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/ms-my.js"], "names": ["module", "exports", "e", "t", "default", "a", "_", "name", "weekdays", "split", "months", "weekStart", "weekdaysShort", "monthsShort", "weekdaysMin", "ordinal", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "relativeTime", "future", "past", "s", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "locale"], "mappings": "gFAAoEA,EAAOC,QAAgL,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAGI,EAAE,CAACC,KAAK,QAAQC,SAAS,6CAA6CC,MAAM,KAAKC,OAAO,oFAAoFD,MAAM,KAAKE,UAAU,EAAEC,cAAc,8BAA8BH,MAAM,KAAKI,YAAY,kDAAkDJ,MAAM,KAAKK,YAAY,uBAAuBL,MAAM,KAAKM,QAAQ,SAASb,GAAG,OAAOA,GAAGc,QAAQ,CAACC,GAAG,QAAQC,IAAI,WAAWC,EAAE,aAAaC,GAAG,cAAcC,IAAI,4BAA4BC,KAAK,mCAAmCC,aAAa,CAACC,OAAO,WAAWC,KAAK,gBAAgBC,EAAE,gBAAgBC,EAAE,UAAUC,GAAG,WAAWC,EAAE,QAAQC,GAAG,SAASC,EAAE,SAASC,GAAG,UAAUC,EAAE,UAAUC,GAAG,WAAWC,EAAE,UAAUC,GAAG,aAAa,OAAOjC,EAAEC,QAAQiC,OAAO/B,EAAE,MAAK,GAAIA,EAAvhCD,CAAE,EAAQ", "file": "chunks/chunk.189.js", "sourcesContent": ["!function(e,a){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=a(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],a):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_ms_my=a(e.dayjs)}(this,(function(e){\"use strict\";function a(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var t=a(e),_={name:\"ms-my\",weekdays:\"Ahad_Isnin_Selasa_Rabu_Khamis_Jumaat_Sabtu\".split(\"_\"),months:\"Janua<PERSON>_Februari_Mac_April_Mei_Jun_Julai_Ogos_September_Oktober_November_Disember\".split(\"_\"),weekStart:1,weekdaysShort:\"Ahd_Isn_Sel_Rab_Kha_Jum_Sab\".split(\"_\"),monthsShort:\"Jan_Feb_Mac_Apr_Mei_Jun_Jul_Ogs_Sep_Okt_Nov_Dis\".split(\"_\"),weekdaysMin:\"Ah_Is_Sl_Rb_Km_Jm_Sb\".split(\"_\"),ordinal:function(e){return e},formats:{LT:\"HH.mm\",LTS:\"HH.mm.ss\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY [pukul] HH.mm\",LLLL:\"dddd, D MMMM YYYY [pukul] HH.mm\"},relativeTime:{future:\"dalam %s\",past:\"%s yang lepas\",s:\"beberapa saat\",m:\"seminit\",mm:\"%d minit\",h:\"sejam\",hh:\"%d jam\",d:\"sehari\",dd:\"%d hari\",M:\"sebulan\",MM:\"%d bulan\",y:\"setahun\",yy:\"%d tahun\"}};return t.default.locale(_,null,!0),_}));"], "sourceRoot": ""}