{"version": 3, "sources": ["webpack:///./src/ui/src/components/FormulaBar/FormulaBar.scss?c368", "webpack:///./src/ui/src/components/FormulaBar/FormulaBar.scss", "webpack:///./src/ui/src/components/FormulaBar/FormulaBar.js", "webpack:///./src/ui/src/components/FormulaBar/FormulaBarContainer.js", "webpack:///./src/ui/src/components/FormulaBar/index.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "FormulaBar", "props", "isReadOnly", "activeCellRange", "cellFormula", "stringCellValue", "t", "useTranslation", "formulaBarValue", "DataElementWrapper", "className", "dataElement", "DataElements", "FORMULA_BAR", "type", "value", "readOnly", "aria-label", "classNames", "Icon", "glyph", "propTypes", "PropTypes", "bool", "string", "formulaOptions", "label", "description", "FormulaBarContainer", "useSelector", "selectors", "getActiveCellRange", "getCellFormula", "getStringCellValue"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEzB5B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,2hCAA4hC,M,sJCI/iC4B,EAAa,SAACC,GAClB,IAAQC,EAA8DD,EAA9DC,WAAYC,EAAkDF,EAAlDE,gBAAiBC,EAAiCH,EAAjCG,YAAaC,EAAoBJ,EAApBI,gBAE1CC,EAAMC,cAAND,EAEFE,EAAkBJ,GAAeC,GAAmB,GAE1D,OACE,kBAACI,EAAA,EAAkB,CAACC,UAAU,aAAaC,YAAaC,IAAaC,aACnE,2BAAOC,KAAK,OAAOJ,UAAU,aAAaK,MAAOZ,EAAiBa,SAAUd,EAAYe,aAAYX,EAAE,sBACtG,yBAAKI,UAAWQ,IAAW,UAAW,CAAEF,SAAUd,KAChD,kBAACiB,EAAA,EAAI,CAACC,MAAM,WAAWV,UAAWQ,IAAW,cAAe,CAAEF,SAAUd,MACxE,2BAAOQ,UAAWQ,IAAW,eAAgB,CAAEF,SAAUd,IAAeY,KAAK,OAAOC,MAAOP,EAAiBQ,SAAUd,EAAYe,aAAYX,EAAE,yBAMxJN,EAAWqB,UAAY,CACrBnB,WAAYoB,IAAUC,KACtBpB,gBAAiBmB,IAAUE,OAC3BpB,YAAakB,IAAUE,OACvBnB,gBAAiBiB,IAAUE,QAGdxB,QC9BTyB,EAAiB,CACrB,CAAEV,MAAO,SAAUW,MAAO,QAASC,YAAa,oBAChD,CAAEZ,MAAO,SAAUW,MAAO,QAASC,YAAa,oBAChD,CAAEZ,MAAO,OAAQW,MAAO,MAAOC,YAAa,kBAC5C,CAAEZ,MAAO,SAAUW,MAAO,QAASC,YAAa,oBAChD,CAAEZ,MAAO,QAASW,MAAO,OAAQC,YAAa,mBAC9C,CAAEZ,MAAO,QAASW,MAAO,OAAQC,YAAa,mBAC9C,CAAEZ,MAAO,UAAWW,MAAO,SAAUC,YAAa,qBAClD,CAAEZ,MAAO,SAAUW,MAAO,QAASC,YAAa,qBAkBnCC,EAhBoB,WAEjC,IAAMzB,EAAkB0B,YAAYC,IAAUC,oBACxC3B,EAAcyB,YAAYC,IAAUE,gBACpC3B,EAAkBwB,YAAYC,IAAUG,oBAE9C,OACE,kBAAC,EAAU,CACT/B,YAAU,EACVuB,eAAgBA,EAChBtB,gBAAiBA,EACjBC,YAAaA,EACbC,gBAAiBA,KCzBRuB", "file": "chunks/chunk.62.js", "sourcesContent": ["var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./FormulaBar.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".FormulaBar{display:flex;border:1px solid var(--gray-5);background-color:var(--gray-2);padding:8px;grid-gap:8px;gap:8px;border-bottom:1px solid #e0e0e0}.FormulaBar .RangeInput.focus-visible,.FormulaBar .RangeInput:focus-visible{outline:var(--focus-visible-outline)!important}.FormulaBar .Formula{display:flex;align-items:center;flex-grow:1;height:32px;position:relative;border:1px solid var(--border);border-radius:4px;background-color:var(--component-background);padding-left:30px}.FormulaBar .Formula.readOnly{border:1px solid var(--disabled-text)}.FormulaBar .Formula:not(.readOnly)[focus-within]{outline:var(--focus-visible-outline)!important}.FormulaBar .Formula:not(.readOnly):focus-within{outline:var(--focus-visible-outline)!important}.FormulaBar .FormulaIcon{position:absolute;left:8px}.FormulaBar .FormulaIcon.readOnly{color:var(--disabled-icon)}.FormulaBar .FormulaInput{flex-grow:1;width:100%;padding:4px;border:none;outline:none;font-size:14px;background-color:transparent}.FormulaBar .FormulaInput.readOnly{color:var(--disabled-text)}\", \"\"]);\n\n// exports\n", "import React from 'react';\nimport { useTranslation } from 'react-i18next';\nimport Icon from 'components/Icon';\nimport './FormulaBar.scss';\nimport PropTypes from 'prop-types';\nimport DataElements from 'constants/dataElement';\nimport DataElementWrapper from 'components/DataElementWrapper';\nimport classNames from 'classnames';\n\nconst FormulaBar = (props) => {\n  const { isReadOnly, activeCellRange, cellFormula, stringCellValue } = props;\n\n  const { t } = useTranslation();\n\n  const formulaBarValue = cellFormula || stringCellValue || '';\n\n  return (\n    <DataElementWrapper className='FormulaBar' dataElement={DataElements.FORMULA_BAR}>\n      <input type=\"text\" className='RangeInput' value={activeCellRange} readOnly={isReadOnly} aria-label={t('formulaBar.range')}/>\n      <div className={classNames('Formula', { readOnly: isReadOnly })}>\n        <Icon glyph=\"function\" className={classNames('FormulaIcon', { readOnly: isReadOnly })}/>\n        <input className={classNames('FormulaInput', { readOnly: isReadOnly })} type=\"text\" value={formulaBarValue} readOnly={isReadOnly} aria-label={t('formulaBar.label')}/>\n      </div>\n    </DataElementWrapper>\n  );\n};\n\nFormulaBar.propTypes = {\n  isReadOnly: PropTypes.bool,\n  activeCellRange: PropTypes.string,\n  cellFormula: PropTypes.string,\n  stringCellValue: PropTypes.string,\n};\n\nexport default FormulaBar;", "import React from 'react';\nimport { useSelector } from 'react-redux';\nimport selectors from 'selectors';\nimport FormulaBar from './FormulaBar';\nconst formulaOptions = [\n  { value: '=SUMIF', label: 'SUMIF', description: 'formulaBar.sumif' },\n  { value: '=SUMSQ', label: 'SUMSQ', description: 'formulaBar.sumsq' },\n  { value: '=SUM', label: 'SUM', description: 'formulaBar.sum' },\n  { value: '=ASINH', label: 'ASINH', description: 'formulaBar.asinh' },\n  { value: '=ACOS', label: 'ACOS', description: 'formulaBar.acos' },\n  { value: '=COSH', label: 'COSH', description: 'formulaBar.cosh' },\n  { value: '=ISEVEN', label: 'ISEVEN', description: 'formulaBar.iseven' },\n  { value: '=ISODD', label: 'ISODD', description: 'formulaBar.isodd' },\n];\nexport const FormulaBarContainer = () => {\n  // This component can pull all Redux state and call any core methods\n  const activeCellRange = useSelector(selectors.getActiveCellRange);\n  const cellFormula = useSelector(selectors.getCellFormula);\n  const stringCellValue = useSelector(selectors.getStringCellValue);\n\n  return (\n    <FormulaBar\n      isReadOnly\n      formulaOptions={formulaOptions}\n      activeCellRange={activeCellRange}\n      cellFormula={cellFormula}\n      stringCellValue={stringCellValue} />\n  );\n};\n\nexport default FormulaBarContainer;", "import FormulaBarContainer from './FormulaBarContainer';\nexport default FormulaBarContainer;"], "sourceRoot": ""}