{"version": 3, "sources": ["webpack:///./src/ui/src/components/CustomizablePopup/CustomizablePopup.js", "webpack:///./src/ui/src/components/CustomizablePopup/index.js"], "names": ["propTypes", "dataElement", "PropTypes", "string", "isRequired", "children", "arrayOf", "any", "CustomizablePopup", "childrenClassName", "items", "useSelector", "state", "selectors", "getPopupItems", "shallowEqual", "childrenA<PERSON>y", "React", "Children", "toArray", "map", "item", "i", "type", "hidden", "key", "mediaQueryClassName", "screen", "join", "component", "find", "child", "props", "className", "<PERSON><PERSON><PERSON><PERSON>on", "ToolGroupButton", "ToggleElementButton", "ActionButton", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CustomElement", "cloneElement"], "mappings": "+yDAaA,IAAMA,EAAY,CAGhBC,YAAaC,IAAUC,OAAOC,WAE9BC,SAAUH,IAAUI,QAAQJ,IAAUK,KAAKH,YAGvCI,EAAoB,SAAH,GAAqD,IAA/CP,EAAW,EAAXA,YAAaI,EAAQ,EAARA,SAAUI,EAAiB,EAAjBA,kBAC5CC,EAAQC,aACZ,SAACC,GAAK,OAAKC,IAAUC,cAAcF,EAAOX,KAC1Cc,KAGIC,EAAgBC,IAAMC,SAASC,QAAQd,GA2B7C,OAAOK,EAAMU,KAAI,SAACC,EAAMC,GACtB,IAAQrB,EAA8BoB,EAA9BpB,YAAasB,EAAiBF,EAAjBE,KAAMC,EAAWH,EAAXG,OACrBC,EAAM,GAAH,OAAMF,EAAI,YAAItB,GAAeqB,GAChCI,EAAsBF,aAAM,EAANA,EACxBJ,KAAI,SAACO,GAAM,wBAAgBA,MAC5BC,KAAK,KACJC,EAAYb,EAAcc,MAC5B,SAACC,GAAK,OAAKA,EAAMC,MAAM/B,cAAgBA,KAIzC,IAAK4B,EAAW,CACd,IAAMG,EAAQ,EAAH,KAAQX,GAAI,IAAEK,sBAAqBO,UAAWxB,IAE5C,eAATc,IACFM,EAAY,kBAACK,EAAA,EAAeF,IAGjB,oBAATT,IACFM,EAAY,kBAACM,EAAA,EAAoBH,IAGtB,wBAATT,IACFM,EAAY,kBAACO,EAAA,EAAwBJ,IAG1B,iBAATT,IACFM,EAAY,kBAACQ,EAAA,EAAiBL,IAGnB,mBAATT,IACFM,EAAY,kBAACS,EAAA,EAAmBN,IAGrB,kBAATT,IACFM,EAAY,kBAACU,EAAA,EAAkBP,IAGpB,WAATT,GAA8B,YAATA,IACvBM,EACE,2BAAKI,UAAS,UAAKV,EAAI,YAAIG,IAA2BM,KAK5D,OAAOH,EACHZ,IAAMuB,aAAaX,EAAW,CAC9BJ,QAEA,SAIRjB,EAAkBR,UAAYA,EAEfQ,QC3GAA", "file": "chunks/chunk.9.js", "sourcesContent": ["import React from 'react';\nimport PropTypes from 'prop-types';\nimport { useSelector, shallowEqual } from 'react-redux';\n\nimport ToolButton from 'components/ToolButton';\nimport ToolGroupButton from 'components/ToolGroupButton';\nimport ToggleElementButton from 'components/ToggleElementButton';\nimport ActionButton from 'components/ActionButton';\nimport StatefulButton from 'components/StatefulButton';\nimport CustomElement from 'components/CustomElement';\n\nimport selectors from 'selectors';\n\nconst propTypes = {\n  // The data element of the popup component.\n  // Used to grab button props from redux and use those props to override the existing ones, if there're any\n  dataElement: PropTypes.string.isRequired,\n  // An object that maps an item's dataElement to a functional React component\n  children: PropTypes.arrayOf(PropTypes.any).isRequired,\n};\n\nconst CustomizablePopup = ({ dataElement, children, childrenClassName }) => {\n  const items = useSelector(\n    (state) => selectors.getPopupItems(state, dataElement),\n    shallowEqual,\n  );\n\n  const childrenArray = React.Children.toArray(children);\n\n  if (process.env.NODE_ENV !== 'production') {\n    // give a error message in the console if a child's dataElement in the childrenArray isn't in the redux state\n    childrenArray.forEach((child) => {\n      const found = items.some(\n        ({ dataElement }) => dataElement === child.props.dataElement,\n      );\n      if (!found) {\n        const error = `\n        A React component with dataElement ${child.props.dataElement} won't be rendered because it isn't in the redux state. Modify initialState.js like below to fix this issue:\n\n        {\n          viewer: {\n            ...,\n            ${dataElement}: [\n              ...,\n              { dataElement: '${child.props.dataElement}' },\n            ]\n          }\n        }\n      `;\n        console.error(error.replace(/\\s+/, ''));\n      }\n    });\n  }\n\n  return items.map((item, i) => {\n    const { dataElement, type, hidden } = item;\n    const key = `${type}-${dataElement || i}`;\n    const mediaQueryClassName = hidden\n      ?.map((screen) => `hide-in-${screen}`)\n      .join(' ');\n    let component = childrenArray.find(\n      (child) => child.props.dataElement === dataElement,\n    );\n\n    // duplicate code in HeaderItems.js, must clean up after 6.0\n    if (!component) {\n      const props = { ...item, mediaQueryClassName, className: childrenClassName };\n\n      if (type === 'toolButton') {\n        component = <ToolButton {...props} />;\n      }\n\n      if (type === 'toolGroupButton') {\n        component = <ToolGroupButton {...props} />;\n      }\n\n      if (type === 'toggleElementButton') {\n        component = <ToggleElementButton {...props} />;\n      }\n\n      if (type === 'actionButton') {\n        component = <ActionButton {...props} />;\n      }\n\n      if (type === 'statefulButton') {\n        component = <StatefulButton {...props} />;\n      }\n\n      if (type === 'customElement') {\n        component = <CustomElement {...props} />;\n      }\n\n      if (type === 'spacer' || type === 'divider') {\n        component = (\n          <div className={`${type} ${mediaQueryClassName}`} {...props} />\n        );\n      }\n    }\n\n    return component\n      ? React.cloneElement(component, {\n        key,\n      })\n      : null;\n  });\n};\n\nCustomizablePopup.propTypes = propTypes;\n\nexport default CustomizablePopup;\n", "import CustomizablePopup from './CustomizablePopup';\n\nexport default CustomizablePopup;"], "sourceRoot": ""}