{"version": 3, "sources": ["webpack:///./src/ui/src/components/Model3DModal/Model3DModal.scss?fe2b", "webpack:///./src/ui/src/components/Model3DModal/Model3DModal.scss", "webpack:///./src/ui/src/components/Model3DModal/Model3DModal.js", "webpack:///./src/ui/src/components/Model3DModal/Model3DModalContainer.js", "webpack:///./src/ui/src/components/Model3DModal/Model3DModalRedux.js", "webpack:///./src/ui/src/components/Model3DModal/index.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "Model3DModal", "fileInput", "urlInput", "error", "setError", "file", "setFile", "url", "setURL", "closeModal", "isDisabled", "isOpen", "t", "useTranslation", "useState", "typeOfInput", "setTypeOfInput", "base64", "setBase64", "urlInputRadio", "React", "createRef", "fileInputRadio", "draw3DAnnotation", "e", "model3DAnnotation", "Core", "Annotations", "Model3DAnnotation", "set3DData", "zoom", "core", "getZoom", "X", "layerX", "Y", "layerY", "<PERSON><PERSON><PERSON>", "Height", "Author", "getCurrentUser", "setPageNumber", "getCurrentPage", "addAnnotations", "drawAnnotationsFromList", "removeEventListener", "getExtension", "fileName", "split", "pop", "drawModel3DHandler", "preventDefault", "base64StrGenerator", "urlToBase64Str", "Promise", "resolve", "xhr", "XMLHttpRequest", "reader", "FileReader", "onloadend", "result", "readAsDataURL", "response", "open", "responseType", "send", "name", "setToolMode", "getViewerElement", "style", "cursor", "addEventListener", "modalClass", "classNames", "Modal", "closed", "handleFileChange", "event", "target", "files", "fileToBase64", "urlInputElement", "className", "type", "ref", "value", "onChange", "trim", "current", "handleURLChange", "onFocus", "click", "placeholder", "fileInputElement", "accept", "onSwipedUp", "onSwipedDown", "preventDefaultTouchmoveEvent", "data-element", "onMouseDown", "stopPropagation", "onSubmit", "Choice", "dataElement", "id", "radio", "label", "center", "url<PERSON><PERSON>r", "fileError", "<PERSON><PERSON>", "onClick", "Model3DModalContainer", "props", "close3DModal", "rest", "useCallback", "newProps", "Model3DModalRedux", "dispatch", "useDispatch", "useSelector", "state", "selectors", "isElementDisabled", "DataElements", "MODEL3D_MODAL", "isElementOpen", "useEffect", "onToolUpdated", "actions", "closeElement", "focus", "closeElements", "PRINT_MODAL", "LOADING_MODAL", "PROGRESS_MODAL", "ERROR_MODAL", "OPEN_FILE_MODAL"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,miPAAoiP,KAG7jP0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,wuBCTvB,8lGAAA3B,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,0fAAAA,EAAA,EAAAA,EAAA,iBAAAA,IAAA,uBAAAA,GAAA,UAAAA,GAAA,GAAAA,EAAA,4gCAAAA,GAAA,gEAAAA,GAAA,0JAAAA,EAAA,6FAAAA,GAAA,mIAAAA,IAAA,4SAAAA,IAAA,2OAAAA,EAAA,iBAAAA,EAAA,EAAAA,IAAA,EAAAA,GAAA,EAAAA,GAAA,SASA,IAoLe4B,EApLM,SAAH,GAYZ,IAXJC,EAAS,EAATA,UACAC,EAAQ,EAARA,SACAC,EAAK,EAALA,MACAC,EAAQ,EAARA,SACAC,EAAI,EAAJA,KACAC,EAAO,EAAPA,QACAC,EAAG,EAAHA,IACAC,EAAM,EAANA,OACAC,EAAU,EAAVA,WACAC,EAAU,EAAVA,WACAC,EAAM,EAANA,OAEOC,EAAqB,EAAhBC,cAAgB,GAApB,GAE6C,IAAfC,mBAAS,OAAM,GAA9CC,EAAW,KAAEC,EAAc,KACM,IAAZF,mBAAS,IAAG,GAAjCG,EAAM,KAAEC,EAAS,KAClBC,EAAgBC,IAAMC,YACtBC,EAAiBF,IAAMC,YAEvBE,EAAmB,SAAnBA,EAAoBC,GACxB,IAGMC,EAAoB,IAAIlD,OAAOmD,KAAKC,YAAYC,kBACtDH,EAAkBI,UAAUZ,GACR,QAAhBF,GACFU,EAAkBI,UAAUtB,EAAK,OAGnC,IAAMuB,EAAOC,IAAKC,UAClBP,EAAkBQ,EAAIT,EAAEU,OAASJ,EACjCL,EAAkBU,EAAIX,EAAEY,OAASN,EACjCL,EAAkBY,MAZD,IAajBZ,EAAkBa,OAZD,IAajBb,EAAkBc,OAASR,IAAKS,iBAChCf,EAAkBgB,cAAcV,IAAKW,kBACrCX,IAAKY,eAAe,CAAClB,IACrBM,IAAKa,wBAAwB,CAACnB,IAC9BM,IAAKc,oBAAoB,QAAStB,IAG9BuB,EAAe,SAACC,GACpB,OAAOA,EAASC,MAAM,KAAKC,OAGvBC,EAAkB,6BAAG,WAAO1B,GAAC,uEACd,GAAnBA,EAAE2B,iBAEkB,QAAhBpC,EAAqB,oBAEG,QAAtB+B,EAAavC,GAAc,gBACuC,OAApEH,EAAS,EAAD,KAAMD,GAAU,CAAE,SAAYS,EAAE,0BAA4B,0BAuB7D,OApBHwC,EAAkB,6BAAG,WAAO7C,GAAG,uEAiBnC,OAfM8C,EAAiB,SAAC9C,GACtB,OAAO,IAAI+C,SAAQ,SAASC,GAC1B,IAAMC,EAAM,IAAIC,eAChBD,EAAI9D,OAAM,YAAG,oFAIT,OAHIgE,EAAS,IAAIC,YACZC,UAAY,WACjBL,EAAQG,EAAOG,SACf,SACIH,EAAOI,cAAcN,EAAIO,UAAS,2CAE1CP,EAAIQ,KAAK,MAAOzD,GAChBiD,EAAIS,aAAe,OACnBT,EAAIU,WAGR,SACab,EAAe9C,GAAI,mFACjC,gBAnBuB,2CAoBxBW,EAAS,SAAOkC,EAAmB7C,GAAI,6DACd,SAAhBQ,EAAsB,oBACC,QAA5B+B,EAAazC,EAAK8D,MAAe,iBACiC,OAApE/D,EAAS,EAAD,KAAMD,GAAU,CAAE,SAAYS,EAAE,0BAA4B,2BAKxEH,IACAsB,IAAKqC,YAAY,OAEFrC,IAAKsC,mBACbC,MAAMC,OAAS,YACtBxC,IAAKyC,iBAAiB,QAASjD,GAAkB,4CAClD,gBA3CuB,sCA6ClBkD,EAAaC,IAAW,CAC5BC,OAAO,EACP3E,cAAc,EACdgE,KAAMrD,EACNiE,QAASjE,IAGLkE,EAAgB,6BAAG,WAAOC,GAAK,yEAUjC,OATF9D,EAAe,QACTX,EAAOyE,EAAMC,OAAOC,MAAM,GAChC1E,EAAQD,GACF4E,EAAe,SAAC5E,GAAI,OAAK,IAAIiD,SAAQ,WACzC,IAAMI,EAAS,IAAIC,WACnBD,EAAOI,cAAczD,GACrBqD,EAAOhE,OAAS,WACd,OAAOwB,EAAUwC,EAAOG,aAE1B,SACIoB,EAAa5E,GAAK,OACxBD,EAAS,IACTI,EAAO,IAAI,2CACZ,gBAdqB,sCAuBhB0E,EACJ,2BACEC,UAAU,WACVC,KAAK,MACLC,IAAKnF,EACLoF,MAAO/E,EACPgF,SAAU,SAAC/D,GAAC,OAbQ,SAACjB,GACvBS,EAAe,OACfR,EAAOD,EAAIiF,QACXpF,EAAS,IACTH,EAAUwF,QAAQH,MAAQ,KASPI,CAAgBlE,EAAEuD,OAAOO,QAC1CK,QAAS,kBAAMxE,EAAcsE,QAAQG,SACrCC,YAAajF,EAAE,sBAIbkF,EACJ,2BACEV,KAAK,OACLC,IAAKpF,EACLsF,SAAUV,EACVkB,OAAO,OACPJ,QAAS,kBAAMrE,EAAemE,QAAQG,WAI1C,OAAOlF,EAAa,KAClB,kBAAC,IAAS,CAACsF,WAAYvF,EAAYwF,aAAcxF,EAAYyF,8BAA4B,GACvF,yBAAKf,UAAWV,EAAY0B,eAAa,eAAeC,YAAa3F,GACnE,yBAAK0E,UAAU,YAAYiB,YAAa,SAAC5E,GAAC,OAAKA,EAAE6E,oBAC/C,yBAAKlB,UAAU,oBACf,0BAAMmB,SAAUpD,GACd,yBAAKiC,UAAU,OAAOvE,EAAE,gCACxB,kBAAC2F,EAAA,EAAM,CACLC,YAAY,mBACZC,GAAG,qBACHtC,KAAK,UACLgB,UAAU,eACVE,IAAKlE,EACLuF,OAAK,EACLC,MAAOzB,EACP0B,QAAM,KAEPzG,aAAK,EAALA,EAAO0G,WAAY,uBAAG1B,UAAU,SAAQ,KAAGhF,EAAM0G,UAClD,kBAACN,EAAA,EAAM,CACLC,YAAY,wBACZC,GAAG,qBACHtC,KAAK,UACLgB,UAAU,eACVE,IAAK/D,EACLoF,OAAK,EACLC,MAAOb,EACPc,QAAM,KAEPzG,aAAK,EAALA,EAAO2G,YAAa,uBAAG3B,UAAU,SAAQ,KAAGhF,EAAM2G,WACnD,kBAACC,EAAA,EAAM,CAACP,YAAY,mBAAmBG,MAAO/F,EAAE,eAAgBoG,QAAS9D,S,8mDCpKtE+D,MAff,SAA+BC,GAC7B,IAAQC,EAA0BD,EAA1BC,aAAiBC,EAAI,EAAKF,EAAK,GAEjCzG,EAAa4G,uBAAY,WAC7BF,MACC,CAACA,IAEEG,EAAW,OACZF,GAAI,IACP3G,eAGF,OAAO,kBAAC,EAAiB6G,I,ywEC0DZC,MAjEf,SAA2BL,GACzB,IAAMM,EAAWC,cACiB,IAAZ3G,mBAAS,IAAG,GAA3BP,EAAG,KAAEC,EAAM,KACkB,IAAZM,mBAAS,IAAG,GAA7BT,EAAI,KAAEC,EAAO,KACmD,IAA7CQ,mBAAS,CAAE,UAAa,GAAI,SAAY,KAAK,GAAhEX,EAAK,KAAEC,EAAQ,KAChBH,EAAYmB,IAAMC,YAClBnB,EAAWkB,IAAMC,YAMrB,IAH2BqG,aAAY,SAACC,GAAK,MAAK,CAClDC,IAAUC,kBAAkBF,EAAOG,IAAaC,eAChDH,IAAUI,cAAcL,EAAOG,IAAaC,mBAC5C,GAHKrH,EAAU,KAAEC,EAAM,KAiBzBsH,qBAAU,WACR,IAAMC,EAAgB,WACpBV,EAASW,IAAQC,aAAaN,IAAaC,iBAG7C,OADAhG,IAAKyC,iBAAiB,cAAe0D,GAC9B,kBAAMnG,IAAKc,oBAAoB,cAAeqF,MACpD,IAEHD,qBAAU,WACJtH,IACFT,EAASuF,QAAQ4C,QACjBb,EAASW,IAAQG,cAAc,CAC7BR,IAAaS,YACbT,IAAaU,cACbV,IAAaW,eACbX,IAAaY,YACbZ,IAAaa,sBAGhB,CAACnB,EAAU7G,IAGd,IAAM2G,EAAW,OACZJ,GAAK,IACRxG,aACAC,SACAwG,aAtCmB,WACnBK,EAASW,IAAQC,aAAaN,IAAaC,gBAC3CvH,EAAO,IACPF,EAAQ,IACRF,EAAS,IACLH,EAAUwF,UACZxF,EAAUwF,QAAQH,MAAQ,OAiC5BrF,YACAC,WACAC,QACAC,WACAC,OACAC,UACAC,MACAC,WAEF,OAAO,kBAAC,EAA0B8G,ICpErBC", "file": "chunks/chunk.69.js", "sourcesContent": ["var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./Model3DModal.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".open.Model3DModal{visibility:visible}.closed.Model3DModal{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.Model3DModal .footer .modal-button.confirm:hover{background:var(--primary-button-hover);border-color:var(--primary-button-hover);color:var(--gray-0)}.Model3DModal .footer .modal-button.confirm{background:var(--primary-button);border-color:var(--primary-button);color:var(--primary-button-text)}.Model3DModal .footer .modal-button.confirm.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.Model3DModal .footer .modal-button.confirm.disabled span{color:var(--primary-button-text)}.Model3DModal .footer .modal-button.cancel:hover,.Model3DModal .footer .modal-button.secondary-btn-custom:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6)}.Model3DModal .footer .modal-button.cancel,.Model3DModal .footer .modal-button.secondary-btn-custom{border:none;box-shadow:inset 0 0 0 1px var(--primary-button);color:var(--primary-button)}.Model3DModal .footer .modal-button.cancel.disabled,.Model3DModal .footer .modal-button.secondary-btn-custom.disabled{cursor:default;border:none;box-shadow:inset 0 0 0 1px rgba(43,115,171,.5);color:rgba(43,115,171,.5)}.Model3DModal .footer .modal-button.cancel.disabled span,.Model3DModal .footer .modal-button.secondary-btn-custom.disabled span{color:rgba(43,115,171,.5)}.Model3DModal{position:fixed;left:0;bottom:0;z-index:100;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background:var(--modal-negative-space)}.Model3DModal .modal-container .wrapper .modal-content{padding:10px}.Model3DModal .footer{display:flex;flex-direction:row;justify-content:flex-end;width:100%;margin-top:13px}.Model3DModal .footer.modal-footer{padding:16px;margin:0;border-top:1px solid var(--divider)}.Model3DModal .footer .modal-button{display:flex;justify-content:center;align-items:center;padding:6px 18px;margin:8px 0 0;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;height:30px;cursor:pointer}.Model3DModal .footer .modal-button.confirm{margin-left:4px}.Model3DModal .footer .modal-button.secondary-btn-custom{border-radius:4px;padding:2px 20px 4px;cursor:pointer}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Model3DModal .footer .modal-button{padding:23px 8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Model3DModal .footer .modal-button{padding:23px 8px}}.Model3DModal .swipe-indicator{background:var(--swipe-indicator-bg);border-radius:2px;height:4px;width:38px;position:absolute;top:12px;margin-left:auto;margin-right:auto;left:0;right:0}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Model3DModal .swipe-indicator{display:none}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .Model3DModal .swipe-indicator{display:none}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Model3DModal .swipe-indicator{width:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Model3DModal .swipe-indicator{width:32px}}.Model3DModal .container{overflow-y:auto;max-height:100%}@media(max-height:500px){.App:not(.is-web-component) .Model3DModal .container,.Model3DModal .App:not(.is-web-component) .container{overflow:auto;max-height:100%}}@container (max-height: 500px){.App.is-web-component .Model3DModal .container,.Model3DModal .App.is-web-component .container{overflow:auto;max-height:100%}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Model3DModal .container,.Model3DModal .App:not(.is-in-desktop-only-mode):not(.is-web-component) .container{width:100%;position:fixed;left:0;bottom:0;padding-top:4px;min-width:100px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Model3DModal .container,.Model3DModal .App.is-web-component:not(.is-in-desktop-only-mode) .container{width:100%;position:fixed;left:0;bottom:0;padding-top:4px;min-width:100px}}.Model3DModal .container{display:flex;flex-direction:column;justify-content:center;align-items:center;width:500px;border-radius:4px;padding:8px;box-shadow:0 0 3px 0 var(--document-box-shadow);background:var(--component-background)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Model3DModal .container{padding:24px 24px 16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Model3DModal .container{padding:24px 24px 16px}}.Model3DModal .container .inputWrapper>label{width:100%}.Model3DModal .container .divider{height:1px;width:100%;background:var(--divider);margin:16px 0}.Model3DModal .container input{margin-top:8px;width:100%;height:30px;border:1px solid var(--border);border-radius:4px;color:var(--text-color);padding:4px 42px 6px 8px}.Model3DModal .container input:focus{outline:none;border:1px solid var(--focus-border)}.Model3DModal .container input::-moz-placeholder{color:var(--placeholder-text)}.Model3DModal .container input::placeholder{color:var(--placeholder-text)}.Model3DModal .container input::-webkit-inner-spin-button,.Model3DModal .container input::-webkit-outer-spin-button{-webkit-appearance:none;margin:0}.Model3DModal .container input[type=number]{-moz-appearance:textfield}.Model3DModal .container form{width:100%}.Model3DModal .container .error{width:100%;color:red;margin:5px 0}.Model3DModal .container .Button{display:flex;justify-content:center;align-items:center;color:var(--primary-button-text);font-weight:600;padding:6px 18px;margin-top:8px;margin-left:5px;width:auto;width:-moz-fit-content;width:fit-content;background:var(--primary-button);border-radius:4px;height:30px;cursor:pointer}.Model3DModal .container .Button:hover{background:var(--primary-button-hover)}.Model3DModal .container .Button.disabled{background:var(--gray-6);border-color:var(--gray-6);cursor:not-allowed}.model-3D-btn{padding:0;border:none;background-color:transparent;border:1px solid var(--icon-color);width:162px;height:26px;border-radius:4px;display:flex;align-items:center;justify-content:center;cursor:pointer;margin-right:8px}:host(:not([data-tabbing=true])) .model-3D-btn,html:not([data-tabbing=true]) .model-3D-btn{outline:none}.model-3D-btn img{max-width:100%;max-height:100%}.model-3D-btn.interactable:hover{background:var(--tools-overlay-button-hover)}.model-3D-btn.active,.model-3D-btn.interactable.active{background:var(--tools-overlay-button-active)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .model-3D-btn.add-btn{width:auto;padding-left:0;width:190px;height:30px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .model-3D-btn.add-btn{width:auto;padding-left:0;width:190px;height:30px}}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React, { useState } from 'react';\r\nimport classNames from 'classnames';\r\nimport { useTranslation } from 'react-i18next';\r\nimport Button from 'components/Button';\r\nimport core from 'core';\r\nimport Choice from '../Choice/Choice';\r\n\r\nimport { Swipeable } from 'react-swipeable';\r\nimport './Model3DModal.scss';\r\n\r\nconst Model3DModal = ({\r\n  fileInput,\r\n  urlInput,\r\n  error,\r\n  setError,\r\n  file,\r\n  setFile,\r\n  url,\r\n  setURL,\r\n  closeModal,\r\n  isDisabled,\r\n  isOpen,\r\n}) => {\r\n  const [t] = useTranslation();\r\n\r\n  const [typeOfInput, setTypeOfInput] = useState('url');\r\n  const [base64, setBase64] = useState('');\r\n  const urlInputRadio = React.createRef();\r\n  const fileInputRadio = React.createRef();\r\n\r\n  const draw3DAnnotation = (e) => {\r\n    const defaultW = 300;\r\n    const defaultH = 300;\r\n\r\n    const model3DAnnotation = new window.Core.Annotations.Model3DAnnotation();\r\n    model3DAnnotation.set3DData(base64);\r\n    if (typeOfInput === 'url') {\r\n      model3DAnnotation.set3DData(url, 'url');\r\n    }\r\n    // reset x and y position with current zoom level\r\n    const zoom = core.getZoom();\r\n    model3DAnnotation.X = e.layerX / zoom;\r\n    model3DAnnotation.Y = e.layerY / zoom;\r\n    model3DAnnotation.Width = defaultW;\r\n    model3DAnnotation.Height = defaultH;\r\n    model3DAnnotation.Author = core.getCurrentUser();\r\n    model3DAnnotation.setPageNumber(core.getCurrentPage());\r\n    core.addAnnotations([model3DAnnotation]);\r\n    core.drawAnnotationsFromList([model3DAnnotation]);\r\n    core.removeEventListener('click', draw3DAnnotation);\r\n  };\r\n\r\n  const getExtension = (fileName) => {\r\n    return fileName.split('.').pop();\r\n  };\r\n\r\n  const drawModel3DHandler = async (e) => {\r\n    e.preventDefault();\r\n\r\n    if (typeOfInput === 'url') {\r\n      // convert url to base 64\r\n      if (getExtension(url) !== 'glb') {\r\n        setError({ ...error, ...{ 'urlError': t('Model3D.formatError') } });\r\n        return;\r\n      }\r\n      const base64StrGenerator = async (url) => {\r\n        // build base64 string from the given file url\r\n        const urlToBase64Str = (url) => {\r\n          return new Promise(function(resolve) {\r\n            const xhr = new XMLHttpRequest();\r\n            xhr.onload = async function() {\r\n              const reader = new FileReader();\r\n              reader.onloadend = function() {\r\n                resolve(reader.result);\r\n              };\r\n              await reader.readAsDataURL(xhr.response);\r\n            };\r\n            xhr.open('GET', url);\r\n            xhr.responseType = 'blob';\r\n            xhr.send();\r\n          });\r\n        };\r\n        // eslint-disable-next-line no-return-await\r\n        return await urlToBase64Str(url);\r\n      };\r\n      setBase64(await base64StrGenerator(url));\r\n    } else if (typeOfInput === 'file') {\r\n      if (getExtension(file.name) !== 'glb') {\r\n        setError({ ...error, ...{ 'urlError': t('Model3D.formatError') } });\r\n        return;\r\n      }\r\n    }\r\n\r\n    closeModal();\r\n    core.setToolMode('Pan');\r\n    // eslint-disable-next-line no-undef\r\n    const viewer = core.getViewerElement();\r\n    viewer.style.cursor = 'crosshair';\r\n    core.addEventListener('click', draw3DAnnotation);\r\n  };\r\n\r\n  const modalClass = classNames({\r\n    Modal: true,\r\n    Model3DModal: true,\r\n    open: isOpen,\r\n    closed: !isOpen,\r\n  });\r\n\r\n  const handleFileChange = async (event) => {\r\n    setTypeOfInput('file');\r\n    const file = event.target.files[0];\r\n    setFile(file);\r\n    const fileToBase64 = (file) => new Promise(() => {\r\n      const reader = new FileReader();\r\n      reader.readAsDataURL(file);\r\n      reader.onload = () => {\r\n        return setBase64(reader.result);\r\n      };\r\n    });\r\n    await fileToBase64(file);\r\n    setError({});\r\n    setURL('');\r\n  };\r\n\r\n  const handleURLChange = (url) => {\r\n    setTypeOfInput('url');\r\n    setURL(url.trim());\r\n    setError('');\r\n    fileInput.current.value = null;\r\n  };\r\n\r\n  const urlInputElement = (\r\n    <input\r\n      className=\"urlInput\"\r\n      type=\"url\"\r\n      ref={urlInput}\r\n      value={url}\r\n      onChange={(e) => handleURLChange(e.target.value)}\r\n      onFocus={() => urlInputRadio.current.click()}\r\n      placeholder={t('Model3D.enterurl')}\r\n    />\r\n  );\r\n\r\n  const fileInputElement = (\r\n    <input\r\n      type=\"file\"\r\n      ref={fileInput}\r\n      onChange={handleFileChange}\r\n      accept=\".glb\"\r\n      onFocus={() => fileInputRadio.current.click()}\r\n    />\r\n  );\r\n\r\n  return isDisabled ? null : (\r\n    <Swipeable onSwipedUp={closeModal} onSwipedDown={closeModal} preventDefaultTouchmoveEvent>\r\n      <div className={modalClass} data-element=\"Model3DModal\" onMouseDown={closeModal}>\r\n        <div className=\"container\" onMouseDown={(e) => e.stopPropagation()}>\r\n          <div className=\"swipe-indicator\" />\r\n          <form onSubmit={drawModel3DHandler}>\r\n            <div className=\"col\">{t('Model3D.enterurlOrLocalFile')}</div>\r\n            <Choice\r\n              dataElement=\"3DAnnotationLink\"\r\n              id=\"3D-annotation-link\"\r\n              name=\"3DInput\"\r\n              className=\"inputWrapper\"\r\n              ref={urlInputRadio}\r\n              radio\r\n              label={urlInputElement}\r\n              center\r\n            />\r\n            {error?.urlError && <p className=\"error\">* {error.urlError}</p>}\r\n            <Choice\r\n              dataElement=\"3DAnnotationLocalFile\"\r\n              id=\"3D-annotation-file\"\r\n              name=\"3DInput\"\r\n              className=\"inputWrapper\"\r\n              ref={fileInputRadio}\r\n              radio\r\n              label={fileInputElement}\r\n              center\r\n            />\r\n            {error?.fileError && <p className=\"error\">* {error.fileError}</p>}\r\n            <Button dataElement=\"linkSubmitButton\" label={t('action.draw')} onClick={drawModel3DHandler} />\r\n          </form>\r\n        </div>\r\n      </div>\r\n    </Swipeable>\r\n  );\r\n};\r\n\r\nexport default Model3DModal;\r\n", "import React, { useCallback } from 'react';\r\nimport Model3DModal from './Model3DModal';\r\n\r\nfunction Model3DModalContainer(props) {\r\n  const { close3DModal, ...rest } = props;\r\n\r\n  const closeModal = useCallback(() => {\r\n    close3DModal();\r\n  }, [close3DModal]);\r\n\r\n  const newProps = {\r\n    ...rest,\r\n    closeModal,\r\n  };\r\n\r\n  return <Model3DModal {...newProps} />;\r\n}\r\n\r\nexport default Model3DModalContainer;\r\n", "import React, { useEffect, useState } from 'react';\nimport selectors from 'selectors';\nimport actions from 'actions';\nimport core from 'core';\nimport { useDispatch, useSelector } from 'react-redux';\nimport Model3DModalContainer from './Model3DModalContainer';\nimport DataElements from 'constants/dataElement';\n\nfunction Model3DModalRedux(props) {\n  const dispatch = useDispatch();\n  const [url, setURL] = useState('');\n  const [file, setFile] = useState({});\n  const [error, setError] = useState({ 'fileError': '', 'urlError': '' });\n  const fileInput = React.createRef();\n  const urlInput = React.createRef();\n\n\n  const [isDisabled, isOpen] = useSelector((state) => [\n    selectors.isElementDisabled(state, DataElements.MODEL3D_MODAL),\n    selectors.isElementOpen(state, DataElements.MODEL3D_MODAL),\n  ]);\n\n  const close3DModal = () => {\n    dispatch(actions.closeElement(DataElements.MODEL3D_MODAL));\n    setURL('');\n    setFile({});\n    setError({});\n    if (fileInput.current) {\n      fileInput.current.value = null;\n    }\n  };\n\n\n  // Hack to close modal if hotkey to open other tool is used.\n  useEffect(() => {\n    const onToolUpdated = () => {\n      dispatch(actions.closeElement(DataElements.MODEL3D_MODAL));\n    };\n    core.addEventListener('toolUpdated', onToolUpdated);\n    return () => core.removeEventListener('toolUpdated', onToolUpdated);\n  }, []);\n\n  useEffect(() => {\n    if (isOpen) {\n      urlInput.current.focus();\n      dispatch(actions.closeElements([\n        DataElements.PRINT_MODAL,\n        DataElements.LOADING_MODAL,\n        DataElements.PROGRESS_MODAL,\n        DataElements.ERROR_MODAL,\n        DataElements.OPEN_FILE_MODAL,\n      ]));\n    }\n  }, [dispatch, isOpen]);\n\n\n  const newProps = {\n    ...props,\n    isDisabled,\n    isOpen,\n    close3DModal,\n    fileInput,\n    urlInput,\n    error,\n    setError,\n    file,\n    setFile,\n    url,\n    setURL\n  };\n  return <Model3DModalContainer {...newProps} />;\n}\n\nexport default Model3DModalRedux;\n", "import Model3DModalRedux from './Model3DModalRedux';\n\nexport default Model3DModalRedux;"], "sourceRoot": ""}