(window.webpackJsonp=window.webpackJsonp||[]).push([[51],{1807:function(e,t,o){var n=o(32),r=o(1808);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.i,r,""]]);var i={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,o=document){const n=[];return o.querySelectorAll(t).forEach(e=>n.push(e)),o.querySelectorAll("*").forEach(o=>{o.shadowRoot&&n.push(...e(t,o.shadowRoot))}),n}("apryse-webviewer"));const o=[];for(let n=0;n<t.length;n++){const r=t[n];if(0===n)r.shadowRoot.appendChild(e),e.onload=function(){o.length>0&&o.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),o.push(t)}}},singleton:!1};n(r,i);e.exports=r.locals||{}},1808:function(e,t,o){(t=e.exports=o(33)(!1)).push([e.i,':host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.header-footer-edit-ui{position:absolute;height:46px;width:100%;align-items:center;display:none}.header-footer-edit-ui.header-edit-ui{top:143px}.header-footer-edit-ui.footer-edit-ui{bottom:143px}.header-footer-edit-ui.active{display:flex}.header-footer-edit-ui .box-shadow-div{width:100%;height:100%;overflow:hidden;position:absolute;display:flex;align-items:center}.header-footer-edit-ui .box-shadow-div:after{content:"";position:absolute;width:100%;height:40px;padding:3px 0;background-color:var(--gray-0);box-shadow:0 0 3px 0 var(--gray-7);z-index:-1}.header-footer-edit-ui .label{color:var(--gray-8);font-size:.8125rem;font-weight:700;margin-left:16px}.header-footer-edit-ui .options-dropdown-container{position:absolute;right:16px}.header-footer-edit-ui .options-dropdown-container .options-button{color:var(--blue-5);padding:8px 32px 8px 8px;background:transparent;width:auto;border:none;cursor:pointer}.header-footer-edit-ui .options-dropdown-container .options-button .Icon{width:16px;height:16px;position:absolute;right:9.5px;top:0;bottom:0;margin:auto;color:var(--blue-5)}.header-footer-edit-ui .options-dropdown-container .options-button.active,.header-footer-edit-ui .options-dropdown-container .options-button.active .Icon,.header-footer-edit-ui .options-dropdown-container .options-button:hover,.header-footer-edit-ui .options-dropdown-container .options-button:hover .Icon{color:var(--blue-6)}.header-footer-edit-ui .options-dropdown-container .options-button.active .Icon{transform:rotate(180deg)}.header-footer-edit-ui .options-dropdown-container .Dropdown__items{padding:4px 0;min-width:157px}.header-footer-edit-ui .options-dropdown-container .Dropdown__items .Dropdown__item{height:32px;padding:0 10px 0 40px;position:relative;width:auto}.header-footer-edit-ui .options-dropdown-container .Dropdown__items .Dropdown__item:first-child{margin-bottom:9px}.header-footer-edit-ui .options-dropdown-container .Dropdown__items .Dropdown__item .Icon{position:absolute;left:10px;fill:var(--gray-7);top:0;bottom:0;margin:auto}.header-footer-edit-ui .options-dropdown-container .Dropdown__items .Divider{flex-basis:100%;width:100%;height:1px;margin:0;position:absolute;bottom:-5px;left:0}',""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1809:function(e,t,o){var n=o(32),r=o(1810);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.i,r,""]]);var i={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,o=document){const n=[];return o.querySelectorAll(t).forEach(e=>n.push(e)),o.querySelectorAll("*").forEach(o=>{o.shadowRoot&&n.push(...e(t,o.shadowRoot))}),n}("apryse-webviewer"));const o=[];for(let n=0;n<t.length;n++){const r=t[n];if(0===n)r.shadowRoot.appendChild(e),e.onload=function(){o.length>0&&o.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),o.push(t)}}},singleton:!1};n(r,i);e.exports=r.locals||{}},1810:function(e,t,o){(e.exports=o(33)(!1)).push([e.i,".HeaderFooterControlsOverlay{position:absolute;top:0;left:0;width:100%;height:100%;z-index:501}",""])},1980:function(e,t,o){"use strict";o.r(t);o(38);var n=o(0),r=o.n(n),i=o(116),a=o(21),c=(o(35),o(23),o(8),o(90),o(19),o(11),o(13),o(14),o(10),o(9),o(12),o(16),o(15),o(20),o(18),o(26),o(27),o(25),o(22),o(57),o(63),o(64),o(65),o(66),o(36),o(39),o(24),o(40),o(62),o(6)),u=o(428),l=o(42),s=o(84),d=o(4),p=o.n(d),f=o(2),h=o(5),m=o(80),v=o(17),y=o.n(v),w=o(1);o(1807);function g(e){return(g="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function b(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */b=function(){return e};var e={},t=Object.prototype,o=t.hasOwnProperty,n=Object.defineProperty||function(e,t,o){e[t]=o.value},r="function"==typeof Symbol?Symbol:{},i=r.iterator||"@@iterator",a=r.asyncIterator||"@@asyncIterator",c=r.toStringTag||"@@toStringTag";function u(e,t,o){return Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,o){return e[t]=o}}function l(e,t,o,r){var i=t&&t.prototype instanceof p?t:p,a=Object.create(i.prototype),c=new k(r||[]);return n(a,"_invoke",{value:_(e,o,c)}),a}function s(e,t,o){try{return{type:"normal",arg:e.call(t,o)}}catch(e){return{type:"throw",arg:e}}}e.wrap=l;var d={};function p(){}function f(){}function h(){}var m={};u(m,i,(function(){return this}));var v=Object.getPrototypeOf,y=v&&v(v(N([])));y&&y!==t&&o.call(y,i)&&(m=y);var w=h.prototype=p.prototype=Object.create(m);function x(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function E(e,t){var r;n(this,"_invoke",{value:function(n,i){function a(){return new t((function(r,a){!function n(r,i,a,c){var u=s(e[r],e,i);if("throw"!==u.type){var l=u.arg,d=l.value;return d&&"object"==g(d)&&o.call(d,"__await")?t.resolve(d.__await).then((function(e){n("next",e,a,c)}),(function(e){n("throw",e,a,c)})):t.resolve(d).then((function(e){l.value=e,a(l)}),(function(e){return n("throw",e,a,c)}))}c(u.arg)}(n,i,r,a)}))}return r=r?r.then(a,a):a()}})}function _(e,t,o){var n="suspendedStart";return function(r,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===r)throw i;return j()}for(o.method=r,o.arg=i;;){var a=o.delegate;if(a){var c=O(a,o);if(c){if(c===d)continue;return c}}if("next"===o.method)o.sent=o._sent=o.arg;else if("throw"===o.method){if("suspendedStart"===n)throw n="completed",o.arg;o.dispatchException(o.arg)}else"return"===o.method&&o.abrupt("return",o.arg);n="executing";var u=s(e,t,o);if("normal"===u.type){if(n=o.done?"completed":"suspendedYield",u.arg===d)continue;return{value:u.arg,done:o.done}}"throw"===u.type&&(n="completed",o.method="throw",o.arg=u.arg)}}}function O(e,t){var o=t.method,n=e.iterator[o];if(void 0===n)return t.delegate=null,"throw"===o&&e.iterator.return&&(t.method="return",t.arg=void 0,O(e,t),"throw"===t.method)||"return"!==o&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+o+"' method")),d;var r=s(n,e.iterator,t.arg);if("throw"===r.type)return t.method="throw",t.arg=r.arg,t.delegate=null,d;var i=r.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,d):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,d)}function L(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function A(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function k(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(L,this),this.reset(!0)}function N(e){if(e){var t=e[i];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,r=function t(){for(;++n<e.length;)if(o.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=void 0,t.done=!0,t};return r.next=r}}return{next:j}}function j(){return{value:void 0,done:!0}}return f.prototype=h,n(w,"constructor",{value:h,configurable:!0}),n(h,"constructor",{value:f,configurable:!0}),f.displayName=u(h,c,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===f||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,h):(e.__proto__=h,u(e,c,"GeneratorFunction")),e.prototype=Object.create(w),e},e.awrap=function(e){return{__await:e}},x(E.prototype),u(E.prototype,a,(function(){return this})),e.AsyncIterator=E,e.async=function(t,o,n,r,i){void 0===i&&(i=Promise);var a=new E(l(t,o,n,r),i);return e.isGeneratorFunction(o)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},x(w),u(w,c,"Generator"),u(w,i,(function(){return this})),u(w,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),o=[];for(var n in t)o.push(n);return o.reverse(),function e(){for(;o.length;){var n=o.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},e.values=N,k.prototype={constructor:k,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(A),!e)for(var t in this)"t"===t.charAt(0)&&o.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(o,n){return a.type="throw",a.arg=e,t.next=o,n&&(t.method="next",t.arg=void 0),!!n}for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var c=o.call(i,"catchLoc"),u=o.call(i,"finallyLoc");if(c&&u){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&o.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,d):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),d},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var o=this.tryEntries[t];if(o.finallyLoc===e)return this.complete(o.completion,o.afterLoc),A(o),d}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var o=this.tryEntries[t];if(o.tryLoc===e){var n=o.completion;if("throw"===n.type){var r=n.arg;A(o)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,o){return this.delegate={iterator:N(e),resultName:t,nextLoc:o},"next"===this.method&&(this.arg=void 0),d}},e}function x(e,t,o,n,r,i,a){try{var c=e[i](a),u=c.value}catch(e){return void o(e)}c.done?t(u):Promise.resolve(u).then(n,r)}function E(e,t,o){return(t=function(e){var t=function(e,t){if("object"!==g(e)||null===e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var n=o.call(e,t||"default");if("object"!==g(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===g(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}function _(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var o=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=o){var n,r,i,a,c=[],u=!0,l=!1;try{if(i=(o=o.call(e)).next,0===t){if(Object(o)!==o)return;u=!1}else for(;!(u=(n=i.call(o)).done)&&(c.push(n.value),c.length!==t);u=!0);}catch(e){l=!0,r=e}finally{try{if(!u&&null!=o.return&&(a=o.return(),Object(a)!==a))return}finally{if(l)throw r}}return c}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return O(e,t);var o=Object.prototype.toString.call(e).slice(8,-1);"Object"===o&&e.constructor&&(o=e.constructor.name);if("Map"===o||"Set"===o)return Array.from(e);if("Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o))return O(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function O(e,t){(null==t||t>e.length)&&(t=e.length);for(var o=0,n=new Array(t);o<t;o++)n[o]=e[o];return n}var L={type:p.a.oneOf(["header","footer"]),pageNumber:p.a.number,isActive:p.a.bool},A=function(e){var t,o=e.type,i=e.pageNumber,a=e.isActive,d=_(Object(u.a)(),1)[0],p=Object(c.d)(),v=Object(n.useRef)(),g="".concat(o,"-options-dropdown-").concat(i),O="".concat(o,"-edit-ui-").concat(i),L=y()((E(t={"header-footer-edit-ui":!0},"".concat(o,"-edit-ui"),!0),E(t,"active",a),t));Object(n.useEffect)((function(){var e=function(e){e.stopPropagation()};return["click","mousedown","mouseup","mousemove","mouseenter","mouseleave"].forEach((function(t){var o;null===(o=v.current)||void 0===o||o.addEventListener(t,e)})),function(){["click","mousedown","mouseup","mousemove","mouseenter","mouseleave"].forEach((function(t){var o;null===(o=v.current)||void 0===o||o.removeEventListener(t,e)}))}}),[]);var A=[{label:d("officeEditor.pageOptions"),key:"page-options",icon:"ic-edit-page",onClick:function(){p(f.a.openElement(h.a.HEADER_FOOTER_OPTIONS_MODAL))}},{label:d("header"===o?"officeEditor.removeHeader":"officeEditor.removeFooter"),key:"remove-".concat(o),icon:"ic-delete-page",onClick:function(){return"header"===o?w.a.getOfficeEditor().removeHeaders(i):"footer"===o?w.a.getOfficeEditor().removeFooters(i):void 0}}],k=function(){var e,t=(e=b().mark((function e(t){var o;return b().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return o=A.find((function(e){return e.key===t})),e.next=3,null==o?void 0:o.onClick();case 3:case"end":return e.stop()}}),e)})),function(){var t=this,o=arguments;return new Promise((function(n,r){var i=e.apply(t,o);function a(e){x(i,n,r,a,c,"next",e)}function c(e){x(i,n,r,a,c,"throw",e)}a(void 0)}))});return function(e){return t.apply(this,arguments)}}();return r.a.createElement("div",{className:L,id:O},r.a.createElement("div",{className:"box-shadow-div",ref:v}),r.a.createElement("div",{className:"label"},d("officeEditor.".concat(o))),r.a.createElement(m.a,{width:"auto",id:g,renderItem:function(e){return r.a.createElement(r.a.Fragment,null,r.a.createElement(l.a,{glyph:e.icon,className:"Dropdown__item-icon"}),r.a.createElement("div",{className:"Dropdown__item-vertical"},r.a.createElement("div",{className:"Dropdown__item-label"},d(e.label))),"page-options"===e.key&&r.a.createElement("div",{className:"Divider"}))},className:"options-dropdown-container",getKey:function(e){return e.key},items:A,onClickItem:k,displayButton:function(e){return r.a.createElement(s.a,{className:"options-button",ariaLabelledby:O,ariaControls:"".concat(g,"-dropdown"),ariaExpanded:e,img:"ic_chevron_down_black_24px",label:d("officeEditor.options"),isActive:e})},stopPropagationOnMouseDown:!0}))};A.propTypes=L;var k=r.a.memo(A),N=(o(1809),{visiblePages:p.a.arrayOf(p.a.number),isHeaderControlsActive:p.a.bool,isFooterControlsActive:p.a.bool}),j=function(e){var t=e.visiblePages,o=e.isHeaderControlsActive,n=e.isFooterControlsActive,c=t.map((function(e){var t=Object(a.a)().getElementById("pageSection".concat(e));return t?Object(i.createPortal)(r.a.createElement("div",{key:e,className:"HeaderFooterControlsOverlay"},r.a.createElement(k,{type:"header",pageNumber:e,isActive:o}),r.a.createElement(k,{type:"footer",pageNumber:e,isActive:n})),t):null}));return r.a.createElement(r.a.Fragment,null,c)};j.propTypes=N;var S=j;t.default=S}}]);
//# sourceMappingURL=chunk.51.js.map