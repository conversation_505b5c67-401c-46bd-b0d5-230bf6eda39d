{"version": 3, "sources": ["webpack:///./src/ui/src/components/ModularComponents/PresetButton/buttons/SheetEditor/BorderStyleButton.js"], "names": ["propTypes", "borderStyle", "PropTypes", "string", "isFlyoutItem", "bool", "style", "object", "className", "BorderStyleButton", "forwardRef", "props", "ref", "menuItems", "dataElement", "icon", "title", "handleClick", "onClick", "additionalClass", "key", "isActive", "img", "ariaPressed", "displayName"], "mappings": "8YAMA,IAAMA,EAAY,CAChBC,YAAaC,IAAUC,OACvBC,aAAcF,IAAUG,KACxBC,MAAOJ,IAAUK,OACjBC,UAAWN,IAAUC,QAGjBM,EAAoBC,sBAAW,SAACC,EAAOC,GAC3C,IAAQR,EAAgDO,EAAhDP,aAAcH,EAAkCU,EAAlCV,YAAaK,EAAqBK,EAArBL,MAAOE,EAAcG,EAAdH,UAG1C,EAAqCK,IAA2B,gBAAxDC,EAAW,EAAXA,YAAaC,EAAI,EAAJA,KAAMC,EAAK,EAALA,MAErBC,EAAc,aAIpB,OACEb,EACE,kBAAC,IAAmB,KACdO,EAAK,CACTC,IAAKA,EACLM,QAASD,EACTE,gBAAuC,MAGvC,kBAAC,IAAY,CACXC,IAAKnB,EACLoB,UAnBS,EAoBTH,QAASD,EACTH,YAAaA,EACbE,MAAOA,EACPM,IAAKP,EACLQ,aAxBS,EAyBTjB,MAAOA,EACPE,UAAWA,OAMrBC,EAAkBT,UAAYA,EAC9BS,EAAkBe,YAAc,oBAEjBf", "file": "chunks/chunk.86.js", "sourcesContent": ["import React, { forwardRef } from 'react';\nimport ActionButton from 'components/ActionButton';\nimport PropTypes from 'prop-types';\nimport FlyoutItemContainer from '../../../FlyoutItemContainer';\nimport { menuItems } from '../../../Helpers/menuItems';\n\nconst propTypes = {\n  borderStyle: PropTypes.string,\n  isFlyoutItem: PropTypes.bool,\n  style: PropTypes.object,\n  className: PropTypes.string,\n};\n\nconst BorderStyleButton = forwardRef((props, ref) => {\n  const { isFlyoutItem, borderStyle, style, className } = props;\n  const isActive = false;\n\n  const { dataElement, icon, title } = menuItems['cellBorderStyle'];\n\n  const handleClick = () => {\n    // handle click\n  };\n\n  return (\n    isFlyoutItem ?\n      <FlyoutItemContainer\n        {...props}\n        ref={ref}\n        onClick={handleClick}\n        additionalClass={isActive ? 'active' : ''}\n      />\n      : (\n        <ActionButton\n          key={borderStyle}\n          isActive={isActive}\n          onClick={handleClick}\n          dataElement={dataElement}\n          title={title}\n          img={icon}\n          ariaPressed={isActive}\n          style={style}\n          className={className}\n        />\n      )\n  );\n});\n\nBorderStyleButton.propTypes = propTypes;\nBorderStyleButton.displayName = 'BorderStyleButton';\n\nexport default BorderStyleButton;"], "sourceRoot": ""}