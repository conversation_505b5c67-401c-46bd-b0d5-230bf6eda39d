{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/en-ca.js"], "names": ["module", "exports", "e", "_", "default", "a", "t", "name", "weekdays", "split", "months", "weekdaysShort", "monthsShort", "weekdaysMin", "ordinal", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "relativeTime", "future", "past", "s", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "locale"], "mappings": "gFAAoEA,EAAOC,QAAgL,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAGI,EAAE,CAACC,KAAK,QAAQC,SAAS,2DAA2DC,MAAM,KAAKC,OAAO,wFAAwFD,MAAM,KAAKE,cAAc,8BAA8BF,MAAM,KAAKG,YAAY,kDAAkDH,MAAM,KAAKI,YAAY,uBAAuBJ,MAAM,KAAKK,QAAQ,SAASZ,GAAG,OAAOA,GAAGa,QAAQ,CAACC,GAAG,SAASC,IAAI,YAAYC,EAAE,aAAaC,GAAG,eAAeC,IAAI,sBAAsBC,KAAK,6BAA6BC,aAAa,CAACC,OAAO,QAAQC,KAAK,SAASC,EAAE,gBAAgBC,EAAE,WAAWC,GAAG,aAAaC,EAAE,UAAUC,GAAG,WAAWC,EAAE,QAAQC,GAAG,UAAUC,EAAE,UAAUC,GAAG,YAAYC,EAAE,SAASC,GAAG,aAAa,OAAOhC,EAAEC,QAAQgC,OAAO9B,EAAE,MAAK,GAAIA,EAAhhCD,CAAE,EAAQ", "file": "chunks/chunk.131.js", "sourcesContent": ["!function(e,a){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=a(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],a):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_en_ca=a(e.dayjs)}(this,(function(e){\"use strict\";function a(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var _=a(e),t={name:\"en-ca\",weekdays:\"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday\".split(\"_\"),months:\"January_February_March_April_May_June_July_August_September_October_November_December\".split(\"_\"),weekdaysShort:\"Sun_Mon_Tue_Wed_Thu_Fri_Sat\".split(\"_\"),monthsShort:\"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec\".split(\"_\"),weekdaysMin:\"Su_Mo_Tu_We_Th_Fr_Sa\".split(\"_\"),ordinal:function(e){return e},formats:{LT:\"h:mm A\",LTS:\"h:mm:ss A\",L:\"YYYY-MM-DD\",LL:\"MMMM D, YYYY\",LLL:\"MMMM D, YYYY h:mm A\",LLLL:\"dddd, MMMM D, YYYY h:mm A\"},relativeTime:{future:\"in %s\",past:\"%s ago\",s:\"a few seconds\",m:\"a minute\",mm:\"%d minutes\",h:\"an hour\",hh:\"%d hours\",d:\"a day\",dd:\"%d days\",M:\"a month\",MM:\"%d months\",y:\"a year\",yy:\"%d years\"}};return _.default.locale(t,null,!0),t}));"], "sourceRoot": ""}