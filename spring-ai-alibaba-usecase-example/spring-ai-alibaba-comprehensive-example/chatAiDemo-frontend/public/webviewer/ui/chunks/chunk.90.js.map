{"version": 3, "sources": ["webpack:///./src/ui/src/components/ModularComponents/PresetButton/buttons/SheetEditor/ColorPickerButton.js"], "names": ["propTypes", "type", "PropTypes", "oneOf", "Object", "values", "CELL_COLOR_OPTIONS", "isRequired", "isFlyoutItem", "bool", "style", "object", "className", "string", "ColorPickerButton", "forwardRef", "props", "ref", "key", "char<PERSON>t", "toUpperCase", "slice", "menuItems", "dataElement", "icon", "title", "handleClick", "onClick", "additionalClass", "isActive", "img", "ariaPressed", "displayName"], "mappings": "yaAOA,IAAMA,EAAY,CAChBC,KAAMC,IAAUC,MAAMC,OAAOC,OAAOC,MAAqBC,WACzDC,aAAcN,IAAUO,KACxBC,MAAOR,IAAUS,OACjBC,UAAWV,IAAUW,QAGjBC,EAAoBC,sBAAW,SAACC,EAAOC,GAC3C,IAAQT,EAAyCQ,EAAzCR,aAAcP,EAA2Be,EAA3Bf,KAAMS,EAAqBM,EAArBN,MAAOE,EAAcI,EAAdJ,UAG7BM,EAAM,OAAH,OAAUjB,EAAKkB,OAAO,GAAGC,eAAa,OAAGnB,EAAKoB,MAAM,IAC7D,EAAqCC,IAAUJ,GAAvCK,EAAW,EAAXA,YAAaC,EAAI,EAAJA,KAAMC,EAAK,EAALA,MAErBC,EAAc,aAIpB,OACElB,EACE,kBAAC,IAAmB,KACdQ,EAAK,CACTC,IAAKA,EACLU,QAASD,EACTE,gBAAuC,MAGvC,kBAAC,IAAY,CACXV,IAAKjB,EACL4B,UApBS,EAqBTF,QAASD,EACTH,YAAaA,EACbE,MAAOA,EACPK,IAAKN,EACLO,aAzBS,EA0BTrB,MAAOA,EACPE,UAAWA,OAMrBE,EAAkBd,UAAYA,EAC9Bc,EAAkBkB,YAAc,oBAEjBlB", "file": "chunks/chunk.90.js", "sourcesContent": ["import React, { forwardRef } from 'react';\nimport ActionButton from 'components/ActionButton';\nimport PropTypes from 'prop-types';\nimport FlyoutItemContainer from '../../../FlyoutItemContainer';\nimport { menuItems } from '../../../Helpers/menuItems';\nimport { CELL_COLOR_OPTIONS } from 'src/constants/spreadsheetEditor';\n\nconst propTypes = {\n  type: PropTypes.oneOf(Object.values(CELL_COLOR_OPTIONS)).isRequired,\n  isFlyoutItem: PropTypes.bool,\n  style: PropTypes.object,\n  className: PropTypes.string,\n};\n\nconst ColorPickerButton = forwardRef((props, ref) => {\n  const { isFlyoutItem, type, style, className } = props;\n  const isActive = false;\n\n  const key = `cell${type.charAt(0).toUpperCase()}${type.slice(1)}`;\n  const { dataElement, icon, title } = menuItems[key];\n\n  const handleClick = () => {\n    // handle button click\n  };\n\n  return (\n    isFlyoutItem ?\n      <FlyoutItemContainer\n        {...props}\n        ref={ref}\n        onClick={handleClick}\n        additionalClass={isActive ? 'active' : ''}\n      />\n      : (\n        <ActionButton\n          key={type}\n          isActive={isActive}\n          onClick={handleClick}\n          dataElement={dataElement}\n          title={title}\n          img={icon}\n          ariaPressed={isActive}\n          style={style}\n          className={className}\n        />\n      )\n  );\n});\n\nColorPickerButton.propTypes = propTypes;\nColorPickerButton.displayName = 'ColorPickerButton';\n\nexport default ColorPickerButton;"], "sourceRoot": ""}