{"version": 3, "sources": ["webpack:///./src/ui/src/constants/bookmarksOutlinesShared.scss?1d5f", "webpack:///./src/ui/src/components/TextButton/TextButton.js", "webpack:///./src/ui/src/components/TextButton/index.js", "webpack:///./src/ui/src/constants/bookmarksOutlinesShared.scss", "webpack:///./src/ui/src/components/TextButton/TextButton.scss?deeb", "webpack:///./src/ui/src/components/TextButton/TextButton.scss", "webpack:///./src/ui/src/components/BookmarksPanel/BookmarksPanel.scss?f400", "webpack:///./src/ui/src/components/BookmarksPanel/BookmarksPanel.scss", "webpack:///./src/ui/src/components/Bookmark/Bookmark.js", "webpack:///./src/ui/src/components/Bookmark/index.js", "webpack:///./src/ui/src/components/BookmarksPanel/BookmarksPanel.js", "webpack:///./src/ui/src/components/BookmarksPanel/index.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "propTypes", "img", "PropTypes", "string", "label", "oneOfType", "number", "dataElement", "onClick", "func", "aria<PERSON><PERSON><PERSON>", "ariaControls", "role", "disabled", "bool", "TextButton", "props", "<PERSON><PERSON>", "className", "React", "memo", "text", "isRequired", "defaultLabel", "pageIndex", "isAdding", "isMultiSelectionMode", "setSelected", "onSave", "onRemove", "onCancel", "panelSelector", "Bookmark", "t", "useTranslation", "useState", "isEditing", "setIsEditing", "isDefault", "setIsDefault", "bookmarkText", "setBookmarkText", "undefined", "clearSingleClick", "setClearSingleClick", "inputRef", "useRef", "isRenameButtonDisabled", "onSaveBookmark", "trim", "onCancelBookmark", "useEffect", "current", "focus", "select", "flyoutSelector", "DataElements", "BOOKMARK_FLYOUT", "currentFlyout", "useSelector", "state", "selectors", "getFlyout", "bookmarkMoreOptionsDataElement", "bookmarkPanelListProps", "labelHeader", "description", "enableMoreOptionsContextMenuFlyout", "contentMenuFlyoutOptions", "shouldHideDeleteButton", "type", "handleOnClick", "val", "menuTypes", "RENAME", "DELETE", "contextMenuMoreButtonOptions", "flyoutToggleElement", "moreOptionsDataElement", "e", "detail", "setTimeout", "core", "setCurrentPage", "onDoubleClick", "checkboxOptions", "id", "onChange", "target", "checked", "PanelListItem", "DataElementWrapper", "classNames", "clearTimeout", "name", "ref", "aria-label", "value", "onKeyDown", "key", "stopPropagation", "isSubmitType", "BookmarksPanel", "isElementDisabled", "BOOKMARK_PANEL", "getBookmarks", "getCurrentPage", "getPageLabels", "isBookmarkIconShortcutVisible", "getFeatureFlags", "shallowEqual", "isDisabled", "bookmarks", "currentPageIndex", "pageLabe<PERSON>", "featureFlags", "isAddingNewBookmark", "setAddingNewBookmark", "setMultiSelectionMode", "selectingBookmarks", "setSelectingBookmarks", "customizableUI", "dispatch", "useDispatch", "setBookmarkIconShortcutVisibility", "pageIndices", "Object", "keys", "map", "parseInt", "index", "includes", "filter", "bm", "onRemoveBookmarks", "pageIndexes", "title", "confirmation<PERSON><PERSON>ning", "message", "onConfirm", "removeUserBookmark", "Number", "confirmBtnText", "actions", "showWarningMessage", "data-element", "BOOKMARK_MULTI_SELECT", "Choice", "BOOKMARK_SHORTCUT_OPTION", "isSwitch", "newText", "addUserBookmark", "updatedBookmarks", "getUserBookmarks", "setUserBookmarks", "toString", "find", "BOOKMARK_ADD_NEW_BUTTON_CONTAINER", "BOOKMARK_ADD_NEW_BUTTON"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,8EC5D7BC,G,QAAY,CAChBC,IAAKC,IAAUC,OACfC,MAAOF,IAAUG,UAAU,CAACH,IAAUC,OAAQD,IAAUI,SACxDC,YAAaL,IAAUC,OACvBK,QAASN,IAAUO,KACnBC,UAAWR,IAAUC,OACrBQ,aAAcT,IAAUC,OACxBS,KAAMV,IAAUC,OAChBU,SAAUX,IAAUY,OAGhBC,EAAa,SAACC,GAClB,IACEf,EAQEe,EARFf,IACAM,EAOES,EAPFT,YACAC,EAMEQ,EANFR,QACAJ,EAKEY,EALFZ,MACAM,EAIEM,EAJFN,UACAC,EAGEK,EAHFL,aACAC,EAEEI,EAFFJ,KACAC,EACEG,EADFH,SAGF,OAAQ,kBAACI,EAAA,EAAM,CACbC,UAAU,aACVjB,IAAKA,EACLG,MAAOA,EACPG,YAAaA,EACbC,QAASA,EACTE,UAAWA,EACXC,aAAcA,EACdC,KAAMA,EACNC,SAAUA,KAIdE,EAAWf,UAAYA,EAERmB,UAAMC,KAAKL,GC1CXA,O,sBCFL5C,EAAO2B,QAAU,EAAQ,GAAR,EAA+D,IAKlFR,KAAK,CAACnB,EAAOC,EAAI,wkPAAykP,M,qBCLlmP,IAAIL,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEzB5B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,4XAA6X,M,qBCLtZ,IAAIL,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,6pHAA8pH,KAGvrH0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,i6CCMvB,IAAMC,EAAY,CAChBqB,KAAMnB,IAAUC,OAAOmB,WACvBlB,MAAOF,IAAUC,OAAOmB,WACxBC,aAAcrB,IAAUC,OACxBqB,UAAWtB,IAAUI,OAAOgB,WAC5BG,SAAUvB,IAAUY,KACpBY,qBAAsBxB,IAAUY,KAChCa,YAAazB,IAAUO,KACvBmB,OAAQ1B,IAAUO,KAAKa,WACvBO,SAAU3B,IAAUO,KACpBqB,SAAU5B,IAAUO,KACpBsB,cAAe7B,IAAUC,QAGrB6B,EAAW,SAAH,GAYR,IAXJX,EAAI,EAAJA,KACAjB,EAAK,EAALA,MACAmB,EAAY,EAAZA,aACAC,EAAS,EAATA,UACAC,EAAQ,EAARA,SACAC,EAAoB,EAApBA,qBACAC,EAAW,EAAXA,YACAC,EAAM,EAANA,OACAC,EAAQ,EAARA,SACAC,EAAQ,EAARA,SACAC,EAAa,EAAbA,cAEOE,EAAqB,EAAhBC,cAAgB,GAApB,GAEyC,IAAfC,oBAAS,GAAM,GAA1CC,EAAS,KAAEC,EAAY,KACmB,IAAfF,oBAAS,GAAM,GAA1CG,EAAS,KAAEC,EAAY,KACwB,IAAdJ,mBAASd,GAAK,GAA/CmB,EAAY,KAAEC,EAAe,KAC+B,IAAnBN,wBAASO,GAAU,GAA5DC,EAAgB,KAAEC,EAAmB,KACtCC,EAAWC,mBAEXC,EAAyB,WAC7B,OAAQP,GAAgBnB,IAASmB,GAe7BQ,EAAiB,WACrBX,GAAa,GACbT,EAA+B,KAAxBY,EAAaS,OAAgBhB,EAAE,oBAAsBO,IAGxDU,EAAmB,WACvBb,GAAa,GAEbD,GAAaK,EAAgBpB,GAC7BI,GAAYK,KAOdqB,qBAAU,WACJX,IAAiBnB,GACnBoB,EAAgBpB,KAEjB,CAACA,IAEJ8B,qBAAU,YACJ1B,GAAYW,KACdS,EAASO,QAAQC,QACjBR,EAASO,QAAQE,UAMjBf,GAHGd,IAAaW,KAKjB,CAACA,IAEJ,IAaMmB,EAAiB,GAAH,OAAMC,IAAaC,gBAAe,YAAIjC,GACpDkC,EAAgBC,aAAY,SAACC,GAAK,OAAKC,IAAUC,UAAUF,EAAOL,MAClEQ,EAAiC,wBAAH,OAA2BhC,EAAa,YAAIP,GAG1EwC,EAAyB,CAC7BC,YAAa1C,EACb2C,YAAa7C,EACb8C,oCAAoC,EACpCC,yBAA0B,CACxBC,wBAAwB,EACxBX,cAAeA,EACfH,eAAgBA,EAChBe,KAVkB,WAWlBC,cA3BkB,SAACC,GACrB,OAAQA,GACN,KAAKC,IAAUC,OACbrC,GAAa,GACb,MACF,KAAKoC,IAAUE,OACb9C,EAASL,MAuBboD,6BAA8B,CAC5BC,oBAAqBtB,EACrBuB,uBAAwBf,GAE1BvD,QAAS,SAACuE,GACJzC,GAA0B,IAAbyC,EAAEC,QACjBpC,EAAoBqC,YAAW,YA1Dd,SAACzD,GACtB0D,IAAKC,eAAe3D,EAAY,GA0D1B2D,CAAe3D,KACd,OAGP4D,cAAe,WACT9C,GACFD,GAAa,IAGjBgD,gBAAiB,CACfC,GAAI,qBAAF,OAAuB9D,EAAY,GACrC+D,SAAU,SAACR,GACTpD,EAAYH,EAAWuD,EAAES,OAAOC,UAElC/E,UAAW,GAAF,OAAKuB,EAAE,iBAAgB,YAAI7B,GACpCS,UAAWa,IAIf,OACE,oCACGY,GAAa,kBAACoD,EAAA,EAAkB1B,IAC/BvC,GAAYW,IAAc,kBAACuD,EAAA,EAAkB,CAC7CzE,UAAW0E,IAAW,CACpB,qCAAqC,EACrC,QAAWnE,GAAYW,EACvB,QAAWE,IAEb8C,cAAe,WACT9C,GACFuD,aAAalD,KAIjB,yBAAKzB,UAAU,8BACb,yBAAKA,UAAU,0BAA2BO,GAAYW,EAAahC,EAAQmB,GAC3E,2BACE+C,KAAK,OACLwB,KAAK,WACLC,IAAKlD,EACL3B,UAAU,6CACV8E,aAAY/D,EAAE,eACdgE,MAAOzD,EACP0D,UA9HY,SAACnB,GACP,UAAVA,EAAEoB,MACJpB,EAAEqB,mBACE3E,GAAaW,IAAcW,MAC7BC,KAGU,WAAV+B,EAAEoB,KACJjD,KAuHMqC,SAAU,SAACR,GAAC,OAAKtC,EAAgBsC,EAAES,OAAOS,UAE5C,yBAAK/E,UAAU,qCACb,kBAACH,EAAA,EAAU,CACTX,MAAO6B,EAAE,iBACTzB,QAAS0C,EACTxC,UAAS,UAAKuB,EAAE,iBAAgB,YAAIA,EAAE,8BAEvCR,GACC,kBAACR,EAAA,EAAM,CACLC,UAAU,+BACVd,MAAO6B,EAAE,cACToE,cAAY,EACZ7F,QAASwC,EACTtC,UAAS,UAAKuB,EAAE,cAAa,YAAIA,EAAE,8BAGtCG,GACC,kBAACnB,EAAA,EAAM,CACLC,UAAU,+BACVd,MAAO6B,EAAE,eACToE,cAAY,EACZxF,SAAUkC,IACVvC,QAASwC,EACTtC,UAAS,UAAKuB,EAAE,eAAc,YAAIA,EAAE,mCAUpDD,EAAShC,UAAYA,EAENgC,ICxNAA,EDwNAA,E,2nFEvMf,IAkNesE,EAlNQ,SAAH,GAA0B,QAApBvE,EAAa,EAAbA,cAkBvB,IAVG4B,aACF,SAACC,GAAK,MAAK,CACTC,IAAU0C,kBAAkB3C,EAAOJ,IAAagD,gBAChD3C,IAAU4C,aAAa7C,GACvBC,IAAU6C,eAAe9C,GAAS,EAClCC,IAAU8C,cAAc/C,GACxBC,IAAU+C,8BAA8BhD,GACxCC,IAAUgD,gBAAgBjD,MAE5BkD,KACD,GAhBCC,EAAU,KACVC,EAAS,KACTC,EAAgB,KAChBC,EAAU,KACVN,EAA6B,KAC7BO,EAAY,KAaqD,IAAfhF,oBAAS,GAAM,GAA5DiF,EAAmB,KAAEC,EAAoB,KACqB,IAAflF,oBAAS,GAAM,GAA9DT,EAAoB,KAAE4F,EAAqB,KACc,IAAZnF,mBAAS,IAAG,GAAzDoF,EAAkB,KAAEC,EAAqB,KAC1CC,EAAiBN,EAAaM,eAE7BxF,EAAqB,EAAhBC,cAAgB,GAApB,GACFwF,EAAWC,cAEjBxE,qBAAU,WACJyD,IAAkCG,EACpC7B,IAAK0C,mCAAkC,GAEvC1C,IAAK0C,mCAAkC,KAExC,CAACb,EAAYH,IAEhB,IAAMiB,EAAcC,OAAOC,KAAKf,GAAWgB,KAAI,SAACxG,GAAS,OAAKyG,SAASzG,EAAW,OAElF2B,qBAAU,WAERoE,EAAmBnI,SAAQ,SAAC8I,GACrBL,EAAYM,SAASD,IACxBV,EAAsBD,EAAmBa,QAAO,SAACC,GAAE,OAAKA,IAAOH,SAIT,IAAvBL,EAAY/I,QAE7CwI,GAAsB,KAEvB,CAACN,IAEJ,IAAMsB,EAAoB,SAACC,GACzB,IAAMC,EAAQvG,EAAE,gCAEVwG,EAAsB,CAC1BC,QAFczG,EAAE,kCAGhBuG,QACAG,UAAW,WACTJ,EAAYnJ,SAAQ,SAACoC,GAAS,OAAK0D,IAAK0D,mBAAmBC,OAAOrH,OAClEgG,EAAsB,KAExBsB,eAAgB7G,EAAE,kBAEpByF,EAASqB,IAAQC,mBAAmBP,KAGtC,OAAI1B,EACK,KAIP,yBACE7F,UAAW0E,KAAU,GACnB,OAAS,EACT,gBAAkB,EAClB,0BAA0B,GAAI,IAC7B7D,GAAgB,GAAI,IACrB,mBAAoB0F,GAAc,IAEpCwB,eAAczF,IAAagD,gBAE3B,yBAAKtF,UAAU,iCACb,yBAAKA,UAAU,gBACZe,EAAE,8BAEHP,GACA,kBAACX,EAAA,EAAU,CACTR,YAAaiD,IAAa0F,sBAC1B9I,MAAO6B,EAAE,eACTpB,SAAUuG,GAA8C,IAAvBS,EAAY/I,OAC7C0B,QAAS,kBAAM8G,GAAsB,IACrC5G,UAAS,UAAKuB,EAAE,eAAc,YAAIA,EAAE,+BAGvCP,GACC,kBAACX,EAAA,EAAU,CACTR,YAAaiD,IAAa0F,sBAC1B9I,MAAO6B,EAAE,uCACTpB,SAAUuG,EACV5G,QAAS,WACP8G,GAAsB,GACtBE,EAAsB,KAExB9G,UAAS,UAAKuB,EAAE,uCAAsC,YAAIA,EAAE,mBAKlE,kBAACkH,EAAA,EAAM,CACL5I,YAAaiD,IAAa4F,yBAC1B9E,KAAK,WACL+E,UAAQ,EACR/D,GAAG,uBACHpE,UAAU,kBACVd,MAAO6B,EAAE,wBACTwD,QAASmB,EACTrB,SAAU,SAACR,GAAC,OAAK2C,EAASqB,IAAQnB,kCAAkC7C,EAAES,OAAOC,cAG7E2B,GAA8C,IAAvBS,EAAY/I,QACnC,yBAAKoC,UAAU,+BAA+Be,EAAE,wBAGlD,yBAAKf,UAAU,wBACZkG,GACC,kBAAC,EAAQ,CACP3F,UAAQ,EACRrB,MAAK,UAAK6B,EAAE,0BAAyB,YAAIiF,EAAWD,GAAiB,cAAMhF,EAAE,4BAC7EZ,KAAiC,QAA7B,EAAE2F,EAAUC,UAAiB,QAAI,GACrCzF,UAAWyF,EACXrF,OAAQ,SAAC0H,GACPpE,IAAKqE,gBAAgBtC,EAAkBqC,GACvCjC,GAAqB,IAEvBvF,SAAU,kBAAMuF,GAAqB,IACrCtF,cAAeA,IAIlB8F,EAAYG,KAAI,SAACxG,GAAS,OACzB,kBAAC,EAAQ,CACP2E,IAAK3E,EACLO,cAAeA,EACfL,qBAAsBA,EACtBtB,MAAK,UAAK6B,EAAE,0BAAyB,YAAIiF,EAAW1F,GAAU,cAAMS,EAAE,4BACtEV,aAAY,UAAKU,EAAE,0BAAyB,YAAIiF,EAAW1F,IAC3DH,KAAM2F,EAAUxF,GAChBA,UAAWA,EACXI,OAAQ,SAAC0H,GACP,IAAME,EAAmB,EAAH,KACjBtE,IAAKuE,oBAAkB,QACzBjI,EAAY8H,IAEfpE,IAAKwE,iBAAiBF,IAExB3H,SAAU,SAACqG,GAAK,OAAKI,EAAkB,CAACJ,KACxCvG,YAAa,SAACuG,EAAO1D,GAEnB0D,EAAQA,EAAMyB,WACVpC,EAAmBqC,MAAK,SAACvB,GAAE,OAAKA,IAAOH,KACpC1D,GACHgD,EAAsBD,EAAmBa,QAAO,SAACC,GAAE,OAAKA,IAAOH,MAG7D1D,GACFgD,EAAsB,GAAD,SAAKD,GAAkB,CAAEW,YAQ1D,kBAACvC,EAAA,EAAkB,CACjBzE,UAAU,0BACVX,YAAaiD,IAAaqG,mCAEzBnI,EACC,oCACE,kBAACT,EAAA,EAAM,CACLC,UAAU,yBACVjB,IAAI,gBACJY,SAAU0G,EAAmBzI,OAAS,KAAOkI,EAAUC,IAAqBG,EAC5E5G,QAAS,kBAAM6G,GAAqB,IACpC3G,UAAS,UAAKuB,EAAE,cAAa,YAAIA,EAAE,8BAErC,kBAAChB,EAAA,EAAM,CACLC,UAAU,yBACVjB,IAAI,mBACJY,SAAwC,IAA9B0G,EAAmBzI,OAC7B0B,QAAS,kBAAM8H,EAAkBf,IACjC7G,UAAS,UAAKuB,EAAE,iBAAgB,YAAIA,EAAE,+BAI1C,kBAAClB,EAAA,EAAU,CACTd,IAAI,gBACJM,YAAaiD,IAAasG,wBAC1B1J,MAAK,UAAK6B,EAAE,cAAa,YAAIA,EAAE,4BAC/BpB,SAAUuG,KAAyBJ,EAAUC,GAC7CzG,QAAS,kBAAM6G,GAAqB,IACpC3G,UAAS,UAAKuB,EAAE,cAAa,YAAIA,EAAE,iCC3NhCqE", "file": "chunks/chunk.44.js", "sourcesContent": ["var api = require(\"!../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../node_modules/css-loader/index.js!../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../node_modules/sass-loader/dist/cjs.js!./bookmarksOutlinesShared.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "import React from 'react';\nimport PropTypes from 'prop-types';\nimport Button from '../Button';\n\nimport './TextButton.scss';\n\nconst propTypes = {\n  img: PropTypes.string,\n  label: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n  dataElement: PropTypes.string,\n  onClick: PropTypes.func,\n  ariaLabel: PropTypes.string,\n  ariaControls: PropTypes.string,\n  role: PropTypes.string,\n  disabled: PropTypes.bool\n};\n\nconst TextButton = (props) => {\n  const {\n    img,\n    dataElement,\n    onClick,\n    label,\n    ariaLabel,\n    ariaControls,\n    role,\n    disabled\n  } = props;\n\n  return (<Button\n    className='TextButton'\n    img={img}\n    label={label}\n    dataElement={dataElement}\n    onClick={onClick}\n    ariaLabel={ariaLabel}\n    ariaControls={ariaControls}\n    role={role}\n    disabled={disabled}\n  />);\n};\n\nTextButton.propTypes = propTypes;\n\nexport default React.memo(TextButton);", "import TextButton from './TextButton';\n\nexport default TextButton;", "exports = module.exports = require(\"../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".bookmark-outline-panel{display:flex;padding-left:var(--padding);padding-right:var(--padding-small)}.bookmark-outline-control-button{width:auto}.bookmark-outline-control-button span{color:inherit}.bookmark-outline-control-button,.bookmark-outline-control-button.disabled,.bookmark-outline-control-button[disabled]{color:var(--secondary-button-text)}.bookmark-outline-control-button.disabled,.bookmark-outline-control-button[disabled]{opacity:.5}.bookmark-outline-control-button.disabled span,.bookmark-outline-control-button[disabled] span{color:inherit}.bookmark-outline-control-button:not(.disabled):active,.bookmark-outline-control-button:not(.disabled):hover,.bookmark-outline-control-button:not([disabled]):active,.bookmark-outline-control-button:not([disabled]):hover{color:var(--secondary-button-hover)}.bookmark-outline-panel-header{display:flex;flex-flow:row nowrap;justify-content:space-between;align-items:center;padding:var(--padding-tiny);border-bottom:1px solid var(--divider)}.bookmark-outline-panel-header .header-title{font-size:16px}.bookmark-outline-row{flex-grow:1;overflow-y:auto}.msg-no-bookmark-outline{color:var(--placeholder-text);text-align:center}.bookmark-outline-single-container{display:flex;flex-flow:row nowrap;align-items:flex-start;border-radius:4px;margin-left:2px;margin-right:2px}.bookmark-outline-single-container.default{padding:var(--padding-tiny);border:1px solid transparent}.bookmark-outline-single-container.default.hover,.bookmark-outline-single-container.default:hover,.bookmark-outline-single-container.default[focus-within]{cursor:pointer}.bookmark-outline-single-container.default.hover,.bookmark-outline-single-container.default:focus-within,.bookmark-outline-single-container.default:hover{cursor:pointer}.bookmark-outline-single-container.default.hover .bookmark-outline-more-button,.bookmark-outline-single-container.default:hover .bookmark-outline-more-button,.bookmark-outline-single-container.default[focus-within] .bookmark-outline-more-button{display:flex;background-color:transparent}.bookmark-outline-single-container.default.hover .bookmark-outline-more-button,.bookmark-outline-single-container.default:focus-within .bookmark-outline-more-button,.bookmark-outline-single-container.default:hover .bookmark-outline-more-button{display:flex;background-color:transparent}.bookmark-outline-single-container.default[focus-within]{border-color:transparent}.bookmark-outline-single-container.default:focus-within{border-color:transparent}.bookmark-outline-single-container.default .bookmark-outline-label-row{overflow:hidden}.bookmark-outline-single-container.default.focus-visible,.bookmark-outline-single-container.default:focus-visible{outline:var(--focus-visible-outline)!important}.bookmark-outline-single-container.editing{background-color:var(--faded-component-background);padding:var(--padding-medium) 20px}.bookmark-outline-single-container.editing.focus-visible,.bookmark-outline-single-container.editing:focus-visible{outline:var(--focus-visible-outline)!important}.bookmark-outline-single-container.preview{display:inline-flex;margin-top:0;padding:var(--padding-small);background-color:var(--component-background);box-shadow:0 0 3px var(--note-box-shadow)}.bookmark-outline-single-container .bookmark-outline-checkbox{flex-grow:0;flex-shrink:0;margin-top:2px;margin-bottom:2px;margin-right:var(--padding-small)}.bookmark-outline-single-container .bookmark-outline-label-row{flex-grow:1;flex-shrink:1;display:flex;flex-flow:row wrap;align-items:flex-start;position:relative;overflow:hidden}.bookmark-outline-single-container .bookmark-outline-label{font-weight:600;flex-grow:1;flex-shrink:1;margin-bottom:var(--padding-small)}.bookmark-outline-single-container .bookmark-outline-input,.bookmark-outline-single-container .bookmark-outline-text{flex-grow:1;flex-shrink:1;flex-basis:calc(100% - 22px);margin-top:2px;margin-bottom:2px}.bookmark-outline-single-container .bookmark-text-input{margin-left:var(--padding-large)}.bookmark-outline-single-container .bookmark-outline-input{color:var(--text-color);width:calc(100% - var(--padding-large));padding:var(--padding-small);border:1px solid var(--border)}.bookmark-outline-single-container .bookmark-outline-input:focus{border-color:var(--outline-color)}.bookmark-outline-single-container .bookmark-outline-input::-moz-placeholder{color:var(--placeholder-text)}.bookmark-outline-single-container .bookmark-outline-input::placeholder{color:var(--placeholder-text)}.bookmark-outline-single-container .bookmark-outline-more-button{display:none;flex-grow:0;flex-shrink:0;width:16px;height:16px;margin:2px 2px 2px var(--padding-tiny)}.bookmark-outline-single-container .bookmark-outline-more-button .Icon{width:14px;height:14px}.bookmark-outline-single-container .bookmark-outline-more-button.icon-only:hover:not(:disabled):not(.disabled){box-shadow:none;outline:solid 1px var(--hover-border)}.bookmark-outline-single-container .bookmark-outline-more-button[focus-within].icon-only{border:none;box-shadow:none}.bookmark-outline-single-container .bookmark-outline-more-button:focus-within.icon-only{border:none;box-shadow:none}.bookmark-outline-single-container .bookmark-outline-more-button[focus-within] .Icon{color:var(--focus-border)}.bookmark-outline-single-container .bookmark-outline-more-button:focus-within .Icon{color:var(--focus-border)}.bookmark-outline-single-container .bookmark-outline-editing-controls{padding:2px;flex-basis:100%;display:flex;flex-flow:row wrap;justify-content:flex-end;align-items:center;margin-top:var(--padding-medium)}.bookmark-outline-single-container .bookmark-outline-cancel-button,.bookmark-outline-single-container .bookmark-outline-save-button{width:auto;padding:6px var(--padding)}.bookmark-outline-single-container .bookmark-outline-cancel-button{color:var(--secondary-button-text)}.bookmark-outline-single-container .bookmark-outline-cancel-button:hover{color:var(--secondary-button-hover)}.bookmark-outline-single-container .bookmark-outline-save-button{color:var(--primary-button-text);background-color:var(--primary-button);margin-left:var(--padding-tiny);border-radius:4px}.bookmark-outline-single-container .bookmark-outline-save-button:hover{background-color:var(--primary-button-hover)}.bookmark-outline-single-container .bookmark-outline-save-button.disabled,.bookmark-outline-single-container .bookmark-outline-save-button:disabled{background-color:var(--primary-button)!important;opacity:.5}.bookmark-outline-single-container .bookmark-outline-save-button.disabled span,.bookmark-outline-single-container .bookmark-outline-save-button:disabled span{color:var(--primary-button-text)}.bookmark-outline-footer{border-top:1.5px solid var(--gray-4);padding-top:var(--padding-medium);padding-bottom:var(--padding-medium);display:flex;justify-content:center;align-items:center}.bookmark-outline-footer .add-new-button .Icon{width:14px;height:14px;margin-right:var(--padding-tiny);color:inherit;fill:currentColor}.bookmark-outline-footer .add-new-button.disabled .Icon.disabled,.bookmark-outline-footer .add-new-button.disabled .Icon.disabled path,.bookmark-outline-footer .add-new-button[disabled] .Icon.disabled,.bookmark-outline-footer .add-new-button[disabled] .Icon.disabled path{color:inherit;fill:currentColor}.bookmark-outline-footer .multi-selection-button{width:auto;padding:7px}.bookmark-outline-footer .multi-selection-button .Icon{width:18px;height:18px}.bookmark-outline-footer .multi-selection-button:not(:first-child){margin-left:var(--padding-tiny)}.bookmark-outline-footer .multi-selection-button:hover{background-color:transparent}.bookmark-outline-footer .multi-selection-button.disabled:hover,.bookmark-outline-footer .multi-selection-button:disabled:hover{box-shadow:none}\", \"\"]);\n\n// exports\n", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./TextButton.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".TextButton{color:var(--secondary-button-text);width:-moz-fit-content;width:fit-content;padding-left:8px;padding-right:8px}.TextButton .Icon{display:flex;align-items:center}.TextButton svg{color:var(--secondary-button-text);height:14px;width:14px}.TextButton:hover{box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6);background-color:var(--faded-component-background)}\", \"\"]);\n\n// exports\n", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./BookmarksPanel.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.BookmarksPanel{display:flex;flex-direction:column;flex:1;font-size:var(--font-size-default)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .BookmarksPanel{margin:16px;padding:0;overflow-y:auto}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .BookmarksPanel{margin:16px;padding:0;overflow-y:auto}}.BookmarksPanel .bookmark-switch{-webkit-font-smoothing:antialiased;margin-top:var(--padding-medium);padding-left:4px}.BookmarksPanel .bookmark-outline-single-container{margin-top:var(--padding-medium)}.BookmarksPanel .bookmark-outline-single-container[focus-within]:not(.editing){background-color:var(--outline-selected);outline:1px solid var(--bookmark-outline-hover-border)}.BookmarksPanel .bookmark-outline-single-container:focus-within:not(.editing){background-color:var(--outline-selected);outline:1px solid var(--bookmark-outline-hover-border)}.BookmarksPanel .msg-no-bookmark-outline{margin-top:var(--padding)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .BookmarksPanel .ui__choice__label{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .BookmarksPanel .ui__choice__label{font-size:13px}}.BookmarksPanel .ui__choice__input__switch{background-color:var(--gray-6)!important}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .BookmarksPanel .bookmark-outline-row{overflow:auto}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .BookmarksPanel .bookmark-outline-row{overflow:auto}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .BookmarksPanel .bookmark-outline-cancel-button,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .BookmarksPanel .bookmark-outline-control-button,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .BookmarksPanel .bookmark-outline-save-button{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .BookmarksPanel .bookmark-outline-cancel-button,.App.is-web-component:not(.is-in-desktop-only-mode) .BookmarksPanel .bookmark-outline-control-button,.App.is-web-component:not(.is-in-desktop-only-mode) .BookmarksPanel .bookmark-outline-save-button{font-size:13px}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .BookmarksPanel .bookmark-outline-input,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .BookmarksPanel .bookmark-text-input{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .BookmarksPanel .bookmark-outline-input,.App.is-web-component:not(.is-in-desktop-only-mode) .BookmarksPanel .bookmark-text-input{font-size:13px}}.BookmarksPanel .panel-list-row .Button{font-weight:700}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React, { useEffect, useRef, useState } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { useSelector } from 'react-redux';\nimport selectors from 'selectors';\nimport classNames from 'classnames';\nimport core from 'core';\nimport PropTypes from 'prop-types';\nimport Button from '../Button';\nimport TextButton from '../TextButton';\nimport DataElementWrapper from '../DataElementWrapper';\nimport { menuTypes } from '../MoreOptionsContextMenuFlyout/MoreOptionsContextMenuFlyout';\nimport DataElements from 'constants/dataElement';\nimport PanelListItem from 'components/PanelListItem';\n\nimport '../../constants/bookmarksOutlinesShared.scss';\n\nconst propTypes = {\n  text: PropTypes.string.isRequired,\n  label: PropTypes.string.isRequired,\n  defaultLabel: PropTypes.string,\n  pageIndex: PropTypes.number.isRequired,\n  isAdding: PropTypes.bool,\n  isMultiSelectionMode: PropTypes.bool,\n  setSelected: PropTypes.func,\n  onSave: PropTypes.func.isRequired,\n  onRemove: PropTypes.func,\n  onCancel: PropTypes.func,\n  panelSelector: PropTypes.string,\n};\n\nconst Bookmark = ({\n  text,\n  label,\n  defaultLabel,\n  pageIndex,\n  isAdding,\n  isMultiSelectionMode,\n  setSelected,\n  onSave,\n  onRemove,\n  onCancel,\n  panelSelector,\n}) => {\n  const [t] = useTranslation();\n\n  const [isEditing, setIsEditing] = useState(false);\n  const [isDefault, setIsDefault] = useState(false);\n  const [bookmarkText, setBookmarkText] = useState(text);\n  const [clearSingleClick, setClearSingleClick] = useState(undefined);\n  const inputRef = useRef();\n\n  const isRenameButtonDisabled = () => {\n    return !bookmarkText || text === bookmarkText;\n  };\n\n  const handleKeyDown = (e) => {\n    if (e.key === 'Enter') {\n      e.stopPropagation();\n      if (isAdding || (isEditing && !isRenameButtonDisabled())) {\n        onSaveBookmark();\n      }\n    }\n    if (e.key === 'Escape') {\n      onCancelBookmark();\n    }\n  };\n\n  const onSaveBookmark = () => {\n    setIsEditing(false);\n    onSave(bookmarkText.trim() === '' ? t('message.untitled') : bookmarkText);\n  };\n\n  const onCancelBookmark = () => {\n    setIsEditing(false);\n    // on cancel reset local bookmark text\n    isEditing && setBookmarkText(text);\n    isAdding && onCancel();\n  };\n\n  const setCurrentPage = (pageIndex) => {\n    core.setCurrentPage(pageIndex + 1);\n  };\n\n  useEffect(() => {\n    if (bookmarkText !== text) {\n      setBookmarkText(text);\n    }\n  }, [text]);\n\n  useEffect(() => {\n    if (isAdding || isEditing) {\n      inputRef.current.focus();\n      inputRef.current.select();\n    }\n\n    if (!isAdding && !isEditing) {\n      setIsDefault(true);\n    } else {\n      setIsDefault(false);\n    }\n  }, [isEditing]);\n\n  const handleOnClick = (val) => {\n    switch (val) {\n      case menuTypes.RENAME:\n        setIsEditing(true);\n        break;\n      case menuTypes.DELETE:\n        onRemove(pageIndex);\n        break;\n      default:\n        break;\n    }\n  };\n\n  const flyoutSelector = `${DataElements.BOOKMARK_FLYOUT}-${pageIndex}`;\n  const currentFlyout = useSelector((state) => selectors.getFlyout(state, flyoutSelector));\n  const bookmarkMoreOptionsDataElement = `bookmark-more-button-${panelSelector}-${pageIndex}`;\n  const panelListType = 'bookmark';\n\n  const bookmarkPanelListProps = {\n    labelHeader: defaultLabel,\n    description: text,\n    enableMoreOptionsContextMenuFlyout: true,\n    contentMenuFlyoutOptions: {\n      shouldHideDeleteButton: false,\n      currentFlyout: currentFlyout,\n      flyoutSelector: flyoutSelector,\n      type: panelListType,\n      handleOnClick : handleOnClick,\n    },\n    contextMenuMoreButtonOptions: {\n      flyoutToggleElement: flyoutSelector,\n      moreOptionsDataElement: bookmarkMoreOptionsDataElement,\n    },\n    onClick: (e) => {\n      if (isDefault && e.detail === 1) {\n        setClearSingleClick(setTimeout(() => {\n          setCurrentPage(pageIndex);\n        }, 300));\n      }\n    },\n    onDoubleClick: () => {\n      if (isDefault) {\n        setIsEditing(true);\n      }\n    },\n    checkboxOptions: {\n      id: `bookmark-checkbox-${pageIndex + 1}`,\n      onChange: (e) => {\n        setSelected(pageIndex, e.target.checked);\n      },\n      ariaLabel: `${t('action.select')} ${label}`,\n      disabled: !isMultiSelectionMode\n    }\n  };\n\n  return (\n    <>\n      {isDefault && <PanelListItem {...bookmarkPanelListProps} />}\n      {(isAdding || isEditing) && <DataElementWrapper\n        className={classNames({\n          'bookmark-outline-single-container': true,\n          'editing': isAdding || isEditing,\n          'default': isDefault,\n        })}\n        onDoubleClick={() => {\n          if (isDefault) {\n            clearTimeout(clearSingleClick);\n          }\n        }}\n      >\n        <div className=\"bookmark-outline-label-row\">\n          <div className=\"bookmark-outline-label\">{(isAdding || isEditing) ? label : defaultLabel}</div>\n          <input\n            type=\"text\"\n            name=\"bookmark\"\n            ref={inputRef}\n            className=\"bookmark-outline-input bookmark-text-input\"\n            aria-label={t('action.name')}\n            value={bookmarkText}\n            onKeyDown={handleKeyDown}\n            onChange={(e) => setBookmarkText(e.target.value)}\n          />\n          <div className=\"bookmark-outline-editing-controls\">\n            <TextButton\n              label={t('action.cancel')}\n              onClick={onCancelBookmark}\n              ariaLabel={`${t('action.cancel')} ${t('component.bookmarkPanel')}`}\n            />\n            {isAdding &&\n              <Button\n                className=\"bookmark-outline-save-button\"\n                label={t('action.add')}\n                isSubmitType\n                onClick={onSaveBookmark}\n                ariaLabel={`${t('action.add')} ${t('component.bookmarkPanel')}`}\n              />\n            }\n            {isEditing &&\n              <Button\n                className=\"bookmark-outline-save-button\"\n                label={t('action.save')}\n                isSubmitType\n                disabled={isRenameButtonDisabled()}\n                onClick={onSaveBookmark}\n                ariaLabel={`${t('action.save')} ${t('component.bookmarkPanel')}`}\n              />\n            }\n          </div>\n        </div>\n      </DataElementWrapper>}\n    </>\n  );\n};\n\nBookmark.propTypes = propTypes;\n\nexport default Bookmark;", "import Bookmark from './Bookmark';\n\nexport default Bookmark;\n", "import React, { useEffect, useState } from 'react';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\n\nimport Bookmark from 'components/Bookmark';\nimport Button from 'components/Button';\nimport DataElementWrapper from 'components/DataElementWrapper';\nimport DataElements from 'constants/dataElement';\nimport Choice from 'components/Choice';\nimport TextButton from '../TextButton';\n\nimport core from 'core';\nimport actions from 'actions';\nimport selectors from 'selectors';\n\nimport '../../constants/bookmarksOutlinesShared.scss';\nimport './BookmarksPanel.scss';\nimport classNames from 'classnames';\n\nconst BookmarksPanel = ({ panelSelector }) => {\n  const [\n    isDisabled,\n    bookmarks,\n    currentPageIndex,\n    pageLabels,\n    isBookmarkIconShortcutVisible,\n    featureFlags,\n  ] = useSelector(\n    (state) => [\n      selectors.isElementDisabled(state, DataElements.BOOKMARK_PANEL),\n      selectors.getBookmarks(state),\n      selectors.getCurrentPage(state) - 1,\n      selectors.getPageLabels(state),\n      selectors.isBookmarkIconShortcutVisible(state),\n      selectors.getFeatureFlags(state),\n    ],\n    shallowEqual,\n  );\n\n  const [isAddingNewBookmark, setAddingNewBookmark] = useState(false);\n  const [isMultiSelectionMode, setMultiSelectionMode] = useState(false);\n  const [selectingBookmarks, setSelectingBookmarks] = useState([]);\n  const customizableUI = featureFlags.customizableUI;\n\n  const [t] = useTranslation();\n  const dispatch = useDispatch();\n\n  useEffect(() => {\n    if (isBookmarkIconShortcutVisible && !isDisabled) {\n      core.setBookmarkIconShortcutVisibility(true);\n    } else {\n      core.setBookmarkIconShortcutVisibility(false);\n    }\n  }, [isDisabled, isBookmarkIconShortcutVisible]);\n\n  const pageIndices = Object.keys(bookmarks).map((pageIndex) => parseInt(pageIndex, 10));\n\n  useEffect(() => {\n    // if bookmark is deleted from the shortcut, should also remove from selectingBookmarks\n    selectingBookmarks.forEach((index) => {\n      if (!pageIndices.includes(index)) {\n        setSelectingBookmarks(selectingBookmarks.filter((bm) => bm !== index));\n      }\n    });\n\n    const shouldResetMultiSelectMode = pageIndices.length === 0;\n    if (shouldResetMultiSelectMode) {\n      setMultiSelectionMode(false);\n    }\n  }, [bookmarks]);\n\n  const onRemoveBookmarks = (pageIndexes) => {\n    const title = t('warning.deleteBookmark.title');\n    const message = t('warning.deleteBookmark.message');\n    const confirmationWarning = {\n      message,\n      title,\n      onConfirm: () => {\n        pageIndexes.forEach((pageIndex) => core.removeUserBookmark(Number(pageIndex)));\n        setSelectingBookmarks([]);\n      },\n      confirmBtnText: t('action.delete'),\n    };\n    dispatch(actions.showWarningMessage(confirmationWarning));\n  };\n\n  if (isDisabled) {\n    return null;\n  }\n\n  return (\n    <div\n      className={classNames({\n        'Panel': true,\n        'BookmarksPanel': true,\n        'bookmark-outline-panel': true,\n        [panelSelector]: true,\n        'modular-ui-panel': customizableUI,\n      })}\n      data-element={DataElements.BOOKMARK_PANEL}\n    >\n      <div className=\"bookmark-outline-panel-header\">\n        <div className=\"header-title\">\n          {t('component.bookmarksPanel')}\n        </div>\n        {!isMultiSelectionMode &&\n          <TextButton\n            dataElement={DataElements.BOOKMARK_MULTI_SELECT}\n            label={t('action.edit')}\n            disabled={isAddingNewBookmark || pageIndices.length === 0}\n            onClick={() => setMultiSelectionMode(true)}\n            ariaLabel={`${t('action.edit')} ${t('component.bookmarksPanel')}`}\n          />\n        }\n        {isMultiSelectionMode &&\n          <TextButton\n            dataElement={DataElements.BOOKMARK_MULTI_SELECT}\n            label={t('option.bookmarkOutlineControls.done')}\n            disabled={isAddingNewBookmark}\n            onClick={() => {\n              setMultiSelectionMode(false);\n              setSelectingBookmarks([]);\n            }}\n            ariaLabel={`${t('option.bookmarkOutlineControls.done')} ${t('action.edit')}`}\n          />\n        }\n      </div>\n\n      <Choice\n        dataElement={DataElements.BOOKMARK_SHORTCUT_OPTION}\n        type=\"checkbox\"\n        isSwitch\n        id=\"bookmark-view-option\"\n        className=\"bookmark-switch\"\n        label={t('message.viewBookmark')}\n        checked={isBookmarkIconShortcutVisible}\n        onChange={(e) => dispatch(actions.setBookmarkIconShortcutVisibility(e.target.checked))}\n      />\n\n      {!isAddingNewBookmark && pageIndices.length === 0 && (\n        <div className=\"msg msg-no-bookmark-outline\">{t('message.noBookmarks')}</div>\n      )}\n\n      <div className=\"bookmark-outline-row\">\n        {isAddingNewBookmark &&\n          <Bookmark\n            isAdding\n            label={`${t('component.bookmarkPage')} ${pageLabels[currentPageIndex]} - ${t('component.bookmarkTitle')}`}\n            text={bookmarks[currentPageIndex] ?? ''}\n            pageIndex={currentPageIndex}\n            onSave={(newText) => {\n              core.addUserBookmark(currentPageIndex, newText);\n              setAddingNewBookmark(false);\n            }}\n            onCancel={() => setAddingNewBookmark(false)}\n            panelSelector={panelSelector}\n          />\n        }\n\n        {pageIndices.map((pageIndex) => (\n          <Bookmark\n            key={pageIndex}\n            panelSelector={panelSelector}\n            isMultiSelectionMode={isMultiSelectionMode}\n            label={`${t('component.bookmarkPage')} ${pageLabels[pageIndex]} - ${t('component.bookmarkTitle')}`}\n            defaultLabel={`${t('component.bookmarkPage')} ${pageLabels[pageIndex]}`}\n            text={bookmarks[pageIndex]}\n            pageIndex={pageIndex}\n            onSave={(newText) => {\n              const updatedBookmarks = {\n                ...core.getUserBookmarks(),\n                [pageIndex]: newText,\n              };\n              core.setUserBookmarks(updatedBookmarks);\n            }}\n            onRemove={(index) => onRemoveBookmarks([index])}\n            setSelected={(index, val) => {\n              // need to stringify the index because using 0 instead of '0' makes the val check fail\n              index = index.toString();\n              if (selectingBookmarks.find((bm) => bm === index)) {\n                if (!val) {\n                  setSelectingBookmarks(selectingBookmarks.filter((bm) => bm !== index));\n                }\n              } else {\n                if (val) {\n                  setSelectingBookmarks([...selectingBookmarks, index]);\n                }\n              }\n            }}\n          />\n        ))}\n      </div>\n\n      <DataElementWrapper\n        className=\"bookmark-outline-footer\"\n        dataElement={DataElements.BOOKMARK_ADD_NEW_BUTTON_CONTAINER}\n      >\n        {isMultiSelectionMode ?\n          <>\n            <Button\n              className=\"multi-selection-button\"\n              img=\"icon-menu-add\"\n              disabled={selectingBookmarks.length > 0 || !!bookmarks[currentPageIndex] || isAddingNewBookmark}\n              onClick={() => setAddingNewBookmark(true)}\n              ariaLabel={`${t('action.add')} ${t('component.bookmarkPanel')}`}\n            />\n            <Button\n              className=\"multi-selection-button\"\n              img=\"icon-delete-line\"\n              disabled={selectingBookmarks.length === 0}\n              onClick={() => onRemoveBookmarks(selectingBookmarks)}\n              ariaLabel={`${t('action.delete')} ${t('component.bookmarkPanel')}`}\n            />\n          </>\n          :\n          <TextButton\n            img=\"icon-menu-add\"\n            dataElement={DataElements.BOOKMARK_ADD_NEW_BUTTON}\n            label={`${t('action.add')} ${t('component.bookmarkPanel')}`}\n            disabled={isAddingNewBookmark || !!bookmarks[currentPageIndex]}\n            onClick={() => setAddingNewBookmark(true)}\n            ariaLabel={`${t('action.add')} ${t('component.bookmarkPanel')}`}\n          />\n        }\n      </DataElementWrapper>\n    </div>\n  );\n};\n\nexport default BookmarksPanel;", "import BookmarksPanel from './BookmarksPanel';\n\nexport default BookmarksPanel;\n"], "sourceRoot": ""}