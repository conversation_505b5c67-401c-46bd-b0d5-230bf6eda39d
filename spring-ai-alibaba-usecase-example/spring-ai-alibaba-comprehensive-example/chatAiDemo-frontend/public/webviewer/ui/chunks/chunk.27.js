(window.webpackJsonp=window.webpackJsonp||[]).push([[27],{1474:function(e,t,n){"use strict";n.d(t,"a",(function(){return d})),n.d(t,"b",(function(){return g})),n.d(t,"c",(function(){return f})),n.d(t,"d",(function(){return c})),n.d(t,"e",(function(){return h})),n.d(t,"f",(function(){return b})),n.d(t,"g",(function(){return l})),n.d(t,"h",(function(){return p}));var r=n(0),o=n(1490),i=(n(141),n(1491),n(1511),n(1508)),a=n(1518),u=n(1509),c=!1,l="undefined"!=typeof document,s=r.createContext("undefined"!=typeof HTMLElement?Object(o.a)({key:"css"}):null),d=s.Provider,p=function(e){return Object(r.forwardRef)((function(t,n){var o=Object(r.useContext)(s);return e(t,o,n)}))};l||(p=function(e){return function(t){var n=Object(r.useContext)(s);return null===n?(n=Object(o.a)({key:"css"}),r.createElement(s.Provider,{value:n},e(t,n))):e(t,n)}});var f=r.createContext({});var b={}.hasOwnProperty,m="__EMOTION_TYPE_PLEASE_DO_NOT_USE__",h=function(e,t){var n={};for(var r in t)b.call(t,r)&&(n[r]=t[r]);return n[m]=e,n},v=function(e){var t=e.cache,n=e.serialized,o=e.isStringTag;Object(i.c)(t,n,o);var a=Object(u.a)((function(){return Object(i.b)(t,n,o)}));if(!l&&void 0!==a){for(var c,s=n.name,d=n.next;void 0!==d;)s+=" "+d.name,d=d.next;return r.createElement("style",((c={})["data-emotion"]=t.key+" "+s,c.dangerouslySetInnerHTML={__html:a},c.nonce=t.sheet.nonce,c))}return null},g=p((function(e,t,n){var o=e.css;"string"==typeof o&&void 0!==t.registered[o]&&(o=t.registered[o]);var u=e[m],l=[o],s="";"string"==typeof e.className?s=Object(i.a)(t.registered,l,e.className):null!=e.className&&(s=e.className+" ");var d=Object(a.a)(l,void 0,r.useContext(f));s+=t.key+"-"+d.name;var p={};for(var h in e)b.call(e,h)&&"css"!==h&&h!==m&&!c&&(p[h]=e[h]);return p.className=s,n&&(p.ref=n),r.createElement(r.Fragment,null,r.createElement(v,{cache:t,serialized:d,isStringTag:"string"==typeof u}),r.createElement(u,p))}))},1482:function(e,t,n){"use strict";n.d(t,"a",(function(){return u})),n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return c}));var r=n(1474),o=n(0),i=(n(1508),n(1509),n(1518)),a=(n(1490),n(1475),n(1491),n(344),function(e,t){var n=arguments;if(null==t||!r.f.call(t,"css"))return o.createElement.apply(void 0,n);var i=n.length,a=new Array(i);a[0]=r.b,a[1]=Object(r.e)(e,t);for(var u=2;u<i;u++)a[u]=n[u];return o.createElement.apply(null,a)});function u(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return Object(i.a)(t)}var c=function(){var e=u.apply(void 0,arguments),t="animation-"+e.name;return{name:t,styles:"@keyframes "+t+"{"+e.styles+"}",anim:1,toString:function(){return"_EMO_"+this.name+"_"+this.styles+"_EMO_"}}}},1483:function(e,t,n){"use strict";n.d(t,"a",(function(){return me})),n.d(t,"b",(function(){return K})),n.d(t,"c",(function(){return Q}));var r=n(141),o=n(341),i=n(160),a=n(161),u=n(267),c=n(266);function l(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(l=function(){return!!e})()}var s=n(231);var d=n(1517),p=n(0),f=n(1485),b=n(1482),m=n(1501),h=n(1484);for(var v={name:"7pg0cj-a11yText",styles:"label:a11yText;z-index:9999;border:0;clip:rect(1px, 1px, 1px, 1px);height:1px;width:1px;position:absolute;overflow:hidden;padding:0;white-space:nowrap"},g=function(e){return Object(b.b)("span",Object(r.a)({css:v},e))},y={guidance:function(e){var t=e.isSearchable,n=e.isMulti,r=e.tabSelectsValue,o=e.context,i=e.isInitialFocus;switch(o){case"menu":return"Use Up and Down to choose options, press Enter to select the currently focused option, press Escape to exit the menu".concat(r?", press Tab to select the option and exit the menu":"",".");case"input":return i?"".concat(e["aria-label"]||"Select"," is focused ").concat(t?",type to refine list":"",", press Down to open the menu, ").concat(n?" press left to focus selected values":""):"";case"value":return"Use left and right to toggle between focused values, press Backspace to remove the currently focused value";default:return""}},onChange:function(e){var t=e.action,n=e.label,r=void 0===n?"":n,o=e.labels,i=e.isDisabled;switch(t){case"deselect-option":case"pop-value":case"remove-value":return"option ".concat(r,", deselected.");case"clear":return"All selected options have been cleared.";case"initial-input-focus":return"option".concat(o.length>1?"s":""," ").concat(o.join(","),", selected.");case"select-option":return"option ".concat(r,i?" is disabled. Select another option.":", selected.");default:return""}},onFocus:function(e){var t=e.context,n=e.focused,r=e.options,o=e.label,i=void 0===o?"":o,a=e.selectValue,u=e.isDisabled,c=e.isSelected,l=e.isAppleDevice,s=function(e,t){return e&&e.length?"".concat(e.indexOf(t)+1," of ").concat(e.length):""};if("value"===t&&a)return"value ".concat(i," focused, ").concat(s(a,n),".");if("menu"===t&&l){var d=u?" disabled":"",p="".concat(c?" selected":"").concat(d);return"".concat(i).concat(p,", ").concat(s(r,n),".")}return""},onFilter:function(e){var t=e.inputValue,n=e.resultsMessage;return"".concat(n).concat(t?" for search term "+t:"",".")}},O=function(e){var t=e.ariaSelection,n=e.focusedOption,r=e.focusedValue,i=e.focusableOptions,a=e.isFocused,u=e.selectValue,c=e.selectProps,l=e.id,s=e.isAppleDevice,d=c.ariaLiveMessages,f=c.getOptionLabel,m=c.inputValue,h=c.isMulti,v=c.isOptionDisabled,O=c.isSearchable,w=c.menuIsOpen,j=c.options,E=c.screenReaderStatus,x=c.tabSelectsValue,C=c.isLoading,S=c["aria-label"],F=c["aria-live"],P=Object(p.useMemo)((function(){return Object(o.a)(Object(o.a)({},y),d||{})}),[d]),I=Object(p.useMemo)((function(){var e,n="";if(t&&P.onChange){var r=t.option,i=t.options,a=t.removedValue,c=t.removedValues,l=t.value,s=a||r||(e=l,Array.isArray(e)?null:e),d=s?f(s):"",p=i||c||void 0,b=p?p.map(f):[],m=Object(o.a)({isDisabled:s&&v(s,u),label:d,labels:b},t);n=P.onChange(m)}return n}),[t,P,v,u,f]),k=Object(p.useMemo)((function(){var e="",t=n||r,o=!!(n&&u&&u.includes(n));if(t&&P.onFocus){var a={focused:t,label:f(t),isDisabled:v(t,u),isSelected:o,options:i,context:t===n?"menu":"value",selectValue:u,isAppleDevice:s};e=P.onFocus(a)}return e}),[n,r,f,v,P,i,u,s]),A=Object(p.useMemo)((function(){var e="";if(w&&j.length&&!C&&P.onFilter){var t=E({count:i.length});e=P.onFilter({inputValue:m,resultsMessage:t})}return e}),[i,m,w,P,j,E,C]),M="initial-input-focus"===(null==t?void 0:t.action),R=Object(p.useMemo)((function(){var e="";if(P.guidance){var t=r?"value":w?"menu":"input";e=P.guidance({"aria-label":S,context:t,isDisabled:n&&v(n,u),isMulti:h,isSearchable:O,tabSelectsValue:x,isInitialFocus:M})}return e}),[S,n,r,h,v,O,w,P,u,x,M]),D=Object(b.b)(p.Fragment,null,Object(b.b)("span",{id:"aria-selection"},I),Object(b.b)("span",{id:"aria-focused"},k),Object(b.b)("span",{id:"aria-results"},A),Object(b.b)("span",{id:"aria-guidance"},R));return Object(b.b)(p.Fragment,null,Object(b.b)(g,{id:l},M&&D),Object(b.b)(g,{"aria-live":F,"aria-atomic":"false","aria-relevant":"additions text",role:"log"},a&&!M&&D))},w=[{base:"A",letters:"AⒶＡÀÁÂẦẤẪẨÃĀĂẰẮẴẲȦǠÄǞẢÅǺǍȀȂẠẬẶḀĄȺⱯ"},{base:"AA",letters:"Ꜳ"},{base:"AE",letters:"ÆǼǢ"},{base:"AO",letters:"Ꜵ"},{base:"AU",letters:"Ꜷ"},{base:"AV",letters:"ꜸꜺ"},{base:"AY",letters:"Ꜽ"},{base:"B",letters:"BⒷＢḂḄḆɃƂƁ"},{base:"C",letters:"CⒸＣĆĈĊČÇḈƇȻꜾ"},{base:"D",letters:"DⒹＤḊĎḌḐḒḎĐƋƊƉꝹ"},{base:"DZ",letters:"ǱǄ"},{base:"Dz",letters:"ǲǅ"},{base:"E",letters:"EⒺＥÈÉÊỀẾỄỂẼĒḔḖĔĖËẺĚȄȆẸỆȨḜĘḘḚƐƎ"},{base:"F",letters:"FⒻＦḞƑꝻ"},{base:"G",letters:"GⒼＧǴĜḠĞĠǦĢǤƓꞠꝽꝾ"},{base:"H",letters:"HⒽＨĤḢḦȞḤḨḪĦⱧⱵꞍ"},{base:"I",letters:"IⒾＩÌÍÎĨĪĬİÏḮỈǏȈȊỊĮḬƗ"},{base:"J",letters:"JⒿＪĴɈ"},{base:"K",letters:"KⓀＫḰǨḲĶḴƘⱩꝀꝂꝄꞢ"},{base:"L",letters:"LⓁＬĿĹĽḶḸĻḼḺŁȽⱢⱠꝈꝆꞀ"},{base:"LJ",letters:"Ǉ"},{base:"Lj",letters:"ǈ"},{base:"M",letters:"MⓂＭḾṀṂⱮƜ"},{base:"N",letters:"NⓃＮǸŃÑṄŇṆŅṊṈȠƝꞐꞤ"},{base:"NJ",letters:"Ǌ"},{base:"Nj",letters:"ǋ"},{base:"O",letters:"OⓄＯÒÓÔỒỐỖỔÕṌȬṎŌṐṒŎȮȰÖȪỎŐǑȌȎƠỜỚỠỞỢỌỘǪǬØǾƆƟꝊꝌ"},{base:"OI",letters:"Ƣ"},{base:"OO",letters:"Ꝏ"},{base:"OU",letters:"Ȣ"},{base:"P",letters:"PⓅＰṔṖƤⱣꝐꝒꝔ"},{base:"Q",letters:"QⓆＱꝖꝘɊ"},{base:"R",letters:"RⓇＲŔṘŘȐȒṚṜŖṞɌⱤꝚꞦꞂ"},{base:"S",letters:"SⓈＳẞŚṤŜṠŠṦṢṨȘŞⱾꞨꞄ"},{base:"T",letters:"TⓉＴṪŤṬȚŢṰṮŦƬƮȾꞆ"},{base:"TZ",letters:"Ꜩ"},{base:"U",letters:"UⓊＵÙÚÛŨṸŪṺŬÜǛǗǕǙỦŮŰǓȔȖƯỪỨỮỬỰỤṲŲṶṴɄ"},{base:"V",letters:"VⓋＶṼṾƲꝞɅ"},{base:"VY",letters:"Ꝡ"},{base:"W",letters:"WⓌＷẀẂŴẆẄẈⱲ"},{base:"X",letters:"XⓍＸẊẌ"},{base:"Y",letters:"YⓎＹỲÝŶỸȲẎŸỶỴƳɎỾ"},{base:"Z",letters:"ZⓏＺŹẐŻŽẒẔƵȤⱿⱫꝢ"},{base:"a",letters:"aⓐａẚàáâầấẫẩãāăằắẵẳȧǡäǟảåǻǎȁȃạậặḁąⱥɐ"},{base:"aa",letters:"ꜳ"},{base:"ae",letters:"æǽǣ"},{base:"ao",letters:"ꜵ"},{base:"au",letters:"ꜷ"},{base:"av",letters:"ꜹꜻ"},{base:"ay",letters:"ꜽ"},{base:"b",letters:"bⓑｂḃḅḇƀƃɓ"},{base:"c",letters:"cⓒｃćĉċčçḉƈȼꜿↄ"},{base:"d",letters:"dⓓｄḋďḍḑḓḏđƌɖɗꝺ"},{base:"dz",letters:"ǳǆ"},{base:"e",letters:"eⓔｅèéêềếễểẽēḕḗĕėëẻěȅȇẹệȩḝęḙḛɇɛǝ"},{base:"f",letters:"fⓕｆḟƒꝼ"},{base:"g",letters:"gⓖｇǵĝḡğġǧģǥɠꞡᵹꝿ"},{base:"h",letters:"hⓗｈĥḣḧȟḥḩḫẖħⱨⱶɥ"},{base:"hv",letters:"ƕ"},{base:"i",letters:"iⓘｉìíîĩīĭïḯỉǐȉȋịįḭɨı"},{base:"j",letters:"jⓙｊĵǰɉ"},{base:"k",letters:"kⓚｋḱǩḳķḵƙⱪꝁꝃꝅꞣ"},{base:"l",letters:"lⓛｌŀĺľḷḹļḽḻſłƚɫⱡꝉꞁꝇ"},{base:"lj",letters:"ǉ"},{base:"m",letters:"mⓜｍḿṁṃɱɯ"},{base:"n",letters:"nⓝｎǹńñṅňṇņṋṉƞɲŉꞑꞥ"},{base:"nj",letters:"ǌ"},{base:"o",letters:"oⓞｏòóôồốỗổõṍȭṏōṑṓŏȯȱöȫỏőǒȍȏơờớỡởợọộǫǭøǿɔꝋꝍɵ"},{base:"oi",letters:"ƣ"},{base:"ou",letters:"ȣ"},{base:"oo",letters:"ꝏ"},{base:"p",letters:"pⓟｐṕṗƥᵽꝑꝓꝕ"},{base:"q",letters:"qⓠｑɋꝗꝙ"},{base:"r",letters:"rⓡｒŕṙřȑȓṛṝŗṟɍɽꝛꞧꞃ"},{base:"s",letters:"sⓢｓßśṥŝṡšṧṣṩșşȿꞩꞅẛ"},{base:"t",letters:"tⓣｔṫẗťṭțţṱṯŧƭʈⱦꞇ"},{base:"tz",letters:"ꜩ"},{base:"u",letters:"uⓤｕùúûũṹūṻŭüǜǘǖǚủůűǔȕȗưừứữửựụṳųṷṵʉ"},{base:"v",letters:"vⓥｖṽṿʋꝟʌ"},{base:"vy",letters:"ꝡ"},{base:"w",letters:"wⓦｗẁẃŵẇẅẘẉⱳ"},{base:"x",letters:"xⓧｘẋẍ"},{base:"y",letters:"yⓨｙỳýŷỹȳẏÿỷẙỵƴɏỿ"},{base:"z",letters:"zⓩｚźẑżžẓẕƶȥɀⱬꝣ"}],j=new RegExp("["+w.map((function(e){return e.letters})).join("")+"]","g"),E={},x=0;x<w.length;x++)for(var C=w[x],S=0;S<C.letters.length;S++)E[C.letters[S]]=C.base;var F=function(e){return e.replace(j,(function(e){return E[e]}))},P=Object(m.a)(F),I=function(e){return e.replace(/^\s+|\s+$/g,"")},k=function(e){return"".concat(e.label," ").concat(e.value)},A=["innerRef"];function M(e){var t=e.innerRef,n=Object(h.a)(e,A),o=Object(f.D)(n,"onExited","in","enter","exit","appear");return Object(b.b)("input",Object(r.a)({ref:t},o,{css:Object(b.a)({label:"dummyInput",background:0,border:0,caretColor:"transparent",fontSize:"inherit",gridArea:"1 / 1 / 2 / 3",outline:0,padding:0,width:1,color:"transparent",left:-100,opacity:0,position:"relative",transform:"scale(.01)"},"","")}))}var R=["boxSizing","height","overflow","paddingRight","position"],D={boxSizing:"border-box",overflow:"hidden",position:"relative",height:"100%"};function V(e){e.preventDefault()}function L(e){e.stopPropagation()}function T(){var e=this.scrollTop,t=this.scrollHeight,n=e+this.offsetHeight;0===e?this.scrollTop=1:n===t&&(this.scrollTop=e-1)}function H(){return"ontouchstart"in window||navigator.maxTouchPoints}var N=!("undefined"==typeof window||!window.document||!window.document.createElement),_=0,B={capture:!1,passive:!1};var U=function(e){var t=e.target;return t.ownerDocument.activeElement&&t.ownerDocument.activeElement.blur()},W={name:"1kfdb0e",styles:"position:fixed;left:0;bottom:0;right:0;top:0"};function z(e){var t=e.children,n=e.lockEnabled,r=e.captureEnabled,o=function(e){var t=e.isEnabled,n=e.onBottomArrive,r=e.onBottomLeave,o=e.onTopArrive,i=e.onTopLeave,a=Object(p.useRef)(!1),u=Object(p.useRef)(!1),c=Object(p.useRef)(0),l=Object(p.useRef)(null),s=Object(p.useCallback)((function(e,t){if(null!==l.current){var c=l.current,s=c.scrollTop,d=c.scrollHeight,p=c.clientHeight,f=l.current,b=t>0,m=d-p-s,h=!1;m>t&&a.current&&(r&&r(e),a.current=!1),b&&u.current&&(i&&i(e),u.current=!1),b&&t>m?(n&&!a.current&&n(e),f.scrollTop=d,h=!0,a.current=!0):!b&&-t>s&&(o&&!u.current&&o(e),f.scrollTop=0,h=!0,u.current=!0),h&&function(e){e.cancelable&&e.preventDefault(),e.stopPropagation()}(e)}}),[n,r,o,i]),d=Object(p.useCallback)((function(e){s(e,e.deltaY)}),[s]),b=Object(p.useCallback)((function(e){c.current=e.changedTouches[0].clientY}),[]),m=Object(p.useCallback)((function(e){var t=c.current-e.changedTouches[0].clientY;s(e,t)}),[s]),h=Object(p.useCallback)((function(e){if(e){var t=!!f.E&&{passive:!1};e.addEventListener("wheel",d,t),e.addEventListener("touchstart",b,t),e.addEventListener("touchmove",m,t)}}),[m,b,d]),v=Object(p.useCallback)((function(e){e&&(e.removeEventListener("wheel",d,!1),e.removeEventListener("touchstart",b,!1),e.removeEventListener("touchmove",m,!1))}),[m,b,d]);return Object(p.useEffect)((function(){if(t){var e=l.current;return h(e),function(){v(e)}}}),[t,h,v]),function(e){l.current=e}}({isEnabled:void 0===r||r,onBottomArrive:e.onBottomArrive,onBottomLeave:e.onBottomLeave,onTopArrive:e.onTopArrive,onTopLeave:e.onTopLeave}),i=function(e){var t=e.isEnabled,n=e.accountForScrollbars,r=void 0===n||n,o=Object(p.useRef)({}),i=Object(p.useRef)(null),a=Object(p.useCallback)((function(e){if(N){var t=document.body,n=t&&t.style;if(r&&R.forEach((function(e){var t=n&&n[e];o.current[e]=t})),r&&_<1){var i=parseInt(o.current.paddingRight,10)||0,a=document.body?document.body.clientWidth:0,u=window.innerWidth-a+i||0;Object.keys(D).forEach((function(e){var t=D[e];n&&(n[e]=t)})),n&&(n.paddingRight="".concat(u,"px"))}t&&H()&&(t.addEventListener("touchmove",V,B),e&&(e.addEventListener("touchstart",T,B),e.addEventListener("touchmove",L,B))),_+=1}}),[r]),u=Object(p.useCallback)((function(e){if(N){var t=document.body,n=t&&t.style;_=Math.max(_-1,0),r&&_<1&&R.forEach((function(e){var t=o.current[e];n&&(n[e]=t)})),t&&H()&&(t.removeEventListener("touchmove",V,B),e&&(e.removeEventListener("touchstart",T,B),e.removeEventListener("touchmove",L,B)))}}),[r]);return Object(p.useEffect)((function(){if(t){var e=i.current;return a(e),function(){u(e)}}}),[t,a,u]),function(e){i.current=e}}({isEnabled:n});return Object(b.b)(p.Fragment,null,n&&Object(b.b)("div",{onClick:U,css:W}),t((function(e){o(e),i(e)})))}var $={name:"1a0ro4n-requiredInput",styles:"label:requiredInput;opacity:0;pointer-events:none;position:absolute;bottom:0;left:0;right:0;width:100%"},q=function(e){var t=e.name,n=e.onFocus;return Object(b.b)("input",{required:!0,name:t,tabIndex:-1,"aria-hidden":"true",onFocus:n,css:$,value:"",onChange:function(){}})};function Y(e){var t;return"undefined"!=typeof window&&null!=window.navigator&&e.test((null===(t=window.navigator.userAgentData)||void 0===t?void 0:t.platform)||window.navigator.platform)}function G(){return Y(/^Mac/i)}function X(){return Y(/^iPhone/i)||Y(/^iPad/i)||G()&&navigator.maxTouchPoints>1}var K=function(e){return e.label},Q=function(e){return e.value},J={clearIndicator:f.m,container:f.n,control:f.p,dropdownIndicator:f.q,group:f.s,groupHeading:f.r,indicatorsContainer:f.u,indicatorSeparator:f.t,input:f.v,loadingIndicator:f.x,loadingMessage:f.w,menu:f.y,menuList:f.z,menuPortal:f.A,multiValue:f.B,multiValueLabel:f.C,multiValueRemove:f.F,noOptionsMessage:f.G,option:f.H,placeholder:f.I,singleValue:f.J,valueContainer:f.K};var Z,ee={borderRadius:4,colors:{primary:"#2684FF",primary75:"#4C9AFF",primary50:"#B2D4FF",primary25:"#DEEBFF",danger:"#DE350B",dangerLight:"#FFBDAD",neutral0:"hsl(0, 0%, 100%)",neutral5:"hsl(0, 0%, 95%)",neutral10:"hsl(0, 0%, 90%)",neutral20:"hsl(0, 0%, 80%)",neutral30:"hsl(0, 0%, 70%)",neutral40:"hsl(0, 0%, 60%)",neutral50:"hsl(0, 0%, 50%)",neutral60:"hsl(0, 0%, 40%)",neutral70:"hsl(0, 0%, 30%)",neutral80:"hsl(0, 0%, 20%)",neutral90:"hsl(0, 0%, 10%)"},spacing:{baseUnit:4,controlHeight:38,menuGutter:8}},te={"aria-live":"polite",backspaceRemovesValue:!0,blurInputOnSelect:Object(f.L)(),captureMenuScroll:!Object(f.L)(),classNames:{},closeMenuOnSelect:!0,closeMenuOnScroll:!1,components:{},controlShouldRenderValue:!0,escapeClearsValue:!1,filterOption:function(e,t){if(e.data.__isNew__)return!0;var n=Object(o.a)({ignoreCase:!0,ignoreAccents:!0,stringify:k,trim:!0,matchFrom:"any"},Z),r=n.ignoreCase,i=n.ignoreAccents,a=n.stringify,u=n.trim,c=n.matchFrom,l=u?I(t):t,s=u?I(a(e)):a(e);return r&&(l=l.toLowerCase(),s=s.toLowerCase()),i&&(l=P(l),s=F(s)),"start"===c?s.substr(0,l.length)===l:s.indexOf(l)>-1},formatGroupLabel:function(e){return e.label},getOptionLabel:K,getOptionValue:Q,isDisabled:!1,isLoading:!1,isMulti:!1,isRtl:!1,isSearchable:!0,isOptionDisabled:function(e){return!!e.isDisabled},loadingMessage:function(){return"Loading..."},maxMenuHeight:300,minMenuHeight:140,menuIsOpen:!1,menuPlacement:"bottom",menuPosition:"absolute",menuShouldBlockScroll:!1,menuShouldScrollIntoView:!Object(f.a)(),noOptionsMessage:function(){return"No options"},openMenuOnFocus:!1,openMenuOnClick:!0,options:[],pageSize:5,placeholder:"Select...",screenReaderStatus:function(e){var t=e.count;return"".concat(t," result").concat(1!==t?"s":""," available")},styles:{},tabIndex:0,tabSelectsValue:!0,unstyled:!1};function ne(e,t,n,r){return{type:"option",data:t,isDisabled:se(e,t,n),isSelected:de(e,t,n),label:ce(e,t),value:le(e,t),index:r}}function re(e,t){return e.options.map((function(n,r){if("options"in n){var o=n.options.map((function(n,r){return ne(e,n,t,r)})).filter((function(t){return ae(e,t)}));return o.length>0?{type:"group",data:n,options:o,index:r}:void 0}var i=ne(e,n,t,r);return ae(e,i)?i:void 0})).filter(f.k)}function oe(e){return e.reduce((function(e,t){return"group"===t.type?e.push.apply(e,Object(d.a)(t.options.map((function(e){return e.data})))):e.push(t.data),e}),[])}function ie(e,t){return e.reduce((function(e,n){return"group"===n.type?e.push.apply(e,Object(d.a)(n.options.map((function(e){return{data:e.data,id:"".concat(t,"-").concat(n.index,"-").concat(e.index)}})))):e.push({data:n.data,id:"".concat(t,"-").concat(n.index)}),e}),[])}function ae(e,t){var n=e.inputValue,r=void 0===n?"":n,o=t.data,i=t.isSelected,a=t.label,u=t.value;return(!fe(e)||!i)&&pe(e,{label:a,value:u,data:o},r)}var ue=function(e,t){var n;return(null===(n=e.find((function(e){return e.data===t})))||void 0===n?void 0:n.id)||null},ce=function(e,t){return e.getOptionLabel(t)},le=function(e,t){return e.getOptionValue(t)};function se(e,t,n){return"function"==typeof e.isOptionDisabled&&e.isOptionDisabled(t,n)}function de(e,t,n){if(n.indexOf(t)>-1)return!0;if("function"==typeof e.isOptionSelected)return e.isOptionSelected(t,n);var r=le(e,t);return n.some((function(t){return le(e,t)===r}))}function pe(e,t,n){return!e.filterOption||e.filterOption(t,n)}var fe=function(e){var t=e.hideSelectedOptions,n=e.isMulti;return void 0===t?n:t},be=1,me=function(e){Object(u.a)(m,e);var t,n,b=(t=m,n=l(),function(){var e,r=Object(c.a)(t);if(n){var o=Object(c.a)(this).constructor;e=Reflect.construct(r,arguments,o)}else e=r.apply(this,arguments);return Object(s.a)(this,e)});function m(e){var t;if(Object(i.a)(this,m),(t=b.call(this,e)).state={ariaSelection:null,focusedOption:null,focusedOptionId:null,focusableOptionsWithIds:[],focusedValue:null,inputIsHidden:!1,isFocused:!1,selectValue:[],clearFocusValueOnUpdate:!1,prevWasFocused:!1,inputIsHiddenAfterUpdate:void 0,prevProps:void 0,instancePrefix:""},t.blockOptionHover=!1,t.isComposing=!1,t.commonProps=void 0,t.initialTouchX=0,t.initialTouchY=0,t.openAfterFocus=!1,t.scrollToFocusedOptionOnUpdate=!1,t.userIsDragging=void 0,t.isAppleDevice=G()||X(),t.controlRef=null,t.getControlRef=function(e){t.controlRef=e},t.focusedOptionRef=null,t.getFocusedOptionRef=function(e){t.focusedOptionRef=e},t.menuListRef=null,t.getMenuListRef=function(e){t.menuListRef=e},t.inputRef=null,t.getInputRef=function(e){t.inputRef=e},t.focus=t.focusInput,t.blur=t.blurInput,t.onChange=function(e,n){var r=t.props,o=r.onChange,i=r.name;n.name=i,t.ariaOnChange(e,n),o(e,n)},t.setValue=function(e,n,r){var o=t.props,i=o.closeMenuOnSelect,a=o.isMulti,u=o.inputValue;t.onInputChange("",{action:"set-value",prevInputValue:u}),i&&(t.setState({inputIsHiddenAfterUpdate:!a}),t.onMenuClose()),t.setState({clearFocusValueOnUpdate:!0}),t.onChange(e,{action:n,option:r})},t.selectOption=function(e){var n=t.props,r=n.blurInputOnSelect,o=n.isMulti,i=n.name,a=t.state.selectValue,u=o&&t.isOptionSelected(e,a),c=t.isOptionDisabled(e,a);if(u){var l=t.getOptionValue(e);t.setValue(Object(f.b)(a.filter((function(e){return t.getOptionValue(e)!==l}))),"deselect-option",e)}else{if(c)return void t.ariaOnChange(Object(f.c)(e),{action:"select-option",option:e,name:i});o?t.setValue(Object(f.b)([].concat(Object(d.a)(a),[e])),"select-option",e):t.setValue(Object(f.c)(e),"select-option")}r&&t.blurInput()},t.removeValue=function(e){var n=t.props.isMulti,r=t.state.selectValue,o=t.getOptionValue(e),i=r.filter((function(e){return t.getOptionValue(e)!==o})),a=Object(f.d)(n,i,i[0]||null);t.onChange(a,{action:"remove-value",removedValue:e}),t.focusInput()},t.clearValue=function(){var e=t.state.selectValue;t.onChange(Object(f.d)(t.props.isMulti,[],null),{action:"clear",removedValues:e})},t.popValue=function(){var e=t.props.isMulti,n=t.state.selectValue,r=n[n.length-1],o=n.slice(0,n.length-1),i=Object(f.d)(e,o,o[0]||null);r&&t.onChange(i,{action:"pop-value",removedValue:r})},t.getFocusedOptionId=function(e){return ue(t.state.focusableOptionsWithIds,e)},t.getFocusableOptionsWithIds=function(){return ie(re(t.props,t.state.selectValue),t.getElementId("option"))},t.getValue=function(){return t.state.selectValue},t.cx=function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return f.e.apply(void 0,[t.props.classNamePrefix].concat(n))},t.getOptionLabel=function(e){return ce(t.props,e)},t.getOptionValue=function(e){return le(t.props,e)},t.getStyles=function(e,n){var r=t.props.unstyled,o=J[e](n,r);o.boxSizing="border-box";var i=t.props.styles[e];return i?i(o,n):o},t.getClassNames=function(e,n){var r,o;return null===(r=(o=t.props.classNames)[e])||void 0===r?void 0:r.call(o,n)},t.getElementId=function(e){return"".concat(t.state.instancePrefix,"-").concat(e)},t.getComponents=function(){return Object(f.f)(t.props)},t.buildCategorizedOptions=function(){return re(t.props,t.state.selectValue)},t.getCategorizedOptions=function(){return t.props.menuIsOpen?t.buildCategorizedOptions():[]},t.buildFocusableOptions=function(){return oe(t.buildCategorizedOptions())},t.getFocusableOptions=function(){return t.props.menuIsOpen?t.buildFocusableOptions():[]},t.ariaOnChange=function(e,n){t.setState({ariaSelection:Object(o.a)({value:e},n)})},t.onMenuMouseDown=function(e){0===e.button&&(e.stopPropagation(),e.preventDefault(),t.focusInput())},t.onMenuMouseMove=function(e){t.blockOptionHover=!1},t.onControlMouseDown=function(e){if(!e.defaultPrevented){var n=t.props.openMenuOnClick;t.state.isFocused?t.props.menuIsOpen?"INPUT"!==e.target.tagName&&"TEXTAREA"!==e.target.tagName&&t.onMenuClose():n&&t.openMenu("first"):(n&&(t.openAfterFocus=!0),t.focusInput()),"INPUT"!==e.target.tagName&&"TEXTAREA"!==e.target.tagName&&e.preventDefault()}},t.onDropdownIndicatorMouseDown=function(e){if(!(e&&"mousedown"===e.type&&0!==e.button||t.props.isDisabled)){var n=t.props,r=n.isMulti,o=n.menuIsOpen;t.focusInput(),o?(t.setState({inputIsHiddenAfterUpdate:!r}),t.onMenuClose()):t.openMenu("first"),e.preventDefault()}},t.onClearIndicatorMouseDown=function(e){e&&"mousedown"===e.type&&0!==e.button||(t.clearValue(),e.preventDefault(),t.openAfterFocus=!1,"touchend"===e.type?t.focusInput():setTimeout((function(){return t.focusInput()})))},t.onScroll=function(e){"boolean"==typeof t.props.closeMenuOnScroll?e.target instanceof HTMLElement&&Object(f.g)(e.target)&&t.props.onMenuClose():"function"==typeof t.props.closeMenuOnScroll&&t.props.closeMenuOnScroll(e)&&t.props.onMenuClose()},t.onCompositionStart=function(){t.isComposing=!0},t.onCompositionEnd=function(){t.isComposing=!1},t.onTouchStart=function(e){var n=e.touches,r=n&&n.item(0);r&&(t.initialTouchX=r.clientX,t.initialTouchY=r.clientY,t.userIsDragging=!1)},t.onTouchMove=function(e){var n=e.touches,r=n&&n.item(0);if(r){var o=Math.abs(r.clientX-t.initialTouchX),i=Math.abs(r.clientY-t.initialTouchY);t.userIsDragging=o>5||i>5}},t.onTouchEnd=function(e){t.userIsDragging||(t.controlRef&&!t.controlRef.contains(e.target)&&t.menuListRef&&!t.menuListRef.contains(e.target)&&t.blurInput(),t.initialTouchX=0,t.initialTouchY=0)},t.onControlTouchEnd=function(e){t.userIsDragging||t.onControlMouseDown(e)},t.onClearIndicatorTouchEnd=function(e){t.userIsDragging||t.onClearIndicatorMouseDown(e)},t.onDropdownIndicatorTouchEnd=function(e){t.userIsDragging||t.onDropdownIndicatorMouseDown(e)},t.handleInputChange=function(e){var n=t.props.inputValue,r=e.currentTarget.value;t.setState({inputIsHiddenAfterUpdate:!1}),t.onInputChange(r,{action:"input-change",prevInputValue:n}),t.props.menuIsOpen||t.onMenuOpen()},t.onInputFocus=function(e){t.props.onFocus&&t.props.onFocus(e),t.setState({inputIsHiddenAfterUpdate:!1,isFocused:!0}),(t.openAfterFocus||t.props.openMenuOnFocus)&&t.openMenu("first"),t.openAfterFocus=!1},t.onInputBlur=function(e){var n=t.props.inputValue;t.menuListRef&&t.menuListRef.contains(document.activeElement)?t.inputRef.focus():(t.props.onBlur&&t.props.onBlur(e),t.onInputChange("",{action:"input-blur",prevInputValue:n}),t.onMenuClose(),t.setState({focusedValue:null,isFocused:!1}))},t.onOptionHover=function(e){if(!t.blockOptionHover&&t.state.focusedOption!==e){var n=t.getFocusableOptions().indexOf(e);t.setState({focusedOption:e,focusedOptionId:n>-1?t.getFocusedOptionId(e):null})}},t.shouldHideSelectedOptions=function(){return fe(t.props)},t.onValueInputFocus=function(e){e.preventDefault(),e.stopPropagation(),t.focus()},t.onKeyDown=function(e){var n=t.props,r=n.isMulti,o=n.backspaceRemovesValue,i=n.escapeClearsValue,a=n.inputValue,u=n.isClearable,c=n.isDisabled,l=n.menuIsOpen,s=n.onKeyDown,d=n.tabSelectsValue,p=n.openMenuOnFocus,f=t.state,b=f.focusedOption,m=f.focusedValue,h=f.selectValue;if(!(c||"function"==typeof s&&(s(e),e.defaultPrevented))){switch(t.blockOptionHover=!0,e.key){case"ArrowLeft":if(!r||a)return;t.focusValue("previous");break;case"ArrowRight":if(!r||a)return;t.focusValue("next");break;case"Delete":case"Backspace":if(a)return;if(m)t.removeValue(m);else{if(!o)return;r?t.popValue():u&&t.clearValue()}break;case"Tab":if(t.isComposing)return;if(e.shiftKey||!l||!d||!b||p&&t.isOptionSelected(b,h))return;t.selectOption(b);break;case"Enter":if(229===e.keyCode)break;if(l){if(!b)return;if(t.isComposing)return;t.selectOption(b);break}return;case"Escape":l?(t.setState({inputIsHiddenAfterUpdate:!1}),t.onInputChange("",{action:"menu-close",prevInputValue:a}),t.onMenuClose()):u&&i&&t.clearValue();break;case" ":if(a)return;if(!l){t.openMenu("first");break}if(!b)return;t.selectOption(b);break;case"ArrowUp":l?t.focusOption("up"):t.openMenu("last");break;case"ArrowDown":l?t.focusOption("down"):t.openMenu("first");break;case"PageUp":if(!l)return;t.focusOption("pageup");break;case"PageDown":if(!l)return;t.focusOption("pagedown");break;case"Home":if(!l)return;t.focusOption("first");break;case"End":if(!l)return;t.focusOption("last");break;default:return}e.preventDefault()}},t.state.instancePrefix="react-select-"+(t.props.instanceId||++be),t.state.selectValue=Object(f.h)(e.value),e.menuIsOpen&&t.state.selectValue.length){var n=t.getFocusableOptionsWithIds(),r=t.buildFocusableOptions(),a=r.indexOf(t.state.selectValue[0]);t.state.focusableOptionsWithIds=n,t.state.focusedOption=r[a],t.state.focusedOptionId=ue(n,r[a])}return t}return Object(a.a)(m,[{key:"componentDidMount",value:function(){this.startListeningComposition(),this.startListeningToTouch(),this.props.closeMenuOnScroll&&document&&document.addEventListener&&document.addEventListener("scroll",this.onScroll,!0),this.props.autoFocus&&this.focusInput(),this.props.menuIsOpen&&this.state.focusedOption&&this.menuListRef&&this.focusedOptionRef&&Object(f.i)(this.menuListRef,this.focusedOptionRef)}},{key:"componentDidUpdate",value:function(e){var t=this.props,n=t.isDisabled,r=t.menuIsOpen,o=this.state.isFocused;(o&&!n&&e.isDisabled||o&&r&&!e.menuIsOpen)&&this.focusInput(),o&&n&&!e.isDisabled?this.setState({isFocused:!1},this.onMenuClose):o||n||!e.isDisabled||this.inputRef!==document.activeElement||this.setState({isFocused:!0}),this.menuListRef&&this.focusedOptionRef&&this.scrollToFocusedOptionOnUpdate&&(Object(f.i)(this.menuListRef,this.focusedOptionRef),this.scrollToFocusedOptionOnUpdate=!1)}},{key:"componentWillUnmount",value:function(){this.stopListeningComposition(),this.stopListeningToTouch(),document.removeEventListener("scroll",this.onScroll,!0)}},{key:"onMenuOpen",value:function(){this.props.onMenuOpen()}},{key:"onMenuClose",value:function(){this.onInputChange("",{action:"menu-close",prevInputValue:this.props.inputValue}),this.props.onMenuClose()}},{key:"onInputChange",value:function(e,t){this.props.onInputChange(e,t)}},{key:"focusInput",value:function(){this.inputRef&&this.inputRef.focus()}},{key:"blurInput",value:function(){this.inputRef&&this.inputRef.blur()}},{key:"openMenu",value:function(e){var t=this,n=this.state,r=n.selectValue,o=n.isFocused,i=this.buildFocusableOptions(),a="first"===e?0:i.length-1;if(!this.props.isMulti){var u=i.indexOf(r[0]);u>-1&&(a=u)}this.scrollToFocusedOptionOnUpdate=!(o&&this.menuListRef),this.setState({inputIsHiddenAfterUpdate:!1,focusedValue:null,focusedOption:i[a],focusedOptionId:this.getFocusedOptionId(i[a])},(function(){return t.onMenuOpen()}))}},{key:"focusValue",value:function(e){var t=this.state,n=t.selectValue,r=t.focusedValue;if(this.props.isMulti){this.setState({focusedOption:null});var o=n.indexOf(r);r||(o=-1);var i=n.length-1,a=-1;if(n.length){switch(e){case"previous":a=0===o?0:-1===o?i:o-1;break;case"next":o>-1&&o<i&&(a=o+1)}this.setState({inputIsHidden:-1!==a,focusedValue:n[a]})}}}},{key:"focusOption",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"first",t=this.props.pageSize,n=this.state.focusedOption,r=this.getFocusableOptions();if(r.length){var o=0,i=r.indexOf(n);n||(i=-1),"up"===e?o=i>0?i-1:r.length-1:"down"===e?o=(i+1)%r.length:"pageup"===e?(o=i-t)<0&&(o=0):"pagedown"===e?(o=i+t)>r.length-1&&(o=r.length-1):"last"===e&&(o=r.length-1),this.scrollToFocusedOptionOnUpdate=!0,this.setState({focusedOption:r[o],focusedValue:null,focusedOptionId:this.getFocusedOptionId(r[o])})}}},{key:"getTheme",value:function(){return this.props.theme?"function"==typeof this.props.theme?this.props.theme(ee):Object(o.a)(Object(o.a)({},ee),this.props.theme):ee}},{key:"getCommonProps",value:function(){var e=this.clearValue,t=this.cx,n=this.getStyles,r=this.getClassNames,o=this.getValue,i=this.selectOption,a=this.setValue,u=this.props,c=u.isMulti,l=u.isRtl,s=u.options;return{clearValue:e,cx:t,getStyles:n,getClassNames:r,getValue:o,hasValue:this.hasValue(),isMulti:c,isRtl:l,options:s,selectOption:i,selectProps:u,setValue:a,theme:this.getTheme()}}},{key:"hasValue",value:function(){return this.state.selectValue.length>0}},{key:"hasOptions",value:function(){return!!this.getFocusableOptions().length}},{key:"isClearable",value:function(){var e=this.props,t=e.isClearable,n=e.isMulti;return void 0===t?n:t}},{key:"isOptionDisabled",value:function(e,t){return se(this.props,e,t)}},{key:"isOptionSelected",value:function(e,t){return de(this.props,e,t)}},{key:"filterOption",value:function(e,t){return pe(this.props,e,t)}},{key:"formatOptionLabel",value:function(e,t){if("function"==typeof this.props.formatOptionLabel){var n=this.props.inputValue,r=this.state.selectValue;return this.props.formatOptionLabel(e,{context:t,inputValue:n,selectValue:r})}return this.getOptionLabel(e)}},{key:"formatGroupLabel",value:function(e){return this.props.formatGroupLabel(e)}},{key:"startListeningComposition",value:function(){document&&document.addEventListener&&(document.addEventListener("compositionstart",this.onCompositionStart,!1),document.addEventListener("compositionend",this.onCompositionEnd,!1))}},{key:"stopListeningComposition",value:function(){document&&document.removeEventListener&&(document.removeEventListener("compositionstart",this.onCompositionStart),document.removeEventListener("compositionend",this.onCompositionEnd))}},{key:"startListeningToTouch",value:function(){document&&document.addEventListener&&(document.addEventListener("touchstart",this.onTouchStart,!1),document.addEventListener("touchmove",this.onTouchMove,!1),document.addEventListener("touchend",this.onTouchEnd,!1))}},{key:"stopListeningToTouch",value:function(){document&&document.removeEventListener&&(document.removeEventListener("touchstart",this.onTouchStart),document.removeEventListener("touchmove",this.onTouchMove),document.removeEventListener("touchend",this.onTouchEnd))}},{key:"renderInput",value:function(){var e=this.props,t=e.isDisabled,n=e.isSearchable,i=e.inputId,a=e.inputValue,u=e.tabIndex,c=e.form,l=e.menuIsOpen,s=e.required,d=this.getComponents().Input,b=this.state,m=b.inputIsHidden,h=b.ariaSelection,v=this.commonProps,g=i||this.getElementId("input"),y=Object(o.a)(Object(o.a)(Object(o.a)({"aria-autocomplete":"list","aria-expanded":l,"aria-haspopup":!0,"aria-errormessage":this.props["aria-errormessage"],"aria-invalid":this.props["aria-invalid"],"aria-label":this.props["aria-label"],"aria-labelledby":this.props["aria-labelledby"],"aria-required":s,role:"combobox","aria-activedescendant":this.isAppleDevice?void 0:this.state.focusedOptionId||""},l&&{"aria-controls":this.getElementId("listbox")}),!n&&{"aria-readonly":!0}),this.hasValue()?"initial-input-focus"===(null==h?void 0:h.action)&&{"aria-describedby":this.getElementId("live-region")}:{"aria-describedby":this.getElementId("placeholder")});return n?p.createElement(d,Object(r.a)({},v,{autoCapitalize:"none",autoComplete:"off",autoCorrect:"off",id:g,innerRef:this.getInputRef,isDisabled:t,isHidden:m,onBlur:this.onInputBlur,onChange:this.handleInputChange,onFocus:this.onInputFocus,spellCheck:"false",tabIndex:u,form:c,type:"text",value:a},y)):p.createElement(M,Object(r.a)({id:g,innerRef:this.getInputRef,onBlur:this.onInputBlur,onChange:f.j,onFocus:this.onInputFocus,disabled:t,tabIndex:u,inputMode:"none",form:c,value:""},y))}},{key:"renderPlaceholderOrValue",value:function(){var e=this,t=this.getComponents(),n=t.MultiValue,o=t.MultiValueContainer,i=t.MultiValueLabel,a=t.MultiValueRemove,u=t.SingleValue,c=t.Placeholder,l=this.commonProps,s=this.props,d=s.controlShouldRenderValue,f=s.isDisabled,b=s.isMulti,m=s.inputValue,h=s.placeholder,v=this.state,g=v.selectValue,y=v.focusedValue,O=v.isFocused;if(!this.hasValue()||!d)return m?null:p.createElement(c,Object(r.a)({},l,{key:"placeholder",isDisabled:f,isFocused:O,innerProps:{id:this.getElementId("placeholder")}}),h);if(b)return g.map((function(t,u){var c=t===y,s="".concat(e.getOptionLabel(t),"-").concat(e.getOptionValue(t));return p.createElement(n,Object(r.a)({},l,{components:{Container:o,Label:i,Remove:a},isFocused:c,isDisabled:f,key:s,index:u,removeProps:{onClick:function(){return e.removeValue(t)},onTouchEnd:function(){return e.removeValue(t)},onMouseDown:function(e){e.preventDefault()}},data:t}),e.formatOptionLabel(t,"value"))}));if(m)return null;var w=g[0];return p.createElement(u,Object(r.a)({},l,{data:w,isDisabled:f}),this.formatOptionLabel(w,"value"))}},{key:"renderClearIndicator",value:function(){var e=this.getComponents().ClearIndicator,t=this.commonProps,n=this.props,o=n.isDisabled,i=n.isLoading,a=this.state.isFocused;if(!this.isClearable()||!e||o||!this.hasValue()||i)return null;var u={onMouseDown:this.onClearIndicatorMouseDown,onTouchEnd:this.onClearIndicatorTouchEnd,"aria-hidden":"true"};return p.createElement(e,Object(r.a)({},t,{innerProps:u,isFocused:a}))}},{key:"renderLoadingIndicator",value:function(){var e=this.getComponents().LoadingIndicator,t=this.commonProps,n=this.props,o=n.isDisabled,i=n.isLoading,a=this.state.isFocused;if(!e||!i)return null;return p.createElement(e,Object(r.a)({},t,{innerProps:{"aria-hidden":"true"},isDisabled:o,isFocused:a}))}},{key:"renderIndicatorSeparator",value:function(){var e=this.getComponents(),t=e.DropdownIndicator,n=e.IndicatorSeparator;if(!t||!n)return null;var o=this.commonProps,i=this.props.isDisabled,a=this.state.isFocused;return p.createElement(n,Object(r.a)({},o,{isDisabled:i,isFocused:a}))}},{key:"renderDropdownIndicator",value:function(){var e=this.getComponents().DropdownIndicator;if(!e)return null;var t=this.commonProps,n=this.props.isDisabled,o=this.state.isFocused,i={onMouseDown:this.onDropdownIndicatorMouseDown,onTouchEnd:this.onDropdownIndicatorTouchEnd,"aria-hidden":"true"};return p.createElement(e,Object(r.a)({},t,{innerProps:i,isDisabled:n,isFocused:o}))}},{key:"renderMenu",value:function(){var e=this,t=this.getComponents(),n=t.Group,o=t.GroupHeading,i=t.Menu,a=t.MenuList,u=t.MenuPortal,c=t.LoadingMessage,l=t.NoOptionsMessage,s=t.Option,d=this.commonProps,b=this.state.focusedOption,m=this.props,h=m.captureMenuScroll,v=m.inputValue,g=m.isLoading,y=m.loadingMessage,O=m.minMenuHeight,w=m.maxMenuHeight,j=m.menuIsOpen,E=m.menuPlacement,x=m.menuPosition,C=m.menuPortalTarget,S=m.menuShouldBlockScroll,F=m.menuShouldScrollIntoView,P=m.noOptionsMessage,I=m.onMenuScrollToTop,k=m.onMenuScrollToBottom;if(!j)return null;var A,M=function(t,n){var o=t.type,i=t.data,a=t.isDisabled,u=t.isSelected,c=t.label,l=t.value,f=b===i,m=a?void 0:function(){return e.onOptionHover(i)},h=a?void 0:function(){return e.selectOption(i)},v="".concat(e.getElementId("option"),"-").concat(n),g={id:v,onClick:h,onMouseMove:m,onMouseOver:m,tabIndex:-1,role:"option","aria-selected":e.isAppleDevice?void 0:u};return p.createElement(s,Object(r.a)({},d,{innerProps:g,data:i,isDisabled:a,isSelected:u,key:v,label:c,type:o,value:l,isFocused:f,innerRef:f?e.getFocusedOptionRef:void 0}),e.formatOptionLabel(t.data,"menu"))};if(this.hasOptions())A=this.getCategorizedOptions().map((function(t){if("group"===t.type){var i=t.data,a=t.options,u=t.index,c="".concat(e.getElementId("group"),"-").concat(u),l="".concat(c,"-heading");return p.createElement(n,Object(r.a)({},d,{key:c,data:i,options:a,Heading:o,headingProps:{id:l,data:t.data},label:e.formatGroupLabel(t.data)}),t.options.map((function(e){return M(e,"".concat(u,"-").concat(e.index))})))}if("option"===t.type)return M(t,"".concat(t.index))}));else if(g){var R=y({inputValue:v});if(null===R)return null;A=p.createElement(c,d,R)}else{var D=P({inputValue:v});if(null===D)return null;A=p.createElement(l,d,D)}var V={minMenuHeight:O,maxMenuHeight:w,menuPlacement:E,menuPosition:x,menuShouldScrollIntoView:F},L=p.createElement(f.l,Object(r.a)({},d,V),(function(t){var n=t.ref,o=t.placerProps,u=o.placement,c=o.maxHeight;return p.createElement(i,Object(r.a)({},d,V,{innerRef:n,innerProps:{onMouseDown:e.onMenuMouseDown,onMouseMove:e.onMenuMouseMove},isLoading:g,placement:u}),p.createElement(z,{captureEnabled:h,onTopArrive:I,onBottomArrive:k,lockEnabled:S},(function(t){return p.createElement(a,Object(r.a)({},d,{innerRef:function(n){e.getMenuListRef(n),t(n)},innerProps:{role:"listbox","aria-multiselectable":d.isMulti,id:e.getElementId("listbox")},isLoading:g,maxHeight:c,focusedOption:b}),A)})))}));return C||"fixed"===x?p.createElement(u,Object(r.a)({},d,{appendTo:C,controlElement:this.controlRef,menuPlacement:E,menuPosition:x}),L):L}},{key:"renderFormField",value:function(){var e=this,t=this.props,n=t.delimiter,r=t.isDisabled,o=t.isMulti,i=t.name,a=t.required,u=this.state.selectValue;if(a&&!this.hasValue()&&!r)return p.createElement(q,{name:i,onFocus:this.onValueInputFocus});if(i&&!r){if(o){if(n){var c=u.map((function(t){return e.getOptionValue(t)})).join(n);return p.createElement("input",{name:i,type:"hidden",value:c})}var l=u.length>0?u.map((function(t,n){return p.createElement("input",{key:"i-".concat(n),name:i,type:"hidden",value:e.getOptionValue(t)})})):p.createElement("input",{name:i,type:"hidden",value:""});return p.createElement("div",null,l)}var s=u[0]?this.getOptionValue(u[0]):"";return p.createElement("input",{name:i,type:"hidden",value:s})}}},{key:"renderLiveRegion",value:function(){var e=this.commonProps,t=this.state,n=t.ariaSelection,o=t.focusedOption,i=t.focusedValue,a=t.isFocused,u=t.selectValue,c=this.getFocusableOptions();return p.createElement(O,Object(r.a)({},e,{id:this.getElementId("live-region"),ariaSelection:n,focusedOption:o,focusedValue:i,isFocused:a,selectValue:u,focusableOptions:c,isAppleDevice:this.isAppleDevice}))}},{key:"render",value:function(){var e=this.getComponents(),t=e.Control,n=e.IndicatorsContainer,o=e.SelectContainer,i=e.ValueContainer,a=this.props,u=a.className,c=a.id,l=a.isDisabled,s=a.menuIsOpen,d=this.state.isFocused,f=this.commonProps=this.getCommonProps();return p.createElement(o,Object(r.a)({},f,{className:u,innerProps:{id:c,onKeyDown:this.onKeyDown},isDisabled:l,isFocused:d}),this.renderLiveRegion(),p.createElement(t,Object(r.a)({},f,{innerRef:this.getControlRef,innerProps:{onMouseDown:this.onControlMouseDown,onTouchEnd:this.onControlTouchEnd},isDisabled:l,isFocused:d,menuIsOpen:s}),p.createElement(i,Object(r.a)({},f,{isDisabled:l}),this.renderPlaceholderOrValue(),this.renderInput()),p.createElement(n,Object(r.a)({},f,{isDisabled:l}),this.renderClearIndicator(),this.renderLoadingIndicator(),this.renderIndicatorSeparator(),this.renderDropdownIndicator())),this.renderMenu(),this.renderFormField())}}],[{key:"getDerivedStateFromProps",value:function(e,t){var n=t.prevProps,r=t.clearFocusValueOnUpdate,i=t.inputIsHiddenAfterUpdate,a=t.ariaSelection,u=t.isFocused,c=t.prevWasFocused,l=t.instancePrefix,s=e.options,d=e.value,p=e.menuIsOpen,b=e.inputValue,m=e.isMulti,h=Object(f.h)(d),v={};if(n&&(d!==n.value||s!==n.options||p!==n.menuIsOpen||b!==n.inputValue)){var g=p?function(e,t){return oe(re(e,t))}(e,h):[],y=p?ie(re(e,h),"".concat(l,"-option")):[],O=r?function(e,t){var n=e.focusedValue,r=e.selectValue.indexOf(n);if(r>-1){if(t.indexOf(n)>-1)return n;if(r<t.length)return t[r]}return null}(t,h):null,w=function(e,t){var n=e.focusedOption;return n&&t.indexOf(n)>-1?n:t[0]}(t,g);v={selectValue:h,focusedOption:w,focusedOptionId:ue(y,w),focusableOptionsWithIds:y,focusedValue:O,clearFocusValueOnUpdate:!1}}var j=null!=i&&e!==n?{inputIsHidden:i,inputIsHiddenAfterUpdate:void 0}:{},E=a,x=u&&c;return u&&!x&&(E={value:Object(f.d)(m,h,h[0]||null),options:h,action:"initial-input-focus"},x=!c),"initial-input-focus"===(null==a?void 0:a.action)&&(E=null),Object(o.a)(Object(o.a)(Object(o.a)({},v),j),{},{prevProps:e,ariaSelection:E,prevWasFocused:x})}}]),m}(p.Component);me.defaultProps=te},1484:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(166);function o(e,t){if(null==e)return{};var n,o,i=Object(r.a)(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)n=a[o],t.includes(n)||{}.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}},1485:function(e,t,n){"use strict";n.d(t,"a",(function(){return P})),n.d(t,"b",(function(){return L})),n.d(t,"c",(function(){return V})),n.d(t,"d",(function(){return D})),n.d(t,"e",(function(){return v})),n.d(t,"f",(function(){return Ve})),n.d(t,"g",(function(){return w})),n.d(t,"h",(function(){return g})),n.d(t,"i",(function(){return S})),n.d(t,"j",(function(){return m})),n.d(t,"k",(function(){return R})),n.d(t,"l",(function(){return $})),n.d(t,"m",(function(){return de})),n.d(t,"n",(function(){return J})),n.d(t,"o",(function(){return De})),n.d(t,"p",(function(){return he})),n.d(t,"q",(function(){return se})),n.d(t,"r",(function(){return Oe})),n.d(t,"s",(function(){return ye})),n.d(t,"t",(function(){return pe})),n.d(t,"u",(function(){return ee})),n.d(t,"v",(function(){return Ee})),n.d(t,"w",(function(){return K})),n.d(t,"x",(function(){return be})),n.d(t,"y",(function(){return W})),n.d(t,"z",(function(){return Y})),n.d(t,"A",(function(){return Q})),n.d(t,"B",(function(){return Fe})),n.d(t,"C",(function(){return Pe})),n.d(t,"D",(function(){return T})),n.d(t,"E",(function(){return M})),n.d(t,"F",(function(){return Ie})),n.d(t,"G",(function(){return X})),n.d(t,"H",(function(){return Ae})),n.d(t,"I",(function(){return Me})),n.d(t,"J",(function(){return Re})),n.d(t,"K",(function(){return Z})),n.d(t,"L",(function(){return F}));var r=n(341),o=n(141),i=n(1482),a=n(1516),u=n(1484),c=n(131);var l=n(289),s=n(0),d=n(116),p=n(1503),f=n(1499),b=["className","clearValue","cx","getStyles","getClassNames","getValue","hasValue","isMulti","isRtl","options","selectOption","selectProps","setValue","theme"],m=function(){};function h(e,t){return t?"-"===t[0]?e+t:e+"__"+t:e}function v(e,t){for(var n=arguments.length,r=new Array(n>2?n-2:0),o=2;o<n;o++)r[o-2]=arguments[o];var i=[].concat(r);if(t&&e)for(var a in t)t.hasOwnProperty(a)&&t[a]&&i.push("".concat(h(e,a)));return i.filter((function(e){return e})).map((function(e){return String(e).trim()})).join(" ")}var g=function(e){return t=e,Array.isArray(t)?e.filter(Boolean):"object"===Object(c.a)(e)&&null!==e?[e]:[];var t},y=function(e){e.className,e.clearValue,e.cx,e.getStyles,e.getClassNames,e.getValue,e.hasValue,e.isMulti,e.isRtl,e.options,e.selectOption,e.selectProps,e.setValue,e.theme;var t=Object(u.a)(e,b);return Object(r.a)({},t)},O=function(e,t,n){var r=e.cx,o=e.getStyles,i=e.getClassNames,a=e.className;return{css:o(t,e),className:r(null!=n?n:{},i(t,e),a)}};function w(e){return[document.documentElement,document.body,window].indexOf(e)>-1}function j(e){return w(e)?window.pageYOffset:e.scrollTop}function E(e,t){w(e)?window.scrollTo(0,t):e.scrollTop=t}function x(e,t,n,r){return n*((e=e/r-1)*e*e+1)+t}function C(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:200,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:m,o=j(e),i=t-o,a=10,u=0;function c(){var t=x(u+=a,o,i,n);E(e,t),u<n?window.requestAnimationFrame(c):r(e)}c()}function S(e,t){var n=e.getBoundingClientRect(),r=t.getBoundingClientRect(),o=t.offsetHeight/3;r.bottom+o>n.bottom?E(e,Math.min(t.offsetTop+t.clientHeight-e.offsetHeight+o,e.scrollHeight)):r.top-o<n.top&&E(e,Math.max(t.offsetTop-o,0))}function F(){try{return document.createEvent("TouchEvent"),!0}catch(e){return!1}}function P(){try{return/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)}catch(e){return!1}}var I=!1,k={get passive(){return I=!0}},A="undefined"!=typeof window?window:{};A.addEventListener&&A.removeEventListener&&(A.addEventListener("p",m,k),A.removeEventListener("p",m,!1));var M=I;function R(e){return null!=e}function D(e,t,n){return e?t:n}function V(e){return e}function L(e){return e}var T=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];var o=Object.entries(e).filter((function(e){var t=Object(a.a)(e,1)[0];return!n.includes(t)}));return o.reduce((function(e,t){var n=Object(a.a)(t,2),r=n[0],o=n[1];return e[r]=o,e}),{})},H=["children","innerProps"],N=["children","innerProps"];function _(e){var t=e.maxHeight,n=e.menuEl,r=e.minHeight,o=e.placement,i=e.shouldScroll,a=e.isFixedPosition,u=e.controlHeight,c=function(e){var t=getComputedStyle(e),n="absolute"===t.position,r=/(auto|scroll)/;if("fixed"===t.position)return document.documentElement;for(var o=e;o=o.parentElement;)if(t=getComputedStyle(o),(!n||"static"!==t.position)&&r.test(t.overflow+t.overflowY+t.overflowX))return o;return document.documentElement}(n),l={placement:"bottom",maxHeight:t};if(!n||!n.offsetParent)return l;var s,d=c.getBoundingClientRect().height,p=n.getBoundingClientRect(),f=p.bottom,b=p.height,m=p.top,h=n.offsetParent.getBoundingClientRect().top,v=a?window.innerHeight:w(s=c)?window.innerHeight:s.clientHeight,g=j(c),y=parseInt(getComputedStyle(n).marginBottom,10),O=parseInt(getComputedStyle(n).marginTop,10),x=h-O,S=v-m,F=x+g,P=d-g-m,I=f-v+g+y,k=g+m-O;switch(o){case"auto":case"bottom":if(S>=b)return{placement:"bottom",maxHeight:t};if(P>=b&&!a)return i&&C(c,I,160),{placement:"bottom",maxHeight:t};if(!a&&P>=r||a&&S>=r)return i&&C(c,I,160),{placement:"bottom",maxHeight:a?S-y:P-y};if("auto"===o||a){var A=t,M=a?x:F;return M>=r&&(A=Math.min(M-y-u,t)),{placement:"top",maxHeight:A}}if("bottom"===o)return i&&E(c,I),{placement:"bottom",maxHeight:t};break;case"top":if(x>=b)return{placement:"top",maxHeight:t};if(F>=b&&!a)return i&&C(c,k,160),{placement:"top",maxHeight:t};if(!a&&F>=r||a&&x>=r){var R=t;return(!a&&F>=r||a&&x>=r)&&(R=a?x-O:F-O),i&&C(c,k,160),{placement:"top",maxHeight:R}}return{placement:"bottom",maxHeight:t};default:throw new Error('Invalid placement provided "'.concat(o,'".'))}return l}var B,U=function(e){return"auto"===e?"bottom":e},W=function(e,t){var n,o=e.placement,i=e.theme,a=i.borderRadius,u=i.spacing,c=i.colors;return Object(r.a)((n={label:"menu"},Object(l.a)(n,function(e){return e?{bottom:"top",top:"bottom"}[e]:"bottom"}(o),"100%"),Object(l.a)(n,"position","absolute"),Object(l.a)(n,"width","100%"),Object(l.a)(n,"zIndex",1),n),t?{}:{backgroundColor:c.neutral0,borderRadius:a,boxShadow:"0 0 0 1px hsla(0, 0%, 0%, 0.1), 0 4px 11px hsla(0, 0%, 0%, 0.1)",marginBottom:u.menuGutter,marginTop:u.menuGutter})},z=Object(s.createContext)(null),$=function(e){var t=e.children,n=e.minMenuHeight,o=e.maxMenuHeight,i=e.menuPlacement,u=e.menuPosition,c=e.menuShouldScrollIntoView,l=e.theme,d=(Object(s.useContext)(z)||{}).setPortalPlacement,p=Object(s.useRef)(null),b=Object(s.useState)(o),m=Object(a.a)(b,2),h=m[0],v=m[1],g=Object(s.useState)(null),y=Object(a.a)(g,2),O=y[0],w=y[1],j=l.spacing.controlHeight;return Object(f.a)((function(){var e=p.current;if(e){var t="fixed"===u,r=_({maxHeight:o,menuEl:e,minHeight:n,placement:i,shouldScroll:c&&!t,isFixedPosition:t,controlHeight:j});v(r.maxHeight),w(r.placement),null==d||d(r.placement)}}),[o,i,u,c,n,d,j]),t({ref:p,placerProps:Object(r.a)(Object(r.a)({},e),{},{placement:O||U(i),maxHeight:h})})},q=function(e){var t=e.children,n=e.innerRef,r=e.innerProps;return Object(i.b)("div",Object(o.a)({},O(e,"menu",{menu:!0}),{ref:n},r),t)},Y=function(e,t){var n=e.maxHeight,o=e.theme.spacing.baseUnit;return Object(r.a)({maxHeight:n,overflowY:"auto",position:"relative",WebkitOverflowScrolling:"touch"},t?{}:{paddingBottom:o,paddingTop:o})},G=function(e,t){var n=e.theme,o=n.spacing.baseUnit,i=n.colors;return Object(r.a)({textAlign:"center"},t?{}:{color:i.neutral40,padding:"".concat(2*o,"px ").concat(3*o,"px")})},X=G,K=G,Q=function(e){var t=e.rect,n=e.offset,r=e.position;return{left:t.left,position:r,top:n,width:t.width,zIndex:1}},J=function(e){var t=e.isDisabled;return{label:"container",direction:e.isRtl?"rtl":void 0,pointerEvents:t?"none":void 0,position:"relative"}},Z=function(e,t){var n=e.theme.spacing,o=e.isMulti,i=e.hasValue,a=e.selectProps.controlShouldRenderValue;return Object(r.a)({alignItems:"center",display:o&&i&&a?"flex":"grid",flex:1,flexWrap:"wrap",WebkitOverflowScrolling:"touch",position:"relative",overflow:"hidden"},t?{}:{padding:"".concat(n.baseUnit/2,"px ").concat(2*n.baseUnit,"px")})},ee=function(){return{alignItems:"center",alignSelf:"stretch",display:"flex",flexShrink:0}},te=["size"],ne=["innerProps","isRtl","size"];var re,oe,ie={name:"8mmkcg",styles:"display:inline-block;fill:currentColor;line-height:1;stroke:currentColor;stroke-width:0"},ae=function(e){var t=e.size,n=Object(u.a)(e,te);return Object(i.b)("svg",Object(o.a)({height:t,width:t,viewBox:"0 0 20 20","aria-hidden":"true",focusable:"false",css:ie},n))},ue=function(e){return Object(i.b)(ae,Object(o.a)({size:20},e),Object(i.b)("path",{d:"M14.348 14.849c-0.469 0.469-1.229 0.469-1.697 0l-2.651-3.030-2.651 3.029c-0.469 0.469-1.229 0.469-1.697 0-0.469-0.469-0.469-1.229 0-1.697l2.758-3.15-2.759-3.152c-0.469-0.469-0.469-1.228 0-1.697s1.228-0.469 1.697 0l2.652 3.031 2.651-3.031c0.469-0.469 1.228-0.469 1.697 0s0.469 1.229 0 1.697l-2.758 3.152 2.758 3.15c0.469 0.469 0.469 1.229 0 1.698z"}))},ce=function(e){return Object(i.b)(ae,Object(o.a)({size:20},e),Object(i.b)("path",{d:"M4.516 7.548c0.436-0.446 1.043-0.481 1.576 0l3.908 3.747 3.908-3.747c0.533-0.481 1.141-0.446 1.574 0 0.436 0.445 0.408 1.197 0 1.615-0.406 0.418-4.695 4.502-4.695 4.502-0.217 0.223-0.502 0.335-0.787 0.335s-0.57-0.112-0.789-0.335c0 0-4.287-4.084-4.695-4.502s-0.436-1.17 0-1.615z"}))},le=function(e,t){var n=e.isFocused,o=e.theme,i=o.spacing.baseUnit,a=o.colors;return Object(r.a)({label:"indicatorContainer",display:"flex",transition:"color 150ms"},t?{}:{color:n?a.neutral60:a.neutral20,padding:2*i,":hover":{color:n?a.neutral80:a.neutral40}})},se=le,de=le,pe=function(e,t){var n=e.isDisabled,o=e.theme,i=o.spacing.baseUnit,a=o.colors;return Object(r.a)({label:"indicatorSeparator",alignSelf:"stretch",width:1},t?{}:{backgroundColor:n?a.neutral10:a.neutral20,marginBottom:2*i,marginTop:2*i})},fe=Object(i.c)(B||(re=["\n  0%, 80%, 100% { opacity: 0; }\n  40% { opacity: 1; }\n"],oe||(oe=re.slice(0)),B=Object.freeze(Object.defineProperties(re,{raw:{value:Object.freeze(oe)}})))),be=function(e,t){var n=e.isFocused,o=e.size,i=e.theme,a=i.colors,u=i.spacing.baseUnit;return Object(r.a)({label:"loadingIndicator",display:"flex",transition:"color 150ms",alignSelf:"center",fontSize:o,lineHeight:1,marginRight:o,textAlign:"center",verticalAlign:"middle"},t?{}:{color:n?a.neutral60:a.neutral20,padding:2*u})},me=function(e){var t=e.delay,n=e.offset;return Object(i.b)("span",{css:Object(i.a)({animation:"".concat(fe," 1s ease-in-out ").concat(t,"ms infinite;"),backgroundColor:"currentColor",borderRadius:"1em",display:"inline-block",marginLeft:n?"1em":void 0,height:"1em",verticalAlign:"top",width:"1em"},"","")})},he=function(e,t){var n=e.isDisabled,o=e.isFocused,i=e.theme,a=i.colors,u=i.borderRadius,c=i.spacing;return Object(r.a)({label:"control",alignItems:"center",cursor:"default",display:"flex",flexWrap:"wrap",justifyContent:"space-between",minHeight:c.controlHeight,outline:"0 !important",position:"relative",transition:"all 100ms"},t?{}:{backgroundColor:n?a.neutral5:a.neutral0,borderColor:n?a.neutral10:o?a.primary:a.neutral20,borderRadius:u,borderStyle:"solid",borderWidth:1,boxShadow:o?"0 0 0 1px ".concat(a.primary):void 0,"&:hover":{borderColor:o?a.primary:a.neutral30}})},ve=function(e){var t=e.children,n=e.isDisabled,r=e.isFocused,a=e.innerRef,u=e.innerProps,c=e.menuIsOpen;return Object(i.b)("div",Object(o.a)({ref:a},O(e,"control",{control:!0,"control--is-disabled":n,"control--is-focused":r,"control--menu-is-open":c}),u,{"aria-disabled":n||void 0}),t)},ge=["data"],ye=function(e,t){var n=e.theme.spacing;return t?{}:{paddingBottom:2*n.baseUnit,paddingTop:2*n.baseUnit}},Oe=function(e,t){var n=e.theme,o=n.colors,i=n.spacing;return Object(r.a)({label:"group",cursor:"default",display:"block"},t?{}:{color:o.neutral40,fontSize:"75%",fontWeight:500,marginBottom:"0.25em",paddingLeft:3*i.baseUnit,paddingRight:3*i.baseUnit,textTransform:"uppercase"})},we=function(e){var t=e.children,n=e.cx,r=e.getStyles,a=e.getClassNames,u=e.Heading,c=e.headingProps,l=e.innerProps,s=e.label,d=e.theme,p=e.selectProps;return Object(i.b)("div",Object(o.a)({},O(e,"group",{group:!0}),l),Object(i.b)(u,Object(o.a)({},c,{selectProps:p,theme:d,getStyles:r,getClassNames:a,cx:n}),s),Object(i.b)("div",null,t))},je=["innerRef","isDisabled","isHidden","inputClassName"],Ee=function(e,t){var n=e.isDisabled,o=e.value,i=e.theme,a=i.spacing,u=i.colors;return Object(r.a)(Object(r.a)({visibility:n?"hidden":"visible",transform:o?"translateZ(0)":""},Ce),t?{}:{margin:a.baseUnit/2,paddingBottom:a.baseUnit/2,paddingTop:a.baseUnit/2,color:u.neutral80})},xe={gridArea:"1 / 2",font:"inherit",minWidth:"2px",border:0,margin:0,outline:0,padding:0},Ce={flex:"1 1 auto",display:"inline-grid",gridArea:"1 / 1 / 2 / 3",gridTemplateColumns:"0 min-content","&:after":Object(r.a)({content:'attr(data-value) " "',visibility:"hidden",whiteSpace:"pre"},xe)},Se=function(e){return Object(r.a)({label:"input",color:"inherit",background:0,opacity:e?0:1,width:"100%"},xe)},Fe=function(e,t){var n=e.theme,o=n.spacing,i=n.borderRadius,a=n.colors;return Object(r.a)({label:"multiValue",display:"flex",minWidth:0},t?{}:{backgroundColor:a.neutral10,borderRadius:i/2,margin:o.baseUnit/2})},Pe=function(e,t){var n=e.theme,o=n.borderRadius,i=n.colors,a=e.cropWithEllipsis;return Object(r.a)({overflow:"hidden",textOverflow:a||void 0===a?"ellipsis":void 0,whiteSpace:"nowrap"},t?{}:{borderRadius:o/2,color:i.neutral80,fontSize:"85%",padding:3,paddingLeft:6})},Ie=function(e,t){var n=e.theme,o=n.spacing,i=n.borderRadius,a=n.colors,u=e.isFocused;return Object(r.a)({alignItems:"center",display:"flex"},t?{}:{borderRadius:i/2,backgroundColor:u?a.dangerLight:void 0,paddingLeft:o.baseUnit,paddingRight:o.baseUnit,":hover":{backgroundColor:a.dangerLight,color:a.danger}})},ke=function(e){var t=e.children,n=e.innerProps;return Object(i.b)("div",n,t)};var Ae=function(e,t){var n=e.isDisabled,o=e.isFocused,i=e.isSelected,a=e.theme,u=a.spacing,c=a.colors;return Object(r.a)({label:"option",cursor:"default",display:"block",fontSize:"inherit",width:"100%",userSelect:"none",WebkitTapHighlightColor:"rgba(0, 0, 0, 0)"},t?{}:{backgroundColor:i?c.primary:o?c.primary25:"transparent",color:n?c.neutral20:i?c.neutral0:"inherit",padding:"".concat(2*u.baseUnit,"px ").concat(3*u.baseUnit,"px"),":active":{backgroundColor:n?void 0:i?c.primary:c.primary50}})},Me=function(e,t){var n=e.theme,o=n.spacing,i=n.colors;return Object(r.a)({label:"placeholder",gridArea:"1 / 1 / 2 / 3"},t?{}:{color:i.neutral50,marginLeft:o.baseUnit/2,marginRight:o.baseUnit/2})},Re=function(e,t){var n=e.isDisabled,o=e.theme,i=o.spacing,a=o.colors;return Object(r.a)({label:"singleValue",gridArea:"1 / 1 / 2 / 3",maxWidth:"100%",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},t?{}:{color:n?a.neutral40:a.neutral80,marginLeft:i.baseUnit/2,marginRight:i.baseUnit/2})},De={ClearIndicator:function(e){var t=e.children,n=e.innerProps;return Object(i.b)("div",Object(o.a)({},O(e,"clearIndicator",{indicator:!0,"clear-indicator":!0}),n),t||Object(i.b)(ue,null))},Control:ve,DropdownIndicator:function(e){var t=e.children,n=e.innerProps;return Object(i.b)("div",Object(o.a)({},O(e,"dropdownIndicator",{indicator:!0,"dropdown-indicator":!0}),n),t||Object(i.b)(ce,null))},DownChevron:ce,CrossIcon:ue,Group:we,GroupHeading:function(e){var t=y(e);t.data;var n=Object(u.a)(t,ge);return Object(i.b)("div",Object(o.a)({},O(e,"groupHeading",{"group-heading":!0}),n))},IndicatorsContainer:function(e){var t=e.children,n=e.innerProps;return Object(i.b)("div",Object(o.a)({},O(e,"indicatorsContainer",{indicators:!0}),n),t)},IndicatorSeparator:function(e){var t=e.innerProps;return Object(i.b)("span",Object(o.a)({},t,O(e,"indicatorSeparator",{"indicator-separator":!0})))},Input:function(e){var t=e.cx,n=e.value,r=y(e),a=r.innerRef,c=r.isDisabled,l=r.isHidden,s=r.inputClassName,d=Object(u.a)(r,je);return Object(i.b)("div",Object(o.a)({},O(e,"input",{"input-container":!0}),{"data-value":n||""}),Object(i.b)("input",Object(o.a)({className:t({input:!0},s),ref:a,style:Se(l),disabled:c},d)))},LoadingIndicator:function(e){var t=e.innerProps,n=e.isRtl,a=e.size,c=void 0===a?4:a,l=Object(u.a)(e,ne);return Object(i.b)("div",Object(o.a)({},O(Object(r.a)(Object(r.a)({},l),{},{innerProps:t,isRtl:n,size:c}),"loadingIndicator",{indicator:!0,"loading-indicator":!0}),t),Object(i.b)(me,{delay:0,offset:n}),Object(i.b)(me,{delay:160,offset:!0}),Object(i.b)(me,{delay:320,offset:!n}))},Menu:q,MenuList:function(e){var t=e.children,n=e.innerProps,r=e.innerRef,a=e.isMulti;return Object(i.b)("div",Object(o.a)({},O(e,"menuList",{"menu-list":!0,"menu-list--is-multi":a}),{ref:r},n),t)},MenuPortal:function(e){var t=e.appendTo,n=e.children,u=e.controlElement,c=e.innerProps,l=e.menuPlacement,b=e.menuPosition,m=Object(s.useRef)(null),h=Object(s.useRef)(null),v=Object(s.useState)(U(l)),g=Object(a.a)(v,2),y=g[0],w=g[1],j=Object(s.useMemo)((function(){return{setPortalPlacement:w}}),[]),E=Object(s.useState)(null),x=Object(a.a)(E,2),C=x[0],S=x[1],F=Object(s.useCallback)((function(){if(u){var e=function(e){var t=e.getBoundingClientRect();return{bottom:t.bottom,height:t.height,left:t.left,right:t.right,top:t.top,width:t.width}}(u),t="fixed"===b?0:window.pageYOffset,n=e[y]+t;n===(null==C?void 0:C.offset)&&e.left===(null==C?void 0:C.rect.left)&&e.width===(null==C?void 0:C.rect.width)||S({offset:n,rect:e})}}),[u,b,y,null==C?void 0:C.offset,null==C?void 0:C.rect.left,null==C?void 0:C.rect.width]);Object(f.a)((function(){F()}),[F]);var P=Object(s.useCallback)((function(){"function"==typeof h.current&&(h.current(),h.current=null),u&&m.current&&(h.current=Object(p.a)(u,m.current,F,{elementResize:"ResizeObserver"in window}))}),[u,F]);Object(f.a)((function(){P()}),[P]);var I=Object(s.useCallback)((function(e){m.current=e,P()}),[P]);if(!t&&"fixed"!==b||!C)return null;var k=Object(i.b)("div",Object(o.a)({ref:I},O(Object(r.a)(Object(r.a)({},e),{},{offset:C.offset,position:b,rect:C.rect}),"menuPortal",{"menu-portal":!0}),c),n);return Object(i.b)(z.Provider,{value:j},t?Object(d.createPortal)(k,t):k)},LoadingMessage:function(e){var t=e.children,n=void 0===t?"Loading...":t,a=e.innerProps,c=Object(u.a)(e,N);return Object(i.b)("div",Object(o.a)({},O(Object(r.a)(Object(r.a)({},c),{},{children:n,innerProps:a}),"loadingMessage",{"menu-notice":!0,"menu-notice--loading":!0}),a),n)},NoOptionsMessage:function(e){var t=e.children,n=void 0===t?"No options":t,a=e.innerProps,c=Object(u.a)(e,H);return Object(i.b)("div",Object(o.a)({},O(Object(r.a)(Object(r.a)({},c),{},{children:n,innerProps:a}),"noOptionsMessage",{"menu-notice":!0,"menu-notice--no-options":!0}),a),n)},MultiValue:function(e){var t=e.children,n=e.components,o=e.data,a=e.innerProps,u=e.isDisabled,c=e.removeProps,l=e.selectProps,s=n.Container,d=n.Label,p=n.Remove;return Object(i.b)(s,{data:o,innerProps:Object(r.a)(Object(r.a)({},O(e,"multiValue",{"multi-value":!0,"multi-value--is-disabled":u})),a),selectProps:l},Object(i.b)(d,{data:o,innerProps:Object(r.a)({},O(e,"multiValueLabel",{"multi-value__label":!0})),selectProps:l},t),Object(i.b)(p,{data:o,innerProps:Object(r.a)(Object(r.a)({},O(e,"multiValueRemove",{"multi-value__remove":!0})),{},{"aria-label":"Remove ".concat(t||"option")},c),selectProps:l}))},MultiValueContainer:ke,MultiValueLabel:ke,MultiValueRemove:function(e){var t=e.children,n=e.innerProps;return Object(i.b)("div",Object(o.a)({role:"button"},n),t||Object(i.b)(ue,{size:14}))},Option:function(e){var t=e.children,n=e.isDisabled,r=e.isFocused,a=e.isSelected,u=e.innerRef,c=e.innerProps;return Object(i.b)("div",Object(o.a)({},O(e,"option",{option:!0,"option--is-disabled":n,"option--is-focused":r,"option--is-selected":a}),{ref:u,"aria-disabled":n},c),t)},Placeholder:function(e){var t=e.children,n=e.innerProps;return Object(i.b)("div",Object(o.a)({},O(e,"placeholder",{placeholder:!0}),n),t)},SelectContainer:function(e){var t=e.children,n=e.innerProps,r=e.isDisabled,a=e.isRtl;return Object(i.b)("div",Object(o.a)({},O(e,"container",{"--is-disabled":r,"--is-rtl":a}),n),t)},SingleValue:function(e){var t=e.children,n=e.isDisabled,r=e.innerProps;return Object(i.b)("div",Object(o.a)({},O(e,"singleValue",{"single-value":!0,"single-value--is-disabled":n}),r),t)},ValueContainer:function(e){var t=e.children,n=e.innerProps,r=e.isMulti,a=e.hasValue;return Object(i.b)("div",Object(o.a)({},O(e,"valueContainer",{"value-container":!0,"value-container--is-multi":r,"value-container--has-value":a}),n),t)}},Ve=function(e){return Object(r.a)(Object(r.a)({},De),e.components)}},1490:function(e,t,n){"use strict";n.d(t,"a",(function(){return le}));var r=function(){function e(e){var t=this;this._insertTag=function(e){var n;n=0===t.tags.length?t.insertionPoint?t.insertionPoint.nextSibling:t.prepend?t.container.firstChild:t.before:t.tags[t.tags.length-1].nextSibling,t.container.insertBefore(e,n),t.tags.push(e)},this.isSpeedy=void 0===e.speedy||e.speedy,this.tags=[],this.ctr=0,this.nonce=e.nonce,this.key=e.key,this.container=e.container,this.prepend=e.prepend,this.insertionPoint=e.insertionPoint,this.before=null}var t=e.prototype;return t.hydrate=function(e){e.forEach(this._insertTag)},t.insert=function(e){this.ctr%(this.isSpeedy?65e3:1)==0&&this._insertTag(function(e){var t=document.createElement("style");return t.setAttribute("data-emotion",e.key),void 0!==e.nonce&&t.setAttribute("nonce",e.nonce),t.appendChild(document.createTextNode("")),t.setAttribute("data-s",""),t}(this));var t=this.tags[this.tags.length-1];if(this.isSpeedy){var n=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]}(t);try{n.insertRule(e,n.cssRules.length)}catch(e){}}else t.appendChild(document.createTextNode(e));this.ctr++},t.flush=function(){this.tags.forEach((function(e){var t;return null==(t=e.parentNode)?void 0:t.removeChild(e)})),this.tags=[],this.ctr=0},e}(),o="-ms-",i="-moz-",a="-webkit-",u="comm",c="rule",l="decl",s="@keyframes",d=Math.abs,p=String.fromCharCode,f=Object.assign;function b(e,t){return 45^y(e,0)?(((t<<2^y(e,0))<<2^y(e,1))<<2^y(e,2))<<2^y(e,3):0}function m(e){return e.trim()}function h(e,t){return(e=t.exec(e))?e[0]:e}function v(e,t,n){return e.replace(t,n)}function g(e,t){return e.indexOf(t)}function y(e,t){return 0|e.charCodeAt(t)}function O(e,t,n){return e.slice(t,n)}function w(e){return e.length}function j(e){return e.length}function E(e,t){return t.push(e),e}function x(e,t){return e.map(t).join("")}var C=1,S=1,F=0,P=0,I=0,k="";function A(e,t,n,r,o,i,a){return{value:e,root:t,parent:n,type:r,props:o,children:i,line:C,column:S,length:a,return:""}}function M(e,t){return f(A("",null,null,"",null,null,0),e,{length:-e.length},t)}function R(){return I=P<F?y(k,P++):0,S++,10===I&&(S=1,C++),I}function D(){return y(k,P)}function V(){return P}function L(e,t){return O(k,e,t)}function T(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function H(e){return C=S=1,F=w(k=e),P=0,[]}function N(e){return k="",e}function _(e){return m(L(P-1,function e(t){for(;R();)switch(I){case t:return P;case 34:case 39:34!==t&&39!==t&&e(I);break;case 40:41===t&&e(t);break;case 92:R()}return P}(91===e?e+2:40===e?e+1:e)))}function B(e){for(;(I=D())&&I<33;)R();return T(e)>2||T(I)>3?"":" "}function U(e,t){for(;--t&&R()&&!(I<48||I>102||I>57&&I<65||I>70&&I<97););return L(e,V()+(t<6&&32==D()&&32==R()))}function W(e,t){for(;R()&&e+I!==57&&(e+I!==84||47!==D()););return"/*"+L(t,P-1)+"*"+p(47===e?e:R())}function z(e){for(;!T(D());)R();return L(e,P)}function $(e){return N(function e(t,n,r,o,i,a,u,c,l){var s=0,d=0,f=u,b=0,m=0,h=0,O=1,j=1,x=1,F=0,A="",M=i,L=a,T=o,H=A;for(;j;)switch(h=F,F=R()){case 40:if(108!=h&&58==y(H,f-1)){-1!=g(H+=v(_(F),"&","&\f"),"&\f")&&(x=-1);break}case 34:case 39:case 91:H+=_(F);break;case 9:case 10:case 13:case 32:H+=B(h);break;case 92:H+=U(V()-1,7);continue;case 47:switch(D()){case 42:case 47:E(Y(W(R(),V()),n,r),l);break;default:H+="/"}break;case 123*O:c[s++]=w(H)*x;case 125*O:case 59:case 0:switch(F){case 0:case 125:j=0;case 59+d:-1==x&&(H=v(H,/\f/g,"")),m>0&&w(H)-f&&E(m>32?G(H+";",o,r,f-1):G(v(H," ","")+";",o,r,f-2),l);break;case 59:H+=";";default:if(E(T=q(H,n,r,s,d,i,c,A,M=[],L=[],f),a),123===F)if(0===d)e(H,n,T,T,M,a,f,c,L);else switch(99===b&&110===y(H,3)?100:b){case 100:case 108:case 109:case 115:e(t,T,T,o&&E(q(t,T,T,0,0,i,c,A,i,M=[],f),L),i,L,f,c,o?M:L);break;default:e(H,T,T,T,[""],L,0,c,L)}}s=d=m=0,O=x=1,A=H="",f=u;break;case 58:f=1+w(H),m=h;default:if(O<1)if(123==F)--O;else if(125==F&&0==O++&&125==(I=P>0?y(k,--P):0,S--,10===I&&(S=1,C--),I))continue;switch(H+=p(F),F*O){case 38:x=d>0?1:(H+="\f",-1);break;case 44:c[s++]=(w(H)-1)*x,x=1;break;case 64:45===D()&&(H+=_(R())),b=D(),d=f=w(A=H+=z(V())),F++;break;case 45:45===h&&2==w(H)&&(O=0)}}return a}("",null,null,null,[""],e=H(e),0,[0],e))}function q(e,t,n,r,o,i,a,u,l,s,p){for(var f=o-1,b=0===o?i:[""],h=j(b),g=0,y=0,w=0;g<r;++g)for(var E=0,x=O(e,f+1,f=d(y=a[g])),C=e;E<h;++E)(C=m(y>0?b[E]+" "+x:v(x,/&\f/g,b[E])))&&(l[w++]=C);return A(e,t,n,0===o?c:u,l,s,p)}function Y(e,t,n){return A(e,t,n,u,p(I),O(e,2,-2),0)}function G(e,t,n,r){return A(e,t,n,l,O(e,0,r),O(e,r+1,-1),r)}function X(e,t){for(var n="",r=j(e),o=0;o<r;o++)n+=t(e[o],o,e,t)||"";return n}function K(e,t,n,r){switch(e.type){case"@layer":if(e.children.length)break;case"@import":case l:return e.return=e.return||e.value;case u:return"";case s:return e.return=e.value+"{"+X(e.children,r)+"}";case c:e.value=e.props.join(",")}return w(n=X(e.children,r))?e.return=e.value+"{"+n+"}":""}function Q(e){var t=j(e);return function(n,r,o,i){for(var a="",u=0;u<t;u++)a+=e[u](n,r,o,i)||"";return a}}function J(e){return function(t){t.root||(t=t.return)&&e(t)}}var Z=n(1491),ee=n(1510),te="undefined"!=typeof document,ne=function(e,t,n){for(var r=0,o=0;r=o,o=D(),38===r&&12===o&&(t[n]=1),!T(o);)R();return L(e,P)},re=function(e,t){return N(function(e,t){var n=-1,r=44;do{switch(T(r)){case 0:38===r&&12===D()&&(t[n]=1),e[n]+=ne(P-1,t,n);break;case 2:e[n]+=_(r);break;case 4:if(44===r){e[++n]=58===D()?"&\f":"",t[n]=e[n].length;break}default:e[n]+=p(r)}}while(r=R());return e}(H(e),t))},oe=new WeakMap,ie=function(e){if("rule"===e.type&&e.parent&&!(e.length<1)){for(var t=e.value,n=e.parent,r=e.column===n.column&&e.line===n.line;"rule"!==n.type;)if(!(n=n.parent))return;if((1!==e.props.length||58===t.charCodeAt(0)||oe.get(n))&&!r){oe.set(e,!0);for(var o=[],i=re(t,o),a=n.props,u=0,c=0;u<i.length;u++)for(var l=0;l<a.length;l++,c++)e.props[c]=o[u]?i[u].replace(/&\f/g,a[l]):a[l]+" "+i[u]}}},ae=function(e){if("decl"===e.type){var t=e.value;108===t.charCodeAt(0)&&98===t.charCodeAt(2)&&(e.return="",e.value="")}};var ue=te?void 0:Object(Z.a)((function(){return Object(ee.a)((function(){var e={};return function(t){return e[t]}}))})),ce=[function(e,t,n,r){if(e.length>-1&&!e.return)switch(e.type){case l:e.return=function e(t,n){switch(b(t,n)){case 5103:return a+"print-"+t+t;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return a+t+t;case 5349:case 4246:case 4810:case 6968:case 2756:return a+t+i+t+o+t+t;case 6828:case 4268:return a+t+o+t+t;case 6165:return a+t+o+"flex-"+t+t;case 5187:return a+t+v(t,/(\w+).+(:[^]+)/,a+"box-$1$2"+o+"flex-$1$2")+t;case 5443:return a+t+o+"flex-item-"+v(t,/flex-|-self/,"")+t;case 4675:return a+t+o+"flex-line-pack"+v(t,/align-content|flex-|-self/,"")+t;case 5548:return a+t+o+v(t,"shrink","negative")+t;case 5292:return a+t+o+v(t,"basis","preferred-size")+t;case 6060:return a+"box-"+v(t,"-grow","")+a+t+o+v(t,"grow","positive")+t;case 4554:return a+v(t,/([^-])(transform)/g,"$1"+a+"$2")+t;case 6187:return v(v(v(t,/(zoom-|grab)/,a+"$1"),/(image-set)/,a+"$1"),t,"")+t;case 5495:case 3959:return v(t,/(image-set\([^]*)/,a+"$1$`$1");case 4968:return v(v(t,/(.+:)(flex-)?(.*)/,a+"box-pack:$3"+o+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+a+t+t;case 4095:case 3583:case 4068:case 2532:return v(t,/(.+)-inline(.+)/,a+"$1$2")+t;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(w(t)-1-n>6)switch(y(t,n+1)){case 109:if(45!==y(t,n+4))break;case 102:return v(t,/(.+:)(.+)-([^]+)/,"$1"+a+"$2-$3$1"+i+(108==y(t,n+3)?"$3":"$2-$3"))+t;case 115:return~g(t,"stretch")?e(v(t,"stretch","fill-available"),n)+t:t}break;case 4949:if(115!==y(t,n+1))break;case 6444:switch(y(t,w(t)-3-(~g(t,"!important")&&10))){case 107:return v(t,":",":"+a)+t;case 101:return v(t,/(.+:)([^;!]+)(;|!.+)?/,"$1"+a+(45===y(t,14)?"inline-":"")+"box$3$1"+a+"$2$3$1"+o+"$2box$3")+t}break;case 5936:switch(y(t,n+11)){case 114:return a+t+o+v(t,/[svh]\w+-[tblr]{2}/,"tb")+t;case 108:return a+t+o+v(t,/[svh]\w+-[tblr]{2}/,"tb-rl")+t;case 45:return a+t+o+v(t,/[svh]\w+-[tblr]{2}/,"lr")+t}return a+t+o+t+t}return t}(e.value,e.length);break;case s:return X([M(e,{value:v(e.value,"@","@"+a)})],r);case c:if(e.length)return x(e.props,(function(t){switch(h(t,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return X([M(e,{props:[v(t,/:(read-\w+)/,":-moz-$1")]})],r);case"::placeholder":return X([M(e,{props:[v(t,/:(plac\w+)/,":"+a+"input-$1")]}),M(e,{props:[v(t,/:(plac\w+)/,":-moz-$1")]}),M(e,{props:[v(t,/:(plac\w+)/,o+"input-$1")]})],r)}return""}))}}],le=function(e){var t=e.key;if(te&&"css"===t){var n=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(n,(function(e){-1!==e.getAttribute("data-emotion").indexOf(" ")&&(document.head.appendChild(e),e.setAttribute("data-s",""))}))}var o,i,a=e.stylisPlugins||ce,u={},c=[];te&&(o=e.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+t+' "]'),(function(e){for(var t=e.getAttribute("data-emotion").split(" "),n=1;n<t.length;n++)u[t[n]]=!0;c.push(e)})));var l=[ie,ae];if(te){var s,d=[K,J((function(e){s.insert(e)}))],p=Q(l.concat(a,d));i=function(e,t,n,r){s=n,X($(e?e+"{"+t.styles+"}":t.styles),p),r&&(v.inserted[t.name]=!0)}}else{var f=[K],b=Q(l.concat(a,f)),m=ue(a)(t),h=function(e,t){var n=t.name;return void 0===m[n]&&(m[n]=X($(e?e+"{"+t.styles+"}":t.styles),b)),m[n]};i=function(e,t,n,r){var o=t.name,i=h(e,t);return void 0===v.compat?(r&&(v.inserted[o]=!0),i):r?void(v.inserted[o]=i):i}}var v={key:t,sheet:new r({key:t,container:o,nonce:e.nonce,speedy:e.speedy,prepend:e.prepend,insertionPoint:e.insertionPoint}),nonce:e.nonce,inserted:u,registered:{},insert:i};return v.sheet.hydrate(c),v}},1491:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r=function(e){var t=new WeakMap;return function(n){if(t.has(n))return t.get(n);var r=e(n);return t.set(n,r),r}}},1499:function(e,t,n){"use strict";var r=n(0),o=r.useLayoutEffect;t.a=o},1500:function(e,t,n){"use strict";n.d(t,"a",(function(){return c}));var r=n(341),o=n(1516),i=n(1484),a=n(0),u=["defaultInputValue","defaultMenuIsOpen","defaultValue","inputValue","menuIsOpen","onChange","onInputChange","onMenuClose","onMenuOpen","value"];function c(e){var t=e.defaultInputValue,n=void 0===t?"":t,c=e.defaultMenuIsOpen,l=void 0!==c&&c,s=e.defaultValue,d=void 0===s?null:s,p=e.inputValue,f=e.menuIsOpen,b=e.onChange,m=e.onInputChange,h=e.onMenuClose,v=e.onMenuOpen,g=e.value,y=Object(i.a)(e,u),O=Object(a.useState)(void 0!==p?p:n),w=Object(o.a)(O,2),j=w[0],E=w[1],x=Object(a.useState)(void 0!==f?f:l),C=Object(o.a)(x,2),S=C[0],F=C[1],P=Object(a.useState)(void 0!==g?g:d),I=Object(o.a)(P,2),k=I[0],A=I[1],M=Object(a.useCallback)((function(e,t){"function"==typeof b&&b(e,t),A(e)}),[b]),R=Object(a.useCallback)((function(e,t){var n;"function"==typeof m&&(n=m(e,t)),E(void 0!==n?n:e)}),[m]),D=Object(a.useCallback)((function(){"function"==typeof v&&v(),F(!0)}),[v]),V=Object(a.useCallback)((function(){"function"==typeof h&&h(),F(!1)}),[h]),L=void 0!==p?p:j,T=void 0!==f?f:S,H=void 0!==g?g:k;return Object(r.a)(Object(r.a)({},y),{},{inputValue:L,menuIsOpen:T,onChange:M,onInputChange:R,onMenuClose:V,onMenuOpen:D,value:H})}},1501:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=Number.isNaN||function(e){return"number"==typeof e&&e!=e};function o(e,t){if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++)if(o=e[n],i=t[n],!(o===i||r(o)&&r(i)))return!1;var o,i;return!0}function i(e,t){void 0===t&&(t=o);var n=null;function r(){for(var r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];if(n&&n.lastThis===this&&t(r,n.lastArgs))return n.lastResult;var i=e.apply(this,r);return n={lastResult:i,lastArgs:r,lastThis:this},i}return r.clear=function(){n=null},r}},1503:function(e,t,n){"use strict";n.d(t,"a",(function(){return k}));const r=Math.min,o=Math.max,i=Math.round,a=Math.floor,u=e=>({x:e,y:e});function c(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function l(){return"undefined"!=typeof window}function s(e){return f(e)?(e.nodeName||"").toLowerCase():"#document"}function d(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function p(e){var t;return null==(t=(f(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function f(e){return!!l()&&(e instanceof Node||e instanceof d(e).Node)}function b(e){return!!l()&&(e instanceof Element||e instanceof d(e).Element)}function m(e){return!!l()&&(e instanceof HTMLElement||e instanceof d(e).HTMLElement)}function h(e){return!(!l()||"undefined"==typeof ShadowRoot)&&(e instanceof ShadowRoot||e instanceof d(e).ShadowRoot)}function v(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=O(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function g(){return!("undefined"==typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function y(e){return["html","body","#document"].includes(s(e))}function O(e){return d(e).getComputedStyle(e)}function w(e){if("html"===s(e))return e;const t=e.assignedSlot||e.parentNode||h(e)&&e.host||p(e);return h(t)?t.host:t}function j(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);const o=function e(t){const n=w(t);return y(n)?t.ownerDocument?t.ownerDocument.body:t.body:m(n)&&v(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),a=d(o);if(i){const e=E(a);return t.concat(a,a.visualViewport||[],v(o)?o:[],e&&n?j(e):[])}return t.concat(o,j(o,[],n))}function E(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function x(e){const t=O(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=m(e),a=o?e.offsetWidth:n,u=o?e.offsetHeight:r,c=i(n)!==a||i(r)!==u;return c&&(n=a,r=u),{width:n,height:r,$:c}}function C(e){return b(e)?e:e.contextElement}function S(e){const t=C(e);if(!m(t))return u(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:a}=x(t);let c=(a?i(n.width):n.width)/r,l=(a?i(n.height):n.height)/o;return c&&Number.isFinite(c)||(c=1),l&&Number.isFinite(l)||(l=1),{x:c,y:l}}const F=u(0);function P(e){const t=d(e);return g()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:F}function I(e,t,n,r){void 0===t&&(t=!1),void 0===n&&(n=!1);const o=e.getBoundingClientRect(),i=C(e);let a=u(1);t&&(r?b(r)&&(a=S(r)):a=S(e));const l=function(e,t,n){return void 0===t&&(t=!1),!(!n||t&&n!==d(e))&&t}(i,n,r)?P(i):u(0);let s=(o.left+l.x)/a.x,p=(o.top+l.y)/a.y,f=o.width/a.x,m=o.height/a.y;if(i){const e=d(i),t=r&&b(r)?d(r):r;let n=e,o=E(n);for(;o&&r&&t!==n;){const e=S(o),t=o.getBoundingClientRect(),r=O(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;s*=e.x,p*=e.y,f*=e.x,m*=e.y,s+=i,p+=a,n=d(o),o=E(n)}}return c({width:f,height:m,x:s,y:p})}function k(e,t,n,i){void 0===i&&(i={});const{ancestorScroll:u=!0,ancestorResize:c=!0,elementResize:l="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:d=!1}=i,f=C(e),b=u||c?[...f?j(f):[],...j(t)]:[];b.forEach(e=>{u&&e.addEventListener("scroll",n,{passive:!0}),c&&e.addEventListener("resize",n)});const m=f&&s?function(e,t){let n,i=null;const u=p(e);function c(){var e;clearTimeout(n),null==(e=i)||e.disconnect(),i=null}return function l(s,d){void 0===s&&(s=!1),void 0===d&&(d=1),c();const{left:p,top:f,width:b,height:m}=e.getBoundingClientRect();if(s||t(),!b||!m)return;const h={rootMargin:-a(f)+"px "+-a(u.clientWidth-(p+b))+"px "+-a(u.clientHeight-(f+m))+"px "+-a(p)+"px",threshold:o(0,r(1,d))||1};let v=!0;function g(e){const t=e[0].intersectionRatio;if(t!==d){if(!v)return l();t?l(!1,t):n=setTimeout(()=>{l(!1,1e-7)},1e3)}v=!1}try{i=new IntersectionObserver(g,{...h,root:u.ownerDocument})}catch(e){i=new IntersectionObserver(g,h)}i.observe(e)}(!0),c}(f,n):null;let h,v=-1,g=null;l&&(g=new ResizeObserver(e=>{let[r]=e;r&&r.target===f&&g&&(g.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),f&&!d&&g.observe(f),g.observe(t));let y=d?I(e):null;return d&&function t(){const r=I(e);!y||r.x===y.x&&r.y===y.y&&r.width===y.width&&r.height===y.height||n();y=r,h=requestAnimationFrame(t)}(),n(),()=>{var e;b.forEach(e=>{u&&e.removeEventListener("scroll",n),c&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=g)||e.disconnect(),g=null,d&&cancelAnimationFrame(h)}}},1506:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(1507);function o(e,t){if(e){if("string"==typeof e)return Object(r.a)(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Object(r.a)(e,t):void 0}}},1507:function(e,t,n){"use strict";function r(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}n.d(t,"a",(function(){return r}))},1508:function(e,t,n){"use strict";n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return i}));var r="undefined"!=typeof document;function o(e,t,n){var r="";return n.split(" ").forEach((function(n){void 0!==e[n]?t.push(e[n]+";"):n&&(r+=n+" ")})),r}var i=function(e,t,n){var o=e.key+"-"+t.name;(!1===n||!1===r&&void 0!==e.compat)&&void 0===e.registered[o]&&(e.registered[o]=t.styles)},a=function(e,t,n){i(e,t,n);var o=e.key+"-"+t.name;if(void 0===e.inserted[t.name]){var a="",u=t;do{var c=e.insert(t===u?"."+o:"",u,e.sheet,!0);r||void 0===c||(a+=c),u=u.next}while(void 0!==u);if(!r&&0!==a.length)return a}}},1509:function(e,t,n){"use strict";n.d(t,"a",(function(){return u})),n.d(t,"b",(function(){return c}));var r=n(0),o="undefined"!=typeof document,i=function(e){return e()},a=!!r.useInsertionEffect&&r.useInsertionEffect,u=o&&a||i,c=a||r.useLayoutEffect},1510:function(e,t,n){"use strict";function r(e){var t=Object.create(null);return function(n){return void 0===t[n]&&(t[n]=e(n)),t[n]}}n.d(t,"a",(function(){return r}))},1511:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(344),o=n.n(r),i=function(e,t){return o()(e,t)}},1516:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(1506);function o(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,u=[],c=!0,l=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(u.push(r.value),u.length!==t);c=!0);}catch(e){l=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(e,t)||Object(r.a)(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}},1517:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(1507);var o=n(1506);function i(e){return function(e){if(Array.isArray(e))return Object(r.a)(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||Object(o.a)(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}},1518:function(e,t,n){"use strict";n.d(t,"a",(function(){return b}));var r={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},o=n(1510),i=/[A-Z]|^ms/g,a=/_EMO_([^_]+?)_([^]*?)_EMO_/g,u=function(e){return 45===e.charCodeAt(1)},c=function(e){return null!=e&&"boolean"!=typeof e},l=Object(o.a)((function(e){return u(e)?e:e.replace(i,"-$&").toLowerCase()})),s=function(e,t){switch(e){case"animation":case"animationName":if("string"==typeof t)return t.replace(a,(function(e,t,n){return p={name:t,styles:n,next:p},t}))}return 1===r[e]||u(e)||"number"!=typeof t||0===t?t:t+"px"};function d(e,t,n){if(null==n)return"";var r=n;if(void 0!==r.__emotion_styles)return r;switch(typeof n){case"boolean":return"";case"object":var o=n;if(1===o.anim)return p={name:o.name,styles:o.styles,next:p},o.name;var i=n;if(void 0!==i.styles){var a=i.next;if(void 0!==a)for(;void 0!==a;)p={name:a.name,styles:a.styles,next:p},a=a.next;return i.styles+";"}return function(e,t,n){var r="";if(Array.isArray(n))for(var o=0;o<n.length;o++)r+=d(e,t,n[o])+";";else for(var i in n){var a=n[i];if("object"!=typeof a){var u=a;null!=t&&void 0!==t[u]?r+=i+"{"+t[u]+"}":c(u)&&(r+=l(i)+":"+s(i,u)+";")}else if(!Array.isArray(a)||"string"!=typeof a[0]||null!=t&&void 0!==t[a[0]]){var p=d(e,t,a);switch(i){case"animation":case"animationName":r+=l(i)+":"+p+";";break;default:r+=i+"{"+p+"}"}}else for(var f=0;f<a.length;f++)c(a[f])&&(r+=l(i)+":"+s(i,a[f])+";")}return r}(e,t,n);case"function":if(void 0!==e){var u=p,f=n(e);return p=u,d(e,t,f)}}var b=n;if(null==t)return b;var m=t[b];return void 0!==m?m:b}var p,f=/label:\s*([^\s;{]+)\s*(;|$)/g;function b(e,t,n){if(1===e.length&&"object"==typeof e[0]&&null!==e[0]&&void 0!==e[0].styles)return e[0];var r=!0,o="";p=void 0;var i=e[0];null==i||void 0===i.raw?(r=!1,o+=d(n,t,i)):o+=i[0];for(var a=1;a<e.length;a++){if(o+=d(n,t,e[a]),r)o+=i[a]}f.lastIndex=0;for(var u,c="";null!==(u=f.exec(o));)c+="-"+u[1];return{name:function(e){for(var t,n=0,r=0,o=e.length;o>=4;++r,o-=4)t=1540483477*(65535&(t=255&e.charCodeAt(r)|(255&e.charCodeAt(++r))<<8|(255&e.charCodeAt(++r))<<16|(255&e.charCodeAt(++r))<<24))+(59797*(t>>>16)<<16),n=1540483477*(65535&(t^=t>>>24))+(59797*(t>>>16)<<16)^1540483477*(65535&n)+(59797*(n>>>16)<<16);switch(o){case 3:n^=(255&e.charCodeAt(r+2))<<16;case 2:n^=(255&e.charCodeAt(r+1))<<8;case 1:n=1540483477*(65535&(n^=255&e.charCodeAt(r)))+(59797*(n>>>16)<<16)}return(((n=1540483477*(65535&(n^=n>>>13))+(59797*(n>>>16)<<16))^n>>>15)>>>0).toString(36)}(o)+c,styles:o,next:p}}},1526:function(e,t,n){var r=n(213);function o(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}e.exports=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?o(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):o(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e},e.exports.__esModule=!0,e.exports.default=e.exports},1527:function(e,t,n){var r=n(1470),o=n(1550),i=n(1471);e.exports=function(e){var t=o();return function(){var n,o=r(e);if(t){var a=r(this).constructor;n=Reflect.construct(o,arguments,a)}else n=o.apply(this,arguments);return i(this,n)}},e.exports.__esModule=!0,e.exports.default=e.exports},1528:function(e,t){e.exports=function(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))},e.exports.__esModule=!0,e.exports.default=e.exports},1538:function(e,t,n){"use strict";var r=n(0),o=n.n(r),i=n(1490),a=n(1474),u=n(21),c=function(e){var t=e.children,n=Object(r.useMemo)((function(){var e=Object(u.a)();return Object(i.a)({key:"wv-react-select-emotion",container:e})}),[]);return window.isApryseWebViewerWebComponent?o.a.createElement(a.a,{value:n},t):t};t.a=c},1550:function(e,t){function n(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(e.exports=n=function(){return!!t},e.exports.__esModule=!0,e.exports.default=e.exports)()}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports},1551:function(e,t,n){"use strict";n.d(t,"a",(function(){return u}));var r=n(1500),o=n(141),i=n(0),a=n(1483),u=(n(1474),n(1490),n(1526),n(285),n(433),n(431),n(432),n(1469),n(1527),n(1522),n(436),n(1528),n(213),n(116),n(1499),Object(i.forwardRef)((function(e,t){var n=Object(r.a)(e);return i.createElement(a.a,Object(o.a)({ref:t},n))})))},1584:function(e,t,n){"use strict";n.d(t,"a",(function(){return b}));var r=n(141),o=n(0),i=n(1483),a=n(1500),u=n(341),c=n(1517),l=n(1484),s=n(1485),d=["allowCreateWhileLoading","createOptionPosition","formatCreateLabel","isValidNewOption","getNewOptionData","onCreateOption","options","onChange"],p=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1?arguments[1]:void 0,n=arguments.length>2?arguments[2]:void 0,r=String(e).toLowerCase(),o=String(n.getOptionValue(t)).toLowerCase(),i=String(n.getOptionLabel(t)).toLowerCase();return o===r||i===r},f={formatCreateLabel:function(e){return'Create "'.concat(e,'"')},isValidNewOption:function(e,t,n,r){return!(!e||t.some((function(t){return p(e,t,r)}))||n.some((function(t){return p(e,t,r)})))},getNewOptionData:function(e,t){return{label:t,value:e,__isNew__:!0}}};n(1526),n(431),n(432),n(1469),n(1527),n(1522),n(285),n(433),n(436),n(1528),n(213),n(116),n(1499);var b=Object(o.forwardRef)((function(e,t){var n,p,b,m,h,v,g,y,O,w,j,E,x,C,S,F,P,I,k,A,M,R,D,V,L,T,H,N,_=Object(a.a)(e),B=(p=(n=_).allowCreateWhileLoading,b=void 0!==p&&p,m=n.createOptionPosition,h=void 0===m?"last":m,v=n.formatCreateLabel,g=void 0===v?f.formatCreateLabel:v,y=n.isValidNewOption,O=void 0===y?f.isValidNewOption:y,w=n.getNewOptionData,j=void 0===w?f.getNewOptionData:w,E=n.onCreateOption,x=n.options,C=void 0===x?[]:x,S=n.onChange,F=Object(l.a)(n,d),P=F.getOptionValue,I=void 0===P?i.c:P,k=F.getOptionLabel,A=void 0===k?i.b:k,M=F.inputValue,R=F.isLoading,D=F.isMulti,V=F.value,L=F.name,T=Object(o.useMemo)((function(){return O(M,Object(s.h)(V),C,{getOptionValue:I,getOptionLabel:A})?j(M,g(M)):void 0}),[g,j,A,I,M,O,C,V]),H=Object(o.useMemo)((function(){return!b&&R||!T?C:"first"===h?[T].concat(Object(c.a)(C)):[].concat(Object(c.a)(C),[T])}),[b,h,R,T,C]),N=Object(o.useCallback)((function(e,t){if("select-option"!==t.action)return S(e,t);var n=Array.isArray(e)?e:[e];if(n[n.length-1]!==T)S(e,t);else if(E)E(M);else{var r=j(M,M),o={action:"create-option",name:L,option:r};S(Object(s.d)(D,[].concat(Object(c.a)(Object(s.h)(V)),[r]),r),o)}}),[j,M,D,L,T,E,S,V]),Object(u.a)(Object(u.a)({},F),{},{options:H,onChange:N}));return o.createElement(i.a,Object(r.a)({ref:t},B))}))},1598:function(e,t,n){var r=n(32),o=n(1678);"string"==typeof(o=o.__esModule?o.default:o)&&(o=[[e.i,o,""]]);var i={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const r=[];return n.querySelectorAll(t).forEach(e=>r.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&r.push(...e(t,n.shadowRoot))}),r}("apryse-webviewer"));const n=[];for(let r=0;r<t.length;r++){const o=t[r];if(0===r)o.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);o.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};r(o,i);e.exports=o.locals||{}},1652:function(e,t,n){"use strict";var r=n(0),o=n.n(r),i=n(1485),a=n(42),u=function(e){var t=e.selectProps.menuIsOpen;return o.a.createElement(i.o.IndicatorsContainer,e,o.a.createElement(a.a,{className:"arrow",glyph:"icon-chevron-".concat(t?"up":"down")}))};t.a=u},1666:function(e,t,n){"use strict";var r=n(0),o=n.n(r),i=n(4),a=n.n(i),u=n(428),c=n(42),l=n(17),s=n.n(l),d=(n(1675),{label:a.a.string,value:a.a.string,onChange:a.a.func,validationMessage:a.a.string,hasError:a.a.bool,ariaDescribedBy:a.a.string,ariaLabelledBy:a.a.string}),p=function(e){var t=e.label,n=e.value,r=e.onChange,i=e.validationMessage,a=e.hasError,l=e.ariaDescribedBy,d=e.ariaLabelledBy,p=Object(u.a)().t;return o.a.createElement("div",{className:"input-container"},o.a.createElement("div",{className:"input-wrapper"},o.a.createElement("input",{className:s()({"text-input":!0,"text-input--error":a}),id:t,type:"text",onChange:function(e){return r(e.target.value)},"aria-label":t,value:n,"aria-describedby":a?{ariaDescribedBy:l}:void 0,"aria-labelledby":d}),a&&o.a.createElement(c.a,{glyph:"icon-alert"})),a&&o.a.createElement("div",{id:"TextInputError",className:"text-input-error"},o.a.createElement("p",{"aria-live":"assertive",className:"no-margin",role:"alert"},p(i))))};p.propTypes=d;var f=p;t.a=f},1673:function(e,t,n){var r=n(32),o=n(1674);"string"==typeof(o=o.__esModule?o.default:o)&&(o=[[e.i,o,""]]);var i={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const r=[];return n.querySelectorAll(t).forEach(e=>r.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&r.push(...e(t,n.shadowRoot))}),r}("apryse-webviewer"));const n=[];for(let r=0;r<t.length;r++){const o=t[r];if(0===r)o.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);o.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};r(o,i);e.exports=o.locals||{}},1674:function(e,t,n){(e.exports=n(33)(!1)).push([e.i,".messageText{width:100%;font-size:11px;margin-top:4px}",""])},1675:function(e,t,n){var r=n(32),o=n(1676);"string"==typeof(o=o.__esModule?o.default:o)&&(o=[[e.i,o,""]]);var i={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const r=[];return n.querySelectorAll(t).forEach(e=>r.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&r.push(...e(t,n.shadowRoot))}),r}("apryse-webviewer"));const n=[];for(let r=0;r<t.length;r++){const o=t[r];if(0===r)o.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);o.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};r(o,i);e.exports=o.locals||{}},1676:function(e,t,n){(e.exports=n(33)(!1)).push([e.i,".input-container .input-wrapper{width:100%;position:relative;display:flex;align-items:center}.input-container .input-wrapper .Icon{position:absolute;right:8px;color:var(--error-border-color)}.input-container .input-wrapper .text-input{padding:6px!important;width:100%;height:32px}.input-container .input-wrapper .text-input.text-input--error{padding-right:30px;border-color:var(--error-border-color)!important}.input-container .text-input-error{color:var(--error-text-color);width:100%;margin-top:4px}",""])},1677:function(e,t,n){var r=n(32),o=n(1818);"string"==typeof(o=o.__esModule?o.default:o)&&(o=[[e.i,o,""]]);var i={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const r=[];return n.querySelectorAll(t).forEach(e=>r.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&r.push(...e(t,n.shadowRoot))}),r}("apryse-webviewer"));const n=[];for(let r=0;r<t.length;r++){const o=t[r];if(0===r)o.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);o.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};r(o,i);e.exports=o.locals||{}},1678:function(e,t,n){(e.exports=n(33)(!1)).push([e.i,".creatable-list{display:flex;flex-direction:column}.creatable-list-item{display:flex;flex-direction:row;align-items:center}.add-item-button{display:flex;align-items:center;width:78px;cursor:pointer}.icon-handle{cursor:grab}",""])},1679:function(e,t,n){var r=n(32),o=n(1680);"string"==typeof(o=o.__esModule?o.default:o)&&(o=[[e.i,o,""]]);var i={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const r=[];return n.querySelectorAll(t).forEach(e=>r.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&r.push(...e(t,n.shadowRoot))}),r}("apryse-webviewer"));const n=[];for(let r=0;r<t.length;r++){const o=t[r];if(0===r)o.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);o.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};r(o,i);e.exports=o.locals||{}},1680:function(e,t,n){(e.exports=n(33)(!1)).push([e.i,'.FormFieldEditPopup .signature-options-container{padding:5px 0;display:grid;grid-template-columns:.5fr 1fr;grid-template-areas:"label dropdown";align-items:center}.FormFieldEditPopup .signature-options-container label{grid-area:label}.FormFieldEditPopup .arrow{width:12px;height:16px;margin-top:2px}',""])},1733:function(e,t,n){"use strict";n(38),n(35),n(28),n(8),n(16),n(124),n(19),n(11),n(13),n(14),n(10),n(9),n(12),n(15),n(20),n(18),n(26),n(27),n(25),n(22),n(29),n(45),n(23),n(24),n(48),n(46);var r=n(0),o=n.n(r),i=n(44),a=n(428),u=n(4),c=n.n(u),l=(n(83),n(94),n(105),n(1954)),s=n(1492),d=n(1512),p=n(74);n(57);function f(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function b(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var m=function(){function e(t,n){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),b(this,"spec",void 0),b(this,"monitor",void 0),this.spec=t,this.monitor=n}var t,n,r;return t=e,(n=[{key:"canDrop",value:function(){var e=this.spec,t=this.monitor;return!e.canDrop||e.canDrop(t.getItem(),t)}},{key:"hover",value:function(){var e=this.spec,t=this.monitor;e.hover&&e.hover(t.getItem(),t)}},{key:"drop",value:function(){var e=this.spec,t=this.monitor;if(e.drop)return e.drop(t.getItem(),t)}}])&&f(t.prototype,n),r&&f(t,r),e}();function h(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==n)return;var r,o,i=[],a=!0,u=!1;try{for(n=n.call(e);!(a=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);a=!0);}catch(e){u=!0,o=e}finally{try{a||null==n.return||n.return()}finally{if(u)throw o}}return i}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return v(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return v(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function v(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function g(e,t,n){var o=Object(s.a)(),i=function(e,t){var n=Object(r.useMemo)((function(){return new m(e,t)}),[t]);return Object(r.useEffect)((function(){n.spec=e}),[e]),n}(e,t),a=function(e){var t=e.accept;return Object(r.useMemo)((function(){return Object(p.a)(null!=e.accept,"accept must be defined"),Array.isArray(t)?t:[t]}),[t])}(e);Object(d.a)((function(){var e=h(Object(l.b)(a,i,o),2),r=e[0],u=e[1];return t.receiveHandlerId(r),n.receiveHandlerId(r),u}),[o,t,i,n,a.map((function(e){return e.toString()})).join("|")])}function y(e){return function(e){if(Array.isArray(e))return O(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return O(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return O(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function O(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function w(e,t){var n=y(t||[]);return null==t&&"function"!=typeof e&&n.push(e),Object(r.useMemo)((function(){return"function"==typeof e?e():e}),n)}var j=n(1952);var E=n(1953);var x=n(1600);function C(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==n)return;var r,o,i=[],a=!0,u=!1;try{for(n=n.call(e);!(a=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);a=!0);}catch(e){u=!0,o=e}finally{try{a||null==n.return||n.return()}finally{if(u)throw o}}return i}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return S(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return S(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function S(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function F(e,t,n){return function(e,t,n){var r=C(Object(x.a)(e,t,n),2),o=r[0],i=r[1];return Object(d.a)((function(){var t=e.getHandlerId();if(null!=t)return e.subscribeToStateChange(i,{handlerIds:[t]})}),[e,i]),o}(t,e||function(){return{}},(function(){return n.reconnect()}))}function P(e){return Object(r.useMemo)((function(){return e.hooks.dropTarget()}),[e])}function I(e,t){var n,o=w(e,t),i=(n=Object(s.a)(),Object(r.useMemo)((function(){return new j.a(n)}),[n])),a=function(e){var t=Object(s.a)(),n=Object(r.useMemo)((function(){return new E.a(t.getBackend())}),[t]);return Object(d.a)((function(){return n.dropTargetOptions=e||null,n.reconnect(),function(){return n.disconnectDropTarget()}}),[e]),n}(o.options);return g(o,i,a),[F(o.collect,i,a),P(a)]}function k(e){return(k="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function A(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function M(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var R=function(){function e(t,n,r){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),M(this,"spec",void 0),M(this,"monitor",void 0),M(this,"connector",void 0),this.spec=t,this.monitor=n,this.connector=r}var t,n,r;return t=e,(n=[{key:"beginDrag",value:function(){var e,t=this.spec,n=this.monitor;return null!==(e="object"===k(t.item)?t.item:"function"==typeof t.item?t.item(n):{})&&void 0!==e?e:null}},{key:"canDrag",value:function(){var e=this.spec,t=this.monitor;return"boolean"==typeof e.canDrag?e.canDrag:"function"!=typeof e.canDrag||e.canDrag(t)}},{key:"isDragging",value:function(e,t){var n=this.spec,r=this.monitor,o=n.isDragging;return o?o(r):t===e.getSourceId()}},{key:"endDrag",value:function(){var e=this.spec,t=this.monitor,n=this.connector,r=e.end;r&&r(t.getItem(),t),n.reconnect()}}])&&A(t.prototype,n),r&&A(t,r),e}();function D(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==n)return;var r,o,i=[],a=!0,u=!1;try{for(n=n.call(e);!(a=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);a=!0);}catch(e){u=!0,o=e}finally{try{a||null==n.return||n.return()}finally{if(u)throw o}}return i}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return V(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return V(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function V(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function L(e,t,n){var o=Object(s.a)(),i=function(e,t,n){var o=Object(r.useMemo)((function(){return new R(e,t,n)}),[t,n]);return Object(r.useEffect)((function(){o.spec=e}),[e]),o}(e,t,n),a=function(e){return Object(r.useMemo)((function(){var t=e.type;return Object(p.a)(null!=t,"spec.type must be defined"),t}),[e])}(e);Object(d.a)((function(){if(null!=a){var e=D(Object(l.a)(a,i,o),2),r=e[0],u=e[1];return t.receiveHandlerId(r),n.receiveHandlerId(r),u}}),[o,t,n,i,a])}var T=n(1955);var H=n(1956);function N(e){return Object(r.useMemo)((function(){return e.hooks.dragSource()}),[e])}function _(e){return Object(r.useMemo)((function(){return e.hooks.dragPreview()}),[e])}function B(e,t){var n=w(e,t);Object(p.a)(!n.begin,"useDrag::spec.begin was deprecated in v14. Replace spec.begin() with spec.item(). (see more here - https://react-dnd.github.io/react-dnd/docs/api/use-drag)");var o,i=(o=Object(s.a)(),Object(r.useMemo)((function(){return new T.a(o)}),[o])),a=function(e,t){var n=Object(s.a)(),o=Object(r.useMemo)((function(){return new H.a(n.getBackend())}),[n]);return Object(d.a)((function(){return o.dragSourceOptions=e||null,o.reconnect(),function(){return o.disconnectDragSource()}}),[o,e]),Object(d.a)((function(){return o.dragPreviewOptions=t||null,o.reconnect(),function(){return o.disconnectDragPreview()}}),[o,t]),o}(n.options,n.previewOptions);return L(n,i,a),[F(n.collect,i,a),N(a),_(a)]}var U=n(42);n(1598);function W(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,u=[],c=!0,l=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(u.push(r.value),u.length!==t);c=!0);}catch(e){l=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return z(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return z(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function z(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var $=function(e){var t=e.option,n=e.index,a=e.onChange,u=e.onDeleteItem,c=e.moveListItem,l=e.id,s=e.addItem,d="item",p=Object(r.useRef)(null),f=W(I({accept:d,hover:function(e,t){var r;if(p.current){var o=e.index,i=n;if(o!==i){var a=null===(r=p.current)||void 0===r?void 0:r.getBoundingClientRect(),u=(a.bottom-a.top)/2,l=t.getClientOffset().y-a.top;o<i&&l<u||o>i&&l>u||(c(o,i),e.index=i)}}}}),2)[1],b=W(B({type:d,item:{type:d,id:l,index:n},collect:function(e){return{isDragging:e.isDragging()}}}),2),m=b[0].isDragging,h=b[1],v=Object(r.useCallback)((function(e){a(e.target.value)}),[a]),g=Object(r.useCallback)((function(e){"Enter"===e.key&&s()}),[s]);h(f(p));var y=m?0:1;return o.a.createElement("div",{ref:p,style:{opacity:y},className:"creatable-list-item"},o.a.createElement("div",{className:"icon-handle"},o.a.createElement(U.a,{glyph:"icon-drag-handle"})),o.a.createElement("input",{type:"text",onChange:v,value:t.displayValue,onKeyPress:g,autoFocus:!0}),o.a.createElement(i.a,{title:"action.delete",img:"icon-delete-line",onClick:u}))};function q(e){return(q="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Y(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function G(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Y(Object(n),!0).forEach((function(t){X(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Y(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function X(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==q(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==q(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===q(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function K(e){return function(e){if(Array.isArray(e))return Z(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||J(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Q(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,u=[],c=!0,l=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(u.push(r.value),u.length!==t);c=!0);}catch(e){l=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(e,t)||J(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function J(e,t){if(e){if("string"==typeof e)return Z(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Z(e,t):void 0}}function Z(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var ee={options:c.a.object,onOptionsUpdated:c.a.func,popupRef:c.a.object},te=function(e){var t=e.options,n=e.onOptionsUpdated,u=e.popupRef,c=Object(r.useRef)(!1);Object(r.useEffect)((function(){c.current=!1}),[n]),Object(r.useEffect)((function(){f(s)}),[t]);var l=Object(a.a)().t,s=t.map((function(e,t){return{id:t,displayValue:e.displayValue,value:e.value}})),d=Q(Object(r.useState)(s),2),p=d[0],f=d[1],b=Q(Object(r.useState)(s.length),2),m=b[0],h=b[1],v=Object(r.useRef)();Object(r.useEffect)((function(){if(c.current){var e=p.map((function(e){return{value:e.value,displayValue:e.displayValue}}));n(e)}else c.current=!0}),[p,n]);var g=Object(r.useCallback)((function(){var e=m;h(m+1),f([].concat(K(p),[{id:e,value:"",displayValue:""}])),u&&w()}),[m,p]),y=function(e){return function(){var t=p.filter((function(t){return e!==t.id}));f(t)}};var O=Object(r.useCallback)((function(e,t){var n,r,o,i=p[e],a=p.filter((function(t,n){return n!==e})),u=(n=t,r=i,(o=a.slice(0)).splice(n,0,r),o);f(u)}),[p]),w=function(){var e=u.current,t=v.current,n=e.getBoundingClientRect().bottom,r=window.innerHeight-n,o=t.scrollHeight>t.clientHeight;if(r<=40&&!o){var i=40*t.childElementCount;t.style.maxHeight="".concat(i,"px")}else r>40&&(t.style.maxHeight="200px")};return o.a.createElement("div",null,o.a.createElement("div",{className:"creatable-list",ref:v},p.map((function(e,t){return o.a.createElement($,{key:e.id,index:t,id:e.id,option:e,onChange:(n=e.id,function(e){var t=p.map((function(t){return t.id!==n?t:G(G({},t),{},{value:e,displayValue:e})}));f(t)}),onDeleteItem:y(e.id),moveListItem:O,addItem:g});var n}))),o.a.createElement(i.a,{title:l("action.addOption"),className:"add-item-button",label:l("action.addOption"),img:"icon-plus-sign",onClick:g}))};te.propTypes=ee;var ne=te;t.a=ne},1736:function(e,t,n){"use strict";n(35),n(26),n(27),n(11),n(13),n(8),n(25),n(22),n(29),n(28),n(45),n(23),n(24),n(48),n(46),n(14),n(10),n(9),n(12);var r=n(0),o=n.n(r),i=n(428),a=n(1584),u=n(1652),c=n(1538);n(1673);function l(e){return(l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){p(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function p(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==l(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==l(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===l(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var f=function(e){var t=e.onChange,n=e.onInputChange,r=e.options,l=e.onCreateOption,s=e.textPlaceholder,p=e.value,f=e.isClearable,b=e.isValid,m=e.messageText,h=Object(i.a)().t,v={control:function(e,t){return d(d({},e),{},{minHeight:"28px",height:"36px",backgroundColor:"var(--component-background)",borderColor:t.selectProps.isValid?"hsl(0, 0%, 80%)":"hsl(28, 80%, 52%)",boxShadow:null,"&:hover":null})},singleValue:function(e){return d(d({},e),{},{color:"var(--text-color)"})},menu:function(e){return d(d({},e),{},{backgroundColor:"var(--component-background)",color:"var(--text-color)"})},option:function(e){return d(d({},e),{},{backgroundColor:"var(--component-background)",color:"var(--text-color)","&:hover":{backgroundColor:"var(--popup-button-hover)"}})},indicatorsContainer:function(e){return d(d({},e),{},{paddingRight:"6px",height:"26px"})}};return o.a.createElement(c.a,null,o.a.createElement(a.a,{isClearable:f,onChange:t,onInputChange:n,options:r,onCreateOption:l,placeholder:s,formatCreateLabel:function(e){return"".concat(h("action.create")," ").concat(e)},value:p,styles:v,isValid:b,components:{IndicatorsContainer:u.a}}),m?o.a.createElement("div",{className:"messageText"},m):void 0)};t.a=f},1737:function(e,t,n){"use strict";n(90),n(8),n(26),n(27),n(11),n(13),n(25),n(22),n(29),n(28),n(45),n(23),n(24),n(48),n(46),n(19),n(14),n(10),n(9),n(12),n(16),n(15),n(20),n(18);var r=n(0),o=n.n(r),i=n(4),a=n.n(i),u=n(1551),c=n(428),l=n(76),s=n(115),d=n(1652),p=n(1538);n(1679);function f(e){return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function b(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,u=[],c=!0,l=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(u.push(r.value),u.length!==t);c=!0);}catch(e){l=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return m(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return m(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function m(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function h(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function v(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?h(Object(n),!0).forEach((function(t){g(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):h(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function g(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==f(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==f(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===f(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var y={onChangeHandler:a.a.func.isRequired,initialOption:a.a.string},O=function(e){var t=e.onChangeHandler,n=e.initialOption,i=Object(c.a)().t,a={control:function(e,t){return v(v({},e),{},{minHeight:"28px",backgroundColor:"var(--component-background)",borderColor:t.isFocused?"var(--gray-10)":"hsl(0, 0%, 80%)",borderRadius:t.isFocused?"4px":e.borderRadius,borderWidth:t.isFocused?"2px":e.borderWidth,boxShadow:t.isFocused?"0 0 0 1px var(--gray-10)":null,"&:hover":{borderColor:t.isFocused?"var(--gray-10)":"hsl(0, 0%, 70%)"}})},valueContainer:function(e){return v(v({},e),{},{padding:"2px"})},singleValue:function(e){return v(v({},e),{},{color:"var(--text-color)"})},menu:function(e){return v(v({},e),{},{backgroundColor:"var(--component-background)"})},option:function(e,t){return v(v({},e),{},{backgroundColor:t.isSelected?"var(--blue-5)":"var(--component-background)","&:hover":{backgroundColor:"var(--blue-6)",color:"var(--gray-0)"},"&:active":{backgroundColor:t.isSelected?"var(--blue-5)":"var(--blue-6)"},border:t.isFocused?"var(--focus-visible-outline) !important":"null"})},indicatorsContainer:function(e){return v(v({},e),{},{paddingRight:"6px",height:"26px"})}},f=[{value:s.a.FULL_SIGNATURE,label:i("formField.types.signature")},{value:s.a.INITIALS,label:i("option.type.initials")}],m=f.find((function(e){return e.value===n})),h=b(Object(r.useState)(m),2),g=h[0],y=h[1],O=b(Object(r.useState)(!1),2),w=O[0],j=O[1];return o.a.createElement(l.a,{className:"signature-options-container",dataElement:"signatureOptionsDropdown"},o.a.createElement("label",{id:"form-field-type-label"},i("formField.type"),":"),o.a.createElement(p.a,null,o.a.createElement(u.a,{value:g,onChange:function(e){y(e),t(e),j(!1)},styles:a,options:f,isSearchable:!1,isClearable:!1,components:{IndicatorsContainer:d.a},"aria-labelledby":"form-field-type-label",onKeyDown:function(e){"Enter"===e.key&&(e.preventDefault(),j((function(e){return!e})))},menuIsOpen:w,onMenuOpen:function(){j(!0)},onMenuClose:function(){j(!1)}})))};O.propTypes=y;var w=O;t.a=w},1818:function(e,t,n){(t=e.exports=n(33)(!1)).push([e.i,'.open.FormFieldEditPopupContainer{visibility:visible}.closed.FormFieldEditPopupContainer{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.FormFieldEditPopupContainer{position:absolute;z-index:70;display:flex;justify-content:center;align-items:center}.FormFieldEditPopupContainer:empty{padding:0}.FormFieldEditPopupContainer .buttons{display:flex}.FormFieldEditPopupContainer .Button{margin:4px;width:32px;height:32px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .FormFieldEditPopupContainer .Button{width:42px;height:42px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .FormFieldEditPopupContainer .Button{width:42px;height:42px}}.FormFieldEditPopupContainer .Button:hover{background:var(--popup-button-hover)}.FormFieldEditPopupContainer .Button:hover:disabled{background:none}.FormFieldEditPopupContainer .Button .Icon{width:18px;height:18px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .FormFieldEditPopupContainer .Button .Icon{width:24px;height:24px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .FormFieldEditPopupContainer .Button .Icon{width:24px;height:24px}}.is-vertical.FormFieldEditPopupContainer .Button.main-menu-button{width:100%;border-radius:0;justify-content:flex-start;padding-left:var(--padding-small);padding-right:var(--padding-small);margin:0 0 var(--padding-tiny) 0}.is-vertical.FormFieldEditPopupContainer .Button.main-menu-button:first-child{margin-top:var(--padding-tiny)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .is-vertical.FormFieldEditPopupContainer .Button.main-menu-button{width:100%;height:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .is-vertical.FormFieldEditPopupContainer .Button.main-menu-button{width:100%;height:32px}}.is-vertical.FormFieldEditPopupContainer .Button.main-menu-button .Icon{margin-right:10px}.is-vertical.FormFieldEditPopupContainer .Button.main-menu-button span{white-space:nowrap}@keyframes bottom-up{0%{transform:translateY(100%)}to{transform:translateY(0)}}@keyframes up-bottom{0%{transform:translateY(0)}to{transform:translateY(100%)}}.FormFieldEditPopup{padding:8px 12px 6px}.FormFieldEditPopup .fields-container{margin-bottom:5px}.FormFieldEditPopup .radio-group-label{grid-area:group-message;font-size:10px;width:240px;padding-top:5px}.FormFieldEditPopup .field-input{padding:5px 0;display:grid;grid-template-columns:1fr 2fr;grid-template-areas:"label dropdown" "group-message group-message"}.FormFieldEditPopup .field-input span{padding-top:10px;padding-right:10px;grid-area:label}.FormFieldEditPopup .field-flags-container{display:flex;flex-direction:column}.FormFieldEditPopup .field-flags-title{all:unset;padding-bottom:5px}.FormFieldEditPopup .form-buttons-container{display:flex;flex-direction:row;justify-content:flex-end;padding-top:5px}.FormFieldEditPopup .ok-form-field-button{display:flex;justify-content:center;align-items:center;padding:6px 18px;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;height:28px;cursor:pointer}.FormFieldEditPopup .ok-form-field-button:not(:disabled){background-color:var(--primary-button);border:1px solid var(--primary-button);color:var(--primary-button-text)}.FormFieldEditPopup .ok-form-field-button:hover:not(:disabled):not(.disabled){background:var(--primary-button-hover);border:1px solid var(--primary-button-hover)}.FormFieldEditPopup .cancel-form-field-button{display:flex;justify-content:center;align-items:center;color:var(--secondary-button-text);padding:6px 18px;width:auto;width:-moz-fit-content;width:fit-content;height:28px;cursor:pointer}.FormFieldEditPopup .cancel-form-field-button:hover{color:var(--secondary-button-hover);background:transparent}.field-options-container{width:180px}.field-options-container .creatable-list{max-height:200px;overflow:auto}.field-options-container input{width:120px;height:28px}.field-options-container .Button.add-item-button{width:90px;margin-left:0;justify-content:left}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .field-options-container .Button.add-item-button{width:110px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .field-options-container .Button.add-item-button{width:110px}}.form-dimension{padding-top:10px;display:flex;align-items:center}.form-dimension-input{padding-left:5px}.form-dimension-input input::-webkit-inner-spin-button,.form-dimension-input input::-webkit-outer-spin-button{-webkit-appearance:none;margin:0}.form-dimension-input input[type=number]{-moz-appearance:textfield}.form-dimension-input input{width:55px;height:28px}.FormFieldEditPopupContainer{border-radius:4px;box-shadow:0 0 3px 0 var(--document-box-shadow);background:var(--component-background)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .FormFieldEditPopupContainer{width:95%;position:fixed;left:1.25%!important;top:105px!important}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .FormFieldEditPopupContainer{width:95%;position:fixed;left:1.25%!important;top:105px!important}}',""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1968:function(e,t,n){"use strict";n.r(t);n(151),n(28),n(8),n(38),n(15),n(10),n(152),n(9),n(12),n(35),n(573),n(78),n(19),n(11),n(13),n(14),n(16),n(20),n(18),n(26),n(27),n(25),n(22),n(29),n(45),n(23),n(24),n(48),n(46);var r=n(0),o=n.n(r),i=n(176),a=n.n(i),u=n(1),c=(n(342),n(17)),l=n.n(c),s=n(44),d=n(428),p=n(1736),f=n(153),b=n(4),m=n.n(b),h={flags:m.a.arrayOf(m.a.shape({label:m.a.string,isChecked:m.a.bool,onChange:m.a.func}))},v=function(e){var t=e.flags,n=Object(d.a)().t;return o.a.createElement("div",{className:"field-flags-container"},o.a.createElement("h2",{className:"field-flags-title",id:"field-flags-group"},n("formField.formFieldPopup.flags")),o.a.createElement("div",{role:"group","aria-labelledby":"field-flags-group"},t.map((function(e){return o.a.createElement(f.a,{id:n(e.label),key:n(e.label),checked:e.isChecked,onChange:function(t){return e.onChange(t.target.checked)},label:n(e.label),"aria-checked":e.isChecked})}))))};v.propTypes=h;var g=v,y=function(e){var t=e.width,n=e.height,r=e.onWidthChange,i=e.onHeightChange,a=Object(d.a)().t;return o.a.createElement("div",{className:"form-dimension"},o.a.createElement("div",null,a("formField.formFieldPopup.size"),":"),o.a.createElement("div",{className:"form-dimension-input"},o.a.createElement("input",{id:"form-field-width",type:"number",min:0,"aria-label":a("formField.formFieldPopup.width"),value:t,onChange:function(e){return r(e.target.value)}})," ",a("formField.formFieldPopup.width")),o.a.createElement("div",{className:"form-dimension-input"},o.a.createElement("input",{id:"form-field-height",type:"number",min:0,"aria-label":a("formField.formFieldPopup.height"),value:n,onChange:function(e){return i(e.target.value)}})," ",a("formField.formFieldPopup.height")))},O=n(182),w=n(1666),j=(n(1677),n(1733)),E=n(1312),x=n(1957),C={indicator:m.a.object,indicatorPlaceholder:m.a.string},S=function(e){var t=e.indicator,n=e.indicatorPlaceholder,r=Object(d.a)().t;return o.a.createElement("div",{className:"form-field-indicator-container"},o.a.createElement("span",{id:"field-indicator-input",className:"field-indicator-title"},r("formField.formFieldPopup.fieldIndicator")),o.a.createElement(E.a,{id:"field-indicator",checked:t.isChecked,onChange:function(e){return r=e.target.checked,t.value.length<1&&r&&t.onChange(n),void t.toggleIndicator(r);var r},label:r(t.label),"aria-label":r(t.label),"aria-checked":t.isChecked}),o.a.createElement("div",{className:"field-indicator"},o.a.createElement(x.a,{id:"indicator-input",type:"text",onChange:function(e){return t.onChange(e.target.value)},value:t.value,fillWidth:"false",placeholder:n,disabled:!t.isChecked,"aria-disabled":!t.isChecked,"aria-labelledby":"field-indicator-input"})))};S.propTypes=C;var F=S;function P(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,u=[],c=!0,l=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(u.push(r.value),u.length!==t);c=!0);}catch(e){l=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return I(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return I(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function I(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var k={fields:m.a.array.isRequired,flags:m.a.array.isRequired,closeFormFieldEditPopup:m.a.func.isRequired,isValid:m.a.bool.isRequired,validationMessage:m.a.string.isRequired,radioButtonGroups:m.a.array,options:m.a.array,onOptionsChange:m.a.func,annotation:m.a.object.isRequired,selectedRadioGroup:m.a.string,getPageHeight:m.a.func.isRequired,getPageWidth:m.a.func.isRequired,redrawAnnotation:m.a.func.isRequired,indicator:m.a.object.isRequired},A=function(e){var t=e.fields,n=e.flags,i=e.closeFormFieldEditPopup,a=e.isValid,u=e.validationMessage,c=e.radioButtonGroups,f=e.options,b=e.onOptionsChange,m=e.annotation,h=e.selectedRadioGroup,v=e.getPageHeight,E=e.getPageWidth,x=e.redrawAnnotation,C=e.indicator,S=Object(d.a)().t,I=l()({Popup:!0,FormFieldEditPopup:!0}),k=P(Object(r.useState)(m.Width.toFixed(0)),2),A=k[0],M=k[1],R=P(Object(r.useState)(m.Height.toFixed(0)),2),D=R[0],V=R[1],L=Object(r.useRef)(null),T=P(Object(r.useState)(""===h?null:{value:h,label:h}),2),H=T[0],N=T[1];function _(e){return"text"===e.type?function(e){var t=e.required&&!a;return o.a.createElement(w.a,{label:"".concat(e.label,"-input"),value:e.value,onChange:e.onChange,validationMessage:u,hasError:t,ariaDescribedBy:t?"FormFieldInputError":void 0,ariaLabelledBy:e.label})}(e):"select"===e.type?function(e){var t=c.map((function(e){return{value:e,label:e}}));return o.a.createElement(o.a.Fragment,null,o.a.createElement(p.a,{textPlaceholder:S("formField.formFieldPopup.fieldName"),options:t,onChange:function(t){return function(e,t){null===t?(e.onChange(""),N(null)):(e.onChange(t.value),N({value:t.value,label:t.value}))}(e,t)},value:H,isValid:a,messageText:S(u)}),o.a.createElement("div",{className:"radio-group-label"},S("formField.formFieldPopup.radioGroups")))}(e):void 0}Object(r.useEffect)((function(){N(""!==h?{value:h,label:h}:null)}),[h]);var B=S("formField.formFieldPopup.indicatorPlaceHolders.".concat(m.getField().getFieldType()));return o.a.createElement("div",{className:I,ref:L},o.a.createElement("div",{className:"fields-container"},t.map((function(e){return o.a.createElement("div",{className:"field-input",key:e.label},o.a.createElement("span",{id:e.label},S(e.label),e.required?"*":"",":"),_(e))}))),f&&o.a.createElement("div",{className:"field-options-container"},S("formField.formFieldPopup.options"),o.a.createElement(j.a,{options:f,onOptionsUpdated:b,popupRef:L})),o.a.createElement(g,{flags:n}),o.a.createElement(y,{width:A,height:D,onWidthChange:function(e){var t=function(e){var t=E()-m.X;if(e>t)return t;return e}(e);m.setWidth(t),M(t),x(m)},onHeightChange:function(e){var t=function(e){var t=v()-m.Y;if(e>t)return t;return e}(e);m.setHeight(t),V(t),x(m)}}),o.a.createElement(O.a,null),o.a.createElement(F,{indicator:C,indicatorPlaceholder:B}),o.a.createElement("div",{className:"form-buttons-container"},o.a.createElement(s.a,{className:"ok-form-field-button",onClick:i,dataElement:"formFieldOK",label:S("action.close"),disabled:!a})))};A.propTypes=k;var M=A,R=n(1737);function D(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,u=[],c=!0,l=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(u.push(r.value),u.length!==t);c=!0);}catch(e){l=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return V(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return V(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function V(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var L={fields:m.a.array,flags:m.a.array,closeFormFieldEditPopup:m.a.func,isValid:m.a.bool,validationMessage:m.a.string,annotation:m.a.object,getPageHeight:m.a.func,getPageWidth:m.a.func,redrawAnnotation:m.a.func,onSignatureOptionChange:m.a.func,getSignatureOptionHandler:m.a.func,indicator:m.a.object},T=function(e){var t=e.fields,n=e.flags,i=e.closeFormFieldEditPopup,a=e.isValid,u=e.validationMessage,c=e.annotation,p=e.getPageHeight,f=e.getPageWidth,b=e.redrawAnnotation,m=e.onSignatureOptionChange,h=e.getSignatureOptionHandler,v=e.indicator,j=Object(d.a)().t,E=l()({Popup:!0,FormFieldEditPopup:!0}),x=D(Object(r.useState)(c.Width.toFixed(0)),2),C=x[0],S=x[1],P=D(Object(r.useState)(c.Height.toFixed(0)),2),I=P[0],k=P[1],A=D(Object(r.useState)(j("formField.formFieldPopup.indicatorPlaceHolders.SignatureFormField.".concat(h(c)))),2),M=A[0],V=A[1];return o.a.createElement("div",{className:E},o.a.createElement(R.a,{onChangeHandler:function(e){m(e);var t=e.value;V(j("formField.formFieldPopup.indicatorPlaceHolders.SignatureFormField.".concat(t)))},initialOption:h(c)}),o.a.createElement("div",{className:"fields-container"},t.map((function(e){return o.a.createElement("div",{className:"field-input",key:e.label},o.a.createElement("span",{id:e.label},j(e.label),e.required?"*":"",":"),function(e){var t=e.required&&!a;return o.a.createElement(w.a,{label:"".concat(e.label,"-input"),value:e.value,onChange:e.onChange,validationMessage:u,hasError:t,ariaDescribedBy:t?"FormFieldInputError":void 0,ariaLabelledBy:e.label})}(e))}))),o.a.createElement(g,{flags:n}),o.a.createElement(y,{width:C,height:I,onWidthChange:function(e){var t=function(e){var t=f()-c.X;if(e>t)return t;return e}(e);c.setWidth(t),S(t),b(c)},onHeightChange:function(e){var t=function(e){var t=p()-c.Y;if(e>t)return t;return e}(e);c.setHeight(t),k(t),b(c)}}),o.a.createElement(O.a,null),o.a.createElement(F,{indicator:v,indicatorPlaceholder:M}),o.a.createElement("div",{className:"form-buttons-container"},o.a.createElement(s.a,{className:"ok-form-field-button",onClick:i,dataElement:"formFieldOK",label:j("action.close"),disabled:!a})))};T.propTypes=L;var H=T,N=n(6),_=n(2),B=n(3),U=n(135),W=n(284),z=n(76),$=n(60),q=n(5),Y=n(56),G=n(123),X=n.n(G),K=n(110),Q=n.n(K);function J(e){return(J="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Z(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ee(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Z(Object(n),!0).forEach((function(t){te(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Z(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function te(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==J(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==J(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===J(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ne(e){return function(e){if(Array.isArray(e))return ie(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||oe(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function re(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,u=[],c=!0,l=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(u.push(r.value),u.length!==t);c=!0);}catch(e){l=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(e,t)||oe(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function oe(e,t){if(e){if("string"==typeof e)return ie(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?ie(e,t):void 0}}function ie(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var ae=window.Core.Annotations,ue={annotation:m.a.object.isRequired};function ce(e){var t=e.annotation,n=u.a.getFormFieldCreationManager(),i=re(Object(r.useState)(""),2),c=i[0],s=i[1],d=re(Object(r.useState)(""),2),p=d[0],f=d[1],b=re(Object(r.useState)(!1),2),m=b[0],h=b[1],v=re(Object(r.useState)(!1),2),g=v[0],y=v[1],O=re(Object(r.useState)(!1),2),w=O[0],j=O[1],E=re(Object(r.useState)(!1),2),x=E[0],C=E[1],S=re(Object(r.useState)(!0),2),F=S[0],P=S[1],I=re(Object(r.useState)([]),2),k=I[0],A=I[1],R=re(Object(r.useState)({left:0,top:0}),2),D=R[0],V=R[1],L=re(Object(r.useState)(""),2),T=L[0],G=L[1],K=re(Object(r.useState)(!1),2),J=K[0],Z=K[1],te=re(Object(r.useState)(""),2),oe=te[0],ie=te[1],ue=Object(r.useRef)(),ce=Object(r.useRef)(!0),le=re(Object(N.e)((function(e){return[B.a.isElementOpen(e,q.a.FORM_FIELD_EDIT_POPUP)]}),N.c),1)[0],se=Object(N.d)();function de(){se(_.a.enableElement(q.a.ANNOTATION_POPUP,Y.b)),se(_.a.closeElement(q.a.FORM_FIELD_EDIT_POPUP)),s(""),f(""),h(!1),y(!1),j(!1),C(!1),P(!0),Z(!1),ie("")}Object(U.a)(ue,(function(){""!==c.trim()&&de()})),Object(r.useEffect)((function(){var e=function(){A(n.getRadioButtonGroups())};return u.a.addEventListener("formFieldCreationModeStarted",e),function(){u.a.removeEventListener("formFieldCreationModeStarted",e)}}),[]),Object(r.useEffect)((function(){var e=u.a.getAnnotationsList().filter((function(e){return e instanceof ae.RadioButtonWidgetAnnotation})).map((function(e){return e.getField().name})),t=ne(new Set(ne(e)));A(t)}),[]);var pe=function(){ue.current&&ce.current&&V(Object(W.c)(t,ue))},fe=X()((function(){pe()}),16);Object(r.useEffect)((function(){return ce.current=!0,window.addEventListener("resize",fe),function(){ce.current=!1,window.removeEventListener("resize",fe)}}),[]),Object(r.useLayoutEffect)((function(){if(le&&t){var e=t.getFieldFlags(),r=e[ae.WidgetFlags.READ_ONLY],o=e[ae.WidgetFlags.MULTILINE],i=e[ae.WidgetFlags.REQUIRED],a=e[ae.WidgetFlags.MULTI_SELECT],u=t.getField();pe(),s(u.name),f(u.value),h(r||!1),y(o||!1),j(i||!1),C(a||!1);var c=ne(new Set([].concat(ne(k),ne(n.getRadioButtonGroups()))));A(c);var l=!!u.name;P(l);var d="";l||(d="formField.formFieldPopup.invalidField.empty"),G(d),Z(n.getShowIndicator(t)),ie(n.getIndicatorText(t))}}),[le]),Object(r.useLayoutEffect)((function(){var e=Q()((function(){ue.current&&pe()}),100),t=u.a.getDocumentViewer().getScrollViewElement();return null==t||t.addEventListener("scroll",e),function(){return null==t?void 0:t.removeEventListener("scroll",e)}}),[t]);var be=Object(r.useCallback)((function(e){var r=n.setFieldName(t,e);P(r.isValid),he(r),s(e),me(t)}),[t]),me=Object(r.useCallback)((function(e){if(e&&e instanceof ae.RadioButtonWidgetAnnotation){var t=e.getField().flags,n=ae.WidgetFlags,r=n.READ_ONLY,o=n.REQUIRED,i=t.get(r),a=t.get(o);h(i||!1),j(a||!1)}}),[t]),he=function(e){var t="";switch(e.errorType){case"empty":t="formField.formFieldPopup.invalidField.empty";break;case"duplicate":t="formField.formFieldPopup.invalidField.duplicate"}G(t)},ve=Object(r.useCallback)((function(e){f(e),t.getField().setValue(e)}),[t]),ge=Object(r.useCallback)((function(e){h(e),t.setFieldFlag(ae.WidgetFlags.READ_ONLY,e)}),[t]),ye=Object(r.useCallback)((function(e){y(e),t.setFieldFlag(ae.WidgetFlags.MULTILINE,e)}),[t]),Oe=Object(r.useCallback)((function(e){j(e),t.setFieldFlag(ae.WidgetFlags.REQUIRED,e)}),[t]),we=Object(r.useCallback)((function(e){C(e),t.setFieldFlag(ae.WidgetFlags.MULTI_SELECT,e)}),[t]),je=Object(r.useCallback)((function(e){t.setFieldOptions(e)}),[t]),Ee=Object(r.useCallback)((function(e){Z(e),n.setShowIndicator(t,e)}),[t]),xe=Object(r.useCallback)((function(e){ie(e),n.setIndicatorText(t,e)}),[t]),Ce=Object(r.useCallback)((function(){de()}),[]),Se=Object(r.useCallback)((function(){F&&-1===k.indexOf(c)&&""!==c&&A([c].concat(ne(k))),de()}),[c,k]),Fe=Object(r.useCallback)((function(e){u.a.getAnnotationManager().drawAnnotationsFromList([e])}),[]),Pe=Object(r.useCallback)((function(){return u.a.getPageHeight(u.a.getCurrentPage())}),[]),Ie=Object(r.useCallback)((function(){return u.a.getPageWidth(u.a.getCurrentPage())}),[]),ke=Object(r.useCallback)((function(e){var r=e.value;n.setSignatureOption(t,r)}),[t]),Ae=Object(r.useCallback)((function(e){return n.getSignatureOption(e)}),[]),Me={NAME:{label:"formField.formFieldPopup.fieldName",onChange:be,value:c,required:!0,type:"text",focus:!0},VALUE:{label:"formField.formFieldPopup.fieldValue",onChange:ve,value:p,type:"text"},RADIO_GROUP:{label:"formField.formFieldPopup.fieldName",onChange:be,value:c,required:!0,type:"select"}},Re={READ_ONLY:{label:"formField.formFieldPopup.readOnly",onChange:ge,isChecked:m},MULTI_LINE:{label:"formField.formFieldPopup.multiLine",onChange:ye,isChecked:g},REQUIRED:{label:"formField.formFieldPopup.required",onChange:Oe,isChecked:w},MULTI_SELECT:{label:"formField.formFieldPopup.multiSelect",onChange:we,isChecked:x}},De=[Me.NAME,Me.VALUE],Ve=[Me.NAME],Le=[Me.RADIO_GROUP],Te=[Me.NAME],He=[Me.NAME],Ne=[Me.NAME],_e=[Re.READ_ONLY,Re.MULTI_LINE,Re.REQUIRED],Be=[Re.REQUIRED,Re.READ_ONLY],Ue=[Re.READ_ONLY,Re.REQUIRED],We=[Re.READ_ONLY,Re.REQUIRED],ze=[Re.MULTI_SELECT,Re.READ_ONLY,Re.REQUIRED],$e=[Re.READ_ONLY,Re.REQUIRED],qe=[Re.READ_ONLY],Ye=Object($.b)(),Ge={label:"formField.formFieldPopup.documentFieldIndicator",toggleIndicator:Ee,isChecked:J,onChange:xe,value:oe},Xe=function(){switch(!0){case t instanceof ae.TextWidgetAnnotation:return o.a.createElement(M,{fields:De,flags:_e,closeFormFieldEditPopup:Ce,isValid:F,validationMessage:T,annotation:t,redrawAnnotation:Fe,getPageHeight:Pe,getPageWidth:Ie,indicator:Ge});case t instanceof ae.SignatureWidgetAnnotation:return o.a.createElement(H,{fields:Ve,flags:Be,closeFormFieldEditPopup:Ce,isValid:F,validationMessage:T,annotation:t,redrawAnnotation:Fe,getPageHeight:Pe,getPageWidth:Ie,onSignatureOptionChange:ke,getSignatureOptionHandler:Ae,indicator:Ge});case t instanceof ae.CheckButtonWidgetAnnotation:return o.a.createElement(M,{fields:Ve,flags:Ue,closeFormFieldEditPopup:Ce,isValid:F,validationMessage:T,annotation:t,redrawAnnotation:Fe,getPageHeight:Pe,getPageWidth:Ie,indicator:Ge});case t instanceof ae.RadioButtonWidgetAnnotation:return o.a.createElement(M,{fields:Le,flags:We,closeFormFieldEditPopup:Se,isValid:F,validationMessage:T,radioButtonGroups:k,annotation:t,selectedRadioGroup:c,redrawAnnotation:Fe,getPageHeight:Pe,getPageWidth:Ie,indicator:Ge});case t instanceof ae.ListWidgetAnnotation:return e=t.getFieldOptions(),o.a.createElement(M,{fields:Te,flags:ze,options:e,onOptionsChange:je,closeFormFieldEditPopup:Ce,isValid:F,validationMessage:T,annotation:t,redrawAnnotation:Fe,getPageHeight:Pe,getPageWidth:Ie,indicator:Ge});case t instanceof ae.ChoiceWidgetAnnotation:return function(){var e=t.getFieldOptions();return o.a.createElement(M,{fields:He,flags:$e,options:e,onOptionsChange:je,closeFormFieldEditPopup:Ce,isValid:F,validationMessage:T,annotation:t,redrawAnnotation:Fe,getPageHeight:Pe,getPageWidth:Ie,indicator:Ge})}();case t instanceof ae.PushButtonWidgetAnnotation:return o.a.createElement(M,{fields:Ne,flags:qe,closeFormFieldEditPopup:Ce,isValid:F,validationMessage:T,annotation:t,redrawAnnotation:Fe,getPageHeight:Pe,getPageWidth:Ie,indicator:Ge});default:return null}var e},Ke=function(){return o.a.createElement(z.a,{className:l()({Popup:!0,FormFieldEditPopupContainer:!0,open:le,closed:!le}),"data-element":q.a.FORM_FIELD_EDIT_POPUP,style:ee({},D),ref:ue},le&&Xe())};return Ye?Ke():o.a.createElement(a.a,{cancel:".Button, .cell, .sliders-container svg, .creatable-list, .ui__input__input, .form-dimension-input, .ui__choice__input"},Ke())}ce.propTypes=ue;var le=o.a.memo(ce);t.default=le}}]);
//# sourceMappingURL=chunk.27.js.map