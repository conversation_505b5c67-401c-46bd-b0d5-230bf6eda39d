{"version": 3, "sources": ["webpack:///./src/ui/src/components/OpenFileModal/OpenFileModal.scss?4152", "webpack:///./src/ui/src/components/OpenFileModal/OpenFileModal.scss", "webpack:///./src/ui/src/components/OpenFileModal/OpenFileModal.js", "webpack:///./src/ui/src/components/OpenFileModal/index.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "mapDispatchToProps", "closeElements", "actions", "connect", "state", "isDisabled", "selectors", "isElementDisabled", "DataElements", "OPEN_FILE_MODAL", "isOpen", "isElementOpen", "tabManager", "getTabManager", "t", "useTranslation", "selectedTab", "useSelector", "getSelectedTab", "useState", "src", "setSrc", "extension", "setExtension", "filename", "setFilename", "size", "setSize", "error", "setError", "closeModal", "useEffect", "PRINT_MODAL", "LOADING_MODAL", "PROGRESS_MODAL", "ERROR_MODAL", "MODEL3D_MODAL", "handleAddTab", "source", "_extension", "_filename", "_size", "acceptFormats", "indexOf", "useDb", "TabManager", "MAX_FILE_SIZE", "addTab", "setActive", "saveCurrentActiveTabState", "useDB", "modalClass", "classNames", "Modal", "OpenFileModal", "open", "closed", "extensionRegExp", "handleFileChange", "file", "Core", "Document", "type", "ext", "mimeTypeToExtension", "exec", "name", "URL", "createObjectURL", "handleURLChange", "url", "trim", "substring", "lastIndexOf", "split", "getHashParameters", "SupportedFileFormats", "SERVER", "CLIENT", "reduce", "uniqueArr", "curr", "className", "data-element", "onMouseDown", "e", "stopPropagation", "ModalWrapper", "title", "closeButtonDataElement", "onCloseClick", "swipeToClose", "<PERSON><PERSON><PERSON><PERSON>", "Tabs", "id", "dataElement", "FileInputPanel", "onFileSelect", "defaultValue", "FilePickerPanel", "onFileProcessed", "url<PERSON><PERSON>r", "fileError", "extensionError", "<PERSON><PERSON>", "label", "style", "width", "onClick", "useFocusOnClose", "disabled"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,o2HAAq2H,KAG93H0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,uwBCTvB,8lGAAA3B,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,+kBAAAA,GAAA,gEAAAA,GAAA,0JAAAA,EAAA,6FAAAA,GAAA,mIAAAA,IAAA,4SAAAA,IAAA,2OAAAA,EAAA,iBAAAA,EAAA,EAAAA,IAAA,EAAAA,GAAA,EAAAA,GAAA,SAkBA,IAkLM4B,EAAqB,CACzBC,cAAeC,IAAQD,eAGVE,eAVS,SAACC,GAAK,MAAM,CAClCC,WAAYC,IAAUC,kBAAkBH,EAAOI,IAAaC,iBAC5DC,OAAQJ,IAAUK,cAAcP,EAAOI,IAAaC,iBACpDG,WAAYN,IAAUO,cAAcT,MASpCJ,EAFaG,EAtLO,SAAH,GAA0D,IAApDE,EAAU,EAAVA,WAAYK,EAAM,EAANA,OAAQE,EAAU,EAAVA,WAAYX,EAAa,EAAbA,cAC/Ca,EAAMC,cAAND,EACDE,EAEL,EAFoBC,aAAY,SAACb,GAAK,MAAK,CAC3CE,IAAUY,eAAed,EAAO,qBAChC,GAFgB,GAGgB,IAAZe,mBAAS,IAAG,GAA3BC,EAAG,KAAEC,EAAM,KAC+B,IAAfF,mBAAS,OAAM,GAA1CG,EAAS,KAAEC,EAAY,KACY,IAAVJ,qBAAU,GAAnCK,EAAQ,KAAEC,EAAW,KACM,IAAVN,qBAAU,GAA3BO,EAAI,KAAEC,EAAO,KACyE,IAAnER,mBAAS,CAAE,UAAa,GAAI,SAAY,GAAI,eAAkB,KAAK,GAAtFS,EAAK,KAAEC,EAAQ,KAEhBC,EAAa,WACjB7B,EAAc,CAACO,IAAaC,kBAC5BY,EAAO,IACPQ,EAAS,CAAE,UAAa,GAAI,SAAY,KACxCJ,EAAY,MACZF,EAAa,OACbI,EAAQ,OAGVI,qBAAU,WACJrB,EACFT,EAAc,CACZO,IAAawB,YACbxB,IAAayB,cACbzB,IAAa0B,eACb1B,IAAa2B,YACb3B,IAAa4B,iBAGff,EAAO,IACPQ,EAAS,CAAE,UAAa,GAAI,SAAY,KACxCJ,EAAY,MACZF,EAAa,MACbI,EAAQ,SAET,CAACjB,IAEJ,IAAM2B,EAAY,6BAAG,WAAOC,EAAQC,EAAYC,EAAWC,GAAK,0EACzDH,EAAQ,CAAF,wCACFT,EAAS,CAAE,SAAY,kCAAiC,UAE5DU,IAAqD,IAAvCG,EAAcC,QAAQJ,GAAkB,yCAClDV,EAAS,CAAE,eAAkB,gCAA+B,OAEb,OAAlDe,GAASH,GAASI,IAAWC,cAAgBL,EAAK,SAClD7B,EAAWmC,OAAOT,EAAQ,CAC9BhB,UAAWiB,EACXf,SAAUgB,EACVQ,WAAW,EACXC,2BAA2B,EAC3BC,MAAON,IACP,OACFd,IAAa,2CACd,gBAhBiB,4CAkBZqB,EAAaC,IAAW,CAC5BC,OAAO,EACPC,eAAe,EACfC,KAAM7C,EACN8C,QAAS9C,IAGL+C,EAAkB,mBAElBC,EAAgB,6BAAG,WAAOC,GAAI,6EACnB,GAAf9B,EAAS,MACJ8B,EAAM,CAAF,qDAGLA,aAAgBpF,OAAOqF,KAAKC,UAAQ,gCAChCxB,EAAasB,EAAMA,EAAKG,KAAMH,EAAKnC,UAAS,8BASpC,OAPRuC,EAAMxF,OAAOqF,KAAKI,oBAAoBL,EAAKG,OAASL,EAAgBQ,KAAKN,EAAKO,MAAM,IAAM,KAC1F1C,EAAWmC,EAAKO,KAChBxC,EAAOiC,EAAKjC,KACZN,EAAM+C,IAAIC,gBAAgBT,GAChCtC,EAAOD,GACPK,EAAYD,GACZD,EAAawC,GACbpC,EAAQD,GAAM,UACRW,EAAasB,EAAMI,EAAKvC,EAAUE,GAAK,4CAEhD,gBAlBqB,sCAoBhB2C,EAAe,6BAAG,WAAOC,GAAG,uEAChCzC,EAAS,MACTR,EAAOiD,EAAIC,QACL/C,EAAW8C,EAAIE,UAAUF,EAAIG,YAAY,KAAO,GAAGC,MAAM,KAAK,GACpEjD,EAAYD,GACZD,EAAakC,EAAgBQ,KAAKzC,GAAU,IAC5CG,EAAQ,MAAM,2CACf,gBAPoB,sCAUjBe,IADeiC,YAAkB,qBAAsB,MAC5BpG,OAAOqF,KAAKgB,qBAAqBC,OAAStG,OAAOqF,KAAKgB,qBAAqBE,OAQ1G,OAPApC,EAAgBA,EAAcqC,QAAO,SAACC,EAAWC,GAI/C,OAHiC,IAA7BD,EAAUrC,QAAQsC,IACpBD,EAAU1F,KAAK2F,GAEVD,IACN,KAEK3E,GACN,yBAAK6E,UAAW/B,EAAYgC,eAAc3E,IAAaC,gBAAiB2E,YAAatD,GACnF,yBAAKoD,UAAU,YAAYE,YAAa,SAACC,GAAC,OAAKA,EAAEC,oBAC/C,kBAACC,EAAA,EAAY,CACXC,MAAO1E,EAAE,mBACTJ,OAAQA,EACR+E,uBAAwB,qBACxBC,aAAc5D,EACd6D,cAAY,EACZC,aAAc9D,GAEd,yBAAKoD,UAAU,oBACf,kBAACW,EAAA,EAAI,CAACX,UAAU,uBAAuBY,GAAG,iBACxC,yBAAKZ,UAAU,yBACb,yBAAKA,UAAU,YACb,kBAAC,IAAG,CAACa,YAAY,uBACf,4BAAQb,UAAU,sBACfpE,EAAE,cAGP,yBAAKoE,UAAU,wBACf,kBAAC,IAAG,CAACa,YAAY,yBACf,4BAAQb,UAAU,sBACfpE,EAAE,6CAKX,kBAAC,IAAQ,CAACiF,YAAY,iBACpB,yBAAKb,UAAU,cACb,kBAACc,EAAA,EAAc,CACbC,aAAc,SAAC3B,GACbD,EAAgBC,IAElB5B,cAAeA,EACfpB,UAAaF,EAAItC,QAAWwC,WAAWxC,OAAewC,EAAL,GACjDC,aAAcA,EACd2E,aAAc9E,MAIpB,kBAAC,IAAQ,CAAC2E,YAAY,mBACpB,yBAAKb,UAAU,cACb,kBAACiB,EAAA,EAAe,CACdC,gBAAiB,SAACzC,GAAI,OAAKD,EAAiBC,SAKpD,yBAAKuB,UAAU,6BACf,yBAAKA,UAAU,WACZtD,aAAK,EAALA,EAAOyE,WAAY,uBAAGnB,UAAU,SAAQ,KAAGtD,EAAMyE,WACjDzE,aAAK,EAALA,EAAO0E,YAAa,uBAAGpB,UAAU,SAAQ,KAAGtD,EAAM0E,YAClD1E,aAAK,EAALA,EAAO2E,iBAAkB,uBAAGrB,UAAU,SAAQ,KAAGtD,EAAM2E,gBACxD,kBAACC,EAAA,EAAM,CACLtB,UAAU,YACVa,YAAY,mBACZU,MAAO3F,EAAE,mBACT4F,MAAO,CAAEC,MAAO,IAChBC,QAASC,aAAgB,kBAAMxE,EAAajB,EAAKE,EAAWE,EAAUE,MACtEoF,SAA0B,wBAAhB9F,IAA2CI,EAAItC,UAAWwC,WAAWxC,iBCpL9EwE", "file": "chunks/chunk.70.js", "sourcesContent": ["var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./OpenFileModal.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.OpenFileModal .container .footer .modal-btn.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.OpenFileModal .container .footer .modal-btn.disabled span{color:var(--primary-button-text)}.OpenFileModal .container{overflow-y:unset;width:600px}.OpenFileModal .container .footer .modal-btn{color:var(--tab-footer-button-color);border:none}.OpenFileModal .container .footer .modal-btn.disabled span{color:var(--tab-footer-button-color)}.OpenFileModal .container .tab-list .tab-options-divider+.tab-options-button{border-left:none!important}.OpenFileModal .container .tab-list .tab-options-button{border-top:1px solid var(--tab-border-color);border-bottom:1px solid var(--tab-border-color)}.OpenFileModal .container .tab-list .tab-options-button:first-child{border-left:1px solid var(--tab-border-color)}.OpenFileModal .container .tab-list .tab-options-button:last-child{border-right:1px solid var(--tab-border-color)}.OpenFileModal .container .tab-list .tab-options-button:hover{background:var(--tab-background-color-hover);border-top:1px solid var(--tab-border-color-hover);border-bottom:1px solid var(--tab-border-color-hover);border-right:1px solid var(--tab-border-color-hover)}.OpenFileModal .container .tab-list .tab-options-button:hover+button,.OpenFileModal .container .tab-list .tab-options-button:hover+div{border-left:none}.OpenFileModal .container .tab-list .tab-options-button.selected{background:var(--tab-color-selected);border:1px solid var(--tab-color-selected);color:var(--tab-text-color-selected)}.OpenFileModal .container .tab-list .tab-options-button.selected+button,.OpenFileModal .container .tab-list .tab-options-button.selected+div{border-left:none!important}.OpenFileModal .container .tab-list .tab-options-button:not(.selected){border-right:1px solid var(--tab-border-color)}.OpenFileModal .image-signature{height:240px!important;width:100%}.OpenFileModal .error{color:red;margin:5px}.OpenFileModal .extension-dropdown{padding:0 16px;display:flex;flex-direction:column}.OpenFileModal .extension-dropdown label,.OpenFileModal .extension-dropdown p{width:100%;font-size:13px;line-height:16px;font-weight:700;order:1;margin-bottom:8px}.OpenFileModal .extension-dropdown label{margin-top:13px}.OpenFileModal .extension-dropdown .Dropdown__wrapper{order:2;width:100%}.OpenFileModal .extension-dropdown .Dropdown__wrapper .Dropdown{width:100%!important}.OpenFileModal .extension-dropdown .Dropdown__wrapper .Dropdown:disabled{border:1px solid var(--gray-4)}.OpenFileModal .extension-dropdown .Dropdown__wrapper .Dropdown:disabled .arrow,.OpenFileModal .extension-dropdown .Dropdown__wrapper .Dropdown:disabled .picked-option{color:var(--gray-5)}.OpenFileModal .extension-dropdown .Dropdown__wrapper .Dropdown__items{width:100%}.OpenFileModal .extension-dropdown .Dropdown__wrapper .arrow{flex:unset;width:12px;height:16px;margin-top:2px}.OpenFileModal .extension-dropdown .Dropdown__wrapper .Dropdown .picked-option .picked-option-text{flex:none;width:unset}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React, { useEffect, useState } from 'react';\nimport { connect, useSelector } from 'react-redux';\nimport TabManager from 'helpers/TabManager';\nimport classNames from 'classnames';\nimport getHashParameters from 'helpers/getHashParameters';\nimport Button from 'components/Button';\nimport { useTranslation } from 'react-i18next';\nimport { Tabs, Tab, TabPanel } from 'components/Tabs';\nimport FileInputPanel from 'components/PageReplacementModal/FileInputPanel';\nimport FilePickerPanel from 'components/PageReplacementModal/FilePickerPanel';\nimport selectors from 'selectors';\nimport actions from 'actions';\nimport DataElements from 'constants/dataElement';\nimport ModalWrapper from '../ModalWrapper';\nimport useFocusOnClose from 'hooks/useFocusOnClose';\n\nimport '../PageReplacementModal/PageReplacementModal.scss';\nimport './OpenFileModal.scss';\n\nconst OpenFileModal = ({ isDisabled, isOpen, tabManager, closeElements }) => {\n  const { t } = useTranslation();\n  const [selectedTab] = useSelector((state) => [\n    selectors.getSelectedTab(state, 'openFileModal'),\n  ]);\n  const [src, setSrc] = useState('');\n  const [extension, setExtension] = useState('pdf');\n  const [filename, setFilename] = useState();\n  const [size, setSize] = useState();\n  const [error, setError] = useState({ 'fileError': '', 'urlError': '', 'extensionError': '' });\n\n  const closeModal = () => {\n    closeElements([DataElements.OPEN_FILE_MODAL]);\n    setSrc('');\n    setError({ 'fileError': '', 'urlError': '' });\n    setFilename(null);\n    setExtension('pdf');\n    setSize(null);\n  };\n\n  useEffect(() => {\n    if (isOpen) {\n      closeElements([\n        DataElements.PRINT_MODAL,\n        DataElements.LOADING_MODAL,\n        DataElements.PROGRESS_MODAL,\n        DataElements.ERROR_MODAL,\n        DataElements.MODEL3D_MODAL,\n      ]);\n    } else {\n      setSrc('');\n      setError({ 'fileError': '', 'urlError': '' });\n      setFilename(null);\n      setExtension(null);\n      setSize(null);\n    }\n  }, [isOpen]);\n\n  const handleAddTab = async (source, _extension, _filename, _size) => {\n    if (!source) {\n      return setError({ 'urlError': 'URL or File must be provided' });\n    }\n    if (!_extension || acceptFormats.indexOf(_extension) === -1) {\n      return setError({ 'extensionError': 'Extension must be provided' });\n    }\n    const useDb = !_size || TabManager.MAX_FILE_SIZE > _size;\n    await tabManager.addTab(source, {\n      extension: _extension,\n      filename: _filename,\n      setActive: true,\n      saveCurrentActiveTabState: true,\n      useDB: useDb\n    });\n    closeModal();\n  };\n\n  const modalClass = classNames({\n    Modal: true,\n    OpenFileModal: true,\n    open: isOpen,\n    closed: !isOpen,\n  });\n\n  const extensionRegExp = /(?:\\.([^.?]+))?$/;\n\n  const handleFileChange = async (file) => {\n    setError(null);\n    if (!file) {\n      return;\n    }\n    if (file instanceof window.Core.Document) {\n      await handleAddTab(file, file.type, file.filename);\n    } else {\n      const ext = window.Core.mimeTypeToExtension[file.type] || extensionRegExp.exec(file.name)[1] || null;\n      const filename = file.name;\n      const size = file.size;\n      const src = URL.createObjectURL(file);\n      setSrc(src);\n      setFilename(filename);\n      setExtension(ext);\n      setSize(size);\n      await handleAddTab(file, ext, filename, size);\n    }\n  };\n\n  const handleURLChange = async (url) => {\n    setError(null);\n    setSrc(url.trim());\n    const filename = url.substring(url.lastIndexOf('/') + 1).split('?')[0];\n    setFilename(filename);\n    setExtension(extensionRegExp.exec(filename)[1]);\n    setSize(null);\n  };\n\n  const wvServer = !!getHashParameters('webviewerServerURL', null);\n  let acceptFormats = wvServer ? window.Core.SupportedFileFormats.SERVER : window.Core.SupportedFileFormats.CLIENT;\n  acceptFormats = acceptFormats.reduce((uniqueArr, curr) => {\n    if (uniqueArr.indexOf(curr) === -1) {\n      uniqueArr.push(curr);\n    }\n    return uniqueArr;\n  }, []);\n\n  return !isDisabled && (\n    <div className={modalClass} data-element={DataElements.OPEN_FILE_MODAL} onMouseDown={closeModal}>\n      <div className=\"container\" onMouseDown={(e) => e.stopPropagation()}>\n        <ModalWrapper\n          title={t('OpenFile.newTab')}\n          isOpen={isOpen}\n          closeButtonDataElement={'openFileModalClose'}\n          onCloseClick={closeModal}\n          swipeToClose\n          closeHandler={closeModal}\n        >\n          <div className=\"swipe-indicator\" />\n          <Tabs className=\"open-file-modal-tabs\" id=\"openFileModal\">\n            <div className=\"tabs-header-container\">\n              <div className=\"tab-list\">\n                <Tab dataElement=\"urlInputPanelButton\">\n                  <button className=\"tab-options-button\">\n                    {t('link.url')}\n                  </button>\n                </Tab>\n                <div className=\"tab-options-divider\" />\n                <Tab dataElement=\"filePickerPanelButton\">\n                  <button className=\"tab-options-button\">\n                    {t('option.pageReplacementModal.localFile')}\n                  </button>\n                </Tab>\n              </div>\n            </div>\n            <TabPanel dataElement=\"urlInputPanel\">\n              <div className=\"panel-body\">\n                <FileInputPanel\n                  onFileSelect={(url) => {\n                    handleURLChange(url);\n                  }}\n                  acceptFormats={acceptFormats}\n                  extension={(!src.length || !extension?.length) ? '' : extension}\n                  setExtension={setExtension}\n                  defaultValue={src}\n                />\n              </div>\n            </TabPanel>\n            <TabPanel dataElement=\"filePickerPanel\">\n              <div className=\"panel-body\">\n                <FilePickerPanel\n                  onFileProcessed={(file) => handleFileChange(file)}\n                />\n              </div>\n            </TabPanel>\n          </Tabs>\n          <div className=\"page-replacement-divider\" />\n          <div className=\"footer\">\n            {error?.urlError && <p className=\"error\">* {error.urlError}</p>}\n            {error?.fileError && <p className=\"error\">* {error.fileError}</p>}\n            {error?.extensionError && <p className=\"error\">* {error.extensionError}</p>}\n            <Button\n              className=\"modal-btn\"\n              dataElement=\"linkSubmitButton\"\n              label={t('OpenFile.addTab')}\n              style={{ width: 90 }}\n              onClick={useFocusOnClose(() => handleAddTab(src, extension, filename, size))}\n              disabled={selectedTab !== 'urlInputPanelButton' || (!src.length || !extension?.length)}\n            />\n          </div>\n        </ModalWrapper>\n      </div>\n    </div>\n  );\n};\n\nconst mapStateToProps = (state) => ({\n  isDisabled: selectors.isElementDisabled(state, DataElements.OPEN_FILE_MODAL),\n  isOpen: selectors.isElementOpen(state, DataElements.OPEN_FILE_MODAL),\n  tabManager: selectors.getTabManager(state),\n});\n\nconst mapDispatchToProps = {\n  closeElements: actions.closeElements,\n};\n\nexport default connect(\n  mapStateToProps,\n  mapDispatchToProps,\n)(OpenFileModal);\n", "import OpenFileModal from './OpenFileModal';\n\nexport default OpenFileModal;\n"], "sourceRoot": ""}