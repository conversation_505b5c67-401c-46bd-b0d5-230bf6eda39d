(window.webpackJsonp=window.webpackJsonp||[]).push([[56],{1870:function(o,t,e){var n=e(32),a=e(1871);"string"==typeof(a=a.__esModule?a.default:a)&&(a=[[o.i,a,""]]);var i={insert:function(o){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(o);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function o(t,e=document){const n=[];return e.querySelectorAll(t).forEach(o=>n.push(o)),e.querySelectorAll("*").forEach(e=>{e.shadowRoot&&n.push(...o(t,e.shadowRoot))}),n}("apryse-webviewer"));const e=[];for(let n=0;n<t.length;n++){const a=t[n];if(0===n)a.shadowRoot.appendChild(o),o.onload=function(){e.length>0&&e.forEach(t=>{t.innerHTML=o.innerHTML})};else{const t=o.cloneNode(!0);a.shadowRoot.appendChild(t),e.push(t)}}},singleton:!1};n(a,i);o.exports=a.locals||{}},1871:function(o,t,e){(t=o.exports=e(33)(!1)).push([o.i,".open.CalibrationModal{visibility:visible}.closed.CalibrationModal{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.CalibrationModal .footer .modal-button.confirm:hover{background:var(--primary-button-hover);border-color:var(--primary-button-hover);color:var(--gray-0)}.CalibrationModal .footer .modal-button.confirm{background:var(--primary-button);border-color:var(--primary-button);color:var(--primary-button-text)}.CalibrationModal .footer .modal-button.confirm.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.CalibrationModal .footer .modal-button.confirm.disabled span{color:var(--primary-button-text)}.CalibrationModal .footer .modal-button.cancel:hover,.CalibrationModal .footer .modal-button.secondary-btn-custom:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6)}.CalibrationModal .footer .modal-button.cancel,.CalibrationModal .footer .modal-button.secondary-btn-custom{border:none;box-shadow:inset 0 0 0 1px var(--primary-button);color:var(--primary-button)}.CalibrationModal .footer .modal-button.cancel.disabled,.CalibrationModal .footer .modal-button.secondary-btn-custom.disabled{cursor:default;border:none;box-shadow:inset 0 0 0 1px rgba(43,115,171,.5);color:rgba(43,115,171,.5)}.CalibrationModal .footer .modal-button.cancel.disabled span,.CalibrationModal .footer .modal-button.secondary-btn-custom.disabled span{color:rgba(43,115,171,.5)}.CalibrationModal{position:fixed;left:0;bottom:0;z-index:100;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background:var(--modal-negative-space)}.CalibrationModal .modal-container .wrapper .modal-content{padding:10px}.CalibrationModal .footer{display:flex;flex-direction:row;justify-content:flex-end;width:100%;margin-top:13px}.CalibrationModal .footer.modal-footer{padding:16px;margin:0;border-top:1px solid var(--divider)}.CalibrationModal .footer .modal-button{display:flex;justify-content:center;align-items:center;padding:6px 18px;margin:8px 0 0;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;height:30px;cursor:pointer}.CalibrationModal .footer .modal-button.confirm{margin-left:4px}.CalibrationModal .footer .modal-button.secondary-btn-custom{border-radius:4px;padding:2px 20px 4px;cursor:pointer}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .CalibrationModal .footer .modal-button{padding:23px 8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .CalibrationModal .footer .modal-button{padding:23px 8px}}.CalibrationModal .swipe-indicator{background:var(--swipe-indicator-bg);border-radius:2px;height:4px;width:38px;position:absolute;top:12px;margin-left:auto;margin-right:auto;left:0;right:0}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .CalibrationModal .swipe-indicator{display:none}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .CalibrationModal .swipe-indicator{display:none}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .CalibrationModal .swipe-indicator{width:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .CalibrationModal .swipe-indicator{width:32px}}.CalibrationModal .container{border-radius:4px;overflow-y:auto;max-height:100%}@media(max-height:500px){.App:not(.is-web-component) .CalibrationModal .container,.CalibrationModal .App:not(.is-web-component) .container{overflow:auto;max-height:100%}}@container (max-height: 500px){.App.is-web-component .CalibrationModal .container,.CalibrationModal .App.is-web-component .container{overflow:auto;max-height:100%}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .CalibrationModal .container,.CalibrationModal .App:not(.is-in-desktop-only-mode):not(.is-web-component) .container{width:100%;position:fixed;left:0;bottom:0;padding-top:4px;min-width:100px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .CalibrationModal .container,.CalibrationModal .App.is-web-component:not(.is-in-desktop-only-mode) .container{width:100%;position:fixed;left:0;bottom:0;padding-top:4px;min-width:100px}}.CalibrationModal .container{display:flex;flex-direction:column;min-width:400px;box-shadow:0 0 3px 0 var(--document-box-shadow);padding:8px;background:var(--component-background)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .CalibrationModal .container{padding:24px 24px 16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .CalibrationModal .container{padding:24px 24px 16px}}.CalibrationModal .container .calibration__header{font-size:16px;margin-top:8px;margin-bottom:8px}.CalibrationModal .container .calibration__body{padding-top:8px}.CalibrationModal .container .calibration__body .calibration__input{margin-top:8px;display:flex;flex-direction:row;justify-content:space-between}.CalibrationModal .container .calibration__body input{width:100%;height:30px;font-size:13px;border:1px solid var(--border);border-radius:4px;color:var(--text-color);padding:4px 42px 6px 8px;margin-right:8px}.CalibrationModal .container .calibration__body input:focus{outline:none;border:1px solid var(--focus-border)}.CalibrationModal .container .calibration__body input::-moz-placeholder{color:var(--placeholder-text)}.CalibrationModal .container .calibration__body input::placeholder{color:var(--placeholder-text)}.CalibrationModal .container .calibration__body .error{border:1px solid var(--error-border-color)}.CalibrationModal .container .calibration__body .unitToInput{height:29px;width:45px;border-radius:5px;border:1px solid var(--border);font-size:13px}.CalibrationModal .container .calibration__body .unitToInput:focus{outline:none;border:1px solid var(--focus-border)}.CalibrationModal .container .calibration__body .unitToInput::-moz-placeholder{color:var(--placeholder-text)}.CalibrationModal .container .calibration__body .unitToInput::placeholder{color:var(--placeholder-text)}.CalibrationModal .container .calibration__body .errorMeasurement{font-size:13px;margin-top:8px;margin-bottom:8px;color:var(--error-text-color);max-width:400px}.CalibrationModal .container .calibration__footer{display:flex;justify-content:flex-end;margin-top:8px}.CalibrationModal .container .calibration__footer .Button{display:flex;justify-content:center;align-items:center;color:var(--primary-button-text);font-weight:600;padding:6px 18px;margin-top:8px;margin-left:5px;width:auto;width:-moz-fit-content;width:fit-content;background:var(--primary-button);border-radius:4px;height:30px;cursor:pointer}.CalibrationModal .container .calibration__footer .Button:hover{background:var(--primary-button-hover)}",""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1994:function(o,t,e){"use strict";e.r(t);e(342),e(142),e(151),e(18),e(1868),e(25),e(38),e(19),e(11),e(13),e(8),e(14),e(10),e(9),e(12),e(16),e(15),e(20);var n=e(0),a=e.n(n),i=e(17),r=e.n(i),l=e(6),d=e(428),c=e(44),p=e(1),s=e(50),b=e(193);function u(o,t){return function(o){if(Array.isArray(o))return o}(o)||function(o,t){var e=null==o?null:"undefined"!=typeof Symbol&&o[Symbol.iterator]||o["@@iterator"];if(null!=e){var n,a,i,r,l=[],d=!0,c=!1;try{if(i=(e=e.call(o)).next,0===t){if(Object(e)!==e)return;d=!1}else for(;!(d=(n=i.call(e)).done)&&(l.push(n.value),l.length!==t);d=!0);}catch(o){c=!0,a=o}finally{try{if(!d&&null!=e.return&&(r=e.return(),Object(r)!==r))return}finally{if(c)throw a}}return l}}(o,t)||function(o,t){if(!o)return;if("string"==typeof o)return m(o,t);var e=Object.prototype.toString.call(o).slice(8,-1);"Object"===e&&o.constructor&&(e=o.constructor.name);if("Map"===e||"Set"===e)return Array.from(o);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return m(o,t)}(o,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function m(o,t){(null==t||t>o.length)&&(t=o.length);for(var e=0,n=new Array(t);e<t;e++)n[e]=o[e];return n}var f=function(o){var t=u(o.split("/"),2),e=t[0],n=t[1];return Number(e)/Number(n)},x=e(2),h=e(3),y=e(140),w=e(5);e(1870);function v(o,t){return function(o){if(Array.isArray(o))return o}(o)||function(o,t){var e=null==o?null:"undefined"!=typeof Symbol&&o[Symbol.iterator]||o["@@iterator"];if(null!=e){var n,a,i,r,l=[],d=!0,c=!1;try{if(i=(e=e.call(o)).next,0===t){if(Object(e)!==e)return;d=!1}else for(;!(d=(n=i.call(e)).done)&&(l.push(n.value),l.length!==t);d=!0);}catch(o){c=!0,a=o}finally{try{if(!d&&null!=e.return&&(r=e.return(),Object(r)!==r))return}finally{if(c)throw a}}return l}}(o,t)||function(o,t){if(!o)return;if("string"==typeof o)return g(o,t);var e=Object.prototype.toString.call(o).slice(8,-1);"Object"===e&&o.constructor&&(e=o.constructor.name);if("Map"===e||"Set"===e)return Array.from(o);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return g(o,t)}(o,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function g(o,t){(null==t||t>o.length)&&(t=o.length);for(var e=0,n=new Array(t);e<t;e++)n[e]=o[e];return n}var M=function(o){var t=o.Measure.axis[0].factor;switch(o.Scale[1][1]){case"ft-in":return o.getLineLength()*t/12;case"in":default:return o.getLineLength()*t}},C=/^\d*(\.\d*)?$/,A=/^\d*(\s\d\/\d*)$/,_=/^(\d\/\d*)*$/,E=function(){var o=v(Object(l.e)((function(o){return[h.a.isElementOpen(o,w.a.CALIBRATION_MODAL),h.a.isElementDisabled(o,w.a.CALIBRATION_MODAL),h.a.getMeasurementUnits(o)]}),l.c),3),t=o[0],e=o[1],i=o[2],u=Object(l.d)(),m=v(Object(n.useState)(null),2),g=m[0],E=m[1],O=v(Object(n.useState)(""),2),S=O[0],j=O[1],k=v(Object(n.useState)(0),2),I=k[0],N=k[1],L=v(Object(n.useState)(""),2),T=L[0],D=L[1],R=v(Object(n.useState)(!1),2),z=R[0],F=R[1],B=v(Object(d.a)(),1)[0],H=Object(n.useRef)(null);Object(n.useEffect)((function(){var o;t&&(null==H||null===(o=H.current)||void 0===o||o.focus()),$(p.a.getSelectedAnnotations())}),[t]),Object(n.useEffect)((function(){var o=function(o,t){"selected"===t?$(o):"deselected"===t&&(E(null),j(""),D(""),N(0))};return p.a.addEventListener("annotationSelected",o),function(){return p.a.removeEventListener("annotationSelected",o)}}),[]),Object(n.useEffect)((function(){var o=function(o,t){"modify"===t&&1===o.length&&o[0]===g&&(j(M(g).toFixed(2)),D(g.Scale[1][1]))};return p.a.addEventListener("annotationChanged",o),function(){return p.a.removeEventListener("annotationChanged",o)}}),[g]);var $=function(o){if(1===(null==o?void 0:o.length)&&"distanceMeasurement"===Object(s.g)(o[0])){var t=o[0];E(t);var e=M(t).toFixed(2);j(e),D(t.Scale[1][1]),N(parseFloat(e))}},U=function(o){return g.Scale=o,W()},W=function(){var o=M(g),t=I/o,e=g.Scale;return[[e[0][0],e[0][1]],[e[1][0]*t,T]]},q=function(){u(x.a.closeElements([w.a.CALIBRATION_MODAL]))};return e||!g?null:a.a.createElement(y.a,{onSwipedUp:q,onSwipedDown:q,preventDefaultTouchmoveEvent:!0},a.a.createElement("div",{className:r()({Modal:!0,CalibrationModal:!0,open:t,closed:!t}),onMouseDown:q}," ",a.a.createElement("div",{className:"container",onMouseDown:function(o){return o.stopPropagation()}},a.a.createElement("div",{className:"swipe-indicator"}),a.a.createElement("div",{className:"calibration__header"},B("component.calibration")),a.a.createElement("div",{className:"calibration__body"},a.a.createElement("div",null,B("message.enterMeasurement")),a.a.createElement("div",{className:"calibration__input"},a.a.createElement("input",{className:z?"error":"",ref:H,type:"text",value:S,onChange:function(o){F(!1),j(o.target.value)},onBlur:function(o){var t=o.target.value.trim();if(""===t&&F(!0),C.test(t))0!==parseFloat(t)?(N(parseFloat(t)),j(t)):F(!0);else if(A.test(t)){var e=v(t.split(" "),2),n=e[0],a=e[1];if(Number.isFinite(f(a))){var i=Number(n)+f(a);N(parseFloat(i)),j(i)}else F(!0)}else if(_.test(t))if(Number.isFinite(f(t))){var r=f(t);N(parseFloat(r)),j(r)}else F(!0);else F(!0)}}),a.a.createElement("select",{className:"unitToInput",value:T,onChange:function(o){D(o.target.value)}},i.to.map((function(o){return a.a.createElement("option",{key:o,value:o},o)})))),z?a.a.createElement("div",{className:"errorMeasurement"},B("message.errorEnterMeasurement")):null),a.a.createElement("div",{className:"calibration__footer"},a.a.createElement(c.a,{dataElement:"passwordSubmitButton",label:B("action.apply"),onClick:function(){var o=W(),t=U(o);p.a.setAnnotationStyles(g,{Scale:t}),Object(b.a)("AnnotationCreateDistanceMeasurement","Scale",t),u(x.a.closeElements([w.a.CALIBRATION_MODAL]))},disabled:z})))))};t.default=E}}]);
//# sourceMappingURL=chunk.56.js.map