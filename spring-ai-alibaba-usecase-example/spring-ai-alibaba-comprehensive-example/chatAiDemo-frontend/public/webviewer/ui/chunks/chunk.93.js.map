{"version": 3, "sources": ["webpack:///./src/ui/src/components/ModularComponents/PresetButton/buttons/SheetEditor/TextAlignmentButton.js"], "names": ["propTypes", "isFlyoutItem", "PropTypes", "bool", "alignment", "string", "style", "object", "className", "TextAlignmentButton", "forwardRef", "props", "ref", "dispatch", "useDispatch", "txt", "menuItems", "dataElement", "icon", "title", "handleClick", "actions", "setFlyoutToggleElement", "toggleElement", "DataElements", "CELL_TEXT_ALIGNMENT_FLYOUT", "onClick", "additionalClass", "aria<PERSON>urrent", "isActive", "img", "displayName"], "mappings": "maASA,IAAMA,EAAY,CAChBC,aAAcC,IAAUC,KACxBC,UAAWF,IAAUG,OACrBC,MAAOJ,IAAUK,OACjBC,UAAWN,IAAUG,QAGjBI,EAAsBC,sBAAW,SAACC,EAAOC,GAC7C,IAAQX,EAA8CU,EAA9CV,aAAcG,EAAgCO,EAAhCP,UAAWE,EAAqBK,EAArBL,MAAOE,EAAcG,EAAdH,UAClCK,EAAWC,cAGbC,EAAM,oBACNX,IACFW,EAAMX,GAER,MAAqCY,IAAUD,GAAvCE,EAAW,EAAXA,YAAaC,EAAI,EAAJA,KAAMC,EAAK,EAALA,MAErBC,EAAc,WAElBP,EAASQ,IAAQC,uBAAuBL,IACxCJ,EAASQ,IAAQE,cAAcC,IAAaC,8BAG9C,OACExB,EACE,kBAAC,IAAmB,KACdU,EAAK,CACTC,IAAKA,EACLc,QAASN,EACTO,gBAAuC,MAGvC,kBAAC,IAAY,CACXC,aAxBS,EAyBTC,UAzBS,EA0BTZ,YAAaA,EACbE,MAAOA,EACPW,IAAKZ,EACLQ,QAASN,EACTd,MAAOA,EACPE,UAAWA,OAMrBC,EAAoBT,UAAYA,EAChCS,EAAoBsB,YAAc,sBAEnBtB", "file": "chunks/chunk.93.js", "sourcesContent": ["import React, { forwardRef } from 'react';\nimport { useDispatch } from 'react-redux';\nimport DataElements from 'constants/dataElement';\nimport ActionButton from 'components/ActionButton';\nimport PropTypes from 'prop-types';\nimport actions from 'actions';\nimport FlyoutItemContainer from '../../../FlyoutItemContainer';\nimport { menuItems } from '../../../Helpers/menuItems';\n\nconst propTypes = {\n  isFlyoutItem: PropTypes.bool,\n  alignment: PropTypes.string,\n  style: PropTypes.object,\n  className: PropTypes.string,\n};\n\nconst TextAlignmentButton = forwardRef((props, ref) => {\n  const { isFlyoutItem, alignment, style, className } = props;\n  const dispatch = useDispatch();\n  const isActive = false;\n\n  let txt = 'cellTextAlignment';\n  if (alignment) {\n    txt = alignment;\n  }\n  const { dataElement, icon, title } = menuItems[txt];\n\n  const handleClick = () => {\n    // handle button click\n    dispatch(actions.setFlyoutToggleElement(dataElement));\n    dispatch(actions.toggleElement(DataElements.CELL_TEXT_ALIGNMENT_FLYOUT));\n  };\n\n  return (\n    isFlyoutItem ?\n      <FlyoutItemContainer\n        {...props}\n        ref={ref}\n        onClick={handleClick}\n        additionalClass={isActive ? 'active' : ''}\n      />\n      : (\n        <ActionButton\n          ariaCurrent={isActive}\n          isActive={isActive}\n          dataElement={dataElement}\n          title={title}\n          img={icon}\n          onClick={handleClick}\n          style={style}\n          className={className}\n        />\n      )\n  );\n});\n\nTextAlignmentButton.propTypes = propTypes;\nTextAlignmentButton.displayName = 'TextAlignmentButton';\n\nexport default TextAlignmentButton;"], "sourceRoot": ""}