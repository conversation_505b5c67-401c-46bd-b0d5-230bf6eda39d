{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/cs.js"], "names": ["module", "exports", "e", "t", "default", "n", "s", "r", "d", "name", "weekdays", "split", "weekdaysShort", "weekdaysMin", "months", "monthsShort", "weekStart", "yearStart", "ordinal", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "l", "relativeTime", "future", "past", "m", "mm", "h", "hh", "dd", "M", "MM", "y", "yy", "locale"], "mappings": "gFAAoEA,EAAOC,QAA6K,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAG,SAASI,EAAEJ,GAAG,OAAOA,EAAE,GAAGA,EAAE,GAAG,MAAMA,EAAE,IAAI,SAASK,EAAEL,EAAEG,EAAEF,EAAEI,GAAG,IAAIC,EAAEN,EAAE,IAAI,OAAOC,GAAG,IAAI,IAAI,OAAOE,GAAGE,EAAE,aAAa,gBAAgB,IAAI,IAAI,OAAOF,EAAE,SAASE,EAAE,SAAS,UAAU,IAAI,KAAK,OAAOF,GAAGE,EAAEC,GAAGF,EAAEJ,GAAG,SAAS,SAASM,EAAE,WAAW,IAAI,IAAI,OAAOH,EAAE,SAASE,EAAE,SAAS,UAAU,IAAI,KAAK,OAAOF,GAAGE,EAAEC,GAAGF,EAAEJ,GAAG,SAAS,SAASM,EAAE,WAAW,IAAI,IAAI,OAAOH,GAAGE,EAAE,MAAM,OAAO,IAAI,KAAK,OAAOF,GAAGE,EAAEC,GAAGF,EAAEJ,GAAG,MAAM,OAAOM,EAAE,MAAM,IAAI,IAAI,OAAOH,GAAGE,EAAE,QAAQ,UAAU,IAAI,KAAK,OAAOF,GAAGE,EAAEC,GAAGF,EAAEJ,GAAG,SAAS,UAAUM,EAAE,SAAS,IAAI,IAAI,OAAOH,GAAGE,EAAE,MAAM,QAAQ,IAAI,KAAK,OAAOF,GAAGE,EAAEC,GAAGF,EAAEJ,GAAG,OAAO,OAAOM,EAAE,QAAQ,IAAIA,EAAE,CAACC,KAAK,KAAKC,SAAS,mDAAmDC,MAAM,KAAKC,cAAc,uBAAuBD,MAAM,KAAKE,YAAY,uBAAuBF,MAAM,KAAKG,OAAO,oFAAoFH,MAAM,KAAKI,YAAY,kDAAkDJ,MAAM,KAAKK,UAAU,EAAEC,UAAU,EAAEC,QAAQ,SAAShB,GAAG,OAAOA,EAAE,KAAKiB,QAAQ,CAACC,GAAG,OAAOC,IAAI,UAAUC,EAAE,aAAaC,GAAG,eAAeC,IAAI,oBAAoBC,KAAK,yBAAyBC,EAAE,cAAcC,aAAa,CAACC,OAAO,QAAQC,KAAK,UAAUvB,EAAEC,EAAEuB,EAAEvB,EAAEwB,GAAGxB,EAAEyB,EAAEzB,EAAE0B,GAAG1B,EAAEC,EAAED,EAAE2B,GAAG3B,EAAE4B,EAAE5B,EAAE6B,GAAG7B,EAAE8B,EAAE9B,EAAE+B,GAAG/B,IAAI,OAAOJ,EAAEC,QAAQmC,OAAO/B,EAAE,MAAK,GAAIA,EAAliDH,CAAE,EAAQ", "file": "chunks/chunk.121.js", "sourcesContent": ["!function(e,n){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=n(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],n):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_cs=n(e.dayjs)}(this,(function(e){\"use strict\";function n(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var t=n(e);function s(e){return e>1&&e<5&&1!=~~(e/10)}function r(e,n,t,r){var d=e+\" \";switch(t){case\"s\":return n||r?\"pár sekund\":\"pár sekundami\";case\"m\":return n?\"minuta\":r?\"minutu\":\"minutou\";case\"mm\":return n||r?d+(s(e)?\"minuty\":\"minut\"):d+\"minutami\";case\"h\":return n?\"hodina\":r?\"hodinu\":\"hodinou\";case\"hh\":return n||r?d+(s(e)?\"hodiny\":\"hodin\"):d+\"hodinami\";case\"d\":return n||r?\"den\":\"dnem\";case\"dd\":return n||r?d+(s(e)?\"dny\":\"dní\"):d+\"dny\";case\"M\":return n||r?\"měsíc\":\"měsícem\";case\"MM\":return n||r?d+(s(e)?\"měsíce\":\"měsíců\"):d+\"měsíci\";case\"y\":return n||r?\"rok\":\"rokem\";case\"yy\":return n||r?d+(s(e)?\"roky\":\"let\"):d+\"lety\"}}var d={name:\"cs\",weekdays:\"neděle_pondělí_úterý_středa_čtvrtek_pátek_sobota\".split(\"_\"),weekdaysShort:\"ne_po_út_st_čt_pá_so\".split(\"_\"),weekdaysMin:\"ne_po_út_st_čt_pá_so\".split(\"_\"),months:\"leden_únor_březen_duben_květen_červen_červenec_srpen_září_říjen_listopad_prosinec\".split(\"_\"),monthsShort:\"led_úno_bře_dub_kvě_čvn_čvc_srp_zář_říj_lis_pro\".split(\"_\"),weekStart:1,yearStart:4,ordinal:function(e){return e+\".\"},formats:{LT:\"H:mm\",LTS:\"H:mm:ss\",L:\"DD.MM.YYYY\",LL:\"D. MMMM YYYY\",LLL:\"D. MMMM YYYY H:mm\",LLLL:\"dddd D. MMMM YYYY H:mm\",l:\"D. M. YYYY\"},relativeTime:{future:\"za %s\",past:\"před %s\",s:r,m:r,mm:r,h:r,hh:r,d:r,dd:r,M:r,MM:r,y:r,yy:r}};return t.default.locale(d,null,!0),d}));"], "sourceRoot": ""}