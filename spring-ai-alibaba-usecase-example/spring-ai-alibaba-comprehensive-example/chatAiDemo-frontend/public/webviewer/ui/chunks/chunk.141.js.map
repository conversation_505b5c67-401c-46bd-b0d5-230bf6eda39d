{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/es-do.js"], "names": ["module", "exports", "e", "s", "default", "o", "d", "name", "weekdays", "split", "weekdaysShort", "weekdaysMin", "months", "monthsShort", "weekStart", "relativeTime", "future", "past", "m", "mm", "h", "hh", "dd", "M", "MM", "y", "yy", "ordinal", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "locale"], "mappings": "gFAAoEA,EAAOC,QAAgL,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAGI,EAAE,CAACC,KAAK,QAAQC,SAAS,uDAAuDC,MAAM,KAAKC,cAAc,qCAAqCD,MAAM,KAAKE,YAAY,uBAAuBF,MAAM,KAAKG,OAAO,2FAA2FH,MAAM,KAAKI,YAAY,kDAAkDJ,MAAM,KAAKK,UAAU,EAAEC,aAAa,CAACC,OAAO,QAAQC,KAAK,UAAUd,EAAE,gBAAgBe,EAAE,YAAYC,GAAG,aAAaC,EAAE,WAAWC,GAAG,WAAWf,EAAE,SAASgB,GAAG,UAAUC,EAAE,SAASC,GAAG,WAAWC,EAAE,SAASC,GAAG,WAAWC,QAAQ,SAASzB,GAAG,OAAOA,EAAE,KAAK0B,QAAQ,CAACC,GAAG,SAASC,IAAI,YAAYC,EAAE,aAAaC,GAAG,wBAAwBC,IAAI,+BAA+BC,KAAK,uCAAuC,OAAO/B,EAAEC,QAAQ+B,OAAO7B,EAAE,MAAK,GAAIA,EAAlkCD,CAAE,EAAQ", "file": "chunks/chunk.141.js", "sourcesContent": ["!function(e,o){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=o(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],o):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_es_do=o(e.dayjs)}(this,(function(e){\"use strict\";function o(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var s=o(e),d={name:\"es-do\",weekdays:\"domingo_lunes_martes_miércoles_jueves_viernes_sábado\".split(\"_\"),weekdaysShort:\"dom._lun._mar._mié._jue._vie._sáb.\".split(\"_\"),weekdaysMin:\"do_lu_ma_mi_ju_vi_sá\".split(\"_\"),months:\"enero_febrero_marzo_abril_mayo_junio_julio_agosto_septiembre_octubre_noviembre_diciembre\".split(\"_\"),monthsShort:\"ene_feb_mar_abr_may_jun_jul_ago_sep_oct_nov_dic\".split(\"_\"),weekStart:1,relativeTime:{future:\"en %s\",past:\"hace %s\",s:\"unos segundos\",m:\"un minuto\",mm:\"%d minutos\",h:\"una hora\",hh:\"%d horas\",d:\"un día\",dd:\"%d días\",M:\"un mes\",MM:\"%d meses\",y:\"un año\",yy:\"%d años\"},ordinal:function(e){return e+\"º\"},formats:{LT:\"h:mm A\",LTS:\"h:mm:ss A\",L:\"DD/MM/YYYY\",LL:\"D [de] MMMM [de] YYYY\",LLL:\"D [de] MMMM [de] YYYY h:mm A\",LLLL:\"dddd, D [de] MMMM [de] YYYY h:mm A\"}};return s.default.locale(d,null,!0),d}));"], "sourceRoot": ""}