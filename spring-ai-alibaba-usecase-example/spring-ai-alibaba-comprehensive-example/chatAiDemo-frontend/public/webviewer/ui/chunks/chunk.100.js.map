{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/af.js"], "names": ["module", "exports", "e", "n", "default", "a", "t", "name", "weekdays", "split", "months", "weekStart", "weekdaysShort", "monthsShort", "weekdaysMin", "ordinal", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "relativeTime", "future", "past", "s", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "locale"], "mappings": "gFAAoEA,EAAOC,QAA6K,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAGI,EAAE,CAACC,KAAK,KAAKC,SAAS,4DAA4DC,MAAM,KAAKC,OAAO,8FAA8FD,MAAM,KAAKE,UAAU,EAAEC,cAAc,8BAA8BH,MAAM,KAAKI,YAAY,kDAAkDJ,MAAM,KAAKK,YAAY,uBAAuBL,MAAM,KAAKM,QAAQ,SAASb,GAAG,OAAOA,GAAGc,QAAQ,CAACC,GAAG,QAAQC,IAAI,WAAWC,EAAE,aAAaC,GAAG,cAAcC,IAAI,oBAAoBC,KAAK,2BAA2BC,aAAa,CAACC,OAAO,SAASC,KAAK,YAAYC,EAAE,mBAAmBC,EAAE,YAAYC,GAAG,YAAYC,EAAE,SAASC,GAAG,SAASC,EAAE,SAASC,GAAG,SAASC,EAAE,WAAWC,GAAG,YAAYC,EAAE,UAAUC,GAAG,YAAY,OAAOjC,EAAEC,QAAQiC,OAAO/B,EAAE,MAAK,GAAIA,EAA3hCD,CAAE,EAAQ", "file": "chunks/chunk.100.js", "sourcesContent": ["!function(e,a){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=a(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],a):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_af=a(e.dayjs)}(this,(function(e){\"use strict\";function a(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var n=a(e),t={name:\"af\",weekdays:\"Sondag_Maandag_Dinsdag_Woensdag_Donderdag_Vrydag_Saterdag\".split(\"_\"),months:\"Januarie_Februarie_Maart_April_Mei_Junie_Julie_Augustus_September_Oktober_November_Desember\".split(\"_\"),weekStart:1,weekdaysShort:\"Son_Maa_Din_Woe_Don_Vry_Sat\".split(\"_\"),monthsShort:\"Jan_Feb_Mrt_Apr_Mei_Jun_Jul_Aug_Sep_Okt_Nov_Des\".split(\"_\"),weekdaysMin:\"So_Ma_Di_Wo_Do_Vr_Sa\".split(\"_\"),ordinal:function(e){return e},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY HH:mm\",LLLL:\"dddd, D MMMM YYYY HH:mm\"},relativeTime:{future:\"oor %s\",past:\"%s gelede\",s:\"'n paar sekondes\",m:\"'n minuut\",mm:\"%d minute\",h:\"'n uur\",hh:\"%d ure\",d:\"'n dag\",dd:\"%d dae\",M:\"'n maand\",MM:\"%d maande\",y:\"'n jaar\",yy:\"%d jaar\"}};return n.default.locale(t,null,!0),t}));"], "sourceRoot": ""}