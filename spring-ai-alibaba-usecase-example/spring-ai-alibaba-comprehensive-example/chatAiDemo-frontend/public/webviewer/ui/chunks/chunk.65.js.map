{"version": 3, "sources": ["webpack:///./src/ui/src/components/LinkAnnotationPopup/LinkAnnotationPopup.scss?e9a7", "webpack:///./src/ui/src/components/LinkAnnotationPopup/LinkAnnotationPopup.scss", "webpack:///./src/ui/src/components/LinkAnnotationPopup/LinkAnnotationPopup.js", "webpack:///./src/ui/src/components/LinkAnnotationPopup/LinkAnnotationPopupContainer.js", "webpack:///./src/ui/src/components/LinkAnnotationPopup/index.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "propTypes", "handleUnLink", "PropTypes", "func", "isAnnotation", "bool", "isMobileDevice", "linkText", "string", "handleOnMouseEnter", "handleOnMouseLeave", "handleMouseMove", "LinkAnnotationPopup", "data-testid", "className", "classNames", "Popup", "onMouseEnter", "onMouseLeave", "onMouseMove", "data-element", "DataElements", "LINK_URI", "ActionButton", "dataElement", "LINK_ANNOTATION_UNLINK_BUTTON", "title", "img", "onClick", "Annotations", "Core", "annotation", "object", "LinkAnnotationPopupContainer", "useSelector", "state", "selectors", "isElementOpen", "LINK_ANNOTATION_POPUP", "getActiveDocumentViewerKey", "shallowEqual", "isOpen", "activeDocumentViewerKey", "useState", "left", "top", "position", "setPosition", "dispatch", "useDispatch", "popupRef", "useRef", "closePopup", "actions", "closeElement", "useOnClickOutside", "useEffect", "onScroll", "scrollViewElement", "core", "getScrollViewElement", "addEventListener", "removeEventListener", "useLayoutEffect", "setPopupPosition", "current", "getAnnotationPopupPositionBasedOn", "contents", "getLinkDestination", "open", "closed", "undefined", "DataElementWrapper", "style", "ref", "annotationManager", "getAnnotationManager", "textHighlightAnnotation", "getGroupAnnotations", "find", "annot", "index", "TextHighlightAnnotation", "Opacity", "linkAnnotations", "getGroupedLinkAnnotations", "linkAnnot", "ungroupAnnotations", "deleteAnnotation", "deleteLinkAnnotationWithGroup", "isMobileSize", "event", "preventDefault", "stopPropagation"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,45HAA65H,KAGt7H0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,kSCFjBC,G,QAAY,CAChBC,aAAcC,IAAUC,KACxBC,aAAcF,IAAUG,KACxBC,eAAgBJ,IAAUG,KAC1BE,SAAUL,IAAUM,OACpBC,mBAAoBP,IAAUC,KAC9BO,mBAAoBR,IAAUC,KAC9BQ,gBAAiBT,IAAUC,OAGvBS,EAAsB,SAAH,GAQnB,IAPJX,EAAY,EAAZA,aACAG,EAAY,EAAZA,aACAE,EAAc,EAAdA,eACAC,EAAQ,EAARA,SACAE,EAAkB,EAAlBA,mBACAC,EAAkB,EAAlBA,mBACAC,EAAe,EAAfA,gBAsBA,OAAIL,IAAmBF,EACd,KAIP,yBACES,cAAY,0BACZC,UAAWC,IAAW,CACpBC,OAAO,EACPJ,qBAAqB,EACrB,iBAAiB,IAEnBK,aAAcR,EACdS,aAAcR,EACdS,YAAaR,GAjCf,yBAAKG,UAAU,WAAWM,eAAcC,IAAaC,UAClDf,GACC,oCACE,yBAAKO,UAAU,oBACZP,GAEH,yBAAKO,UAAU,aAGnB,kBAACS,EAAA,EAAY,CACXT,UAAU,mBACVU,YAAaH,IAAaI,8BAC1BC,MAAM,gBACNC,IAAI,mBACJC,QAAS3B,OA0BjBW,EAAoBZ,UAAYA,EAEjBY,Q,2yEC7Cf,IAAQiB,EAAgBtD,OAAOuD,KAAvBD,YAEF7B,EAAY,CAChB+B,WAAY7B,IAAU8B,OACtBvB,mBAAoBP,IAAUC,KAC9BO,mBAAoBR,IAAUC,MAgB1B8B,EAA+B,SAAH,GAI5B,IAHJF,EAAU,EAAVA,WACAtB,EAAkB,EAAlBA,mBACAC,EAAkB,EAAlBA,mBAQgB,IAHZwB,aAAY,SAACC,GAAK,MAAK,CACzBC,IAAUC,cAAcF,EAAOd,IAAaiB,uBAC5CF,IAAUG,2BAA2BJ,MACpCK,KAAa,GALdC,EAAM,KACNC,EAAuB,KAMoC,IAA7BC,mBAAS,CAAEC,KAAM,EAAGC,IAAK,IAAI,GAAtDC,EAAQ,KAAEC,EAAW,KACtBC,EAAWC,cACXC,EAAWC,iBAAO,MAElBC,EAAa,WAAH,OAASJ,EAASK,IAAQC,aAAajC,IAAaiB,yBAOpEiB,YAAkBL,GAAU,WACtBT,GACFW,OAIJI,qBAAU,WACR,IAAMC,EAAW,WACfL,KAGIM,EAAoBC,IAAKC,uBAE/B,OADAF,WAAmBG,iBAAiB,SAAUJ,GACvC,WACLC,WAAmBI,oBAAoB,SAAUL,MAElD,IAEHM,2BAAgB,YACVhC,GAAcU,IAChBuB,MAED,CAACjC,EAAYU,EAAQC,IAExB,IAAMsB,EAAmB,WACnBjC,GAAcmB,EAASe,SACzBlB,EAAYmB,YAAkCnC,EAAYmB,EAAUR,EAAyB,KAI3FyB,EAAWC,YAAmBrC,IAAe,GAQ7CjB,EAAYC,IAAW,CAC3BC,OAAO,EACPiB,8BAA8B,EAC9BoC,KAAM5B,EACN6B,QAAS7B,IAGLrC,OAA8BmE,IAAfxC,EAErB,OACE,kBAACyC,EAAA,EAAkB,CACjBhD,YAAaH,IAAaiB,sBAC1BxB,UAAWA,EACX2D,MAAK,KAAO3B,GACZ4B,IAAKxB,GAEL,kBAAC,EAAmB,CAClB3C,SAAU4D,EACVlE,aAxBe,YArEsB,SAAC8B,GAA4C,IAAhCW,EAA0B,UAAH,6CAAG,EAC5EiC,EAAoBhB,IAAKiB,qBAAqBlC,GAC9CmC,EAA0BF,EAAkBG,oBAAoB/C,GAAYgD,MAAK,SAACC,EAAOC,GAAK,OAAKD,aAAiBnD,EAAYqD,yBAA6C,IAAlBF,EAAMG,SAA2B,IAAVF,KAClLG,EAAkBC,YAA0BtD,GAClDqD,EAAgBhG,SAAQ,SAACkG,GACvBX,EAAkBY,mBAAmB,CAACD,IAClCT,GACFF,EAAkBa,iBAAiBF,EAAW,CAAE,OAAU,WAAY,MAG1EX,EAAkBa,iBAAiBzD,EAAY,CAAE,OAAU,WAAY,GA4DrE0D,CAA8B1D,EAAYW,GAC1CU,IACA1C,KAsBIN,aAAcA,EACdE,eAAgBoF,cAChBjF,mBAAoBA,EACpBC,mBAAoBA,EACpBC,gBAlEkB,SAACgF,GACvBA,EAAMC,iBACND,EAAME,uBAuEV5D,EAA6BjC,UAAYA,EAE1BiC,QC1IAA", "file": "chunks/chunk.65.js", "sourcesContent": ["var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./LinkAnnotationPopup.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".open.LinkAnnotationPopupContainer{visibility:visible}.closed.LinkAnnotationPopupContainer{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.LinkAnnotationPopupContainer{position:absolute;z-index:70;display:flex;justify-content:center;align-items:center}.LinkAnnotationPopupContainer:empty{padding:0}.LinkAnnotationPopupContainer .buttons{display:flex}.LinkAnnotationPopupContainer .Button{margin:4px;width:32px;height:32px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .LinkAnnotationPopupContainer .Button{width:42px;height:42px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .LinkAnnotationPopupContainer .Button{width:42px;height:42px}}.LinkAnnotationPopupContainer .Button:hover{background:var(--popup-button-hover)}.LinkAnnotationPopupContainer .Button:hover:disabled{background:none}.LinkAnnotationPopupContainer .Button .Icon{width:18px;height:18px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .LinkAnnotationPopupContainer .Button .Icon{width:24px;height:24px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .LinkAnnotationPopupContainer .Button .Icon{width:24px;height:24px}}.is-vertical.LinkAnnotationPopupContainer .Button.main-menu-button{width:100%;border-radius:0;justify-content:flex-start;padding-left:var(--padding-small);padding-right:var(--padding-small);margin:0 0 var(--padding-tiny) 0}.is-vertical.LinkAnnotationPopupContainer .Button.main-menu-button:first-child{margin-top:var(--padding-tiny)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .is-vertical.LinkAnnotationPopupContainer .Button.main-menu-button{width:100%;height:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .is-vertical.LinkAnnotationPopupContainer .Button.main-menu-button{width:100%;height:32px}}.is-vertical.LinkAnnotationPopupContainer .Button.main-menu-button .Icon{margin-right:10px}.is-vertical.LinkAnnotationPopupContainer .Button.main-menu-button span{white-space:nowrap}@keyframes bottom-up{0%{transform:translateY(100%)}to{transform:translateY(0)}}@keyframes up-bottom{0%{transform:translateY(0)}to{transform:translateY(100%)}}.LinkAnnotationPopupContainer{border-radius:4px;box-shadow:0 0 3px 0 var(--document-box-shadow);background:var(--component-background)}.LinkAnnotationPopup.is-horizontal .contents{display:flex;justify-content:center;align-items:center;grid-gap:8px;gap:8px}.LinkAnnotationPopup.is-horizontal .contents .link-annot-input{margin:8px 0 8px 8px;color:var(--gray-8);font-style:normal;font-weight:400;line-height:normal;border:none;width:-moz-fit-content;width:fit-content;max-width:240px;cursor:pointer;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:3;line-clamp:3;-webkit-box-orient:vertical;box-sizing:border-box;word-break:break-all}.LinkAnnotationPopup.is-horizontal .contents .divider{width:1px;height:20px;background:var(--divider);flex-shrink:0}.LinkAnnotationPopup.is-horizontal .contents .main-menu-button{margin:4px 8px 4px 0}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import ActionButton from 'components/ActionButton';\nimport classNames from 'classnames';\nimport DataElements from 'constants/dataElement';\nimport PropTypes from 'prop-types';\nimport React from 'react';\n\nimport './LinkAnnotationPopup.scss';\n\nconst propTypes = {\n  handleUnLink: PropTypes.func,\n  isAnnotation: PropTypes.bool,\n  isMobileDevice: PropTypes.bool,\n  linkText: PropTypes.string,\n  handleOnMouseEnter: PropTypes.func,\n  handleOnMouseLeave: PropTypes.func,\n  handleMouseMove: PropTypes.func,\n};\n\nconst LinkAnnotationPopup = ({\n  handleUnLink,\n  isAnnotation,\n  isMobileDevice,\n  linkText,\n  handleOnMouseEnter,\n  handleOnMouseLeave,\n  handleMouseMove\n}) => {\n  const renderContents = () => (\n    <div className=\"contents\" data-element={DataElements.LINK_URI}>\n      {linkText && (\n        <>\n          <div className=\"link-annot-input\">\n            {linkText}\n          </div>\n          <div className=\"divider\" />\n        </>\n      )}\n      <ActionButton\n        className=\"main-menu-button\"\n        dataElement={DataElements.LINK_ANNOTATION_UNLINK_BUTTON}\n        title='action.unlink'\n        img='icon-tool-unlink'\n        onClick={handleUnLink}\n      />\n    </div>\n  );\n\n  if (isMobileDevice || !isAnnotation) {\n    return null;\n  }\n\n  return (\n    <div\n      data-testid=\"link-annotation-element\"\n      className={classNames({\n        Popup: true,\n        LinkAnnotationPopup: true,\n        'is-horizontal': true\n      })}\n      onMouseEnter={handleOnMouseEnter}\n      onMouseLeave={handleOnMouseLeave}\n      onMouseMove={handleMouseMove}\n    >\n      {renderContents()}\n    </div>\n  );\n};\n\nLinkAnnotationPopup.propTypes = propTypes;\n\nexport default LinkAnnotationPopup;\n", "import actions from 'actions';\nimport classNames from 'classnames';\nimport core from 'core';\nimport DataElements from 'constants/dataElement';\nimport DataElementWrapper from '../DataElementWrapper';\nimport { getAnnotationPopupPositionBasedOn } from 'helpers/getPopupPosition';\nimport { isMobileSize } from 'helpers/getDeviceSize';\nimport LinkAnnotationPopup from './LinkAnnotationPopup';\nimport PropTypes from 'prop-types';\nimport selectors from 'selectors';\nimport getLinkDestination from 'helpers/getLinkDestination';\nimport useOnClickOutside from 'hooks/useOnClickOutside';\nimport React, {\n  useState,\n  useRef,\n  useLayoutEffect,\n  useEffect\n} from 'react';\nimport {\n  useSelector,\n  shallowEqual,\n  useDispatch\n} from 'react-redux';\nimport getGroupedLinkAnnotations from 'src/helpers/getGroupedLinkAnnotations';\n\nconst { Annotations } = window.Core;\n\nconst propTypes = {\n  annotation: PropTypes.object,\n  handleOnMouseEnter: PropTypes.func,\n  handleOnMouseLeave: PropTypes.func,\n};\n\nexport const deleteLinkAnnotationWithGroup = (annotation, activeDocumentViewerKey = 1) => {\n  const annotationManager = core.getAnnotationManager(activeDocumentViewerKey);\n  const textHighlightAnnotation = annotationManager.getGroupAnnotations(annotation).find((annot, index) => annot instanceof Annotations.TextHighlightAnnotation && annot.Opacity === 0 && index === 0);\n  const linkAnnotations = getGroupedLinkAnnotations(annotation);\n  linkAnnotations.forEach((linkAnnot) => {\n    annotationManager.ungroupAnnotations([linkAnnot]);\n    if (textHighlightAnnotation) {\n      annotationManager.deleteAnnotation(linkAnnot, { 'source': 'unlink' }, true);\n    }\n  });\n  annotationManager.deleteAnnotation(annotation, { 'source': 'unlink' }, true);\n};\n\nconst LinkAnnotationPopupContainer = ({\n  annotation,\n  handleOnMouseEnter,\n  handleOnMouseLeave\n}) => {\n  const [\n    isOpen,\n    activeDocumentViewerKey,\n  ] = useSelector((state) => [\n    selectors.isElementOpen(state, DataElements.LINK_ANNOTATION_POPUP),\n    selectors.getActiveDocumentViewerKey(state),\n  ], shallowEqual);\n\n  const [position, setPosition] = useState({ left: 0, top: 0 });\n  const dispatch = useDispatch();\n  const popupRef = useRef(null);\n\n  const closePopup = () => dispatch(actions.closeElement(DataElements.LINK_ANNOTATION_POPUP));\n\n  const handleMouseMove = (event) => {\n    event.preventDefault();\n    event.stopPropagation();\n  };\n\n  useOnClickOutside(popupRef, () => {\n    if (isOpen) {\n      closePopup();\n    }\n  });\n\n  useEffect(() => {\n    const onScroll = () => {\n      closePopup();\n    };\n\n    const scrollViewElement = core.getScrollViewElement();\n    scrollViewElement?.addEventListener('scroll', onScroll);\n    return () => {\n      scrollViewElement?.removeEventListener('scroll', onScroll);\n    };\n  }, []);\n\n  useLayoutEffect(() => {\n    if (annotation || isOpen) {\n      setPopupPosition();\n    }\n  }, [annotation, isOpen, activeDocumentViewerKey]);\n\n  const setPopupPosition = () => {\n    if (annotation && popupRef.current) {\n      setPosition(getAnnotationPopupPositionBasedOn(annotation, popupRef, activeDocumentViewerKey, 5));\n    }\n  };\n\n  const contents = getLinkDestination(annotation) || '';\n\n  const handleUnLink = () => {\n    deleteLinkAnnotationWithGroup(annotation, activeDocumentViewerKey);\n    closePopup();\n    handleOnMouseLeave();\n  };\n\n  const className = classNames({\n    Popup: true,\n    LinkAnnotationPopupContainer: true,\n    open: isOpen,\n    closed: !isOpen,\n  });\n\n  const isAnnotation = annotation !== undefined;\n\n  return (\n    <DataElementWrapper\n      dataElement={DataElements.LINK_ANNOTATION_POPUP}\n      className={className}\n      style={{ ...position }}\n      ref={popupRef}\n    >\n      <LinkAnnotationPopup\n        linkText={contents}\n        handleUnLink={handleUnLink}\n        isAnnotation={isAnnotation}\n        isMobileDevice={isMobileSize()}\n        handleOnMouseEnter={handleOnMouseEnter}\n        handleOnMouseLeave={handleOnMouseLeave}\n        handleMouseMove={handleMouseMove}\n      />\n    </DataElementWrapper>\n\n  );\n};\n\nLinkAnnotationPopupContainer.propTypes = propTypes;\n\nexport default LinkAnnotationPopupContainer;", "import LinkAnnotationPopupContainer from './LinkAnnotationPopupContainer';\n\nexport default LinkAnnotationPopupContainer;"], "sourceRoot": ""}