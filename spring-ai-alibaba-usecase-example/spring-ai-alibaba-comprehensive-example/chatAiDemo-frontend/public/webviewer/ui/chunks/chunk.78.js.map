{"version": 3, "sources": ["webpack:///./src/ui/src/components/WarningModal/WarningModal.scss?f75b", "webpack:///./src/ui/src/components/WarningModal/WarningModal.scss", "webpack:///./src/ui/src/components/WarningModal/WarningModal.js", "webpack:///./src/ui/src/components/WarningModal/index.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "WarningModal", "doNotAskCheckboxRef", "React", "createRef", "useSelector", "state", "selectors", "getWarningTitle", "getWarningMessage", "getWarningConfirmEvent", "getWarningConfirmBtnText", "getWarningSecondaryEvent", "getWarningSecondaryBtnText", "getWarningSecondaryBtnClass", "getWarningCancelEvent", "isElementDisabled", "DataElements", "WARNING_MODAL", "isElementOpen", "getShowAskAgainCheckbox", "getWarningTemplateStrings", "getWarningModalClass", "getWarningCloseEvent", "shallowEqual", "title", "message", "onConfirm", "confirmBtnText", "onSecondary", "secondaryBtnText", "secondaryBtnClass", "onCancel", "isDisabled", "isOpen", "showAskAgainCheckbox", "templateStrings", "warningModalClass", "onClose", "useState", "disableWarning", "setDisableWarning", "dispatch", "useDispatch", "className", "getClassName", "label", "i18next", "t", "useEffect", "core", "addEventListener", "cancel", "removeEventListener", "translatedMessage", "closeModal", "actions", "closeElements", "closeWithFocusTransfer", "useFocusOnClose", "confirm", "e", "secondary", "onMouseDown", "role", "aria-modal", "aria-label", "aria-<PERSON><PERSON>", "ModalWrapper", "<PERSON><PERSON><PERSON><PERSON>", "onCloseClick", "swipeToClose", "stopPropagation", "exists", "includes", "split", "map", "str", "index", "Fragment", "key", "Choice", "dataElement", "ref", "id", "name", "onChange", "target", "checked", "center", "<PERSON><PERSON>", "classNames", "onClick"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,qtOAAstO,KAG/uO0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qqCCTvB,8lGAAA3B,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,+kBAAAA,GAAA,gEAAAA,GAAA,0JAAAA,EAAA,6FAAAA,GAAA,mIAAAA,IAAA,4SAAAA,IAAA,2OAAAA,EAAA,iBAAAA,EAAA,EAAAA,IAAA,EAAAA,GAAA,EAAAA,GAAA,SAeA,IA8Je4B,EA9JM,WACnB,IAAMC,EAAsBC,IAAMC,YAmCjC,IAlBGC,aACF,SAACC,GAAK,MAAK,CACTC,IAAUC,gBAAgBF,IAAU,GACpCC,IAAUE,kBAAkBH,GAC5BC,IAAUG,uBAAuBJ,GACjCC,IAAUI,yBAAyBL,GACnCC,IAAUK,yBAAyBN,GACnCC,IAAUM,2BAA2BP,GACrCC,IAAUO,4BAA4BR,GACtCC,IAAUQ,sBAAsBT,GAChCC,IAAUS,kBAAkBV,EAAOW,IAAaC,eAChDX,IAAUY,cAAcb,EAAOW,IAAaC,eAC5CX,IAAUa,wBAAwBd,GAClCC,IAAUc,0BAA0Bf,IAAU,GAC9CC,IAAUe,qBAAqBhB,IAAU,GACzCC,IAAUgB,qBAAqBjB,IAAU,MAE3CkB,KACD,IAhCCC,EAAK,KACLC,EAAO,KACPC,EAAS,KACTC,EAAc,KACdC,EAAW,KACXC,EAAgB,KAChBC,EAAiB,KACjBC,EAAQ,KACRC,EAAU,KACVC,EAAM,KACNC,EAAoB,MACpBC,EAAe,MACfC,EAAiB,MACjBC,EAAO,MAqBkD,IAAfC,oBAAS,GAAM,GAApDC,EAAc,KAAEC,EAAiB,KAClCC,EAAWC,cAEXC,EAAYC,YAAa,sBAAD,OAAuBR,GAAqB,CAAEH,WACtEY,EAAQC,IAAQC,EAAEpB,EAAgBQ,IAAoBW,IAAQC,EAAE,aAEtEC,qBAAU,WAGR,OAFAC,IAAKC,iBAAiB,mBAAoBC,GAEnC,WACLF,IAAKG,oBAAoB,mBAAoBD,MAE9C,IAGHH,qBAAU,WACJf,GACFO,GAAkB,KAEnB,CAACP,IAEJ,IAAoC,EAE5BoB,EAcFC,EAAU,6BAAG,kFACbrB,EAAQ,CAAF,eACD,GADC,KACRI,GAAO,qCAAUA,EAAQE,GAAe,OACxCE,EAASc,IAAQC,cAAcxC,IAAaC,gBAAgB,2CAE/D,kBALe,mCAOVwC,EAAyBC,YAAgBJ,GAEzCH,EAAM,6BAAG,8EACL,GADK,KACbpB,GAAQ,qCAAUA,IAAU,OAC5B0B,IAAyB,2CAC1B,kBAHW,mCAKNE,EAAO,6BAAG,WAAOC,GAAC,iEACb,GADa,KACtBlC,GAAS,qCAAUA,EAAUkC,GAAE,OAC/BH,IAAyB,2CAC1B,gBAHY,sCAKPI,EAAS,6BAAG,8EACL,GADK,KAChBjC,GAAW,qCAAUA,IAAa,OAClC6B,IAAyB,2CAC1B,kBAHc,mCAKf,OAAOzB,EAAa,KAClB,yBACEW,UAAWA,EACXmB,YAAaX,EACbY,KAAK,cACLC,aAAW,OACXC,aAAYnB,IAAQC,EAAEvB,EAAOW,GAC7B+B,mBAAkBpB,IAAQC,EAAEvB,EAAOW,IAEnC,kBAACgC,EAAA,EAAY,CACX3C,MAAOsB,IAAQC,EAAEvB,EAAOW,GACxBF,OAAQA,EACRmC,aAAcjB,EACdkB,aAAclB,EACdmB,cAAY,GACZ,yBAAK3B,UAAU,YAAYmB,YAAa,SAACF,GAAC,OAAKA,EAAEW,oBAC/C,yBAAK5B,UAAU,SAtDfU,EAD8C,iBAAZ5B,GAAwBqB,IAAQ0B,OAAO/C,GAC1BqB,IAAQC,EAAEtB,EAASU,GAAmBV,EAC7D,QAA9B,EAAI4B,EAAkBoB,gBAAQ,OAA1B,OAAApB,EAA6B,MACxBA,EAAkBqB,MAAM,MAAMC,KAAI,SAACC,EAAKC,GAAK,OAClD,kBAAC,IAAMC,SAAQ,CAACC,IAAKF,GACR,IAAVA,EACG,oCAAGD,GACH,oCAAE,6BAAOA,OAKZvB,IA8CD,yBAAKV,UAAU,YACf,yBAAKA,UAAU,UACZT,GACC,kBAAC8C,EAAA,EAAM,CACLC,YAAY,wBACZC,IAAKjF,EACLkF,GAAG,4BACHC,KAAK,4BACLvC,MAAOC,IAAQC,EAAE,yBACjBsC,SAAU,SAACzB,GAAC,OAAKpB,EAAkBoB,EAAE0B,OAAOC,UAC5CA,QAAShD,EACTiD,QAAM,IAIT5D,GACC,kBAAC6D,EAAA,EAAM,CACL9C,UAAW+C,IAAW,EAAD,CACnB,gBAAgB,EAChB,wBAAwB,GACvB5D,EAAoBA,IAEvBmD,YAAY,0BACZpC,MAAOC,IAAQC,EAAElB,EAAkBM,GACnCwD,QAAS9B,IAGb,kBAAC4B,EAAA,EAAM,CACL9C,UAAU,uBACVsC,YAAY,yBACZpC,MAAOA,EACP8C,QAAShC,SCnKR3D", "file": "chunks/chunk.78.js", "sourcesContent": ["var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./WarningModal.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".open.WarningModal{visibility:visible}.closed.WarningModal{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.WarningModal .footer .modal-button.confirm:hover{background:var(--primary-button-hover);border-color:var(--primary-button-hover);color:var(--gray-0)}.WarningModal .footer .modal-button.confirm{background:var(--primary-button);border-color:var(--primary-button);color:var(--primary-button-text)}.WarningModal .footer .modal-button.confirm.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.WarningModal .footer .modal-button.confirm.disabled span{color:var(--primary-button-text)}.WarningModal .footer .modal-button.cancel:hover,.WarningModal .footer .modal-button.second-option-button:hover,.WarningModal .footer .modal-button.secondary-btn-custom:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6)}.WarningModal .footer .modal-button.cancel,.WarningModal .footer .modal-button.second-option-button,.WarningModal .footer .modal-button.secondary-btn-custom{border:none;box-shadow:inset 0 0 0 1px var(--primary-button);color:var(--primary-button)}.WarningModal .footer .modal-button.cancel.disabled,.WarningModal .footer .modal-button.secondary-btn-custom.disabled{cursor:default;border:none;box-shadow:inset 0 0 0 1px rgba(43,115,171,.5);color:rgba(43,115,171,.5)}.WarningModal .footer .modal-button.cancel.disabled span,.WarningModal .footer .modal-button.secondary-btn-custom.disabled span{color:rgba(43,115,171,.5)}.WarningModal{position:fixed;left:0;bottom:0;z-index:100;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background:var(--modal-negative-space)}.WarningModal .modal-container .wrapper .modal-content{padding:10px}.WarningModal .footer{display:flex;flex-direction:row;justify-content:flex-end;width:100%;margin-top:13px}.WarningModal .footer.modal-footer{padding:16px;margin:0;border-top:1px solid var(--divider)}.WarningModal .footer .modal-button{display:flex;justify-content:center;align-items:center;padding:6px 18px;margin:8px 0 0;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;height:30px;cursor:pointer}.WarningModal .footer .modal-button.confirm{margin-left:4px}.WarningModal .footer .modal-button.secondary-btn-custom{border-radius:4px;padding:2px 20px 4px;cursor:pointer}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .WarningModal .footer .modal-button{padding:23px 8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .WarningModal .footer .modal-button{padding:23px 8px}}.WarningModal .swipe-indicator{background:var(--swipe-indicator-bg);border-radius:2px;height:4px;width:38px;position:absolute;top:12px;margin-left:auto;margin-right:auto;left:0;right:0}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .WarningModal .swipe-indicator{display:none}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .WarningModal .swipe-indicator{display:none}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .WarningModal .swipe-indicator{width:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .WarningModal .swipe-indicator{width:32px}}.WarningModal{z-index:101}.WarningModal .container{display:flex;width:480px;flex-direction:column;align-items:flex-start;border-radius:4px;background:var(--component-background)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .WarningModal .container{width:100%}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .WarningModal .container{width:100%}}.WarningModal.connect-to-url-modal{overflow:hidden;word-break:break-all}.WarningModal .header{display:flex;align-items:center;position:relative;width:100%;padding:16px}.WarningModal .header .header-text span{font-size:16px;font-weight:700}.WarningModal .header .Button{position:absolute;top:10px;right:10px}.WarningModal .header .Button .Icon{height:22px;width:22px;color:var(--icon-color)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .WarningModal .header .Button .Icon{height:28px;width:28px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .WarningModal .header .Button .Icon{height:28px;width:28px}}.WarningModal .header .Button:hover{background:var(--view-header-button-hover);border-radius:4px}.WarningModal .body{font-size:13px;padding:16px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .WarningModal .body{padding:16px 32px 16px 16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .WarningModal .body{padding:16px 32px 16px 16px}}.WarningModal .body .customMessage p:first-child{margin-top:0}.WarningModal .body .customMessage p:last-child{margin-bottom:0}.WarningModal .body .footer{justify-content:flex-start}.WarningModal .footer{padding:16px;margin-top:0}.WarningModal .footer .ui__choice--center{flex:auto;align-self:center}.WarningModal .footer .modal-button{height:32px;margin:0}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .WarningModal .footer .modal-button{padding:0;border:none;background-color:transparent;background:var(--primary-button);border-radius:4px;height:32px;width:52px;display:flex;align-items:center;justify-content:center;position:relative;color:var(--primary-button-text);cursor:pointer;font-size:13px}:host(:not([data-tabbing=true])) .App:not(.is-in-desktop-only-mode):not(.is-web-component) .WarningModal .footer .modal-button,html:not([data-tabbing=true]) .App:not(.is-in-desktop-only-mode):not(.is-web-component) .WarningModal .footer .modal-button{outline:none}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .WarningModal .footer .modal-button{padding:0;border:none;background-color:transparent;background:var(--primary-button);border-radius:4px;height:32px;width:52px;display:flex;align-items:center;justify-content:center;position:relative;color:var(--primary-button-text);cursor:pointer;font-size:13px}:host(:not([data-tabbing=true])) .App.is-web-component:not(.is-in-desktop-only-mode) .WarningModal .footer .modal-button,html:not([data-tabbing=true]) .App.is-web-component:not(.is-in-desktop-only-mode) .WarningModal .footer .modal-button{outline:none}}.WarningModal .divider{height:1px;width:100%;background:var(--divider)}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React, { useEffect, useState } from 'react';\nimport { useSelector, shallowEqual, useDispatch } from 'react-redux';\nimport i18next from 'i18next';\nimport core from 'core';\nimport Button from 'components/Button';\nimport Choice from 'components/Choice/Choice';\nimport getClassName from 'helpers/getClassName';\nimport classNames from 'classnames';\nimport actions from 'actions';\nimport selectors from 'selectors';\nimport DataElements from 'constants/dataElement';\nimport ModalWrapper from 'components/ModalWrapper';\nimport useFocusOnClose from 'hooks/useFocusOnClose';\n\nimport './WarningModal.scss';\n\nconst WarningModal = () => {\n  const doNotAskCheckboxRef = React.createRef();\n\n  const [\n    title,\n    message,\n    onConfirm,\n    confirmBtnText,\n    onSecondary,\n    secondaryBtnText,\n    secondaryBtnClass,\n    onCancel,\n    isDisabled,\n    isOpen,\n    showAskAgainCheckbox,\n    templateStrings,\n    warningModalClass,\n    onClose,\n  ] = useSelector(\n    (state) => [\n      selectors.getWarningTitle(state) || '',\n      selectors.getWarningMessage(state),\n      selectors.getWarningConfirmEvent(state),\n      selectors.getWarningConfirmBtnText(state),\n      selectors.getWarningSecondaryEvent(state),\n      selectors.getWarningSecondaryBtnText(state),\n      selectors.getWarningSecondaryBtnClass(state),\n      selectors.getWarningCancelEvent(state),\n      selectors.isElementDisabled(state, DataElements.WARNING_MODAL),\n      selectors.isElementOpen(state, DataElements.WARNING_MODAL),\n      selectors.getShowAskAgainCheckbox(state),\n      selectors.getWarningTemplateStrings(state) || {},\n      selectors.getWarningModalClass(state) || '',\n      selectors.getWarningCloseEvent(state) || '',\n    ],\n    shallowEqual,\n  );\n\n  const [disableWarning, setDisableWarning] = useState(false);\n  const dispatch = useDispatch();\n\n  const className = getClassName(`Modal WarningModal ${warningModalClass}`, { isOpen });\n  const label = i18next.t(confirmBtnText, templateStrings) || i18next.t('action.ok');\n\n  useEffect(() => {\n    core.addEventListener('documentUnloaded', cancel);\n\n    return () => {\n      core.removeEventListener('documentUnloaded', cancel);\n    };\n  }, []);\n\n  // Reset disableWarning state\n  useEffect(() => {\n    if (isOpen) {\n      setDisableWarning(false);\n    }\n  }, [isOpen]);\n\n  const getMessageWithNewLine = () => {\n    const messageIsAValidStringKey = typeof message === 'string' && i18next.exists(message);\n    const translatedMessage = messageIsAValidStringKey ? i18next.t(message, templateStrings) : message;\n    if (translatedMessage.includes?.('\\n')) {\n      return translatedMessage.split('\\n').map((str, index) => (\n        <React.Fragment key={index}>\n          {index === 0\n            ? <>{str}</>\n            : <><br />{str}</>\n          }\n        </React.Fragment>\n      ));\n    }\n    return translatedMessage;\n  };\n\n  const closeModal = async () => {\n    if (isOpen) {\n      onClose && await onClose(disableWarning);\n      dispatch(actions.closeElements(DataElements.WARNING_MODAL));\n    }\n  };\n\n  const closeWithFocusTransfer = useFocusOnClose(closeModal);\n\n  const cancel = async () => {\n    onCancel && await onCancel();\n    closeWithFocusTransfer();\n  };\n\n  const confirm = async (e) => {\n    onConfirm && await onConfirm(e);\n    closeWithFocusTransfer();\n  };\n\n  const secondary = async () => {\n    onSecondary && await onSecondary();\n    closeWithFocusTransfer();\n  };\n\n  return isDisabled ? null : (\n    <div\n      className={className}\n      onMouseDown={cancel}\n      role=\"alertdialog\"\n      aria-modal=\"true\"\n      aria-label={i18next.t(title, templateStrings)}\n      aria-describedby={i18next.t(title, templateStrings)}\n    >\n      <ModalWrapper\n        title={i18next.t(title, templateStrings)}\n        isOpen={isOpen}\n        closeHandler={cancel}\n        onCloseClick={cancel}\n        swipeToClose>\n        <div className=\"container\" onMouseDown={(e) => e.stopPropagation()}>\n          <div className=\"body\">\n            {getMessageWithNewLine()}\n          </div>\n          <div className=\"divider\" />\n          <div className=\"footer\">\n            {showAskAgainCheckbox && (\n              <Choice\n                dataElement=\"doNotAskAgainCheckbox\"\n                ref={doNotAskCheckboxRef}\n                id=\"do-not-ask-again-checkbox\"\n                name=\"do-not-ask-again-checkbox\"\n                label={i18next.t('message.doNotAskAgain')}\n                onChange={(e) => setDisableWarning(e.target.checked)}\n                checked={disableWarning}\n                center\n              />\n\n            )}\n            {onSecondary && (\n              <Button\n                className={classNames({\n                  'modal-button': true,\n                  'second-option-button': true,\n                  [secondaryBtnClass]: secondaryBtnClass,\n                })}\n                dataElement=\"WarningModalClearButton\"\n                label={i18next.t(secondaryBtnText, templateStrings)}\n                onClick={secondary}\n              />\n            )}\n            <Button\n              className=\"confirm modal-button\"\n              dataElement=\"WarningModalSignButton\"\n              label={label}\n              onClick={confirm}\n            />\n          </div>\n        </div>\n      </ModalWrapper>\n    </div>\n  );\n};\n\nexport default WarningModal;", "import WarningModal from './WarningModal';\n\nexport default WarningModal;"], "sourceRoot": ""}