{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/tzl.js"], "names": ["module", "exports", "e", "t", "default", "_", "a", "name", "weekdays", "split", "months", "weekStart", "weekdaysShort", "monthsShort", "weekdaysMin", "ordinal", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "locale"], "mappings": "gFAAoEA,EAAOC,QAA8K,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAGI,EAAE,CAACC,KAAK,MAAMC,SAAS,sDAAsDC,MAAM,KAAKC,OAAO,sFAAsFD,MAAM,KAAKE,UAAU,EAAEC,cAAc,8BAA8BH,MAAM,KAAKI,YAAY,kDAAkDJ,MAAM,KAAKK,YAAY,uBAAuBL,MAAM,KAAKM,QAAQ,SAASb,GAAG,OAAOA,GAAGc,QAAQ,CAACC,GAAG,QAAQC,IAAI,WAAWC,EAAE,aAAaC,GAAG,wBAAwBC,IAAI,8BAA8BC,KAAK,2CAA2C,OAAOnB,EAAEC,QAAQmB,OAAOjB,EAAE,MAAK,GAAIA,EAA72BD,CAAE,EAAQ", "file": "chunks/chunk.228.js", "sourcesContent": ["!function(e,_){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=_(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],_):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_tzl=_(e.dayjs)}(this,(function(e){\"use strict\";function _(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var t=_(e),a={name:\"tzl\",weekdays:\"Súladi_Lúneçi_Maitzi_Márcuri_Xhúadi_Viénerçi_Sáturi\".split(\"_\"),months:\"Januar_Fevraglh_Març_Avrïu_Mai_Gün_Julia_Guscht_Setemvar_Listopäts_Noemvar_Zecemvar\".split(\"_\"),weekStart:1,weekdaysShort:\"<PERSON><PERSON>_<PERSON>ún_<PERSON>_Már_Xhú_Vié_<PERSON>t\".split(\"_\"),monthsShort:\"Jan_Fev_Mar_Avr_Mai_Gün_Jul_Gus_Set_Lis_Noe_Zec\".split(\"_\"),weekdaysMin:\"Sú_Lú_Ma_Má_Xh_Vi_Sá\".split(\"_\"),ordinal:function(e){return e},formats:{LT:\"HH.mm\",LTS:\"HH.mm.ss\",L:\"DD.MM.YYYY\",LL:\"D. MMMM [dallas] YYYY\",LLL:\"D. MMMM [dallas] YYYY HH.mm\",LLLL:\"dddd, [li] D. MMMM [dallas] YYYY HH.mm\"}};return t.default.locale(a,null,!0),a}));"], "sourceRoot": ""}