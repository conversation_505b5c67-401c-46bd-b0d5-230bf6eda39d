{"version": 3, "sources": ["webpack:///./src/ui/src/components/MenuOverlay/MenuOverlay.scss?7a49", "webpack:///./src/ui/src/components/MenuOverlay/MenuOverlay.scss", "webpack:///./src/ui/src/components/MenuOverlay/MenuOverlay.js", "webpack:///./src/ui/src/components/MenuOverlay/index.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "InitialMenuOverLayItem", "dataElement", "children", "items", "useSelector", "state", "selectors", "getMenuOverlayItems", "shallowEqual", "childrenA<PERSON>y", "React", "Children", "toArray", "map", "item", "type", "hidden", "key", "mediaQueryClassName", "screen", "join", "component", "find", "child", "props", "ActionButton", "CustomElement", "cloneElement", "MenuOverlay", "dispatch", "useDispatch", "t", "useTranslation", "useState", "documentType", "setDocumentType", "isEmbedPrintSupported", "useClientSidePrint", "colorMap", "getColorMap", "sortStrategy", "getSortStrategy", "isFullScreen", "timezone", "getTimezone", "isCreatePortfolioButtonEnabled", "isElementDisabled", "DataElements", "CREATE_PORTFOLIO_BUTTON", "core", "isFullPDFEnabled", "closeMenuOverlay", "useCallback", "actions", "closeElements", "MENU_OVERLAY", "useEffect", "onDocumentLoaded", "getDocument", "getType", "addEventListener", "removeEventListener", "handleNewDocumentClick", "loadDocument", "filename", "enableOfficeEditing", "FlyoutMenu", "menu", "trigger", "MENU_OVERLAY_BUTTON", "aria<PERSON><PERSON><PERSON>", "isOfficeEditorMode", "NEW_DOCUMENT_BUTTON", "className", "img", "label", "role", "onClick", "FILE_PICKER_BUTTON", "openFilePicker", "workerTypes", "XOD", "DOWNLOAD_BUTTON", "downloadPdf", "FULLSCREEN_BUTTON", "toggleFullscreen", "SAVE_AS_BUTTON", "openElement", "SAVE_MODAL", "PRINT_BUTTON", "print", "isGrayscale", "getDocumentViewer", "isGrayscaleModeEnabled", "CREATE_PORTFOLIO_MODAL", "SETTINGS_BUTTON", "SETTINGS_MODAL"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,stCAAutC,KAGhvC0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,oyBCTvB,8lGAAA3B,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4YAAAA,GAAA,gEAAAA,GAAA,0JAAAA,EAAA,6FAAAA,GAAA,mIAAAA,IAAA,4SAAAA,IAAA,2OAAAA,EAAA,iBAAAA,EAAA,EAAAA,IAAA,EAAAA,GAAA,EAAAA,GAAA,6PAAAA,EAAA,EAAAA,EAAA,iBAAAA,IAAA,uBAAAA,GAAA,UAAAA,GAAA,GAAAA,EAAA,msBAmBA,IAAM4B,EAAyB,SAAH,GAAkC,IAA5BC,EAAW,EAAXA,YAAaC,EAAQ,EAARA,SACvCC,EAAQC,aAAY,SAACC,GAAK,OAAKC,IAAUC,oBAAoBF,EAAOJ,KAAcO,KAElFC,EAAgBC,IAAMC,SAASC,QAAQV,GAE7C,OAAOC,EAAMU,KAAI,SAACC,EAAM1C,GACtB,IAAQ6B,EAA8Ba,EAA9Bb,YAAac,EAAiBD,EAAjBC,KAAMC,EAAWF,EAAXE,OACrBC,EAAM,GAAH,OAAMF,EAAI,YAAId,GAAe7B,GAChC8C,EAAsBF,aAAM,EAANA,EAAQH,KAAI,SAACM,GAAM,wBAAgBA,MAAUC,KAAK,KAC1EC,EAAYZ,EAAca,MAAK,SAACC,GAAK,OAAKA,EAAMC,MAAMvB,cAAgBA,KAE1E,IAAKoB,EAAW,CACd,IAAMG,EAAQ,EAAH,KAAQV,GAAI,IAAEI,wBAEzB,OAAQH,GACN,IAAK,eACHM,EAAY,kBAACI,EAAA,EAAiBD,GAC9B,MACF,IAAK,gBACHH,EAAY,kBAACK,EAAA,EAAkBF,IAKrC,OAAOH,EACHX,IAAMiB,aAAaN,EAAW,CAC9BJ,QAEA,SAkKOW,MA9Jf,WACE,IAAMC,EAAWC,cACVC,EAAqB,EAAhBC,cAAgB,GAApB,GAE0C,IAAVC,qBAAU,GAA3CC,EAAY,KAAEC,EAAe,KAE9BC,EAAwBhC,YAAYE,IAAU8B,uBAC9CC,EAAqBjC,YAAYE,IAAU+B,oBAC3CC,EAAWlC,YAAYE,IAAUiC,aACjCC,EAAepC,YAAYE,IAAUmC,iBACrCC,EAAetC,aAAY,SAACC,GAAK,OAAKC,IAAUoC,aAAarC,MAC7DsC,EAAWvC,aAAY,SAACC,GAAK,OAAKC,IAAUsC,YAAYvC,MACxDwC,GAAkCzC,aAAY,SAACC,GAAK,OAAKC,IAAUwC,kBAAkBzC,EAAO0C,IAAaC,6BAA6BC,IAAKC,mBAE3IC,EAAmBC,uBAAY,kBAAMvB,EAASwB,IAAQC,cAAc,CAACP,IAAaQ,kBAAiB,CAAC1B,IAE1G2B,qBAAU,WACR,IAAMC,EAAmB,WAAM,MACvB1C,EAAyB,QAArB,EAAGkC,IAAKS,qBAAa,aAAlB,EAAoBC,UACjCxB,EAAgBpB,IAIlB,OAFA0C,IACAR,IAAKW,iBAAiB,iBAAkBH,GACjC,WACLR,IAAKY,oBAAoB,iBAAkBJ,MAE5C,IAEH,IAwBMK,EAAsB,eAvG9B,EAuG8B,GAvG9B,EAuG8B,UAAG,8EAC7BX,IACAY,YAAalC,EAAU,KAAM,CAC3BmC,SAAU,gBACVC,qBAAqB,IACpB,0CA5GP,+KA6GG,kBAN2B,mCAQ5B,OACE,kBAACC,EAAA,EAAU,CACTC,KAAMpB,IAAaQ,aACnBa,QAASrB,IAAasB,oBACtBC,UAAWvC,EAAE,0BAEb,kBAAC,EAAsB,KACpBwC,eACC,kBAAC9C,EAAA,EAAY,CACXxB,YAAa8C,IAAayB,oBAC1BC,UAAU,MACVC,IAAI,iBACJC,MAAO5C,EAAE,sBACTuC,UAAWvC,EAAE,sBACb6C,KAAK,SACLC,QAASf,IAGb,kBAACrC,EAAA,EAAY,CACXxB,YAAa8C,IAAa+B,mBAC1BL,UAAU,MACVC,IAAI,+BACJC,MAAO5C,EAAE,mBACTuC,UAAWvC,EAAE,mBACb6C,KAAK,SACLC,QAASE,MAEV7C,IAAiB8C,IAAYC,MAAQV,eACpC,kBAAC9C,EAAA,EAAY,CACXxB,YAAa8C,IAAamC,gBAC1BT,UAAU,MACVC,IAAI,gBACJC,MAAO5C,EAAE,mBACTuC,UAAWvC,EAAE,mBACb6C,KAAK,SACLC,QA9De,WACvBM,YAAYtD,MAgEP0C,eACC,kBAAC9C,EAAA,EAAY,CACXxB,YAAa8C,IAAaqC,kBAC1BX,UAAU,MACVC,IAAKhC,EAAe,+BAAiC,0BACrDiC,MAAsB5C,EAAfW,EAAiB,wBAA6B,0BACrD4B,UAA0BvC,EAAfW,EAAiB,wBAA6B,0BACzDkC,KAAK,SACLC,QAASQ,MAGZnD,IAAiB8C,IAAYC,KAC5B,kBAACxD,EAAA,EAAY,CACXxB,YAAa8C,IAAauC,eAC1Bb,UAAU,MACVC,IAAI,YACJC,MAAO5C,EAAE,oBACTuC,UAAWvC,EAAE,oBACb6C,KAAK,SACLC,QAhFY,WACpB1B,IACAtB,EAASwB,IAAQkC,YAAYxC,IAAayC,gBAiFtC,kBAAC/D,EAAA,EAAY,CACXxB,YAAa8C,IAAa0C,aAC1BhB,UAAU,MACVC,IAAI,yBACJC,MAAO5C,EAAE,gBACTuC,UAAWvC,EAAE,gBACb6C,KAAK,SACLC,QAnGuB,WAC7B1B,IACAuC,YAAM7D,EAAUQ,EAAoBD,EAAuBI,EAAcF,EAAU,CAAEqD,YAAa1C,IAAK2C,oBAAoBC,yBAA0BlD,iBAoGnJ,yBAAK8B,UAAU,YACd5B,GACC,oCACE,kBAACpB,EAAA,EAAY,CACXxB,YAAa8C,IAAaC,wBAC1ByB,UAAU,MACVC,IAAI,qBACJC,MAAO5C,EAAE,gCACTuC,UAAWvC,EAAE,gCACb6C,KAAK,SACLC,QA7FyB,WACjC1B,IACAtB,EAASwB,IAAQkC,YAAYxC,IAAa+C,4BA6FpC,yBAAKrB,UAAU,aAGnB,kBAAChD,EAAA,EAAY,CACXxB,YAAa8C,IAAagD,gBAC1BtB,UAAU,MACVC,IAAI,4BACJC,MAAO5C,EAAE,4BACTuC,UAAWvC,EAAE,4BACb6C,KAAK,SACLC,QA9G4B,WAChC1B,IACAtB,EAASwB,IAAQkC,YAAYxC,IAAaiD,sBC9F/BpE", "file": "chunks/chunk.68.js", "sourcesContent": ["var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./MenuOverlay.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.FlyoutMenu[data-element=menuOverlay] .ActionButton{justify-content:flex-start}.FlyoutMenu[data-element=menuOverlay] .ActionButton .Icon{margin:4px}.FlyoutMenu[data-element=menuOverlay] .ActionButton span{margin:0 4px}.FlyoutMenu[data-element=menuOverlay] .ActionButton.row.disabled:hover{background:none;cursor:default}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import actions from 'actions';\nimport ActionButton from 'components/ActionButton';\nimport CustomElement from 'components/CustomElement';\nimport { workerTypes } from 'constants/types';\nimport core from 'core';\nimport downloadPdf from 'helpers/downloadPdf';\nimport openFilePicker from 'helpers/openFilePicker';\nimport toggleFullscreen from 'helpers/toggleFullscreen';\nimport { print } from 'helpers/print';\nimport React, { useCallback, useEffect, useState } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { useDispatch, useSelector, shallowEqual } from 'react-redux';\nimport selectors from 'selectors';\nimport FlyoutMenu from '../FlyoutMenu/FlyoutMenu';\nimport DataElements from 'constants/dataElement';\nimport loadDocument from 'helpers/loadDocument';\nimport { isOfficeEditorMode } from 'helpers/officeEditor';\n\nimport './MenuOverlay.scss';\n\nconst InitialMenuOverLayItem = ({ dataElement, children }) => {\n  const items = useSelector((state) => selectors.getMenuOverlayItems(state, dataElement), shallowEqual);\n\n  const childrenArray = React.Children.toArray(children);\n\n  return items.map((item, i) => {\n    const { dataElement, type, hidden } = item;\n    const key = `${type}-${dataElement || i}`;\n    const mediaQueryClassName = hidden?.map((screen) => `hide-in-${screen}`).join(' ');\n    let component = childrenArray.find((child) => child.props.dataElement === dataElement);\n\n    if (!component) {\n      const props = { ...item, mediaQueryClassName };\n\n      switch (type) {\n        case 'actionButton':\n          component = <ActionButton {...props} />;\n          break;\n        case 'customElement':\n          component = <CustomElement {...props} />;\n          break;\n      }\n    }\n\n    return component\n      ? React.cloneElement(component, {\n        key,\n      })\n      : null;\n  });\n};\n\nfunction MenuOverlay() {\n  const dispatch = useDispatch();\n  const [t] = useTranslation();\n\n  const [documentType, setDocumentType] = useState();\n\n  const isEmbedPrintSupported = useSelector(selectors.isEmbedPrintSupported);\n  const useClientSidePrint = useSelector(selectors.useClientSidePrint);\n  const colorMap = useSelector(selectors.getColorMap);\n  const sortStrategy = useSelector(selectors.getSortStrategy);\n  const isFullScreen = useSelector((state) => selectors.isFullScreen(state));\n  const timezone = useSelector((state) => selectors.getTimezone(state));\n  const isCreatePortfolioButtonEnabled = !useSelector((state) => selectors.isElementDisabled(state, DataElements.CREATE_PORTFOLIO_BUTTON)) && core.isFullPDFEnabled();\n\n  const closeMenuOverlay = useCallback(() => dispatch(actions.closeElements([DataElements.MENU_OVERLAY])), [dispatch]);\n\n  useEffect(() => {\n    const onDocumentLoaded = () => {\n      const type = core.getDocument()?.getType();\n      setDocumentType(type);\n    };\n    onDocumentLoaded();\n    core.addEventListener('documentLoaded', onDocumentLoaded);\n    return () => {\n      core.removeEventListener('documentLoaded', onDocumentLoaded);\n    };\n  }, []);\n\n  const handlePrintButtonClick = () => {\n    closeMenuOverlay();\n    print(dispatch, useClientSidePrint, isEmbedPrintSupported, sortStrategy, colorMap, { isGrayscale: core.getDocumentViewer().isGrayscaleModeEnabled(), timezone });\n  };\n\n  const downloadDocument = () => {\n    downloadPdf(dispatch);\n  };\n\n  const openSaveModal = () => {\n    closeMenuOverlay();\n    dispatch(actions.openElement(DataElements.SAVE_MODAL));\n  };\n\n  const handleSettingsButtonClick = () => {\n    closeMenuOverlay();\n    dispatch(actions.openElement(DataElements.SETTINGS_MODAL));\n  };\n\n  const handlePortfolioButtonClick = () => {\n    closeMenuOverlay();\n    dispatch(actions.openElement(DataElements.CREATE_PORTFOLIO_MODAL));\n  };\n\n  const handleNewDocumentClick = async () => {\n    closeMenuOverlay();\n    loadDocument(dispatch, null, {\n      filename: 'Untitled.docx',\n      enableOfficeEditing: true\n    });\n  };\n\n  return (\n    <FlyoutMenu\n      menu={DataElements.MENU_OVERLAY}\n      trigger={DataElements.MENU_OVERLAY_BUTTON}\n      ariaLabel={t('component.menuOverlay')}\n    >\n      <InitialMenuOverLayItem>\n        {isOfficeEditorMode() && (\n          <ActionButton\n            dataElement={DataElements.NEW_DOCUMENT_BUTTON}\n            className=\"row\"\n            img=\"icon-plus-sign\"\n            label={t('action.newDocument')}\n            ariaLabel={t('action.newDocument')}\n            role=\"option\"\n            onClick={handleNewDocumentClick}\n          />\n        )}\n        <ActionButton\n          dataElement={DataElements.FILE_PICKER_BUTTON}\n          className=\"row\"\n          img=\"icon-header-file-picker-line\"\n          label={t('action.openFile')}\n          ariaLabel={t('action.openFile')}\n          role=\"option\"\n          onClick={openFilePicker}\n        />\n        {documentType !== workerTypes.XOD && !isOfficeEditorMode() && (\n          <ActionButton\n            dataElement={DataElements.DOWNLOAD_BUTTON}\n            className=\"row\"\n            img=\"icon-download\"\n            label={t('action.download')}\n            ariaLabel={t('action.download')}\n            role=\"option\"\n            onClick={downloadDocument}\n          />\n        )}\n        {isOfficeEditorMode() && (\n          <ActionButton\n            dataElement={DataElements.FULLSCREEN_BUTTON}\n            className=\"row\"\n            img={isFullScreen ? 'icon-header-full-screen-exit' : 'icon-header-full-screen'}\n            label={isFullScreen ? t('action.exitFullscreen') : t('action.enterFullscreen')}\n            ariaLabel={isFullScreen ? t('action.exitFullscreen') : t('action.enterFullscreen')}\n            role=\"option\"\n            onClick={toggleFullscreen}\n          />\n        )}\n        {documentType !== workerTypes.XOD && (\n          <ActionButton\n            dataElement={DataElements.SAVE_AS_BUTTON}\n            className=\"row\"\n            img=\"icon-save\"\n            label={t('saveModal.saveAs')}\n            ariaLabel={t('saveModal.saveAs')}\n            role=\"option\"\n            onClick={openSaveModal}\n          />\n        )}\n        <ActionButton\n          dataElement={DataElements.PRINT_BUTTON}\n          className=\"row\"\n          img=\"icon-header-print-line\"\n          label={t('action.print')}\n          ariaLabel={t('action.print')}\n          role=\"option\"\n          onClick={handlePrintButtonClick}\n        />\n      </InitialMenuOverLayItem>\n      <div className=\"divider\"></div>\n      {isCreatePortfolioButtonEnabled && (\n        <>\n          <ActionButton\n            dataElement={DataElements.CREATE_PORTFOLIO_BUTTON}\n            className=\"row\"\n            img=\"icon-pdf-portfolio\"\n            label={t('portfolio.createPDFPortfolio')}\n            ariaLabel={t('portfolio.createPDFPortfolio')}\n            role=\"option\"\n            onClick={handlePortfolioButtonClick}\n          />\n          <div className=\"divider\"></div>\n        </>\n      )}\n      <ActionButton\n        dataElement={DataElements.SETTINGS_BUTTON}\n        className=\"row\"\n        img=\"icon-header-settings-line\"\n        label={t('option.settings.settings')}\n        ariaLabel={t('option.settings.settings')}\n        role=\"option\"\n        onClick={handleSettingsButtonClick}\n      />\n    </FlyoutMenu>\n  );\n}\n\nexport default MenuOverlay;\n", "import MenuOverlay from './MenuOverlay';\n\nexport default MenuOverlay;"], "sourceRoot": ""}