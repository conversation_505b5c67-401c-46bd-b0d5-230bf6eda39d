{"version": 3, "sources": ["webpack:///./src/ui/src/components/ThumbnailCard/ThumbnailCard.js", "webpack:///./src/ui/src/components/ThumbnailCard/index.js", "webpack:///./src/ui/src/components/PageThumbnailsGrid/PageThumbnailsGrid.js", "webpack:///./src/ui/src/components/PageThumbnailsGrid/index.js", "webpack:///./src/ui/src/components/ThumbnailCard/ThumbnailCard.scss?1314", "webpack:///./src/ui/src/components/ThumbnailCard/ThumbnailCard.scss"], "names": ["propTypes", "onChange", "PropTypes", "func", "checked", "bool", "index", "number", "thumbnail", "object", "ThumbnailCard", "src", "img", "t", "useTranslation", "altText", "pageNumber", "currentSrc", "url", "toDataURL", "className", "alt", "onClick", "Choice", "id", "name", "i", "PageThumbnailsGrid", "document", "onThumbnailSelected", "selectedThumbnails", "onfileLoa<PERSON><PERSON><PERSON><PERSON>", "useState", "thumbnails", "setThumbnails", "completedPages", "setCompletedPagesCount", "useEffect", "inFlightPromiseIds", "pageCount", "getPageCount", "thumbnailPromises", "thumbnail<PERSON>romise", "Promise", "resolve", "promiseId", "loadThumbnail", "result", "push", "all", "sortedThumbnails", "sort", "a", "b", "length", "generateThumbnails", "for<PERSON>ach", "cancelLoadThumbnail", "map", "key", "processText", "api", "content", "__esModule", "default", "module", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "el", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals"], "mappings": "6TAMMA,EAAY,CAChBC,SAAUC,IAAUC,KACpBC,QAASF,IAAUG,KACnBC,MAAOJ,IAAUK,OACjBC,UAAWN,IAAUO,QAGjBC,EAAgB,SAAH,GAAgD,IAM3DC,EANiBV,EAAQ,EAARA,SAAUG,EAAO,EAAPA,QAASE,EAAK,EAALA,MAAOE,EAAS,EAATA,UAC7CI,EAAM,KACFC,EAAMC,cAAND,EACFE,EAAU,GAAH,OAAMF,EAAE,6BAA4B,YAAIL,EAAUQ,WAAU,KAErER,IAEEA,EAAUS,WACZN,EAAMH,EAAUS,WACPT,EAAUU,IACnBP,EAAMH,EAAUU,IACPV,EAAUW,YACnBR,EAAMH,EAAUW,aAGlBP,EAAM,yBAAKQ,UAAU,iBAAiBC,IAAKN,EAASJ,IAAKA,KAG3D,OACE,yBAAKS,UAAU,aAAaE,QAASrB,GACnC,yBAAKmB,UAAU,cACb,yBAAKA,UAAU,eACZR,GAEH,kBAACW,EAAA,EAAM,CACLC,GAAE,0BAAqBlB,GACvBc,UAAU,iBACVK,KAAI,eAAUnB,GACdF,QAASA,KAGb,yBAAKgB,UAAU,oBAAoBd,EAAQ,KAKjDI,EAAcV,UAAYA,EAEXU,ICjDAA,EDiDAA,E,sYElDf,8lGAAAgB,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,+kBAAAA,GAAA,gEAAAA,GAAA,0JAAAA,EAAA,6FAAAA,GAAA,mIAAAA,IAAA,4SAAAA,IAAA,2OAAAA,EAAA,iBAAAA,EAAA,EAAAA,IAAA,EAAAA,GAAA,EAAAA,GAAA,SA2EeC,MAxEf,SAA4B,GAA4E,IAA1EC,EAAQ,EAARA,SAAUC,EAAmB,EAAnBA,oBAAqBC,EAAkB,EAAlBA,mBAAoBC,EAAmB,EAAnBA,oBAC/B,IAAZC,mBAAS,IAAG,GAAzCC,EAAU,KAAEC,EAAa,KAC4B,IAAXF,mBAAS,GAAE,GAArDG,EAAc,KAAEC,EAAsB,KACtCvB,EAAqB,EAAhBC,cAAgB,GAApB,GAkDR,GAhDAuB,qBAAU,WACR,IAAMC,EAAqB,GACM,aAmChC,OAnCgC,cAAjC,8FACQL,EAAa,GACbM,EAAYX,EAASY,eACrBC,EAAoB,GAAE,iGAEpBC,EAAmB,IAAIC,SAAQ,SAACC,GACpC,IAAMC,EAAYjB,EAASkB,cAAcpB,GAAG,SAACqB,GAC3C,IAAIvC,EAIFA,EADEuC,EAAO9B,WACG,CACVD,WAAYU,EACZT,WAAY8B,EAAO9B,YAGT,CACVD,WAAYU,EACZR,IAAK6B,EAAO5B,aAGhBc,EAAWe,KAAKxC,GAChB4B,EAAuBV,GACvBkB,OAEFN,EAAmBU,KAAKH,MAE1BJ,EAAkBO,KAAKN,GAAkB,0CAvBlChB,EAAI,EAAC,YAAEA,GAAKa,GAAS,4DAAEb,IAAG,wCA0B7BiB,QAAQM,IAAIR,GAAkB,QAC9BS,EAAmB,UAAIjB,GAAYkB,MAAK,SAACC,EAAGC,GAAC,OAAKD,EAAEpC,WAAaqC,EAAErC,cACzEkB,EAAcgB,GACdZ,EAAmBgB,OAAS,EAC5BvB,GAAoB,GAAO,6CAC5B,sBAMD,OAJIH,GAtC0B,WACG,wBAsC/B2B,GAGK,WACLjB,EAAmBkB,SAAQ,SAAChC,GAAE,OAAKI,EAAS6B,oBAAoBjC,SAEjE,CAACI,IAEAK,EAAWqB,OAAS,EACtB,OAAOrB,EAAWyB,KAAI,SAAClD,EAAWF,GAChC,IAAMU,EAAaV,EAAQ,EAC3B,OACE,kBAAC,EAAa,CAACqD,IAAK3C,EAClBf,SAAU,kBAAM4B,EAAoBb,IACpCZ,UAAW0B,EAAmBd,GAC9BV,MAAOA,EACPE,UAAWA,OAMnB,IAAM+B,EAAYX,EAAWA,EAASY,eAAiB,EACjDoB,EAAc,GAAH,OAAMzB,EAAc,YAAII,GACzC,OAAQ,6BAAM1B,EAAE,sBAAsB,IAAE+C,ICvE3BjC,O,qBCFf,IAAIkC,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOvC,EAAIoC,EAAS,MAG9C,IAAII,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAzC,SAAS0C,KAAKC,YAAYJ,GAI5B,IAAIK,EAEJA,EAAgB5C,SAAS6C,qBAAqB,oBAEzCD,EAAclB,SACjBkB,EAzBF,SAASE,EAAwBC,EAASC,EAAOhD,UAC/C,MAAMiD,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASnB,QAAQuB,GAAMF,EAAS7B,KAAK+B,IAG3DH,EAAKE,iBAAiB,KAAKtB,QAAQuB,IAC7BA,EAAGC,YACLH,EAAS7B,QAAQ0B,EAAwBC,EAASI,EAAGC,eAIlDH,EAYSH,CAAwB,qBAG1C,MAAMO,EAAkB,GACxB,IAAK,IAAIvD,EAAI,EAAGA,EAAI8C,EAAclB,OAAQ5B,IAAK,CAC7C,MAAMwD,EAAeV,EAAc9C,GACnC,GAAU,IAANA,EACFwD,EAAaF,WAAWT,YAAYJ,GACpCA,EAASgB,OAAS,WACZF,EAAgB3B,OAAS,GAC3B2B,EAAgBzB,QAAS4B,IAEvBA,EAAUC,UAAYlB,EAASkB,iBAIhC,CACL,MAAMD,EAAYjB,EAASmB,WAAU,GACrCJ,EAAaF,WAAWT,YAAYa,GACpCH,EAAgBjC,KAAKoC,MAIzC,WAAoB,GAEPvB,EAAIC,EAASI,GAI1BD,EAAOsB,QAAUzB,EAAQ0B,QAAU,I,sBClEnCD,EAAUtB,EAAOsB,QAAU,EAAQ,GAAR,EAAkE,IAKrFvC,KAAK,CAACiB,EAAOvC,EAAI,ghFAAihF,KAG1iF6D,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB", "file": "chunks/chunk.18.js", "sourcesContent": ["import React from 'react';\nimport Choice from '../Choice/Choice';\nimport './ThumbnailCard.scss';\nimport { useTranslation } from 'react-i18next';\nimport PropTypes from 'prop-types';\n\nconst propTypes = {\n  onChange: PropTypes.func,\n  checked: PropTypes.bool,\n  index: PropTypes.number,\n  thumbnail: PropTypes.object,\n};\n\nconst ThumbnailCard = ({ onChange, checked, index, thumbnail }) => {\n  let img = null;\n  const { t } = useTranslation();\n  const altText = `${t('component.thumbnailsPanel')} ${thumbnail.pageNumber} `;\n\n  if (thumbnail) {\n    let src;\n    if (thumbnail.currentSrc) {\n      src = thumbnail.currentSrc;\n    } else if (thumbnail.url) {\n      src = thumbnail.url;\n    } else if (thumbnail.toDataURL) {\n      src = thumbnail.toDataURL();\n    }\n\n    img = <img className=\"thumb-card-img\" alt={altText} src={src} />;\n  }\n\n  return (\n    <div className=\"thumb-card\" onClick={onChange}>\n      <div className='thumb-body'>\n        <div className=\"thumb-image\">\n          {img}\n        </div>\n        <Choice\n          id={`custom-checkbox-${index}`}\n          className=\"thumb-checkbox\"\n          name={`thumb${index}`}\n          checked={checked}\n        />\n      </div>\n      <div className=\"thumb-card-title\">{index + 1}</div>\n    </div>\n  );\n};\n\nThumbnailCard.propTypes = propTypes;\n\nexport default ThumbnailCard;", "import ThumbnailCard from './ThumbnailCard';\n\nexport default ThumbnailCard;", "import React, { useState, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport ThumbnailCard from 'components/ThumbnailCard';\n\nfunction PageThumbnailsGrid({ document, onThumbnailSelected, selectedThumbnails, onfileLoadedHandler }) {\n  const [thumbnails, setThumbnails] = useState([]);\n  const [completedPages, setCompletedPagesCount] = useState(0);\n  const [t] = useTranslation();\n\n  useEffect(() => {\n    const inFlightPromiseIds = [];\n    async function generateThumbnails() {\n      const thumbnails = [];\n      const pageCount = document.getPageCount();\n      const thumbnailPromises = [];\n      for (let i = 1; i <= pageCount; i++) {\n        const thumbnailPromise = new Promise((resolve) => {\n          const promiseId = document.loadThumbnail(i, (result) => {\n            let thumbnail;\n            // If we get an embedded thumbnail we set the currentSrc prop\n            // otherwise we set the URl to avoid having to call this repeatedly every time the thumbnail card is rendered\n            if (result.currentSrc) {\n              thumbnail = {\n                pageNumber: i,\n                currentSrc: result.currentSrc,\n              };\n            } else {\n              thumbnail = {\n                pageNumber: i,\n                url: result.toDataURL(),\n              };\n            }\n            thumbnails.push(thumbnail);\n            setCompletedPagesCount(i);\n            resolve();\n          });\n          inFlightPromiseIds.push(promiseId);\n        });\n        thumbnailPromises.push(thumbnailPromise);\n      }\n\n      await Promise.all(thumbnailPromises);\n      const sortedThumbnails = [...thumbnails].sort((a, b) => a.pageNumber - b.pageNumber);\n      setThumbnails(sortedThumbnails);\n      inFlightPromiseIds.length = 0;\n      onfileLoadedHandler(false);\n    }\n\n    if (document) {\n      generateThumbnails();\n    }\n\n    return () => {\n      inFlightPromiseIds.forEach((id) => document.cancelLoadThumbnail(id));\n    };\n  }, [document]);\n\n  if (thumbnails.length > 0) {\n    return thumbnails.map((thumbnail, index) => {\n      const pageNumber = index + 1;\n      return (\n        <ThumbnailCard key={pageNumber}\n          onChange={() => onThumbnailSelected(pageNumber)}\n          checked={!!selectedThumbnails[pageNumber]}\n          index={index}\n          thumbnail={thumbnail}\n        />\n      );\n    });\n  }\n\n  const pageCount = document ? document.getPageCount() : 0;\n  const processText = `${completedPages}/${pageCount}`;\n  return (<div>{t('message.processing')} {processText}</div>);\n}\n\nexport default PageThumbnailsGrid;", "import PageThumbnailsGrid from './PageThumbnailsGrid';\n\nexport default PageThumbnailsGrid;", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./ThumbnailCard.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.thumb-card{box-sizing:border-box;width:133px;height:-moz-fit-content;height:fit-content;background:var(--gray-1);border-radius:6px;padding:0 24px 2px;position:relative;cursor:pointer}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .thumb-card{width:142px}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .thumb-card{width:142px}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .thumb-card{width:97px;padding:0 12px 2px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .thumb-card{width:97px;padding:0 12px 2px}}@media(max-width:430px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .thumb-card{width:97px;padding:0 12px 2px}}@container (max-width: 430px){.App.is-web-component:not(.is-in-desktop-only-mode) .thumb-card{width:97px;padding:0 12px 2px}}.thumb-card:hover{background:var(--tools-overlay-button-hover)}.thumb-body{display:flex;align-items:flex-start}.thumb-image{padding-top:12px;padding-right:5px;text-align:center}.thumb-card-img{width:83px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .thumb-card-img{width:68px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .thumb-card-img{width:68px}}@media(max-width:430px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .thumb-card-img{width:68px}}@container (max-width: 430px){.App.is-web-component:not(.is-in-desktop-only-mode) .thumb-card-img{width:68px}}.thumb-card-title{text-align:center;padding-top:8px;padding-bottom:10px}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};"], "sourceRoot": ""}