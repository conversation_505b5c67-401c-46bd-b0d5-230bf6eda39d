{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/en-tt.js"], "names": ["module", "exports", "e", "a", "default", "t", "_", "name", "weekdays", "split", "weekdaysShort", "weekdaysMin", "months", "monthsShort", "weekStart", "yearStart", "relativeTime", "future", "past", "s", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "ordinal", "locale"], "mappings": "gFAAoEA,EAAOC,QAAgL,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAGI,EAAE,CAACC,KAAK,QAAQC,SAAS,2DAA2DC,MAAM,KAAKC,cAAc,8BAA8BD,MAAM,KAAKE,YAAY,uBAAuBF,MAAM,KAAKG,OAAO,wFAAwFH,MAAM,KAAKI,YAAY,kDAAkDJ,MAAM,KAAKK,UAAU,EAAEC,UAAU,EAAEC,aAAa,CAACC,OAAO,QAAQC,KAAK,SAASC,EAAE,gBAAgBC,EAAE,WAAWC,GAAG,aAAaC,EAAE,UAAUC,GAAG,WAAWC,EAAE,QAAQC,GAAG,UAAUC,EAAE,UAAUC,GAAG,YAAYC,EAAE,SAASC,GAAG,YAAYC,QAAQ,CAACC,GAAG,QAAQC,IAAI,WAAWC,EAAE,aAAaC,GAAG,cAAcC,IAAI,oBAAoBC,KAAK,2BAA2BC,QAAQ,SAASnC,GAAG,IAAIG,EAAE,CAAC,KAAK,KAAK,KAAK,MAAMF,EAAED,EAAE,IAAI,MAAM,IAAIA,GAAGG,GAAGF,EAAE,IAAI,KAAKE,EAAEF,IAAIE,EAAE,IAAI,MAAM,OAAOF,EAAEC,QAAQkC,OAAOhC,EAAE,MAAK,GAAIA,EAAvmCD,CAAE,EAAQ", "file": "chunks/chunk.138.js", "sourcesContent": ["!function(e,t){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=t(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],t):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_en_tt=t(e.dayjs)}(this,(function(e){\"use strict\";function t(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var a=t(e),_={name:\"en-tt\",weekdays:\"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday\".split(\"_\"),weekdaysShort:\"Sun_Mon_Tue_Wed_Thu_Fri_Sat\".split(\"_\"),weekdaysMin:\"Su_Mo_Tu_We_Th_Fr_Sa\".split(\"_\"),months:\"January_February_March_April_May_June_July_August_September_October_November_December\".split(\"_\"),monthsShort:\"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec\".split(\"_\"),weekStart:1,yearStart:4,relativeTime:{future:\"in %s\",past:\"%s ago\",s:\"a few seconds\",m:\"a minute\",mm:\"%d minutes\",h:\"an hour\",hh:\"%d hours\",d:\"a day\",dd:\"%d days\",M:\"a month\",MM:\"%d months\",y:\"a year\",yy:\"%d years\"},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY HH:mm\",LLLL:\"dddd, D MMMM YYYY HH:mm\"},ordinal:function(e){var t=[\"th\",\"st\",\"nd\",\"rd\"],a=e%100;return\"[\"+e+(t[(a-20)%10]||t[a]||t[0])+\"]\"}};return a.default.locale(_,null,!0),_}));"], "sourceRoot": ""}