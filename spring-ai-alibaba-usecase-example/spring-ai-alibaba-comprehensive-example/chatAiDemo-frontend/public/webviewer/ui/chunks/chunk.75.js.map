{"version": 3, "sources": ["webpack:///./src/ui/src/components/ScaleOverlay/ScaleOverlay.scss?9199", "webpack:///./src/ui/src/components/ScaleOverlay/ScaleOverlay.scss", "webpack:///./src/ui/src/components/ScaleOverlay/ScaleSelector.js", "webpack:///./src/ui/src/components/ScaleOverlay/ScaleHeader.js", "webpack:///./src/ui/src/constants/measurementTypes.js", "webpack:///./src/ui/src/helpers/getNumberOfDecimalPlaces.js", "webpack:///./src/ui/src/helpers/getAngleInRadians.js", "webpack:///./src/ui/src/helpers/getFormattedUnit.js", "webpack:///./src/ui/src/components/MeasurementOverlay/LineMeasurementInput.js", "webpack:///./src/ui/src/components/MeasurementOverlay/EllipseMeasurementOverlay.js", "webpack:///./src/ui/src/components/ScaleOverlay/MeasurementDetail.js", "webpack:///./src/ui/src/components/ScaleOverlay/CalibrationOverlay.js", "webpack:///./src/ui/src/components/ScaleOverlay/ScaleOverlay.js", "webpack:///./src/ui/src/components/ScaleOverlay/ScaleOverlayContainer.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "propTypes", "scales", "PropTypes", "arrayOf", "object", "isRequired", "selectedScales", "string", "onScaleSelected", "func", "onAddingNewScale", "ariaLabelledBy", "Scale", "Core", "ScaleSelector", "t", "useTranslation", "dispatch", "useDispatch", "useState", "scalesInfo", "setScalesInfo", "useEffect", "getScalesInfo", "isMultipleScalesMode", "useSelector", "state", "selectors", "getIsMultipleScalesMode", "isMultipleScales", "coreScales", "core", "getScales", "scale", "scaleData", "toString", "measurements", "relatedPages", "Set", "canDelete", "measurementItem", "Annotations", "Annotation", "add", "canModify", "title", "measurementsNum", "pages", "openScaleDeletionModal", "scaleInfo", "warning", "message", "className", "join", "confirmBtnText", "onConfirm", "deleteScale", "actions", "showWarningMessage", "renderScale", "precision", "getScalePrecision", "pageScaleStr", "getFormattedValue", "pageScale", "value", "unit", "worldScaleStr", "worldScale", "scaleDisplay", "selectedScale", "isDropDownOpen", "setOpenDropDown", "toggleDropdown", "prevValue", "selectorRef", "useRef", "useOnClickOutside", "DataElementWrapper", "dataElement", "tabIndex", "ref", "onKeyDown", "e", "key", "onClick", "data-testid", "aria-expanded", "aria-<PERSON>by", "aria-controls", "role", "Icon", "glyph", "ariaHidden", "id", "classNames", "map", "includes", "preventDefault", "stopPropagation", "disabled", "aria-label", "<PERSON><PERSON>", "label", "ScaleHeader", "onClickFocusWrapped", "useFocusHandler", "measurementTypeTranslationMap", "distanceMeasurement", "perimeterMeasurement", "areaMeasurement", "rectangularAreaMeasurement", "cloudyRectangularAreaMeasurement", "ellipseMeasurement", "arcMeasurement", "split", "pt1", "pt2", "pt3", "angle", "AB", "Math", "sqrt", "pow", "x", "y", "BC", "AC", "acos", "atan2", "abs", "PI", "unitMap", "LineMeasurementInput", "annotation", "isOpen", "bool", "selectedTool", "isReadOnly", "isDocumentReadOnly", "factor", "Measure", "axis", "DisplayUnits", "getLine<PERSON><PERSON>th", "toFixed", "<PERSON><PERSON><PERSON><PERSON>", "onAnnotationChanged", "setAngle", "computeAngle", "addEventListener", "removeEventListener", "onInputChanged", "event", "target", "validateLine<PERSON>ength", "finishAnnotation", "getTool", "finish", "selectAnnotation", "getAnnotationManager", "deselectAnnotation", "Precision", "ensureLineIsWithinBounds", "useCallback", "lengthInPts", "value1", "value2", "maxLengthInPts", "getMaxLineLengthInPts", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "min", "forceLineRedraw", "getAnnotationUnit", "annotUnit", "annotationManager", "drawAnnotations", "PageNumber", "trigger", "maxX", "maxY", "currentPageNumber", "getCurrentPage", "documentWidth", "getPageWidth", "documentHeight", "getPageHeight", "angleInDegrees", "getAngle", "startPoint", "getStartPoint", "startX", "startY", "maxLenX", "cos", "maxLenY", "sin", "onAngleChange", "angleInRadians", "start", "Start", "endX", "endY", "setEndPoint", "adjustRect", "setLineAngle", "type", "autoFocus", "isMobileDevice", "onChange", "onBlur", "getFormattedUnit", "max", "getAngleInRadians", "End", "deltaX", "deltaY", "renderDeltas", "props", "propName", "componentName", "checkPropTypes", "shape", "number", "getContents", "Error", "defaults", "EllipseMeasurementOverlay", "area", "getMeasurementTextWithScaleAndUnits", "data", "refreshRadius", "setRadius", "computeRadius", "onAnnotationDeselected", "annotations", "action", "ensureDiameterIsWithinBounds", "getWidth", "decimalPlaces", "getNumberOfDecimalPlaces", "<PERSON><PERSON><PERSON>", "onChangeRadiusLength", "eventValue", "radius", "diameterInPts", "rect", "getRect", "newRect", "x1", "y1", "x2", "y2", "setHeight", "<PERSON><PERSON><PERSON><PERSON>", "resize", "forceEllipseRedraw", "redrawAnnotation", "getMaxDiameterInPts", "boundingRect", "width", "height", "Height", "precisionFractions", "validateDiameter", "MeasurementDetail", "icon", "color", "<PERSON><PERSON><PERSON>", "useMemo", "mapAnnotationToKey", "mapToolNameToKey", "name", "getDataWithKey", "Color", "toHexString", "StrokeColor", "contents", "keyDisplayNameMap", "renderValue", "Length", "<PERSON><PERSON>", "getIPathAnnotationPts", "path", "<PERSON><PERSON><PERSON>", "pts", "filter", "pt", "<PERSON><PERSON>", "undefined", "renderAngle", "renderDetails", "CalibrationOverlayPropTypes", "tempScale", "onCancelCalibrationMode", "onApplyCalibration", "CalibrationOverlay", "isCalibrationPopupOpen", "isElementOpen", "canApplyCalibration", "onMouseDown", "data-element", "onTouchStart", "updateIsCalibration", "enableOrDisableToolElements", "ScaleOverlay", "forceUpdate", "getCalibrationInfo", "getActiveToolName", "shallowEqual", "isCalibration", "previousToolName", "isFractionalUnit", "activeToolName", "setScales", "shouldShowMeasurementDetail", "Object", "keys", "onScaleUpdated", "newScales", "updateScales", "onCreateAnnotationWithNoScale", "has", "totalScales", "canModifyMeasurement", "memo", "measurementDataElements", "ScaleOverlayContainer", "isElementDisabled", "DataElements", "SCALE_OVERLAY_CONTAINER", "getScaleOverlayPosition", "isDisabled", "initialPosition", "position", "setPosition", "useReducer", "getDocumentContainerWidth", "getDocumentContainerHeight", "documentContainerWidth", "documentContainerHeight", "containerRef", "documentElement", "getViewerElement", "documentContainerElement", "getScrollViewElement", "style", "initialPositionParts", "offset", "left", "top", "offsetTop", "containerHeight", "current", "getBoundingClientRect", "offsetLeft", "offsetWidth", "containerWidth", "isNaN", "calculateStyle", "bounds", "syncDraggablePosition", "updateCalibrationInfo", "setCustomElementOverrides", "openScaleModal", "setSelectedScale", "openElements", "SCALE_MODAL", "setIsAddingNewScale", "newScale", "applyTo", "scaleToDelete", "getDocumentViewer", "getMeasurementManager", "getOldScalesToDeleteAfterApplying", "createAndApplyScale", "confirmScaleToDelete", "setToolMode", "setIsElementHidden", "deleteAnnotations", "isMobileSize", "MobilePopupWrapper", "bottom", "right", "DEFAULT_DISTANCE", "onDrag", "onStop", "cancel", "Overlay", "open", "closed"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,w14BAAy14B,KAGl34B0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,krDCGvB,IAAMC,EAAY,CAChBC,OAAQC,IAAUC,QAAQD,IAAUE,QAAQC,WAC5CC,eAAgBJ,IAAUC,QAAQD,IAAUK,QAAQF,WACpDG,gBAAiBN,IAAUO,KAAKJ,WAChCK,iBAAkBR,IAAUO,KAAKJ,WACjCM,eAAgBT,IAAUK,QAGtBK,EAAQrC,OAAOsC,KAAKD,MAEpBE,EAAgB,SAAH,GAAgG,QAA1Fb,cAAM,IAAG,KAAE,MAAEK,sBAAc,IAAG,KAAE,EAAEE,EAAe,EAAfA,gBAAiBE,EAAgB,EAAhBA,iBAAkBC,EAAc,EAAdA,eACrFI,EAAqB,EAAhBC,cAAgB,GAApB,GACFC,EAAWC,cAE+B,IAAZC,mBAAS,IAAG,GAAzCC,EAAU,KAAEC,EAAa,KAEhCC,qBAAU,WACR,GAAKrB,GAA4B,IAAlBA,EAAOnB,OAAtB,CAKA,IAAMsC,EAAaG,EAActB,GACjCoB,EAAcD,QALZC,EAAc,MAMf,CAACpB,IAEJ,IAAMuB,EAAuBC,aAAY,SAACC,GAAK,OAAKC,IAAUC,wBAAwBF,MAChFG,EAAmBvB,EAAexB,OAAS,EAE3CyC,EAAgB,SAACtB,GACrB,IAAMmB,EAAa,GACbU,EAAaC,IAAKC,YAgCxB,OA9BA/B,EAAOb,SAAQ,SAAC6C,GACd,IAAMC,EAAYJ,EAAWG,EAAME,YAC7BC,EAAe,GACfC,EAAe,IAAIC,IACrBC,GAAY,EAEhBL,EAAU9C,SAAQ,SAACoD,GACIA,aAA2BjE,OAAOsC,KAAK4B,YAAYC,aAKxEL,EAAaM,IAAIH,EAA4B,YAC7CJ,EAAa9C,KAAKkD,GAGbT,IAAKa,UAAUJ,KAClBD,GAAY,OAIhBnB,EAAW9B,KAAK,CACd2C,MAAOA,EACPY,MAAOZ,EAAME,WACbW,gBAAiBV,EAAatD,OAC9BiE,MAAO,EAAIV,GACXE,iBAIGnB,GAGH4B,EAAyB,SAACC,GAC9B,GAAKA,EAAL,CAIA,IAkCMC,EAAU,CACdC,UAnCkCF,EAAUF,MAAMjE,OAElD,yBAAKsE,UAAU,iBACb,2BACE,8BACGrC,EAAE,6DACH,sCAAQA,EAAE,wDAAuD,YAAIkC,EAAUF,MAAMM,KAAK,MAAK,MAC9FtC,EAAE,6DACH,sCAAQkC,EAAUH,gBAAe,YAAIG,EAAUH,gBAAkB,EAAI/B,EAAE,oDAAsDA,EAAE,mDAAkD,OAEnL,8BAAM,KACN,8BACGA,EAAE,kDACH,sCAAQA,EAAE,oDAAmD,MAC5DA,EAAE,+DAGP,2BACGA,EAAE,uDAIP,yBAAKqC,UAAU,iBACb,2BACE,8BACGrC,EAAE,qDACFA,EAAE,2DAUT8B,MALY,GAAH,OAAM9B,EAAE,mDAAkD,YAAIkC,EAAUJ,OAMjFS,eALqBvC,EAAE,kBAMvBwC,UAAW,kBAAMxB,IAAKyB,YAAYP,EAAUhB,SAE9ChB,EAASwC,IAAQC,mBAAmBR,MAGhCS,EAAc,SAAC1B,GACnB,IAAM2B,EAAY7B,IAAK8B,kBAAkB5B,GACnC6B,EAAelD,EAAMmD,kBAAkB9B,EAAM+B,UAAUC,MAAOhC,EAAM+B,UAAUE,KAAMN,GAAW,GAC/FO,EAAgBvD,EAAMmD,kBAAkB9B,EAAMmC,WAAWH,MAAOhC,EAAMmC,WAAWF,KAAMN,GAAW,GAClGS,EAAe,GAAH,OAAMP,EAAY,cAAMK,GAE1C,OAAO,6BAAME,IAGXxB,EAAQ9B,EAAE,kDAEd,GAAIT,EAAexB,SAAW+C,EAAkB,CAC9C,IAAMyC,EAAgB,IAAI1D,EAAMN,EAAe,IAC/CuC,EAAQc,EAAYW,GAMtB,IAAyD,IAAfnD,oBAAS,GAAM,GAAlDoD,EAAc,KAAEC,EAAe,KAChCC,EAAiB,WACrBD,GAAgB,SAACE,GAAS,OAAMA,MAG5BC,EAAcC,iBAAO,MAE3BC,YAAkBF,GAAa,WAC7BH,GAAgB,MASlB,OACE,kBAACM,EAAA,EAAkB,CACjB1B,UAAU,yBACV2B,YAAY,gBACZC,UAAW,EACXC,IAAKN,EACLO,UAZkB,SAACC,GACP,UAAVA,EAAEC,KAA6B,MAAVD,EAAEC,KACzBX,KAWAY,QAASZ,GAGT,yBACEa,cAAY,iBACZlC,UAAU,0BACVmC,gBAAehB,EACfiB,kBAAiB7E,EACjB8E,gBAAc,yBACdC,KAAK,WACLV,SAAU,GAEV,yBAAK5B,UAAU,sBACb,yBAAKA,UAAU,sBACZP,GAEH,yBAAKO,UAAU,uBACb,kBAACuC,EAAA,EAAI,CAACC,MAAM,oBAAoBC,YAAY,OAIjDtB,GACC,wBAAIuB,GAAG,yBAAyB1C,UAAW2C,IAAW,uBACpD,4BACE,yBAAK3C,UAAU,sBAAsBP,GACrC,yBAAKO,UAAU,uBACb,4BACEA,UAAU,gCAEV,kBAACuC,EAAA,EAAI,CAACC,MAAM,kBAAkBC,YAAY,OAI/CzE,EAAW4E,KAAI,SAAC/C,GAAS,OACxB,wBAAImC,IAAKnC,EAAUJ,MAAOO,UAAW2C,IAAW,CAC9C,iCAAiC,EACjC,kBAAmBzF,EAAe2F,SAAShD,EAAUJ,UAErD,4BACEO,UAAW2C,IAAW,CACpB1H,SAAS,IAEXgH,QAAS,kBAAM7E,EAAgBF,EAAgB2C,EAAUJ,QACzDqC,UAAW,SAACC,GACI,UAAVA,EAAEC,KAA6B,MAAVD,EAAEC,MACzBD,EAAEe,iBACF1F,EAAgBF,EAAgB2C,EAAUJ,UAI7Cc,EAAYV,EAAUhB,QAEzB,4BACEmB,UAAU,SACV4B,SAAU,EACVK,QAAS,SAACF,GACRA,EAAEe,iBACFf,EAAEgB,kBACFnD,EAAuBC,IAEzBiC,UAAW,SAACC,GACI,UAAVA,EAAEC,KAA6B,MAAVD,EAAEC,MACzBD,EAAEe,iBACFlD,EAAuBC,KAG3BmD,SAAUhF,EAAWtC,QAAU,IAAMmE,EAAUV,UAC/C8D,aAAA,UAAetF,EAAE,iBAAgB,YAAIkC,EAAUJ,QAE/C,kBAAC8C,EAAA,EAAI,CAACC,MAAM,0BAIjBpE,GACC,4BACE,kBAAC8E,EAAA,EAAM,CAACjB,QAAS3E,EAAkB6F,MAAOxF,EAAE,+CAAgDqC,UAAU,sBASpHtC,EAAcd,UAAYA,EAEXc,Q,wiCCxPf,IAAMd,EAAY,CAChBC,OAAQC,IAAUC,QAAQD,IAAUE,QAAQC,WAC5CC,eAAgBJ,IAAUC,QAAQD,IAAUK,QAAQF,WACpDG,gBAAiBN,IAAUO,KAAKJ,WAChCK,iBAAkBR,IAAUO,KAAKJ,YAG7BmG,EAAc,SAAH,GAAsE,IAAhEvG,EAAM,EAANA,OAAQK,EAAc,EAAdA,eAAgBE,EAAe,EAAfA,gBAAiBE,EAAgB,EAAhBA,iBACvDK,EAAqB,EAAhBC,cAAgB,GAApB,GAEFyF,EAAsBC,YAAgBhG,GAE5C,OACE,yBAAK0C,UAAU,wBACb,kBAACuC,EAAA,EAAI,CAACC,MAAM,eAAexC,UAAU,uBACrC,wBAAI0C,GAAG,uBAAuB1C,UAAU,uBAAuBrC,EAAE,mCAChEd,EAAOnB,OACN,kBAAC,EAAa,CACZmB,OAAQA,EACRK,eAAgBA,EAChBE,gBAAiBA,EACjBE,iBAAkBA,EAClBC,eAAe,yBAGjB,kBAAC2F,EAAA,EAAM,CAAClD,UAAU,gBAAgBiC,QAASoB,EAAqB1B,YAAY,cAAcwB,MAAOxF,EAAE,mDAM3GyF,EAAYxG,UAAYA,EACTwG,Q,uBCxCFG,EAAgC,CAC3CC,oBAAqB,gDACrBC,qBAAsB,iDACtBC,gBAAiB,4CACjBC,2BAA4B,4CAC5BC,iCAAkC,4CAClCC,mBAAoB,4CACpBC,eAAgB,4C,SCPH,WAACtD,GAAS,OAAoB,IAAdA,EAAkB,EAAIA,aAAS,EAATA,EAAWzB,WAAWgF,MAAM,KAAK,GAAGrI,Q,QCA1E,WAACsI,EAAKC,EAAKC,GACxB,IAAIC,EAEJ,GAAIH,GAAOC,EACT,GAAIC,EAAK,CAEP,IAAME,EAAKC,KAAKC,KAAKD,KAAKE,IAAIN,EAAIO,EAAIR,EAAIQ,EAAG,GAAKH,KAAKE,IAAIN,EAAIQ,EAAIT,EAAIS,EAAG,IACpEC,EAAKL,KAAKC,KAAKD,KAAKE,IAAIN,EAAIO,EAAIN,EAAIM,EAAG,GAAKH,KAAKE,IAAIN,EAAIQ,EAAIP,EAAIO,EAAG,IACpEE,EAAKN,KAAKC,KAAKD,KAAKE,IAAIL,EAAIM,EAAIR,EAAIQ,EAAG,GAAKH,KAAKE,IAAIL,EAAIO,EAAIT,EAAIS,EAAG,IAC1EN,EAAQE,KAAKO,MAAMF,EAAKA,EAAKN,EAAKA,EAAKO,EAAKA,IAAO,EAAID,EAAKN,SAG5DD,EAAQE,KAAKQ,MAAMZ,EAAIQ,EAAIT,EAAIS,EAAGR,EAAIO,EAAIR,EAAIQ,GAG9CL,GADAA,EAAQE,KAAKS,IAAIX,IACDE,KAAKU,GAAK,EAAIV,KAAKU,GAAKZ,EAAQA,EAIpD,OAAOA,GCnBM,WAACrD,GACd,OAAQA,GACN,IAAK,MACH,MAAO,KACT,IAAK,MACH,MAAO,KACT,IAAK,QACH,MAAO,KACT,QACE,OAAOA,I,+hCCCb,IAAMkE,EAAU,CACd,MAAQ,KACR,MAAQ,MAGVC,EAAqBrI,UAAY,CAC/BsI,WAAYpI,IAAUE,OACtBmI,OAAQrI,IAAUsI,KAAKnI,WACvBoI,aAAcvI,IAAUE,OACxBwC,UAAW1C,IAAUsI,MAGvB,IAAM5H,EAAQrC,OAAOsC,KAAKD,MAE1B,SAASyH,EAAqB,GAAiD,MAA/CC,EAAU,EAAVA,WAAYC,EAAM,EAANA,OAAQE,EAAY,EAAZA,aAAc7F,EAAS,EAATA,UACzD7B,EAAqB,EAAhBC,cAAgB,GAApB,GACF0H,EAAajH,aAAY,SAACC,GAAK,OAAKC,IAAUgH,mBAAmBjH,MACjEkH,EAASN,aAAU,EAAVA,EAAYO,QAAQC,KAAK,GAAGF,OACrC1E,GAAOoE,aAAU,EAAVA,EAAYS,aAAa,MAAMN,SAAqB,QAAT,EAAZA,EAAcI,eAAO,WAAT,EAAZ,EAAuB3E,MACyB,IAAhE/C,qBAAUmH,aAAU,EAAVA,EAAYU,iBAAkBJ,GAAU,GAAGK,QAAQ,IAAG,GAArFnK,EAAM,KAAEoK,EAAS,KAExB5H,qBAAU,WACR,GAAKgH,EAAL,CAIA,IAAMa,EAAsB,WAC1BD,GAAWZ,EAAWU,gBAAkBJ,GAAQK,QAAQ,IACxDG,EAASC,MAIX,OAFAtH,IAAKuH,iBAAiB,YAAaH,GAE5B,WACLpH,IAAKwH,oBAAoB,YAAaJ,IAVtCC,EAASC,OAYV,CAACf,EAAYe,EAAcT,EAAQH,IAEtC,IAAMe,EAAiB,SAACC,GACtBP,EAAUO,EAAMC,OAAOzF,OACvB0F,EAAmBF,GACnBG,KAGIA,EAAmB,WACV7H,IAAK8H,QAAQ,uCACrBC,UAGDC,EAAmB,WACGhI,IAAKiI,uBACbD,iBAAiBzB,IAG/B2B,EAAqB,WACClI,IAAKiI,uBACbC,mBAAmB3B,IAGjCqB,EAAqB,SAACF,GAC1B,GAAKnB,EAAL,CAGA,IAAIxJ,EAAS2I,KAAKS,IAAIuB,EAAMC,OAAOzF,OAC/BnF,EAASwJ,EAAW4B,YACtBpL,EAASwJ,EAAW4B,UACpBhB,EAAUpK,IAEZ,IAAM8J,EAASN,EAAWO,QAAQC,KAAK,GAAGF,OAE1CuB,EADoBrL,EAAS8J,KAQzBuB,EAA2BC,uBAC/B,SAACC,GACC,GAN0BC,EAMAhC,EAAWU,gBANHuB,EAMoBF,IALjD5C,KAAKS,IAAIoC,EAASC,GAAU,IAKmC,CAClE,IAAMC,EAAiBC,IACvBnC,EAAWoC,cAAcjD,KAAKkD,IAAIH,EAAgBH,IAClDO,IATuB,IAACN,EAAQC,IAYpC,CAACjC,EAAYsC,EAAiBH,IAG1BI,EAAoB,SAACvC,GAAe,MACpCwC,EAQJ,OAPIxC,SAAwB,QAAd,EAAVA,EAAYS,oBAAY,OAAxB,EAA0BjK,SAE1BgM,EADqC,IAAnCxC,EAAWS,aAAajK,QAA+C,QAA/BwJ,EAAWS,aAAa,IAA+C,QAA/BT,EAAWS,aAAa,GAC9F,KAEAT,EAAWS,aAAa,IAGjCX,EAAQ0C,IAAcA,GAAa1C,EAAQlE,IAASA,GA2BvD0G,EAAkBR,uBAAY,WAClC,IAAMW,EAAoBhJ,IAAKiI,uBAC/Be,EAAkBC,gBAAgB1C,EAAW2C,YAC7CF,EAAkBG,QAAQ,oBAAqB,CAAC,CAAC5C,GAAa,SAAU,OACvE,CAACA,IAEEmC,EAAwBL,uBAAY,WACxC,IAQIe,EACAC,EATEC,EAAoBtJ,IAAKuJ,iBACzBC,EAAgBxJ,IAAKyJ,aAAaH,GAClCI,EAAiB1J,IAAK2J,cAAcL,GACpCM,EAAiBrD,EAAWsD,YAAc,IAAMnE,KAAKU,IAAIc,QAAQ,GACjE4C,EAAavD,EAAWwD,gBACxBC,EAASF,EAAWjE,EACpBoE,EAASH,EAAWhE,EAKxBsD,EADE1D,KAAKS,IAAIyD,GAAkB,GACtBJ,EAEA,EAIPH,EADEO,EAAiB,EACZF,EAEA,EAGT,IAAMQ,EAAUxE,KAAKS,KAAKiD,EAAOY,GAAUtE,KAAKyE,IAAI5D,EAAWsD,aACzDO,EAAU1E,KAAKS,KAAKkD,EAAOY,GAAUvE,KAAK2E,IAAI9D,EAAWsD,aAE/D,OAAOnE,KAAKkD,IAAIsB,EAASE,KACxB,CAAC7D,IAcE+D,EAAgB,SAAC5C,GACrBL,EAASK,EAAMC,OAAOzF,OAbH,SAACwF,GACpB,IACM6C,EADQ7C,EAAMC,OAAOzF,OACKwD,KAAKU,GAAK,MAAQ,EAC5CkC,EAAc/B,EAAWU,gBACzBuD,EAAQjE,EAAWkE,MACnBC,EAAOhF,KAAKyE,IAAII,GAAkBjC,EAAckC,EAAM3E,EACtD8E,EAAOjF,KAAK2E,IAAIE,GAAkBjC,EAAckC,EAAM1E,EAC5DS,EAAWqE,YAAYF,EAAMC,GAC7BpE,EAAWsE,aACXhC,IAKAiC,CAAapD,GACbG,KAGIP,EAAee,uBAAY,WAC/B,IAAK9B,EACH,OAAO,EAET,IAAIgE,EAAiBhE,EAAWsD,WAIhC,QADAU,GADAA,IAAmB,GACe,EAAIA,EAAiB,EAAI7E,KAAKU,GAAKmE,GAC3C7E,KAAKU,GAAM,KAAKc,QAAQ,KACjD,CAACX,IAE8C,IAAxBnH,mBAASkI,KAAe,GAA3C9B,EAAK,KAAE6B,EAAQ,KAQtB,OANA9H,qBAAU,WACHiH,GACH4B,EAAyB7B,EAAWU,mBAErC,CAACV,EAAY6B,EAA0B5B,IAGxC,oCACE,yBAAKnF,UAAU,4BACb,yBAAKA,UAAU,oBACZrC,EAAE,sCAAsC,KAE3C,2BACEqC,UAAU,cACV0J,KAAK,SACLnC,IAAI,IACJvE,SAAUsC,IAAeJ,IAAe1F,EACxCqB,MAAQqE,EAAiBxJ,EAAJ,EACrBiO,WAAYC,IACZC,SAAU,SAACxD,GACTD,EAAeC,GACfM,KAEFmD,OAAQ,SAACzD,GACPE,EAAmBF,IAErBvE,UAAW,SAACuE,GACQ,UAAdA,EAAMrE,MACRoE,EAAeC,GACfQ,QAILkD,EAAiBjJ,IAEpB,yBAAKd,UAAU,4BACb,yBAAKA,UAAU,oBAAoBrC,EAAE,mCAAmC,KACxE,2BACEqC,UAAU,cACV0J,KAAK,SACLnC,IAAI,IACJyC,IAAI,MACJhH,SAAUsC,IAAeJ,IAAe1F,EACxCqB,MAAOsD,EACPwF,WAAYC,IACZC,SAAU,SAACxD,GACT4C,EAAc5C,GACdM,KAEF7E,UAAW,SAACuE,GACQ,UAAdA,EAAMrE,MACRiH,EAAc5C,GACdQ,QAGJ,KAjJa,WACnB,IAAM1C,EAASe,GAAc+E,EAAkB/E,EAAWkE,MAAOlE,EAAWgF,MAAS,EAC/EpJ,EAAO2G,EAAkBvC,GACzBiF,EAAS3M,EAAMmD,kBAAkBuE,GAAcb,KAAKS,IAAIpJ,EAAS2I,KAAKyE,IAAI3E,IAASrD,EAAMoE,aAAU,EAAVA,EAAY4B,WACrGsD,EAAS5M,EAAMmD,kBAAkBuE,GAAcb,KAAKS,IAAIpJ,EAAS2I,KAAK2E,IAAI7E,IAASrD,EAAMoE,aAAU,EAAVA,EAAY4B,WAE3G,OACE,oCACE,yBAAK9G,UAAU,4BACb,yBAAKA,UAAU,oBAAoBrC,EAAE,mCAAmC,KACxE,yBAAKqC,UAAU,eACZmK,IAGL,yBAAKnK,UAAU,4BACb,yBAAKA,UAAU,oBAAoBrC,EAAE,mCAAmC,KACxE,yBAAKqC,UAAU,eACZoK,KAmINC,IAKQpF,Q,+hCC3Pf,IAAMrI,EAAY,CAChBsI,WAAY,SAACoF,EAAOC,EAAUC,GAC5B,OAAKF,EAAMpF,YAAeoF,EAAMjF,cAG5BiF,EAAMpF,YACRpI,IAAU2N,eACR,CACEvF,WAAYpI,IAAU4N,MAAM,CAC1B5D,UAAWhK,IAAU6N,OACrBhF,aAAc7I,IAAUC,QAAQD,IAAUK,QAC1CyN,YAAa9N,IAAUO,QAG3B,CAAE6H,WAAYoF,EAAMpF,YACpB,OACA,6BAGG,MAhBE,IAAI2F,MAAM,qEAAD,OAAsEL,EAAa,QAkBvGnF,aAAc,SAACiF,EAAOC,EAAUC,GAC9B,OAAKF,EAAMpF,YAAeoF,EAAMjF,cAG5BiF,EAAMjF,cACRvI,IAAU2N,eACR,CACEpF,aAAcvI,IAAU4N,MAAM,CAC5BI,SAAUhO,IAAU4N,MAAM,CACxB5D,UAAWhK,IAAU6N,SAEvBlF,QAAS3I,IAAU4N,MAAM,CACvB5J,KAAMhE,IAAUK,YAItB,CAAEkI,aAAciF,EAAMjF,cACtB,OACA,6BAGG,MAnBE,IAAIwF,MAAM,qEAAD,OAAsEL,EAAa,QAqBvGrF,OAAQrI,IAAUsI,KAAKnI,WACvBuC,UAAW1C,IAAUsI,MAGvB,SAAS2F,EAA0B,GAAiD,YAA/C7F,EAAU,EAAVA,WAAYC,EAAM,EAANA,OAAQE,EAAY,EAAZA,aAAc7F,EAAS,EAATA,UAC7D7B,EAAMC,cAAND,EAEF2H,EAAajH,aAAY,SAACC,GAAK,OAAKC,IAAUgH,mBAAmBjH,MACjE0M,GAAO9F,SAA+C,QAArC,EAAVA,EAAY+F,2CAAmC,WAArC,EAAV,OAAA/F,KAAuD,EAC9DgG,EAAO,CACX1K,UAAY0E,EAAiDA,EAAW4B,UAA/CzB,SAAsB,QAAV,EAAZA,EAAcyF,gBAAQ,WAAV,EAAZ,EAAwBhE,UACjDhG,KAAMiJ,EAAoE,QAAlD,GAAA7E,aAAU,EAAVA,EAAY1H,SAAS6H,SAAsB,QAAV,EAAZA,EAAcyF,gBAAQ,WAAV,EAAZ,EAAwBtN,cAAK,aAAnD,EAAuD,GAAG,IACjFwN,QAGIG,EAAgB,WACpBC,EAAUC,MAGZnN,qBAAU,WAIR,OAHAiN,IACAxM,IAAKuH,iBAAiB,YAAaiF,GAE5B,WACLxM,IAAKwH,oBAAoB,YAAagF,MAEvC,CAACjG,IAEJhH,qBAAU,WACR,IAAMoN,EAAyB,SAACC,EAAaC,GAC3C,GAAe,eAAXA,EAAyB,CAC3B,IAAMtG,EAAaqG,EAAY,GAC/BE,EAA6BvG,EAAWwG,WAAYxG,KAMxD,OAFAvG,IAAKuH,iBAAiB,qBAAsBoF,GAErC,WACL3M,IAAKwH,oBAAoB,qBAAsBmF,MAEhD,IAEH,IAAMD,EAAgB,WACpB,IAAKnG,EACH,OAAO,EAET,IAAMyG,EAAgBzG,GAAc0G,EAAyB1G,EAAW4B,YAAc,EAChFtB,EAASN,EAAWO,QAAQC,KAAK,GAAGF,OAE1C,QADqBN,EAAW2G,MAAQ,GAAGhG,QAAQ8F,GAC7BnG,GAAQK,QAAQ8F,IAkBlCG,EAAuB,SAACzF,GAC5B,IAAM0F,EAAa1F,EAAMC,OAAOzF,OAAS,EACnCmL,EAASD,EAAa,EAAIA,EAAa,KAGvCE,EAA8B,GADhBD,EADL9G,EAAWO,QAAQC,KAAK,GAAGF,QAGpC0G,EAAOhH,EAAWiH,UAMlBC,EAAU,CAAEC,GAJbH,EAAS,GAIYI,GAHrBJ,EAAS,GAGoBK,GAF7BL,EAAS,GAAID,EAEwBO,GADrCN,EAAS,GAAID,GAGlB/G,EAAWuH,UAAUR,GACrB/G,EAAWwH,SAAST,GACpB/G,EAAWyH,OAAOP,GAClBhB,EAAUY,GACVY,EAAmB1H,GAhCNvG,IAAK8H,QAAQ,sCACrBC,UAmCDkG,EAAqB,SAAC1H,GAC1B,IAAMyC,EAAoBhJ,IAAKiI,uBAC/Be,EAAkBkF,iBAAiB3H,GACnCyC,EAAkBG,QAAQ,oBAAqB,CAAC,CAAC5C,GAAa,SAAU,MAGpE4H,EAAsB9F,uBAAY,SAAC9B,GACvC,IAAM+C,EAAoBtJ,IAAKuJ,iBACzBC,EAAgBxJ,IAAKyJ,aAAaH,GAClCI,EAAiB1J,IAAK2J,cAAcL,GAIpCF,EAAOI,EAHEjD,EAAc,EAIvB8C,EAAOK,EAHEnD,EAAc,EAK7B,OAAOb,KAAKkD,IAAIQ,EAAMC,MAYlByD,EAA+BzE,uBAAY,SAACiF,EAAe/G,GAG/D,GAAI+G,EAFqBa,EAAoB5H,GAEP,CACpC,IAAM6H,EAAe7H,EAAWiH,UACxBE,EAAmBU,EAAnBV,GAAIE,EAAeQ,EAAfR,GAAID,EAAWS,EAAXT,GAAIE,EAAOO,EAAPP,GAChBQ,EAAQ9H,EAAW2G,MACnBoB,EAAS/H,EAAWgI,OAClBjF,EAAoBtJ,IAAKuJ,iBACzBC,EAAgBxJ,IAAKyJ,aAAaH,GAClCI,EAAiB1J,IAAK2J,cAAcL,GAEtCsE,EAAKpE,IACP4E,EAAiB,GAAI5E,EACrB6E,EAAQ7E,EAAgBkE,GAEtBG,EAAKnE,IACP0E,EAAiB,GAAI1E,EACrB4E,EAAS5E,EAAiBiE,GAGxBU,EAAQ7E,EACVjD,EAAWwH,SAASM,GAEpB9H,EAAWwH,SAASvE,GAElB8E,EAAS5E,EACXnD,EAAWuH,UAAUQ,GAErB/H,EAAWuH,UAAUpE,GAEvBnD,EAAWyH,OAAOI,GAClBH,EAAmB1H,MAEpB,CAAC4H,IAEJ5O,qBAAU,WACHiH,GACHsG,EAA6BvG,EAAWwG,WAAYxG,KAErD,CAACA,EAAYuG,EAA8BtG,IAE9C,IAAqD,IAAzBpH,mBAASsN,KAAgB,GAA9CW,EAAM,KAAEZ,EAAS,KAExB,OACE,yBAAKpL,UAAU,iCACb,yBAAKA,UAAU,4BACb,yBAAKA,UAAU,oBAAoBrC,EAAE,2BAA2B,KAChE,yBAAKqC,UAAU,eACZmN,IAAmBjC,EAAK1K,YAAc0K,EAAK1K,YAGhD,yBAAKR,UAAU,4BACb,yBAAKA,UAAU,oBAAoBrC,EAAE,kCAAkC,KACvE,yBAAKqC,UAAU,eACZkL,EAAKF,OAGV,yBAAKhL,UAAU,4BACb,yBAAKA,UAAU,oBAAoBrC,EAAE,oCAAoC,KACzE,2BACEgM,WAAYC,IACZ5J,UAAU,cACV0J,KAAK,SACLnC,IAAI,IACJvE,SAAUsC,IAAeJ,IAAe1F,EACxCqB,MAAOmL,EACPnC,SAAU,SAACxD,GACTyF,EAAqBzF,GA/HH1H,IAAKiI,uBACbD,iBAAiBzB,IAiI7B4E,OAAQ,SAACzD,GAAK,OAhFG,SAACA,GACxB,IAAM2F,EAAS3H,KAAKS,IAAIuB,EAAMC,OAAOzF,OAC/B2E,EAASN,EAAWO,QAAQC,KAAK,GAAGF,OAG1CiG,EADoC,GADhBO,EAASxG,GAEeN,GAC5CiG,IA0EyBiC,CAAiB/G,IACpCvE,UAAW,SAACuE,GACQ,UAAdA,EAAMrE,MACR8J,EAAqBzF,GAhIL1H,IAAKiI,uBACbC,mBAAmB3B,OAoIhCgG,EAAKpK,OAMdiK,EAA0BnO,UAAYA,EAEvBmO,Q,qwBCtPf,IAAMnO,EAAY,CAChBsI,WAAYpI,IAAUE,OACtBmI,OAAQrI,IAAUsI,KAAKnI,WACvBoI,aAAcvI,IAAUE,OACxBwC,UAAW1C,IAAUsI,MAGjBiI,EAAoB,SAAH,GAAwD,IAgBnErL,EAAKsL,EAAMC,EACbC,EAjBmBtI,EAAU,EAAVA,WAAYC,EAAM,EAANA,OAAQE,EAAY,EAAZA,aAAc7F,EAAS,EAATA,UACrD7B,EAAMC,cAAND,EAEFuN,EAAOuC,mBAAQ,WAAM,YACnBzL,EAAMkD,EAAawI,YAAmBxI,GAAcyI,YAAiBtI,EAAauI,MAExF,MAAO,CACL5L,MACAsL,KAAMO,YAAe7L,GAAKsL,KAC1BC,MAAOrI,EAAaA,EAAW4I,MAAMC,cAAgB1I,SAAsB,QAAV,EAAZA,EAAcyF,gBAAQ,OAAa,QAAb,EAAtB,EAAwBkD,mBAAW,WAAvB,EAAZ,EAAqCD,cAC1FE,SAAU/I,EAA2D,QAAjD,EAAGA,EAAW+F,2CAAmC,aAA9C,OAAA/F,GAAqD,EAC5E1E,UAAY0E,EAAiDA,EAAW4B,UAA/CzB,SAAsB,QAAV,EAAZA,EAAcyF,gBAAQ,WAAV,EAAZ,EAAwBhE,cA6IrD,OACE,yBAAK9G,UAAU,sBAzIPgC,EAAqBkJ,EAArBlJ,IAAKsL,EAAgBpC,EAAhBoC,KAAMC,EAAUrC,EAAVqC,MACbC,EAAiBjK,EAA8BvB,GAGnD,yBAAKhC,UAAU,UACb,kBAACuC,EAAA,EAAI,CAACC,MAAO8K,EAAMC,MAAOA,EAAOvN,UAAU,SAC3C,6BAAMrC,EAAE6P,MAmGQ,WACpB,IAZMxB,EAZAtQ,EAwBEsG,EAAmBkJ,EAAnBlJ,IAAKxB,EAAc0K,EAAd1K,UACb,MAAY,uBAARwB,EACK,kBAAC,EAAyB,CAACkD,WAAYA,EAAYG,aAAcA,EAAcF,OAAQA,EAAQ3F,UAAWA,IAIjH,yBAAKQ,UAAU,iCACb,yBAAKA,UAAU,4BACb,yBAAKA,UAAU,oBAAoBrC,EAAE,2BAA2B,KAChE,yBAAKqC,UAAU,eACZmN,IAAmB3M,IAAcA,IAG7B,wBAARwB,GACC,kBAAC,EAAoB,CAACkD,WAAYA,EAAYC,OAAQA,EAAQE,aAAcA,EAAc7F,UAAWA,IAEtG,CACC,6BACA,mCACA,uBACA,mBACAqD,SAASb,IApHG,WAClB,IAAQA,EAAkBkJ,EAAlBlJ,IAAKiM,EAAa/C,EAAb+C,SAEPC,EAAoB,CACxB1K,oBAAqB7F,EAAE,sCACvB8F,qBAAsB9F,EAAE,uCACxB+F,gBAAiB/F,EAAE,kCACnBgG,2BAA4BhG,EAAE,kCAC9BiG,iCAAkCjG,EAAE,mCAGtC,OACE,yBAAKqC,UAAU,4BACb,yBAAKA,UAAU,oBAAoBkO,EAAkBlM,GAAK,KAC1D,yBAAKhC,UAAU,eACZiO,IAqGgBE,GACV,mBAARnM,IA9CCtG,GAASwJ,aAAU,EAAVA,EAAYkJ,SAAU,EAEnC,yBAAKpO,UAAU,4BACb,yBAAKA,UAAU,oBAAoBrC,EAAE,qCACrC,yBAAKqC,UAAU,eACZtE,KA0CM,mBAARsG,IAnCCgK,GAAS9G,aAAU,EAAVA,EAAYmJ,SAAU,EAEnC,yBAAKrO,UAAU,4BACb,yBAAKA,UAAU,oBAAoBrC,EAAE,qCACrC,yBAAKqC,UAAU,eACZgM,MA+BD,CAAC,6BAA8B,sBAAuB,oCAAoCnJ,SAASb,IAlGvF,WAClB,IAAKkD,EACH,OACE,yBAAKlF,UAAU,4BACb,yBAAKA,UAAU,oBAAoBrC,EAAE,mCAAmC,KACxE,yBAAKqC,UAAU,eAAc,OAMnC,IAAQgC,EAAQkJ,EAARlJ,IACFsM,EAAwB,SAACpJ,GAC7B,IAAMqJ,EAAOrJ,EAAWsJ,UAClB9S,EAAS6S,EAAK7S,OACpB,MAAO,CAAC6S,EAAK7S,EAAS,GAAI6S,EAAK7S,EAAS,GAAI6S,EAAK7S,EAAS,KAUtD+S,EARW,CACfjL,oBAAqB,YAAa,MAAO,CAAZ,EAAL4F,MAAU,EAAHc,MAC/BzG,qBAAsB6K,EACtB5K,gBAAiB4K,EACjB3K,2BAA4B2K,EAC5B1K,iCAAkC0K,EAClCxK,eAAgBwK,GAEGtM,GAAKkD,GAAYwJ,QAAO,SAACC,GAAE,QAAOA,KAEnDxK,EAAQ8F,EAAiB,eAAIwE,IACjC,GAAItK,EAAO,CACT,IAAMwH,EAAgBC,EAAyB1G,EAAW4B,WAC1D3C,GAAUA,EAAQE,KAAKU,GAAM,KAAKc,QAAQ8F,GAO5C,MAJY,mBAAR3J,IACFmC,EAAQe,EAAW0J,MAAM/I,QAAQ,SAIvBgJ,IAAV1K,GACE,yBAAKnE,UAAU,4BACb,yBAAKA,UAAU,oBAAoBrC,EAAE,mCAAmC,KACxE,yBAAKqC,UAAU,eACZmE,EAAM,MAyDT2K,IAQHC,KAKP1B,EAAkBzQ,UAAYA,EAEfyQ,S,miCChLf,IAAM7P,GAAQrC,OAAOsC,KAAKD,MAEpBwR,GAA8B,CAClCC,UAAWnS,IAAUK,OACrB+R,wBAAyBpS,IAAUO,KACnC8R,mBAAoBrS,IAAUO,MAG1B+R,GAAqB,SAAH,GAAmE,MAA7DH,EAAS,EAATA,UAAWC,EAAuB,EAAvBA,wBAAyBC,EAAkB,EAAlBA,mBACzDxR,EAAqB,GAAhBC,cAAgB,GAApB,GAEFyR,EAAyBhR,aAAY,SAACC,GAAK,OAAKC,IAAU+Q,cAAchR,EAAO,sBAE/EiR,EAAsBF,GAA0BJ,EAEtD,OACE,yBAAKjP,UAAU,2BACb,yBAAKA,UAAU,wBACb,kBAACuC,EAAA,EAAI,CAACC,MAAM,eAAexC,UAAU,uBACrC,yBAAKA,UAAU,uBAAuBrC,EAAE,6CAE1C,yBAAKqC,UAAU,yBAGTrC,EAFF4R,EAEI,qDADA,oDAGR,yBAAKvP,UAAU,YACf,yBAAKA,UAAU,wBACb,4BACEA,UAAU,qBACVwP,YAAaN,GAEZvR,EAAE,kBAEL,4BACEqC,UAAU,oBACVgD,WAAYqM,GAA0BJ,IAA6C,QAA/B,MAAIzR,GAAMyR,GAAWjO,kBAAU,aAA/B,EAAiCH,OAAQ,GAC7F4O,eAAa,mBACbD,YAAaL,EACbO,aAAcP,GAEbxR,EAAE,oBAObyR,GAAmBxS,UAAYoS,GAEhBI,U,y4CC9Cf,IAAM5R,GAAQrC,OAAOsC,KAAKD,MAEpBZ,GAAY,CAChB2O,YAAazO,IAAUC,QAAQD,IAAUE,QAAQC,WACjDoI,aAAcvI,IAAUE,OACxB2S,oBAAqB7S,IAAUO,KAAKJ,WACpC2S,4BAA6B9S,IAAUO,KAAKJ,WAC5CG,gBAAiBN,IAAUO,KAAKJ,WAChCiS,wBAAyBpS,IAAUO,KAAKJ,WACxCkS,mBAAoBrS,IAAUO,KAAKJ,WACnCK,iBAAkBR,IAAUO,KAAKJ,YAG7B4S,GAAe,SAAH,GAUZ,IATJtE,EAAW,EAAXA,YACAlG,EAAY,EAAZA,aACAsK,EAAmB,EAAnBA,oBACAC,EAA2B,EAA3BA,4BACAxS,EAAe,EAAfA,gBACA8R,EAAuB,EAAvBA,wBACAC,EAAkB,EAAlBA,mBACA7R,EAAgB,EAAhBA,iBACAwS,EAAW,EAAXA,YAQgB,KAHZzR,aAAY,SAACC,GAAK,MAAK,CACzBC,IAAUwR,mBAAmBzR,GAC7BC,IAAUyR,kBAAkB1R,MAC3B2R,KAAa,UALZC,EAAa,EAAbA,cAAejB,EAAS,EAATA,UAAS,IAAEkB,wBAAgB,IAAG,wCAAqC,EAAEC,EAAgB,EAAhBA,iBACtFC,EAAc,KAKsC,KAA1BtS,mBAASY,IAAKC,aAAY,GAA/C/B,EAAM,KAAEyT,EAAS,KAClBC,KAAgCC,OAAOC,KAAK5T,GAAQnB,SAAa2J,KAAkBkG,EAAY7P,QAAU6P,EAAY7P,OAAS,IAEpIwC,qBAAU,WACR4R,MACC,CAACjT,IAEJqB,qBAAU,WACR,IAAMwS,EAAiB,SAACC,GACtBL,EAAUK,IAENC,EAAe,WACnBN,EAAU3R,IAAKC,cAGXiS,EAAgC,WACpCvT,KAOF,OALAqB,IAAKuH,iBAAiB,eAAgBwK,GACtC/R,IAAKuH,iBAAiB,8BAA+B2K,GACrDlS,IAAKuH,iBAAiB,oBAAqB0K,GAC3CjS,IAAKuH,iBAAiB,oBAAqB0K,GAEpC,WACLjS,IAAKwH,oBAAoB,eAAgBuK,GACzC/R,IAAKwH,oBAAoB,8BAA+B0K,GACxDlS,IAAKwH,oBAAoB,oBAAqByK,GAC9CjS,IAAKwH,oBAAoB,oBAAqByK,MAE/C,IAEH1S,qBAAU,WACe,2CAAnBmS,IACDH,GAAiBP,GAAoB,GACtCC,GAA4B,KAE5BD,GAAoB,GACpBC,GAA4B,MAE7B,CAACS,EAAgBV,EAAqBC,IAEzC,IAAM1S,EAAiBuQ,mBAAQ,WAC7B,IAAM5Q,EAAS,IAAIqC,IAOnB,MANA,aAAIqM,GAAW,CAAElG,IAAcrJ,SAAQ,SAACoD,GAAoB,MACpDP,EAAQO,SAAwB,QAAT,EAAfA,EAAiBqG,eAAO,WAAT,EAAf,EAA0B5G,MACpCA,IAAUhC,EAAOiU,IAAIjS,IACvBhC,EAAO0C,IAAIV,MAGR,GAAIhC,KACV,CAAC0O,EAAalG,EAAcxI,IAEzBkU,EAAcP,OAAOC,KAAK5T,GAAQ+F,KAAI,SAAC/D,GAAK,OAAK,IAAIrB,GAAMqB,MAC3DmS,EAA8C,IAAvBzF,EAAY7P,QAAeiD,IAAKa,UAAU+L,EAAY,IAEnF,OAAO2E,EACL,kBAAC,GAAkB,CACjBjB,UAAWA,EACXC,wBAAyB,kBAAMA,EAAwBiB,IACvDhB,mBAAoB,kBAAMA,EAAmBgB,EAAkBlB,EAAWmB,IAC1ED,iBAAkBA,IAGpB,oCACE,kBAAC,EAAW,CACVtT,OAAQkU,EACR7T,eAAgBA,EAChBE,gBAAiBA,EACjBE,iBAAkBA,IAEnBiT,GACC,kBAAC,GAAiB,CAChBrL,WAAYqG,EAAY7P,OAAS,EAAI,KAAO6P,EAAY,IAAM,KAC9DlG,aAAcA,EACdF,QAAM,EACN3F,UAAWwR,MAOrBnB,GAAajT,UAAYA,GAEVqU,sBAAKpB,I,y7CC9GpB,IAAMrS,GAAQrC,OAAOsC,KAAKD,MAEpB0T,GAA0B,CAC9B,0BACA,gCACA,2BACA,sBACA,+BACA,6BACA,uBACA,qCACA,sBAiRaC,UAzQe,SAAH,GAAsC,IAAhC5F,EAAW,EAAXA,YAAalG,EAAY,EAAZA,aACtCxH,EAAWC,cACVH,EAAqB,GAAhBC,cAAgB,GAApB,GAWP,KANGS,aACF,SAACC,GAAK,MAAK,CACTC,IAAU6S,kBAAkB9S,EAAO+S,KAAaC,yBAChD/S,IAAU+Q,cAAchR,EAAO+S,KAAaC,yBAC5C/S,IAAUgT,wBAAwBjT,OAErC,GATCkT,EAAU,KACVrM,EAAM,KACNsM,EAAe,KAQuC,KAAxB1T,mBAAS,CAAEyG,EAAG,EAAGC,EAAG,IAAI,GAAjDiN,EAAQ,KAAEC,EAAW,KACnB7B,EAAmD,GAApC8B,sBAAW,SAACpN,GAAC,OAAKA,EAAI,IAAG,GAAG,kBAAM,KAAE,GAAxC,GAQlB,KAHEnG,aAAY,SAACC,GAAK,MAAK,CACzBC,IAAUsT,0BAA0BvT,GACpCC,IAAUuT,2BAA2BxT,OACrC,GALAyT,EAAsB,KACtBC,EAAuB,KAMnBC,EAAezQ,iBAAO,MAEtB0Q,EAAkBvT,IAAKwT,mBACvBC,EAA2BzT,IAAK0T,uBAqChCC,EAnCiB,WACrB,IAAMC,EAAuBd,EAAgB1N,MAAM,KAC7CyO,EAAS,CAAEC,KAAM,EAAGC,IAAK,GAC/B,GAAgC,QAA5BH,EAAqB,GACvBC,EAAOE,KAAMR,aAAe,EAAfA,EAAiBS,WApCX,IAHY,OAwC1B,CACL,IAAIC,EAAkB,IAClBX,WAAcY,UAChBD,EAAkBX,EAAaY,QAAQC,wBAAwB7F,QAEjEuF,EAAOE,IAAMV,GAA0BI,aAAwB,EAAxBA,EAA0BO,WA1C9C,GA0C6EC,GA7CjE,GAgDjC,GAAgC,UAA5BL,EAAqB,GACvBC,EAAOC,KA/Ce,KA+CRV,EACVG,SAAmBD,KAAcY,UACnCL,EAAOC,KAAOpO,KAAKkD,KACjB2K,aAAe,EAAfA,EAAiBa,aAAab,aAAe,EAAfA,EAAiBc,aAjDhC,IAiDkER,EAAOC,KACxFV,EAAyBE,EAAaY,QAAQC,wBAAwB9F,MAlDvD,SAqDd,CACL,GAAIkF,SAAmBD,KAAcY,QAAS,CAC5C,IAAMI,EAAiBhB,EAAaY,QAAQC,wBAAwB9F,MACpEwF,EAAOC,MAAOP,aAAe,EAAfA,EAAiBa,YAxDd,GAwD8CE,GAxD9C,GAyDbb,GAA4BI,EAAOC,KAAOL,EAAyBW,aACrEP,EAAOC,KAAOL,EAAyBW,WA1DxB,MA6DdP,EAAOC,MAAQS,MAAMV,EAAOC,OAASD,EAAOC,KAAO,KACtDD,EAAOC,KA9DU,IAiErB,OAAOD,EAEKW,GAEdjV,qBAAU,WACRyT,EAAY,CAAEnN,EAAG,EAAGC,EAAG,MACtB,CAACgN,IAEJ,IACQc,EACAa,EAsCFC,EAAwB,SAACtR,EAAG,GAAa,IAAXyC,EAAC,EAADA,EAAGC,EAAC,EAADA,EACrCkN,EAAY,CACVnN,IACAC,OAIEkL,EAAsB3I,uBAAY,SAACkJ,GACvCrS,EAASwC,IAAQiT,sBAAsB,CAAEpD,qBACxC,IAEGN,EAA8B5I,uBAAY,SAAChE,GAC/CkO,GAAwBlV,SAAQ,SAAC2F,GAC/B9D,EACEwC,IAAQkT,0BAA0B5R,EAAa,CAC7CqB,mBAIL,IAIGwQ,EAAiBxM,uBAAY,SAACnI,GAClCA,GAHuB,SAACA,GAAUhB,EAASwC,IAAQoT,iBAAiB5U,IAG3D4U,CAAiB,IAAIjW,GAAMqB,IACpChB,EAASwC,IAAQqT,aAAa,CAACrC,KAAasC,eAC5C9V,EAASwC,IAAQuT,yBAChB,IAEGxW,EAAkB4J,uBAAY,SAAC9J,EAAgB2B,GACnD,IAAMgV,EAAW,IAAIrW,GAAMqB,GAC3B,GAA8B,IAA1B3B,EAAexB,QAAgBwB,EAAe2F,SAAShE,GACzD2U,EAAe3U,OACV,CACL,IAAMiV,EAAU,GAAH,UAAOvI,GAAW,CAAElG,IAC3B0O,EAAgBpV,IAAKqV,oBAAoBC,wBAAwBC,kCAAkC,CAAErV,MAAOgV,EAAUC,YAAW,GACjIK,EAAsB,WAC1BxV,IAAKwV,oBACHN,EAAQ,aACJtI,GAAW,CAAElG,MAGjB0O,EACFK,EAAqBL,EAAeI,GAEpCA,OAGH,CAAC5I,EAAalG,IAEX+O,EAAuB,SAACL,EAAeI,GAC3C,IAmBMrU,EAAU,CACdC,QAnBA,yBAAKC,UAAU,iBACb,2BACE,8BACGrC,EAAE,qDACH,2BAAIoW,GACHpW,EAAE,2DAGP,2BACE,8BACGA,EAAE,uDAUT8B,MALY,GAAH,OAAM9B,EAAE,mDAAkD,YAAIoW,GAMvE7T,eALqBvC,EAAE,kBAMvBwC,UAAW,kBAAMgU,MAEnBtW,EAASwC,IAAQC,mBAAmBR,KAGhCoP,EAA0BlI,uBAAY,SAACmJ,GAC3CxR,IAAK0V,YAAYlE,GACjBR,GAAoB,GACpB9R,EAASwC,IAAQiU,mBAAmBjD,KAAasC,aAAa,MAC7D,IAEGxE,EAAqBnI,uBAAY,SAACmJ,EAAkBlB,EAAWmB,GACnEvS,EAASwC,IAAQiT,sBAAsB,CAAEpD,eAAe,EAAOjB,YAAWmB,sBAC1EvS,EAASwC,IAAQiU,mBAAmBjD,KAAasC,aAAa,IAC9DhV,IAAK0V,YAAYlE,GACjBxR,IAAK4V,kBAAkB,CAAChJ,EAAY,OACnC,CAACA,IAEEjO,EAAmB0J,uBAAY,WACnCwM,IACA3V,EAASwC,IAAQuT,qBAAoB,MACpC,IAIH,OAFiBY,gBAGPhD,GACN,kBAACiD,GAAA,EAAkB,KACjB,kBAAC,GAAY,CACXlJ,YAAaA,EACblG,aAAcA,EACdsK,oBAAqBA,EACrBC,4BAA6BA,EAC7BxS,gBAAiBA,EACjB8R,wBAAyBA,EACzBC,mBAAoBA,EACpB7R,iBAAkBA,EAClBwS,YAAaA,EACblO,SAAU,MAKR4P,GACN,kBAAC,KAAS,CACRE,SAAUA,EACV0B,QA/JEb,EAAuBd,EAAgB1N,MAAM,KAC7CqP,EAAS,CAAEV,IAAK,EAAGgC,OAAQ,EAAGjC,KAAM,EAAGkC,MAAO,GACpB,QAA5BpC,EAAqB,IACvBa,EAAOV,IAAM,EACbU,EAAOsB,OAAS1C,EAA2B4C,GACvC3C,EAAaY,QACfO,EAAOsB,QAAUzC,EAAaY,QAAQC,wBAAwB7F,OAE9DmG,EAAOsB,QArFsB,KAwF/BtB,EAAOV,IAAkCkC,GAA3B5C,EACVC,EAAaY,QACfO,EAAOV,KAAOT,EAAaY,QAAQC,wBAAwB7F,OAE3DmG,EAAOV,KA5FsB,GA8F/BU,EAAOsB,OAAS,GAGc,UAA5BnC,EAAqB,IACvBa,EAAOX,MAAQV,EACfqB,EAAOuB,MAAQ5C,EAAyB,EACpCO,IACFc,EAAOuB,MAAQ5C,EAAyBO,EAAY,QAGtDc,EAAOX,KAAOL,aAAwB,EAAxBA,EAA0BW,WACpCT,IACFc,EAAOX,MAAOL,aAAwB,EAAxBA,EAA0BW,YAAaT,EAAY,KAvGhD,IAyGnBc,EAAOuB,MAAQ5C,EAzGI,GAFc,GA4G7BO,IACFc,EAAOuB,OAASrC,EAAY,OAGzBc,GA4HHyB,OAAQxB,EACRyB,OAAQzB,EACR0B,OAAQ,2CAER,yBACE/U,UAAW2C,IAAW,CACpBqS,SAAS,EACTnF,cAAc,EACdoF,KAAM9P,EACN+P,QAAS/P,IAEXsK,eAAc4B,KAAaC,wBAC3BgB,MAAOA,EACPzQ,IAAKoQ,GAEL,kBAAC,GAAY,CACX1G,YAAaA,EACblG,aAAcA,EACdsK,oBAAqBA,EACrBC,4BAA6BA,EAC7BxS,gBAAiBA,EACjB8R,wBAAyBA,EACzBC,mBAAoBA,EACpB7R,iBAAkBA,EAClBwS,YAAaA,EACblO,SAAU", "file": "chunks/chunk.75.js", "sourcesContent": ["var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./ScaleOverlay.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".ScaleOverlay{position:absolute;z-index:95;border-radius:4px;box-shadow:0 0 3px 0 var(--document-box-shadow);background:var(--component-background)}.open.ScaleOverlay{visibility:visible}.closed.ScaleOverlay{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.MobilePopupWrapper .scale-overlay-calibrate .scale-overlay-footer .calibration-apply:enabled:hover,.ScaleOverlay .scale-overlay-footer .calibration-apply:enabled:hover{background:var(--primary-button-hover);border-color:var(--primary-button-hover);color:var(--gray-0)}.MobilePopupWrapper .scale-overlay-calibrate .scale-overlay-footer .calibration-apply:disabled,.ScaleOverlay .scale-overlay-footer .calibration-apply:disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.MobilePopupWrapper .scale-overlay-calibrate .scale-overlay-footer .calibration-apply:disabled span,.ScaleOverlay .scale-overlay-footer .calibration-apply:disabled span{color:var(--primary-button-text)}.ScaleOverlay .scale-overlay-header .add-new-scale:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6)}.MobilePopupWrapper{display:block;padding:12px 16px}.MobilePopupWrapper .scale-overlay-header{height:32px;display:flex;flex-direction:row;align-items:center;font-size:13px;justify-content:flex-start}.MobilePopupWrapper .scale-overlay-header .scale-overlay-icon{width:24px;height:24px;margin-right:12px}.MobilePopupWrapper .scale-overlay-header .scale-overlay-icon svg{width:24px;height:24px}.MobilePopupWrapper .scale-overlay-header .scale-overlay-title{color:var(--text-color);font-weight:700;font-size:13px;margin-right:16px}.MobilePopupWrapper .scale-overlay-header .add-new-scale{width:100%;padding:var(--padding-small) var(--padding);background-color:transparent;color:var(--secondary-button-text);font-weight:400;border:1px solid;margin-left:12px}.MobilePopupWrapper .scale-overlay-header .add-new-scale span{font-size:13px}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector{position:relative;font-size:13px}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .scale-overlay-selection{width:176px;height:32px;background-color:transparent;border:1px solid var(--border);border-radius:var(--border-radius);line-height:32px;padding:0 var(--padding-small);color:var(--text-color);font-weight:400;font-size:13px;text-align:left;text-overflow:ellipsis;white-space:nowrap;cursor:pointer}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .scale-overlay-selection:hover .scale-overlay-arrow,.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .scale-overlay-selection[focus-within] .scale-overlay-arrow{outline:1px solid var(--primary-button-hover);border-radius:var(--border-radius)}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .scale-overlay-selection:focus-within .scale-overlay-arrow,.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .scale-overlay-selection:hover .scale-overlay-arrow{outline:1px solid var(--primary-button-hover);border-radius:var(--border-radius)}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .scale-overlay-selection:hover .Icon,.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .scale-overlay-selection[focus-within] .Icon{color:var(--primary-button-hover)}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .scale-overlay-selection:focus-within .Icon,.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .scale-overlay-selection:hover .Icon{color:var(--primary-button-hover)}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .scale-overlay-arrow{display:flex;justify-content:center;width:20px;height:20px;color:var(--text-color);cursor:pointer}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .scale-overlay-arrow .Icon{width:20px;height:20px}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .scale-overlay-list{display:flex;grid-row-gap:4px;row-gap:4px;flex-direction:column;border-top:1px solid transparent;width:176px;font-size:13px}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector ul{margin:0;list-style-type:none;position:absolute;min-width:157px;left:0;top:0;text-align:left;letter-spacing:0;display:none;border-radius:var(--border-radius);pointer-events:all;z-index:1000;background-color:var(--component-background);box-shadow:0 0 4px var(--box-shadow);padding-left:0;padding-bottom:var(--padding-small)}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .scale-overlay-selected-arrow{border:0;padding:0;background-color:transparent;width:100%;height:100%}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .scale-overlay-selected-arrow .Icon{margin:auto}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .scale-overlay-item{display:flex;flex-direction:row;align-items:center;justify-content:space-between;height:32px;grid-column-gap:var(--padding-small);-moz-column-gap:var(--padding-small);column-gap:var(--padding-small);font-size:13px}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .scale-overlay-item .options{font-size:13px}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li{display:flex;flex-direction:row;align-items:center;justify-content:space-between;height:32px;line-height:32px;padding-left:var(--padding-small);padding-right:var(--padding-small);border:1px solid transparent;grid-column-gap:var(--padding-small);-moz-column-gap:var(--padding-small);column-gap:var(--padding-small);font-size:13px}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li .add-new-scale{width:100%;padding:var(--padding-small) var(--padding);background-color:transparent;color:var(--secondary-button-text);font-weight:400;border:1px solid;font-size:13px;margin:0}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:first-child:hover .scale-overlay-arrow{outline:1px solid var(--primary-button-hover);border-radius:var(--border-radius)}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:first-child:hover .Icon{color:var(--primary-button-hover)}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):hover,.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child)[focus-within]{background-color:var(--scale-overlay-item-hover)}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):focus-within,.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):hover{background-color:var(--scale-overlay-item-hover)}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):hover .options,.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child)[focus-within] .options{color:var(--dropdown-item-active-text);font-size:13px}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):focus-within .options,.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):hover .options{color:var(--dropdown-item-active-text);font-size:13px}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):hover .Icon,.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child)[focus-within] .Icon{color:var(--dropdown-item-active-icon)}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):focus-within .Icon,.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):hover .Icon{color:var(--dropdown-item-active-icon)}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):hover .delete:hover:not(:disabled) .Icon,.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):hover .delete[focus-within]:not(:disabled) .Icon,.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child)[focus-within] .delete:hover:not(:disabled) .Icon,.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child)[focus-within] .delete[focus-within]:not(:disabled) .Icon{color:var(--icon-color)}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):focus-within .delete:focus-within:not(:disabled) .Icon,.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):focus-within .delete:hover:not(:disabled) .Icon,.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):hover .delete:focus-within:not(:disabled) .Icon,.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):hover .delete:hover:not(:disabled) .Icon{color:var(--icon-color)}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:last-child{height:32px;font-size:13px}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .option-selected{background-color:var(--dropdown-item-active);font-size:13px}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .option-selected .options{color:var(--dropdown-item-active-text);font-size:13px}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .option-selected .Icon{color:var(--dropdown-item-active-icon)}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li .delete{width:24px;height:24px;border:none;background-color:transparent;padding:0;border-radius:var(--border-radius);display:flex;align-items:center;justify-content:center}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li .delete .Icon{width:16px;height:16px;display:flex}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .delete:hover,.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .delete[focus-within]{background-color:var(--popup-button-hover)}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .delete:focus-within,.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .delete:hover{background-color:var(--popup-button-hover)}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .delete:disabled{background-color:transparent;cursor:not-allowed}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li .options{border:none;background-color:transparent;padding:0;display:flex;align-items:center;justify-content:space-between;height:100%;font-size:13px}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .scale-value{margin-right:1px}.MobilePopupWrapper .scale-overlay-calibrate{height:125px}.MobilePopupWrapper .scale-overlay-calibrate .scale-overlay-header{height:32px;margin-bottom:8px;flex-direction:row;align-items:center;justify-content:flex-start;font-size:13px;font-weight:700}.MobilePopupWrapper .scale-overlay-calibrate .scale-overlay-content{font-size:13px;margin-bottom:8px}.MobilePopupWrapper .scale-overlay-calibrate .divider{width:calc(100% + 2.375rem);height:1px;margin-left:-1rem;background:var(--gray-5);margin-top:12px;margin-bottom:16px}.MobilePopupWrapper .scale-overlay-calibrate .scale-overlay-footer{margin-top:var(--padding-medium);display:flex;flex-direction:row;justify-content:space-between;align-items:center}.MobilePopupWrapper .scale-overlay-calibrate .scale-overlay-footer .calibration-cancel{padding:0;background-color:transparent;border-radius:var(--border-radius);width:55px;height:32px;color:var(--secondary-button-text);cursor:pointer;border:none;font-size:13px}:host(:not([data-tabbing=true])) .MobilePopupWrapper .scale-overlay-calibrate .scale-overlay-footer .calibration-cancel,html:not([data-tabbing=true]) .MobilePopupWrapper .scale-overlay-calibrate .scale-overlay-footer .calibration-cancel{outline:none}.MobilePopupWrapper .scale-overlay-calibrate .scale-overlay-footer .calibration-cancel:enabled:hover{color:var(--secondary-button-hover)}.MobilePopupWrapper .scale-overlay-calibrate .scale-overlay-footer .calibration-cancel:disabled{opacity:.5}.MobilePopupWrapper .scale-overlay-calibrate .scale-overlay-footer .calibration-apply{padding:0;border:none;background-color:transparent;background:var(--primary-button);border-radius:var(--border-radius);height:32px;width:66px;color:var(--primary-button-text);cursor:pointer;font-size:13px}:host(:not([data-tabbing=true])) .MobilePopupWrapper .scale-overlay-calibrate .scale-overlay-footer .calibration-apply,html:not([data-tabbing=true]) .MobilePopupWrapper .scale-overlay-calibrate .scale-overlay-footer .calibration-apply{outline:none}.MobilePopupWrapper .MeasurementDetail{word-wrap:break-word;flex-wrap:wrap;font-weight:400;height:168px}.MobilePopupWrapper .MeasurementDetail .header{display:flex;justify-content:left;align-items:center;width:100%;margin-top:var(--padding-medium);font-weight:700;grid-gap:var(--padding-small);gap:var(--padding-small);font-size:13px}.MobilePopupWrapper .MeasurementDetail .header .icon{width:24px;height:24px}.MobilePopupWrapper .MeasurementDetail .scale-input{width:70px;height:24px;margin-right:var(--padding-tiny);padding:var(--padding-tiny);font-size:13px}.MobilePopupWrapper .MeasurementDetail .scale-input:disabled{opacity:.5}.MobilePopupWrapper .MeasurementDetail .measurement__detail-container{margin-left:var(--padding-huge)}.MobilePopupWrapper .MeasurementDetail .measurement__detail-container .measurement__detail-item{display:flex;height:24px;align-items:center;margin:var(--padding-tiny);font-size:13px}.MobilePopupWrapper .MeasurementDetail .distance_input{display:flex;margin-top:var(--padding-small)}.MobilePopupWrapper .MeasurementDetail .distance_input .distance-show{margin-right:var(--padding-small);width:79px;height:32px}.MobilePopupWrapper .MeasurementDetail .distance-show{text-decoration:underline;margin-right:var(--padding-small)}.MobilePopupWrapper .MeasurementDetail .angle_input{display:flex;margin-top:var(--padding-small);text-decoration:underline}.MobilePopupWrapper .MeasurementDetail .measurement__deltas--X,.MobilePopupWrapper .MeasurementDetail .measurement__deltas--Y{display:flex;margin-top:var(--padding-small)}.MobilePopupWrapper .MeasurementDetail .measurement_list{width:64px;font-size:13px}.MobilePopupWrapper .MeasurementDetail .measurement{padding:5px}.ScaleOverlay{flex-direction:column;padding:var(--padding-small) var(--padding-medium)}.ScaleOverlay button{cursor:pointer}.ScaleOverlay .divider{width:calc(100% + 1.375rem);height:1px;margin-left:-1rem;background:var(--gray-5);margin-top:12px}.ScaleOverlay[\\\\:has\\\\(.scale-overlay-calibrate\\\\)]{padding:var(--padding-medium) 0}.ScaleOverlay:has(.scale-overlay-calibrate){padding:var(--padding-medium) 0}.ScaleOverlay[\\\\:has\\\\(.scale-overlay-calibrate\\\\)] .scale-overlay-content,.ScaleOverlay[\\\\:has\\\\(.scale-overlay-calibrate\\\\)] .scale-overlay-footer,.ScaleOverlay[\\\\:has\\\\(.scale-overlay-calibrate\\\\)] .scale-overlay-header{padding:0 var(--padding-medium)}.ScaleOverlay:has(.scale-overlay-calibrate) .scale-overlay-content,.ScaleOverlay:has(.scale-overlay-calibrate) .scale-overlay-footer,.ScaleOverlay:has(.scale-overlay-calibrate) .scale-overlay-header{padding:0 var(--padding-medium)}.ScaleOverlay[\\\\:has\\\\(.scale-overlay-calibrate\\\\)] .divider{width:100%;margin-left:0}.ScaleOverlay:has(.scale-overlay-calibrate) .divider{width:100%;margin-left:0}.ScaleOverlay .scale-overlay-header{position:relative;display:flex;flex-direction:row;justify-content:left;font-weight:400;align-items:center;grid-gap:var(--padding-small);gap:var(--padding-small)}.ScaleOverlay .scale-overlay-header .scale-overlay-title{color:var(--text-color);font-weight:700}.ScaleOverlay .scale-overlay-header .add-new-scale{width:100%;padding:var(--padding-small) var(--padding);background-color:transparent;color:var(--secondary-button-text);font-weight:400;border:1px solid var(--secondary-button-border)}.ScaleOverlay .scale-overlay-header .add-new-scale:hover{border:1px solid var(--secondary-button-hover);box-shadow:none}.ScaleOverlay .scale-overlay-header .scale-overlay-icon,.ScaleOverlay .scale-overlay-header .scale-overlay-icon svg{width:24px;height:24px}.ScaleOverlay .scale-overlay-header .scale-overlay-selector{position:relative}.ScaleOverlay .scale-overlay-header .scale-overlay-selector .scale-overlay-selection{min-width:157px;background-color:transparent;border:1px solid var(--border);border-radius:var(--border-radius);line-height:32px;padding:0 var(--padding-small);color:var(--text-color);font-weight:400;text-align:left;text-overflow:ellipsis;white-space:nowrap;cursor:pointer}.ScaleOverlay .scale-overlay-header .scale-overlay-selector .scale-overlay-selection:hover .scale-overlay-arrow,.ScaleOverlay .scale-overlay-header .scale-overlay-selector .scale-overlay-selection[focus-within] .scale-overlay-arrow{outline:1px solid var(--primary-button-hover);border-radius:var(--border-radius)}.ScaleOverlay .scale-overlay-header .scale-overlay-selector .scale-overlay-selection:focus-within .scale-overlay-arrow,.ScaleOverlay .scale-overlay-header .scale-overlay-selector .scale-overlay-selection:hover .scale-overlay-arrow{outline:1px solid var(--primary-button-hover);border-radius:var(--border-radius)}.ScaleOverlay .scale-overlay-header .scale-overlay-selector .scale-overlay-selection:hover .Icon,.ScaleOverlay .scale-overlay-header .scale-overlay-selector .scale-overlay-selection[focus-within] .Icon{color:var(--primary-button-hover)}.ScaleOverlay .scale-overlay-header .scale-overlay-selector .scale-overlay-selection:focus-within .Icon,.ScaleOverlay .scale-overlay-header .scale-overlay-selector .scale-overlay-selection:hover .Icon{color:var(--primary-button-hover)}.ScaleOverlay .scale-overlay-header .scale-overlay-selector .scale-overlay-arrow{display:flex;justify-content:center;align-items:center;width:24px;height:24px;color:var(--text-color);cursor:pointer}.ScaleOverlay .scale-overlay-header .scale-overlay-selector .scale-overlay-arrow .Icon{width:16px;height:16px}.ScaleOverlay .scale-overlay-header .scale-overlay-selector .scale-overlay-list{display:flex;grid-row-gap:4px;row-gap:4px;flex-direction:column;border-top:1px solid transparent}.ScaleOverlay .scale-overlay-header .scale-overlay-selector ul{margin:0;list-style-type:none;position:absolute;min-width:157px;left:0;top:0;text-align:left;letter-spacing:0;display:none;border-radius:var(--border-radius);pointer-events:all;z-index:1000;background-color:var(--component-background);box-shadow:0 0 4px var(--box-shadow);padding-left:0;padding-bottom:var(--padding-small)}.ScaleOverlay .scale-overlay-header .scale-overlay-selector .scale-overlay-selected-arrow{border:0;padding:0;background-color:transparent;width:100%;height:100%}.ScaleOverlay .scale-overlay-header .scale-overlay-selector .scale-overlay-selected-arrow .Icon{margin:auto}.ScaleOverlay .scale-overlay-header .scale-overlay-selector .scale-overlay-item,.ScaleOverlay .scale-overlay-header .scale-overlay-selector li{display:flex;flex-direction:row;align-items:center;justify-content:space-between;height:32px;grid-column-gap:var(--padding-small);-moz-column-gap:var(--padding-small);column-gap:var(--padding-small)}.ScaleOverlay .scale-overlay-header .scale-overlay-selector li{line-height:32px;padding-left:var(--padding-small);padding-right:var(--padding-small);border:1px solid transparent}.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:first-child:hover .scale-overlay-arrow{outline:1px solid var(--primary-button-hover);border-radius:var(--border-radius)}.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:first-child:hover .Icon{color:var(--primary-button-hover)}.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):hover,.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child)[focus-within]{background-color:var(--scale-overlay-item-hover)}.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):focus-within,.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):hover{background-color:var(--scale-overlay-item-hover)}.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):hover .options,.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child)[focus-within] .options{color:var(--dropdown-item-active-text)}.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):focus-within .options,.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):hover .options{color:var(--dropdown-item-active-text)}.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):hover .Icon,.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child)[focus-within] .Icon{color:var(--dropdown-item-active-icon)}.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):focus-within .Icon,.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):hover .Icon{color:var(--dropdown-item-active-icon)}.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):hover .delete:hover:not(:disabled) .Icon,.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):hover .delete[focus-within]:not(:disabled) .Icon,.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child)[focus-within] .delete:hover:not(:disabled) .Icon,.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child)[focus-within] .delete[focus-within]:not(:disabled) .Icon{color:var(--icon-color)}.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):focus-within .delete:focus-within:not(:disabled) .Icon,.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):focus-within .delete:hover:not(:disabled) .Icon,.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):hover .delete:focus-within:not(:disabled) .Icon,.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):hover .delete:hover:not(:disabled) .Icon{color:var(--icon-color)}.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:last-child{height:32px}.ScaleOverlay .scale-overlay-header .scale-overlay-selector .option-selected{background-color:var(--dropdown-item-active)}.ScaleOverlay .scale-overlay-header .scale-overlay-selector .option-selected .options{color:var(--dropdown-item-active-text)}.ScaleOverlay .scale-overlay-header .scale-overlay-selector .option-selected .Icon{color:var(--dropdown-item-active-icon)}.ScaleOverlay .scale-overlay-header .scale-overlay-selector li .delete{width:24px;height:24px;border:none;background-color:transparent;padding:0;border-radius:var(--border-radius);display:flex;align-items:center;justify-content:center}.ScaleOverlay .scale-overlay-header .scale-overlay-selector li .delete .Icon{width:16px;height:16px;display:flex}.ScaleOverlay .scale-overlay-header .scale-overlay-selector .delete:hover,.ScaleOverlay .scale-overlay-header .scale-overlay-selector .delete[focus-within]{background-color:var(--popup-button-hover)}.ScaleOverlay .scale-overlay-header .scale-overlay-selector .delete:focus-within,.ScaleOverlay .scale-overlay-header .scale-overlay-selector .delete:hover{background-color:var(--popup-button-hover)}.ScaleOverlay .scale-overlay-header .scale-overlay-selector .delete:disabled{background-color:transparent;cursor:not-allowed}.ScaleOverlay .scale-overlay-header .scale-overlay-selector li .options{border:none;background-color:transparent;padding:0;display:flex;align-items:center;justify-content:space-between;height:100%}.ScaleOverlay .scale-overlay-header .scale-overlay-selector .scale-value{margin-right:1px}.ScaleOverlay .scale-overlay-content{width:192px;min-height:32px;font-weight:400;font-size:13px;line-height:16px;margin-top:16px}.ScaleOverlay .scale-overlay-footer{margin-top:var(--padding-medium);display:flex;flex-direction:row;justify-content:space-between;width:220px;align-items:center}.ScaleOverlay .scale-overlay-footer .calibration-cancel{padding:0;background-color:transparent;border-radius:var(--border-radius);width:72px;height:32px;color:var(--secondary-button-text);cursor:pointer;border:none}:host(:not([data-tabbing=true])) .ScaleOverlay .scale-overlay-footer .calibration-cancel,html:not([data-tabbing=true]) .ScaleOverlay .scale-overlay-footer .calibration-cancel{outline:none}.ScaleOverlay .scale-overlay-footer .calibration-cancel:enabled:hover{color:var(--secondary-button-hover)}.ScaleOverlay .scale-overlay-footer .calibration-cancel:disabled{opacity:.5}.ScaleOverlay .scale-overlay-footer .calibration-apply{padding:0;border:none;background-color:transparent;background:var(--primary-button);border-radius:var(--border-radius);height:32px;width:72px;color:var(--primary-button-text);cursor:pointer}:host(:not([data-tabbing=true])) .ScaleOverlay .scale-overlay-footer .calibration-apply,html:not([data-tabbing=true]) .ScaleOverlay .scale-overlay-footer .calibration-apply{outline:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ScaleOverlay .scale-overlay-footer .calibration-apply{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .ScaleOverlay .scale-overlay-footer .calibration-apply{font-size:13px}}.ScaleOverlay .MeasurementDetail{word-wrap:break-word;flex-wrap:wrap;font-weight:400}.ScaleOverlay .MeasurementDetail .header{display:flex;justify-content:left;align-items:center;width:100%;margin-top:var(--padding-medium);font-weight:700;grid-gap:var(--padding-small);gap:var(--padding-small);font-size:13px}.ScaleOverlay .MeasurementDetail .header .icon{width:24px;height:24px}.ScaleOverlay .MeasurementDetail .scale-input{width:70px;height:24px;margin-right:var(--padding-tiny);padding:var(--padding-tiny)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ScaleOverlay .MeasurementDetail .scale-input{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .ScaleOverlay .MeasurementDetail .scale-input{font-size:13px}}.ScaleOverlay .MeasurementDetail .scale-input:disabled{opacity:.5}.ScaleOverlay .MeasurementDetail .measurement__detail-container{margin-left:var(--padding-huge)}.ScaleOverlay .MeasurementDetail .measurement__detail-container .measurement__detail-item{display:flex;height:24px;align-items:center;margin:var(--padding-tiny);font-size:13px}.ScaleOverlay .MeasurementDetail .distance_input{display:flex;margin-top:var(--padding-small)}.ScaleOverlay .MeasurementDetail .distance_input .distance-show{margin-right:var(--padding-small)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ScaleOverlay .MeasurementDetail .distance_input .distance-show{width:79px;height:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .ScaleOverlay .MeasurementDetail .distance_input .distance-show{width:79px;height:32px}}.ScaleOverlay .MeasurementDetail .distance-show{text-decoration:underline;margin-right:var(--padding-small)}.ScaleOverlay .MeasurementDetail .angle_input{display:flex;margin-top:var(--padding-small);text-decoration:underline}.ScaleOverlay .MeasurementDetail .measurement__deltas--X,.ScaleOverlay .MeasurementDetail .measurement__deltas--Y{display:flex;margin-top:var(--padding-small)}.ScaleOverlay .MeasurementDetail .measurement_list{width:64px;font-size:13px}.ScaleOverlay .MeasurementDetail .measurement{padding:5px}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import classNames from 'classnames';\nimport Icon from 'components/Icon';\nimport DataElementWrapper from 'components/DataElementWrapper';\nimport PropTypes from 'prop-types';\nimport React, { useState, useRef, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport actions from 'actions';\nimport { useDispatch, useSelector } from 'react-redux';\nimport selectors from 'selectors';\nimport core from 'core';\nimport useOnClickOutside from 'hooks/useOnClickOutside';\nimport Button from 'components/Button';\n\nconst propTypes = {\n  scales: PropTypes.arrayOf(PropTypes.object).isRequired,\n  selectedScales: PropTypes.arrayOf(PropTypes.string).isRequired,\n  onScaleSelected: PropTypes.func.isRequired,\n  onAddingNewScale: PropTypes.func.isRequired,\n  ariaLabelledBy: PropTypes.string,\n};\n\nconst Scale = window.Core.Scale;\n\nconst ScaleSelector = ({ scales = [], selectedScales = [], onScaleSelected, onAddingNewScale, ariaLabelledBy }) => {\n  const [t] = useTranslation();\n  const dispatch = useDispatch();\n\n  const [scalesInfo, setScalesInfo] = useState([]);\n\n  useEffect(() => {\n    if (!scales || scales.length === 0) {\n      setScalesInfo([]);\n      return;\n    }\n\n    const scalesInfo = getScalesInfo(scales);\n    setScalesInfo(scalesInfo);\n  }, [scales]);\n\n  const isMultipleScalesMode = useSelector((state) => selectors.getIsMultipleScalesMode(state));\n  const isMultipleScales = selectedScales.length > 1;\n\n  const getScalesInfo = (scales) => {\n    const scalesInfo = [];\n    const coreScales = core.getScales();\n\n    scales.forEach((scale) => {\n      const scaleData = coreScales[scale.toString()];\n      const measurements = [];\n      const relatedPages = new Set();\n      let canDelete = true;\n\n      scaleData.forEach((measurementItem) => {\n        const isAnnotation = measurementItem instanceof window.Core.Annotations.Annotation;\n        if (!isAnnotation) {\n          return;\n        }\n\n        relatedPages.add(measurementItem['PageNumber']);\n        measurements.push(measurementItem);\n\n        // If any associated measurement cannot be deleted then we won't allow this scale to be deleted.\n        if (!core.canModify(measurementItem)) {\n          canDelete = false;\n        }\n      });\n\n      scalesInfo.push({\n        scale: scale,\n        title: scale.toString(),\n        measurementsNum: measurements.length,\n        pages: [...relatedPages],\n        canDelete\n      });\n    });\n\n    return scalesInfo;\n  };\n\n  const openScaleDeletionModal = (scaleInfo) => {\n    if (!scaleInfo) {\n      return;\n    }\n\n    const hasAssociatedMeasurements = !!scaleInfo.pages.length;\n    const message = hasAssociatedMeasurements ? (\n      <div className='customMessage'>\n        <p>\n          <span>\n            {t('option.measurement.deleteScaleModal.scaleIsOn-delete-info')}\n            <b>{` ${t('option.measurement.deleteScaleModal.page-delete-info')} ${scaleInfo.pages.join(', ')} `}</b>\n            {t('option.measurement.deleteScaleModal.appliedTo-delete-info')}\n            <b>{` ${scaleInfo.measurementsNum} ${scaleInfo.measurementsNum > 1 ? t('option.measurement.deleteScaleModal.measurements') : t('option.measurement.deleteScaleModal.measurement')}.`}</b>\n          </span>\n          <span> </span>\n          <span>\n            {t('option.measurement.deleteScaleModal.deletionIs')}\n            <b>{` ${t('option.measurement.deleteScaleModal.irreversible')} `}</b>\n            {t('option.measurement.deleteScaleModal.willDeleteMeasurement')}\n          </span>\n        </p>\n        <p>\n          {t('option.measurement.deleteScaleModal.confirmDelete')}\n        </p>\n      </div>\n    ) : (\n      <div className='customMessage'>\n        <p>\n          <span>\n            {t('option.measurement.deleteScaleModal.confirmDelete')}\n            {t('option.measurement.deleteScaleModal.thisCantBeUndone')}\n          </span>\n        </p>\n      </div>\n    );\n    const title = `${t('option.measurement.deleteScaleModal.deleteScale')} ${scaleInfo.title}`;\n    const confirmBtnText = t('action.confirm');\n\n    const warning = {\n      message,\n      title,\n      confirmBtnText,\n      onConfirm: () => core.deleteScale(scaleInfo.scale)\n    };\n    dispatch(actions.showWarningMessage(warning));\n  };\n\n  const renderScale = (scale) => {\n    const precision = core.getScalePrecision(scale);\n    const pageScaleStr = Scale.getFormattedValue(scale.pageScale.value, scale.pageScale.unit, precision, false);\n    const worldScaleStr = Scale.getFormattedValue(scale.worldScale.value, scale.worldScale.unit, precision, false);\n    const scaleDisplay = `${pageScaleStr} = ${worldScaleStr}`;\n\n    return <div>{scaleDisplay}</div>;\n  };\n\n  let title = t('option.measurement.scaleOverlay.multipleScales');\n\n  if (selectedScales.length && !isMultipleScales) {\n    const selectedScale = new Scale(selectedScales[0]);\n    title = renderScale(selectedScale);\n  }\n\n  // TODO: This is a bandaid solution to fix a Safari bug. This dropdown should be refactored to use a react-select component\n  // instead of hiding and displaying based on focus pseudoclasses,\n  // otherwise it is hard to debug as the open/close logic is in a CSS stylesheet and not super evident\n  const [isDropDownOpen, setOpenDropDown] = useState(false);\n  const toggleDropdown = () => {\n    setOpenDropDown((prevValue) => !prevValue);\n  };\n\n  const selectorRef = useRef(null);\n\n  useOnClickOutside(selectorRef, () => {\n    setOpenDropDown(false);\n  });\n\n  const handleKeyDown = (e) => {\n    if (e.key === 'Enter' || e.key === ' ') {\n      toggleDropdown();\n    }\n  };\n\n  return (\n    <DataElementWrapper\n      className=\"scale-overlay-selector\"\n      dataElement=\"scaleSelector\"\n      tabIndex={-1}\n      ref={selectorRef}\n      onKeyDown={handleKeyDown}\n      onClick={toggleDropdown}\n    >\n      {/* Cleanup this <div> to a <select> https://apryse.atlassian.net/browse/WVR-7613 */}\n      <div\n        data-testid='scale-selector'\n        className=\"scale-overlay-selection\"\n        aria-expanded={isDropDownOpen}\n        aria-labelledby={ariaLabelledBy}\n        aria-controls=\"scale-overlay-dropdown\"\n        role=\"combobox\"\n        tabIndex={0}\n      >\n        <div className=\"scale-overlay-item\">\n          <div className=\"scale-overlay-name\">\n            {title}\n          </div>\n          <div className=\"scale-overlay-arrow\">\n            <Icon glyph=\"icon-chevron-down\" ariaHidden={true} />\n          </div>\n        </div>\n      </div>\n      {isDropDownOpen && (\n        <ul id=\"scale-overlay-dropdown\" className={classNames('scale-overlay-list')} >\n          <li>\n            <div className=\"scale-overlay-name\">{title}</div>\n            <div className=\"scale-overlay-arrow\">\n              <button\n                className=\"scale-overlay-selected-arrow\"\n              >\n                <Icon glyph=\"icon-chevron-up\" ariaHidden={true}/>\n              </button>\n            </div>\n          </li>\n          {scalesInfo.map((scaleInfo) => (\n            <li key={scaleInfo.title} className={classNames({\n              'className=\"scale-overlay-item': true,\n              'option-selected': selectedScales.includes(scaleInfo.title)\n            })}>\n              <button\n                className={classNames({\n                  options: true,\n                })}\n                onClick={() => onScaleSelected(selectedScales, scaleInfo.title)}\n                onKeyDown={(e) => {\n                  if (e.key === 'Enter' || e.key === ' ') {\n                    e.preventDefault();\n                    onScaleSelected(selectedScales, scaleInfo.title);\n                  }\n                }}\n              >\n                {renderScale(scaleInfo.scale)}\n              </button>\n              <button\n                className=\"delete\"\n                tabIndex={0}\n                onClick={(e) => {\n                  e.preventDefault();\n                  e.stopPropagation();\n                  openScaleDeletionModal(scaleInfo);\n                }}\n                onKeyDown={(e) => {\n                  if (e.key === 'Enter' || e.key === ' ') {\n                    e.preventDefault();\n                    openScaleDeletionModal(scaleInfo);\n                  }\n                }}\n                disabled={scalesInfo.length <= 1 || !scaleInfo.canDelete}\n                aria-label={`${t('action.delete')} ${scaleInfo.title}`}\n              >\n                <Icon glyph=\"icon-delete-line\" />\n              </button>\n            </li>\n          ))}\n          {isMultipleScalesMode && (\n            <li>\n              <Button onClick={onAddingNewScale} label={t('option.measurement.scaleOverlay.addNewScale')} className=\"add-new-scale\" />\n            </li>\n          )}\n        </ul>\n      )}\n    </DataElementWrapper>\n  );\n};\n\nScaleSelector.propTypes = propTypes;\n\nexport default ScaleSelector;\n", "import React from 'react';\nimport Button from 'components/Button';\nimport Icon from 'components/Icon';\nimport ScaleSelector from './ScaleSelector';\nimport PropTypes from 'prop-types';\nimport { useTranslation } from 'react-i18next';\nimport useFocusHandler from 'hooks/useFocusHandler';\n\nconst propTypes = {\n  scales: PropTypes.arrayOf(PropTypes.object).isRequired,\n  selectedScales: PropTypes.arrayOf(PropTypes.string).isRequired,\n  onScaleSelected: PropTypes.func.isRequired,\n  onAddingNewScale: PropTypes.func.isRequired,\n};\n\nconst ScaleHeader = ({ scales, selectedScales, onScaleSelected, onAddingNewScale }) => {\n  const [t] = useTranslation();\n\n  const onClickFocusWrapped = useFocusHandler(onAddingNewScale);\n\n  return (\n    <div className=\"scale-overlay-header\">\n      <Icon glyph=\"ic-calibrate\" className=\"scale-overlay-icon\" />\n      <h4 id=\"scale-dropdown-label\" className=\"scale-overlay-title\">{t('option.measurementOption.scale')}</h4>\n      {scales.length ? (\n        <ScaleSelector\n          scales={scales}\n          selectedScales={selectedScales}\n          onScaleSelected={onScaleSelected}\n          onAddingNewScale={onAddingNewScale}\n          ariaLabelledBy=\"scale-dropdown-label\"\n        />\n      ) : (\n        <Button className=\"add-new-scale\" onClick={onClickFocusWrapped} dataElement=\"addNewScale\" label={t('option.measurement.scaleOverlay.addNewScale')} />\n      )}\n    </div>\n  );\n};\n\nScaleHeader.propTypes = propTypes;\nexport default ScaleHeader;\n", "export const measurementTypeTranslationMap = {\n  distanceMeasurement: 'option.measurementOverlay.distanceMeasurement',\n  perimeterMeasurement: 'option.measurementOverlay.perimeterMeasurement',\n  areaMeasurement: 'option.measurementOverlay.areaMeasurement',\n  rectangularAreaMeasurement: 'option.measurementOverlay.areaMeasurement',\n  cloudyRectangularAreaMeasurement: 'option.measurementOverlay.areaMeasurement',\n  ellipseMeasurement: 'option.measurementOverlay.areaMeasurement',\n  arcMeasurement: 'option.measurementOverlay.arcMeasurement',\n};", "export default (precision) => (precision === 1 ? 0 : precision?.toString().split('.')[1].length);\n", "export default (pt1, pt2, pt3) => {\n  let angle;\n\n  if (pt1 && pt2) {\n    if (pt3) {\n      // calculate the angle using Law of cosines\n      const AB = Math.sqrt(Math.pow(pt2.x - pt1.x, 2) + Math.pow(pt2.y - pt1.y, 2));\n      const BC = Math.sqrt(Math.pow(pt2.x - pt3.x, 2) + Math.pow(pt2.y - pt3.y, 2));\n      const AC = Math.sqrt(Math.pow(pt3.x - pt1.x, 2) + Math.pow(pt3.y - pt1.y, 2));\n      angle = Math.acos((BC * BC + AB * AB - AC * AC) / (2 * BC * AB));\n    } else {\n      // if there are only two points returns the angle in the plane (in radians) between the positive x-axis and the ray from (0,0) to the point (x,y)\n      angle = Math.atan2(pt2.y - pt1.y, pt2.x - pt1.x);\n      // keep the angle range between 0 and Math.PI / 2\n      angle = Math.abs(angle);\n      angle = angle > Math.PI / 2 ? Math.PI - angle : angle;\n    }\n  }\n\n  return angle;\n};\n", "export default (unit) => {\n  switch (unit) {\n    case 'ft\\'':\n      return 'ft';\n    case 'in\"':\n      return 'in';\n    case 'ft-in':\n      return 'ft';\n    default:\n      return unit;\n  }\n};\n", "import React, { useEffect, useState, useCallback } from 'react';\nimport { useSelector } from 'react-redux';\nimport PropTypes from 'prop-types';\nimport { useTranslation } from 'react-i18next';\nimport core from 'core';\nimport { isMobileDevice } from 'helpers/device';\nimport selectors from 'selectors';\nimport getAngleInRadians from 'helpers/getAngleInRadians';\nimport getFormattedUnit from 'helpers/getFormattedUnit';\n\nconst unitMap = {\n  'in\\\"': 'in',\n  'ft\\'': 'ft'\n};\n\nLineMeasurementInput.propTypes = {\n  annotation: PropTypes.object,\n  isOpen: PropTypes.bool.isRequired,\n  selectedTool: PropTypes.object,\n  canModify: PropTypes.bool,\n};\n\nconst Scale = window.Core.Scale;\n\nfunction LineMeasurementInput({ annotation, isOpen, selectedTool, canModify }) {\n  const [t] = useTranslation();\n  const isReadOnly = useSelector((state) => selectors.isDocumentReadOnly(state));\n  const factor = annotation?.Measure.axis[0].factor;\n  const unit = annotation?.DisplayUnits[0] || selectedTool?.Measure?.unit;\n  const [length, setLength] = useState((annotation?.getLineLength() * factor || 0).toFixed(2));\n\n  useEffect(() => {\n    if (!annotation) {\n      setAngle(computeAngle());\n      return;\n    }\n    const onAnnotationChanged = () => {\n      setLength((annotation.getLineLength() * factor).toFixed(2));\n      setAngle(computeAngle());\n    };\n    core.addEventListener('mouseMove', onAnnotationChanged);\n\n    return () => {\n      core.removeEventListener('mouseMove', onAnnotationChanged);\n    };\n  }, [annotation, computeAngle, factor, selectedTool]);\n\n  const onInputChanged = (event) => {\n    setLength(event.target.value);\n    validateLineLength(event);\n    finishAnnotation();\n  };\n\n  const finishAnnotation = () => {\n    const tool = core.getTool('AnnotationCreateDistanceMeasurement');\n    tool.finish();\n  };\n\n  const selectAnnotation = () => {\n    const annotationManager = core.getAnnotationManager();\n    annotationManager.selectAnnotation(annotation);\n  };\n\n  const deselectAnnotation = () => {\n    const annotationManager = core.getAnnotationManager();\n    annotationManager.deselectAnnotation(annotation);\n  };\n\n  const validateLineLength = (event) => {\n    if (!annotation) {\n      return;\n    }\n    let length = Math.abs(event.target.value);\n    if (length < annotation.Precision) {\n      length = annotation.Precision;\n      setLength(length);\n    }\n    const factor = annotation.Measure.axis[0].factor;\n    const lengthInPts = length / factor;\n    ensureLineIsWithinBounds(lengthInPts);\n  };\n\n  const isApproximatelyEqual = (value1, value2) => {\n    return Math.abs(value1 - value2) < 0.1;\n  };\n\n  const ensureLineIsWithinBounds = useCallback(\n    (lengthInPts) => {\n      if (!isApproximatelyEqual(annotation.getLineLength(), lengthInPts)) {\n        const maxLengthInPts = getMaxLineLengthInPts();\n        annotation.setLineLength(Math.min(maxLengthInPts, lengthInPts));\n        forceLineRedraw();\n      }\n    },\n    [annotation, forceLineRedraw, getMaxLineLengthInPts],\n  );\n\n  const getAnnotationUnit = (annotation) => {\n    let annotUnit;\n    if (annotation?.DisplayUnits?.length) {\n      if (annotation.DisplayUnits.length === 2 && annotation.DisplayUnits[0] === \"ft'\" && annotation.DisplayUnits[1] === 'in\"') {\n        annotUnit = 'in';\n      } else {\n        annotUnit = annotation.DisplayUnits[0];\n      }\n    }\n    return unitMap[annotUnit] || annotUnit || unitMap[unit] || unit;\n  };\n\n  const renderDeltas = () => {\n    const angle = (annotation && getAngleInRadians(annotation.Start, annotation.End)) || 0;\n    const unit = getAnnotationUnit(annotation);\n    const deltaX = Scale.getFormattedValue(annotation && Math.abs(length * Math.cos(angle)), unit, annotation?.Precision);\n    const deltaY = Scale.getFormattedValue(annotation && Math.abs(length * Math.sin(angle)), unit, annotation?.Precision);\n\n    return (\n      <>\n        <div className=\"measurement__detail-item\">\n          <div className=\"measurement_list\">{t('option.measurementOverlay.xAxis')}:</div>\n          <div className='measurement'>\n            {deltaX}\n          </div>\n        </div>\n        <div className=\"measurement__detail-item\">\n          <div className=\"measurement_list\">{t('option.measurementOverlay.yAxis')}:</div>\n          <div className='measurement'>\n            {deltaY}\n          </div>\n        </div>\n      </>\n    );\n  };\n\n  const forceLineRedraw = useCallback(() => {\n    const annotationManager = core.getAnnotationManager();\n    annotationManager.drawAnnotations(annotation.PageNumber);\n    annotationManager.trigger('annotationChanged', [[annotation], 'modify', {}]);\n  }, [annotation]);\n\n  const getMaxLineLengthInPts = useCallback(() => {\n    const currentPageNumber = core.getCurrentPage();\n    const documentWidth = core.getPageWidth(currentPageNumber);\n    const documentHeight = core.getPageHeight(currentPageNumber);\n    const angleInDegrees = annotation.getAngle() * (180 / Math.PI).toFixed(2);\n    const startPoint = annotation.getStartPoint();\n    const startX = startPoint.x;\n    const startY = startPoint.y;\n\n    let maxX;\n    let maxY;\n    if (Math.abs(angleInDegrees) < 90) {\n      maxX = documentWidth;\n    } else {\n      maxX = 0;\n    }\n\n    if (angleInDegrees > 0) {\n      maxY = documentHeight;\n    } else {\n      maxY = 0;\n    }\n\n    const maxLenX = Math.abs((maxX - startX) / Math.cos(annotation.getAngle()));\n    const maxLenY = Math.abs((maxY - startY) / Math.sin(annotation.getAngle()));\n\n    return Math.min(maxLenX, maxLenY);\n  }, [annotation]);\n\n  const setLineAngle = (event) => {\n    const angle = event.target.value;\n    const angleInRadians = angle * (Math.PI / 180) * -1;\n    const lengthInPts = annotation.getLineLength();\n    const start = annotation.Start;\n    const endX = Math.cos(angleInRadians) * lengthInPts + start.x;\n    const endY = Math.sin(angleInRadians) * lengthInPts + start.y;\n    annotation.setEndPoint(endX, endY);\n    annotation.adjustRect();\n    forceLineRedraw();\n  };\n\n  const onAngleChange = (event) => {\n    setAngle(event.target.value);\n    setLineAngle(event);\n    finishAnnotation();\n  };\n\n  const computeAngle = useCallback(() => {\n    if (!annotation) {\n      return 0;\n    }\n    let angleInRadians = annotation.getAngle();\n    // Multiply by -1 to achieve 0-360 degrees counterclockwise\n    angleInRadians *= -1;\n    angleInRadians = angleInRadians < 0 ? angleInRadians + 2 * Math.PI : angleInRadians;\n    return ((angleInRadians / Math.PI) * 180).toFixed(2);\n  }, [annotation]);\n\n  const [angle, setAngle] = useState(computeAngle());\n\n  useEffect(() => {\n    if (!isOpen) {\n      ensureLineIsWithinBounds(annotation.getLineLength());\n    }\n  }, [annotation, ensureLineIsWithinBounds, isOpen]);\n\n  return (\n    <>\n      <div className=\"measurement__detail-item\">\n        <div className=\"measurement_list\">\n          {t('option.measurementOverlay.distance')}:\n        </div>\n        <input\n          className=\"scale-input\"\n          type=\"number\"\n          min=\"0\"\n          disabled={isReadOnly || !annotation || !canModify}\n          value={!annotation ? 0 : length}\n          autoFocus={!isMobileDevice}\n          onChange={(event) => {\n            onInputChanged(event);\n            selectAnnotation();\n          }}\n          onBlur={(event) => {\n            validateLineLength(event);\n          }}\n          onKeyDown={(event) => {\n            if (event.key === 'Enter') {\n              onInputChanged(event);\n              deselectAnnotation();\n            }\n          }}\n        />\n        {getFormattedUnit(unit)}\n      </div>\n      <div className=\"measurement__detail-item\">\n        <div className=\"measurement_list\">{t('option.measurementOverlay.angle')}:</div>\n        <input\n          className=\"scale-input\"\n          type=\"number\"\n          min=\"0\"\n          max=\"360\"\n          disabled={isReadOnly || !annotation || !canModify}\n          value={angle}\n          autoFocus={!isMobileDevice}\n          onChange={(event) => {\n            onAngleChange(event);\n            selectAnnotation();\n          }}\n          onKeyDown={(event) => {\n            if (event.key === 'Enter') {\n              onAngleChange(event);\n              deselectAnnotation();\n            }\n          }}\n        />\n            &deg;\n      </div>\n      {renderDeltas()}\n    </>\n  );\n}\n\nexport default LineMeasurementInput;\n", "import core from 'core';\nimport getNumberOfDecimalPlaces from 'helpers/getNumberOfDecimalPlaces';\nimport { isMobileDevice } from 'src/helpers/device';\nimport { precisionFractions } from 'constants/measurementScale';\nimport PropTypes from 'prop-types';\nimport selectors from 'selectors';\nimport { useSelector } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\nimport React, { useState, useEffect, useCallback } from 'react';\nimport getFormattedUnit from 'src/helpers/getFormattedUnit';\n\nconst propTypes = {\n  annotation: (props, propName, componentName) => {\n    if (!props.annotation && !props.selectedTool) {\n      return new Error(`One of props 'annotation' or 'selectedTool' was not specified in '${componentName}'.`);\n    }\n    if (props.annotation) {\n      PropTypes.checkPropTypes(\n        {\n          annotation: PropTypes.shape({\n            Precision: PropTypes.number,\n            DisplayUnits: PropTypes.arrayOf(PropTypes.string),\n            getContents: PropTypes.func,\n          }),\n        },\n        { annotation: props.annotation },\n        'prop',\n        'EllipseMeasurementOverlay',\n      );\n    }\n    return null;\n  },\n  selectedTool: (props, propName, componentName) => {\n    if (!props.annotation && !props.selectedTool) {\n      return new Error(`One of props 'annotation' or 'selectedTool' was not specified in '${componentName}'.`);\n    }\n    if (props.selectedTool) {\n      PropTypes.checkPropTypes(\n        {\n          selectedTool: PropTypes.shape({\n            defaults: PropTypes.shape({\n              Precision: PropTypes.number,\n            }),\n            Measure: PropTypes.shape({\n              unit: PropTypes.string,\n            }),\n          }),\n        },\n        { selectedTool: props.selectedTool },\n        'prop',\n        'EllipseMeasurementOverlay',\n      );\n    }\n    return null;\n  },\n  isOpen: PropTypes.bool.isRequired,\n  canModify: PropTypes.bool,\n};\n\nfunction EllipseMeasurementOverlay({ annotation, isOpen, selectedTool, canModify }) {\n  const { t } = useTranslation();\n\n  const isReadOnly = useSelector((state) => selectors.isDocumentReadOnly(state));\n  const area = annotation?.getMeasurementTextWithScaleAndUnits?.() || 0;\n  const data = {\n    precision: !annotation ? selectedTool?.defaults?.Precision : annotation.Precision,\n    unit: getFormattedUnit((annotation?.Scale || selectedTool?.defaults?.Scale)?.[1][1]),\n    area,\n  };\n\n  const refreshRadius = () => {\n    setRadius(computeRadius());\n  };\n\n  useEffect(() => {\n    refreshRadius();\n    core.addEventListener('mouseMove', refreshRadius);\n\n    return () => {\n      core.removeEventListener('mouseMove', refreshRadius);\n    };\n  }, [annotation]);\n\n  useEffect(() => {\n    const onAnnotationDeselected = (annotations, action) => {\n      if (action === 'deselected') {\n        const annotation = annotations[0];\n        ensureDiameterIsWithinBounds(annotation.getWidth(), annotation);\n      }\n    };\n\n    core.addEventListener('annotationSelected', onAnnotationDeselected);\n\n    return () => {\n      core.removeEventListener('annotationSelected', onAnnotationDeselected);\n    };\n  }, []);\n\n  const computeRadius = () => {\n    if (!annotation) {\n      return 0;\n    }\n    const decimalPlaces = annotation && getNumberOfDecimalPlaces(annotation.Precision) || 0;\n    const factor = annotation.Measure.axis[0].factor;\n    const radiusInPts = (annotation.Width / 2).toFixed(decimalPlaces);\n    return (radiusInPts * factor).toFixed(decimalPlaces);\n  };\n\n  const finishAnnotation = () => {\n    const tool = core.getTool('AnnotationCreateEllipseMeasurement');\n    tool.finish();\n  };\n\n  const selectAnnotation = () => {\n    const annotationManager = core.getAnnotationManager();\n    annotationManager.selectAnnotation(annotation);\n  };\n\n  const deselectAnnot = () => {\n    const annotationManager = core.getAnnotationManager();\n    annotationManager.deselectAnnotation(annotation);\n  };\n\n  const onChangeRadiusLength = (event) => {\n    const eventValue = event.target.value || 0;\n    const radius = eventValue > 0 ? eventValue : 0.0001;\n    const factor = annotation.Measure.axis[0].factor;\n    const radiusInPts = radius / factor;\n    const diameterInPts = radiusInPts * 2;\n    const rect = annotation.getRect();\n    let { X1, X2, Y1, Y2 } = 0;\n    X1 = rect['x1'];\n    Y1 = rect['y1'];\n    X2 = rect['x1'] + diameterInPts;\n    Y2 = rect['y1'] + diameterInPts;\n    const newRect = { x1: X1, y1: Y1, x2: X2, y2: Y2 };\n\n    annotation.setHeight(diameterInPts);\n    annotation.setWidth(diameterInPts);\n    annotation.resize(newRect);\n    setRadius(radius);\n    forceEllipseRedraw(annotation);\n    finishAnnotation();\n  };\n\n  const forceEllipseRedraw = (annotation) => {\n    const annotationManager = core.getAnnotationManager();\n    annotationManager.redrawAnnotation(annotation);\n    annotationManager.trigger('annotationChanged', [[annotation], 'modify', []]);\n  };\n\n  const getMaxDiameterInPts = useCallback((annotation) => {\n    const currentPageNumber = core.getCurrentPage();\n    const documentWidth = core.getPageWidth(currentPageNumber);\n    const documentHeight = core.getPageHeight(currentPageNumber);\n    const startX = annotation['X'];\n    const startY = annotation['Y'];\n\n    const maxX = documentWidth - startX;\n    const maxY = documentHeight - startY;\n\n    return Math.min(maxX, maxY);\n  });\n\n  const validateDiameter = (event) => {\n    const radius = Math.abs(event.target.value);\n    const factor = annotation.Measure.axis[0].factor;\n    const radiusInPts = radius / factor;\n    const diameterInPts = radiusInPts * 2;\n    ensureDiameterIsWithinBounds(diameterInPts, annotation);\n    refreshRadius();\n  };\n\n  const ensureDiameterIsWithinBounds = useCallback((diameterInPts, annotation) => {\n    const maxDiameterInPts = getMaxDiameterInPts(annotation);\n\n    if (diameterInPts > maxDiameterInPts) {\n      const boundingRect = annotation.getRect();\n      const { x1, x2, y1, y2 } = boundingRect;\n      let width = annotation.Width;\n      let height = annotation.Height;\n      const currentPageNumber = core.getCurrentPage();\n      const documentWidth = core.getPageWidth(currentPageNumber);\n      const documentHeight = core.getPageHeight(currentPageNumber);\n\n      if (x2 > documentWidth) {\n        boundingRect['x2'] = documentWidth;\n        width = documentWidth - x1;\n      }\n      if (y2 > documentHeight) {\n        boundingRect['y2'] = documentHeight;\n        height = documentHeight - y1;\n      }\n\n      if (width < documentWidth) {\n        annotation.setWidth(width);\n      } else {\n        annotation.setWidth(documentWidth);\n      }\n      if (height < documentHeight) {\n        annotation.setHeight(height);\n      } else {\n        annotation.setHeight(documentHeight);\n      }\n      annotation.resize(boundingRect);\n      forceEllipseRedraw(annotation);\n    }\n  }, [getMaxDiameterInPts]);\n\n  useEffect(() => {\n    if (!isOpen) {\n      ensureDiameterIsWithinBounds(annotation.getWidth(), annotation);\n    }\n  }, [annotation, ensureDiameterIsWithinBounds, isOpen]);\n\n  const [radius, setRadius] = useState(computeRadius());\n\n  return (\n    <div className=\"measurement__detail-container\">\n      <div className=\"measurement__detail-item\">\n        <div className=\"measurement_list\">{t('option.shared.precision')}:</div>\n        <div className='measurement'>\n          {precisionFractions[data.precision] || data.precision}\n        </div>\n      </div>\n      <div className=\"measurement__detail-item\">\n        <div className=\"measurement_list\">{t('option.measurementOverlay.area')}:</div>\n        <div className='measurement'>\n          {data.area}\n        </div>\n      </div>\n      <div className=\"measurement__detail-item\">\n        <div className=\"measurement_list\">{t('option.measurementOverlay.radius')}:</div>\n        <input\n          autoFocus={!isMobileDevice}\n          className=\"scale-input\"\n          type=\"number\"\n          min=\"0\"\n          disabled={isReadOnly || !annotation || !canModify}\n          value={radius}\n          onChange={(event) => {\n            onChangeRadiusLength(event);\n            selectAnnotation();\n          }}\n          onBlur={(event) => validateDiameter(event)}\n          onKeyDown={(event) => {\n            if (event.key === 'Enter') {\n              onChangeRadiusLength(event);\n              deselectAnnot();\n            }\n          }}\n        />\n        {data.unit}\n      </div>\n    </div>\n  );\n}\n\nEllipseMeasurementOverlay.propTypes = propTypes;\n\nexport default EllipseMeasurementOverlay;\n", "import React, { useMemo } from 'react';\nimport PropTypes from 'prop-types';\nimport { useTranslation } from 'react-i18next';\nimport { mapAnnotationToKey, mapToolNameToKey, getDataWithKey } from 'constants/map';\nimport { measurementTypeTranslationMap } from 'constants/measurementTypes';\nimport { precisionFractions } from 'constants/measurementScale';\nimport getNumberOfDecimalPlaces from 'helpers/getNumberOfDecimalPlaces';\nimport Icon from 'components/Icon';\nimport LineMeasurementInput from 'components/MeasurementOverlay/LineMeasurementInput';\nimport EllipseMeasurementOverlay from 'components/MeasurementOverlay/EllipseMeasurementOverlay';\nimport getAngleInRadians from 'helpers/getAngleInRadians';\n\nimport './ScaleOverlay.scss';\n\nconst propTypes = {\n  annotation: PropTypes.object,\n  isOpen: PropTypes.bool.isRequired,\n  selectedTool: PropTypes.object,\n  canModify: PropTypes.bool,\n};\n\nconst MeasurementDetail = ({ annotation, isOpen, selectedTool, canModify }) => {\n  const { t } = useTranslation();\n\n  const data = useMemo(() => {\n    const key = annotation ? mapAnnotationToKey(annotation) : mapToolNameToKey(selectedTool.name);\n\n    return {\n      key,\n      icon: getDataWithKey(key).icon,\n      color: annotation ? annotation.Color.toHexString() : selectedTool?.defaults?.StrokeColor?.toHexString(),\n      contents: annotation ? annotation.getMeasurementTextWithScaleAndUnits?.() : 0,\n      precision: !annotation ? selectedTool?.defaults?.Precision : annotation.Precision,\n    };\n  });\n\n  const renderTitle = () => {\n    const { key, icon, color } = data;\n    const translationKey = measurementTypeTranslationMap[key];\n\n    return (\n      <div className=\"header\">\n        <Icon glyph={icon} color={color} className=\"icon\" />\n        <div>{t(translationKey)}</div>\n      </div>\n    );\n  };\n\n  const renderValue = () => {\n    const { key, contents } = data;\n\n    const keyDisplayNameMap = {\n      distanceMeasurement: t('option.measurementOverlay.distance'),\n      perimeterMeasurement: t('option.measurementOverlay.perimeter'),\n      areaMeasurement: t('option.measurementOverlay.area'),\n      rectangularAreaMeasurement: t('option.measurementOverlay.area'),\n      cloudyRectangularAreaMeasurement: t('option.measurementOverlay.area'),\n    };\n\n    return (\n      <div className=\"measurement__detail-item\">\n        <div className=\"measurement_list\">{keyDisplayNameMap[key]}:</div>\n        <div className=\"measurement\">\n          {contents}\n        </div>\n      </div>\n    );\n  };\n\n  const renderAngle = () => {\n    if (!annotation) {\n      return (\n        <div className=\"measurement__detail-item\">\n          <div className=\"measurement_list\">{t('option.measurementOverlay.angle')}:</div>\n          <div className=\"measurement\">\n            0&deg;\n          </div>\n        </div>\n      );\n    }\n    const { key } = data;\n    const getIPathAnnotationPts = (annotation) => {\n      const path = annotation.getPath();\n      const length = path.length;\n      return [path[length - 3], path[length - 2], path[length - 1]];\n    };\n    const keyPtMap = {\n      distanceMeasurement: ({ Start, End }) => [Start, End],\n      perimeterMeasurement: getIPathAnnotationPts,\n      areaMeasurement: getIPathAnnotationPts,\n      rectangularAreaMeasurement: getIPathAnnotationPts,\n      cloudyRectangularAreaMeasurement: getIPathAnnotationPts,\n      arcMeasurement: getIPathAnnotationPts,\n    };\n    const pts = keyPtMap[key](annotation).filter((pt) => !!pt);\n\n    let angle = getAngleInRadians(...pts);\n    if (angle) {\n      const decimalPlaces = getNumberOfDecimalPlaces(annotation.Precision);\n      angle = ((angle / Math.PI) * 180).toFixed(decimalPlaces);\n    }\n\n    if (key === 'arcMeasurement') {\n      angle = annotation.Angle.toFixed(2);\n    }\n\n    return (\n      angle !== undefined && (\n        <div className=\"measurement__detail-item\">\n          <div className=\"measurement_list\">{t('option.measurementOverlay.angle')}:</div>\n          <div className=\"measurement\">\n            {angle}&deg;\n          </div>\n        </div>\n      )\n    );\n  };\n\n  const renderLength = () => {\n    const length = annotation?.Length || 0;\n    return (\n      <div className=\"measurement__detail-item\">\n        <div className=\"measurement_list\">{t('option.measurementOverlay.length')}</div>\n        <div className=\"measurement\">\n          {length}\n        </div>\n      </div>\n    );\n  };\n\n  const renderRadius = () => {\n    const radius = annotation?.Radius || 0;\n    return (\n      <div className=\"measurement__detail-item\">\n        <div className=\"measurement_list\">{t('option.measurementOverlay.radius')}</div>\n        <div className=\"measurement\">\n          {radius}\n        </div>\n      </div>\n    );\n  };\n\n  const renderDetails = () => {\n    const { key, precision } = data;\n    if (key === 'ellipseMeasurement') {\n      return <EllipseMeasurementOverlay annotation={annotation} selectedTool={selectedTool} isOpen={isOpen} canModify={canModify} />;\n    }\n\n    return (\n      <div className=\"measurement__detail-container\">\n        <div className=\"measurement__detail-item\">\n          <div className=\"measurement_list\">{t('option.shared.precision')}:</div>\n          <div className=\"measurement\">\n            {precisionFractions[precision] || precision}\n          </div>\n        </div>\n        {key === 'distanceMeasurement' && (\n          <LineMeasurementInput annotation={annotation} isOpen={isOpen} selectedTool={selectedTool} canModify={canModify} />\n        )}\n        {[\n          'rectangularAreaMeasurement',\n          'cloudyRectangularAreaMeasurement',\n          'perimeterMeasurement',\n          'areaMeasurement',\n        ].includes(key) && renderValue()}\n        {key === 'arcMeasurement' && renderLength()}\n        {key === 'arcMeasurement' && renderRadius()}\n        {!['rectangularAreaMeasurement', 'distanceMeasurement', 'cloudyRectangularAreaMeasurement'].includes(key) &&\n          renderAngle()}\n      </div>\n    );\n  };\n\n  return (\n    <div className=\"MeasurementDetail\">\n      {renderTitle()}\n      {renderDetails()}\n    </div>\n  );\n};\n\nMeasurementDetail.propTypes = propTypes;\n\nexport default MeasurementDetail;\n", "import React from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { useSelector } from 'react-redux';\nimport selectors from 'selectors';\nimport Icon from 'components/Icon';\nimport PropTypes from 'prop-types';\n\nconst Scale = window.Core.Scale;\n\nconst CalibrationOverlayPropTypes = {\n  tempScale: PropTypes.string,\n  onCancelCalibrationMode: PropTypes.func,\n  onApplyCalibration: PropTypes.func\n};\n\nconst CalibrationOverlay = ({ tempScale, onCancelCalibrationMode, onApplyCalibration }) => {\n  const [t] = useTranslation();\n\n  const isCalibrationPopupOpen = useSelector((state) => selectors.isElementOpen(state, 'annotationPopup'));\n\n  const canApplyCalibration = isCalibrationPopupOpen && tempScale;\n\n  return (\n    <div className=\"scale-overlay-calibrate\">\n      <div className=\"scale-overlay-header\">\n        <Icon glyph=\"ic-calibrate\" className=\"scale-overlay-icon\" />\n        <div className=\"scale-overlay-title\">{t('option.measurement.scaleModal.calibrate')}</div>\n      </div>\n      <div className=\"scale-overlay-content\">\n        {!canApplyCalibration\n          ? t('option.measurement.scaleOverlay.selectTwoPoints')\n          : t('option.measurement.scaleOverlay.inputKnowDimension')}\n      </div>\n      <div className=\"divider\" />\n      <div className=\"scale-overlay-footer\">\n        <button\n          className=\"calibration-cancel\"\n          onMouseDown={onCancelCalibrationMode}\n        >\n          {t('action.cancel')}\n        </button>\n        <button\n          className=\"calibration-apply\"\n          disabled={!(isCalibrationPopupOpen && tempScale && (new Scale(tempScale).worldScale?.value > 0))}\n          data-element=\"calibrationApply\"\n          onMouseDown={onApplyCalibration}\n          onTouchStart={onApplyCalibration}\n        >\n          {t('action.apply')}\n        </button>\n      </div>\n    </div>\n  );\n};\n\nCalibrationOverlay.propTypes = CalibrationOverlayPropTypes;\n\nexport default CalibrationOverlay;", "import ScaleHeader from './ScaleHeader';\nimport core from 'core';\nimport MeasurementDetail from './MeasurementDetail';\nimport PropTypes from 'prop-types';\nimport selectors from 'selectors';\nimport React, { useEffect, useState, useMemo, memo } from 'react';\nimport { useSelector, shallowEqual } from 'react-redux';\nimport CalibrationOverlay from './CalibrationOverlay';\n\nimport './ScaleOverlay.scss';\n\nconst Scale = window.Core.Scale;\n\nconst propTypes = {\n  annotations: PropTypes.arrayOf(PropTypes.object).isRequired,\n  selectedTool: PropTypes.object,\n  updateIsCalibration: PropTypes.func.isRequired,\n  enableOrDisableToolElements: PropTypes.func.isRequired,\n  onScaleSelected: PropTypes.func.isRequired,\n  onCancelCalibrationMode: PropTypes.func.isRequired,\n  onApplyCalibration: PropTypes.func.isRequired,\n  onAddingNewScale: PropTypes.func.isRequired\n};\n\nconst ScaleOverlay = ({\n  annotations,\n  selectedTool,\n  updateIsCalibration,\n  enableOrDisableToolElements,\n  onScaleSelected,\n  onCancelCalibrationMode,\n  onApplyCalibration,\n  onAddingNewScale,\n  forceUpdate,\n}) => {\n  const [\n    { isCalibration, tempScale, previousToolName = 'AnnotationCreateDistanceMeasurement', isFractionalUnit },\n    activeToolName\n  ] = useSelector((state) => [\n    selectors.getCalibrationInfo(state),\n    selectors.getActiveToolName(state)\n  ], shallowEqual);\n  const [scales, setScales] = useState(core.getScales());\n  const shouldShowMeasurementDetail = !!Object.keys(scales).length && !(!selectedTool && (!annotations.length || annotations.length > 1));\n\n  useEffect(() => {\n    forceUpdate();\n  }, [scales]);\n\n  useEffect(() => {\n    const onScaleUpdated = (newScales) => {\n      setScales(newScales);\n    };\n    const updateScales = () => {\n      setScales(core.getScales());\n    };\n\n    const onCreateAnnotationWithNoScale = () => {\n      onAddingNewScale();\n    };\n    core.addEventListener('scaleUpdated', onScaleUpdated);\n    core.addEventListener('createAnnotationWithNoScale', onCreateAnnotationWithNoScale);\n    core.addEventListener('annotationsLoaded', updateScales);\n    core.addEventListener('annotationChanged', updateScales);\n\n    return () => {\n      core.removeEventListener('scaleUpdated', onScaleUpdated);\n      core.removeEventListener('createAnnotationWithNoScale', onCreateAnnotationWithNoScale);\n      core.removeEventListener('annotationsLoaded', updateScales);\n      core.removeEventListener('annotationChanged', updateScales);\n    };\n  }, []);\n\n  useEffect(() => {\n    if (activeToolName === 'AnnotationCreateCalibrationMeasurement') {\n      !isCalibration && updateIsCalibration(true);\n      enableOrDisableToolElements(true);\n    } else {\n      updateIsCalibration(false);\n      enableOrDisableToolElements(false);\n    }\n  }, [activeToolName, updateIsCalibration, enableOrDisableToolElements]);\n\n  const selectedScales = useMemo(() => {\n    const scales = new Set();\n    [...annotations, selectedTool].forEach((measurementItem) => {\n      const scale = measurementItem?.Measure?.scale;\n      if (scale && !scales.has(scale)) {\n        scales.add(scale);\n      }\n    });\n    return [...scales];\n  }, [annotations, selectedTool, scales]);\n\n  const totalScales = Object.keys(scales).map((scale) => new Scale(scale));\n  const canModifyMeasurement = annotations.length === 1 ? core.canModify(annotations[0]) : false;\n\n  return isCalibration ? (\n    <CalibrationOverlay\n      tempScale={tempScale}\n      onCancelCalibrationMode={() => onCancelCalibrationMode(previousToolName)}\n      onApplyCalibration={() => onApplyCalibration(previousToolName, tempScale, isFractionalUnit)}\n      previousToolName={previousToolName}\n    />\n  ) : (\n    <>\n      <ScaleHeader\n        scales={totalScales}\n        selectedScales={selectedScales}\n        onScaleSelected={onScaleSelected}\n        onAddingNewScale={onAddingNewScale}\n      />\n      {shouldShowMeasurementDetail && (\n        <MeasurementDetail\n          annotation={annotations.length > 1 ? null : annotations[0] || null}\n          selectedTool={selectedTool}\n          isOpen\n          canModify={canModifyMeasurement}\n        />\n      )}\n    </>\n  );\n};\n\nScaleOverlay.propTypes = propTypes;\n\nexport default memo(ScaleOverlay);\n", "import actions from 'actions';\nimport ScaleOverlay from './ScaleOverlay';\nimport classNames from 'classnames';\nimport core from 'core';\nimport Draggable from 'react-draggable';\nimport selectors from 'selectors';\nimport { useSelector, useDispatch } from 'react-redux';\nimport React, { useState, useRef, useCallback, useReducer, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport DataElements from 'constants/dataElement';\n\nimport './ScaleOverlay.scss';\n\nimport MobilePopupWrapper from '../MobilePopupWrapper';\nimport { isMobileSize } from 'helpers/getDeviceSize';\n\nconst Scale = window.Core.Scale;\n\nconst measurementDataElements = [\n  'distanceToolGroupButton',\n  'arcMeasurementToolGroupButton',\n  'perimeterToolGroupButton',\n  'areaToolGroupButton',\n  'rectangleAreaToolGroupButton',\n  'ellipseAreaToolGroupButton',\n  'countToolGroupButton',\n  'cloudyRectangleAreaToolGroupButton',\n  'arcToolGroupButton'\n];\n\nconst DEFAULT_CONTAINER_TOP_OFFSET = 85;\nconst DEFAULT_CONTAINER_RIGHT_OFFSET = 35;\nconst DEFAULT_WIDTH_RATIO = 0.666;\nconst DEFAULT_DISTANCE = 10;\n\nconst ScaleOverlayContainer = ({ annotations, selectedTool }) => {\n  const dispatch = useDispatch();\n  const [t] = useTranslation();\n  const [\n    isDisabled,\n    isOpen,\n    initialPosition,\n  ] = useSelector(\n    (state) => [\n      selectors.isElementDisabled(state, DataElements.SCALE_OVERLAY_CONTAINER),\n      selectors.isElementOpen(state, DataElements.SCALE_OVERLAY_CONTAINER),\n      selectors.getScaleOverlayPosition(state),\n    ],\n  );\n  const [position, setPosition] = useState({ x: 0, y: 0 });\n  const [, forceUpdate] = useReducer((x) => x + 1, 0, () => 0);\n\n  const [\n    documentContainerWidth,\n    documentContainerHeight\n  ] = useSelector((state) => [\n    selectors.getDocumentContainerWidth(state),\n    selectors.getDocumentContainerHeight(state)\n  ]);\n\n  const containerRef = useRef(null);\n\n  const documentElement = core.getViewerElement();\n  const documentContainerElement = core.getScrollViewElement();\n\n  const calculateStyle = () => {\n    const initialPositionParts = initialPosition.split('-');\n    const offset = { left: 0, top: 0, };\n    if (initialPositionParts[0] === 'top') {\n      offset.top = documentElement?.offsetTop + DEFAULT_DISTANCE || DEFAULT_CONTAINER_TOP_OFFSET;\n    } else {\n      let containerHeight = 400;\n      if (containerRef?.current) {\n        containerHeight = containerRef.current.getBoundingClientRect().height;\n      }\n      offset.top = documentContainerHeight + documentContainerElement?.offsetTop - DEFAULT_DISTANCE - containerHeight || DEFAULT_CONTAINER_TOP_OFFSET;\n    }\n\n    if (initialPositionParts[1] === 'right') {\n      offset.left = documentContainerWidth * DEFAULT_WIDTH_RATIO;\n      if (documentElement && containerRef?.current) {\n        offset.left = Math.min(\n          documentElement?.offsetLeft + documentElement?.offsetWidth + DEFAULT_DISTANCE || offset.left,\n          documentContainerWidth - containerRef.current.getBoundingClientRect().width - DEFAULT_DISTANCE,\n        );\n      }\n    } else {\n      if (documentElement && containerRef?.current) {\n        const containerWidth = containerRef.current.getBoundingClientRect().width;\n        offset.left = documentElement?.offsetLeft - DEFAULT_DISTANCE - containerWidth || DEFAULT_DISTANCE;\n        if (documentContainerElement && offset.left < documentContainerElement.offsetLeft) {\n          offset.left = documentContainerElement.offsetLeft + DEFAULT_DISTANCE;\n        }\n      }\n      if (!offset.left || isNaN(offset.left) || offset.left < 0) {\n        offset.left = DEFAULT_DISTANCE;\n      }\n    }\n    return offset;\n  };\n  const style = calculateStyle();\n\n  useEffect(() => {\n    setPosition({ x: 0, y: 0 });\n  }, [initialPosition]);\n\n  const containerBounds = () => {\n    const initialPositionParts = initialPosition.split('-');\n    const bounds = { top: 0, bottom: 0, left: 0, right: 0 };\n    if (initialPositionParts[0] === 'top') {\n      bounds.top = 0;\n      bounds.bottom = documentContainerHeight - (DEFAULT_DISTANCE * 2);\n      if (containerRef.current) {\n        bounds.bottom -= containerRef.current.getBoundingClientRect().height;\n      } else {\n        bounds.bottom -= DEFAULT_CONTAINER_TOP_OFFSET;\n      }\n    } else {\n      bounds.top = -documentContainerHeight + (DEFAULT_DISTANCE * 2);\n      if (containerRef.current) {\n        bounds.top += containerRef.current.getBoundingClientRect().height;\n      } else {\n        bounds.top += DEFAULT_CONTAINER_TOP_OFFSET;\n      }\n      bounds.bottom = 0;\n    }\n\n    if (initialPositionParts[1] === 'right') {\n      bounds.left = -documentContainerWidth;\n      bounds.right = documentContainerWidth / 3;\n      if (style) {\n        bounds.right = documentContainerWidth - style['left'];\n      }\n    } else {\n      bounds.left = documentContainerElement?.offsetLeft;\n      if (style) {\n        bounds.left = documentContainerElement?.offsetLeft - style['left'] + DEFAULT_DISTANCE;\n      }\n      bounds.right = documentContainerWidth - DEFAULT_DISTANCE - DEFAULT_CONTAINER_RIGHT_OFFSET;\n      if (style) {\n        bounds.right -= style['left'];\n      }\n    }\n    return bounds;\n  };\n\n  const syncDraggablePosition = (e, { x, y }) => {\n    setPosition({\n      x,\n      y,\n    });\n  };\n\n  const updateIsCalibration = useCallback((isCalibration) => {\n    dispatch(actions.updateCalibrationInfo({ isCalibration }));\n  }, []);\n\n  const enableOrDisableToolElements = useCallback((disabled) => {\n    measurementDataElements.forEach((dataElement) => {\n      dispatch(\n        actions.setCustomElementOverrides(dataElement, {\n          disabled,\n        }),\n      );\n    });\n  }, []);\n\n  const setSelectedScale = (scale) => dispatch(actions.setSelectedScale(scale));\n\n  const openScaleModal = useCallback((scale) => {\n    scale && setSelectedScale(new Scale(scale));\n    dispatch(actions.openElements([DataElements.SCALE_MODAL]));\n    dispatch(actions.setIsAddingNewScale());\n  }, []);\n\n  const onScaleSelected = useCallback((selectedScales, scale) => {\n    const newScale = new Scale(scale);\n    if (selectedScales.length === 1 && selectedScales.includes(scale)) {\n      openScaleModal(scale);\n    } else {\n      const applyTo = [...annotations, selectedTool];\n      const scaleToDelete = core.getDocumentViewer().getMeasurementManager().getOldScalesToDeleteAfterApplying({ scale: newScale, applyTo })[0];\n      const createAndApplyScale = () => {\n        core.createAndApplyScale(\n          newScale,\n          [...annotations, selectedTool]\n        );\n      };\n      if (scaleToDelete) {\n        confirmScaleToDelete(scaleToDelete, createAndApplyScale);\n      } else {\n        createAndApplyScale();\n      }\n    }\n  }, [annotations, selectedTool]);\n\n  const confirmScaleToDelete = (scaleToDelete, createAndApplyScale) => {\n    const message = (\n      <div className='customMessage'>\n        <p>\n          <span>\n            {t('option.measurement.deleteScaleModal.ifChangeScale')}\n            <b>{scaleToDelete}</b>\n            {t('option.measurement.deleteScaleModal.notUsedWillDelete')}\n          </span>\n        </p>\n        <p>\n          <span>\n            {t('option.measurement.deleteScaleModal.ifToContinue')}\n          </span>\n        </p>\n      </div>\n    );\n    const title = `${t('option.measurement.deleteScaleModal.deleteScale')} ${scaleToDelete}`;\n    const confirmBtnText = t('action.confirm');\n\n    const warning = {\n      message,\n      title,\n      confirmBtnText,\n      onConfirm: () => createAndApplyScale()\n    };\n    dispatch(actions.showWarningMessage(warning));\n  };\n\n  const onCancelCalibrationMode = useCallback((previousToolName) => {\n    core.setToolMode(previousToolName);\n    updateIsCalibration(false);\n    dispatch(actions.setIsElementHidden(DataElements.SCALE_MODAL, false));\n  }, []);\n\n  const onApplyCalibration = useCallback((previousToolName, tempScale, isFractionalUnit) => {\n    dispatch(actions.updateCalibrationInfo({ isCalibration: false, tempScale, isFractionalUnit }));\n    dispatch(actions.setIsElementHidden(DataElements.SCALE_MODAL, false));\n    core.setToolMode(previousToolName);\n    core.deleteAnnotations([annotations[0]]);\n  }, [annotations]);\n\n  const onAddingNewScale = useCallback(() => {\n    openScaleModal();\n    dispatch(actions.setIsAddingNewScale(true));\n  }, []);\n\n  const isMobile = isMobileSize();\n\n  if (isMobile) {\n    return !isDisabled && (\n      <MobilePopupWrapper>\n        <ScaleOverlay\n          annotations={annotations}\n          selectedTool={selectedTool}\n          updateIsCalibration={updateIsCalibration}\n          enableOrDisableToolElements={enableOrDisableToolElements}\n          onScaleSelected={onScaleSelected}\n          onCancelCalibrationMode={onCancelCalibrationMode}\n          onApplyCalibration={onApplyCalibration}\n          onAddingNewScale={onAddingNewScale}\n          forceUpdate={forceUpdate}\n          tabIndex={0}\n        />\n      </MobilePopupWrapper>\n    );\n  } else {\n    return !isDisabled && (\n      <Draggable\n        position={position}\n        bounds={containerBounds()}\n        onDrag={syncDraggablePosition}\n        onStop={syncDraggablePosition}\n        cancel={'.scale-overlay-selector, .add-new-scale'}\n      >\n        <div\n          className={classNames({\n            Overlay: true,\n            ScaleOverlay: true,\n            open: isOpen,\n            closed: !isOpen,\n          })}\n          data-element={DataElements.SCALE_OVERLAY_CONTAINER}\n          style={style}\n          ref={containerRef}\n        >\n          <ScaleOverlay\n            annotations={annotations}\n            selectedTool={selectedTool}\n            updateIsCalibration={updateIsCalibration}\n            enableOrDisableToolElements={enableOrDisableToolElements}\n            onScaleSelected={onScaleSelected}\n            onCancelCalibrationMode={onCancelCalibrationMode}\n            onApplyCalibration={onApplyCalibration}\n            onAddingNewScale={onAddingNewScale}\n            forceUpdate={forceUpdate}\n            tabIndex={0}\n          />\n        </div>\n      </Draggable>\n    );\n  }\n};\n\nexport default ScaleOverlayContainer;\n"], "sourceRoot": ""}