(window.webpackJsonp=window.webpackJsonp||[]).push([[58],{1822:function(t,e,n){var o=n(32),i=n(1823);"string"==typeof(i=i.__esModule?i.default:i)&&(i=[[t.i,i,""]]);var a={insert:function(t){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(t);let e;e=document.getElementsByTagName("apryse-webviewer"),e.length||(e=function t(e,n=document){const o=[];return n.querySelectorAll(e).forEach(t=>o.push(t)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&o.push(...t(e,n.shadowRoot))}),o}("apryse-webviewer"));const n=[];for(let o=0;o<e.length;o++){const i=e[o];if(0===o)i.shadowRoot.appendChild(t),t.onload=function(){n.length>0&&n.forEach(e=>{e.innerHTML=t.innerHTML})};else{const e=t.cloneNode(!0);i.shadowRoot.appendChild(e),n.push(e)}}},singleton:!1};o(i,a);t.exports=i.locals||{}},1823:function(t,e,n){(e=t.exports=n(33)(!1)).push([t.i,".open.ContextMenuPopup{visibility:visible}.closed.ContextMenuPopup{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.ContextMenuPopup{position:absolute;z-index:70;display:flex;justify-content:center;align-items:center}.ContextMenuPopup:empty{padding:0}.ContextMenuPopup .buttons{display:flex}.ContextMenuPopup .Button{margin:4px;width:32px;height:32px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ContextMenuPopup .Button{width:42px;height:42px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .ContextMenuPopup .Button{width:42px;height:42px}}.ContextMenuPopup .Button:hover{background:var(--popup-button-hover)}.ContextMenuPopup .Button:hover:disabled{background:none}.ContextMenuPopup .Button .Icon{width:18px;height:18px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ContextMenuPopup .Button .Icon{width:24px;height:24px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .ContextMenuPopup .Button .Icon{width:24px;height:24px}}.is-vertical.ContextMenuPopup .Button.main-menu-button{width:100%;border-radius:0;justify-content:flex-start;padding-left:var(--padding-small);padding-right:var(--padding-small);margin:0 0 var(--padding-tiny) 0}.is-vertical.ContextMenuPopup .Button.main-menu-button:first-child{margin-top:var(--padding-tiny)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .is-vertical.ContextMenuPopup .Button.main-menu-button{width:100%;height:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .is-vertical.ContextMenuPopup .Button.main-menu-button{width:100%;height:32px}}.is-vertical.ContextMenuPopup .Button.main-menu-button .Icon{margin-right:10px}.is-vertical.ContextMenuPopup .Button.main-menu-button span{white-space:nowrap}@keyframes bottom-up{0%{transform:translateY(100%)}to{transform:translateY(0)}}@keyframes up-bottom{0%{transform:translateY(0)}to{transform:translateY(100%)}}.ContextMenuPopup{box-shadow:0 0 3px 0 var(--document-box-shadow);background:var(--component-background);border-radius:4px}.ContextMenuPopup.is-horizontal .container{display:inherit}.ContextMenuPopup.is-vertical{flex-direction:column;align-items:flex-start}.ContextMenuPopup.isOfficeEditor .container{display:block}.ContextMenuPopup.isOfficeEditor .container .office-action-item{width:300px;padding:8px;display:flex;justify-content:space-between;cursor:pointer}.ContextMenuPopup.isOfficeEditor .container .office-action-item:hover{background-color:var(--blue-4)}.ContextMenuPopup.isOfficeEditor .container .office-action-item.disabled{cursor:default;background-color:inherit;color:var(--disabled-text)}.ContextMenuPopup.isOfficeEditor .container .office-action-item .icon-title{display:flex;align-items:center}.ContextMenuPopup.isOfficeEditor .container .office-action-item .icon-title .Icon{margin-right:10px}.ContextMenuPopup.isOfficeEditor .container .office-action-item .shortcut{display:flex;align-items:center}.ContextMenuPopup .divider{height:1px;background:var(--divider);margin-top:8px;margin-bottom:8px;width:100%}",""]),e.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},2001:function(t,e,n){"use strict";n.r(e);n(35),n(88),n(19),n(11),n(13),n(8),n(14),n(10),n(9),n(12),n(16),n(15),n(20),n(18),n(26),n(27),n(25),n(22),n(29),n(28),n(45),n(23),n(24),n(48),n(46);var o=n(0),i=n.n(o),a=n(17),r=n.n(a),c=n(176),l=n.n(c),u=n(6),s=n(295),p=n(428),d=n(84),m=n(1735),f=n(42),E=n(135),h=n(103),b=n(2),g=n(3),O=n(1),x=n(37),C=n(73),y=n(21),w=n(5);n(1822);function T(t){return(T="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function v(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,o)}return n}function P(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?v(Object(n),!0).forEach((function(e){k(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):v(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function k(t,e,n){return(e=function(t){var e=function(t,e){if("object"!==T(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var o=n.call(t,e||"default");if("object"!==T(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===T(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function _(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var o,i,a,r,c=[],l=!0,u=!1;try{if(a=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;l=!1}else for(;!(l=(o=a.call(n)).done)&&(c.push(o.value),c.length!==e);l=!0);}catch(t){u=!0,i=t}finally{try{if(!l&&null!=n.return&&(r=n.return(),Object(r)!==r))return}finally{if(u)throw i}}return c}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return M(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return M(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function M(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=new Array(e);n<e;n++)o[n]=t[n];return o}var I=function(t){var e=t.dataElement,n=t.onClick,o=t.img,a=t.title,c=t.shortcut,l=void 0===c?"":c,s=t.disabled,d=void 0!==s&&s,m=_(Object(p.a)(),1)[0],E=Object(u.d)();return i.a.createElement("div",{className:r()("office-action-item",{disabled:d}),onClick:function(t){d||(n(),E(b.a.closeElement(w.a.CONTEXT_MENU_POPUP))),t.stopPropagation()},tabIndex:d?-1:0,"data-element":e,onKeyDown:function(t){"Enter"!==t.key||d||(n(),E(b.a.closeElement(w.a.CONTEXT_MENU_POPUP)))}},i.a.createElement("div",{className:"icon-title"},o&&i.a.createElement(f.a,{glyph:o,disabled:d}),!o&&i.a.createElement("span",{className:"Icon"}),i.a.createElement("div",null,m(a))),i.a.createElement("div",{className:"shortcut"},l))},j=function(t){var e=t.clickPosition,n=_(Object(u.e)((function(t){return[g.a.isElementOpen(t,w.a.CONTEXT_MENU_POPUP),g.a.isElementDisabled(t,w.a.CONTEXT_MENU_POPUP),g.a.isRightClickAnnotationPopupEnabled(t),g.a.isMultiViewerMode(t),g.a.getActiveDocumentViewerKey(t),g.a.isCursorInTable(t)]}),u.c),6),a=n[0],c=n[1],f=n[2],T=n[3],v=n[4],k=n[5],M=_(Object(p.a)(),1)[0],j=Object(u.d)(),N=Object(u.f)(),R=_(Object(o.useState)({left:0,top:0}),2),A=R[0],S=R[1],F=Object(o.useRef)(),B=!!x.l||Object(x.k)();Object(E.a)(F,(function(){j(b.a.closeElement(w.a.CONTEXT_MENU_POPUP))})),Object(o.useEffect)((function(){a&&j(b.a.closeElements([w.a.ANNOTATION_POPUP,w.a.TEXT_POPUP,w.a.INLINE_COMMENT_POPUP]))}),[a]),Object(o.useLayoutEffect)((function(){var t=e.left,n=e.top,o=F.current.getBoundingClientRect(),i=o.width,a=o.height,r=T?Object(y.a)().querySelector("#DocumentContainer".concat(v)):Object(y.a)().querySelector(".DocumentContainer");if(r){var c=r.getBoundingClientRect(),l=0,u=0;if(window.isApryseWebViewerWebComponent){var s,p=null===(s=Object(y.a)())||void 0===s?void 0:s.host,d=null==p?void 0:p.getBoundingClientRect();d&&(l=d.left,u=d.top,l+=p.scrollLeft,u+=p.scrollTop)}n-=u,(t-=l)<c.left-l&&(t=c.left+2-l),t+i>c.right-l&&(t=c.right-i-2-l),n<c.top-u&&(n=c.top+2-u),n+a>c.bottom-u&&(n=c.bottom-a-2),S({left:t,top:n})}}),[e,T,v]);var D=x.j?"⌘ Command":"Ctrl",U=x.j?"⌘Cmd":"Ctrl",H=function(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];if(x.c){var e=M(t?"officeEditor.pastingTitle":"officeEditor.pastingWithoutFormatTitle"),n=M(t?"officeEditor.pastingMessage":"officeEditor.pastingWithoutFormatMessage"),o="".concat(D,t?" + V":" + Shift + V"),i=M("action.close"),a={message:"".concat(n,":\n\n").concat(o),title:e,confirmBtnText:i,onConfirm:function(){setTimeout((function(){O.a.getViewerElement().focus()}))},onCancel:function(){setTimeout((function(){O.a.getViewerElement().focus()}))}};j(b.a.showWarningMessage(a))}else O.a.getOfficeEditor().pasteText(t)};if(c)return null;var L=i.a.createElement("div",{className:r()("Popup","ContextMenuPopup",{open:a,closed:!a,isOfficeEditor:Object(C.g)(),"is-vertical":f&&!Object(C.g)(),"is-horizontal":!f&&!Object(C.g)()}),ref:F,"data-element":w.a.CONTEXT_MENU_POPUP,style:P({},A),onClick:function(){return j(b.a.closeElement(w.a.CONTEXT_MENU_POPUP))}},i.a.createElement(s.a,{locked:a&&0!==A.top&&0!==A.left},i.a.createElement("div",{className:"container"},Object(C.g)()?i.a.createElement(i.a.Fragment,null,i.a.createElement(I,{title:"action.cut",img:"icon-cut",dataElement:w.a.OFFICE_EDITOR_CUT,onClick:function(){return O.a.getOfficeEditor().cutSelectedText()},shortcut:"".concat(U,"+X"),disabled:!O.a.getOfficeEditor().isTextSelected()}),i.a.createElement(I,{title:"action.copy",img:"icon-copy",dataElement:w.a.OFFICE_EDITOR_COPY,onClick:function(){return O.a.getOfficeEditor().copySelectedText()},shortcut:"".concat(U,"+C"),disabled:!O.a.getOfficeEditor().isTextSelected()}),i.a.createElement(I,{title:"action.paste",img:"icon-paste",dataElement:w.a.OFFICE_EDITOR_PASTE,onClick:function(){return H()},shortcut:"".concat(U,"+V")}),i.a.createElement(I,{title:"action.pasteWithoutFormatting",img:"icon-paste-without-formatting",dataElement:w.a.OFFICE_EDITOR_PASTE_WITHOUT_FORMATTING,onClick:function(){return H(!1)},shortcut:"".concat(U,"+Shift+V")}),!k&&i.a.createElement(I,{title:"action.delete",img:"icon-delete-line",dataElement:w.a.OFFICE_EDITOR_DELETE,onClick:function(){return O.a.getOfficeEditor().removeSelection()},disabled:!(O.a.getOfficeEditor().isTextSelected()||O.a.getOfficeEditor().isImageSelected())}),k&&i.a.createElement(i.a.Fragment,null,i.a.createElement("div",{className:"divider"}),i.a.createElement(I,{title:"officeEditor.insertRowAbove",dataElement:w.a.OFFICE_EDITOR_INSERT_ROW_ABOVE,onClick:function(){return O.a.getOfficeEditor().insertRows(1,!0)}}),i.a.createElement(I,{title:"officeEditor.insertRowBelow",dataElement:w.a.OFFICE_EDITOR_INSERT_ROW_BELOW,onClick:function(){return O.a.getOfficeEditor().insertRows(1,!1)}}),i.a.createElement(I,{title:"officeEditor.insertColumnRight",dataElement:w.a.OFFICE_EDITOR_INSERT_COLUMN_RIGHT,onClick:function(){return O.a.getOfficeEditor().insertColumns(1,!0)}}),i.a.createElement(I,{title:"officeEditor.insertColumnLeft",dataElement:w.a.OFFICE_EDITOR_INSERT_COLUMN_LEFT,onClick:function(){return O.a.getOfficeEditor().insertColumns(1,!1)}}),i.a.createElement(I,{title:"officeEditor.deleteRow",dataElement:w.a.OFFICE_EDITOR_DELETE_ROW,onClick:function(){return O.a.getOfficeEditor().removeRows()}}),i.a.createElement(I,{title:"officeEditor.deleteColumn",dataElement:w.a.OFFICE_EDITOR_DELETE_COLUMN,onClick:function(){return O.a.getOfficeEditor().removeColumns()}}),i.a.createElement(I,{title:"officeEditor.deleteTable",dataElement:w.a.OFFICE_EDITOR_DELETE_TABLE,onClick:function(){return O.a.getOfficeEditor().removeTable()}}))):i.a.createElement(m.a,{dataElement:w.a.CONTEXT_MENU_POPUP,childrenClassName:"main-menu-button"},i.a.createElement(d.a,{className:"main-menu-button",dataElement:"panToolButton",label:f?"tool.pan":"",title:f?"":"tool.pan",img:"icon-header-pan",onClick:function(){return Object(h.a)(N,"Pan")}}),i.a.createElement(d.a,{className:"main-menu-button",dataElement:"stickyToolButton",label:f?"annotation.stickyNote":"",title:f?"":"annotation.stickyNote",img:"icon-tool-comment-line",onClick:function(){return Object(h.a)(N,"AnnotationCreateSticky")}}),i.a.createElement(d.a,{className:"main-menu-button",dataElement:"highlightToolButton",label:f?"annotation.highlight":"",title:f?"":"annotation.highlight",img:"icon-tool-highlight",onClick:function(){return Object(h.a)(N,"AnnotationCreateTextHighlight")}}),i.a.createElement(d.a,{className:"main-menu-button",dataElement:"freeHandToolButton",label:f?"annotation.freehand":"",title:f?"":"annotation.freehand",img:"icon-tool-pen-line",onClick:function(){return Object(h.a)(N,"AnnotationCreateFreeHand")}}),i.a.createElement(d.a,{className:"main-menu-button",dataElement:"freeHandHighlightToolButton",label:f?"annotation.freeHandHighlight":"",title:f?"":"annotation.freeHandHighlight",img:"icon-tool-pen-highlight",onClick:function(){return Object(h.a)(N,"AnnotationCreateFreeHandHighlight")}}),i.a.createElement(d.a,{className:"main-menu-button",dataElement:"freeTextToolButton",label:f?"annotation.freetext":"",title:f?"":"annotation.freetext",img:"icon-tool-text-free-text",onClick:function(){return Object(h.a)(N,"AnnotationCreateFreeText")}}),i.a.createElement(d.a,{className:"main-menu-button",dataElement:"markInsertTextToolButton",label:f?"annotation.markInsertText":"",title:f?"":"annotation.markInsertText",img:"ic-insert text",onClick:function(){return Object(h.a)(N,"AnnotationCreateMarkInsertText")}}),i.a.createElement(d.a,{className:"main-menu-button",dataElement:"markReplaceTextToolButton",label:f?"annotation.markReplaceText":"",title:f?"":"annotation.markReplaceText",img:"ic-replace text",onClick:function(){return Object(h.a)(N,"AnnotationCreateMarkReplaceText")}})))));return x.e||B?L:i.a.createElement(l.a,{cancel:".Button, .cell, .sliders-container svg, select, button, input"},L)},N=i.a.memo(j);e.default=N}}]);
//# sourceMappingURL=chunk.58.js.map