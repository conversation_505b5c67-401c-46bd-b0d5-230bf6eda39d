{"version": 3, "sources": ["webpack:///./src/ui/src/components/HeaderFooterOptionsModal/HeaderFooterOptionsModal.scss?f8fd", "webpack:///./src/ui/src/components/HeaderFooterOptionsModal/HeaderFooterOptionsModal.scss", "webpack:///./src/ui/src/components/HeaderFooterOptionsModal/HeaderFooterOptionsModal.js", "webpack:///./src/ui/src/components/HeaderFooterOptionsModal/index.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "LAYOUTS", "HeaderFooterOptionsModal", "t", "useTranslation", "dispatch", "useDispatch", "useState", "headerFromTop", "setHeaderFromTop", "footerFromBottom", "setFooterFromBottom", "layout", "setLayout", "isOpen", "useSelector", "state", "selectors", "isElementOpen", "DataElements", "HEADER_FOOTER_OPTIONS_MODAL", "closeModal", "actions", "closeElement", "validateInput", "input", "replace", "modalClass", "classNames", "className", "data-element", "ModalWrapper", "title", "closehandler", "onCloseClick", "swipeToClose", "htmlFor", "Input", "type", "id", "data-testid", "onChange", "e", "val", "target", "value", "min", "step", "onSubmit", "preventDefault", "Choice", "checked", "radio", "name", "label", "<PERSON><PERSON>", "onClick"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,w0NAA20N,KAGp2N0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,4wCCGvB,IAAMC,EACE,OADFA,EAEG,QAFHA,EAGM,WAHNA,EAIY,iBA4HHC,EAzHkB,WAC/B,IAAOC,EAAqB,EAAhBC,cAAgB,GAApB,GACFC,EAAWC,cAEsC,IAAbC,mBAAS,KAAI,GAAhDC,EAAa,KAAEC,EAAgB,KACuB,IAAbF,mBAAS,KAAI,GAAtDG,EAAgB,KAAEC,EAAmB,KACM,IAAtBJ,mBAASN,GAAa,GAA3CW,EAAM,KAAEC,EAAS,KAElBC,EAASC,aAAY,SAACC,GAAK,OAAKC,IAAUC,cAAcF,EAAOG,IAAaC,gCAE5EC,EAAa,WACjBhB,EAASiB,IAAQC,aAAaJ,IAAaC,+BAKvCI,EAAgB,SAACC,GACrB,OAAKA,GAASA,EAAQ,EACb,EAEcA,EAAMC,QAAQ,MAAO,KAsBxCC,EAAaC,IAAW,CAC5B,0BAA4B,IAG9B,OAAOd,GACL,yBAAKe,UAAWF,EAAYG,eAAcX,IAAaC,6BACrD,kBAACW,EAAA,EAAY,CACXjB,OAAQA,EACRkB,MAAO7B,EAAE,+CACT8B,aAAcZ,EACda,aAAcb,EACdc,cAAY,GAEZ,yBAAKN,UAAU,cACb,yBAAKA,UAAU,SAAS1B,EAAE,kDAC1B,yBAAK0B,UAAU,mBACb,2BAAOO,QAAQ,qBAAqBP,UAAU,SAAS1B,EAAE,wDACzD,kBAACkC,EAAA,EAAK,CACJC,KAAK,SACLC,GAAG,qBACHC,cAAY,qBACZC,SAvCkB,SAACC,GAC7B,IAAMC,EAAMnB,EAAckB,EAAEE,OAAOC,OACnCpC,EAAiBkC,IAsCPE,MAAOrC,EACPsC,IAAI,IACJC,KAAK,SAGT,yBAAKlB,UAAU,mBACb,2BAAOO,QAAQ,wBAAwBP,UAAU,SAAS1B,EAAE,2DAC5D,kBAACkC,EAAA,EAAK,CACJC,KAAK,SACLC,GAAG,wBACHC,cAAY,wBACZC,SA9CqB,SAACC,GAChC,IAAMC,EAAMnB,EAAckB,EAAEE,OAAOC,OACnClC,EAAoBgC,IA6CVE,MAAOnC,EACPoC,IAAI,IACJC,KAAK,SAGT,yBAAKlB,UAAU,SAAS1B,EAAE,yDAC1B,0BAAM0B,UAAU,kBAAkBY,SAhDnB,SAACC,GACtB7B,EAAU6B,EAAEE,OAAOC,QA+C+CG,SApE7C,SAACN,GAAC,OAAKA,EAAEO,mBAqEtB,kBAACC,EAAA,EAAM,CACLC,QAASvC,IAAWX,EACpBmD,OAAK,EACLC,KAAK,gBACLC,MAAOnD,EAAE,6DACT0C,MAAO5C,IAET,kBAACiD,EAAA,EAAM,CACLC,QAASvC,IAAWX,EACpBmD,OAAK,EACLC,KAAK,gBACLC,MAAOnD,EAAE,oEACT0C,MAAO5C,IAET,kBAACiD,EAAA,EAAM,CACLC,QAASvC,IAAWX,EACpBmD,OAAK,EACLC,KAAK,gBACLC,MAAOnD,EAAE,uEACT0C,MAAO5C,IAET,kBAACiD,EAAA,EAAM,CACLC,QAASvC,IAAWX,EACpBmD,OAAK,EACLC,KAAK,gBACLC,MAAOnD,EAAE,4EACT0C,MAAO5C,MAIb,yBAAK4B,UAAU,UACb,kBAAC0B,EAAA,EAAM,CAACC,QA5ED,WACbnC,KA2E+BiC,MAAOnD,EAAE,qBCpI7BD", "file": "chunks/chunk.64.js", "sourcesContent": ["var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./HeaderFooterOptionsModal.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".open.HeaderFooterOptionsModal{visibility:visible}.closed.HeaderFooterOptionsModal{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.HeaderFooterOptionsModal .footer .modal-button.confirm:hover{background:var(--primary-button-hover);border-color:var(--primary-button-hover);color:var(--gray-0)}.HeaderFooterOptionsModal .footer .modal-button.confirm{background:var(--primary-button);border-color:var(--primary-button);color:var(--primary-button-text)}.HeaderFooterOptionsModal .footer .modal-button.confirm.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.HeaderFooterOptionsModal .footer .modal-button.confirm.disabled span{color:var(--primary-button-text)}.HeaderFooterOptionsModal .footer .modal-button.cancel:hover,.HeaderFooterOptionsModal .footer .modal-button.secondary-btn-custom:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6)}.HeaderFooterOptionsModal .footer .modal-button.cancel,.HeaderFooterOptionsModal .footer .modal-button.secondary-btn-custom{border:none;box-shadow:inset 0 0 0 1px var(--primary-button);color:var(--primary-button)}.HeaderFooterOptionsModal .footer .modal-button.cancel.disabled,.HeaderFooterOptionsModal .footer .modal-button.secondary-btn-custom.disabled{cursor:default;border:none;box-shadow:inset 0 0 0 1px rgba(43,115,171,.5);color:rgba(43,115,171,.5)}.HeaderFooterOptionsModal .footer .modal-button.cancel.disabled span,.HeaderFooterOptionsModal .footer .modal-button.secondary-btn-custom.disabled span{color:rgba(43,115,171,.5)}.HeaderFooterOptionsModal{position:fixed;left:0;bottom:0;z-index:100;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background:var(--modal-negative-space)}.HeaderFooterOptionsModal .modal-container .wrapper .modal-content{padding:10px}.HeaderFooterOptionsModal .footer{display:flex;flex-direction:row;justify-content:flex-end;width:100%;margin-top:13px}.HeaderFooterOptionsModal .footer.modal-footer{padding:16px;margin:0;border-top:1px solid var(--divider)}.HeaderFooterOptionsModal .footer .modal-button{display:flex;justify-content:center;align-items:center;padding:6px 18px;margin:8px 0 0;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;height:30px;cursor:pointer}.HeaderFooterOptionsModal .footer .modal-button.confirm{margin-left:4px}.HeaderFooterOptionsModal .footer .modal-button.secondary-btn-custom{border-radius:4px;padding:2px 20px 4px;cursor:pointer}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .HeaderFooterOptionsModal .footer .modal-button{padding:23px 8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .HeaderFooterOptionsModal .footer .modal-button{padding:23px 8px}}.HeaderFooterOptionsModal .swipe-indicator{background:var(--swipe-indicator-bg);border-radius:2px;height:4px;width:38px;position:absolute;top:12px;margin-left:auto;margin-right:auto;left:0;right:0}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .HeaderFooterOptionsModal .swipe-indicator{display:none}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .HeaderFooterOptionsModal .swipe-indicator{display:none}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .HeaderFooterOptionsModal .swipe-indicator{width:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .HeaderFooterOptionsModal .swipe-indicator{width:32px}}.HeaderFooterOptionsModal{flex-direction:column}.HeaderFooterOptionsModal .modal-container{display:flex;flex-direction:column;height:auto;width:480px}.HeaderFooterOptionsModal .modal-container .modal-body{padding:16px;display:flex;flex-direction:column;font-size:var(--font-size-default);font-family:var(--font-family);grid-gap:16px;gap:16px}.HeaderFooterOptionsModal .modal-container .modal-body .title{line-height:16px;font-weight:var(--font-weight-bold)}.HeaderFooterOptionsModal .modal-container .modal-body .input-container{display:flex;flex-direction:column;grid-gap:8px;gap:8px}.HeaderFooterOptionsModal .modal-container .modal-body .input-container .label{color:var(--gray-12);display:block;text-align:left;font-weight:var(--font-weight-normal)}.HeaderFooterOptionsModal .modal-container .modal-body .input-container .ui__input{border-color:var(--gray-5);position:relative}.HeaderFooterOptionsModal .modal-container .modal-body .input-container .ui__input:after{content:\\\"cm\\\";font-size:13px;color:var(--gray-8);position:absolute;right:16px;pointer-events:none}.HeaderFooterOptionsModal .modal-container .modal-body .input-container .ui__input.ui__input--focused{box-shadow:none;border-color:var(--focus-border)}.HeaderFooterOptionsModal .modal-container .modal-body .input-container input{padding:8px 40px 8px 8px;height:32px;font-size:var(--font-size-default)}.HeaderFooterOptionsModal .modal-container .modal-body .input-container input[type=number]{-moz-appearance:textfield}.HeaderFooterOptionsModal .modal-container .modal-body .input-container input::-webkit-inner-spin-button,.HeaderFooterOptionsModal .modal-container .modal-body .input-container input::-webkit-outer-spin-button{-webkit-appearance:none;margin:0}.HeaderFooterOptionsModal .modal-container .modal-body .radio-container{display:flex;flex-direction:column;grid-gap:16px;gap:16px;margin:0}.HeaderFooterOptionsModal .modal-container .modal-body .radio-container .ui__choice--checked .ui__choice__input__check{border-color:var(--blue-5)}.HeaderFooterOptionsModal .modal-container .modal-body .radio-container .ui__choice__input__check{border-color:var(--gray-7)}.HeaderFooterOptionsModal .modal-container .footer{padding:16px;display:flex;justify-content:flex-end;border-top:1px solid var(--gray-5)}.HeaderFooterOptionsModal .modal-container .footer button{border:none;border-radius:4px;background:var(--primary-button);min-width:59px;width:auto;padding:8px 16px;height:32px;color:var(--primary-button-text)}.HeaderFooterOptionsModal .modal-container .footer button:hover{background:var(--primary-button-hover)}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React, { useState } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\nimport selectors from 'selectors';\nimport actions from 'actions';\nimport classNames from 'classnames';\nimport DataElements from 'constants/dataElement';\nimport Button from 'components/Button';\nimport ModalWrapper from 'components/ModalWrapper';\nimport { Choice, Input } from '@pdftron/webviewer-react-toolkit';\n\nimport './HeaderFooterOptionsModal.scss';\n\nconst LAYOUTS = {\n  NONE: 'none',\n  FIRST: 'first',\n  EVEN_ODD: 'even_odd',\n  FIRST_EVEN_ODD: 'first_even_odd',\n};\n\nconst HeaderFooterOptionsModal = () => {\n  const [t] = useTranslation();\n  const dispatch = useDispatch();\n\n  const [headerFromTop, setHeaderFromTop] = useState('0');\n  const [footerFromBottom, setFooterFromBottom] = useState('0');\n  const [layout, setLayout] = useState(LAYOUTS.NONE);\n\n  const isOpen = useSelector((state) => selectors.isElementOpen(state, DataElements.HEADER_FOOTER_OPTIONS_MODAL));\n\n  const closeModal = () => {\n    dispatch(actions.closeElement(DataElements.HEADER_FOOTER_OPTIONS_MODAL));\n  };\n\n  const preventDefault = (e) => e.preventDefault();\n\n  const validateInput = (input) => {\n    if (!input || input < 0) {\n      return 0;\n    }\n    const validatedInput = input.replace(/^0+/, '');\n    return validatedInput;\n  };\n\n  const onHeaderFromTopChange = (e) => {\n    const val = validateInput(e.target.value);\n    setHeaderFromTop(val);\n  };\n\n  const onFooterFromBottomChange = (e) => {\n    const val = validateInput(e.target.value);\n    setFooterFromBottom(val);\n  };\n\n  const onLayoutChange = (e) => {\n    setLayout(e.target.value);\n  };\n\n  const onSave = () => {\n    closeModal();\n  };\n\n  const modalClass = classNames({\n    'HeaderFooterOptionsModal': true\n  });\n\n  return isOpen && (\n    <div className={modalClass} data-element={DataElements.HEADER_FOOTER_OPTIONS_MODAL}>\n      <ModalWrapper\n        isOpen={isOpen}\n        title={t('officeEditor.headerFooterOptionsModal.title')}\n        closehandler={closeModal}\n        onCloseClick={closeModal}\n        swipeToClose\n      >\n        <div className='modal-body'>\n          <div className='title'>{t('officeEditor.headerFooterOptionsModal.margins')}</div>\n          <div className='input-container'>\n            <label htmlFor='headerFromTopInput' className='label'>{t('officeEditor.headerFooterOptionsModal.headerFromTop')}</label>\n            <Input\n              type='number'\n              id='headerFromTopInput'\n              data-testid=\"headerFromTopInput\"\n              onChange={onHeaderFromTopChange}\n              value={headerFromTop}\n              min='0'\n              step='any'\n            />\n          </div>\n          <div className='input-container'>\n            <label htmlFor='footerFromBottomInput' className='label'>{t('officeEditor.headerFooterOptionsModal.footerFromBottom')}</label>\n            <Input\n              type='number'\n              id='footerFromBottomInput'\n              data-testid=\"footerFromBottomInput\"\n              onChange={onFooterFromBottomChange}\n              value={footerFromBottom}\n              min='0'\n              step='any'\n            />\n          </div>\n          <div className='title'>{t('officeEditor.headerFooterOptionsModal.layouts.layout')}</div>\n          <form className='radio-container' onChange={onLayoutChange} onSubmit={preventDefault}>\n            <Choice\n              checked={layout === LAYOUTS.NONE}\n              radio\n              name='layout-option'\n              label={t('officeEditor.headerFooterOptionsModal.layouts.noSelection')}\n              value={LAYOUTS.NONE}\n            />\n            <Choice\n              checked={layout === LAYOUTS.FIRST}\n              radio\n              name='layout-option'\n              label={t('officeEditor.headerFooterOptionsModal.layouts.differentFirstPage')}\n              value={LAYOUTS.FIRST}\n            />\n            <Choice\n              checked={layout === LAYOUTS.EVEN_ODD}\n              radio\n              name='layout-option'\n              label={t('officeEditor.headerFooterOptionsModal.layouts.differentEvenOddPages')}\n              value={LAYOUTS.EVEN_ODD}\n            />\n            <Choice\n              checked={layout === LAYOUTS.FIRST_EVEN_ODD}\n              radio\n              name='layout-option'\n              label={t('officeEditor.headerFooterOptionsModal.layouts.differentFirstEvenOddPages')}\n              value={LAYOUTS.FIRST_EVEN_ODD}\n            />\n          </form>\n        </div>\n        <div className='footer'>\n          <Button onClick={onSave} label={t('action.save')} />\n        </div>\n      </ModalWrapper>\n    </div>\n  );\n};\n\nexport default HeaderFooterOptionsModal;\n", "import HeaderFooterOptionsModal from './HeaderFooterOptionsModal';\n\nexport default HeaderFooterOptionsModal;\n"], "sourceRoot": ""}