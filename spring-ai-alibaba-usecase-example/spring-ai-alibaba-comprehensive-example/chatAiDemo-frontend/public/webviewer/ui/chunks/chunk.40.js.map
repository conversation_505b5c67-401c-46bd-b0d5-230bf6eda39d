{"version": 3, "sources": ["webpack:///./src/ui/src/constants/bookmarksOutlinesShared.scss?1d5f", "webpack:///./src/ui/src/constants/dnd.js", "webpack:///./src/ui/src/constants/bookmarksOutlinesShared.scss", "webpack:///./src/ui/src/components/PortfolioItemContent/PortfolioItemContent.scss?cc43", "webpack:///./src/ui/src/components/PortfolioItemContent/PortfolioItemContent.scss", "webpack:///./src/ui/src/components/PortfolioItem/PortfolioItem.scss?6c9e", "webpack:///./src/ui/src/components/PortfolioItem/PortfolioItem.scss", "webpack:///./src/ui/src/components/PortfolioPanel/PortfolioPanel.scss?1ad7", "webpack:///./src/ui/src/components/PortfolioPanel/PortfolioPanel.scss", "webpack:///./src/ui/src/components/PortfolioPanel/PortfolioContext.js", "webpack:///./src/ui/src/components/PortfolioItemContent/PortfolioItemContent.js", "webpack:///./src/ui/src/components/PortfolioItemContent/index.js", "webpack:///./src/ui/src/components/PortfolioItem/PortfolioItem.js", "webpack:///./src/ui/src/components/PortfolioItem/index.js", "webpack:///./src/ui/src/components/PortfolioPanel/PortfolioDragLayer.js", "webpack:///./src/ui/src/components/PortfolioPanel/PortfolioPanel.js", "webpack:///./src/ui/src/components/PortfolioPanel/index.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "ItemTypes", "OUTLINE", "PORTFOLIO", "DropLocation", "ON_TARGET_HORIZONTAL_MIDPOINT", "ABOVE_TARGET", "BELOW_TARGET", "INITIAL", "BUFFER_ROOM", "PortfolioContext", "React", "createContext", "propTypes", "portfolioItem", "PropTypes", "object", "isRequired", "isPortfolioRenaming", "bool", "setPortfolioRenaming", "func", "movePortfolio", "PortfolioItemContent", "useContext", "refreshPortfolio", "renamePortfolioItem", "removePortfolioItem", "openPortfolioItem", "downloadPortfolioItem", "isNameDuplicated", "setActivePortfolioItem", "name", "nameWithoutExtension", "extension", "id", "t", "useTranslation", "inputRef", "useRef", "useState", "isDefault", "setIsDefault", "portfolioEditName", "setPortfolioEditName", "onDoubleClick", "useCallback", "isRenameButtonDisabled", "onRenamePortfolioItem", "onCancelPortfolio", "useEffect", "current", "focus", "select", "flyoutSelector", "DataElements", "BOOKMARK_OUTLINE_FLYOUT", "currentFlyout", "useSelector", "state", "selectors", "getFlyout", "contextMenuMoreButtonOptions", "moreOptionsDataElement", "flyoutToggleElement", "contentMenuFlyoutOptions", "shouldHideDeleteButton", "type", "handleOnClick", "val", "menuTypes", "MOVE_UP", "MOVE_DOWN", "OPENFILE", "isOpenableFile", "RENAME", "DOWNLOAD", "DELETE", "className", "PanelListItem", "iconGlyph", "labelHeader", "enableMoreOptionsContextMenuFlyout", "classNames", "htmlFor", "Input", "ref", "wrapperClassName", "value", "onKeyDown", "e", "key", "stopPropagation", "onChange", "target", "<PERSON><PERSON><PERSON><PERSON>", "messageText", "message", "aria-label", "<PERSON><PERSON>", "label", "onClick", "isSubmitType", "disabled", "connectDragSource", "connectDragPreview", "connectDropTarget", "isDragging", "isDraggedUpwards", "isDraggedDownwards", "PortfolioItem", "forwardRef", "elementRef", "getEmptyImage", "captureDraggingState", "opacity", "useImperativeHandle", "getNode", "isRenaming", "setIsRenaming", "style", "displayName", "PortfolioItemNested", "<PERSON><PERSON>arget", "hover", "props", "dropTargetMonitor", "dropTargetContainer", "dragObject", "getItem", "dragPortfolioItem", "dragSourceNode", "dropPortfolioItem", "dropTargetNode", "contains", "undefined", "dropLocation", "dropTargetBoundingRect", "getBoundingClientRect", "dropTargetVerticalMiddlePoint", "height", "top", "dropTargetClientY", "getClientOffset", "y", "classList", "remove", "drop", "movePortfolioInward", "movePortfolioBeforeTarget", "movePortfolioAfterTarget", "connect", "dropTargetState", "drop<PERSON>ar<PERSON>", "isOver", "shallow", "DragSource", "beginDrag", "dragSourceMonitor", "dragSourceContainer", "sourceId", "canDrag", "dragSourceState", "dragSource", "dragPreview", "layerStyles", "position", "pointerEvents", "zIndex", "left", "width", "getItemStyles", "initialOffset", "currentOffset", "display", "x", "transform", "WebkitTransform", "PortfolioDragLayer", "useDragLayer", "dragLayerState", "itemType", "getItemType", "item", "getInitialSourceClientOffset", "renderDragItemPreview", "PortfolioPanel", "isElementDisabled", "PORTFOLIO_PANEL", "getTabManager", "getPortfolio", "shallowEqual", "isDisabled", "tabManager", "portfolioFiles", "dispatch", "useDispatch", "activePortfolioItem", "isAddingNewFolder", "setAddingNewFolder", "fileInputRef", "onAddFile", "click", "addNewFile", "files", "file", "some", "fileName", "title", "confirmBtnText", "warning", "onConfirm", "actions", "showWarningMessage", "doc", "core", "getDocument", "getPDFDoc", "pdfDoc", "addFile", "newName", "renamePortfolioFile", "getPortfolioFiles", "setPortfolio", "openElement", "LOADING_MODAL", "downloadPortfolioFile", "closeElement", "dragItem", "dropItem", "console", "log", "moveFileInArray", "dragItemId", "dropItemId", "moveDirection", "fileArray", "fromIndex", "findIndex", "dropItemIndex", "moveToIndex", "MoveDirection", "splice", "entries", "index", "order", "reorderPortfolioFile", "fileId", "direction", "DataElementWrapper", "dataElement", "PORTFOLIO_ADD_FILE", "img", "event", "Provider", "isPortfolioItemActive", "addNewFolder", "fileToRemove", "find", "deletePortfolioFile", "enableMultiTab", "addPortfolioTab", "filter", "DndProvider", "backend", "isMobileDevice", "TouchBackEnd", "HTML5Backend", "map", "isFolder", "isAdding"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,kCClEnC,sGAAO,IAAMC,EAAY,CACvBC,QAAS,UACTC,UAAW,aAGAC,EAAe,CAC1BC,8BAA+B,6BAC/BC,aAAc,cACdC,aAAc,cACdC,QAAS,WAGEC,EAAc,G,sBCZjBrC,EAAO2B,QAAU,EAAQ,GAAR,EAA+D,IAKlFR,KAAK,CAACnB,EAAOC,EAAI,wkPAAykP,M,qBCLlmP,IAAIL,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,09FAA29F,KAGp/F0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAIhC,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEzB5B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,GAAI,M,qBCL7B,IAAIL,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,6pCAA8pC,KAGvrC0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,oTCNRU,EAFUC,IAAMC,gB,+qCCc/B,IAAMC,EAAY,CAChBC,cAAeC,IAAUC,OAAOC,WAChCC,oBAAqBH,IAAUI,KAC/BC,qBAAsBL,IAAUM,KAChCC,cAAeP,IAAUM,MAGrBE,EAAuB,SAAH,GAKpB,IAJJT,EAAa,EAAbA,cACAI,EAAmB,EAAnBA,oBACAE,EAAoB,EAApBA,qBACAE,EAAa,EAAbA,cAEA,EAQIE,qBAAWd,GAPbe,EAAgB,EAAhBA,iBACAC,EAAmB,EAAnBA,oBACAC,EAAmB,EAAnBA,oBACAC,EAAiB,EAAjBA,kBACAC,EAAqB,EAArBA,sBACAC,EAAgB,EAAhBA,iBACAC,EAAsB,EAAtBA,uBAGMC,EAA8ClB,EAA9CkB,KAAMC,EAAwCnB,EAAxCmB,qBAAsBC,EAAkBpB,EAAlBoB,UAAWC,EAAOrB,EAAPqB,GAExCC,EAAqB,EAAhBC,cAAgB,GAApB,GACFC,EAAWC,mBACgC,IAAfC,oBAAS,GAAM,GAA1CC,EAAS,KAAEC,EAAY,KACkD,IAA9BF,mBAASP,GAAqB,GAAzEU,EAAiB,KAAEC,EAAoB,KAExCC,EAAgBC,uBAAY,WAE5B5B,IAIJU,EAAkBd,GAClBiB,EAAuBjB,EAAcqB,OAEpC,CAACjB,EAAqBJ,EAAec,EAAmBG,IAOrDgB,EAAyB,WAC7B,OAAQJ,GAAqBV,IAAyBU,GAAqBb,EAAiB,GAAD,OAAIa,EAAiB,YAAIT,GAAaC,IAe7Ha,EAAwB,WAC5B5B,GAAqB,GACrBM,EAAoBS,EAAI,GAAF,OAAKQ,EAAiB,YAAIT,KAG5Ce,EAAoB,WACpB/B,IACFE,GAAqB,GACrBwB,EAAqBX,IAEvBR,KAUFyB,qBAAU,WAEJhC,IACFoB,EAASa,QAAQC,QACjBd,EAASa,QAAQE,UAGnBX,GAAcxB,KACb,CAACA,IAEJ,IA2BMoC,EAAiB,GAAH,OAAMC,IAAaC,wBAAuB,YAAIrB,GAC5DsB,EAAgBC,aAAY,SAACC,GAAK,OAAKC,IAAUC,UAAUF,EAAOL,MAElEQ,EAA+B,CACnCC,uBAAwB,8BAAF,OAAgC5B,GACtD6B,oBAAqBV,GAGjBW,EAA2B,CAC/BC,wBAAwB,EACxBT,gBACAH,iBACAa,KAAM,YACNC,cAxCoB,SAACC,GACrB,OAAQA,GACN,KAAKC,IAAUC,QAGf,KAAKD,IAAUE,UACblD,EAAca,EAAIkC,GAClB,MACF,KAAKC,IAAUG,SACTC,YAAexC,IACjBN,EAAkBd,GAEpB,MACF,KAAKwD,IAAUK,OACbvD,GAAqB,GACrB,MACF,KAAKkD,IAAUM,SACb/C,EAAsBf,GACtB,MACF,KAAKwD,IAAUO,OACblD,EAAoBQ,MAuB1B,OACE,yBAAK2C,UAAU,qCACZrC,GACE,kBAACsC,EAAA,EAAa,CAACC,UA/Fb,6DA+FmCC,YAAajD,EAAMkD,oCAAoC,EAAMrC,cAAeA,EAAeiB,6BAA8BA,EAA8BG,yBAA0BA,IAExN/C,GACC,yBAAK4D,UAAWK,IAAW,CACzB,8BAA8B,EAC9B,QAAWjE,KAEX,2BAAO4D,UAAU,wBAAwBM,QAASjD,GAAKC,EAAE,qCACzD,kBAACiD,EAAA,EAAK,CACJlD,GAAIA,EACJgC,KAAK,OACLnC,KAAK,UACLsD,IAAKhD,EACLiD,iBAAiB,kBACjBC,MAAO7C,EACP8C,UAvGY,SAACC,GACP,UAAVA,EAAEC,MACJD,EAAEE,kBACE1E,IAAwB6B,KAC1BC,KAGU,WAAV0C,EAAEC,KACJ1C,KAgGM4C,SAAU,SAACH,GAAC,OAAK9C,EAAqB8C,EAAEI,OAAON,QAC/CO,WAAS,EACTC,YAhFHlE,EAAiB,GAAD,OAAIa,EAAiB,YAAIT,GAAaC,GAGpDC,EAAE,mCAFA,GAgFD6D,QAASnE,EAAiB,GAAD,OAAIa,EAAiB,YAAIT,GAAaC,GAAM,UAAY,UACjF+D,aAAA,UAAe9D,EAAE,iBAAgB,YAAIO,EAAiB,YAAIT,KAG5D,yBAAK4C,UAAU,qCACb,kBAACqB,EAAA,EAAM,CACLrB,UAAU,iCACVsB,MAAOhE,EAAE,iBACTiE,QAASpD,IAEV/B,GACC,kBAACiF,EAAA,EAAM,CACLrB,UAAU,+BACVsB,MAAOhE,EAAE,eACTkE,cAAc,EACdC,SAAUxD,IACVsD,QAASrD,QAUzBzB,EAAqBV,UAAYA,EAElBU,ICvMAA,EDuMAA,E,uiCE/Lf,IAAMV,EAAY,CAChBC,cAAeC,IAAUC,OAAOC,WAChCuF,kBAAmBzF,IAAUM,KAC7BoF,mBAAoB1F,IAAUM,KAC9BqF,kBAAmB3F,IAAUM,KAC7BsF,WAAY5F,IAAUI,KACtByF,iBAAkB7F,IAAUI,KAC5B0F,mBAAoB9F,IAAUI,KAC9BG,cAAeP,IAAUM,MAGrByF,EAAgBC,sBAAW,WAS9BzB,GAAQ,IARTxE,EAAa,EAAbA,cACA0F,EAAiB,EAAjBA,kBACAC,EAAkB,EAAlBA,mBACAC,EAAiB,EAAjBA,kBACAC,EAAU,EAAVA,WACAC,EAAgB,EAAhBA,iBACAC,EAAkB,EAAlBA,mBACAvF,EAAa,EAAbA,cAGM0F,EAAazE,iBAAO,MAC1BiE,EAAkBQ,GAClBP,EAAmBQ,cAAiB,CAAEC,sBAAsB,IAC5DR,EAAkBM,GAClB,IAAMG,EAAUR,EAAa,GAAM,EACnCS,8BAAoB9B,GAAK,iBAAO,CAC9B+B,QAAS,kBAAML,EAAW7D,aAE5B,IAAmD,IAAfX,oBAAS,GAAM,GAA5C8E,EAAU,KAAEC,EAAa,KAEhC,OACE,yBACEjC,IAAK0B,EACLlC,UAAU,yBACV0C,MAAO,CAAEL,YAET,yBAAKrC,UAAU,oBAAoB0C,MAAO,CAAEL,QAASP,EAAmB,EAAI,KAC5E,kBAAC,EAAoB,CACnBtF,cAAeA,EACfR,cAAeA,EACfI,oBAAqBoG,EACrBlG,qBAAsBmG,IAExB,yBAAKzC,UAAU,oBAAoB0C,MAAO,CAAEL,QAASN,EAAqB,EAAI,SAKpFC,EAAcjG,UAAYA,EAC1BiG,EAAcW,YAAc,gBAE5B,IAAMC,EAAsBC,YAC1B1H,IAAUE,UACV,CACEyH,MAAK,SAACC,EAAOC,EAAmBC,GAC9B,GAAKA,EAAL,CAIA,IAAMC,EAAaF,EAAkBG,UACrC,GAAKD,EAAL,CAIA,IAAQE,EAAsCF,EAAtCE,kBAAmBC,EAAmBH,EAAnBG,eACJC,EAAsBP,EAArC/G,cAEFuH,EAAiBN,EAAoBV,UAC3C,GAAKc,GAAmBE,EAAxB,CAKA,GADkDF,EAAeG,SAASD,GAIxE,OAFAL,EAAWK,oBAAiBE,OAC5BP,EAAWQ,aAAepI,IAAaI,SASzC,GALAwH,EAAWK,eAAiBA,EACbH,EAAkB/F,KACjBiG,EAAkBjG,GAGlC,CAIA,IAAMsG,EAAyBJ,EAAeK,wBACxCC,EAAiCF,EAAuBG,OAAS,EAAKH,EAAuBI,IAE7FC,EADehB,EAAkBiB,kBACAC,EACvC,QAAQ,GACN,KAAKF,EAAoBH,EACvBX,EAAWQ,aAAepI,IAAaG,aACvC8H,EAAeY,UAAUC,OAAO,aAChC,MACF,KAAKJ,EAAoBH,EACvBX,EAAWQ,aAAepI,IAAaE,aACvC+H,EAAeY,UAAUC,OAAO,aAChC,MACF,QACElB,EAAWQ,aAAepI,IAAaI,QACvC6H,EAAeY,UAAUC,OAAO,mBAItCC,KAAI,SAACtB,EAAOC,EAAmBC,GAC7B,GAAKA,EAAL,CAGA,IAAMC,EAAaF,EAAkBG,UAC7BC,EAAsCF,EAAtCE,kBAAmBG,EAAmBL,EAAnBK,eACJD,EAAgGP,EAA/G/G,cAAkCsI,EAA6EvB,EAA7EuB,oBAAqBC,EAAwDxB,EAAxDwB,0BAA2BC,EAA6BzB,EAA7ByB,yBAE1F,GAAKjB,EAAL,CAIA,OAAQL,EAAWQ,cACjB,KAAKpI,IAAaC,8BAChB+I,EAAoBlB,EAAmBE,GACvC,MACF,KAAKhI,IAAaE,aAChB+I,EAA0BnB,EAAkB/F,GAAIiG,EAAkBjG,IAClE,MACF,KAAK/B,IAAaG,aAChB+I,EAAyBpB,EAAkB/F,GAAIiG,EAAkBjG,IAKrEkG,EAAeY,UAAUC,OAAO,aAChClB,EAAWQ,aAAepI,IAAaI,aAG3C,SAAC+I,EAASC,GAAe,cAAM,CAC7B9C,kBAAmB6C,EAAQE,aAC3B7C,iBAAkB4C,EAAgBE,OAAO,CAAEC,SAAS,MAAqC,QAAzB,EAAAH,EAAgBvB,iBAAS,aAAzB,EAA2BO,gBAAiBpI,IAAaE,aACzHuG,mBAAoB2C,EAAgBE,OAAO,CAAEC,SAAS,MAAqC,QAAzB,EAAAH,EAAgBvB,iBAAS,aAAzB,EAA2BO,gBAAiBpI,IAAaG,gBAxFnGoH,CA0F1BiC,YACA3J,IAAUE,UACV,CACE0J,UAAW,SAAChC,EAAOiC,EAAmBC,GAAmB,MAAM,CAC7DC,SAAUF,EAAkBE,SAC5B9B,kBAAmBL,EAAM/G,cACzBqH,eAAgB4B,EAAoB1C,UACpCmB,aAAcpI,IAAaI,UAE7ByJ,QAAS,kBAAM,KAEjB,SAACV,EAASW,GAAe,MAAM,CAC7B1D,kBAAmB+C,EAAQY,aAC3B1D,mBAAoB8C,EAAQa,cAC5BzD,WAAYuD,EAAgBvD,gBAd9BiD,CAgBA9C,IAEFY,EAAoB7G,UAAYA,EAEjB6G,IC3KAZ,ED2KAY,E,UEzKT2C,EAAc,CAClBC,SAAU,QACVC,cAAe,OACfC,OAAQ,MACRC,KAAM,EACN5B,IAAK,EACL6B,MAAO,OACP9B,OAAQ,QAGJ+B,EAAgB,SAACC,EAAeC,GACpC,IAAKD,IAAkBC,EACrB,MAAO,CACLC,QAAS,QAGb,IAAQC,EAASF,EAATE,EAAG/B,EAAM6B,EAAN7B,EACLgC,EAAY,kBAAH,OAAqBD,EAAC,2BAAmB/B,EAAC,eACzD,MAAO,CACLgC,YACAC,gBAAiBD,IAIRE,EAAqB,WAChC,MAMIC,aAAa,SAACC,GAAc,MAAM,CACpCC,SAAUD,EAAeE,cACzBC,KAAMH,EAAenD,UACrBtB,WAAYyE,EAAezE,aAC3BiE,cAAeQ,EAAeI,+BAC9BX,cAAeO,EAAerC,sBAV9BsC,EAAQ,EAARA,SACAE,EAAI,EAAJA,KACA5E,EAAU,EAAVA,WACAiE,EAAa,EAAbA,cACAC,EAAa,EAAbA,cA2BF,OAAKlE,EAKH,yBAAKa,MAAO6C,GACV,yBACEvF,UAAU,4CACV0C,MAAOmD,EAAcC,EAAeC,IA1BZ,WAC5B,IAAKU,EACH,OAAO,KAGT,IAAQrD,EAAsBqD,EAAtBrD,kBAER,OAAImD,IAAapL,IAAUE,UAEvB,oCACG+H,EAAkBlG,MAKlB,KAaFyJ,KATE,M,45CC7DX,8lGAAApN,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,+kBAAAA,GAAA,gEAAAA,GAAA,0JAAAA,EAAA,6FAAAA,GAAA,mIAAAA,IAAA,IAAAA,IAAA,4gBAAAA,EAAA,iBAAAA,EAAA,EAAAA,IAAA,EAAAA,GAAA,EAAAA,GAAA,SAyBA,IAwPeqN,GAxPQ,WACrB,IAWC,IAPGhI,aACF,SAACC,GAAK,MAAK,CACTC,IAAU+H,kBAAkBhI,EAAOJ,IAAaqI,iBAChDhI,IAAUiI,cAAclI,GACxBC,IAAUkI,aAAanI,MAEzBoI,KACD,GAVCC,EAAU,KACVC,EAAU,KACVC,EAAc,KAUVC,EAAWC,cACVhK,EAAqB,EAAhBC,cAAgB,GAApB,GAE4D,IAAdG,mBAAS,MAAK,GAA7D6J,EAAmB,KAAEtK,EAAsB,KACa,IAAfS,oBAAS,GAAM,GAAxD8J,EAAiB,KAAEC,EAAkB,KAEtCC,EAAejK,iBAAO,MAEtBkK,EAAY,WAAM,MACtBD,SAAqB,QAAT,EAAZA,EAAcrJ,eAAO,OAArB,EAAuBuJ,SAGnBC,EAAU,6BAAG,WAAOjH,GAAC,qFACG,GACP,KADfkH,EAAQlH,EAAEI,OAAO8G,OACb7N,OAAY,iBAE2D,GADzE8N,EAAOD,EAAM,IACMV,EAAeY,MAAK,SAACvB,GAAI,OAAKA,EAAKvJ,OAAS6K,EAAK7K,QACpD,CAAF,gBACZiE,EAAU7D,EAAE,qCAAsC,CAAE2K,SAAUF,EAAK7K,OACnEgL,EAAQ5K,EAAE,+BACV6K,EAAiB7K,EAAE,sBACnB8K,EAAU,CACdjH,UACA+G,QACAC,iBACAE,UAAW,kBAAMV,MAEnBN,EAASiB,IAAQC,mBAAmBH,IAAU,wBAEhB,KAAxBI,EAAMC,IAAKC,eACR,CAAF,iCACgBF,EAAIG,YAAW,QAAxB,KAANC,EAAS,EAAH,MACA,CAAF,iCACFC,YAAQD,EAAQb,GAAK,QAC3BpL,IAAmB,4CAK5B,gBA3Be,sCAqCVC,EAAmB,6BAAG,WAAOS,EAAIyL,GAAO,iFACtCC,YAAoB1L,EAAIyL,GAAQ,OACtCnM,IAAmB,2CACpB,gBAHwB,wCAKnBA,EAAgB,6BAAG,8EACP,OADO,KACvB0K,EAAQ,KAACiB,IAAO,SAAoBU,cAAmB,6BAAtCC,aAAY,+BAC7BxB,GAAmB,GAAO,2CAC3B,kBAHqB,mCAkChB1K,EAAqB,6BAAG,WAAOf,GAAa,iEACU,OAA1DqL,EAASiB,IAAQY,YAAYzK,IAAa0K,gBAAgB,SACpDC,YAAsBpN,GAAc,OAC1CqL,EAASiB,IAAQe,aAAa5K,IAAa0K,gBAAgB,2CAC5D,gBAJ0B,sCAMrB7E,EAAsB,SAACgF,EAAUC,GAGrCC,QAAQC,IAAIH,EAASpM,KAAM,SAAUqM,EAASrM,OAI1CwM,EAAe,6BAAG,WAAOtC,EAAgBuC,EAAYC,EAAYC,GAAa,uFAE5EC,EAAY,EAAI1C,GAChB2C,EAAYD,EAAUE,WAAU,SAACjC,GAAI,OAAKA,EAAK1K,KAAOsM,KACtDM,EAAgBH,EAAUE,WAAU,SAACjC,GAAI,OAAKA,EAAK1K,KAAOuM,KAC5DM,EAAcD,EAEdJ,IAAkBM,IAAc3O,cAAgBuO,EAAYE,IAC9DC,EAAcD,EAAgB,GAG5BJ,IAAkBM,IAAc1O,cAAgBsO,EAAYE,IAC9DC,EAAcD,EAAgB,GAIhCH,EAAUM,OAAOF,EAAa,EAAGJ,EAAUM,OAAOL,EAAW,GAAG,IAAI,IACxCD,EAAUO,WAAS,0DAAxB,GAAwB,eAAnCC,EAAK,MAAEvC,EAAI,MACZwC,QAAUD,EAAK,kCAChBE,YAAqBzC,EAAK1K,GAAIiN,GAAM,wMAG/C,gBAtBoB,4CAwBf9N,EAAa,6BAAG,WAAOiO,EAAQC,GAAS,qGACf1B,cAAmB,OAGkC,GAH5E5B,EAAiB,EAAH,KACd2C,EAAY3C,EAAe4C,WAAU,SAACjC,GAAI,OAAKA,EAAK1K,KAAOoN,OAC/B,IAAdV,GAAmBW,IAAclL,IAAUC,SACzDsK,IAAc3C,EAAenN,OAAQ,GAAKyQ,IAAclL,IAAUE,WACxD,CAAF,gDAGRwK,EAAcQ,IAAclL,IAAUC,QAAUsK,EAAY,EAAIA,EAAY,EAClF3C,EAAegD,OAAOF,EAAa,EAAG9C,EAAegD,OAAOL,EAAW,GAAG,IAAI,IAClD3C,EAAeiD,WAAS,2DAA7B,GAA6B,eAAxCC,EAAK,MAAEvC,EAAI,MACZwC,QAAUD,EAAK,kCAChBE,YAAqBzC,EAAK1K,GAAIiN,GAAM,kKAGxC3N,IAAkB,iEACzB,gBAhBkB,wCAkBb4H,EAA4BvG,sBAAW,6BAAC,WAAO2L,EAAYC,GAAU,iFACnEF,EAAgBtC,EAAgBuC,EAAYC,EAAYO,IAAc3O,cAAa,OACzFmB,IAAmB,2CACpB,qDAH4C,GAG1C,CAACyK,IAEE5C,EAA2BxG,sBAAW,6BAAC,WAAO2L,EAAYC,GAAU,iFAClEF,EAAgBtC,EAAgBuC,EAAYC,EAAYO,IAAc1O,cAAa,OACzFkB,IAAmB,2CACpB,qDAH2C,GAGzC,CAACyK,IAEJ,OAAOF,EAAa,KAClB,kBAACyD,EAAA,EAAkB,CACjB3K,UAAU,8CACV4K,YAAanM,IAAaqI,iBAE1B,yBAAK9G,UAAU,iCACb,wBAAIA,UAAU,gBACX1C,EAAE,kCAGL,yBAAK0C,UAAU,2BACb,kBAACqB,EAAA,EAAM,CACLrB,UAAU,iCACV4K,YAAanM,IAAaoM,mBAC1BC,IAAI,gBACJ5C,MAAO5K,EAAE,qBACTmE,SAAU+F,EACVjG,QAASoG,IAGX,2BACEnH,IAAKkH,EACLhF,MAAO,CAAEsD,QAAS,QAClB3G,KAAK,OACL0B,SAAU,SAACgK,GACTlD,EAAWkD,GACXA,EAAM/J,OAAON,MAAQ,UAM7B,kBAAC,EAAiBsK,SAAQ,CACxBtK,MAAO,CACL6G,sBACAtK,yBACAgO,sBAAuB,SAACjP,GAAa,OAAKA,aAAa,EAAbA,EAAeqB,MAAOkK,GAChEC,oBACAC,qBACAyD,aAvJa,SAAChO,GACfA,IACHA,EAAOI,EAAE,qBAGXX,KAmJMC,sBACAC,oBAvIoB,SAACQ,GAC3B,IAQW,EARL8N,EAAe/D,EAAegE,MAAK,SAACrD,GAAI,OAAKA,EAAK1K,KAAOA,KAIzD+K,EAAU,CACdjH,QAJc7D,EAAE,4BAA6B,CAAE2K,SAAUkD,EAAajO,OAKtEgL,MAJY5K,EAAE,iBAKd6K,eAJqB7K,EAAE,iBAKvB+K,WAAS,cAAE,8FACHgD,YAAoBhO,GAAG,OAC7BV,IAAmB,2CACpB,6CAEH0K,EAASiB,IAAQC,mBAAmBH,KA0H9BtL,kBAvHkB,SAACd,GACrB4D,YAAe5D,EAAcoB,aAC/BiK,EAASiE,eACTjE,EAASiB,IAAQiD,gBAAgBvP,MAqH7Be,wBACAJ,mBACAK,iBAnHiB,SAAC8L,EAASzL,GAEjC,OADmB+J,EAAeoE,QAAO,SAACzD,GAAI,OAAKA,EAAK1K,KAAOA,KAC7C2K,MAAK,SAACD,GAAI,OAAKA,EAAK7K,OAAS4L,MAkHzC3B,eAGF,kBAACsE,EAAA,EAAW,CAACC,QAASC,IAAiBC,IAAeC,KACpD,kBAAC,EAAkB,MAEnB,yBAAK7L,UAAU,wBACZoH,EAAe0E,KAAI,SAACrF,GAAI,OACvB,kBAAC,EAAa,CACZ5F,IAAK4F,EAAKpJ,GACVrB,cAAeyK,EACfnC,oBAAqBA,EACrBC,0BAA2BA,EAC3BC,yBAA0BA,EAC1BhI,cAAeA,OAIlBgL,GACC,kBAACmD,EAAA,EAAkB,CAAC3K,UAAU,6CAC5B,kBAAC,EAAoB,CACnB9C,KAAM,GACNG,GAAI,IACJ0O,UAAQ,EACRC,UAAQ,UCrQXpF", "file": "chunks/chunk.40.js", "sourcesContent": ["var api = require(\"!../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../node_modules/css-loader/index.js!../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../node_modules/sass-loader/dist/cjs.js!./bookmarksOutlinesShared.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "export const ItemTypes = {\n  OUTLINE: 'outline',\n  PORTFOLIO: 'portfolio',\n};\n\nexport const DropLocation = {\n  ON_TARGET_HORIZONTAL_MIDPOINT: 'onTargetHorizontalMidPoint',\n  ABOVE_TARGET: 'aboveTarget',\n  BELOW_TARGET: 'belowTarget',\n  INITIAL: 'initial',\n};\n\nexport const BUFFER_ROOM = 8;", "exports = module.exports = require(\"../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".bookmark-outline-panel{display:flex;padding-left:var(--padding);padding-right:var(--padding-small)}.bookmark-outline-control-button{width:auto}.bookmark-outline-control-button span{color:inherit}.bookmark-outline-control-button,.bookmark-outline-control-button.disabled,.bookmark-outline-control-button[disabled]{color:var(--secondary-button-text)}.bookmark-outline-control-button.disabled,.bookmark-outline-control-button[disabled]{opacity:.5}.bookmark-outline-control-button.disabled span,.bookmark-outline-control-button[disabled] span{color:inherit}.bookmark-outline-control-button:not(.disabled):active,.bookmark-outline-control-button:not(.disabled):hover,.bookmark-outline-control-button:not([disabled]):active,.bookmark-outline-control-button:not([disabled]):hover{color:var(--secondary-button-hover)}.bookmark-outline-panel-header{display:flex;flex-flow:row nowrap;justify-content:space-between;align-items:center;padding:var(--padding-tiny);border-bottom:1px solid var(--divider)}.bookmark-outline-panel-header .header-title{font-size:16px}.bookmark-outline-row{flex-grow:1;overflow-y:auto}.msg-no-bookmark-outline{color:var(--placeholder-text);text-align:center}.bookmark-outline-single-container{display:flex;flex-flow:row nowrap;align-items:flex-start;border-radius:4px;margin-left:2px;margin-right:2px}.bookmark-outline-single-container.default{padding:var(--padding-tiny);border:1px solid transparent}.bookmark-outline-single-container.default.hover,.bookmark-outline-single-container.default:hover,.bookmark-outline-single-container.default[focus-within]{cursor:pointer}.bookmark-outline-single-container.default.hover,.bookmark-outline-single-container.default:focus-within,.bookmark-outline-single-container.default:hover{cursor:pointer}.bookmark-outline-single-container.default.hover .bookmark-outline-more-button,.bookmark-outline-single-container.default:hover .bookmark-outline-more-button,.bookmark-outline-single-container.default[focus-within] .bookmark-outline-more-button{display:flex;background-color:transparent}.bookmark-outline-single-container.default.hover .bookmark-outline-more-button,.bookmark-outline-single-container.default:focus-within .bookmark-outline-more-button,.bookmark-outline-single-container.default:hover .bookmark-outline-more-button{display:flex;background-color:transparent}.bookmark-outline-single-container.default[focus-within]{border-color:transparent}.bookmark-outline-single-container.default:focus-within{border-color:transparent}.bookmark-outline-single-container.default .bookmark-outline-label-row{overflow:hidden}.bookmark-outline-single-container.default.focus-visible,.bookmark-outline-single-container.default:focus-visible{outline:var(--focus-visible-outline)!important}.bookmark-outline-single-container.editing{background-color:var(--faded-component-background);padding:var(--padding-medium) 20px}.bookmark-outline-single-container.editing.focus-visible,.bookmark-outline-single-container.editing:focus-visible{outline:var(--focus-visible-outline)!important}.bookmark-outline-single-container.preview{display:inline-flex;margin-top:0;padding:var(--padding-small);background-color:var(--component-background);box-shadow:0 0 3px var(--note-box-shadow)}.bookmark-outline-single-container .bookmark-outline-checkbox{flex-grow:0;flex-shrink:0;margin-top:2px;margin-bottom:2px;margin-right:var(--padding-small)}.bookmark-outline-single-container .bookmark-outline-label-row{flex-grow:1;flex-shrink:1;display:flex;flex-flow:row wrap;align-items:flex-start;position:relative;overflow:hidden}.bookmark-outline-single-container .bookmark-outline-label{font-weight:600;flex-grow:1;flex-shrink:1;margin-bottom:var(--padding-small)}.bookmark-outline-single-container .bookmark-outline-input,.bookmark-outline-single-container .bookmark-outline-text{flex-grow:1;flex-shrink:1;flex-basis:calc(100% - 22px);margin-top:2px;margin-bottom:2px}.bookmark-outline-single-container .bookmark-text-input{margin-left:var(--padding-large)}.bookmark-outline-single-container .bookmark-outline-input{color:var(--text-color);width:calc(100% - var(--padding-large));padding:var(--padding-small);border:1px solid var(--border)}.bookmark-outline-single-container .bookmark-outline-input:focus{border-color:var(--outline-color)}.bookmark-outline-single-container .bookmark-outline-input::-moz-placeholder{color:var(--placeholder-text)}.bookmark-outline-single-container .bookmark-outline-input::placeholder{color:var(--placeholder-text)}.bookmark-outline-single-container .bookmark-outline-more-button{display:none;flex-grow:0;flex-shrink:0;width:16px;height:16px;margin:2px 2px 2px var(--padding-tiny)}.bookmark-outline-single-container .bookmark-outline-more-button .Icon{width:14px;height:14px}.bookmark-outline-single-container .bookmark-outline-more-button.icon-only:hover:not(:disabled):not(.disabled){box-shadow:none;outline:solid 1px var(--hover-border)}.bookmark-outline-single-container .bookmark-outline-more-button[focus-within].icon-only{border:none;box-shadow:none}.bookmark-outline-single-container .bookmark-outline-more-button:focus-within.icon-only{border:none;box-shadow:none}.bookmark-outline-single-container .bookmark-outline-more-button[focus-within] .Icon{color:var(--focus-border)}.bookmark-outline-single-container .bookmark-outline-more-button:focus-within .Icon{color:var(--focus-border)}.bookmark-outline-single-container .bookmark-outline-editing-controls{padding:2px;flex-basis:100%;display:flex;flex-flow:row wrap;justify-content:flex-end;align-items:center;margin-top:var(--padding-medium)}.bookmark-outline-single-container .bookmark-outline-cancel-button,.bookmark-outline-single-container .bookmark-outline-save-button{width:auto;padding:6px var(--padding)}.bookmark-outline-single-container .bookmark-outline-cancel-button{color:var(--secondary-button-text)}.bookmark-outline-single-container .bookmark-outline-cancel-button:hover{color:var(--secondary-button-hover)}.bookmark-outline-single-container .bookmark-outline-save-button{color:var(--primary-button-text);background-color:var(--primary-button);margin-left:var(--padding-tiny);border-radius:4px}.bookmark-outline-single-container .bookmark-outline-save-button:hover{background-color:var(--primary-button-hover)}.bookmark-outline-single-container .bookmark-outline-save-button.disabled,.bookmark-outline-single-container .bookmark-outline-save-button:disabled{background-color:var(--primary-button)!important;opacity:.5}.bookmark-outline-single-container .bookmark-outline-save-button.disabled span,.bookmark-outline-single-container .bookmark-outline-save-button:disabled span{color:var(--primary-button-text)}.bookmark-outline-footer{border-top:1.5px solid var(--gray-4);padding-top:var(--padding-medium);padding-bottom:var(--padding-medium);display:flex;justify-content:center;align-items:center}.bookmark-outline-footer .add-new-button .Icon{width:14px;height:14px;margin-right:var(--padding-tiny);color:inherit;fill:currentColor}.bookmark-outline-footer .add-new-button.disabled .Icon.disabled,.bookmark-outline-footer .add-new-button.disabled .Icon.disabled path,.bookmark-outline-footer .add-new-button[disabled] .Icon.disabled,.bookmark-outline-footer .add-new-button[disabled] .Icon.disabled path{color:inherit;fill:currentColor}.bookmark-outline-footer .multi-selection-button{width:auto;padding:7px}.bookmark-outline-footer .multi-selection-button .Icon{width:18px;height:18px}.bookmark-outline-footer .multi-selection-button:not(:first-child){margin-left:var(--padding-tiny)}.bookmark-outline-footer .multi-selection-button:hover{background-color:transparent}.bookmark-outline-footer .multi-selection-button.disabled:hover,.bookmark-outline-footer .multi-selection-button:disabled:hover{box-shadow:none}\", \"\"]);\n\n// exports\n", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./PortfolioItemContent.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.PortfolioPanel .bookmark-outline-single-container .bookmark-outline-label-row{align-items:center}.PortfolioPanel .bookmark-outline-single-container .bookmark-outline-label-row.editing{background-color:var(--faded-component-background);padding:var(--padding-medium) 20px}.PortfolioPanel .bookmark-outline-single-container .bookmark-outline-label-row.editing.focus-visible,.PortfolioPanel .bookmark-outline-single-container .bookmark-outline-label-row.editing:focus-visible{outline:var(--focus-visible-outline)}.PortfolioPanel .bookmark-outline-single-container .portfolio-input-label{font-weight:600;padding-bottom:var(--padding-small)}.PortfolioPanel .bookmark-outline-single-container .portfolio-input{-webkit-font-smoothing:antialiased}.PortfolioPanel .bookmark-outline-single-container .portfolio-input .ui__input{border-color:var(--border)}.PortfolioPanel .bookmark-outline-single-container .portfolio-input .ui__input.ui__input--message-warning{border-color:var(--error-border-color)}.PortfolioPanel .bookmark-outline-single-container .portfolio-input .ui__input.ui__input--message-warning.ui__input--focused{box-shadow:none}.PortfolioPanel .bookmark-outline-single-container .portfolio-input .ui__input.ui__input--message-warning .ui__input__icon svg{fill:var(--error-border-color)}.PortfolioPanel .bookmark-outline-single-container .portfolio-input .ui__input.ui__input--message-default.ui__input--focused{border-color:var(--outline-color);box-shadow:none}.PortfolioPanel .bookmark-outline-single-container .portfolio-input .ui__input .ui__input__input{padding:var(--padding-small);color:var(--text-color);height:auto;overflow:auto;line-height:normal}.PortfolioPanel .bookmark-outline-single-container .portfolio-input .ui__input .ui__input__input::-moz-placeholder{color:var(--placeholder-text)}.PortfolioPanel .bookmark-outline-single-container .portfolio-input .ui__input .ui__input__input::placeholder{color:var(--placeholder-text)}.PortfolioPanel .bookmark-outline-single-container .portfolio-input .ui__input__messageText{color:var(--error-text-color);margin-top:var(--padding-small);font-size:inherit}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./PortfolioItem.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \"\", \"\"]);\n\n// exports\n", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./PortfolioPanel.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.PortfolioPanel .portfolio-panel-control{display:flex}.PortfolioPanel .bookmark-outline-row{padding-top:6px}.PortfolioPanel .bookmark-outline-panel-header .header-title{font-size:16px;font-weight:400;margin:0}.PortfolioPanel .portfolio-panel-list{padding-top:8px}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React from 'react';\n\nconst PortfolioContext = React.createContext();\n\nexport default PortfolioContext;", "import React, { useContext, useEffect, useRef, useState, useCallback } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { useSelector } from 'react-redux';\nimport PropTypes from 'prop-types';\nimport { menuTypes } from '../MoreOptionsContextMenuFlyout/MoreOptionsContextMenuFlyout';\nimport DataElements from 'constants/dataElement';\nimport selectors from 'selectors';\nimport { Input } from '@pdftron/webviewer-react-toolkit';\nimport Button from 'components/Button';\nimport PortfolioContext from 'components/PortfolioPanel/PortfolioContext';\nimport { isOpenableFile } from 'helpers/portfolio';\nimport '../../constants/bookmarksOutlinesShared.scss';\nimport './PortfolioItemContent.scss';\nimport PanelListItem from '../PanelListItem/PanelListItem';\nimport classNames from 'classnames';\n\nconst propTypes = {\n  portfolioItem: PropTypes.object.isRequired,\n  isPortfolioRenaming: PropTypes.bool,\n  setPortfolioRenaming: PropTypes.func,\n  movePortfolio: PropTypes.func,\n};\n\nconst PortfolioItemContent = ({\n  portfolioItem,\n  isPortfolioRenaming,\n  setPortfolioRenaming,\n  movePortfolio\n}) => {\n  const {\n    refreshPortfolio,\n    renamePortfolioItem,\n    removePortfolioItem,\n    openPortfolioItem,\n    downloadPortfolioItem,\n    isNameDuplicated,\n    setActivePortfolioItem,\n  } = useContext(PortfolioContext);\n\n  const { name, nameWithoutExtension, extension, id } = portfolioItem;\n\n  const [t] = useTranslation();\n  const inputRef = useRef();\n  const [isDefault, setIsDefault] = useState(false);\n  const [portfolioEditName, setPortfolioEditName] = useState(nameWithoutExtension);\n\n  const onDoubleClick = useCallback(() => {\n    // If the item is in renaming-mode, double-clicking on it won't do anything\n    if (isPortfolioRenaming) {\n      return;\n    }\n\n    openPortfolioItem(portfolioItem);\n    setActivePortfolioItem(portfolioItem.id);\n\n  }, [isPortfolioRenaming, portfolioItem, openPortfolioItem, setActivePortfolioItem]);\n\n\n  const getIcon = () => {\n    return 'icon-header-page-manipulation-page-layout-single-page-line';\n  };\n\n  const isRenameButtonDisabled = () => {\n    return !portfolioEditName || nameWithoutExtension === portfolioEditName || isNameDuplicated(`${portfolioEditName}.${extension}`, id);\n  };\n\n  const handleKeyDown = (e) => {\n    if (e.key === 'Enter') {\n      e.stopPropagation();\n      if (isPortfolioRenaming && !isRenameButtonDisabled()) {\n        onRenamePortfolioItem();\n      }\n    }\n    if (e.key === 'Escape') {\n      onCancelPortfolio();\n    }\n  };\n\n  const onRenamePortfolioItem = () => {\n    setPortfolioRenaming(false);\n    renamePortfolioItem(id, `${portfolioEditName}.${extension}`);\n  };\n\n  const onCancelPortfolio = () => {\n    if (isPortfolioRenaming) {\n      setPortfolioRenaming(false);\n      setPortfolioEditName(nameWithoutExtension);\n    }\n    refreshPortfolio();\n  };\n\n  const duplicatedMessage = () => {\n    if (!isNameDuplicated(`${portfolioEditName}.${extension}`, id)) {\n      return '';\n    }\n    return t('portfolio.fileNameAlreadyExists');\n  };\n\n  useEffect(() => {\n    // when in adding or renaming mode, input is automatically focused\n    if (isPortfolioRenaming) {\n      inputRef.current.focus();\n      inputRef.current.select();\n    }\n\n    setIsDefault(!isPortfolioRenaming);\n  }, [isPortfolioRenaming]);\n\n  const handleOnClick = (val) => {\n    switch (val) {\n      case menuTypes.MOVE_UP:\n        movePortfolio(id, val);\n        break;\n      case menuTypes.MOVE_DOWN:\n        movePortfolio(id, val);\n        break;\n      case menuTypes.OPENFILE:\n        if (isOpenableFile(extension)) {\n          openPortfolioItem(portfolioItem);\n        }\n        break;\n      case menuTypes.RENAME:\n        setPortfolioRenaming(true);\n        break;\n      case menuTypes.DOWNLOAD:\n        downloadPortfolioItem(portfolioItem);\n        break;\n      case menuTypes.DELETE:\n        removePortfolioItem(id);\n        break;\n      default:\n        break;\n    }\n  };\n\n  const flyoutSelector = `${DataElements.BOOKMARK_OUTLINE_FLYOUT}-${id}`;\n  const currentFlyout = useSelector((state) => selectors.getFlyout(state, flyoutSelector));\n\n  const contextMenuMoreButtonOptions = {\n    moreOptionsDataElement: `portfolio-item-more-button-${id}`,\n    flyoutToggleElement: flyoutSelector,\n  };\n\n  const contentMenuFlyoutOptions = {\n    shouldHideDeleteButton: false,\n    currentFlyout,\n    flyoutSelector,\n    type: 'portfolio',\n    handleOnClick,\n  };\n\n  return (\n    <div className='bookmark-outline-single-container'>\n      {isDefault &&\n         <PanelListItem iconGlyph={getIcon()} labelHeader={name} enableMoreOptionsContextMenuFlyout={true} onDoubleClick={onDoubleClick} contextMenuMoreButtonOptions={contextMenuMoreButtonOptions} contentMenuFlyoutOptions={contentMenuFlyoutOptions}/>\n      }\n      {isPortfolioRenaming &&\n        <div className={classNames({\n          'bookmark-outline-label-row': true,\n          'editing': isPortfolioRenaming,\n        })}>\n          <label className='portfolio-input-label' htmlFor={id}>{t('portfolio.portfolioDocumentTitle')}</label>\n          <Input\n            id={id}\n            type=\"text\"\n            name=\"outline\"\n            ref={inputRef}\n            wrapperClassName=\"portfolio-input\"\n            value={portfolioEditName}\n            onKeyDown={handleKeyDown}\n            onChange={(e) => setPortfolioEditName(e.target.value)}\n            fillWidth\n            messageText={duplicatedMessage()}\n            message={isNameDuplicated(`${portfolioEditName}.${extension}`, id) ? 'warning' : 'default'}\n            aria-label={`${t('action.rename')} ${portfolioEditName}.${extension}`}\n          />\n\n          <div className=\"bookmark-outline-editing-controls\">\n            <Button\n              className=\"bookmark-outline-cancel-button\"\n              label={t('action.cancel')}\n              onClick={onCancelPortfolio}\n            />\n            {isPortfolioRenaming &&\n              <Button\n                className=\"bookmark-outline-save-button\"\n                label={t('action.save')}\n                isSubmitType={true}\n                disabled={isRenameButtonDisabled()}\n                onClick={onRenamePortfolioItem}\n              />\n            }\n          </div>\n        </div>\n      }\n    </div>\n  );\n};\n\nPortfolioItemContent.propTypes = propTypes;\n\nexport default PortfolioItemContent;\n", "import PortfolioItemContent from './PortfolioItemContent';\n\nexport default PortfolioItemContent;\n", "import React, { useState, forwardRef, useImperativeHandle, useRef } from 'react';\nimport PropTypes from 'prop-types';\nimport { DragSource, DropTarget } from 'react-dnd';\nimport { getEmptyImage } from 'react-dnd-html5-backend';\nimport { ItemTypes, DropLocation } from 'constants/dnd';\n\nimport PortfolioItemContent from 'components/PortfolioItemContent';\n\nimport './PortfolioItem.scss';\n\nconst propTypes = {\n  portfolioItem: PropTypes.object.isRequired,\n  connectDragSource: PropTypes.func,\n  connectDragPreview: PropTypes.func,\n  connectDropTarget: PropTypes.func,\n  isDragging: PropTypes.bool,\n  isDraggedUpwards: PropTypes.bool,\n  isDraggedDownwards: PropTypes.bool,\n  movePortfolio: PropTypes.func,\n};\n\nconst PortfolioItem = forwardRef(({\n  portfolioItem,\n  connectDragSource,\n  connectDragPreview,\n  connectDropTarget,\n  isDragging,\n  isDraggedUpwards,\n  isDraggedDownwards,\n  movePortfolio\n}, ref) => {\n\n  const elementRef = useRef(null);\n  connectDragSource(elementRef);\n  connectDragPreview(getEmptyImage(), { captureDraggingState: true });\n  connectDropTarget(elementRef);\n  const opacity = isDragging ? 0.5 : 1;\n  useImperativeHandle(ref, () => ({\n    getNode: () => elementRef.current,\n  }));\n  const [isRenaming, setIsRenaming] = useState(false);\n\n  return (\n    <div\n      ref={elementRef}\n      className=\"outline-drag-container\"\n      style={{ opacity }}\n    >\n      <div className=\"outline-drag-line\" style={{ opacity: isDraggedUpwards ? 1 : 0 }} />\n      <PortfolioItemContent\n        movePortfolio={movePortfolio}\n        portfolioItem={portfolioItem}\n        isPortfolioRenaming={isRenaming}\n        setPortfolioRenaming={setIsRenaming}\n      />\n      <div className=\"outline-drag-line\" style={{ opacity: isDraggedDownwards ? 1 : 0 }} />\n    </div>\n  );\n});\n\nPortfolioItem.propTypes = propTypes;\nPortfolioItem.displayName = 'PortfolioItem';\n\nconst PortfolioItemNested = DropTarget(\n  ItemTypes.PORTFOLIO,\n  {\n    hover(props, dropTargetMonitor, dropTargetContainer) {\n      if (!dropTargetContainer) {\n        return;\n      }\n\n      const dragObject = dropTargetMonitor.getItem();\n      if (!dragObject) {\n        return;\n      }\n\n      const { dragPortfolioItem, dragSourceNode } = dragObject;\n      const { portfolioItem: dropPortfolioItem } = props;\n\n      const dropTargetNode = dropTargetContainer.getNode();\n      if (!dragSourceNode || !dropTargetNode) {\n        return;\n      }\n\n      const portfolioItemIsBeingDraggedIntoDescendant = dragSourceNode.contains(dropTargetNode);\n      if (portfolioItemIsBeingDraggedIntoDescendant) {\n        dragObject.dropTargetNode = undefined;\n        dragObject.dropLocation = DropLocation.INITIAL;\n        return;\n      }\n\n      dragObject.dropTargetNode = dropTargetNode;\n      const dragId = dragPortfolioItem.id;\n      const hoverId = dropPortfolioItem.id;\n      // do nothing if drag object and drop object are the same item\n      // depends on the data structure, could have more conditions here\n      if (dragId === hoverId) {\n        return;\n      }\n\n      const dropTargetBoundingRect = dropTargetNode.getBoundingClientRect();\n      const dropTargetVerticalMiddlePoint = (dropTargetBoundingRect.height / 2) + dropTargetBoundingRect.top;\n      const clientOffset = dropTargetMonitor.getClientOffset();\n      const dropTargetClientY = clientOffset.y;\n      switch (true) {\n        case dropTargetClientY > dropTargetVerticalMiddlePoint:\n          dragObject.dropLocation = DropLocation.BELOW_TARGET;\n          dropTargetNode.classList.remove('isNesting');\n          break;\n        case dropTargetClientY < dropTargetVerticalMiddlePoint:\n          dragObject.dropLocation = DropLocation.ABOVE_TARGET;\n          dropTargetNode.classList.remove('isNesting');\n          break;\n        default:\n          dragObject.dropLocation = DropLocation.INITIAL;\n          dropTargetNode.classList.remove('isNesting');\n          break;\n      }\n    },\n    drop(props, dropTargetMonitor, dropTargetContainer) {\n      if (!dropTargetContainer) {\n        return;\n      }\n      const dragObject = dropTargetMonitor.getItem();\n      const { dragPortfolioItem, dropTargetNode } = dragObject;\n      const { portfolioItem: dropPortfolioItem, movePortfolioInward, movePortfolioBeforeTarget, movePortfolioAfterTarget } = props;\n\n      if (!dropTargetNode) {\n        return;\n      }\n\n      switch (dragObject.dropLocation) {\n        case DropLocation.ON_TARGET_HORIZONTAL_MIDPOINT:\n          movePortfolioInward(dragPortfolioItem, dropPortfolioItem);\n          break;\n        case DropLocation.ABOVE_TARGET:\n          movePortfolioBeforeTarget(dragPortfolioItem.id, dropPortfolioItem.id);\n          break;\n        case DropLocation.BELOW_TARGET:\n          movePortfolioAfterTarget(dragPortfolioItem.id, dropPortfolioItem.id);\n          break;\n        default:\n          break;\n      }\n      dropTargetNode.classList.remove('isNesting');\n      dragObject.dropLocation = DropLocation.INITIAL;\n    }\n  },\n  (connect, dropTargetState) => ({\n    connectDropTarget: connect.dropTarget(),\n    isDraggedUpwards: dropTargetState.isOver({ shallow: true }) && (dropTargetState.getItem()?.dropLocation === DropLocation.ABOVE_TARGET),\n    isDraggedDownwards: dropTargetState.isOver({ shallow: true }) && (dropTargetState.getItem()?.dropLocation === DropLocation.BELOW_TARGET),\n  })\n)(DragSource(\n  ItemTypes.PORTFOLIO,\n  {\n    beginDrag: (props, dragSourceMonitor, dragSourceContainer) => ({\n      sourceId: dragSourceMonitor.sourceId,\n      dragPortfolioItem: props.portfolioItem,\n      dragSourceNode: dragSourceContainer.getNode(),\n      dropLocation: DropLocation.INITIAL,\n    }),\n    canDrag: () => true,\n  },\n  (connect, dragSourceState) => ({\n    connectDragSource: connect.dragSource(),\n    connectDragPreview: connect.dragPreview(),\n    isDragging: dragSourceState.isDragging(),\n  })\n)(PortfolioItem));\n\nPortfolioItemNested.propTypes = propTypes;\n\nexport default PortfolioItemNested;\n", "import PortfolioItem from './PortfolioItem';\n\nexport default PortfolioItem;\n", "import React from 'react';\nimport { useDragLayer } from 'react-dnd';\nimport { ItemTypes } from 'constants/dnd';\n\nconst layerStyles = {\n  position: 'fixed',\n  pointerEvents: 'none',\n  zIndex: 99999,\n  left: 0,\n  top: 0,\n  width: '100%',\n  height: '100%'\n};\n\nconst getItemStyles = (initialOffset, currentOffset) => {\n  if (!initialOffset || !currentOffset) {\n    return {\n      display: 'none'\n    };\n  }\n  const { x, y } = currentOffset;\n  const transform = `translate(calc(${x}px - 50%), calc(${y}px - 100%))`;\n  return {\n    transform,\n    WebkitTransform: transform,\n  };\n};\n\nexport const PortfolioDragLayer = () => {\n  const {\n    itemType,\n    item,\n    isDragging,\n    initialOffset,\n    currentOffset\n  } = useDragLayer((dragLayerState) => ({\n    itemType: dragLayerState.getItemType(),\n    item: dragLayerState.getItem(),\n    isDragging: dragLayerState.isDragging(),\n    initialOffset: dragLayerState.getInitialSourceClientOffset(),\n    currentOffset: dragLayerState.getClientOffset(),\n  }));\n\n  const renderDragItemPreview = () => {\n    if (!item) {\n      return null;\n    }\n\n    const { dragPortfolioItem } = item;\n\n    if (itemType === ItemTypes.PORTFOLIO) {\n      return (\n        <>\n          {dragPortfolioItem.name}\n        </>\n      );\n    }\n\n    return null;\n  };\n\n  if (!isDragging) {\n    return null;\n  }\n\n  return (\n    <div style={layerStyles}>\n      <div\n        className=\"bookmark-outline-single-container preview\"\n        style={getItemStyles(initialOffset, currentOffset)}\n      >\n        {renderDragItemPreview()}\n      </div>\n    </div>\n  );\n};\n", "import React, { useState, useRef, useCallback } from 'react';\nimport { shallowEqual, useSelector, useDispatch } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\nimport { DndProvider } from 'react-dnd';\nimport TouchBackEnd from 'react-dnd-touch-backend';\nimport { HTML5Backend } from 'react-dnd-html5-backend';\nimport selectors from 'selectors';\nimport actions from 'actions';\n\nimport Button from 'components/Button';\nimport PortfolioContext from './PortfolioContext';\nimport PortfolioItem from 'components/PortfolioItem';\nimport PortfolioItemContent from 'components/PortfolioItemContent';\nimport { PortfolioDragLayer } from './PortfolioDragLayer';\nimport DataElementWrapper from 'components/DataElementWrapper';\nimport DataElements from 'constants/dataElement';\nimport { DropLocation as MoveDirection } from 'constants/dnd';\nimport { isMobileDevice } from 'helpers/device';\nimport { enableMultiTab } from 'helpers/TabManager';\nimport { addFile, deletePortfolioFile, downloadPortfolioFile, getPortfolioFiles, isOpenableFile, renamePortfolioFile, reorderPortfolioFile } from 'helpers/portfolio';\nimport core from 'core';\n\nimport '../../constants/bookmarksOutlinesShared.scss';\nimport './PortfolioPanel.scss';\nimport { menuTypes } from '../MoreOptionsContextMenuFlyout/MoreOptionsContextMenuFlyout';\n\nconst PortfolioPanel = () => {\n  const [\n    isDisabled,\n    tabManager,\n    portfolioFiles,\n  ] = useSelector(\n    (state) => [\n      selectors.isElementDisabled(state, DataElements.PORTFOLIO_PANEL),\n      selectors.getTabManager(state),\n      selectors.getPortfolio(state),\n    ],\n    shallowEqual,\n  );\n\n  const dispatch = useDispatch();\n  const [t] = useTranslation();\n\n  const [activePortfolioItem, setActivePortfolioItem] = useState(null);\n  const [isAddingNewFolder, setAddingNewFolder] = useState(false);\n\n  const fileInputRef = useRef(null);\n\n  const onAddFile = () => {\n    fileInputRef?.current?.click();\n  };\n\n  const addNewFile = async (e) => {\n    const files = e.target.files;\n    if (files.length === 1) {\n      const file = files[0];\n      const isNameConflicted = portfolioFiles.some((item) => item.name === file.name);\n      if (isNameConflicted) {\n        const message = t('portfolio.fileAlreadyExistsMessage', { fileName: file.name });\n        const title = t('portfolio.fileAlreadyExists');\n        const confirmBtnText = t('portfolio.reselect');\n        const warning = {\n          message,\n          title,\n          confirmBtnText,\n          onConfirm: () => onAddFile()\n        };\n        dispatch(actions.showWarningMessage(warning));\n      } else {\n        const doc = core.getDocument();\n        if (doc) {\n          const pdfDoc = await doc.getPDFDoc();\n          if (pdfDoc) {\n            await addFile(pdfDoc, file);\n            refreshPortfolio();\n          }\n        }\n      }\n    }\n  };\n\n  const addNewFolder = (name) => {\n    if (!name) {\n      name = t('message.untitled');\n    }\n    // TODO: add new folder to portfolio here\n    refreshPortfolio();\n  };\n\n  const renamePortfolioItem = async (id, newName) => {\n    await renamePortfolioFile(id, newName);\n    refreshPortfolio();\n  };\n\n  const refreshPortfolio = async () => {\n    dispatch(actions.setPortfolio(await getPortfolioFiles()));\n    setAddingNewFolder(false);\n  };\n\n  const removePortfolioItem = (id) => {\n    const fileToRemove = portfolioFiles.find((file) => file.id === id);\n    const message = t('portfolio.deletePortfolio', { fileName: fileToRemove.name });\n    const title = t('action.delete');\n    const confirmBtnText = t('action.delete');\n    const warning = {\n      message,\n      title,\n      confirmBtnText,\n      onConfirm: async () => {\n        await deletePortfolioFile(id);\n        refreshPortfolio();\n      },\n    };\n    dispatch(actions.showWarningMessage(warning));\n  };\n\n  const openPortfolioItem = (portfolioItem) => {\n    if (isOpenableFile(portfolioItem.extension)) {\n      dispatch(enableMultiTab());\n      dispatch(actions.addPortfolioTab(portfolioItem));\n    }\n  };\n\n  const isNameDuplicated = (newName, id) => {\n    const otherFiles = portfolioFiles.filter((file) => file.id !== id);\n    return otherFiles.some((file) => file.name === newName);\n  };\n\n  const downloadPortfolioItem = async (portfolioItem) => {\n    dispatch(actions.openElement(DataElements.LOADING_MODAL));\n    await downloadPortfolioFile(portfolioItem);\n    dispatch(actions.closeElement(DataElements.LOADING_MODAL));\n  };\n\n  const movePortfolioInward = (dragItem, dropItem) => {\n    /* eslint-disable no-console */\n    // no-console function is empty for now\n    console.log(dragItem.name, 'Inward', dropItem.name);\n    /* eslint-enable no-console */\n  };\n\n  const moveFileInArray = async (portfolioFiles, dragItemId, dropItemId, moveDirection) => {\n    // clone to another array to avoid modifying portfolioFiles\n    const fileArray = [...portfolioFiles];\n    const fromIndex = fileArray.findIndex((file) => file.id === dragItemId);\n    const dropItemIndex = fileArray.findIndex((file) => file.id === dropItemId);\n    let moveToIndex = dropItemIndex;\n    // If move 1 to before 3, we want to delete 1 and re-insert at index (3-1=2)\n    if (moveDirection === MoveDirection.ABOVE_TARGET && fromIndex < dropItemIndex) {\n      moveToIndex = dropItemIndex - 1;\n    }\n    // If move 3 to after 1, we want to delete 3 and re-insert at index (1+1=2)\n    if (moveDirection === MoveDirection.BELOW_TARGET && fromIndex > dropItemIndex) {\n      moveToIndex = dropItemIndex + 1;\n    }\n\n    // Move elements in an array based on index\n    fileArray.splice(moveToIndex, 0, fileArray.splice(fromIndex, 1)[0]);\n    for (const [index, file] of fileArray.entries()) {\n      if (file.order !== index) {\n        await reorderPortfolioFile(file.id, index);\n      }\n    }\n  };\n\n  const movePortfolio = async (fileId, direction) => {\n    const portfolioFiles = await getPortfolioFiles();\n    const fromIndex = portfolioFiles.findIndex((file) => file.id === fileId);\n    const outOfBound = (fromIndex === 0 && direction === menuTypes.MOVE_UP)\n      || (fromIndex === portfolioFiles.length -1 && direction === menuTypes.MOVE_DOWN);\n    if (outOfBound) {\n      return;\n    }\n    const moveToIndex = direction === menuTypes.MOVE_UP ? fromIndex - 1 : fromIndex + 1;\n    portfolioFiles.splice(moveToIndex, 0, portfolioFiles.splice(fromIndex, 1)[0]);\n    for (const [index, file] of portfolioFiles.entries()) {\n      if (file.order !== index) {\n        await reorderPortfolioFile(file.id, index);\n      }\n    }\n    await refreshPortfolio();\n  };\n\n  const movePortfolioBeforeTarget = useCallback(async (dragItemId, dropItemId) => {\n    await moveFileInArray(portfolioFiles, dragItemId, dropItemId, MoveDirection.ABOVE_TARGET);\n    refreshPortfolio();\n  }, [portfolioFiles]);\n\n  const movePortfolioAfterTarget = useCallback(async (dragItemId, dropItemId) => {\n    await moveFileInArray(portfolioFiles, dragItemId, dropItemId, MoveDirection.BELOW_TARGET);\n    refreshPortfolio();\n  }, [portfolioFiles]);\n\n  return isDisabled ? null : (\n    <DataElementWrapper\n      className=\"Panel PortfolioPanel bookmark-outline-panel\"\n      dataElement={DataElements.PORTFOLIO_PANEL}\n    >\n      <div className=\"bookmark-outline-panel-header\">\n        <h2 className=\"header-title\">\n          {t('portfolio.portfolioPanelTitle')}\n        </h2>\n\n        <div className=\"portfolio-panel-control\">\n          <Button\n            className=\"portfolio-panel-control-button\"\n            dataElement={DataElements.PORTFOLIO_ADD_FILE}\n            img=\"icon-add-file\"\n            title={t('portfolio.addFile')}\n            disabled={isAddingNewFolder}\n            onClick={onAddFile}\n          />\n\n          <input\n            ref={fileInputRef}\n            style={{ display: 'none' }}\n            type=\"file\"\n            onChange={(event) => {\n              addNewFile(event);\n              event.target.value = null;\n            }}\n          />\n        </div>\n      </div>\n\n      <PortfolioContext.Provider\n        value={{\n          activePortfolioItem,\n          setActivePortfolioItem,\n          isPortfolioItemActive: (portfolioItem) => portfolioItem?.id === activePortfolioItem,\n          isAddingNewFolder,\n          setAddingNewFolder,\n          addNewFolder,\n          renamePortfolioItem,\n          removePortfolioItem,\n          openPortfolioItem,\n          downloadPortfolioItem,\n          refreshPortfolio,\n          isNameDuplicated,\n          tabManager,\n        }}\n      >\n        <DndProvider backend={isMobileDevice ? TouchBackEnd : HTML5Backend}>\n          <PortfolioDragLayer />\n\n          <div className='portfolio-panel-list'>\n            {portfolioFiles.map((item) => (\n              <PortfolioItem\n                key={item.id}\n                portfolioItem={item}\n                movePortfolioInward={movePortfolioInward}\n                movePortfolioBeforeTarget={movePortfolioBeforeTarget}\n                movePortfolioAfterTarget={movePortfolioAfterTarget}\n                movePortfolio={movePortfolio}\n              />\n            ))}\n\n            {isAddingNewFolder && (\n              <DataElementWrapper className=\"bookmark-outline-single-container editing\">\n                <PortfolioItemContent\n                  name={''}\n                  id={'0'}\n                  isFolder\n                  isAdding\n                />\n              </DataElementWrapper>\n            )}\n          </div>\n        </DndProvider>\n      </PortfolioContext.Provider>\n    </DataElementWrapper>\n  );\n};\n\nexport default PortfolioPanel;\n", "import PortfolioPanel from './PortfolioPanel';\n\nexport default PortfolioPanel;\n"], "sourceRoot": ""}