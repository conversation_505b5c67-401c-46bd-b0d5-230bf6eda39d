{"version": 3, "sources": ["webpack:///./src/ui/src/components/RubberStampPanel/RubberStampPanel.scss?9f58", "webpack:///./src/ui/src/components/RubberStampPanel/RubberStampPanel.scss", "webpack:///./src/ui/src/components/RubberStampPanel/CreateRubberStampButton.js", "webpack:///./src/ui/src/components/RubberStampPanel/StandardRubberStamps.js", "webpack:///./src/ui/src/components/RubberStampPanel/CustomRubberStamps.js", "webpack:///./src/ui/src/components/RubberStampPanel/RubberStampPanel.js", "webpack:///./src/ui/src/components/RubberStampPanel/index.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "CreateRubberStampButton", "dispatch", "useDispatch", "t", "useTranslation", "openRubberStampModalWithFocus", "useFocusHandler", "actions", "openElement", "DataElements", "CUSTOM_STAMP_MODAL", "DataElementWrapper", "dataElement", "CREATE_RUBBER_STAMP_BUTTON_WRAP", "<PERSON><PERSON>", "className", "CREATE_RUBBER_STAMP_BUTTON", "label", "onClick", "RubberStamp", "React", "memo", "imgSrc", "annotation", "index", "isActive", "aria<PERSON><PERSON><PERSON>", "Icon", "tabIndex", "key", "classNames", "aria-label", "aria-current", "src", "alt", "displayName", "propTypes", "PropTypes", "string", "object", "number", "func", "bool", "StandardRubberStamps", "standardStamps", "selectedStampIndex", "setSelectedRubberStamp", "isMobile", "isMobileSize", "useSelector", "state", "selectors", "getMobilePanelSize", "getLastSelectedStampIndex", "mobilePanelSize", "lastSelectedStampIndex", "rubberStamps", "map", "isStampActive", "shouldShowOnlyFirstStamp", "isNull", "PANEL_SIZES", "SMALL_SIZE", "header", "useCallback", "CollapsibleSection", "headingLevel", "ariaControls", "expansionDescription", "id", "CustomRubberStamp", "stampInfo", "delete<PERSON><PERSON><PERSON>", "standardStampsOffset", "customStampData", "getCustomData", "JSON", "parse", "e", "title", "author", "DateCreated", "data-element", "img", "CustomRubberStamps", "customStamps", "stampToolArray", "core", "getToolsFromAllDocumentViewers", "deleteCustomStamp", "deletedIndex", "tool", "stamps", "getCustomStamps", "deleteCustomStamps", "indexToShow", "setSelectedStampIndex", "setLastSelectedStampIndex", "isMobileModeWithLargerSize", "isMobileModeSmallSize", "customStampIndex", "array", "RubberStampPanel", "getStandardStamps", "shallowEqual", "getSelectedStampIndex", "featureFlags", "getFeatureFlags", "store", "useStore", "customizableUI", "setToolMode", "text", "setRubberStamp", "showPreview", "setMobilePanelSize", "useEffect", "onVisibilityChanged", "activeTool", "getToolMode", "activeToolName", "name", "detail", "element", "isVisible", "panelNames", "RUBBER_STAMP", "defaultTool", "setToolModeAndGroup", "addEventListener", "Events", "VISIBILITY_CHANGED", "removeEventListener", "RUBBER_STAMP_PANEL", "Divider"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,6zOAA8zO,KAGv1O0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,02CCDvB,IAqBeC,EArBiB,WAC9B,IAAMC,EAAWC,cACVC,EAAqB,EAAhBC,cAAgB,GAApB,GAMFC,EAAgCC,aAJT,WAC3BL,EAASM,IAAQC,YAAYC,IAAaC,wBAK5C,OACE,kBAACC,EAAA,EAAkB,CAACC,YAAaH,IAAaI,iCAC5C,kBAACC,EAAA,EAAM,CACLC,UAAW,0BACXH,YAAaH,IAAaO,2BAC1BC,MAAOd,EAAE,+BACTe,QAASb,M,mmCCdjB,IAAMc,EAAcC,IAAMC,MAAK,YAAsD,IAAnDC,EAAM,EAANA,OAAQC,EAAU,EAAVA,WAAYC,EAAK,EAALA,MAAON,EAAO,EAAPA,QAASO,EAAQ,EAARA,SAC7DtB,EAAqB,EAAhBC,cAAgB,GAApB,GACFsB,EAAY,eAAH,OAAkBH,EAAWI,MAC5C,OACE,4BACEC,SAAU,EACVC,IAAKL,EACLT,UAAWe,IAAW,eAAgB,CAAE,OAAUL,IAClDM,aAAY5B,EAAEuB,GACdR,QAAS,kBAAMA,EAAQK,EAAYC,IACnCQ,eAAcP,GAEd,yBAAKQ,IAAKX,EAAQY,IAAI,SAK5Bf,EAAYgB,YAAc,cAC1BhB,EAAYiB,UAAY,CACtBd,OAAQe,IAAUC,OAClBf,WAAYc,IAAUE,OACtBf,MAAOa,IAAUG,OACjBtB,QAASmB,IAAUI,KACnBhB,SAAUY,IAAUK,MAGtB,IAAMC,EAAuB,SAAH,GAAuE,IAAjEC,EAAc,EAAdA,eAAgBC,EAAkB,EAAlBA,mBAAoBC,EAAsB,EAAtBA,uBAC3D3C,EAAqB,EAAhBC,cAAgB,GAApB,GACF2C,EAAWC,cAUhB,IALGC,aACF,SAACC,GAAK,MAAK,CACTC,IAAUC,mBAAmBF,GAC7BC,IAAUE,0BAA0BH,OAEvC,GAPCI,EAAe,KACfC,EAAsB,KAQlBC,EAAeZ,EAAea,KAAI,WAAyBjC,GAAU,IAAhCF,EAAM,EAANA,OAAQC,EAAU,EAAVA,WAC3CmC,EAAgBb,IAAuBrB,EAEvCmC,EAA2BnC,KADT+B,GAA0B,IACYK,iBAAOf,GAKrE,OAH4BE,GAAaA,GAAYO,IAAoBO,IAAYC,YAClFf,GAAYO,IAAoBO,IAAYC,aAAeJ,GAAiBC,GAG7E,kBAACxC,EAAW,CACVU,IAAKL,EACLA,MAAOA,EACPF,OAAQA,EACRC,WAAYA,EACZL,QAAS4B,EACTrB,SAAUiC,IAEV,QAGAK,EAASC,uBAAY,WACzB,OACE7D,EAAE,+BAEH,CAACA,IAGJ,OACE,kBAAC8D,EAAA,EAAkB,CACjBF,OAAQA,EACRG,aAAc,EACdC,aANiB,qBAOjBC,qBAAsBjE,EAAE,8BAExB,yBAAKY,UAAU,qBAAqBsD,GATnB,sBAUdb,KAMTb,EAAqBR,YAAc,uBACpBf,UAAMC,KAAKsB,G,2hCChF1B,IAEM2B,EAAoBlD,IAAMC,MAAK,YAS7B,IAGFkD,EAVFjD,EAAM,EAANA,OACAC,EAAU,EAAVA,WACAC,EAAK,EAALA,MACAN,EAAO,EAAPA,QACAsD,EAAa,EAAbA,cACAC,EAAoB,EAApBA,qBACAhD,EAAQ,EAARA,SAEKtB,EAAqB,EAAhBC,cAAgB,GAApB,GACFsE,EAAkBnD,EAAWoD,cAAc,oBAEjD,IACEJ,EAAYK,KAAKC,MAAMH,GACvB,MAAOI,GACPP,EAAY,CAAEQ,MAAO,GAAF,OAAK5E,EAAE,wCAE5B,OACE,yBAAKY,UAAU,0BAA0Ba,SAAS,MAChD,4BACEC,IAAKL,EACLT,UAAWe,IAAW,eAAgB,CAAE,OAAUL,IAClDM,aAAA,UAAe5B,EAAE,oBAAmB,YAAIoE,EAAUQ,MAAK,YAAIR,EAAUS,OAAM,YAAIzD,EAAW0D,aAC1F/D,QAAS,kBAAMA,EAAQK,EAAYC,EAAQiD,IAC3CzC,eAAcP,GAEd,yBAAKQ,IAAKX,EAAQY,IAAI,MAExB,kBAACpB,EAAA,EAAM,CACLoE,eAAa,+BACbhE,QAAS,WACPsD,EAAchD,IAEhB2D,IAAI,mBACJzD,UAAS,UAAKvB,EAAE,iBAAgB,YAAIA,EAAE,oBAAmB,YAAIqB,EAAQ,SAM7E8C,EAAkBnC,YAAc,oBAChCmC,EAAkBlC,UAAY,CAC5Bd,OAAQe,IAAUC,OAClBf,WAAYc,IAAUE,OACtBf,MAAOa,IAAUG,OACjBtB,QAASmB,IAAUI,KACnB+B,cAAenC,IAAUI,KACzBgC,qBAAsBpC,IAAUG,OAChCf,SAAUY,IAAUK,MAItB,IAAM0C,EAAqB,SAAH,GAMhB,IAJJC,EAAY,EAAZA,aACAxC,EAAkB,EAAlBA,mBACAC,EAAsB,EAAtBA,uBACA2B,EAAoB,EAApBA,qBAEIa,EAAiBC,IAAKC,+BA9DZ,+BAgEVzC,EAAWC,cACX/C,EAAWC,cAUhB,IALG+C,aACF,SAACC,GAAK,MAAK,CACTC,IAAUC,mBAAmBF,GAC7BC,IAAUE,0BAA0BH,OAEvC,GAPCI,EAAe,KACfC,EAAsB,KAsBlBkC,EAAoBzB,uBAAY,SAACxC,GAAU,IACd,EAfRkE,EAcsB,E,goBAAA,CAC5BJ,GAAc,IAAjC,IAAK,EAAL,qBAAmC,KAAxBK,EAAI,QACPC,EAASD,EAAKE,kBACpBF,EAAKG,mBAAmB,CAACF,EAAOpE,KAChC,IAAMuE,GAlBiBL,EAkBelE,EAjBZ,IAiBmBmE,EAAKE,kBAAkB/G,OAhB7D,KAEY,IAAjB4G,EACKjB,EAELiB,EAAe,GAAK,EAEfA,EAAe,EAAIjB,EAErBiB,EAAejB,GAQpBxE,EAASM,IAAQyF,sBAAsB,OACvC/F,EAASM,IAAQ0F,0BAA0BF,KAC5C,iCACA,IAEGG,EAA6BnD,GAAYO,IAAoBO,IAAYC,WACzEqC,EAAwBpD,GAAYO,IAAoBO,IAAYC,WAEpEN,EAAe6B,EAAa5B,KAAI,WAAyBjC,GAAU,IAAhCF,EAAM,EAANA,OAAQC,EAAU,EAAVA,WACzC6E,EAAmB5E,EAAQiD,EAC3Bf,EAAgBb,IAAuBuD,EAEvCzC,EAA2BwC,IAA2BvC,iBAAOf,IAAuBwC,EAAavG,QAAUsH,IAAqB7C,GAA2BG,GACjK,OAAKX,GAAYmD,GAA8BvC,GAA6BwC,GAAyBzC,EAEjG,kBAACY,EAAiB,CAChBzC,IAAKL,EACLA,MAAOA,EACPF,OAAQA,EACRC,WAAYA,EACZL,QAAS4B,EACT2B,qBAAsBA,EACtBD,cAAeiB,EACfhE,SAAUiC,IAIT,QAGT,OAA4B,IAAxB2B,EAAavG,OACR,KAIP,yBAAKiC,UAAU,sBACZyC,IAKP4B,EAAmBjD,YAAc,qBACjCiD,EAAmBhD,UAAY,CAC7BiD,aAAchD,IAAUgE,MACxBxD,mBAAoBR,IAAUG,OAC9BM,uBAAwBT,IAAUI,KAClCgC,qBAAsBpC,IAAUG,QAEnBpB,UAAMC,KAAK+D,G,80BC5J1B,8lGAAAhH,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,gbAAAA,EAAA,gDAAAA,GAAA,oCAAAA,OAAA,8fAAAA,GAAA,gEAAAA,GAAA,0JAAAA,EAAA,6FAAAA,GAAA,mIAAAA,IAAA,IAAAA,IAAA,ygBAAAA,EAAA,iBAAAA,EAAA,EAAAA,IAAA,EAAAA,GAAA,EAAAA,GAAA,SAmBA,IAqFekI,EAnFU,WAAM,MACtBnG,EAAqB,EAAhBC,cAAgB,GAApB,GACFH,EAAWC,cACXoF,EAAiBC,IAAKC,+BALZ,+BAMVzC,EAAWC,cAEXJ,EAAiBK,YAAYE,IAAUoD,kBAAmBC,KAC1DnB,EAAepC,YAAYE,IAAU0C,gBAAiBW,KACtD3D,EAAqBI,YAAYE,IAAUsD,uBAC3CnD,EAAkBL,YAAYE,IAAUC,oBACxCsD,EAAezD,YAAYE,IAAUwD,gBAAiBH,KAEtDI,EAAQC,cACRC,EAAiBJ,EAAaI,eAE9BhE,EAAyBkB,sBAAW,eApC5C,EAoC4C,GApC5C,EAoC4C,UAAC,WAAOzC,EAAYC,GAAK,6EACjE+D,IAAKwB,YAlBS,+BAkBc,IACTzB,GAAc,yDACoB,OAD1CK,EAAI,QACPqB,EAAO7G,EAAE,eAAD,OAAgBoB,EAAiB,OAAI,SAC7CoE,EAAKsB,eAAe1F,EAAYyF,GAAK,OAC3CrB,EAAKuB,cAAc,+IAErBjH,EAASM,IAAQyF,sBAAsBxE,IAEnCuB,GAAYO,IAAoBO,IAAYC,YAC9C7D,EAASM,IAAQ4G,mBAAmBtD,IAAYC,aACjD,+DA/CL,+KAgDG,qDAZyC,GAYvC,IAwBH,OAtBAsD,qBAAU,WACRnH,EAASM,IAAQyF,sBAAsB,SACtC,IAEHoB,qBAAU,WACR,IAAMC,EAAsB,SAACvC,GAC3B,IAAMwC,EAAa/B,IAAKgC,cAClBC,EAAiBF,aAAU,EAAVA,EAAYG,KACnC,EAA+B3C,EAAE4C,OAAzBC,EAAO,EAAPA,QAASC,EAAS,EAATA,UACbD,IAAYE,IAAWC,cAAiBF,GAxChC,gCAyCNJ,GAAgCA,IAAmBO,KACrDC,YAAoBpB,EAAOmB,MAMjC,OADAxJ,OAAO0J,iBAAiBC,IAAOC,mBAAoBd,GAC5C,WACL9I,OAAO6J,oBAAoBF,IAAOC,mBAAoBd,MAEvD,IAGD,kBAAC1G,EAAA,EAAkB,CAACC,YAAaH,IAAa4H,mBAAoBtH,UAAWe,KAAU,GACrF,OAAS,EACT,kBAAoB,GAAI,IACvBwB,EAAkBP,GAAQ,IAC3B,mBAAoB+D,GAAc,KAElC,wBAAI/F,UAAU,6BACXZ,EAAE,4BAEL,kBAAC,EAAuB,MACxB,yBAAKY,UACHe,IAAW,EAAD,CACR,2BAA2B,GAC1BwB,EAAkBP,KAErB,kBAAC,EAAkB,CACjBF,mBAAoBA,EACpB4B,qBAAsB7B,EAAe9D,OACrCgE,uBAAwBA,EACxBuC,aAAcA,IAChB,kBAACiD,EAAA,EAAO,MACR,kBAAC,EAAoB,CACnBxF,uBAAwBA,EACxBF,eAAgBA,EAChBC,mBAAoBA,OChGfyD", "file": "chunks/chunk.72.js", "sourcesContent": ["var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./RubberStampPanel.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.RubberStampPanel{height:100%;display:flex;flex-direction:column}.RubberStampPanel .rubber-stamp-panel-header{font-size:16px;font-weight:700}.RubberStampPanel .CreateRubberStampButton{width:100%;padding:8px;color:var(--blue-5);border:1px solid var(--blue-5);border-radius:4px;margin:16px 0}.RubberStampPanel .CreateRubberStampButton:hover{color:var(--primary-button-hover);border-color:var(--primary-button-hover)}.RubberStampPanel .rubber-stamps-container{overflow:auto;padding:0 4px}.RubberStampPanel .rubber-stamps-container .collapsible-page-group-header button{font-size:14px;font-weight:700;margin:16px 0}.RubberStampPanel .rubber-stamps-container .rubber-stamps-list{display:flex;flex-direction:column;grid-gap:4px;gap:4px;padding-top:2px;padding-bottom:16px}.RubberStampPanel .rubber-stamps-container .rubber-stamps-list .rubber-stamp{border:none;background-color:transparent;height:48px;padding:0 8px;border:1px solid var(--border);border-radius:4px;display:flex;align-items:center;justify-content:center;cursor:pointer;background-color:var(--gray-0);flex-grow:1}:host(:not([data-tabbing=true])) .RubberStampPanel .rubber-stamps-container .rubber-stamps-list .rubber-stamp,html:not([data-tabbing=true]) .RubberStampPanel .rubber-stamps-container .rubber-stamps-list .rubber-stamp{outline:none}.RubberStampPanel .rubber-stamps-container .rubber-stamps-list .rubber-stamp img{max-width:100%;max-height:100%}.RubberStampPanel .rubber-stamps-container .rubber-stamps-list .rubber-stamp:hover{border:1px solid var(--blue-6)}.RubberStampPanel .rubber-stamps-container .rubber-stamps-list .rubber-stamp.active{border:1px solid var(--blue-5);background:var(--gray-2)}.RubberStampPanel .custom-rubber-stamp-row{display:flex;align-items:center;grid-gap:8px;gap:8px}.RubberStampPanel .custom-rubber-stamp-row .icon-button{padding:0;border:none;background-color:transparent;display:flex;align-items:center;justify-content:center;width:34px;height:34px;border-radius:4px;cursor:pointer}:host(:not([data-tabbing=true])) .RubberStampPanel .custom-rubber-stamp-row .icon-button,html:not([data-tabbing=true]) .RubberStampPanel .custom-rubber-stamp-row .icon-button{outline:none}.RubberStampPanel .custom-rubber-stamp-row .icon-button:hover{border:1px solid var(--blue-6);background:var(--gray-2)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RubberStampPanel{width:100%}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RubberStampPanel .rubber-stamp-panel-header{margin:0 16px 16px}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RubberStampPanel [data-element=createRubberStampButtonWrap]{padding:0 16px}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RubberStampPanel [data-element=createRubberStampButtonWrap] .CreateRubberStampButton{margin-top:0}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RubberStampPanel.small-size .collapsible-page-group-header,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RubberStampPanel.small-size .CreateRubberStampButton,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RubberStampPanel.small-size .Divider,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RubberStampPanel.small-size .rubber-stamps-list-header{display:none}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RubberStampPanel .rubber-stamps-container{position:relative;padding:0 14px 16px;overflow-y:scroll}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RubberStampPanel .rubber-stamps-container.small-size{overflow:hidden}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RubberStampPanel .rubber-stamps-container .rubber-stamps-list .icon-button:hover,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RubberStampPanel .rubber-stamps-container .rubber-stamps-list .rubber-stamp:hover{background-color:var(--gray-0)}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RubberStampPanel .rubber-stamps-container .rubber-stamps-list .icon-button.active,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RubberStampPanel .rubber-stamps-container .rubber-stamps-list .rubber-stamp.active{background:var(--tools-overlay-button-active)}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RubberStampPanel .rubber-stamps-container .Divider{margin:0}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RubberStampPanel{width:100%}.App.is-web-component:not(.is-in-desktop-only-mode) .RubberStampPanel .rubber-stamp-panel-header{margin:0 16px 16px}.App.is-web-component:not(.is-in-desktop-only-mode) .RubberStampPanel [data-element=createRubberStampButtonWrap]{padding:0 16px}.App.is-web-component:not(.is-in-desktop-only-mode) .RubberStampPanel [data-element=createRubberStampButtonWrap] .CreateRubberStampButton{margin-top:0}.App.is-web-component:not(.is-in-desktop-only-mode) .RubberStampPanel.small-size .collapsible-page-group-header,.App.is-web-component:not(.is-in-desktop-only-mode) .RubberStampPanel.small-size .CreateRubberStampButton,.App.is-web-component:not(.is-in-desktop-only-mode) .RubberStampPanel.small-size .Divider,.App.is-web-component:not(.is-in-desktop-only-mode) .RubberStampPanel.small-size .rubber-stamps-list-header{display:none}.App.is-web-component:not(.is-in-desktop-only-mode) .RubberStampPanel .rubber-stamps-container{position:relative;padding:0 14px 16px;overflow-y:scroll}.App.is-web-component:not(.is-in-desktop-only-mode) .RubberStampPanel .rubber-stamps-container.small-size{overflow:hidden}.App.is-web-component:not(.is-in-desktop-only-mode) .RubberStampPanel .rubber-stamps-container .rubber-stamps-list .icon-button:hover,.App.is-web-component:not(.is-in-desktop-only-mode) .RubberStampPanel .rubber-stamps-container .rubber-stamps-list .rubber-stamp:hover{background-color:var(--gray-0)}.App.is-web-component:not(.is-in-desktop-only-mode) .RubberStampPanel .rubber-stamps-container .rubber-stamps-list .icon-button.active,.App.is-web-component:not(.is-in-desktop-only-mode) .RubberStampPanel .rubber-stamps-container .rubber-stamps-list .rubber-stamp.active{background:var(--tools-overlay-button-active)}.App.is-web-component:not(.is-in-desktop-only-mode) .RubberStampPanel .rubber-stamps-container .Divider{margin:0}}.RubberStampPanel.modular-ui-panel .rubber-stamps-list .rubber-stamp{border-color:var(--lighter-border)}.RubberStampPanel.modular-ui-panel .rubber-stamps-list .rubber-stamp.focus-visible,.RubberStampPanel.modular-ui-panel .rubber-stamps-list .rubber-stamp:focus-visible{outline:var(--focus-visible-outline)!important}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React from 'react';\nimport Button from '../Button';\nimport { useDispatch } from 'react-redux';\nimport actions from 'actions';\nimport { useTranslation } from 'react-i18next';\nimport DataElementWrapper from '../DataElementWrapper';\nimport DataElements from 'constants/dataElement';\nimport useFocusHandler from 'hooks/useFocusHandler';\n\nconst CreateRubberStampButton = () => {\n  const dispatch = useDispatch();\n  const [t] = useTranslation();\n\n  const openRubberStampModal = () => {\n    dispatch(actions.openElement(DataElements.CUSTOM_STAMP_MODAL));\n  };\n\n  const openRubberStampModalWithFocus = useFocusHandler(openRubberStampModal);\n\n  return (\n    <DataElementWrapper dataElement={DataElements.CREATE_RUBBER_STAMP_BUTTON_WRAP}>\n      <Button\n        className={'CreateRubberStampButton'}\n        dataElement={DataElements.CREATE_RUBBER_STAMP_BUTTON}\n        label={t('component.createStampButton')}\n        onClick={openRubberStampModalWithFocus} />\n    </DataElementWrapper>\n  );\n};\n\nexport default CreateRubberStampButton;", "import React, { useCallback } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport classNames from 'classnames';\nimport selectors from 'selectors';\nimport CollapsibleSection from 'components/CollapsibleSection';\nimport { useSelector } from 'react-redux';\nimport { isMobileSize } from 'helpers/getDeviceSize';\nimport { PANEL_SIZES } from 'constants/panel';\nimport { isNull } from 'lodash';\nimport PropTypes from 'prop-types';\n\nconst RubberStamp = React.memo(({ imgSrc, annotation, index, onClick, isActive }) => {\n  const [t] = useTranslation();\n  const ariaLabel = `rubberStamp.${annotation.Icon}`;\n  return (\n    <button\n      tabIndex={0}\n      key={index}\n      className={classNames('rubber-stamp', { 'active': isActive })}\n      aria-label={t(ariaLabel)}\n      onClick={() => onClick(annotation, index)}\n      aria-current={isActive}\n    >\n      <img src={imgSrc} alt=\"\" />\n    </button>\n  );\n});\n\nRubberStamp.displayName = 'RubberStamp';\nRubberStamp.propTypes = {\n  imgSrc: PropTypes.string,\n  annotation: PropTypes.object,\n  index: PropTypes.number,\n  onClick: PropTypes.func,\n  isActive: PropTypes.bool,\n};\n\nconst StandardRubberStamps = ({ standardStamps, selectedStampIndex, setSelectedRubberStamp }) => {\n  const [t] = useTranslation();\n  const isMobile = isMobileSize();\n\n  const [\n    mobilePanelSize,\n    lastSelectedStampIndex,\n  ] = useSelector(\n    (state) => [\n      selectors.getMobilePanelSize(state),\n      selectors.getLastSelectedStampIndex(state),\n    ]\n  );\n\n  const rubberStamps = standardStamps.map(({ imgSrc, annotation }, index) => {\n    const isStampActive = selectedStampIndex === index;\n    const lastStampToShow = lastSelectedStampIndex || 0;\n    const shouldShowOnlyFirstStamp = index === lastStampToShow && isNull(selectedStampIndex);\n\n    const shouldRenderStamp = (!isMobile || (isMobile && mobilePanelSize !== PANEL_SIZES.SMALL_SIZE)) ||\n      (isMobile && mobilePanelSize === PANEL_SIZES.SMALL_SIZE && (isStampActive || shouldShowOnlyFirstStamp));\n\n    return shouldRenderStamp ? (\n      <RubberStamp\n        key={index}\n        index={index}\n        imgSrc={imgSrc}\n        annotation={annotation}\n        onClick={setSelectedRubberStamp}\n        isActive={isStampActive}\n      />\n    ) : null;\n  });\n\n  const header = useCallback(() => {\n    return (\n      t('rubberStampPanel.standard')\n    );\n  }, [t]);\n  const ariaControls = 'rubber-stamps-list';\n\n  return (\n    <CollapsibleSection\n      header={header}\n      headingLevel={2}\n      ariaControls={ariaControls}\n      expansionDescription={t('rubberStampPanel.standard')}\n    >\n      <div className='rubber-stamps-list' id={ariaControls}>\n        {rubberStamps}\n      </div>\n    </CollapsibleSection>\n  );\n};\n\nStandardRubberStamps.displayName = 'StandardRubberStamps';\nexport default React.memo(StandardRubberStamps);", "import React, { useCallback } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport core from 'core';\nimport classNames from 'classnames';\nimport selectors from 'selectors';\nimport actions from 'actions';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { isMobileSize } from 'src/helpers/getDeviceSize';\nimport { PANEL_SIZES } from 'src/constants/panel';\nimport { isNull } from 'lodash';\nimport PropTypes from 'prop-types';\nimport Button from 'components/Button';\n\nconst TOOL_NAME = 'AnnotationCreateRubberStamp';\n\nconst CustomRubberStamp = React.memo((\n  {\n    imgSrc,\n    annotation,\n    index,\n    onClick,\n    deleteHandler,\n    standardStampsOffset,\n    isActive,\n  }) => {\n  const [t] = useTranslation();\n  const customStampData = annotation.getCustomData('trn-custom-stamp');\n  let stampInfo;\n  try {\n    stampInfo = JSON.parse(customStampData);\n  } catch (e) {\n    stampInfo = { title: `${t('annotation.defaultCustomStampTitle')}` };\n  }\n  return (\n    <div className='custom-rubber-stamp-row' tabIndex='-1'>\n      <button\n        key={index}\n        className={classNames('rubber-stamp', { 'active': isActive })}\n        aria-label={`${t('annotation.stamp')} ${stampInfo.title} ${stampInfo.author} ${annotation.DateCreated}`}\n        onClick={() => onClick(annotation, index + standardStampsOffset)}\n        aria-current={isActive}\n      >\n        <img src={imgSrc} alt=\"\" />\n      </button>\n      <Button\n        data-element=\"defaultSignatureDeleteButton\"\n        onClick={() => {\n          deleteHandler(index);\n        }}\n        img=\"icon-delete-line\"\n        ariaLabel={`${t('action.delete')} ${t('annotation.stamp')} ${index + 1}`}\n      />\n    </div>\n  );\n});\n\nCustomRubberStamp.displayName = 'CustomRubberStamp';\nCustomRubberStamp.propTypes = {\n  imgSrc: PropTypes.string,\n  annotation: PropTypes.object,\n  index: PropTypes.number,\n  onClick: PropTypes.func,\n  deleteHandler: PropTypes.func,\n  standardStampsOffset: PropTypes.number,\n  isActive: PropTypes.bool,\n};\n\n\nconst CustomRubberStamps = (\n  {\n    customStamps,\n    selectedStampIndex,\n    setSelectedRubberStamp,\n    standardStampsOffset,\n  }) => {\n  const stampToolArray = core.getToolsFromAllDocumentViewers(TOOL_NAME);\n\n  const isMobile = isMobileSize();\n  const dispatch = useDispatch();\n\n  const [\n    mobilePanelSize,\n    lastSelectedStampIndex,\n  ] = useSelector(\n    (state) => [\n      selectors.getMobilePanelSize(state),\n      selectors.getLastSelectedStampIndex(state),\n    ],\n  );\n\n  const getNextStampIndex = (deletedIndex, newSizeCustomStamps) => {\n    if (newSizeCustomStamps === 0) {\n      return null;\n    }\n    if (deletedIndex === 0) {\n      return standardStampsOffset;\n    }\n    if (deletedIndex - 1 >= 0) {\n      // If there is a stamp before the deleted stamp, select the stamp before the deleted stamp\n      return deletedIndex - 1 + standardStampsOffset;\n    }\n    return deletedIndex + standardStampsOffset;\n  };\n\n  const deleteCustomStamp = useCallback((index) => {\n    for (const tool of stampToolArray) {\n      const stamps = tool.getCustomStamps();\n      tool.deleteCustomStamps([stamps[index]]);\n      const indexToShow = getNextStampIndex(index, tool.getCustomStamps().length);\n      dispatch(actions.setSelectedStampIndex(null));\n      dispatch(actions.setLastSelectedStampIndex(indexToShow));\n    }\n  }, []);\n\n  const isMobileModeWithLargerSize = isMobile && mobilePanelSize !== PANEL_SIZES.SMALL_SIZE;\n  const isMobileModeSmallSize = isMobile && mobilePanelSize === PANEL_SIZES.SMALL_SIZE;\n\n  const rubberStamps = customStamps.map(({ imgSrc, annotation }, index) => {\n    const customStampIndex = index + standardStampsOffset;\n    const isStampActive = selectedStampIndex === customStampIndex;\n\n    const shouldShowOnlyFirstStamp = isMobileModeSmallSize && ((isNull(selectedStampIndex) && customStamps.length && customStampIndex === lastSelectedStampIndex) || isStampActive);\n    if (!isMobile || isMobileModeWithLargerSize || shouldShowOnlyFirstStamp || (isMobileModeSmallSize && isStampActive)) {\n      return (\n        <CustomRubberStamp\n          key={index}\n          index={index}\n          imgSrc={imgSrc}\n          annotation={annotation}\n          onClick={setSelectedRubberStamp}\n          standardStampsOffset={standardStampsOffset}\n          deleteHandler={deleteCustomStamp}\n          isActive={isStampActive}\n        />\n      );\n    }\n    return null;\n  });\n\n  if (customStamps.length === 0) {\n    return null;\n  }\n\n  return (\n    <div className='rubber-stamps-list'>\n      {rubberStamps}\n    </div>\n  );\n};\n\nCustomRubberStamps.displayName = 'CustomRubberStamps';\nCustomRubberStamps.propTypes = {\n  customStamps: PropTypes.array,\n  selectedStampIndex: PropTypes.number,\n  setSelectedRubberStamp: PropTypes.func,\n  standardStampsOffset: PropTypes.number,\n};\nexport default React.memo(CustomRubberStamps);", "import React, { useCallback, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { useSelector, useDispatch, shallowEqual, useStore } from 'react-redux';\nimport selectors from 'selectors';\nimport actions from 'actions';\nimport core from 'core';\nimport classNames from 'classnames';\nimport DataElements from 'constants/dataElement';\nimport { PANEL_SIZES, panelNames } from 'constants/panel';\nimport defaultTool from 'constants/defaultTool';\nimport Events from 'constants/events';\nimport DataElementWrapper from 'components/DataElementWrapper';\nimport CreateRubberStampButton from './CreateRubberStampButton';\nimport Divider from '../ModularComponents/Divider';\nimport './RubberStampPanel.scss';\nimport StandardRubberStamps from './StandardRubberStamps';\nimport CustomRubberStamps from './CustomRubberStamps';\nimport { isMobileSize } from 'helpers/getDeviceSize';\nimport setToolModeAndGroup from 'helpers/setToolModeAndGroup';\n\nconst TOOL_NAME = 'AnnotationCreateRubberStamp';\n\nconst RubberStampPanel = () => {\n  const [t] = useTranslation();\n  const dispatch = useDispatch();\n  const stampToolArray = core.getToolsFromAllDocumentViewers(TOOL_NAME);\n  const isMobile = isMobileSize();\n\n  const standardStamps = useSelector(selectors.getStandardStamps, shallowEqual);\n  const customStamps = useSelector(selectors.getCustomStamps, shallowEqual);\n  const selectedStampIndex = useSelector(selectors.getSelectedStampIndex);\n  const mobilePanelSize = useSelector(selectors.getMobilePanelSize);\n  const featureFlags = useSelector(selectors.getFeatureFlags, shallowEqual);\n\n  const store = useStore();\n  const customizableUI = featureFlags.customizableUI;\n\n  const setSelectedRubberStamp = useCallback(async (annotation, index) => {\n    core.setToolMode(TOOL_NAME);\n    for (const tool of stampToolArray) {\n      const text = t(`rubberStamp.${annotation['Icon']}`);\n      await tool.setRubberStamp(annotation, text);\n      tool.showPreview();\n    }\n    dispatch(actions.setSelectedStampIndex(index));\n\n    if (isMobile && mobilePanelSize !== PANEL_SIZES.SMALL_SIZE) {\n      dispatch(actions.setMobilePanelSize(PANEL_SIZES.SMALL_SIZE));\n    }\n  }, []);\n\n  useEffect(() => {\n    dispatch(actions.setSelectedStampIndex(null));\n  }, []);\n\n  useEffect(() => {\n    const onVisibilityChanged = (e) => {\n      const activeTool = core.getToolMode();\n      const activeToolName = activeTool?.name;\n      const { element, isVisible } = e.detail;\n      if (element === panelNames.RUBBER_STAMP && !isVisible) {\n        if (activeToolName === TOOL_NAME || activeToolName === defaultTool) {\n          setToolModeAndGroup(store, defaultTool);\n        }\n      }\n    };\n\n    window.addEventListener(Events.VISIBILITY_CHANGED, onVisibilityChanged);\n    return () => {\n      window.removeEventListener(Events.VISIBILITY_CHANGED, onVisibilityChanged);\n    };\n  }, []);\n\n  return (\n    <DataElementWrapper dataElement={DataElements.RUBBER_STAMP_PANEL} className={classNames({\n      'Panel': true,\n      'RubberStampPanel': true,\n      [mobilePanelSize]: isMobile,\n      'modular-ui-panel': customizableUI,\n    })}>\n      <h1 className='rubber-stamp-panel-header'>\n        {t('rubberStampPanel.header')}\n      </h1>\n      <CreateRubberStampButton />\n      <div className={\n        classNames({\n          'rubber-stamps-container': true,\n          [mobilePanelSize]: isMobile,\n        })}>\n        <CustomRubberStamps\n          selectedStampIndex={selectedStampIndex}\n          standardStampsOffset={standardStamps.length}\n          setSelectedRubberStamp={setSelectedRubberStamp}\n          customStamps={customStamps} />\n        <Divider />\n        <StandardRubberStamps\n          setSelectedRubberStamp={setSelectedRubberStamp}\n          standardStamps={standardStamps}\n          selectedStampIndex={selectedStampIndex} />\n      </div>\n\n    </DataElementWrapper>\n  );\n};\n\nexport default RubberStampPanel;", "import RubberStampPanel from './RubberStampPanel';\n\nexport default RubberStampPanel;"], "sourceRoot": ""}