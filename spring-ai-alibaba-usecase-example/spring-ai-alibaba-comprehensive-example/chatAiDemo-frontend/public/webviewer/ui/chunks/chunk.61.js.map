{"version": 3, "sources": ["webpack:///./src/ui/src/components/FilterAnnotModal/FilterAnnotModal.scss?5af6", "webpack:///./src/ui/src/components/FilterAnnotModal/FilterAnnotModal.scss", "webpack:///./src/ui/src/components/FilterAnnotModal/FilterAnnotModal.js", "webpack:///./src/ui/src/components/FilterAnnotModal/index.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "TABS_ID", "FilterAnnotModal", "useSelector", "state", "selectors", "isElementDisabled", "DataElements", "FILTER_MODAL", "isElementOpen", "getColorMap", "getSelectedTab", "getAnnotationFilters", "getIsMeasurementAnnotationFilterEnabled", "getCustomNoteFilter", "isDisabled", "isOpen", "colorMap", "selectedTab", "annotationFilters", "isMeasurementAnnotationFilterEnabled", "customNoteFilter", "t", "useTranslation", "dispatch", "useDispatch", "useState", "authors", "setAuthors", "annotTypes", "setAnnotTypes", "colors", "setColorTypes", "statuses", "setStatusTypes", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "typesFilter", "setTypesFilter", "colorFilter", "setColorFilter", "checkRepliesForAuthorFilter", "setCheckRepliesForAuthorFilter", "isDocumentFilterActive", "setIsDocumentFilterActive", "statusFilter", "setStatus<PERSON>ilter", "filterCount", "setFilterCount", "ifShowAnnotationStatus", "setIfShowAnnotationStatus", "getIconColor", "annot", "key", "mapAnnotationToKey", "iconColor", "similarColorExist", "currColors", "newColor", "colorObject", "map", "c", "Object", "assign", "R", "parseInt", "G", "B", "filter", "Math", "abs", "filterApply", "newFilter", "documentViewerKey", "type", "author", "color", "status", "isMeasurementAnnotation", "IT", "getCustomData", "<PERSON><PERSON>ey", "includes", "getAnnotationClass", "core", "getDisplayAuthor", "allReplies", "getReplies", "reply", "COMMON_COLORS", "getStatus", "actions", "setInternalNoteFilter", "setAnnotationFilters", "includeReplies", "typeFilter", "redrawList", "getDocumentViewers", "documentViewer", "index", "getAnnotationManager", "getAnnotationsList", "shouldHide", "NoView", "drawAnnotationsFromList", "fireEvent", "Events", "ANNOTATION_FILTER_CHANGED", "types", "closeModal", "filterClear", "closeElement", "setToolMode", "defaultTool", "useEffect", "clearAllFilters", "addEventListener", "removeEventListener", "annotLists", "filteredList", "filteredList2", "annots", "concat", "Hidden", "authorsToBeAdded", "Set", "annotTypesToBeAdded", "annotColorsToBeAdded", "annotStatusesToBeAdded", "displayAuthor", "add", "getFormFieldCreationManager", "isInFormFieldCreationMode", "Core", "Annotations", "WidgetAnnotation", "StickyAnnotation", "isReply", "Link", "rgbaToHex", "A", "ANNOTATION_STATUS_FILTER_PANEL_BUTTON", "setSelectedTab", "ANNOTATION_USER_FILTER_PANEL_BUTTON", "modalClass", "classNames", "Modal", "open", "closed", "className", "data-element", "ModalWrapper", "title", "<PERSON><PERSON><PERSON><PERSON>", "onCloseClick", "swipeToClose", "onMouseDown", "e", "stopPropagation", "Tabs", "id", "dataElement", "ANNOTATION_COLOR_FILTER_PANEL_BUTTON", "ANNOTATION_TYPE_FILTER_PANEL_BUTTON", "val", "Choice", "aria-label", "label", "<PERSON><PERSON><PERSON>", "checked", "onChange", "indexOf", "target", "getAttribute", "toUpperCase", "hideOnClick", "style", "background", "getHexToRgbaString", "sort", "type1", "type2", "toLocaleLowerCase", "role", "aria-<PERSON>by", "<PERSON><PERSON>", "onClick", "disabled"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,+2VAAg3V,KAGz4V0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,43ECavB,IAAMC,EAAU,mBAueDC,EAreU,WACvB,IASE,IAR0DC,aAAY,SAACC,GAAK,MAAK,CACjFC,IAAUC,kBAAkBF,EAAOG,IAAaC,cAChDH,IAAUI,cAAcL,EAAOG,IAAaC,cAC5CH,IAAUK,YAAYN,GACtBC,IAAUM,eAAeP,EAAOH,GAChCI,IAAUO,qBAAqBR,GAC/BC,IAAUQ,wCAAwCT,GAClDC,IAAUS,oBAAoBV,OAC9B,GATKW,EAAU,KAAEC,EAAM,KAAEC,EAAQ,KAAEC,EAAW,KAAEC,EAAiB,KACjEC,EAAoC,KAAEC,EAAgB,KAUjDC,EAAqB,EAAhBC,cAAgB,GAApB,GACFC,EAAWC,cAEyB,IAAZC,mBAAS,IAAG,GAAnCC,EAAO,KAAEC,EAAU,KACsB,IAAZF,mBAAS,IAAG,GAAzCG,EAAU,KAAEC,EAAa,KACY,IAAZJ,mBAAS,IAAG,GAArCK,EAAM,KAAEC,EAAa,KACmB,IAAZN,mBAAS,IAAG,GAAxCO,EAAQ,KAAEC,EAAc,KACqB,IAAZR,mBAAS,IAAG,GAA7CS,EAAY,KAAEC,EAAe,KACc,IAAZV,mBAAS,IAAG,GAA3CW,EAAW,KAAEC,EAAc,KACgB,IAAZZ,mBAAS,IAAG,GAA3Ca,GAAW,KAAEC,GAAc,KACkD,KAAdd,oBAAS,GAAK,GAA7Ee,GAA2B,MAAEC,GAA8B,MACS,KAAfhB,oBAAS,GAAM,GAApEiB,GAAsB,MAAEC,GAAyB,MACJ,KAAZlB,mBAAS,IAAG,GAA7CmB,GAAY,MAAEC,GAAe,MACa,KAAXpB,mBAAS,GAAE,GAA1CqB,GAAW,MAAEC,GAAc,MACyC,KAAftB,oBAAS,GAAM,GAApEuB,GAAsB,MAAEC,GAAyB,MAElDC,GAAe,SAACC,GAAU,MACxBC,EAAMC,YAAmBF,GAG/B,OAAOA,EAFgC,QAAhB,EAAGnC,EAASoC,UAAI,aAAb,EAAeE,YAKrCC,GAAoB,SAACC,EAAYC,GACrC,IAAMC,EAAcF,EAAWG,KAAI,SAACC,GAAC,OAAKC,OAAOC,OAAO,CACtDC,EAAGC,SAAS,GAAD,OAAIJ,EAAE,IAAE,OAAGA,EAAE,IAAM,IAC9BK,EAAGD,SAAS,GAAD,OAAIJ,EAAE,IAAE,OAAGA,EAAE,IAAM,IAC9BM,EAAGF,SAAS,GAAD,OAAIJ,EAAE,IAAE,OAAGA,EAAE,IAAM,SAShC,QALsBF,EACnBS,QAAO,SAACP,GAAC,OAAKQ,KAAKC,IAAIZ,EAASM,EAAIH,EAAEG,GAFvB,IAGXK,KAAKC,IAAIZ,EAASQ,EAAIL,EAAEK,GAHb,IAIXG,KAAKC,IAAIZ,EAASS,EAAIN,EAAEM,GAJb,MAMKpF,QAGnBwF,GAAc,WAClB,IAAMC,EAAY,SAACpB,GAAiC,IAA1BqB,EAAoB,UAAH,6CAAG,EACxCC,GAAO,EACPC,GAAS,EACTC,GAAQ,EACRC,GAAS,EACb,GAAIxC,EAAYtD,OAAS,EAAG,CAC1B,IAAM+F,EAA0B1B,EAAM2B,IAAM3B,EAAM4B,cAAc,gBAChE,GAAI5D,GAAwC0D,EAAyB,CACnE,IAAMG,EAAiB3B,YAAmBF,GAC1CsB,EAAOrC,EAAY6C,SAASD,QAE5BP,EAAOrC,EAAY6C,SAASC,YAAmB/B,IAGnD,GAAIjB,EAAapD,OAAS,KACxB4F,EAASxC,EAAa+C,SAASE,IAAKC,iBAAiBjC,EAAc,OAAGqB,MACvDhC,GAA6B,CAC1C,IAC8B,EADxB6C,EAAalC,EAAMmC,aAAa,IAClBD,GAAU,IAA9B,IAAK,EAAL,qBAAgC,KAArBE,EAAK,QAGd,GAAIrD,EAAa+C,SAASE,IAAKC,iBAAiBG,EAAc,OAAGf,IAAqB,CACpFE,GAAS,EACT,QAEH,+BAGL,GAAIpC,GAAYxD,OAAS,EAAG,CAC1B,IAAMwE,EAAYJ,GAAaC,GAE7BwB,EADErB,EACMC,GAAkBjB,GAAagB,GAG/BhB,GAAY2C,SAASO,IAAqB,OAUtD,OAPI5C,GAAa9D,OAAS,IAEtB8F,EADEzB,EAAMsC,YACC7C,GAAaqC,SAAS9B,EAAMsC,aAE5B7C,GAAaqC,SAAS,SAG5BR,GAAQC,GAAUC,GAASC,GAEpCrD,EAASmE,IAAQC,sBAAsBpB,IACvChD,EAASmE,IAAQE,qBAAqB,CACpClD,0BACAmD,eAAgBrD,GAChBN,eACAI,eACAwD,WAAY1D,EACZQ,mBAEF,IAAMmD,EAAa,GACfrD,GACFyC,IAAKa,qBAAqB5G,SAAQ,SAAC6G,EAAgBC,GAAK,OAAKD,EAAeE,uBACzEC,qBAAqBhH,SAAQ,SAAC+D,GAC7B,IAAMkD,GAAc9B,EAAUpB,EAAO+C,EAAQ,GACzCG,IAAelD,EAAMmD,SACvBnD,EAAMmD,OAASD,EACfN,EAAWzG,KAAK6D,UAItBgC,IAAKa,qBAAqB5G,SAAQ,SAAC6G,GAAc,OAAKA,EAAeE,uBAClEC,qBAAqBhH,SAAQ,SAAC+D,IACR,IAAjBA,EAAMmD,SACRnD,EAAMmD,QAAS,EACfP,EAAWzG,KAAK6D,UAIxBgC,IAAKa,qBAAqB5G,SAAQ,SAAC6G,GAAc,OAAKA,EAAeE,uBAAuBI,wBAAwBR,MACpHS,YACEC,IAAOC,0BACP,CACEC,MAAOvE,EACPV,QAASQ,EACTJ,OAAQQ,GACRN,SAAUY,GACVJ,iCAGJoE,MAGIC,GAAc,WAClBpE,IAA+B,GAC/BE,IAA0B,GAC1BR,EAAgB,IAChBE,EAAe,IACfE,GAAe,IACfM,GAAgB,IAEhB,IAAMkD,EAAa,GACnBZ,IAAKa,qBAAqB5G,SAAQ,SAAC6G,GAAc,OAAKA,EAAeE,uBAAuBC,qBACzFhH,SAAQ,SAAC+D,IACa,IAAjBA,EAAMmD,SACRnD,EAAMmD,QAAS,EACfP,EAAWzG,KAAK6D,UAGtBgC,IAAKa,qBAAqB5G,SAAQ,SAAC6G,GAAc,OAAKA,EAAeE,uBAAuBI,wBAAwBR,OAGhHa,GAAa,WACjBrF,EAASmE,IAAQoB,aAAaxG,IAAaC,eAC3C4E,IAAK4B,YAAYC,MAGnBC,qBAAU,WACR,IAAMC,EAAkB,WACtBL,KACAvC,MAGF,OADAa,IAAKgC,iBAAiB,mBAAoBD,GACnC,WACL/B,IAAKiC,oBAAoB,mBAAoBF,MAE9C,IAEHD,qBAAU,WAAM,MACVI,EAAalC,IAAKa,qBAAqBrC,KAAI,SAACsC,GAAc,OAAKA,EAAeE,uBAAuBC,wBAEzG,GAAIhF,EAAkB,CACpB,IAAMkG,EAAgBD,EAAW,GAAMA,EAAW,GAAGlD,OAAO/C,GAAmB,GAC/EiG,EAAW,GAAKC,EAChB,IAAMC,EAAiBF,EAAW,GAAMA,EAAW,GAAGlD,OAAO/C,GAAoB,GACjFiG,EAAW,GAAKE,EAGlB,IAAMC,GAAS,MAAGC,OAAM,UAAIJ,IAAYlD,QAAO,SAAChB,GAAK,OAAMA,EAAMuE,UAG3DC,EAAmB,IAAIC,IACvBC,EAAsB,IAAID,IAC1BE,EAAuB,IAAIF,IAC3BG,EAAyB,IAAIH,IA4CnC,OA3CAJ,EAAOpI,SAAQ,SAAC+D,GACd,IAAM6E,EAAgB7C,IAAKC,iBAAiBjC,EAAc,QAY1D,GAXI6E,GAAmC,KAAlBA,GACnBL,EAAiBM,IAAID,MAGK7C,IAAKgB,uBAAuB+B,8BAA8BC,6BAE3DhF,aAAiB5E,OAAO6J,KAAKC,YAAYC,kBACjEnF,aAAiB5E,OAAO6J,KAAKC,YAAYE,kBAAoBpF,EAAMqF,WACpErF,aAAiB5E,OAAO6J,KAAKC,YAAYI,MAG3C,CAKA,IAAM5D,EAA0B1B,EAAM2B,IAAM3B,EAAM4B,cAAc,gBAChE,GAAI5D,GAAwC0D,EAAyB,CACnE,IAAMG,EAAiB3B,YAAmBF,GAC1C0E,EAAoBI,IAAIjD,QAExB6C,EAAoBI,IAAI/C,YAAmB/B,IAE7C,IAAMG,EAAYJ,GAAaC,GAC3BG,IAAcC,GAAkB,EAAIuE,GAAuBxE,IAC7DwE,EAAqBG,IAAIS,YAAUpF,EAAUS,EAAGT,EAAUW,EAAGX,EAAUY,EAAGZ,EAAUqF,IAGlFxF,EAAMsC,YACRsC,EAAuBE,IAAI9E,EAAMsC,aAEjCsC,EAAuBE,IAAI,YAI/BtG,EAAW,EAAIgG,IACf9F,EAAc,EAAIgG,IAClB9F,EAAc,EAAI+F,IAClB7F,EAAe,EAAI8F,IAEnB5C,IAAKgC,iBAAiB,mBAAoBP,IACnC,WACLzB,IAAKiC,oBAAoB,mBAAoBR,OAE9C,CAAC7F,EAAQI,IAEZ8F,qBAAU,WACJhG,IAAgBX,IAAasI,uCAA0C5F,IACzEzB,EAASmE,IAAQmD,eAAe7I,EAASM,IAAawI,wCAEvD,CAAC/H,EAAQE,EAAa+B,KAEzBiE,qBAAU,WACRlE,IACGP,GAA8B,EAAI,IAClCE,GAAyB,EAAI,GAC9BR,EAAapD,OACbwD,GAAYxD,OACZsD,EAAYtD,OACZ8D,GAAa9D,UAEd,CAAC0D,GAA6BE,GAAwBR,EAAcI,GAAaF,EAAaQ,KAEjGqE,qBAAU,WACRhE,GAA2BjB,EAASlD,OAAS,GAA2B,IAApBkD,EAASlD,QAAgC,SAAhBkD,EAAS,MACrF,CAACA,IAEJiF,qBAAU,WACJlG,IACF4B,GAA0BzB,EAAkBwB,wBAC5CD,GAA+BvB,EAAkB2E,gBACjD1D,EAAgBjB,EAAkBgB,cAClCK,GAAerB,EAAkBoB,aACjCD,EAAenB,EAAkB4E,YACjCjD,GAAgB3B,EAAkB0B,iBAEnC,CAAC7B,IAEJ,IA4GMgI,GAAaC,IAAW,CAC5BC,OAAO,EACPhJ,kBAAkB,EAClBiJ,KAAMnI,EACNoI,QAASpI,IAGX,OAAOD,EAAa,KAClB,yBAAKsI,UAAWL,GAAYM,eAAc/I,IAAaC,cACrD,kBAAC+I,EAAA,EAAY,CACXvI,OAAQA,EACRwI,MAAK,UAAKlI,EAAE,mCAAkC,aAAKyB,GAAW,KAC9D0G,aAAc5C,GACd6C,aAAc7C,GACd8C,cAAY,GAEZ,yBAAKN,UAAU,YAAYO,YAAa,SAACC,GAAC,OAAKA,EAAEC,oBAC9C1E,IAAKiB,qBAAqBtH,OAAS,EAClC,yBAAKsK,UAAU,gBACb,yBAAKA,UAAU,QACb,kBAACU,EAAA,EAAI,CAACC,GAAI/J,GACR,yBAAKoJ,UAAU,YACb,kBAAC,IAAG,CAACY,YAAa1J,IAAawI,qCAC7B,4BAAQM,UAAU,sBACf/H,EAAE,kCAGP,yBAAK+H,UAAU,wBACf,kBAAC,IAAG,CAACY,YAAa1J,IAAa2J,sCAC7B,4BAAQb,UAAU,sBACf/H,EAAE,mCAGP,yBAAK+H,UAAU,wBACf,kBAAC,IAAG,CAACY,YAAa1J,IAAa4J,qCAC7B,4BAAQd,UAAU,sBACf/H,EAAE,kCAGN2B,IACC,oCACE,yBAAKoG,UAAU,wBACf,kBAAC,IAAG,CAACY,YAAa1J,IAAasI,uCAC7B,4BAAQQ,UAAU,sBACf/H,EAAE,sCAMb,yBAAK+H,UAAU,kBACb,kBAAC,IAAQ,CAACY,YAAY,6BA7JpC,yBAAKZ,UAAU,oCACZ,EAAI1H,GAASiC,KAAI,SAACwG,EAAKjE,GACtB,OACE,kBAACkE,EAAA,EAAM,CACL3F,KAAK,WACLrB,IAAK8C,EACLmE,aAAA,UAAeF,EAAG,YAAI9I,EAAE,6BACxBiJ,MAAO,kBAACC,EAAA,EAAO,CAACvM,QAASmM,GAAK,6BAAMA,IACpCK,QAAStI,EAAa+C,SAASkF,GAC/BJ,GAAII,EACJM,SAAU,SAACb,IACkD,IAAvD1H,EAAawI,QAAQd,EAAEe,OAAOC,aAAa,OAC7CzI,EAAgB,GAAD,SAAKD,GAAY,CAAE0H,EAAEe,OAAOC,aAAa,SAExDzI,EAAgBD,EAAaiC,QAAO,SAACO,GAAM,OAAKA,IAAWkF,EAAEe,OAAOC,aAAa,kBAkJ/E,kBAAC,IAAQ,CAACZ,YAAY,8BA3GpC,yBAAKZ,UAAU,iBACZ,EAAItH,GAAQ6B,KAAI,SAACwG,EAAKjE,GAAU,MAC/B,OACE,yBAAKkD,UAAU,cAAchG,IAAG,eAAU8C,IACxC,kBAACqE,EAAA,EAAO,CAACvM,QAAO,UAAKqD,EAAE,kCAAiC,YAAI8I,SAAgB,QAAb,EAAHA,EAAKU,mBAAW,WAAb,EAAH,OAAAV,IAAwBW,aAAa,GAC/F,kBAACV,EAAA,EAAM,CACL3F,KAAK,WACL+F,QAASlI,GAAY2C,SAASkF,GAC9BE,aAAA,UAAehJ,EAAE,kCAAiC,YAAI8I,aAAG,EAAHA,EAAKU,cAAa,YAAIxJ,EAAE,6BAC9E0I,GAAII,EACJM,SAAU,SAACb,IACiD,IAAtDtH,GAAYoI,QAAQd,EAAEe,OAAOC,aAAa,OAC5CrI,GAAe,GAAD,SAAKD,IAAW,CAAEsH,EAAEe,OAAOC,aAAa,SAEtDrI,GAAeD,GAAY6B,QAAO,SAACQ,GAAK,OAAKA,IAAUiF,EAAEe,OAAOC,aAAa,cAKrF,yBAAKxB,UAAU,YAAY2B,MAAO,CAAEC,WAAYC,YAAmBd,YA2F7D,kBAAC,IAAQ,CAACH,YAAY,6BAzIpC,yBAAKZ,UAAU,oCACZ,EAAIxH,GACFsJ,MAAK,SAACC,EAAOC,GAAK,OAAM/J,EAAE,cAAD,OAAe8J,KAAY9J,EAAE,cAAD,OAAe+J,KAAY,EAAI,KACpFzH,KAAI,SAACwG,EAAKjE,GACT,OACE,kBAACkE,EAAA,EAAM,CACL3F,KAAK,WACLrB,IAAK8C,EACLoE,MAAO,kBAACC,EAAA,EAAO,CAACvM,QAASqD,EAAE,cAAD,OAAe8I,KAAQ,6BAAM9I,EAAE,cAAD,OAAe8I,MACvEK,QAASpI,EAAY6C,SAASkF,GAC9BJ,GAAII,EACJM,SAAU,SAACb,IACiD,IAAtDxH,EAAYsI,QAAQd,EAAEe,OAAOC,aAAa,OAC5CvI,EAAe,GAAD,SAAKD,GAAW,CAAEwH,EAAEe,OAAOC,aAAa,SAEtDvI,EAAeD,EAAY+B,QAAO,SAACM,GAAI,OAAKA,IAASmF,EAAEe,OAAOC,aAAa,kBA6H1E5H,IACC,kBAAC,IAAQ,CAACgH,YAAY,+BArFtC,yBAAKZ,UAAU,sCACZ,EAAIpH,GAAU2B,KAAI,SAACwG,EAAKjE,GACvB,OACE,kBAACkE,EAAA,EAAM,CACL3F,KAAK,WACLrB,IAAK8C,EACLsE,QAAS5H,GAAaqC,SAASkF,GAC/BE,aAAA,UAAeF,EAAG,YAAI9I,EAAE,6BACxBiJ,MAAOjJ,EAAE,gBAAD,OAAiB8I,EAAIkB,sBAC7BtB,GAAII,EACJM,SAAU,SAACb,IACkD,IAAvDhH,GAAa8H,QAAQd,EAAEe,OAAOC,aAAa,OAC7C/H,GAAgB,GAAD,SAAKD,IAAY,CAAEgH,EAAEe,OAAOC,aAAa,SAExD/H,GAAgBD,GAAauB,QAAO,SAACS,GAAM,OAAKA,IAAWgF,EAAEe,OAAOC,aAAa,qBA8ErF,yBAAKxB,UAAU,YACf,8BAAUA,UAAU,iBAClB,4BAAQW,GAAG,kBAAkBX,UAAU,mBAAmB/H,EAAE,2CAC5D,yBAAK+H,UAAU,WAAWkC,KAAK,QAAQC,kBAAgB,mBACrD,kBAACnB,EAAA,EAAM,CACLE,MAAOjJ,EAAE,0CACTmJ,QAAShI,GACTiI,SAAU,SAACb,GAAC,OAAKnH,GAA+BmH,EAAEe,OAAOH,UACzDT,GAAG,uCAEL,kBAACK,EAAA,EAAM,CACLE,MAAOjJ,EAAE,0CACTmJ,QAAS9H,GACT+H,SAAU,SAACb,GAAC,OAAKjH,GAA0BiH,EAAEe,OAAOH,UACpDT,GAAG,yCAIT,yBAAKX,UAAU,YACf,yBAAKA,UAAU,UACb,kBAACoC,EAAA,EAAM,CAACpC,UAAU,qBAAqBqC,QAAS5E,GAAayD,MAAOjJ,EAAE,mBACpEqK,SAA0B,IAAhB5I,KACZ,kBAAC0I,EAAA,EAAM,CAACpC,UAAU,qBAAqBqC,QAASnH,GAAagG,MAAOjJ,EAAE,oBAI1E,6BACE,yBAAK+H,UAAU,oBACf,yBAAKA,UAAU,WAAW/H,EAAE,qCCnf3BpB", "file": "chunks/chunk.61.js", "sourcesContent": ["var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./FilterAnnotModal.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".open.FilterAnnotModal{visibility:visible}.closed.FilterAnnotModal{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.FilterAnnotModal .footer .modal-button.confirm:hover{background:var(--primary-button-hover);border-color:var(--primary-button-hover);color:var(--gray-0)}.FilterAnnotModal .footer .modal-button.confirm{background:var(--primary-button);border-color:var(--primary-button);color:var(--primary-button-text)}.FilterAnnotModal .footer .modal-button.confirm.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.FilterAnnotModal .footer .modal-button.confirm.disabled span{color:var(--primary-button-text)}.FilterAnnotModal .footer .modal-button.cancel:hover,.FilterAnnotModal .footer .modal-button.secondary-btn-custom:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6)}.FilterAnnotModal .footer .modal-button.cancel,.FilterAnnotModal .footer .modal-button.secondary-btn-custom{border:none;box-shadow:inset 0 0 0 1px var(--primary-button);color:var(--primary-button)}.FilterAnnotModal .footer .modal-button.cancel.disabled,.FilterAnnotModal .footer .modal-button.secondary-btn-custom.disabled{cursor:default;border:none;box-shadow:inset 0 0 0 1px rgba(43,115,171,.5);color:rgba(43,115,171,.5)}.FilterAnnotModal .footer .modal-button.cancel.disabled span,.FilterAnnotModal .footer .modal-button.secondary-btn-custom.disabled span{color:rgba(43,115,171,.5)}.FilterAnnotModal{position:fixed;left:0;bottom:0;z-index:100;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background:var(--modal-negative-space)}.FilterAnnotModal .modal-container .wrapper .modal-content{padding:10px}.FilterAnnotModal .footer{display:flex;flex-direction:row;justify-content:flex-end;width:100%;margin-top:13px}.FilterAnnotModal .footer.modal-footer{padding:16px;margin:0;border-top:1px solid var(--divider)}.FilterAnnotModal .footer .modal-button{display:flex;justify-content:center;align-items:center;padding:6px 18px;margin:8px 0 0;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;height:30px;cursor:pointer}.FilterAnnotModal .footer .modal-button.confirm{margin-left:4px}.FilterAnnotModal .footer .modal-button.secondary-btn-custom{border-radius:4px;padding:2px 20px 4px;cursor:pointer}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .FilterAnnotModal .footer .modal-button{padding:23px 8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .FilterAnnotModal .footer .modal-button{padding:23px 8px}}.FilterAnnotModal .swipe-indicator{background:var(--swipe-indicator-bg);border-radius:2px;height:4px;width:38px;position:absolute;top:12px;margin-left:auto;margin-right:auto;left:0;right:0}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .FilterAnnotModal .swipe-indicator{display:none}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .FilterAnnotModal .swipe-indicator{display:none}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .FilterAnnotModal .swipe-indicator{width:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .FilterAnnotModal .swipe-indicator{width:32px}}.FilterAnnotModal .container .filter-modal .body .tab-list{width:100%;height:28px;display:flex;border-radius:4px;color:var(--text-color)}.FilterAnnotModal .container .filter-modal .body .tab-list .tab-options-button{background-color:transparent;text-align:center;vertical-align:middle;line-height:24px;text-overflow:ellipsis;overflow:hidden;white-space:nowrap;flex:1;border-radius:0;cursor:pointer}.FilterAnnotModal .container .filter-modal .body .tab-list .tab-options-button:first-child{border-bottom-left-radius:4px;border-top-left-radius:4px}.FilterAnnotModal .container .filter-modal .body .tab-list .tab-options-button:last-child{border-bottom-right-radius:4px;border-top-right-radius:4px}.FilterAnnotModal .container .filter-modal .body .tab-list .tab-options-button:hover{background:var(--popup-button-hover)}.FilterAnnotModal .container .filter-modal .body .tab-list .tab-options-button.selected{background:var(--tab-color-selected);cursor:default}.FilterAnnotModal .container .filter-modal .body .tab-list .tab-options-button.focus-visible,.FilterAnnotModal .container .filter-modal .body .tab-list .tab-options-button:focus-visible{outline:var(--focus-visible-outline)}.FilterAnnotModal{font-size:.9em}.FilterAnnotModal .container{display:flex;flex-direction:column;align-items:center;border-radius:4px;background:var(--component-background);width:360px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .FilterAnnotModal .container{width:100%}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .FilterAnnotModal .container{width:100%}}.FilterAnnotModal .container .message{font-size:14px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .FilterAnnotModal .container .message{font-size:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .FilterAnnotModal .container .message{font-size:16px}}.FilterAnnotModal .container .filter-modal{display:flex;flex-direction:column;width:100%}.FilterAnnotModal .container .filter-modal .divider{height:1px;width:100%;background:var(--divider)}.FilterAnnotModal .container .filter-modal .header{display:flex;justify-content:space-between;margin:16px;font-size:16px;font-weight:700;align-items:center;height:24px}.FilterAnnotModal .container .filter-modal .header .Button{height:32px}.FilterAnnotModal .container .filter-modal .body{padding:16px}.FilterAnnotModal .container .filter-modal .body .filter-options{height:200px;width:100%}.FilterAnnotModal .container .filter-modal .body .filter-options .tab-panel{height:100%;width:100%;display:flex;flex-direction:column}.FilterAnnotModal .container .filter-modal .body .filter-options .tab-panel .include-replies{display:flex;height:20px;margin-top:12px}.FilterAnnotModal .container .filter-modal .body .filter-options .tab-panel .include-replies label{color:var(--gray-7)}.FilterAnnotModal .container .filter-modal .body .filter-options .tab-panel .three-column-filter{margin-top:20px;overflow-y:scroll;display:grid;grid-gap:5px 16px;grid-template-columns:repeat(3,minmax(0,1fr))}.FilterAnnotModal .container .filter-modal .body .filter-options .tab-panel .color-filters{margin-top:20px;overflow-y:scroll;display:grid;grid-gap:16px;grid-template-columns:repeat(5,minmax(0,1fr))}.FilterAnnotModal .container .filter-modal .body .filter-options .tab-panel .buttons{display:grid;padding:6px 0;grid-template-columns:100px 100px}.FilterAnnotModal .container .filter-modal .body .filter-options .tab-panel .colorSelect{display:flex;flex-direction:row}.FilterAnnotModal .container .filter-modal .body .filter-options .tab-panel .colorSelect .colorCell{width:22px;height:22px;border-radius:10000000px;margin-left:4px;border:1px solid var(--gray-6)}.FilterAnnotModal .container .filter-modal .body .filter-options .tab-panel label{overflow:hidden}.FilterAnnotModal .container .filter-modal .body .filter-options .tab-panel label div{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.FilterAnnotModal .container .filter-modal .settings-body{display:flex;flex-direction:column;margin:16px 0 0;border:0;padding:16px;font-size:var(--font-size-default);grid-gap:12px;gap:12px}.FilterAnnotModal .container .filter-modal .settings-body .settings-header{font-weight:var(--font-weight-bold);padding:0}.FilterAnnotModal .container .filter-modal .settings-body .settings{display:flex;flex-direction:column;grid-gap:8px;gap:8px}.FilterAnnotModal .container .filter-modal .footer{display:flex;justify-content:space-between;width:100%;padding:16px;margin-top:0}.FilterAnnotModal .container .filter-modal .footer .Button{display:flex;justify-content:center;align-items:center;color:var(--primary-button-text);padding:6px 18px;width:auto;width:-moz-fit-content;width:fit-content;background:var(--primary-button);border-radius:4px;height:32px;cursor:pointer}.FilterAnnotModal .container .filter-modal .footer .Button.filter-annot-apply:hover{background:var(--primary-button-hover)}.FilterAnnotModal .container .filter-modal .footer .Button.disabled{border-color:var(--gray-6);cursor:not-allowed}.FilterAnnotModal .container .filter-modal .footer .filter-annot-clear{color:var(--blue-5);background:none}.FilterAnnotModal .container .filter-modal .footer .filter-annot-clear:not(.disabled):hover{color:var(--secondary-button-hover);background:none}.FilterAnnotModal .container .filter-modal .footer .filter-annot-clear:disabled{opacity:.5}.FilterAnnotModal .container .filter-modal .footer .filter-annot-clear:disabled span{color:unset}.FilterAnnotModal .container .tab-list .tab-options-divider+.tab-options-button{border-left:none!important}.FilterAnnotModal .container .tab-list .tab-options-button{border-top:1px solid var(--tab-border-color);border-bottom:1px solid var(--tab-border-color)}.FilterAnnotModal .container .tab-list .tab-options-button:first-child{border-left:1px solid var(--tab-border-color)}.FilterAnnotModal .container .tab-list .tab-options-button:last-child{border-right:1px solid var(--tab-border-color)}.FilterAnnotModal .container .tab-list .tab-options-button:hover{background:var(--tab-background-color-hover);border-top:1px solid var(--tab-border-color-hover);border-bottom:1px solid var(--tab-border-color-hover);border-right:1px solid var(--tab-border-color-hover)}.FilterAnnotModal .container .tab-list .tab-options-button:hover+button,.FilterAnnotModal .container .tab-list .tab-options-button:hover+div{border-left:none}.FilterAnnotModal .container .tab-list .tab-options-button.selected{background:var(--tab-color-selected);border:1px solid var(--tab-color-selected);color:var(--tab-text-color-selected)}.FilterAnnotModal .container .tab-list .tab-options-button.selected+button,.FilterAnnotModal .container .tab-list .tab-options-button.selected+div{border-left:none!important}.FilterAnnotModal .container .tab-list .tab-options-button:not(.selected){border-right:1px solid var(--tab-border-color)}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React, { useState, useEffect } from 'react';\nimport classNames from 'classnames';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\nimport core from 'core';\nimport actions from 'actions';\nimport selectors from 'selectors';\nimport fireEvent from 'helpers/fireEvent';\nimport defaultTool from 'constants/defaultTool';\nimport Events from 'constants/events';\nimport { mapAnnotationToKey } from 'constants/map';\nimport DataElements from 'constants/dataElement';\nimport { rgbaToHex, getHexToRgbaString } from 'helpers/color';\nimport { getAnnotationClass } from 'helpers/getAnnotationClass';\nimport Choice from 'components/Choice';\nimport Button from 'components/Button';\nimport { Tabs, Tab, TabPanel } from 'components/Tabs';\nimport Tooltip from 'components/Tooltip';\nimport { COMMON_COLORS } from 'constants/commonColors';\nimport ModalWrapper from 'components/ModalWrapper';\n\nimport './FilterAnnotModal.scss';\n\nconst TABS_ID = 'filterAnnotModal';\n\nconst FilterAnnotModal = () => {\n  const [isDisabled, isOpen, colorMap, selectedTab, annotationFilters,\n    isMeasurementAnnotationFilterEnabled, customNoteFilter] = useSelector((state) => [\n    selectors.isElementDisabled(state, DataElements.FILTER_MODAL),\n    selectors.isElementOpen(state, DataElements.FILTER_MODAL),\n    selectors.getColorMap(state),\n    selectors.getSelectedTab(state, TABS_ID),\n    selectors.getAnnotationFilters(state),\n    selectors.getIsMeasurementAnnotationFilterEnabled(state),\n    selectors.getCustomNoteFilter(state),\n  ]);\n\n  const [t] = useTranslation();\n  const dispatch = useDispatch();\n\n  const [authors, setAuthors] = useState([]);\n  const [annotTypes, setAnnotTypes] = useState([]);\n  const [colors, setColorTypes] = useState([]);\n  const [statuses, setStatusTypes] = useState([]);\n  const [authorFilter, setAuthorFilter] = useState([]);\n  const [typesFilter, setTypesFilter] = useState([]);\n  const [colorFilter, setColorFilter] = useState([]);\n  const [checkRepliesForAuthorFilter, setCheckRepliesForAuthorFilter] = useState(true);\n  const [isDocumentFilterActive, setIsDocumentFilterActive] = useState(false);\n  const [statusFilter, setStatusFilter] = useState([]);\n  const [filterCount, setFilterCount] = useState(0);\n  const [ifShowAnnotationStatus, setIfShowAnnotationStatus] = useState(false);\n\n  const getIconColor = (annot) => {\n    const key = mapAnnotationToKey(annot);\n    const iconColorProperty = colorMap[key]?.iconColor;\n\n    return annot[iconColorProperty];\n  };\n\n  const similarColorExist = (currColors, newColor) => {\n    const colorObject = currColors.map((c) => Object.assign({\n      R: parseInt(`${c[1]}${c[2]}`, 16),\n      G: parseInt(`${c[3]}${c[4]}`, 16),\n      B: parseInt(`${c[5]}${c[6]}`, 16)\n    }));\n\n    const threshold = 10;\n    const similarColors = colorObject\n      .filter((c) => Math.abs(newColor.R - c.R) < threshold\n        && Math.abs(newColor.G - c.G) < threshold\n        && Math.abs(newColor.B - c.B) < threshold);\n\n    return !!similarColors.length;\n  };\n\n  const filterApply = () => {\n    const newFilter = (annot, documentViewerKey = 1) => {\n      let type = true;\n      let author = true;\n      let color = true;\n      let status = true;\n      if (typesFilter.length > 0) {\n        const isMeasurementAnnotation = annot.IT || annot.getCustomData('trn-is-count');\n        if (isMeasurementAnnotationFilterEnabled && isMeasurementAnnotation) {\n          const measurementKey = mapAnnotationToKey(annot);\n          type = typesFilter.includes(measurementKey);\n        } else {\n          type = typesFilter.includes(getAnnotationClass(annot));\n        }\n      }\n      if (authorFilter.length > 0) {\n        author = authorFilter.includes(core.getDisplayAuthor(annot['Author'], documentViewerKey));\n        if (!author && checkRepliesForAuthorFilter) {\n          const allReplies = annot.getReplies();\n          for (const reply of allReplies) {\n            // Short-circuit the search if at least one reply is created by\n            // one of the desired authors\n            if (authorFilter.includes(core.getDisplayAuthor(reply['Author'], documentViewerKey))) {\n              author = true;\n              break;\n            }\n          }\n        }\n      }\n      if (colorFilter.length > 0) {\n        const iconColor = getIconColor(annot);\n        if (iconColor) {\n          color = similarColorExist(colorFilter, iconColor);\n        } else {\n          // check for default color if no color is available\n          color = colorFilter.includes(COMMON_COLORS['gray8']);\n        }\n      }\n      if (statusFilter.length > 0) {\n        if (annot.getStatus()) {\n          status = statusFilter.includes(annot.getStatus());\n        } else {\n          status = statusFilter.includes('None');\n        }\n      }\n      return type && author && color && status;\n    };\n    dispatch(actions.setInternalNoteFilter(newFilter));\n    dispatch(actions.setAnnotationFilters({\n      isDocumentFilterActive,\n      includeReplies: checkRepliesForAuthorFilter,\n      authorFilter,\n      colorFilter,\n      typeFilter: typesFilter,\n      statusFilter\n    }));\n    const redrawList = [];\n    if (isDocumentFilterActive) {\n      core.getDocumentViewers().forEach((documentViewer, index) => documentViewer.getAnnotationManager()\n        .getAnnotationsList().forEach((annot) => {\n          const shouldHide = !newFilter(annot, index + 1);\n          if (shouldHide !== annot.NoView) {\n            annot.NoView = shouldHide;\n            redrawList.push(annot);\n          }\n        }));\n    } else {\n      core.getDocumentViewers().forEach((documentViewer) => documentViewer.getAnnotationManager()\n        .getAnnotationsList().forEach((annot) => {\n          if (annot.NoView === true) {\n            annot.NoView = false;\n            redrawList.push(annot);\n          }\n        }));\n    }\n    core.getDocumentViewers().forEach((documentViewer) => documentViewer.getAnnotationManager().drawAnnotationsFromList(redrawList));\n    fireEvent(\n      Events.ANNOTATION_FILTER_CHANGED,\n      {\n        types: typesFilter,\n        authors: authorFilter,\n        colors: colorFilter,\n        statuses: statusFilter,\n        checkRepliesForAuthorFilter\n      }\n    );\n    closeModal();\n  };\n\n  const filterClear = () => {\n    setCheckRepliesForAuthorFilter(false);\n    setIsDocumentFilterActive(false);\n    setAuthorFilter([]);\n    setTypesFilter([]);\n    setColorFilter([]);\n    setStatusFilter([]);\n\n    const redrawList = [];\n    core.getDocumentViewers().forEach((documentViewer) => documentViewer.getAnnotationManager().getAnnotationsList()\n      .forEach((annot) => {\n        if (annot.NoView === true) {\n          annot.NoView = false;\n          redrawList.push(annot);\n        }\n      }));\n    core.getDocumentViewers().forEach((documentViewer) => documentViewer.getAnnotationManager().drawAnnotationsFromList(redrawList));\n  };\n\n  const closeModal = () => {\n    dispatch(actions.closeElement(DataElements.FILTER_MODAL));\n    core.setToolMode(defaultTool);\n  };\n\n  useEffect(() => {\n    const clearAllFilters = () => {\n      filterClear();\n      filterApply();\n    };\n    core.addEventListener('documentUnloaded', clearAllFilters);\n    return () => {\n      core.removeEventListener('documentUnloaded', clearAllFilters);\n    };\n  }, []);\n\n  useEffect(() => {\n    let annotLists = core.getDocumentViewers().map((documentViewer) => documentViewer.getAnnotationManager().getAnnotationsList());\n\n    if (customNoteFilter) {\n      const filteredList = (annotLists[0]) ? annotLists[0].filter(customNoteFilter): [];\n      annotLists[0] = filteredList;\n      const filteredList2 = (annotLists[1]) ? annotLists[1].filter(customNoteFilter) : [];\n      annotLists[1] = filteredList2;\n    }\n\n    const annots = [].concat(...annotLists).filter((annot) => !annot.Hidden);\n    // set is a great way to remove any duplicate additions and ensure the unique items are present\n    // the only gotcha that it should not be used by state since not always it will trigger a rerender\n    const authorsToBeAdded = new Set();\n    const annotTypesToBeAdded = new Set();\n    const annotColorsToBeAdded = new Set();\n    const annotStatusesToBeAdded = new Set();\n    annots.forEach((annot) => {\n      const displayAuthor = core.getDisplayAuthor(annot['Author']);\n      if (displayAuthor && displayAuthor !== '') {\n        authorsToBeAdded.add(displayAuthor);\n      }\n\n      const isInFormBuilderMode = core.getAnnotationManager().getFormFieldCreationManager().isInFormFieldCreationMode();\n      const ignoreFilter = (\n        (!isInFormBuilderMode && annot instanceof window.Core.Annotations.WidgetAnnotation) ||\n        (annot instanceof window.Core.Annotations.StickyAnnotation && annot.isReply()) ||\n        annot instanceof window.Core.Annotations.Link\n      );\n\n      if (ignoreFilter) {\n        return;\n      }\n\n      // If the annotation is also measurement, and the API is on, we want to instead add the measurement type\n      const isMeasurementAnnotation = annot.IT || annot.getCustomData('trn-is-count');\n      if (isMeasurementAnnotationFilterEnabled && isMeasurementAnnotation) {\n        const measurementKey = mapAnnotationToKey(annot);\n        annotTypesToBeAdded.add(measurementKey);\n      } else {\n        annotTypesToBeAdded.add(getAnnotationClass(annot));\n      }\n      const iconColor = getIconColor(annot);\n      if (iconColor && !similarColorExist([...annotColorsToBeAdded], iconColor)) {\n        annotColorsToBeAdded.add(rgbaToHex(iconColor.R, iconColor.G, iconColor.B, iconColor.A));\n      }\n\n      if (annot.getStatus()) {\n        annotStatusesToBeAdded.add(annot.getStatus());\n      } else {\n        annotStatusesToBeAdded.add('None');\n      }\n    });\n\n    setAuthors([...authorsToBeAdded]);\n    setAnnotTypes([...annotTypesToBeAdded]);\n    setColorTypes([...annotColorsToBeAdded]);\n    setStatusTypes([...annotStatusesToBeAdded]);\n\n    core.addEventListener('documentUnloaded', closeModal);\n    return () => {\n      core.removeEventListener('documentUnloaded', closeModal);\n    };\n  }, [isOpen, isMeasurementAnnotationFilterEnabled]);\n\n  useEffect(() => {\n    if (selectedTab === DataElements.ANNOTATION_STATUS_FILTER_PANEL_BUTTON && !ifShowAnnotationStatus) {\n      dispatch(actions.setSelectedTab(TABS_ID, DataElements.ANNOTATION_USER_FILTER_PANEL_BUTTON));\n    }\n  }, [isOpen, selectedTab, ifShowAnnotationStatus]);\n\n  useEffect(() => {\n    setFilterCount(\n      (checkRepliesForAuthorFilter ? 1 : 0) +\n      (isDocumentFilterActive ? 1 : 0) +\n      authorFilter.length +\n      colorFilter.length +\n      typesFilter.length +\n      statusFilter.length\n    );\n  }, [checkRepliesForAuthorFilter, isDocumentFilterActive, authorFilter, colorFilter, typesFilter, statusFilter]);\n\n  useEffect(() => {\n    setIfShowAnnotationStatus((statuses.length > 1) || (statuses.length === 1 && statuses[0] !== 'None'));\n  }, [statuses]);\n\n  useEffect(() => {\n    if (isOpen) {\n      setIsDocumentFilterActive(annotationFilters.isDocumentFilterActive);\n      setCheckRepliesForAuthorFilter(annotationFilters.includeReplies);\n      setAuthorFilter(annotationFilters.authorFilter);\n      setColorFilter(annotationFilters.colorFilter);\n      setTypesFilter(annotationFilters.typeFilter);\n      setStatusFilter(annotationFilters.statusFilter);\n    }\n  }, [isOpen]);\n\n  const renderAuthors = () => {\n    return (\n      <div className=\"user-filters three-column-filter\">\n        {[...authors].map((val, index) => {\n          return (\n            <Choice\n              type=\"checkbox\"\n              key={index}\n              aria-label={`${val} ${t('formField.types.checkbox')}`}\n              label={<Tooltip content={val}><div>{val}</div></Tooltip>}\n              checked={authorFilter.includes(val)}\n              id={val}\n              onChange={(e) => {\n                if (authorFilter.indexOf(e.target.getAttribute('id')) === -1) {\n                  setAuthorFilter([...authorFilter, e.target.getAttribute('id')]);\n                } else {\n                  setAuthorFilter(authorFilter.filter((author) => author !== e.target.getAttribute('id')));\n                }\n              }}\n            />\n          );\n        })}\n      </div>\n    );\n  };\n\n  const renderAnnotTypes = () => {\n    return (\n      <div className=\"type-filters three-column-filter\">\n        {[...annotTypes]\n          .sort((type1, type2) => (t(`annotation.${type1}`) <= t(`annotation.${type2}`) ? -1 : 1))\n          .map((val, index) => {\n            return (\n              <Choice\n                type=\"checkbox\"\n                key={index}\n                label={<Tooltip content={t(`annotation.${val}`)}><div>{t(`annotation.${val}`)}</div></Tooltip>}\n                checked={typesFilter.includes(val)}\n                id={val}\n                onChange={(e) => {\n                  if (typesFilter.indexOf(e.target.getAttribute('id')) === -1) {\n                    setTypesFilter([...typesFilter, e.target.getAttribute('id')]);\n                  } else {\n                    setTypesFilter(typesFilter.filter((type) => type !== e.target.getAttribute('id')));\n                  }\n                }}\n              />\n            );\n          })}\n      </div>\n    );\n  };\n\n  const renderColorTypes = () => {\n    return (\n      <div className=\"color-filters\">\n        {[...colors].map((val, index) => {\n          return (\n            <div className=\"colorSelect\" key={`color${index}`}>\n              <Tooltip content={`${t('option.colorPalette.colorLabel')} ${val?.toUpperCase?.()}`} hideOnClick={false}>\n                <Choice\n                  type=\"checkbox\"\n                  checked={colorFilter.includes(val)}\n                  aria-label={`${t('option.colorPalette.colorLabel')} ${val?.toUpperCase()} ${t('formField.types.checkbox')}`}\n                  id={val}\n                  onChange={(e) => {\n                    if (colorFilter.indexOf(e.target.getAttribute('id')) === -1) {\n                      setColorFilter([...colorFilter, e.target.getAttribute('id')]);\n                    } else {\n                      setColorFilter(colorFilter.filter((color) => color !== e.target.getAttribute('id')));\n                    }\n                  }}\n                />\n              </Tooltip>\n              <div className=\"colorCell\" style={{ background: getHexToRgbaString(val) }}></div>\n            </div>\n          );\n        })}\n      </div>\n    );\n  };\n\n  const renderStatusTypes = () => {\n    return (\n      <div className=\"status-filters three-column-filter\">\n        {[...statuses].map((val, index) => {\n          return (\n            <Choice\n              type=\"checkbox\"\n              key={index}\n              checked={statusFilter.includes(val)}\n              aria-label={`${val} ${t('formField.types.checkbox')}`}\n              label={t(`option.state.${val.toLocaleLowerCase()}`)}\n              id={val}\n              onChange={(e) => {\n                if (statusFilter.indexOf(e.target.getAttribute('id')) === -1) {\n                  setStatusFilter([...statusFilter, e.target.getAttribute('id')]);\n                } else {\n                  setStatusFilter(statusFilter.filter((status) => status !== e.target.getAttribute('id')));\n                }\n              }}\n            />\n          );\n        })}\n      </div>\n    );\n  };\n\n  const modalClass = classNames({\n    Modal: true,\n    FilterAnnotModal: true,\n    open: isOpen,\n    closed: !isOpen,\n  });\n\n  return isDisabled ? null : (\n    <div className={modalClass} data-element={DataElements.FILTER_MODAL}>\n      <ModalWrapper\n        isOpen={isOpen}\n        title={`${t('option.filterAnnotModal.filters')} (${filterCount})`}\n        closeHandler={closeModal}\n        onCloseClick={closeModal}\n        swipeToClose\n      >\n        <div className=\"container\" onMouseDown={(e) => e.stopPropagation()}>\n          {core.getAnnotationsList().length > 0 ? (\n            <div className=\"filter-modal\">\n              <div className=\"body\">\n                <Tabs id={TABS_ID}>\n                  <div className=\"tab-list\">\n                    <Tab dataElement={DataElements.ANNOTATION_USER_FILTER_PANEL_BUTTON}>\n                      <button className=\"tab-options-button\">\n                        {t('option.filterAnnotModal.user')}\n                      </button>\n                    </Tab>\n                    <div className=\"tab-options-divider\" />\n                    <Tab dataElement={DataElements.ANNOTATION_COLOR_FILTER_PANEL_BUTTON}>\n                      <button className=\"tab-options-button\">\n                        {t('option.filterAnnotModal.color')}\n                      </button>\n                    </Tab>\n                    <div className=\"tab-options-divider\" />\n                    <Tab dataElement={DataElements.ANNOTATION_TYPE_FILTER_PANEL_BUTTON}>\n                      <button className=\"tab-options-button\">\n                        {t('option.filterAnnotModal.type')}\n                      </button>\n                    </Tab>\n                    {ifShowAnnotationStatus && (\n                      <>\n                        <div className=\"tab-options-divider\" />\n                        <Tab dataElement={DataElements.ANNOTATION_STATUS_FILTER_PANEL_BUTTON}>\n                          <button className=\"tab-options-button\">\n                            {t('option.filterAnnotModal.status')}\n                          </button>\n                        </Tab>\n                      </>\n                    )}\n                  </div>\n                  <div className=\"filter-options\">\n                    <TabPanel dataElement=\"annotationUserFilterPanel\">\n                      {renderAuthors()}\n                    </TabPanel>\n                    <TabPanel dataElement=\"annotationColorFilterPanel\">\n                      {renderColorTypes()}\n                    </TabPanel>\n                    <TabPanel dataElement=\"annotationTypeFilterPanel\">\n                      {renderAnnotTypes()}\n                    </TabPanel>\n                    {ifShowAnnotationStatus && (\n                      <TabPanel dataElement=\"annotationStatusFilterPanel\">\n                        {renderStatusTypes()}\n                      </TabPanel>\n                    )}\n                  </div>\n                </Tabs>\n              </div>\n              <div className=\"divider\"></div>\n              <fieldset className=\"settings-body\">\n                <legend id=\"filter-settings\" className=\"settings-header\">{t('option.filterAnnotModal.filterSettings')}</legend>\n                <div className=\"settings\" role=\"group\" aria-labelledby='filter-settings'>\n                  <Choice\n                    label={t('option.filterAnnotModal.includeReplies')}\n                    checked={checkRepliesForAuthorFilter}\n                    onChange={(e) => setCheckRepliesForAuthorFilter(e.target.checked)}\n                    id=\"filter-annot-modal-include-replies\"\n                  />\n                  <Choice\n                    label={t('option.filterAnnotModal.filterDocument')}\n                    checked={isDocumentFilterActive}\n                    onChange={(e) => setIsDocumentFilterActive(e.target.checked)}\n                    id=\"filter-annot-modal-filter-document\"\n                  />\n                </div>\n              </fieldset>\n              <div className=\"divider\"></div>\n              <div className=\"footer\">\n                <Button className=\"filter-annot-clear\" onClick={filterClear} label={t('action.clearAll')}\n                  disabled={filterCount === 0} />\n                <Button className=\"filter-annot-apply\" onClick={filterApply} label={t('action.apply')} />\n              </div>\n            </div>\n          ) : (\n            <div>\n              <div className=\"swipe-indicator\" />\n              <div className=\"message\">{t('message.noAnnotationsFilter')}</div>\n            </div>\n          )}\n        </div>\n      </ModalWrapper>\n    </div>\n  );\n};\n\nexport default FilterAnnotModal;\n", "import FilterAnnotModal from './FilterAnnotModal';\n\nexport default FilterAnnotModal;"], "sourceRoot": ""}