{"version": 3, "sources": ["webpack:///./src/ui/src/components/Spinner/Spinner.js", "webpack:///./src/ui/src/components/Spinner/index.js", "webpack:///./src/ui/src/components/ListSeparator/ListSeparator.js", "webpack:///./src/ui/src/components/ListSeparator/index.js", "webpack:///./src/ui/src/components/Spinner/Spinner.scss?ed1d", "webpack:///./src/ui/src/components/Spinner/Spinner.scss", "webpack:///./src/ui/src/components/ListSeparator/ListSeparator.scss?fd29", "webpack:///./src/ui/src/components/ListSeparator/ListSeparator.scss", "webpack:///./src/ui/src/components/SearchResult/SearchResult.scss?7fd5", "webpack:///./src/ui/src/components/SearchResult/SearchResult.scss", "webpack:///./src/ui/src/components/SearchOverlay/SearchOverlay.scss?68e2", "webpack:///./src/ui/src/components/SearchOverlay/SearchOverlay.scss", "webpack:///./src/ui/src/components/SearchPanel/SearchPanel.scss?9418", "webpack:///./src/ui/src/components/SearchPanel/SearchPanel.scss", "webpack:///./src/ui/src/components/SearchResult/SearchResult.js", "webpack:///./src/ui/src/components/SearchResult/index.js", "webpack:///./src/ui/src/components/SearchOverlay/SearchOverlay.js", "webpack:///./src/ui/src/components/SearchOverlay/SearchOverlayContainer.js", "webpack:///./src/ui/src/components/SearchOverlay/SearchOverlayRedux.js", "webpack:///./src/ui/src/components/SearchOverlay/index.js", "webpack:///./src/ui/src/hooks/useSearch.js", "webpack:///./src/ui/src/components/SearchPanel/SearchPanel.js", "webpack:///./src/ui/src/components/SearchPanel/SearchPanelContainer.js", "webpack:///./src/ui/src/components/SearchPanel/index.js"], "names": ["Spinner", "height", "width", "spinnerStyle", "className", "style", "propTypes", "renderContent", "PropTypes", "func", "children", "node", "ListSeparator", "props", "content", "React", "memo", "api", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "SearchResultListSeparatorPropTypes", "currentResultIndex", "number", "isRequired", "searchResults", "arrayOf", "object", "t", "pageLabe<PERSON>", "any", "isProcessingSearchResults", "bool", "SearchResultListSeparator", "previousIndex", "currentListItem", "previousListItem", "isFirstListItem", "isInDifferentPage", "pageNum", "listSeparatorText", "role", "SearchResultListItemPropTypes", "result", "activeResultIndex", "onSearchResultClick", "activeDocumentViewerKey", "SearchResultListItem", "customizableUI", "useSelector", "state", "featureFlags", "ambientStr", "resultStrStart", "resultStrEnd", "resultStr", "textBeforeSearchValue", "slice", "searchValue", "textAfterSearchValue", "classNames", "onClick", "aria-current", "SearchResultPropTypes", "searchStatus", "oneOf", "onClickResult", "SearchResult", "isSearchInProgress", "cellMeasureCache", "useMemo", "CellMeasurerCache", "defaultHeight", "fixedWidth", "listRef", "useRef", "useState", "listSize", "setListSize", "clearAll", "<PERSON><PERSON><PERSON><PERSON>", "useCallback", "rendererOptions", "index", "key", "parent", "cache", "columnIndex", "rowIndex", "registerChild", "ref", "useEffect", "current", "scrollToRow", "tabIndex", "overscanRowCount", "rowCount", "deferredMeasurementCache", "rowHeight", "scrollToIndex", "SearchResultWithContentRectHOC", "measureRef", "contentRect", "rest", "bounds", "oneOfType", "shape", "SearchResultWithContentRectHOCAndBounds", "withContentRect", "SearchResultsContainer", "isPanelOpen", "isSearchOverlayDisabled", "string", "isCaseSensitive", "isWholeWord", "isWildcard", "setSearchValue", "setCaseSensitive", "setSearchStatus", "setWholeWord", "setWildcard", "executeSearch", "selectNextResult", "selectPreviousResult", "SearchOverlay", "useTranslation", "replaceValue", "nextResultValue", "setReplaceValue", "setIsSearchInProgress", "isReplaceBtnDisabled", "setReplaceBtnDisabled", "isReplaceAllBtnDisabled", "setReplaceAllBtnDisabled", "isMoreOptionsOpen", "setMoreOptionOpen", "showReplaceSpinner", "setShowReplaceSpinner", "isReplacementRegexValid", "setReplacementRegexValid", "allowInitialSearch", "setAllowInitialSearch", "isSearchAndReplaceDisabled", "selectors", "isElementDisabled", "getFeatureFlags", "searchTextInputRef", "RegExp", "error", "numberOfResultsFound", "setTimeout", "focus", "console", "warn", "caseSensitive", "wholeWord", "wildcard", "clearSearchResult", "core", "addEventListener", "onPagesUpdated", "removeEventListener", "search", "isOfficeEditorMode", "getDocument", "getOfficeEditor", "updateSearchData", "debouncedSearch", "debounce", "throttleSearch", "throttle", "onOfficeDocumentEdited", "clearSearchResults", "caseSensitiveSearchOptionOnChange", "event", "isChecked", "target", "checked", "wholeWordSearchOptionOnChange", "wildcardOptionOnChange", "nextButtonOnClick", "previousButtonOnClick", "searchAndReplaceAll", "getInstanceNode", "instance", "Core", "ContentEdit", "searchAndReplaceText", "documentViewer", "getPageSearchResults", "replaceWith", "toggleMoreOptionsBtn", "localStorage", "setItem", "searchAndReplaceOne", "getActiveSearchResult", "dispatch", "useDispatch", "isSearchDoneAndNotProcessingResults", "showSpinner", "searchOptionsComponents", "Choice", "dataElement", "id", "onChange", "label", "Icon", "glyph", "type", "autoComplete", "value", "placeholder", "aria-label", "undefined", "data-element", "disabled", "title", "confirmation<PERSON><PERSON>ning", "message", "confirmBtnText", "onConfirm", "actions", "showWarningMessage", "aria-live", "store", "searchOptions", "regex", "overrideSearchExecution", "getOverrideSearchExecution", "searchTextFullFactory", "searchTextFull", "nextResultIndex", "setActiveSearchResult", "nextIndex", "setNextResultValue", "prevResultIndex", "SearchOverlayContainer", "useStore", "mapDispatchToProps", "closeElements", "ConnectedSearchOverlayRedux", "connect", "DataElements", "SEARCH_OVERLAY", "getSearchValue", "getReplaceValue", "getNextResultValue", "connectedComponent", "useSearch", "setSearchResults", "activeSearchResult", "activeSearchResultIndex", "setActiveSearchResultIndex", "documentViewersCount", "getDocumentViewers", "coreSearchResults", "getDocumentViewer", "newActiveSearchResultIndex", "findIndex", "searchResult", "isSearchResultEqual", "activeDocumentViewer", "activeSearchResultChanged", "newActiveSearchResult", "searchResultsChanged", "newSearchResults", "searchInProgressEventHandler", "isSearching", "defaultActiveSearchResult", "documentViewers", "isOpen", "isMobile", "array", "currentWidth", "closeSearchPanel", "setActiveResult", "isInDesktopOnlyMode", "isCustomPanel", "noop", "SearchPanel", "onCloseButtonClick", "resultIndex", "searchEventListener", "addSearchListener", "removeSearchListener", "getClassName", "min<PERSON><PERSON><PERSON>", "DataElementWrapper", "SearchPanelContainer", "SEARCH_PANEL", "parentDataElement", "isMobileSize", "isElementOpen", "getPageLabels", "shouldClearSearchPanelOnClose", "getActiveDocumentViewerKey", "shallowEqual", "getPanel<PERSON>idth", "getSearchPanelWidth", "clearSearchInputValue", "RESIZE_BAR_WIDTH", "combinedProps"], "mappings": "gHAceA,G,QAXC,SAAH,GAA4C,QAAtCC,cAAM,IAAG,SAAM,MAAEC,MAC5BC,EAAe,CACnBF,SACAC,WAHqC,IAAG,SAAM,GAMhD,OACE,yBAAKE,UAAU,UAAUC,MAAOF,MCRrBH,O,sECGTM,G,QAAY,CAChBC,cAAeC,IAAUC,KACzBC,SAAUF,IAAUG,OAGhBC,EAAiB,SAASC,GAC9B,IAAMC,EAAUD,EAAMN,cAAgBM,EAAMN,gBAAkBM,EAAMH,SACpE,OAAO,wBAAIN,UAAU,iBAAiBU,IAGxCF,EAAcN,UAAYA,EAEXS,UAAMC,KAAKJ,GCfXA,O,qBCFf,IAAIK,EAAM,EAAQ,IACFH,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQI,WAAaJ,EAAQK,QAAUL,KAG/CA,EAAU,CAAC,CAACM,EAAOC,EAAIP,EAAS,MAG9C,IAAIQ,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP3B,EAAIH,EAASQ,GAI1BF,EAAO2B,QAAUjC,EAAQkC,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,goCAAioC,KAG1pC0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAI/B,EAAM,EAAQ,IACFH,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQI,WAAaJ,EAAQK,QAAUL,KAG/CA,EAAU,CAAC,CAACM,EAAOC,EAAIP,EAAS,MAG9C,IAAIQ,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP3B,EAAIH,EAASQ,GAI1BF,EAAO2B,QAAUjC,EAAQkC,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,0kCAA2kC,KAGpmC0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAI/B,EAAM,EAAQ,IACFH,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQI,WAAaJ,EAAQK,QAAUL,KAG/CA,EAAU,CAAC,CAACM,EAAOC,EAAIP,EAAS,MAG9C,IAAIQ,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP3B,EAAIH,EAASQ,GAI1BF,EAAO2B,QAAUjC,EAAQkC,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,w/DAAy/D,KAGlhE0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAI/B,EAAM,EAAQ,IACFH,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQI,WAAaJ,EAAQK,QAAUL,KAG/CA,EAAU,CAAC,CAACM,EAAOC,EAAIP,EAAS,MAG9C,IAAIQ,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP3B,EAAIH,EAASQ,GAI1BF,EAAO2B,QAAUjC,EAAQkC,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,s3PAAu3P,KAGh5P0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAI/B,EAAM,EAAQ,IACFH,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQI,WAAaJ,EAAQK,QAAUL,KAG/CA,EAAU,CAAC,CAACM,EAAOC,EAAIP,EAAS,MAG9C,IAAIQ,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP3B,EAAIH,EAASQ,GAI1BF,EAAO2B,QAAUjC,EAAQkC,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,2mJAA4mJ,KAGroJ0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qgECAvB,IAAMC,EAAqC,CACzCC,mBAAoB1C,IAAU2C,OAAOC,WACrCC,cAAe7C,IAAU8C,QAAQ9C,IAAU+C,QAAQH,WACnDI,EAAGhD,IAAUC,KAAK2C,WAClBK,WAAYjD,IAAU8C,QAAQ9C,IAAUkD,KAAKN,WAC7CO,0BAA2BnD,IAAUoD,MAGvC,SAASC,EAA0BhD,GACjC,IAAQqC,EAAqDrC,EAArDqC,mBAAoBG,EAAiCxC,EAAjCwC,cAAeG,EAAkB3C,EAAlB2C,EAAGC,EAAe5C,EAAf4C,WAExCK,EAAuC,IAAvBZ,EAA2BA,EAAqBA,EAAqB,EACrFa,EAAkBV,EAAcH,GAChCc,EAAmBX,EAAcS,GAEjCG,EAAkBD,IAAqBD,EACvCG,EAAoBF,EAAiBG,UAAYJ,EAAgBI,QAEvE,GAAIF,GAAmBC,EAAmB,CACxC,IAAME,EAAoB,GAAH,OAAMZ,EAAE,sBAAqB,YAAIC,EAAWM,EAAgBI,QAAU,IAC7F,OACE,yBAAKE,KAAK,QACR,kBAACzD,EAAA,EAAa,KAAEwD,IAItB,OAAO,KAGTP,EAA0BvD,UAAY2C,EAEtC,IAAMqB,EAAgC,CACpCC,OAAQ/D,IAAU+C,OAAOH,WACzBF,mBAAoB1C,IAAU2C,OAAOC,WACrCoB,kBAAmBhE,IAAU2C,OAAOC,WACpCqB,oBAAqBjE,IAAUC,KAC/BiE,wBAAyBlE,IAAU2C,QAGrC,SAASwB,EAAqB9D,GAC5B,IAAO+D,EAA6E,EAA3DC,aAAY,SAACC,GAAK,MAAK,CAACA,EAAMC,aAAaH,mBAAgB,GAA/D,GACbL,EAAgG1D,EAAhG0D,OAAQrB,EAAwFrC,EAAxFqC,mBAAoBsB,EAAoE3D,EAApE2D,kBAAmBC,EAAiD5D,EAAjD4D,oBAAqBC,EAA4B7D,EAA5B6D,wBACpEM,EAAwDT,EAAxDS,WAAYC,EAA4CV,EAA5CU,eAAgBC,EAA4BX,EAA5BW,aAAcC,EAAcZ,EAAdY,UAC5CC,EAAwBJ,EAAWK,MAAM,EAAGJ,GAC5CK,EAA6B,KAAfN,EAAoBG,EAAYH,EAAWK,MAAMJ,EAAgBC,GAC/EK,EAAuBP,EAAWK,MAAMH,GAC9C,OACE,4BACEb,KAAK,OACLjE,UAAWoF,IAAW,CACpB,cAAgB,EAChB,SAAYtC,IAAuBsB,EACnC,aAAcI,IAEhBa,QAAS,WACHhB,GACFA,EAAoBvB,EAAoBqB,EAAQG,IAGpDgB,eAAcxC,IAAuBsB,GAEpCY,EACD,0BAAMhF,UAAU,gBACbkF,GAEFC,GAIPZ,EAAqBrE,UAAYgE,EAEjC,IAAMqB,EAAwB,CAC5BzF,MAAOM,IAAU2C,OACjBlD,OAAQO,IAAU2C,OAClBqB,kBAAmBhE,IAAU2C,OAC7ByC,aAAcpF,IAAUqF,MAAM,CAAC,uBAAwB,qBAAsB,gBAC7ExC,cAAe7C,IAAU8C,QAAQ9C,IAAU+C,QAC3CC,EAAGhD,IAAUC,KAAK2C,WAClB0C,cAAetF,IAAUC,KACzBgD,WAAYjD,IAAU8C,QAAQ9C,IAAUkD,KACxCgB,wBAAyBlE,IAAU2C,QAGrC,SAAS4C,EAAalF,GACpB,IAAQZ,EAAiKY,EAAjKZ,OAAQ2F,EAAyJ/E,EAAzJ+E,aAAcvC,EAA2IxC,EAA3IwC,cAAemB,EAA4H3D,EAA5H2D,kBAAmBhB,EAAyG3C,EAAzG2C,EAAGsC,EAAsGjF,EAAtGiF,cAAerC,EAAuF5C,EAAvF4C,WAAYE,EAA2E9C,EAA3E8C,0BAA2BqC,EAAgDnF,EAAhDmF,mBAAoBtB,EAA4B7D,EAA5B6D,wBACvIuB,EAAmBlF,IAAMmF,SAAQ,WACrC,OAAO,IAAIC,oBAAkB,CAAEC,cAAe,GAAIC,YAAY,MAC7D,IACGC,EAAUvF,IAAMwF,OAAO,MACoB,IAAjBxF,IAAMyF,SAAS,GAAE,GAA1CC,EAAQ,KAAEC,EAAW,KAEC,IAAzBrD,EAActB,QAEhBkE,EAAiBU,WAGftD,EAActB,QAAUsB,EAActB,SAAW0E,IAGnDC,EAAYrD,EAActB,QAC1BkE,EAAiBU,YAGnB,IAAMC,EAAc7F,IAAM8F,aAAY,SAA6BC,GACjE,IAAQC,EAA8BD,EAA9BC,MAAOC,EAAuBF,EAAvBE,IAAKC,EAAkBH,EAAlBG,OAAQ5G,EAAUyG,EAAVzG,MACtBkE,EAASlB,EAAc0D,GAC7B,OACE,kBAAC,IAAY,CACXG,MAAOjB,EACPkB,YAAa,EACbH,IAAKA,EACLC,OAAQA,EACRG,SAAUL,IAET,gBAAGM,EAAa,EAAbA,cAAa,OACf,yBAAKhD,KAAK,MAAMiD,IAAKD,EAAehH,MAAOA,GACzC,kBAACwD,EAAyB,CACxBX,mBAAoB6D,EACpB1D,cAAeA,EACfI,WAAYA,EACZD,EAAGA,IAEL,kBAACmB,EAAoB,CACnBJ,OAAQA,EACRrB,mBAAoB6D,EACpBvC,kBAAmBA,EACnBC,oBAAqBqB,EACrBpB,wBAAyBA,UAMlC,CAACuB,EAAkB5C,EAAemB,EAAmBhB,EAAGC,IAQ3D,OANA1C,IAAMwG,WAAU,WACD,MAATjB,IACa,QAAf,EAAAA,EAAQkB,eAAO,OAAf,EAAiBC,YAAYjD,MAE9B,CAACA,IAEU,MAAVvE,EAIK,KAGY,gBAAjB2F,GAC0B,IAAzBvC,EAActB,QACb4B,EAWJ,kBAAC,IAAe,CACdzD,MAAO,IACPD,OAAQA,EACRyH,UAAW,EACXC,iBAAkB,GAClBC,SAAUvE,EAActB,OACxB8F,yBAA0B5B,EAC1B6B,UAAW7B,EAAiB6B,UAC5BlB,YAAaA,EACbU,IAAKhB,EACLyB,cAAevD,EAAoB,IApBjCwB,EACK,KAGP,yBAAK5F,UAAU,QAAO,uBAAGA,UAAU,aAAaoD,EAAE,uBAsBxD,SAASwE,EAA+BnH,GACtC,IAAQoH,EAAqCpH,EAArCoH,WAAYC,EAAyBrH,EAAzBqH,YAAgBC,EAAI,EAAKtH,EAAK,GAC1CZ,EAAWiI,EAAYE,OAAvBnI,OACR,OACE,yBAAKG,UAAU,UAAUkH,IAAKW,GAC5B,kBAAC,EAAY,GAAChI,OAAQA,GAAYkI,KAPxCpC,EAAazF,UAAYqF,EAWzBqC,EAA+B1H,UAAY,CACzC4H,YAAa1H,IAAU+C,OACvB0E,WAAYzH,IAAU6H,UAAU,CAC9B7H,IAAUC,KACVD,IAAU8H,MAAM,CAAEd,QAAShH,IAAUkD,SAIzC,IAAM6E,EAA0CC,YAAgB,SAAhBA,CAA0BR,GC1M3DS,ED4MgB,SAAC5H,GAC9B,OAAQ,kBAAC0H,EAA4C1H,I,ijBE9MvD,8lGAAAQ,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,+kBAAAA,GAAA,gEAAAA,GAAA,0JAAAA,EAAA,6FAAAA,GAAA,mIAAAA,IAAA,4SAAAA,IAAA,2OAAAA,EAAA,iBAAAA,EAAA,EAAAA,IAAA,EAAAA,GAAA,EAAAA,GAAA,SAkBA,IAAMf,EAAY,CAChBoI,YAAalI,IAAUoD,KACvB+E,wBAAyBnI,IAAUoD,KACnC0B,YAAa9E,IAAUoI,OACvBhD,aAAcpF,IAAUqF,MAAM,CAAC,uBAAwB,qBAAsB,gBAC7EgD,gBAAiBrI,IAAUoD,KAC3BkF,YAAatI,IAAUoD,KACvBmF,WAAYvI,IAAUoD,KACtBP,cAAe7C,IAAU8C,QAAQ9C,IAAU+C,QAC3CiB,kBAAmBhE,IAAU2C,OAC7B6F,eAAgBxI,IAAUC,KAAK2C,WAC/B6F,iBAAkBzI,IAAUC,KAAK2C,WACjC8F,gBAAiB1I,IAAUC,KAAK2C,WAChC+F,aAAc3I,IAAUC,KAAK2C,WAC7BgG,YAAa5I,IAAUC,KAAK2C,WAC5BiG,cAAe7I,IAAUC,KAAK2C,WAC9BkG,iBAAkB9I,IAAUC,KAC5B8I,qBAAsB/I,IAAUC,KAChCkD,0BAA2BnD,IAAUoD,KACrCc,wBAAyBlE,IAAU2C,QAGrC,SAASqG,EAAc3I,GACrB,IAAQ2C,EAAMiG,cAANjG,EACAmF,EAA0J9H,EAA1J8H,wBAAyBtF,EAAiIxC,EAAjIwC,cAAemB,EAAkH3D,EAAlH2D,kBAAmB8E,EAA+FzI,EAA/FyI,iBAAkBC,EAA6E1I,EAA7E0I,qBAAsB5F,EAAuD9C,EAAvD8C,0BAA2Be,EAA4B7D,EAA5B6D,wBAC9HY,EAA+FzE,EAA/FyE,YAAa0D,EAAkFnI,EAAlFmI,eAAgBK,EAAkExI,EAAlEwI,cAAeK,EAAmD7I,EAAnD6I,aAAcC,EAAqC9I,EAArC8I,gBAAiBC,EAAoB/I,EAApB+I,gBAC3Ef,EAAsJhI,EAAtJgI,gBAAiBI,EAAqIpI,EAArIoI,iBAAkBH,EAAmHjI,EAAnHiI,YAAaK,EAAsGtI,EAAtGsI,aAAcJ,EAAwFlI,EAAxFkI,WAAYK,EAA4EvI,EAA5EuI,YAAaF,EAA+DrI,EAA/DqI,gBAAiBlD,EAA8CnF,EAA9CmF,mBAAoB6D,EAA0BhJ,EAA1BgJ,sBAC5HjE,EAA8B/E,EAA9B+E,aAAc8C,EAAgB7H,EAAhB6H,YAC8C,IAAdlC,oBAAS,GAAK,GAA7DsD,EAAoB,KAAEC,EAAqB,KACwB,IAAdvD,oBAAS,GAAK,GAAnEwD,EAAuB,KAAEC,EAAwB,KACK,IAAdzD,oBAAS,GAAK,GAAtD0D,EAAiB,KAAEC,EAAiB,KACwB,IAAf3D,oBAAS,GAAM,GAA5D4D,GAAkB,KAAEC,GAAqB,KAC0B,KAAd7D,oBAAS,GAAK,GAAnE8D,GAAuB,MAAEC,GAAwB,MACW,KAAf/D,oBAAS,GAAM,GAA5DgE,GAAkB,MAAEC,GAAqB,MAC1CC,GAA6B7F,aAAY,SAACC,GAAK,OAAK6F,IAAUC,kBAAkB9F,EAAO,uBACvFF,GAAiBC,aAAY,SAACC,GAAK,aAAqC,QAArC,EAAK6F,IAAUE,gBAAgB/F,UAAM,aAAhC,EAAkCF,kBAC1EkG,GAAqBvE,mBAG3BgB,qBAAU,WACR,IAE2B,IAAIwD,OAAO,wBACpC,MAAOC,GACPT,IAAyB,MAE1B,IAEHhD,qBAAU,WACJ0D,GAAuB,GACzB/B,EAAgB,iBAEjB,CAAC7F,IAEJkE,qBAAU,WACJuD,GAAmBtD,SAAWkB,GAEhCwC,YAAW,WACTJ,GAAmBtD,QAAQ2D,QAC3BV,IAAsB,KAtBX,KA0BVC,IAA+BJ,KAA2B5B,GAC7D0C,QAAQC,KAAK,yDAEd,CAAC3C,EAAaG,IAEjBtB,qBAAU,WACJjC,GAAeA,EAAYvD,OAAS,EAClCyI,IACFnB,EAAc/D,EAAa,CACzBgG,cAAezC,EACf0C,UAAWzC,EACX0C,SAAUzC,IAId0C,OAED,CAAC5C,EAAiBC,EAAaC,EAAYrE,IAE9C6C,qBAAU,WAER,OADAmE,IAAKC,iBAAiB,eAAgBC,IAC/B,WACLF,IAAKG,oBAAoB,eAAgBD,QAI7C,IAAMA,GAAiB,WACrBE,GAAOxG,IAGHwG,GAAM,6BAAG,WAAOxG,GAAW,sEAC3BA,GAAeA,EAAYvD,OAAS,GAAC,gBAED,GADtC8H,GAAsB,GACtBX,EAAgB,uBAEZ6C,cAAsB,CAAF,+BAChBL,IAAKM,cAAcC,kBAAkBC,mBAAkB,OAE/D7C,EAAc/D,EAAa,CACzBgG,cAAezC,EACf0C,UAAWzC,EACX0C,SAAUzC,IACT,uBACOzD,GACVmG,KACD,4CACF,gBAhBW,sCAkBNU,GAAkBtF,sBACtBuF,IAASN,GA3EM,KA4Ef,CAACjD,EAAiBC,EAAaC,IAG3BsD,GAAiBxF,sBACrByF,IAASR,GAhFM,KAiFf,CAACjD,EAAiBC,EAAaC,IAGjCxB,qBAAU,WAAM,MACRgF,EAAyB,WACzBjH,GAAeA,EAAYvD,OAAS,GACtCsK,GAAe/G,IAMnB,OAFkB,QAAlB,EAAAoG,IAAKM,qBAAa,OAAlB,EAAoBL,iBAAiB,uBAAwBY,GAEtD,WAAM,MACO,QAAlB,EAAAb,IAAKM,qBAAa,OAAlB,EAAoBH,oBAAoB,uBAAwBU,MAEjE,CAACjH,IAoBJ,SAASmG,KACPC,IAAKc,qBACLxD,EAAe,IACfE,EAAgB,wBAChBU,EAAgB,IAChBG,GAAsB,GACtBE,GAAyB,GAG3B,IAAMwC,GAAoC5F,uBACxC,SAAmD6F,GACjD,IAAMC,EAAYD,EAAME,OAAOC,QAC/B5D,EAAiB0D,KAChB,IAGCG,GAAgCjG,uBACpC,SAA+C6F,GAC7C,IAAMC,EAAYD,EAAME,OAAOC,QAC/B1D,EAAawD,KACZ,IAGCI,GAAyBlG,uBAC7B,SAAwC6F,GACtC,IAAMC,EAAYD,EAAME,OAAOC,QAC/BzD,EAAYuD,KACX,IAGCK,GAAoBnG,uBACxB,WACMyC,GACFA,EAAiBjG,EAAemB,KAGpC,CAAC8E,EAAkBjG,EAAemB,IAG9ByI,GAAwBpG,uBAC5B,WACM0C,GACFA,EAAqBlG,EAAemB,KAGxC,CAAC+E,EAAsBlG,EAAemB,IAGlC0I,GAAsBrG,sBAAW,6BACrC,kFACMmD,IAA2BL,EAAe,iDAGlB,OAA5BU,IAAsB,GAAM,SACtB8C,cAAkBC,SAASC,KAAKC,YAAYC,qBAAqB,CACrEC,eAAgBL,cAAkBC,SAASC,KAAKG,eAChDnK,cAAeqI,IAAK+B,uBACpBC,YAAahE,IACb,OACFW,IAAsB,GAAO,2CAVW,OAWzC,WAXyC,gCADL,GAarC,CAACX,IAGGiE,GAAuB,WAC3BnM,OAAOoM,aAAaC,QAAQ,oBAAqB3D,GACjDC,GAAmBD,IAGf4D,GAAsBjH,sBAAW,6BACrC,kFACMiD,IAAwBH,EAAe,iDAGf,OAA5BU,IAAsB,GAAM,SAEtB8C,cAAkBC,SAASC,KAAKC,YAAYC,qBAAqB,CACrEC,eAAgBL,cAAkBC,SAASC,KAAKG,eAChDE,YAAahE,EACbrG,cAAe,CAACqI,IAAKqC,2BACrB,OAEF1D,IAAsB,GAAO,2CAZW,OAazC,WAbyC,gCADL,GAerC,CAACX,EAAcC,EAAiBG,IAG5BkE,GAAWC,cA8BjB,GAAItF,EACF,OAAO,KAET,IAAMsC,GAAuB5H,EAAgBA,EAActB,OAAS,EAE9DmM,GAAuD,gBAAjBtI,IAAmCjC,EACzEwK,IAAgBD,IAAuClI,EAAsB,kBAAChG,EAAA,EAAO,MAAM,KAE3FoO,GAA2B,yBAAKhO,UAAU,WAC9C,kBAACiO,EAAA,EAAM,CACLC,YAAY,4BACZC,GAAG,wBACH1B,QAAShE,EACT2F,SAAU/B,GACVgC,MAAOjL,EAAE,oCACTkE,SAAUgB,EAAc,GAAK,IAE/B,kBAAC2F,EAAA,EAAM,CACLC,YAAY,wBACZC,GAAG,oBACH1B,QAAS/D,EACT0F,SAAU1B,GACV2B,MAAOjL,EAAE,oCACTkE,SAAUgB,EAAc,GAAK,IAE/B,kBAAC2F,EAAA,EAAM,CACLC,YAAY,uBACZC,GAAG,mBACH1B,QAAS9D,EACTyF,SAAUzB,GACV0B,MAAOjL,EAAE,+BACTkE,SAAUgB,EAAc,GAAK,KAIjC,OACE,yBAAKtI,UAAWoF,IAAW,CACzB,eAAiB,EACjB,aAAcZ,MAEd,yBAAKxE,UAAU,mBACZwE,IAAkB,kBAAC8J,EAAA,EAAI,CAACC,MAAM,uBAC/B,2BACEvO,UAAU,qBACVkH,IAAKwD,GACL8D,KAAK,OACLC,aAAa,MACbL,SAtLkB,SAAC9B,GACzB1D,EAAe0D,EAAME,OAAOkC,OAC5B3C,GAAgBO,EAAME,OAAOkC,OAEzBpC,EAAME,OAAOkC,OAASpF,IACxBK,GAAsB,GACtBE,GAAyB,KAiLrB6E,MAAOxJ,EACPyJ,YAAanK,GAAiB,GAAKpB,EAAE,qCACrCwL,aAAYxL,EAAE,qCACd+K,GAAG,qBACH7G,SAAUgB,EAAc,GAAK,SAEbuG,IAAhB3J,GAA8BA,EAAYvD,OAAS,GACnD,4BACE3B,UAAU,qBACVqF,QAASgG,GACTuD,aAAYxL,EAAE,sCAEd,kBAACkL,EAAA,EAAI,CAACC,MAAM,iBAMfjE,KAA+BJ,GAA2B,KACxDJ,EACG,yBAAK9J,UAAU,iBACf,4BAAQA,UAAU,SAASqF,QAASkI,IAAuBnK,EAAE,kCAAkC,IAAC,kBAACkL,EAAA,EAAI,CAACC,MAAM,sBAE5G,yBAAKvO,UAAU,iBACf,4BAAQA,UAAU,SAASqF,QAASkI,IAAuBnK,EAAE,kCAAkC,IAAC,kBAACkL,EAAA,EAAI,CAACC,MAAM,wBAIhHzE,EACA,6BACGkE,GAEE1D,KAA+BJ,GAA2B,KACzD,yBAAK4E,eAAa,mBAAmB9O,UAAU,mBAC7C,uBAAGA,UAAU,4BAA4BoD,EAAE,+BAC3C,yBAAKpD,UAAU,mBACb,2BAAOwO,KAAM,OACXI,aAAYxL,EAAE,8BACdgL,SAnNa,SAAC9B,GAChC9C,EAAgB8C,EAAME,OAAOkC,OACzBpC,EAAME,OAAOkC,OAASxJ,IACxByE,GAAsB,GACtBE,GAAyB,KAgNT6E,MAAOpF,KAGX,yBAAKtJ,UAAU,mBACXgK,GAAsB,kBAACpK,EAAA,EAAO,CAACE,MAAO,GAAID,OAAQ,KAAS,KAC7D,4BAAQG,UAAU,yBAAyB+O,SAAUnF,EACnDvE,QAzHkB,WACpC,IAAM2J,EAAQ5L,EAAE,kCAEV6L,EAAsB,CAC1BC,QAFc9L,EAAE,+CAGhB4L,QACAG,eAAgB/L,EAAE,8BAClBgM,UAAW,WACTtC,OAGJc,GAASyB,IAAQC,mBAAmBL,MA8GuB7L,EAAE,kCAC7C,4BAAQpD,UAAU,qBAAqB+O,SAAUrF,IAAyBH,IAAoB+B,IAAKqC,wBACjGtI,QA7GkB,WACpC,IAAM2J,EAAQ5L,EAAE,kCAEV6L,EAAsB,CAC1BC,QAFc9L,EAAE,+CAGhB4L,QACAG,eAAgB/L,EAAE,8BAClBgM,UAAW,WACT1B,OAGJE,GAASyB,IAAQC,mBAAmBL,MAkGuB7L,EAAE,kCAnBlC4K,GA0BzB,yBAAKhO,UAAU,YACf,yBAAKA,UAAU,UACK,yBAAjBwF,EAAgD,KAAOuI,GACxD,uBAAG/N,UAAU,YAAYuP,YAAU,aAAazB,KAAwClI,EAAqB,GAAH,OAAMiF,GAAoB,YAAIzH,EAAE,iCAA+ByL,GACxKhE,GAAuB,GACtB,yBAAK7K,UAAU,WACb,4BAAQA,UAAU,SAASqF,QAASwH,GAAuB+B,aAAYxL,EAAE,sBACvE,kBAACkL,EAAA,EAAI,CAACtO,UAAU,QAAQuO,MAAM,uBAEhC,4BAAQvO,UAAU,SAASqF,QAASuH,GAAmBgC,aAAYxL,EAAE,sBACnE,kBAACkL,EAAA,EAAI,CAACtO,UAAU,QAAQuO,MAAM,2BAS5CnF,EAAclJ,UAAYA,EAEXkJ,Q,ioCCnZR,SAASH,GAAc/D,EAAahE,EAASsO,GAClD,IAAMC,E,kWAAgB,CAAH,CACjBC,OAAO,GACJxO,GAGL,GAAIgE,QAAmD,CAGrD,IAAMyK,EAA0BC,cAChC,GAAID,EACFA,EAAwBzK,EAAauK,QAEdI,aAAsBL,EAC7CM,CAAe5K,EAAauK,GAAe,IAK1C,SAASvG,KAAkE,IAAjDjG,EAAgB,UAAH,6CAAG,GAAImB,EAAiB,uCAAEwJ,EAAQ,uCAC9E,GAAI3K,EAActB,OAAS,EAAG,CAC5B,IAAMoO,EAAkB3L,IAAsBnB,EAActB,OAAS,EAAI,EAAIyC,EAAoB,EAEjG,GADAkH,IAAK0E,sBAAsB/M,EAAc8M,IACrCnC,EAAU,CACZ,IAAMqC,EAAaF,EAAkB,EAAKA,EAAkB,EAAI,EAChEnC,EAASyB,IAAQa,mBAAmBjN,EAAc8M,GAAkBE,MAKnE,SAAS9G,KAAsE,IAAjDlG,EAAgB,UAAH,6CAAG,GAAImB,EAAiB,uCAAEwJ,EAAQ,uCAClF,GAAI3K,EAActB,OAAS,EAAG,CAC5B,IAAMwO,EAAkB/L,GAAqB,EAAInB,EAActB,OAAS,EAAIyC,EAAoB,EAChGkH,IAAK0E,sBAAsB/M,EAAckN,IACrCvC,GACFA,EAASyB,IAAQa,mBAAmBjN,EAAckN,MAwBzCC,OAnBf,SAAgC3P,GAC9B,IAAMmN,EAAWC,cACX2B,EAAQa,cACd,OACE,kBAAC,EAAa,IACZpH,cAAe,SAAC/D,GAA8B,IAAjBhE,EAAU,UAAH,6CAAG,GACrC+H,GAAc/D,EAAahE,EAASsO,IAEtCtG,iBAAkB,WAA2C,IAA1CjG,EAAgB,UAAH,6CAAG,GAAImB,EAAiB,uCACtD8E,GAAiBjG,EAAemB,EAAmBwJ,IAErDzE,qBAAsB,WAA2C,IAA1ClG,EAAgB,UAAH,6CAAG,GAAImB,EAAiB,uCAC1D+E,GAAqBlG,EAAemB,EAAmBwJ,KAErDnN,K,QC7CJ6P,GAAqB,CACzBC,cAAelB,IAAQkB,cACvB3H,eAAgByG,IAAQzG,eACxBY,gBAAiB6F,IAAQ7F,gBACzBX,iBAAkBwG,IAAQxG,iBAC1BE,aAAcsG,IAAQtG,aACtBC,YAAaqG,IAAQrG,aAOvB,IAAMwH,GAA8BC,aAxBZ,SAAC/L,GAAK,MAAM,CAClC6D,wBAAyBgC,IAAUC,kBAAkB9F,EAAOgM,KAAaC,gBACzEzL,YAAaqF,IAAUqG,eAAelM,GACtC4E,aAAciB,IAAUsG,gBAAgBnM,GACxC6E,gBAAiBgB,IAAUuG,mBAAmBpM,GAC9C+D,gBAAiB8B,IAAU9B,gBAAgB/D,GAC3CgE,YAAa6B,IAAU7B,YAAYhE,GACnCiE,WAAY4B,IAAU5B,WAAWjE,GACjCnB,0BAA2BgH,IAAUhH,0BAA0BmB,MAkB/D4L,GAFkCG,EAJpC,SAA4BhQ,GAC1B,OAAQ,kBAAC,GAA2BA,MC1BvBsQ,GDkCY,SAACtQ,GAC1B,OAAO,kBAAC+P,GAAgC/P,I,skCEwE3BuQ,OAxGf,SAAmB1M,GACjB,IAA4D,KAAlB3D,IAAMyF,SAAS,IAAG,GAArDnD,EAAa,KAAEgO,EAAgB,KAC8B,KAAhBtQ,IAAMyF,WAAU,GAA7D8K,EAAkB,KAAElB,EAAqB,KAC+B,KAAjBrP,IAAMyF,SAAS,GAAE,GAAxE+K,EAAuB,KAAEC,EAA0B,KACoB,KAAtCzQ,IAAMyF,SAAS,wBAAuB,GAAvEZ,EAAY,KAAEsD,EAAe,KAC9B8E,EAAWC,cAEXwD,EADkB/F,IAAKgG,qBACgB3P,OAwF7C,OArFAhB,IAAMwG,WAAU,WAId,IACMoK,EADuBjG,IAAKkG,kBAAkBlN,GACL+I,wBAA0B,GACzE,GAAIkE,EAAkB5P,OAAS,EAAG,CAChC,IAAMuP,EAAqB5F,IAAKqC,wBAChC,GAAIuD,EAAoB,CACtB,IAAMO,EAA6BF,EAAkBG,WAAU,SAACC,GAC9D,OAAOrG,IAAKsG,oBAAoBD,EAAcT,MAEhDD,EAAiBM,GACbE,GAA8B,IAChCzB,EAAsBuB,EAAkBE,IACxCL,EAA2BK,SAG7BR,EAAiBM,GACjBvB,EAAsBuB,EAAkB,IACxCH,EAA2B,MAG9B,IAEHzQ,IAAMwG,WAAU,WACd,IAAM0K,EAAuBvG,IAAKkG,kBAAkBlN,GACpD,SAASwN,EAA0BC,GACjC,IACMN,GADoBI,EAAqBxE,wBAA0B,IACpBqE,WAAU,SAACC,GAC9D,OAAOrG,IAAKsG,oBAAoBD,EAAcI,MAE5CN,GAA8B,IAChCzB,EAAsB+B,GACtBX,EAA2BK,IAI/B,SAASO,IAA4C,IAAvBC,EAAmB,UAAH,6CAAG,GAC/ChB,EAAiBgB,GACbA,GAAgD,IAA5BA,EAAiBtQ,SACvCqO,OAAsBnB,GACtBuC,GAA4B,IAIhC,SAASc,EAA6BC,GACpC,GAAIA,QAEFrJ,EAAgB,6BACX,GAAIqJ,EACTrJ,EAAgB,0BACX,CACL,IAAMsJ,EAA4BP,EAAqBlE,wBAEvD,GAAIyE,EAA2B,CAC7BpC,EAAsBoC,GAEtB,IACMX,GADoBI,EAAqBxE,wBAA0B,IACpBqE,WAAU,SAACC,GAC9D,OAAOrG,IAAKsG,oBAAoBD,EAAcS,MAEhDhB,EAA2BK,GAC3B7D,EAASyB,IAAQa,mBAAmBkC,IAGtCtJ,EAAgB,gBAGpB,IAAMuJ,EAAkB/G,IAAKgG,qBAO7B,OALAe,EAAgBpQ,SAAQ,SAACmL,GACvBA,EAAe7B,iBAAiB,4BAA6BuG,GAC7D1E,EAAe7B,iBAAiB,uBAAwByG,GACxD5E,EAAe7B,iBAAiB,mBAAoB2G,MAE/C,WACLG,EAAgBpQ,SAAQ,SAACmL,GACvBA,EAAe3B,oBAAoB,4BAA6BqG,GAChE1E,EAAe3B,oBAAoB,uBAAwBuG,GAC3D5E,EAAe3B,oBAAoB,mBAAoByG,SAG1D,CAAClC,EAAuBoB,EAA4BtI,EAAiB8E,EAAUyD,EAAsB/M,IAEjG,CACLkB,eACAvC,gBACAiO,qBACAC,0BACArI,oB,miCC5FJ,IAAM5I,GAAY,CAChBoS,OAAQlS,IAAUoD,KAClB+O,SAAUnS,IAAUoD,KACpBH,WAAYjD,IAAUoS,MACtBC,aAAcrS,IAAU2C,OACxB2P,iBAAkBtS,IAAUC,KAC5BsS,gBAAiBvS,IAAUC,KAC3BuS,oBAAqBxS,IAAUoD,KAC/BD,0BAA2BnD,IAAUoD,KACrCc,wBAAyBlE,IAAU2C,OACnC8P,cAAezS,IAAUoD,MAG3B,SAASsP,MAET,SAASC,GAAYtS,GACnB,IACE6R,EAYE7R,EAZF6R,OACAG,EAWEhS,EAXFgS,aACApP,EAUE5C,EAVF4C,WAAU,EAUR5C,EATFiS,wBAAgB,IAAG,EAAAI,GAAI,IASrBrS,EARFkS,uBAAe,IAAG,EAAAG,GAAI,IAQpBrS,EAPFyP,0BAAkB,IAAG,EAAA4C,GAAI,IAOvBrS,EANF8R,gBAAQ,IAAG,GAAK,EAChBK,EAKEnS,EALFmS,oBACArP,EAIE9C,EAJF8C,0BACAe,EAGE7D,EAHF6D,wBAAuB,EAGrB7D,EAFFyN,mBAAW,IAAG,gBAAa,IAEzBzN,EADFoS,qBAAa,IAAG,GAAK,EAGfzP,EAAMiG,cAANjG,EACR,EAAkF4N,GAAU1M,GAApFkB,EAAY,EAAZA,aAAcvC,EAAa,EAAbA,cAAekO,EAAuB,EAAvBA,wBAAyBrI,EAAe,EAAfA,gBAExDkK,EAAqBrS,IAAM8F,aAAY,WACvCiM,GACFA,MAED,CAACA,IAEEhN,EAAgB/E,IAAM8F,aAAY,SAAuBwM,EAAa9O,EAAQG,GAClFqO,EAAgBxO,EAAQG,IACnBsO,GAAuBL,GAC1BG,IAGFxC,EAAmB/L,KAClB,CAACuO,EAAkBH,IAEmD,KAArB5R,IAAMyF,UAAS,GAAM,GAAlER,EAAkB,KAAE6D,EAAqB,KAE1CyJ,EAAsB,WAC1BzJ,GAAsB,IAGxB9I,IAAMwG,WAAU,WAEdgM,YAAkBD,KACjB,IAEHvS,IAAMwG,WAAU,WAEd,OAAO,WACLiM,YAAqBF,MAEtB,IAEH,IAAMlT,EAAYqT,aAAa,oBAAqB,CAAEf,WAClDrS,EAAQ,GAKZ,OAJK4S,IAAkBD,GAAwBL,IAC7CtS,EAAQ,CAAEH,MAAO,GAAF,OAAK2S,EAAY,MAAMa,SAAU,GAAF,OAAKb,EAAY,QAI/D,kBAACc,GAAA,EAAkB,CACjBvT,UAAWA,EACXkO,YAAaA,EACbjO,MAAOA,IAEL2S,GAAuBL,GACvB,yBACEvS,UAAU,mBAEV,4BACEA,UAAU,uBACVqF,QAAS2N,GAET,kBAAC1E,EAAA,EAAI,CACHC,MAAM,sBACNvO,UAAU,iBAIlB,kBAAC,GAAa,CACZwF,aAAcA,EACdsD,gBAAiBA,EACjB7F,cAAeA,EACfmB,kBAAmB+M,EACnB7I,YAAagK,EACb1M,mBAAoBA,EACpB6D,sBAAuBA,EACvBnF,wBAAyBA,IAE3B,kBAAC,EAAY,CACXlB,EAAGA,EACHoC,aAAcA,EACdvC,cAAeA,EACfmB,kBAAmB+M,EACnBzL,cAAeA,EACfrC,WAAYA,EACZE,0BAA2BA,EAC3BqC,mBAAoBA,EACpBtB,wBAAyBA,KAMjCyO,GAAY7S,UAAYA,GAET6S,U,0xECgBAS,OA3If,SAA8B/S,GAC5B,MAAmFA,EAA3EyN,mBAAW,IAAG,EAAAwC,KAAa+C,aAAY,IAAoChT,EAAlCiT,yBAAiB,IAAG,OAAA7E,EAAS,EACxE0D,EAAWoB,cAmBhB,KAVGlP,aACF,SAACC,GAAK,MAAK,CACT6F,IAAUqJ,cAAclP,EAAOwJ,GAC/B3D,IAAUsJ,cAAcnP,GACxB6F,IAAUuJ,8BAA8BpP,GACxC6F,IAAUqI,oBAAoBlO,GAC9B6F,IAAUhH,0BAA0BmB,GACpC6F,IAAUwJ,2BAA2BrP,MAEvCsP,KACD,GAhBC1B,EAAM,KACNjP,EAAU,KACVyQ,EAA6B,KAC7BlB,EAAmB,KACnBrP,EAAyB,KACzBe,EAAuB,KAYrBmO,EAAehO,aAAY,SAACC,GAAK,OAClCgP,GAAqBxF,IAAgBwC,KAAa+C,aAEjDlJ,IAAU0J,cAAcvP,EAAOgP,GAAqBxF,GADpD3D,IAAU2J,oBAAoBxP,MAI5BkJ,EAAWC,cACX6E,EAAmB/R,IAAM8F,aAC7B,WACEmH,EAASyB,IAAQkB,cAAc,CAACrC,OAElC,CAACN,IAGGuG,EAAwBxT,IAAM8F,aAClC,WACEmH,EAASyB,IAAQzG,eAAe,OAElC,CAACgF,IAGGsC,EAAqBvP,IAAM8F,aAC/B,SAA4BxD,GAC1B2K,EAASyB,IAAQa,mBAAmBjN,MAEtC,CAAC2K,IAGG+E,EAAkBhS,IAAM8F,aAAY,SAAyBtC,EAAQG,GACzE,GAAIA,EAEF,OAD6BgH,IAAKkG,kBAAkBlN,GACxB0L,sBAAsB7L,KAEnD,IA8BHxD,IAAMwG,WACJ,YACOyL,GAAuBL,IAMvBD,GAAUwB,IACbxI,IAAKc,qBACL+H,OAGJ,CAAC5B,EAAUD,EAAQwB,EAA+BlB,IAGpDjS,IAAMwG,WAAU,WACd,OAAO,YACAyL,GAAuBL,GAMxBuB,IACFxI,IAAKc,qBACL+H,QAGH,CAAC5B,EAAUuB,EAA+BlB,IAEzC1E,IAAgBwC,KAAa+C,eAE/BhB,GAAgB,GAChBA,GAAgB2B,MAGlB,IAAMC,EAAgB,SACjB5T,GAAK,IACR6R,SACAG,eACApP,aACAqP,mBACAC,kBACAzC,qBACAqC,WACAK,sBACArP,4BACAe,4BAGF,OAAO,kBAAC,GAAgB+P,IChJXb", "file": "chunks/chunk.34.js", "sourcesContent": ["import React from 'react';\nimport './Spinner.scss';\n\nconst Spinner = ({ height = '50px', width = '54px' }) => {\n  const spinnerStyle = {\n    height,\n    width,\n  };\n\n  return (\n    <div className=\"spinner\" style={spinnerStyle}></div>\n  );\n};\n\nexport default Spinner;", "import Spinner from './Spinner';\n\nexport default Spinner;\n", "import React from 'react';\nimport PropTypes from 'prop-types';\n\nimport './ListSeparator.scss';\n\nconst propTypes = {\n  renderContent: PropTypes.func,\n  children: PropTypes.node,\n};\n\nconst ListSeparator = (function(props) {\n  const content = props.renderContent ? props.renderContent() : props.children;\n  return <h4 className=\"ListSeparator\">{content}</h4>;\n});\n\nListSeparator.propTypes = propTypes;\n\nexport default React.memo(ListSeparator);\n", "import ListSeparator from './ListSeparator';\n\nexport default ListSeparator;", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./Spinner.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.spinner{border-top:5px solid var(--border);border:5px solid var(--border);border-top-color:var(--focus-border);border-radius:50%;animation:spin 1.2s ease infinite}@keyframes spin{0%{transform:rotate(0deg)}to{transform:rotate(1turn)}}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./ListSeparator.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.ListSeparator{margin-top:16px;margin-bottom:8px;font-family:Lato;font-weight:500;color:var(--list-separator-color);-webkit-user-select:none;-moz-user-select:none;user-select:none}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./SearchResult.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.SearchResult{background-color:transparent;border:1px solid transparent;display:block;width:calc(100% - 4px);text-align:left;cursor:pointer;border-radius:4px;box-shadow:0 0 3px 0 var(--box-shadow);padding:10px 12px;margin-left:2px;margin-bottom:8px;background:var(--component-background);word-break:break-all}:host(:not([data-tabbing=true])) .SearchResult,html:not([data-tabbing=true]) .SearchResult{outline:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SearchResult{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SearchResult{font-size:13px}}.SearchResult .search-value{background:var(--yellow-1);word-break:break-all}.SearchResult.modular-ui.selected,.SearchResult.modular-ui:hover{border:1px solid var(--focus-border)}.SearchResult.modular-ui.selected{background-color:var(--faded-component-background);box-shadow:none}.SearchResult.modular-ui.focus-visible,.SearchResult.modular-ui:focus-visible{outline:var(--focus-visible-outline)}.SearchResult.modular-ui .search-value{font-weight:700;color:var(--blue-5);background:none}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./SearchOverlay.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.SearchOverlay.modular-ui .replace-buttons .btn-replace:hover:not(:disabled){background:var(--primary-button-hover);border-color:var(--primary-button-hover);color:var(--gray-0)}.SearchOverlay.modular-ui .extra-options .Button:hover,.SearchOverlay.modular-ui .replace-buttons .btn-replace-all:hover:not(:disabled){border:none;background:none;color:var(--blue-6)}.SearchOverlay.modular-ui .extra-options .Button:hover .Icon,.SearchOverlay.modular-ui .replace-buttons .btn-replace-all:hover:not(:disabled) .Icon{color:var(--blue-6)}.SearchOverlay.modular-ui .footer .buttons .button:hover,.SearchOverlay.modular-ui .input-container .clearSearch-button:hover{cursor:pointer;border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6);background-color:var(--faded-component-background)}.SearchOverlay{position:static;display:flex;flex-direction:column;padding-right:8px;flex-wrap:nowrap;border:0;border-radius:0;background:transparent;visibility:visible!important;flex-grow:0}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SearchOverlay .search-and-replace-title{margin:8px 0}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SearchOverlay .search-and-replace-title{margin:8px 0}}.SearchOverlay .input-container{position:relative;box-sizing:border-box;border:1px solid var(--border);border-radius:4px;height:28px;display:flex;align-items:center;justify-content:flex-end;color:var(--text-color);padding:6px 2px 6px 6px;background:var(--component-background)}.SearchOverlay .input-container input{width:100%;padding-right:26px;height:20px;border:none;background:transparent}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SearchOverlay .input-container input{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SearchOverlay .input-container input{font-size:13px}}.SearchOverlay .input-container input::-moz-placeholder{color:var(--placeholder-text)}.SearchOverlay .input-container input::placeholder{color:var(--placeholder-text)}.SearchOverlay .input-container .Icon{width:16px;height:16px}.SearchOverlay .input-container .clearSearch-button{padding:0;border:none;background-color:transparent;cursor:pointer;border-radius:4px;display:flex;align-items:center;justify-content:center;position:absolute;width:24px;height:24px}:host(:not([data-tabbing=true])) .SearchOverlay .input-container .clearSearch-button,html:not([data-tabbing=true]) .SearchOverlay .input-container .clearSearch-button{outline:none}.SearchOverlay .input-container .clearSearch-button svg{color:var(--gray-7)}.SearchOverlay .input-container .clearSearch-button:hover{background:var(--blue-1)}.SearchOverlay .divider{height:1px;width:100%;background:var(--divider);margin:16px 0}.SearchOverlay .options{display:flex;flex-wrap:wrap;margin-top:8px}.SearchOverlay .options>span{margin-right:16px}.SearchOverlay .options>span>label{white-space:nowrap}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SearchOverlay .options>span>label{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SearchOverlay .options>span>label{font-size:13px}}.SearchOverlay .replace-options .replace-buttons{display:flex;flex-direction:row;justify-content:flex-end;padding-top:10px}.SearchOverlay .replace-options .replace-buttons .spinner{margin:0;position:absolute;left:30px}.SearchOverlay .replace-options .replace-buttons .btn-replace{display:flex;justify-content:center;align-items:center;background-color:var(--blue-5);border:1px solid var(--blue-5);color:var(--gray-0);padding:6px 18px;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;height:32px;cursor:pointer}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SearchOverlay .replace-options .replace-buttons .btn-replace{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SearchOverlay .replace-options .replace-buttons .btn-replace{font-size:13px}}.SearchOverlay .replace-options .replace-buttons .btn-replace-all{display:flex;justify-content:center;align-items:center;color:var(--blue-5);padding:6px 18px;width:auto;width:-moz-fit-content;width:fit-content;height:32px;cursor:pointer;margin-right:5px}.SearchOverlay .replace-options .replace-buttons .btn-replace-all:hover:not(:disabled){color:var(--blue-6)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SearchOverlay .replace-options .replace-buttons .btn-replace-all{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SearchOverlay .replace-options .replace-buttons .btn-replace-all{font-size:13px}}.SearchOverlay .replace-options .replace-buttons button:disabled{opacity:.5;cursor:default}.SearchOverlay .extra-options{margin-top:8px}.SearchOverlay .extra-options button.Button{display:flex;justify-content:center;align-items:center;color:var(--blue-5);width:auto;cursor:pointer;margin-right:5px;font-weight:500;height:25px}.SearchOverlay .extra-options button.Button .Icon{color:var(--blue-5);width:14px;margin-top:10px;margin-left:6px}.SearchOverlay .footer{display:flex;align-items:center;justify-content:space-between;color:var(--faded-text);margin-bottom:16px}.SearchOverlay .footer .spinner{height:25px!important;width:25px!important;margin:0}.SearchOverlay .footer .buttons{display:flex;flex-direction:row;align-self:flex-end;justify-content:space-evenly;margin-left:auto;width:64px;height:28px}.SearchOverlay .footer .buttons .button{padding:0;border:none;background-color:transparent;width:28px;height:28px;border-radius:4px;display:flex;align-items:center;justify-content:center;cursor:pointer}:host(:not([data-tabbing=true])) .SearchOverlay .footer .buttons .button,html:not([data-tabbing=true]) .SearchOverlay .footer .buttons .button{outline:none}.SearchOverlay .footer .buttons .button svg{color:var(--gray-6)}.SearchOverlay .footer .buttons .button:hover{background-color:var(--blue-1)}.SearchOverlay .footer .buttons .arrow{width:18px;height:18px}.SearchOverlay.modular-ui .input-container[focus-within]{outline:none;border:1px solid var(--focus-border)}.SearchOverlay.modular-ui .input-container:focus-within{outline:none;border:1px solid var(--focus-border)}.SearchOverlay.modular-ui .input-container .search-panel-input{padding-left:8px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SearchOverlay.modular-ui .input-container .search-panel-input{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SearchOverlay.modular-ui .input-container .search-panel-input{font-size:13px}}.SearchOverlay.modular-ui .extra-options .Button:hover .Icon{color:var(--blue-6)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SearchOverlay.modular-ui .extra-options .Button{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SearchOverlay.modular-ui .extra-options .Button{font-size:13px}}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./SearchPanel.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.search-panel-container{z-index:65;flex-direction:column;background-color:var(--panel-background);display:flex;flex-direction:row;position:relative;overflow:hidden}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .search-panel-container{z-index:95}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .search-panel-container{z-index:95}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .search-panel-container{border-left:1px solid var(--side-panel-border)}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .search-panel-container{border-left:1px solid var(--side-panel-border)}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .search-panel-container{position:fixed;top:0;right:0;height:100%;width:100%}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .search-panel-container{position:fixed;top:0;right:0;height:100%;width:100%}}.SearchPanel{padding:16px 8px 0 16px;display:flex;flex-direction:column;height:100%}.SearchPanel .ListSeparator:first-child{margin-top:0}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.SearchPanel .ReactVirtualized__Grid__innerScrollContainer{max-width:1000px!important}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SearchPanel{width:100%;min-width:100%;padding-top:0}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SearchPanel .close-container{display:flex;align-items:center;justify-content:flex-end;height:28px;margin-bottom:8px;width:100%;padding-right:12px}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SearchPanel .close-container .close-icon-container{padding:0;border:none;background-color:transparent;z-index:3;cursor:pointer}:host(:not([data-tabbing=true])) .App:not(.is-in-desktop-only-mode):not(.is-web-component) .SearchPanel .close-container .close-icon-container,html:not([data-tabbing=true]) .App:not(.is-in-desktop-only-mode):not(.is-web-component) .SearchPanel .close-container .close-icon-container{outline:none}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SearchPanel .close-container .close-icon-container .close-icon{width:24px;height:24px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SearchPanel{width:100%;min-width:100%;padding-top:0}.App.is-web-component:not(.is-in-desktop-only-mode) .SearchPanel .close-container{display:flex;align-items:center;justify-content:flex-end;height:28px;margin-bottom:8px;width:100%;padding-right:12px}.App.is-web-component:not(.is-in-desktop-only-mode) .SearchPanel .close-container .close-icon-container{padding:0;border:none;background-color:transparent;z-index:3;cursor:pointer}:host(:not([data-tabbing=true])) .App.is-web-component:not(.is-in-desktop-only-mode) .SearchPanel .close-container .close-icon-container,html:not([data-tabbing=true]) .App.is-web-component:not(.is-in-desktop-only-mode) .SearchPanel .close-container .close-icon-container{outline:none}.App.is-web-component:not(.is-in-desktop-only-mode) .SearchPanel .close-container .close-icon-container .close-icon{width:24px;height:24px}}.SearchPanel .results{overflow-y:auto;overflow-y:overlay;flex:1}.SearchPanel .results.wild-card-visible{margin-top:110px!important}.SearchPanel .results .ReactVirtualized__List{overflow:overlay!important;padding-right:8px;width:auto!important}.SearchPanel .results .ReactVirtualized__List:focus{outline:none}.SearchPanel .results .ReactVirtualized__List .ReactVirtualized__Grid__innerScrollContainer{max-width:unset!important}.SearchPanel .loader-wrapper{display:flex;padding:10px;justify-content:center}.SearchPanel .info{padding:15px 0}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React from 'react';\nimport { useSelector } from 'react-redux';\nimport { withContentRect } from 'react-measure';\nimport PropTypes from 'prop-types';\nimport './SearchResult.scss';\nimport VirtualizedList from 'react-virtualized/dist/commonjs/List';\nimport CellMeasurer, { CellMeasurerCache } from 'react-virtualized/dist/commonjs/CellMeasurer';\nimport ListSeparator from 'components/ListSeparator';\nimport classNames from 'classnames';\n\nconst SearchResultListSeparatorPropTypes = {\n  currentResultIndex: PropTypes.number.isRequired,\n  searchResults: PropTypes.arrayOf(PropTypes.object).isRequired,\n  t: PropTypes.func.isRequired,\n  pageLabels: PropTypes.arrayOf(PropTypes.any).isRequired,\n  isProcessingSearchResults: PropTypes.bool\n};\n\nfunction SearchResultListSeparator(props) {\n  const { currentResultIndex, searchResults, t, pageLabels } = props;\n\n  const previousIndex = currentResultIndex === 0 ? currentResultIndex : currentResultIndex - 1;\n  const currentListItem = searchResults[currentResultIndex];\n  const previousListItem = searchResults[previousIndex];\n\n  const isFirstListItem = previousListItem === currentListItem;\n  const isInDifferentPage = previousListItem.pageNum !== currentListItem.pageNum;\n\n  if (isFirstListItem || isInDifferentPage) {\n    const listSeparatorText = `${t('option.shared.page')} ${pageLabels[currentListItem.pageNum - 1]}`;\n    return (\n      <div role=\"cell\">\n        <ListSeparator>{listSeparatorText}</ListSeparator>\n      </div>\n    );\n  }\n  return null;\n}\n\nSearchResultListSeparator.propTypes = SearchResultListSeparatorPropTypes;\n\nconst SearchResultListItemPropTypes = {\n  result: PropTypes.object.isRequired,\n  currentResultIndex: PropTypes.number.isRequired,\n  activeResultIndex: PropTypes.number.isRequired,\n  onSearchResultClick: PropTypes.func,\n  activeDocumentViewerKey: PropTypes.number\n};\n\nfunction SearchResultListItem(props) {\n  const [customizableUI] = useSelector((state) => [state.featureFlags.customizableUI]);\n  const { result, currentResultIndex, activeResultIndex, onSearchResultClick, activeDocumentViewerKey } = props;\n  const { ambientStr, resultStrStart, resultStrEnd, resultStr } = result;\n  const textBeforeSearchValue = ambientStr.slice(0, resultStrStart);\n  const searchValue = ambientStr === '' ? resultStr : ambientStr.slice(resultStrStart, resultStrEnd);\n  const textAfterSearchValue = ambientStr.slice(resultStrEnd);\n  return (\n    <button\n      role=\"cell\"\n      className={classNames({\n        'SearchResult': true,\n        'selected': currentResultIndex === activeResultIndex,\n        'modular-ui': customizableUI\n      })}\n      onClick={() => {\n        if (onSearchResultClick) {\n          onSearchResultClick(currentResultIndex, result, activeDocumentViewerKey);\n        }\n      }}\n      aria-current={currentResultIndex === activeResultIndex}\n    >\n      {textBeforeSearchValue}\n      <span className='search-value'>\n        {searchValue}\n      </span>\n      {textAfterSearchValue}\n    </button>\n  );\n}\nSearchResultListItem.propTypes = SearchResultListItemPropTypes;\n\nconst SearchResultPropTypes = {\n  width: PropTypes.number,\n  height: PropTypes.number,\n  activeResultIndex: PropTypes.number,\n  searchStatus: PropTypes.oneOf(['SEARCH_NOT_INITIATED', 'SEARCH_IN_PROGRESS', 'SEARCH_DONE']),\n  searchResults: PropTypes.arrayOf(PropTypes.object),\n  t: PropTypes.func.isRequired,\n  onClickResult: PropTypes.func,\n  pageLabels: PropTypes.arrayOf(PropTypes.any),\n  activeDocumentViewerKey: PropTypes.number\n};\n\nfunction SearchResult(props) {\n  const { height, searchStatus, searchResults, activeResultIndex, t, onClickResult, pageLabels, isProcessingSearchResults, isSearchInProgress, activeDocumentViewerKey } = props;\n  const cellMeasureCache = React.useMemo(() => {\n    return new CellMeasurerCache({ defaultHeight: 50, fixedWidth: true });\n  }, []);\n  const listRef = React.useRef(null);\n  const [listSize, setListSize] = React.useState(0);\n\n  if (searchResults.length === 0) {\n    // clear measure cache, when doing a new search\n    cellMeasureCache.clearAll();\n  }\n\n  if (searchResults.length && searchResults.length !== listSize) {\n    // If the search list is mutated in the backend, we\n    // need to clear cache and recalculate heights\n    setListSize(searchResults.length);\n    cellMeasureCache.clearAll();\n  }\n\n  const rowRenderer = React.useCallback(function rowRendererCallback(rendererOptions) {\n    const { index, key, parent, style } = rendererOptions;\n    const result = searchResults[index];\n    return (\n      <CellMeasurer\n        cache={cellMeasureCache}\n        columnIndex={0}\n        key={key}\n        parent={parent}\n        rowIndex={index}\n      >\n        {({ registerChild }) => (\n          <div role=\"row\" ref={registerChild} style={style}>\n            <SearchResultListSeparator\n              currentResultIndex={index}\n              searchResults={searchResults}\n              pageLabels={pageLabels}\n              t={t}\n            />\n            <SearchResultListItem\n              result={result}\n              currentResultIndex={index}\n              activeResultIndex={activeResultIndex}\n              onSearchResultClick={onClickResult}\n              activeDocumentViewerKey={activeDocumentViewerKey}\n            />\n          </div>\n        )}\n      </CellMeasurer>\n    );\n  }, [cellMeasureCache, searchResults, activeResultIndex, t, pageLabels]);\n\n  React.useEffect(() => {\n    if (listRef) {\n      listRef.current?.scrollToRow(activeResultIndex);\n    }\n  }, [activeResultIndex]);\n\n  if (height == null) { // eslint-disable-line eqeqeq\n    // VirtualizedList requires width and height of the component which is calculated by withContentRect HOC.\n    // On first render when HOC haven't yet set these values, both are undefined, thus having this check here\n    // and skip rendering if values are missing\n    return null;\n  }\n\n  if (searchStatus === 'SEARCH_DONE'\n    && searchResults.length === 0\n    && !isProcessingSearchResults) {\n    if (isSearchInProgress) {\n      return null;\n    }\n    return (\n      <div className=\"info\"><p className=\"no-margin\">{t('message.noResults')}</p></div>\n    );\n  }\n\n\n  return (\n    <VirtualizedList\n      width={200}\n      height={height}\n      tabIndex={-1}\n      overscanRowCount={10}\n      rowCount={searchResults.length}\n      deferredMeasurementCache={cellMeasureCache}\n      rowHeight={cellMeasureCache.rowHeight}\n      rowRenderer={rowRenderer}\n      ref={listRef}\n      scrollToIndex={activeResultIndex - 1}\n    />\n  );\n}\nSearchResult.propTypes = SearchResultPropTypes;\n\nfunction SearchResultWithContentRectHOC(props) {\n  const { measureRef, contentRect, ...rest } = props;\n  const { height } = contentRect.bounds;\n  return (\n    <div className=\"results\" ref={measureRef}>\n      <SearchResult height={height} {...rest} />\n    </div>\n  );\n}\nSearchResultWithContentRectHOC.propTypes = {\n  contentRect: PropTypes.object,\n  measureRef: PropTypes.oneOfType([\n    PropTypes.func,\n    PropTypes.shape({ current: PropTypes.any })\n  ])\n};\n\nconst SearchResultWithContentRectHOCAndBounds = withContentRect('bounds')(SearchResultWithContentRectHOC);\n\nconst SearchResultsContainer = (props) => {\n  return (<SearchResultWithContentRectHOCAndBounds {...props} />);\n};\nexport default SearchResultsContainer;\n", "import SearchResultsContainer from './SearchResult';\n\nexport default SearchResultsContainer;\n", "import React, { useEffect, useCallback, useRef, useState } from 'react';\nimport PropTypes from 'prop-types';\nimport core from 'core';\nimport { useTranslation } from 'react-i18next';\nimport debounce from 'lodash.debounce';\nimport throttle from 'lodash/throttle';\nimport { useSelector, useDispatch } from 'react-redux';\nimport actions from 'actions';\nimport selectors from 'selectors';\nimport classNames from 'classnames';\n\nimport Icon from 'components/Icon';\nimport Choice from '../Choice/Choice';\nimport Spinner from '../Spinner';\nimport { getInstanceNode } from 'helpers/getRootNode';\nimport { isOfficeEditorMode } from 'helpers/officeEditor';\nimport './SearchOverlay.scss';\nimport '../Button/Button.scss';\n\nconst propTypes = {\n  isPanelOpen: PropTypes.bool,\n  isSearchOverlayDisabled: PropTypes.bool,\n  searchValue: PropTypes.string,\n  searchStatus: PropTypes.oneOf(['SEARCH_NOT_INITIATED', 'SEARCH_IN_PROGRESS', 'SEARCH_DONE']),\n  isCaseSensitive: PropTypes.bool,\n  isWholeWord: PropTypes.bool,\n  isWildcard: PropTypes.bool,\n  searchResults: PropTypes.arrayOf(PropTypes.object),\n  activeResultIndex: PropTypes.number,\n  setSearchValue: PropTypes.func.isRequired,\n  setCaseSensitive: PropTypes.func.isRequired,\n  setSearchStatus: PropTypes.func.isRequired,\n  setWholeWord: PropTypes.func.isRequired,\n  setWildcard: PropTypes.func.isRequired,\n  executeSearch: PropTypes.func.isRequired,\n  selectNextResult: PropTypes.func,\n  selectPreviousResult: PropTypes.func,\n  isProcessingSearchResults: PropTypes.bool,\n  activeDocumentViewerKey: PropTypes.number,\n};\n\nfunction SearchOverlay(props) {\n  const { t } = useTranslation();\n  const { isSearchOverlayDisabled, searchResults, activeResultIndex, selectNextResult, selectPreviousResult, isProcessingSearchResults, activeDocumentViewerKey } = props;\n  const { searchValue, setSearchValue, executeSearch, replaceValue, nextResultValue, setReplaceValue } = props;\n  const { isCaseSensitive, setCaseSensitive, isWholeWord, setWholeWord, isWildcard, setWildcard, setSearchStatus, isSearchInProgress, setIsSearchInProgress } = props;\n  const { searchStatus, isPanelOpen } = props;\n  const [isReplaceBtnDisabled, setReplaceBtnDisabled] = useState(true);\n  const [isReplaceAllBtnDisabled, setReplaceAllBtnDisabled] = useState(true);\n  const [isMoreOptionsOpen, setMoreOptionOpen] = useState(true);\n  const [showReplaceSpinner, setShowReplaceSpinner] = useState(false);\n  const [isReplacementRegexValid, setReplacementRegexValid] = useState(true);\n  const [allowInitialSearch, setAllowInitialSearch] = useState(false);\n  const isSearchAndReplaceDisabled = useSelector((state) => selectors.isElementDisabled(state, 'searchAndReplace'));\n  const customizableUI = useSelector((state) => selectors.getFeatureFlags(state)?.customizableUI);\n  const searchTextInputRef = useRef();\n  const waitTime = 300; // Wait time in milliseconds\n\n  useEffect(() => {\n    try {\n      // eslint-disable-next-line no-unused-vars\n      const replacementRegex = new RegExp('(?<!<\\/?[^>]*|&[^;]*)');\n    } catch (error) {\n      setReplacementRegexValid(false);\n    }\n  }, []);\n\n  useEffect(() => {\n    if (numberOfResultsFound > 0) {\n      setSearchStatus('SEARCH_DONE');\n    }\n  }, [searchResults]);\n\n  useEffect(() => {\n    if (searchTextInputRef.current && isPanelOpen) {\n      // give time for the search panel to open before focusing on the input\n      setTimeout(() => {\n        searchTextInputRef.current.focus();\n        setAllowInitialSearch(true);\n      }, waitTime);\n    }\n\n    if (!isSearchAndReplaceDisabled && !isReplacementRegexValid && isPanelOpen) {\n      console.warn('Search and Replace is not supported in this browser');\n    }\n  }, [isPanelOpen, isCaseSensitive]);\n\n  useEffect(() => {\n    if (searchValue && searchValue.length > 0) {\n      if (allowInitialSearch) {\n        executeSearch(searchValue, {\n          caseSensitive: isCaseSensitive,\n          wholeWord: isWholeWord,\n          wildcard: isWildcard,\n        });\n      }\n    } else {\n      clearSearchResult();\n    }\n  }, [isCaseSensitive, isWholeWord, isWildcard, activeDocumentViewerKey]);\n\n  useEffect(() => {\n    core.addEventListener('pagesUpdated', onPagesUpdated);\n    return () => {\n      core.removeEventListener('pagesUpdated', onPagesUpdated);\n    };\n  });\n\n  const onPagesUpdated = () => {\n    search(searchValue);\n  };\n\n  const search = async (searchValue) => {\n    if (searchValue && searchValue.length > 1) {\n      setIsSearchInProgress(true);\n      setSearchStatus('SEARCH_IN_PROGRESS');\n\n      if (isOfficeEditorMode()) {\n        await core.getDocument().getOfficeEditor().updateSearchData();\n      }\n      executeSearch(searchValue, {\n        caseSensitive: isCaseSensitive,\n        wholeWord: isWholeWord,\n        wildcard: isWildcard,\n      });\n    } else if (!searchValue) {\n      clearSearchResult();\n    }\n  };\n\n  const debouncedSearch = useCallback(\n    debounce(search, waitTime),\n    [isCaseSensitive, isWholeWord, isWildcard]\n  );\n\n  const throttleSearch = useCallback(\n    throttle(search, waitTime),\n    [isCaseSensitive, isWholeWord, isWildcard]\n  );\n\n  useEffect(() => {\n    const onOfficeDocumentEdited = () => {\n      if (searchValue && searchValue.length > 0) {\n        throttleSearch(searchValue);\n      }\n    };\n\n    core.getDocument()?.addEventListener('officeDocumentEdited', onOfficeDocumentEdited);\n\n    return () => {\n      core.getDocument()?.removeEventListener('officeDocumentEdited', onOfficeDocumentEdited);\n    };\n  }, [searchValue]);\n\n  const textInputOnChange = (event) => {\n    setSearchValue(event.target.value);\n    debouncedSearch(event.target.value);\n\n    if (event.target.value && replaceValue) {\n      setReplaceBtnDisabled(false);\n      setReplaceAllBtnDisabled(false);\n    }\n  };\n\n  const replaceTextInputOnChange = (event) => {\n    setReplaceValue(event.target.value);\n    if (event.target.value && searchValue) {\n      setReplaceBtnDisabled(false);\n      setReplaceAllBtnDisabled(false);\n    }\n  };\n\n  function clearSearchResult() {\n    core.clearSearchResults();\n    setSearchValue('');\n    setSearchStatus('SEARCH_NOT_INITIATED');\n    setReplaceValue('');\n    setReplaceBtnDisabled(true);\n    setReplaceAllBtnDisabled(true);\n  }\n\n  const caseSensitiveSearchOptionOnChange = useCallback(\n    function caseSensitiveSearchOptionOnChangeCallback(event) {\n      const isChecked = event.target.checked;\n      setCaseSensitive(isChecked);\n    }, [],\n  );\n\n  const wholeWordSearchOptionOnChange = useCallback(\n    function wholeWordSearchOptionOnChangeCallback(event) {\n      const isChecked = event.target.checked;\n      setWholeWord(isChecked);\n    }, [],\n  );\n\n  const wildcardOptionOnChange = useCallback(\n    function wildcardOptionOnChangeCallback(event) {\n      const isChecked = event.target.checked;\n      setWildcard(isChecked);\n    }, [],\n  );\n\n  const nextButtonOnClick = useCallback(\n    function nextButtonOnClickCallback() {\n      if (selectNextResult) {\n        selectNextResult(searchResults, activeResultIndex);\n      }\n    },\n    [selectNextResult, searchResults, activeResultIndex],\n  );\n\n  const previousButtonOnClick = useCallback(\n    function previousButtonOnClickCallback() {\n      if (selectPreviousResult) {\n        selectPreviousResult(searchResults, activeResultIndex);\n      }\n    },\n    [selectPreviousResult, searchResults, activeResultIndex],\n  );\n\n  const searchAndReplaceAll = useCallback(\n    async function searchAndReplaceAllCallback() {\n      if (isReplaceAllBtnDisabled && nextResultValue) {\n        return;\n      }\n      setShowReplaceSpinner(true);\n      await getInstanceNode().instance.Core.ContentEdit.searchAndReplaceText({\n        documentViewer: getInstanceNode().instance.Core.documentViewer,\n        searchResults: core.getPageSearchResults(),\n        replaceWith: replaceValue,\n      });\n      setShowReplaceSpinner(false);\n    },\n    [replaceValue]\n  );\n\n  const toggleMoreOptionsBtn = () => {\n    window.localStorage.setItem('searchMoreOption', !isMoreOptionsOpen);\n    setMoreOptionOpen(!isMoreOptionsOpen);\n  };\n\n  const searchAndReplaceOne = useCallback(\n    async function searchAndReplaceOneCallback() {\n      if (isReplaceBtnDisabled && nextResultValue) {\n        return;\n      }\n      setShowReplaceSpinner(true);\n\n      await getInstanceNode().instance.Core.ContentEdit.searchAndReplaceText({\n        documentViewer: getInstanceNode().instance.Core.documentViewer,\n        replaceWith: replaceValue,\n        searchResults: [core.getActiveSearchResult()],\n      });\n\n      setShowReplaceSpinner(false);\n    },\n    [replaceValue, nextResultValue, isReplaceBtnDisabled]\n  );\n\n  const dispatch = useDispatch();\n\n  const replaceAllConfirmationWarning = () => {\n    const title = t('option.searchPanel.replaceText');\n    const message = t('option.searchPanel.confirmMessageReplaceAll');\n    const confirmationWarning = {\n      message,\n      title,\n      confirmBtnText: t('option.searchPanel.confirm'),\n      onConfirm: () => {\n        searchAndReplaceAll();\n      },\n    };\n    dispatch(actions.showWarningMessage(confirmationWarning));\n  };\n\n  const replaceOneConfirmationWarning = () => {\n    const title = t('option.searchPanel.replaceText');\n    const message = t('option.searchPanel.confirmMessageReplaceOne');\n    const confirmationWarning = {\n      message,\n      title,\n      confirmBtnText: t('option.searchPanel.confirm'),\n      onConfirm: () => {\n        searchAndReplaceOne();\n      },\n    };\n    dispatch(actions.showWarningMessage(confirmationWarning));\n  };\n\n  if (isSearchOverlayDisabled) {\n    return null;\n  }\n  const numberOfResultsFound = searchResults ? searchResults.length : 0;\n\n  const isSearchDoneAndNotProcessingResults = searchStatus === 'SEARCH_DONE' && !isProcessingSearchResults;\n  const showSpinner = (!isSearchDoneAndNotProcessingResults || isSearchInProgress) ? <Spinner /> : null;\n\n  const searchOptionsComponents = (<div className=\"options\">\n    <Choice\n      dataElement=\"caseSensitiveSearchOption\"\n      id=\"case-sensitive-option\"\n      checked={isCaseSensitive}\n      onChange={caseSensitiveSearchOptionOnChange}\n      label={t('option.searchPanel.caseSensitive')}\n      tabIndex={isPanelOpen ? 0 : -1}\n    />\n    <Choice\n      dataElement=\"wholeWordSearchOption\"\n      id=\"whole-word-option\"\n      checked={isWholeWord}\n      onChange={wholeWordSearchOptionOnChange}\n      label={t('option.searchPanel.wholeWordOnly')}\n      tabIndex={isPanelOpen ? 0 : -1}\n    />\n    <Choice\n      dataElement=\"wildCardSearchOption\"\n      id=\"wild-card-option\"\n      checked={isWildcard}\n      onChange={wildcardOptionOnChange}\n      label={t('option.searchPanel.wildcard')}\n      tabIndex={isPanelOpen ? 0 : -1}\n    />\n  </div>);\n\n  return (\n    <div className={classNames({\n      'SearchOverlay': true,\n      'modular-ui': customizableUI\n    })}>\n      <div className='input-container'>\n        {customizableUI && <Icon glyph=\"icon-header-search\" />}\n        <input\n          className='search-panel-input'\n          ref={searchTextInputRef}\n          type=\"text\"\n          autoComplete=\"off\"\n          onChange={textInputOnChange}\n          value={searchValue}\n          placeholder={customizableUI ? '' : t('message.searchDocumentPlaceholder')}\n          aria-label={t('message.searchDocumentPlaceholder')}\n          id=\"SearchPanel__input\"\n          tabIndex={isPanelOpen ? 0 : -1}\n        />\n        {(searchValue !== undefined) && searchValue.length > 0 && (\n          <button\n            className=\"clearSearch-button\"\n            onClick={clearSearchResult}\n            aria-label={t('message.searchDocumentPlaceholder')}\n          >\n            <Icon glyph=\"icon-close\" />\n          </button>\n        )\n        }\n      </div>\n      {\n        (isSearchAndReplaceDisabled || !isReplacementRegexValid) ? null :\n          (isMoreOptionsOpen)\n            ? <div className=\"extra-options\">\n              <button className='Button' onClick={toggleMoreOptionsBtn}>{t('option.searchPanel.lessOptions')} <Icon glyph=\"icon-chevron-up\" /></button>\n            </div>\n            : <div className=\"extra-options\">\n              <button className='Button' onClick={toggleMoreOptionsBtn}>{t('option.searchPanel.moreOptions')} <Icon glyph=\"icon-chevron-down\" /></button>\n            </div>\n      }\n      {\n        (!isMoreOptionsOpen) ? searchOptionsComponents :\n          <div>\n            {searchOptionsComponents}\n            {\n              (isSearchAndReplaceDisabled || !isReplacementRegexValid) ? null :\n                <div data-element=\"searchAndReplace\" className='replace-options'>\n                  <p className=\"search-and-replace-title\">{t('option.searchPanel.replace')}</p>\n                  <div className='input-container'>\n                    <input type={'text'}\n                      aria-label={t('option.searchPanel.replace')}\n                      onChange={replaceTextInputOnChange}\n                      value={replaceValue}\n                    />\n                  </div>\n                  <div className='replace-buttons'>\n                    {(showReplaceSpinner) ? <Spinner width={25} height={25} /> : null}\n                    <button className='Button btn-replace-all' disabled={isReplaceAllBtnDisabled}\n                      onClick={replaceAllConfirmationWarning}>{t('option.searchPanel.replaceAll')}</button>\n                    <button className='Button btn-replace' disabled={isReplaceBtnDisabled || !nextResultValue || !core.getActiveSearchResult()}\n                      onClick={replaceOneConfirmationWarning}>{t('option.searchPanel.replace')}</button>\n                  </div>\n                </div>\n            }\n          </div>\n      }\n\n      <div className=\"divider\" />\n      <div className=\"footer\">\n        {searchStatus === 'SEARCH_NOT_INITIATED' || '' ? null : showSpinner}\n        <p className=\"no-margin\" aria-live=\"assertive\">{isSearchDoneAndNotProcessingResults && !isSearchInProgress ? `${numberOfResultsFound} ${t('message.numResultsFound')}` : undefined}</p>\n        {numberOfResultsFound > 0 && (\n          <div className=\"buttons\">\n            <button className=\"button\" onClick={previousButtonOnClick} aria-label={t('action.prevResult')}>\n              <Icon className=\"arrow\" glyph=\"icon-chevron-left\" />\n            </button>\n            <button className=\"button\" onClick={nextButtonOnClick} aria-label={t('action.nextResult')}>\n              <Icon className=\"arrow\" glyph=\"icon-chevron-right\" />\n            </button>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n\nSearchOverlay.propTypes = propTypes;\n\nexport default SearchOverlay;\n", "import React from 'react';\nimport { useDispatch, useStore } from 'react-redux';\nimport SearchOverlay from './SearchOverlay';\nimport { getOverrideSearchExecution } from 'helpers/search';\nimport searchTextFullFactory from '../../apis/searchTextFull';\nimport core from 'core';\nimport actions from 'actions';\n\n// exported so that we can test these internal functions\nexport function executeSearch(searchValue, options, store) {\n  const searchOptions = {\n    regex: false,\n    ...options,\n  };\n\n  if (searchValue !== null && searchValue !== undefined) {\n    // user can override search execution with instance.overrideSearchExecution()\n    // Here we check if user has done that and call that rather than default search execution\n    const overrideSearchExecution = getOverrideSearchExecution();\n    if (overrideSearchExecution) {\n      overrideSearchExecution(searchValue, searchOptions);\n    } else {\n      const searchTextFull = searchTextFullFactory(store);\n      searchTextFull(searchValue, searchOptions, false);\n    }\n  }\n}\n\nexport function selectNextResult(searchResults = [], activeResultIndex, dispatch) {\n  if (searchResults.length > 0) {\n    const nextResultIndex = activeResultIndex === searchResults.length - 1 ? 0 : activeResultIndex + 1;\n    core.setActiveSearchResult(searchResults[nextResultIndex]);\n    if (dispatch) {\n      const nextIndex = (nextResultIndex > 0) ? nextResultIndex - 1 : 0;\n      dispatch(actions.setNextResultValue(searchResults[nextResultIndex], nextIndex));\n    }\n  }\n}\n\nexport function selectPreviousResult(searchResults = [], activeResultIndex, dispatch) {\n  if (searchResults.length > 0) {\n    const prevResultIndex = activeResultIndex <= 0 ? searchResults.length - 1 : activeResultIndex - 1;\n    core.setActiveSearchResult(searchResults[prevResultIndex]);\n    if (dispatch) {\n      dispatch(actions.setNextResultValue(searchResults[prevResultIndex]));\n    }\n  }\n}\n\nfunction SearchOverlayContainer(props) {\n  const dispatch = useDispatch();\n  const store = useStore();\n  return (\n    <SearchOverlay\n      executeSearch={(searchValue, options = {}) => {\n        executeSearch(searchValue, options, store);\n      }}\n      selectNextResult={(searchResults = [], activeResultIndex) => {\n        selectNextResult(searchResults, activeResultIndex, dispatch);\n      }}\n      selectPreviousResult={(searchResults = [], activeResultIndex) => {\n        selectPreviousResult(searchResults, activeResultIndex, dispatch);\n      }}\n      {...props}\n    />\n  );\n}\n\nexport default SearchOverlayContainer;\n", "import selectors from 'selectors';\nimport actions from 'actions';\nimport { connect } from 'react-redux';\nimport React from 'react';\nimport SearchOverlayContainer from './SearchOverlayContainer';\nimport DataElements from 'src/constants/dataElement';\n\nconst mapStateToProps = (state) => ({\n  isSearchOverlayDisabled: selectors.isElementDisabled(state, DataElements.SEARCH_OVERLAY),\n  searchValue: selectors.getSearchValue(state),\n  replaceValue: selectors.getReplaceValue(state),\n  nextResultValue: selectors.getNextResultValue(state),\n  isCaseSensitive: selectors.isCaseSensitive(state),\n  isWholeWord: selectors.isWholeWord(state),\n  isWildcard: selectors.isWildcard(state),\n  isProcessingSearchResults: selectors.isProcessingSearchResults(state),\n});\n\nconst mapDispatchToProps = {\n  closeElements: actions.closeElements,\n  setSearchValue: actions.setSearchValue,\n  setReplaceValue: actions.setReplaceValue,\n  setCaseSensitive: actions.setCaseSensitive,\n  setWholeWord: actions.setWholeWord,\n  setWildcard: actions.setWildcard,\n};\n\nfunction SearchOverlayRedux(props) {\n  return (<SearchOverlayContainer {...props} />);\n}\n\nconst ConnectedSearchOverlayRedux = connect(\n  mapStateToProps,\n  mapDispatchToProps,\n)(SearchOverlayRedux);\n\nconst connectedComponent = (props) => {\n  return <ConnectedSearchOverlayRedux {...props} />;\n};\n\nexport default connectedComponent;", "import connectedComponent from './SearchOverlayRedux';\n\nexport default connectedComponent;\n", "import React from 'react';\nimport core from 'core';\nimport { useDispatch } from 'react-redux';\nimport actions from 'actions/index';\n\nfunction useSearch(activeDocumentViewerKey) {\n  const [searchResults, setSearchResults] = React.useState([]);\n  const [activeSearchResult, setActiveSearchResult] = React.useState();\n  const [activeSearchResultIndex, setActiveSearchResultIndex] = React.useState(0);\n  const [searchStatus, setSearchStatus] = React.useState('SEARCH_NOT_INITIATED');\n  const dispatch = useDispatch();\n  const documentViewers = core.getDocumentViewers();\n  const documentViewersCount = documentViewers.length;\n\n\n  React.useEffect(() => {\n    // First time useSearch is mounted we check if core has results\n    // and if it has, we make sure those are set. This will make sure if external search is done\n    // that the result will reflect on the UI those set in core\n    const activeDocumentViewer = core.getDocumentViewer(activeDocumentViewerKey);\n    const coreSearchResults = activeDocumentViewer.getPageSearchResults() || [];\n    if (coreSearchResults.length > 0) {\n      const activeSearchResult = core.getActiveSearchResult();\n      if (activeSearchResult) {\n        const newActiveSearchResultIndex = coreSearchResults.findIndex((searchResult) => {\n          return core.isSearchResultEqual(searchResult, activeSearchResult);\n        });\n        setSearchResults(coreSearchResults);\n        if (newActiveSearchResultIndex >= 0) {\n          setActiveSearchResult(coreSearchResults[newActiveSearchResultIndex]);\n          setActiveSearchResultIndex(newActiveSearchResultIndex);\n        }\n      } else {\n        setSearchResults(coreSearchResults);\n        setActiveSearchResult(coreSearchResults[0]);\n        setActiveSearchResultIndex(0);\n      }\n    }\n  }, []);\n\n  React.useEffect(() => {\n    const activeDocumentViewer = core.getDocumentViewer(activeDocumentViewerKey);\n    function activeSearchResultChanged(newActiveSearchResult) {\n      const coreSearchResults = activeDocumentViewer.getPageSearchResults() || [];\n      const newActiveSearchResultIndex = coreSearchResults.findIndex((searchResult) => {\n        return core.isSearchResultEqual(searchResult, newActiveSearchResult);\n      });\n      if (newActiveSearchResultIndex >= 0) {\n        setActiveSearchResult(newActiveSearchResult);\n        setActiveSearchResultIndex(newActiveSearchResultIndex);\n      }\n    }\n\n    function searchResultsChanged(newSearchResults = []) {\n      setSearchResults(newSearchResults);\n      if (newSearchResults && newSearchResults.length === 0) {\n        setActiveSearchResult(undefined);\n        setActiveSearchResultIndex(-1);\n      }\n    }\n\n    function searchInProgressEventHandler(isSearching) {\n      if (isSearching === undefined || isSearching === null) {\n        // if isSearching is not passed at all, we consider that to mean that search was reset to original state\n        setSearchStatus('SEARCH_NOT_INITIATED');\n      } else if (isSearching) {\n        setSearchStatus('SEARCH_IN_PROGRESS');\n      } else {\n        const defaultActiveSearchResult = activeDocumentViewer.getActiveSearchResult();\n\n        if (defaultActiveSearchResult) {\n          setActiveSearchResult(defaultActiveSearchResult);\n          // In core default active search result is the first result\n          const coreSearchResults = activeDocumentViewer.getPageSearchResults() || [];\n          const newActiveSearchResultIndex = coreSearchResults.findIndex((searchResult) => {\n            return core.isSearchResultEqual(searchResult, defaultActiveSearchResult);\n          });\n          setActiveSearchResultIndex(newActiveSearchResultIndex);\n          dispatch(actions.setNextResultValue(defaultActiveSearchResult));\n        }\n\n        setSearchStatus('SEARCH_DONE');\n      }\n    }\n    const documentViewers = core.getDocumentViewers();\n\n    documentViewers.forEach((documentViewer) => {\n      documentViewer.addEventListener('activeSearchResultChanged', activeSearchResultChanged);\n      documentViewer.addEventListener('searchResultsChanged', searchResultsChanged);\n      documentViewer.addEventListener('searchInProgress', searchInProgressEventHandler);\n    });\n    return () => {\n      documentViewers.forEach((documentViewer) => {\n        documentViewer.removeEventListener('activeSearchResultChanged', activeSearchResultChanged);\n        documentViewer.removeEventListener('searchResultsChanged', searchResultsChanged);\n        documentViewer.removeEventListener('searchInProgress', searchInProgressEventHandler);\n      });\n    };\n  }, [setActiveSearchResult, setActiveSearchResultIndex, setSearchStatus, dispatch, documentViewersCount, activeDocumentViewerKey]);\n\n  return {\n    searchStatus,\n    searchResults,\n    activeSearchResult,\n    activeSearchResultIndex,\n    setSearchStatus,\n  };\n}\n\nexport default useSearch;\n", "import React from 'react';\nimport PropTypes from 'prop-types';\nimport { useTranslation } from 'react-i18next';\nimport SearchResult from 'components/SearchResult';\nimport SearchOverlay from 'components/SearchOverlay';\nimport Icon from 'components/Icon';\nimport getClassName from 'helpers/getClassName';\nimport DataElementWrapper from 'components/DataElementWrapper';\nimport { addSearchListener, removeSearchListener } from 'helpers/search';\n\nimport './SearchPanel.scss';\nimport useSearch from 'hooks/useSearch';\n\nconst propTypes = {\n  isOpen: PropTypes.bool,\n  isMobile: PropTypes.bool,\n  pageLabels: PropTypes.array,\n  currentWidth: PropTypes.number,\n  closeSearchPanel: PropTypes.func,\n  setActiveResult: PropTypes.func,\n  isInDesktopOnlyMode: PropTypes.bool,\n  isProcessingSearchResults: PropTypes.bool,\n  activeDocumentViewerKey: PropTypes.number,\n  isCustomPanel: PropTypes.bool,\n};\n\nfunction noop() { }\n\nfunction SearchPanel(props) {\n  const {\n    isOpen,\n    currentWidth,\n    pageLabels,\n    closeSearchPanel = noop,\n    setActiveResult = noop,\n    setNextResultValue = noop,\n    isMobile = false,\n    isInDesktopOnlyMode,\n    isProcessingSearchResults,\n    activeDocumentViewerKey,\n    dataElement = 'searchPanel',\n    isCustomPanel = false,\n  } = props;\n\n  const { t } = useTranslation();\n  const { searchStatus, searchResults, activeSearchResultIndex, setSearchStatus } = useSearch(activeDocumentViewerKey);\n\n  const onCloseButtonClick = React.useCallback(function onCloseButtonClick() {\n    if (closeSearchPanel) {\n      closeSearchPanel();\n    }\n  }, [closeSearchPanel]);\n\n  const onClickResult = React.useCallback(function onClickResult(resultIndex, result, activeDocumentViewerKey) {\n    setActiveResult(result, activeDocumentViewerKey);\n    if (!isInDesktopOnlyMode && isMobile) {\n      closeSearchPanel();\n    }\n\n    setNextResultValue(result);\n  }, [closeSearchPanel, isMobile]);\n\n  const [isSearchInProgress, setIsSearchInProgress] = React.useState(false);\n\n  const searchEventListener = () => {\n    setIsSearchInProgress(false);\n  };\n\n  React.useEffect(() => {\n    // componentDidMount\n    addSearchListener(searchEventListener);\n  }, []);\n\n  React.useEffect(() => {\n    // componentWillUnmount\n    return () => {\n      removeSearchListener(searchEventListener);\n    };\n  }, []);\n\n  const className = getClassName('Panel SearchPanel', { isOpen });\n  let style = {};\n  if (!isCustomPanel && (isInDesktopOnlyMode || !isMobile)) {\n    style = { width: `${currentWidth}px`, minWidth: `${currentWidth}px` };\n  }\n\n  return (\n    <DataElementWrapper\n      className={className}\n      dataElement={dataElement}\n      style={style}\n    >\n      {!isInDesktopOnlyMode && isMobile &&\n        <div\n          className=\"close-container\"\n        >\n          <button\n            className=\"close-icon-container\"\n            onClick={onCloseButtonClick}\n          >\n            <Icon\n              glyph=\"ic_close_black_24px\"\n              className=\"close-icon\"\n            />\n          </button>\n        </div>}\n      <SearchOverlay\n        searchStatus={searchStatus}\n        setSearchStatus={setSearchStatus}\n        searchResults={searchResults}\n        activeResultIndex={activeSearchResultIndex}\n        isPanelOpen={isOpen}\n        isSearchInProgress={isSearchInProgress}\n        setIsSearchInProgress={setIsSearchInProgress}\n        activeDocumentViewerKey={activeDocumentViewerKey}\n      />\n      <SearchResult\n        t={t}\n        searchStatus={searchStatus}\n        searchResults={searchResults}\n        activeResultIndex={activeSearchResultIndex}\n        onClickResult={onClickResult}\n        pageLabels={pageLabels}\n        isProcessingSearchResults={isProcessingSearchResults}\n        isSearchInProgress={isSearchInProgress}\n        activeDocumentViewerKey={activeDocumentViewerKey}\n      />\n    </DataElementWrapper>\n  );\n}\n\nSearchPanel.propTypes = propTypes;\n\nexport default SearchPanel;\n", "import React from 'react';\nimport { useSelector, useDispatch, shallowEqual } from 'react-redux';\nimport core from 'core';\nimport actions from 'actions';\nimport selectors from 'selectors';\nimport { isMobileSize } from 'helpers/getDeviceSize';\nimport SearchPanel from './SearchPanel';\nimport DataElements from 'constants/dataElement';\nimport { RESIZE_BAR_WIDTH } from 'constants/panel';\n\nfunction SearchPanelContainer(props) {\n  const { dataElement = DataElements.SEARCH_PANEL, parentDataElement = undefined } = props;\n  const isMobile = isMobileSize();\n\n  const [\n    isOpen,\n    pageLabels,\n    shouldClearSearchPanelOnClose,\n    isInDesktopOnlyMode,\n    isProcessingSearchResults,\n    activeDocumentViewerKey,\n  ] = useSelector(\n    (state) => [\n      selectors.isElementOpen(state, dataElement),\n      selectors.getPageLabels(state),\n      selectors.shouldClearSearchPanelOnClose(state),\n      selectors.isInDesktopOnlyMode(state),\n      selectors.isProcessingSearchResults(state),\n      selectors.getActiveDocumentViewerKey(state),\n    ],\n    shallowEqual,\n  );\n  let currentWidth = useSelector((state) => (\n    !parentDataElement && dataElement === DataElements.SEARCH_PANEL ?\n      selectors.getSearchPanelWidth(state) :\n      selectors.getPanelWidth(state, parentDataElement || dataElement)\n  ));\n\n  const dispatch = useDispatch();\n  const closeSearchPanel = React.useCallback(\n    function closeSearchPanel() {\n      dispatch(actions.closeElements([dataElement]));\n    },\n    [dispatch],\n  );\n\n  const clearSearchInputValue = React.useCallback(\n    function clearSearchInputValue() {\n      dispatch(actions.setSearchValue(''));\n    },\n    [dispatch],\n  );\n\n  const setNextResultValue = React.useCallback(\n    function setNextResultValue(searchResults) {\n      dispatch(actions.setNextResultValue(searchResults));\n    },\n    [dispatch],\n  );\n\n  const setActiveResult = React.useCallback(function setActiveResult(result, activeDocumentViewerKey) {\n    if (activeDocumentViewerKey) {\n      const activeDocumentviewer = core.getDocumentViewer(activeDocumentViewerKey);\n      return activeDocumentviewer.setActiveSearchResult(result);\n    }\n  }, []);\n\n  /*\n  React.useEffect(function SearchPanelVisibilityChangedEffect() {\n    function clearSearchResultsOnPanelClose(event) {\n      if (!event && !event.detail) {\n        return;\n      }\n      const { detail } = event;\n      if (detail.element === 'searchPanel' && detail.isVisible === false) {\n        // clear search results when search panel is closed\n        core.clearSearchResults();\n        clearSearchInputValue();\n      }\n    }\n    if (isMobile) {\n      // for mobile we want to keep results in panel as search panel is on top of the content\n      // and user will need to close the panel to view the content.\n      return;\n    }\n    if (!shouldClearSearchPanelOnClose) {\n      return;\n    }\n    window.addEventListener('visibilityChanged',  clearSearchResultsOnPanelClose);\n    return function SearchPanelVisibilityChangedEffectCleanUp() {\n      window.removeEventListener('visibilityChanged',  clearSearchResultsOnPanelClose);\n    };\n  }, [isMobile, clearSearchInputValue, shouldClearSearchPanelOnClose]);\n   */\n\n  React.useEffect(\n    function clearSearchResult() {\n      if (!isInDesktopOnlyMode && isMobile) {\n        // for mobile we want to keep results in panel as search panel is on top of the content\n        // and user will need to close the panel to view the content.\n        return;\n      }\n\n      if (!isOpen && shouldClearSearchPanelOnClose) {\n        core.clearSearchResults();\n        clearSearchInputValue();\n      }\n    },\n    [isMobile, isOpen, shouldClearSearchPanelOnClose, isInDesktopOnlyMode],\n  );\n\n  React.useEffect(() => {\n    return () => {\n      if (!isInDesktopOnlyMode && isMobile) {\n        // for mobile we want to keep results in panel as search panel is on top of the content\n        // and user will need to close the panel to view the content.\n        return;\n      }\n\n      if (shouldClearSearchPanelOnClose) {\n        core.clearSearchResults();\n        clearSearchInputValue();\n      }\n    };\n  }, [isMobile, shouldClearSearchPanelOnClose, isInDesktopOnlyMode]);\n\n  if (dataElement !== DataElements.SEARCH_PANEL) {\n    // Adjust width for custom panels\n    currentWidth -= 16; // padding\n    currentWidth -= RESIZE_BAR_WIDTH;\n  }\n\n  const combinedProps = {\n    ...props,\n    isOpen,\n    currentWidth,\n    pageLabels,\n    closeSearchPanel,\n    setActiveResult,\n    setNextResultValue,\n    isMobile,\n    isInDesktopOnlyMode,\n    isProcessingSearchResults,\n    activeDocumentViewerKey,\n  };\n\n  return <SearchPanel {...combinedProps} />;\n}\n\nexport default SearchPanelContainer;\n", "import SearchPanelContainer from './SearchPanelContainer';\n\nexport default SearchPanelContainer;\n"], "sourceRoot": ""}