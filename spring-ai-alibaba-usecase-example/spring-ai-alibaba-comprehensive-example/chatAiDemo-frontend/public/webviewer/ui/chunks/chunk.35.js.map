{"version": 3, "sources": ["webpack:///./src/ui/src/components/SignaturePanel/Spinner/Spinner.scss?1db5", "webpack:///./src/ui/src/components/SignaturePanel/Spinner/Spinner.scss", "webpack:///./src/ui/src/components/SignaturePanel/WidgetInfo/WidgetInfo.scss?1717", "webpack:///./src/ui/src/components/SignaturePanel/WidgetInfo/WidgetInfo.scss", "webpack:///./src/ui/src/components/SignaturePanel/SignaturePanel.scss?b9c4", "webpack:///./src/ui/src/components/SignaturePanel/SignaturePanel.scss", "webpack:///./src/ui/src/helpers/setVerificationResult.js", "webpack:///./src/ui/src/components/SignaturePanel/Spinner/index.js", "webpack:///./src/ui/src/components/SignaturePanel/WidgetLocator/WidgetLocator.js", "webpack:///./src/ui/src/components/SignaturePanel/WidgetLocator/index.js", "webpack:///./src/ui/src/components/SignaturePanel/WidgetInfo/WidgetInfo.js", "webpack:///./src/ui/src/components/SignaturePanel/WidgetInfo/index.js", "webpack:///./src/ui/src/components/SignaturePanel/SignaturePanel.js", "webpack:///./src/ui/src/components/SignaturePanel/index.js", "webpack:///./src/ui/src/components/SignaturePanel/SignatureIcon/SignatureIcon.scss?795b", "webpack:///./src/ui/src/components/SignaturePanel/SignatureIcon/SignatureIcon.scss", "webpack:///./src/ui/src/components/SignatureValidationModal/SignatureValidationModal.scss?5bd1", "webpack:///./src/ui/src/components/SignatureValidationModal/SignatureValidationModal.scss", "webpack:///./src/ui/src/components/SignaturePanel/SignatureIcon/SignatureIcon.js", "webpack:///./src/ui/src/components/SignaturePanel/SignatureIcon/index.js", "webpack:///./src/ui/src/components/SignatureValidationModal/SignatureValidationModal.js", "webpack:///./src/ui/src/components/SignatureValidationModal/index.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "doc", "certificates", "trustLists", "currentLanguage", "revocationChecking", "revocationProxyPrefix", "dispatch", "getVerificationResult", "verificationResult", "actions", "setVerificationResult", "PDFNet", "Core", "VerificationResult", "TrustStatus", "DigestStatus", "ModificationPermissionsStatus", "DocumentStatus", "verificationResults", "runWithCleanup", "getPDFDoc", "VerificationOptions", "create", "SecurityLevel", "e_compatibility_and_archiving", "opts", "enableOnlineCRLRevocationChecking", "setRevocationProxyPrefix", "undefined", "certificate", "addTrustedCertificateFromURL", "console", "error", "File", "Object", "prototype", "toString", "call", "fileReader", "FileReader", "arrayBufferPromise", "Promise", "resolve", "reject", "addEventListener", "e", "Uint8Array", "target", "result", "readAsA<PERSON>y<PERSON><PERSON>er", "addTrustedCertificate", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Int8Array", "Uint8ClampedArray", "trustList", "trustListDataStructure", "constructor", "name", "supportedDataStructures", "fdfDocBuffer", "arrayBuffer", "includes", "join", "FDFDoc", "createFromMemoryBuffer", "fdf", "loadTrustList", "getFieldIteratorBegin", "fieldIterator", "hasNext", "current", "field", "<PERSON><PERSON><PERSON><PERSON>", "getType", "Field", "Type", "e_signature", "DigitalSignatureField", "createFromField", "digitalSigField", "verify", "getSDFObj", "getObjNum", "id", "signer", "signTime", "documentPermission", "isCertification", "contactInfo", "location", "reason", "validAtTimeOfSigning", "signer<PERSON><PERSON>", "issuerField", "subjectField", "hasCryptographicSignature", "signed", "getSubFilter", "subFilter", "SubFilterType", "e_adbe_pkcs7_detached", "getSignerCertFromCMS", "signer<PERSON><PERSON>", "getSubjectField", "retrievedSubjectField", "processX501DistinguishedName", "processedSubjectField", "e_ETSI_RFC3161", "getSignatureName", "getContactInfo", "getSigningTime", "formatPDFNetDate", "getLocation", "getReason", "getDocumentPermissions", "getVerificationStatus", "verificationStatus", "getDocumentStatus", "documentStatus", "getDigestStatus", "digestStatus", "getTrustStatus", "trustStatus", "getPermissionsStatus", "permissionStatus", "getDigestAlgorithm", "digestAlgorithm", "getDisallowed<PERSON><PERSON>es", "map", "change", "getTypeAsString", "objnum", "type", "all", "disallowedChanges", "validSignerIdentity", "e_trust_verified", "trustVerificationResultString", "trustVerificationResultBoolean", "timeOfTrustVerificationEnum", "trustVerificationTime", "hasTrustVerificationResult", "getTrustVerificationResult", "trustVerificationResult", "wasSuccessful", "getResultString", "getTimeOfTrustVerificationEnum", "getTimeOfTrustVerification", "epochTrustVerificationTime", "formatDate", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "certPath", "firstX509Cert", "getIssuerField", "retrievedIssuerField", "processedIssuerField", "assign", "lastX509Cert", "getNotBeforeEpochTime", "notBeforeEpochTime", "getNotAfterEpochTime", "notAfterEpochTime", "warn", "badgeIcon", "e_no_error", "e_digest_verified", "e_digest_verification_disabled", "e_no_trust_status", "e_unmodified", "e_has_allowed_changes", "e_permissions_verification_disabled", "e_commonName", "getName", "fieldName", "next", "date", "year", "month", "day", "hour", "minute", "second", "Date", "UTC", "toLocaleDateString", "replace", "weekday", "timeZoneName", "epochTime", "setUTCSeconds", "x501DistinguishedNameObject", "processedObject", "getAllAttributesAndValues", "allAttributeAndValues", "x501AttributeTypeAndValue", "getAttributeTypeOID", "objectIdentifier", "getRawValue", "key", "getStringValue", "value", "translateObjectIdentifierBotanOID", "objectIdentifierOIDenum", "JSON", "stringify", "Spinner", "className", "WidgetLocator", "rect", "useState", "show", "setShow", "useEffect", "scrollViewContainer", "core", "getScrollViewElement", "handleScroll", "removeEventListener", "setTimeout", "ReactDOM", "createPortal", "style", "position", "top", "y1", "left", "x1", "width", "x2", "height", "y2", "border", "zIndex", "getRootNode", "querySelector", "renderPermissionStatus", "translate", "e_invalidated_by_disallowed_changes", "e_no_permissions_status", "propTypes", "PropTypes", "string", "isRequired", "onClick", "func", "instanceOf", "Annotations", "Forms", "WidgetInfo", "verificationType", "useSelector", "state", "selectors", "locatorRect", "setLocatorRect", "signatureDetailsExpanded", "setSignatureDetailsExpanded", "TimeMode", "useTranslation", "useDispatch", "titleInteraction", "widgets", "widget", "jumpToAnnotation", "scrollLeft", "scrollTop", "getRect", "windowTopLeft", "getDisplayModeObject", "pageToWindow", "x", "y", "PageNumber", "windowBottomRight", "jumpToWidget", "openSignatureModalWithFocus", "useFocusHandler", "setSignatureValidationModalWidgetName", "openElement", "Fragment", "PanelListItem", "labelHeader", "iconGlyph", "useI18String", "onKeyDown", "tabIndex", "verificationTimeMessage", "e_current", "e_signing", "e_timestamp", "renderTrustVerification", "data-element", "aria-label", "<PERSON><PERSON>", "img", "classNames", "arrow", "expanded", "role", "ariaExpanded", "isActive", "aria<PERSON><PERSON><PERSON>", "SignaturePanel", "fields", "setFields", "showSpinner", "setShowSpinner", "certificateErrorMessage", "setCertificateErrorMessage", "getDocument", "setDocument", "isElementDisabled", "getCertificates", "getTrustLists", "getCurrentLanguage", "getIsRevocationCheckingEnabled", "getRevocationProxyPrefix", "isDisabled", "onDocumentLoaded", "onDocumentUnloaded", "useCallback", "onAnnotationChanged", "annotations", "action", "isInFormCreationMode", "getAnnotationManager", "getFormFieldCreationManager", "isInFormFieldCreationMode", "addSignatureWidgetAnnotations", "getAnnotationsList", "removeAnnotations", "addNonSignedFields", "currentAnnotations", "<PERSON><PERSON><PERSON>s", "annotation", "SignatureWidgetAnnotation", "getField", "newSet", "Set", "removeMatchingWidget", "WidgetAnnotation", "annotationManager", "widgetToDelete", "filter", "annotationToFilter", "getCustomData", "Id", "deleteAnnotations", "resetFields", "getAnnotationsLoadedPromise", "then", "fieldManager", "getFieldManager", "keys", "message", "Icon", "glyph", "panelData", "panelNames", "SIGNATURE", "icon", "renderLoadingOrErrors", "index", "collapsible", "badge", "size", "SignatureIcon", "SignatureValidationModal", "validationModalWidgetName", "digitalSignatureValidation", "isElementOpen", "DataElements", "SIGNATURE_VALIDATION_MODAL", "shallowEqual", "isOpen", "closeModal", "useFocusOnClose", "closeElements", "SIGNATURE_MODAL", "LOADING_MODAL", "PRINT_MODAL", "ERROR_MODAL", "PASSWORD_MODAL", "typeCapitalized", "DigestAlgorithm", "Modal", "open", "closed", "ModalWrapper", "title", "<PERSON><PERSON><PERSON><PERSON>", "onCloseClick", "swipeToClose", "status", "interpolation", "escapeValue", "renderSignatureSummary", "editor", "DocumentPermissions", "e_no_changes_allowed", "e_formfilling_signing_allowed", "e_annotating_formfilling_signing_allowed", "e_unrestricted", "renderDocumentPermission", "e_untrusted", "e_trust_verification_disabled", "renderTrustStatus", "e_corrupt_file", "e_unsigned", "e_bad_byteranges", "e_corrupt_cryptographic_contents", "renderDocumentStatus", "e_digest_invalid", "e_weak_digest_algorithm_but_digest_verifiable", "e_no_digest_status", "e_unsupported_encoding", "renderDigestStatus", "e_SHA1", "e_SHA256", "e_SHA384", "e_SHA512", "e_RIPEMD160", "e_unknown_digest_algorithm", "renderDigestAlgorithm", "label"], "mappings": "kFAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEzB5B,EAAO2B,QAAU,EAAQ,GAAR,EAAqE,IAKxFR,KAAK,CAACnB,EAAOC,EAAI,qOAAsO,M,qBCL/P,IAAIL,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEzB5B,EAAO2B,QAAU,EAAQ,GAAR,EAAqE,IAKxFR,KAAK,CAACnB,EAAOC,EAAI,23GAA43G,M,qBCLr5G,IAAIL,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,61FAA81F,KAGv3F0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,2iECTvB,kuNACA,IA+CA,+BAAe,WAAOC,EAAKC,EAAcC,EAAYC,EAAiBC,EAAoBC,EAAuBC,GAAQ,uFACtFC,EAAsBP,EAAKC,EAAcC,EAAYC,EAAiBC,EAAoBC,GAAsB,OACrF,OADtDG,EAAqB,EAAH,KACxBF,EAASG,IAAQC,sBAAsBF,IAAqB,kBACrDA,GAAkB,2CAC1B,+DAJD,GA8BMD,EAAqB,6BAAG,WAAOP,EAAKC,EAAcC,EAAYC,EAAiBC,EAAoBC,GAAqB,mFAS9F,OARtBM,EAAWpC,OAAOqC,KAAlBD,OACAE,EAAuBF,EAAvBE,mBAENC,EAIED,EAJFC,YACAC,EAGEF,EAHFE,aACAC,EAEEH,EAFFG,8BACAC,EACEJ,EADFI,eAEIC,EAAsB,GAAE,SAGxBP,EAAOQ,eAAc,YAAC,0OAIdnB,EAAIoB,YAAW,OAAxB,OAAHpB,EAAM,EAAH,cACgBW,EAAOU,oBAAoBC,OAC5CX,EAAOU,oBAAoBE,cAAcC,+BAC1C,OAFS,GAAJC,EAAO,EAAH,MAINrB,EAAoB,CAAF,+BACdqB,EAAKC,mCAAkC,GAAK,UAGtB,OAA1BrB,EAA8B,kCAC1BoB,EAAKE,yBAvGuB,6BAuGkC,wCACjCC,IAA1BvB,EAAmC,kCACtCoB,EAAKE,yBAAyBtB,GAAsB,YAGlCJ,GAAY,8GAAhB,GACO,iBADlB4B,EAAW,SACe,0CAEzBJ,EAAKK,6BAA6BD,GAAY,8BAKlD,OALkD,yBAEpDE,QAAQC,MACN,sEAA+DH,EAAW,MACxE,iEACF,mEAIJA,aAAuBI,MACyB,kBAAhDC,OAAOC,UAAUC,SAASC,KAAKR,IAAgC,iBAcnD,OAZNS,EAAa,IAAIC,WACjBC,EAAqB,IAAIC,SAAQ,SAACC,EAASC,GAC/CL,EAAWM,iBAAiB,OAAM,6BAAE,WAAOC,GAAC,iEAC1CH,EAAQ,IAAII,WAAWD,EAAEE,OAAOC,SAAS,2CAC1C,mDAFiC,IAGlCV,EAAWM,iBAAiB,SAAS,WACnCD,EAAO,0CAGTL,EAAWW,kBAAkBpB,MAC7B,eAEMJ,EAAI,UAA6Be,EAAkB,0CAA9CU,sBAAqB,gDAK9B,OAL8B,2BAEhCnB,QAAQC,MACN,mEACE,qEACF,mEAIJH,aAAuBsB,aACpBtB,aAAuBuB,WACvBvB,aAAuBiB,YACvBjB,aAAuBwB,mBAAiB,4CAGnC5B,EAAKyB,sBAAsBrB,GAAY,gCAK3C,OAL2C,2BAE7CE,QAAQC,MACN,mEACE,qEACF,6ZAMgB9B,GAAU,2DAQhB,GARPoD,EAAS,QACZC,EAAyBD,EAAUE,YAAYC,KAC/CC,EAA0B,CAC9B,cACA,YACA,aACA,qBAEEC,OAAY,EACe,SAA3BJ,EAAiC,kCACdD,EAAUM,cAAa,QAA5CD,EAAe,EAAH,iCACHD,EAAwBG,SAASN,GAAyB,CAAF,gBACjEI,EAAeL,EAAU,wBAMvB,OAJFvB,QAAQC,MACN,6HACqE,2BAC/C,UAAI0B,EAAyB,UAAQI,KAAK,OAChE,2DAIgBnD,EAAOoD,OAAOC,uBAAuBL,GAAa,QAA3D,OAAHM,EAAM,EAAH,eACHxC,EAAKyC,cAAcD,GAAI,gCAK3B,OAL2B,2BAE7BlC,QAAQC,MACN,wEACE,qEACF,0LAKsBhC,EAAImE,wBAAuB,QAAjDC,EAAgB,EAAH,8BACLA,EAAcC,UAAS,sDACfD,EAAcE,UAAS,QAAhC,OAALC,EAAQ,EAAH,eAEDA,EAAMC,UAAS,+DACdD,EAAME,UAAS,yBAAK9D,EAAO+D,MAAMC,KAAKC,YAAW,6GAI9BjE,EAAOkE,sBAAsBC,gBAAgBP,GAAM,SAA5D,OAAfQ,EAAkB,EAAH,2BAEEA,EAAgBC,OAAOvD,GAAK,SAArC,OAANuB,EAAS,EAAH,gBACY+B,EAAgBE,YAAW,kCAAEC,YAAS,SAYvC,OAZjBC,EAAK,EAAH,KAEJC,OAAM,EACNC,OAAQ,EACRC,OAAkB,EAClBC,OAAe,EACfC,OAAW,EACXC,OAAQ,EACRC,OAAM,EACNC,OAAoB,EACpBC,OAAU,EACRC,EAAc,GACdC,EAAe,GAAE,WAEFf,EAAgBgB,4BAA2B,SAApD,KAANC,EAAS,EAAH,MACA,CAAF,mCACgBjB,EAAgBkB,eAAc,SAAvC,IAATC,EAAY,EAAH,QACGvF,EAAOkE,sBAAsBsB,cAAcC,sBAAqB,oCACvDrB,EAAgBsB,uBAAsB,SAA/C,OAAVC,EAAa,EAAH,gBAaoBA,EAAWC,kBAAiB,SAArC,OAArBC,EAAwB,EAAH,gBACSC,EAA6BD,GAAsB,oDAAI,GAAE,SAAvFE,EAAwB,EAAH,GAC3BtB,EAASsB,EAAoC,aAAE,YAI7CR,IAAcvF,EAAOkE,sBAAsBsB,cAAcQ,eAAc,qBACpEvB,EAAQ,CAAF,mCAEDL,EAAgB6B,mBAAkB,iEAC/B7B,EAAgB8B,iBAAgB,8BAF3CzB,EAAS,EAAH,8BAKSL,EAAgB+B,iBAAgB,SAAzC,OAARzB,EAAW,EAAH,gBAEEA,EAASb,UAAS,sCAC1Ba,EAAW0B,EAAiB1B,EAAUlF,GAAiB,0BAEvDkF,EAAW,KAAK,2BAGEN,EAAgB8B,iBAAgB,SAAzC,OAAXrB,EAAc,EAAH,gBACMT,EAAgBiC,cAAa,SAAtC,OAARvB,EAAW,EAAH,gBACOV,EAAgBkC,YAAW,SAA1CvB,EAAS,EAAH,gCAGmBX,EAAgBmC,yBAAwB,SAAjD,OAAlB5B,EAAqB,EAAH,gBACMP,EAAgBQ,kBAAiB,SAAzDA,EAAkB,EAAH,gCAGgBvC,EAAOmE,wBAAuB,SAAvC,OAAlBC,EAAqB,EAAH,gBACKpE,EAAOqE,oBAAmB,SAAnC,OAAdC,EAAiB,EAAH,gBACOtE,EAAOuE,kBAAiB,SAAjC,OAAZC,EAAe,EAAH,gBACQxE,EAAOyE,iBAAgB,SAAhC,OAAXC,GAAc,EAAH,gBACc1E,EAAO2E,uBAAsB,SAAtC,OAAhBC,GAAmB,EAAH,gBACQ5E,EAAO6E,qBAAoB,SAClB,OADjCC,GAAkB,EAAH,UACWrF,QAAO,WAC9BO,EAAO+E,uBAAsB,SAGlC,OAHkC,aAAEC,IAAG,6BAAC,WAAOC,GAAM,iFACvCA,EAAO/C,YAAW,mCACpB+C,EAAOC,kBAAiB,6CADpCC,OAAQ,EAAF,GACNC,KAAM,EAAF,gDACJ,mDAHuC,IAGvC,gBAJoCC,IAAG,0BAWlB,OAXnBC,GAAoB,EAAH,KAMjBC,GAAsBb,KAAgB5G,EAAY0H,iBAEpDC,QAA6B,EAC7BC,QAA8B,EAC9BC,QAA2B,EAC3BC,QAAqB,aACgB5F,EAAO6F,6BAA4B,SAA5C,IAAG,EAAH,KACA,CAAF,mCACU7F,EAAO8F,6BAA4B,SAA5C,OAAvBC,GAA0B,EAAH,gBAEUA,GAAwBC,gBAAe,SAAhD,OAA9BN,GAAiC,EAAH,gBACQK,GAAwBE,kBAAiB,SAAlD,OAA7BR,GAAgC,EAAH,gBACOM,GAAwBG,iCAAgC,SAAjE,OAA3BP,GAA8B,EAAH,gBAEcI,GAAwBI,6BAA4B,SAG5F,OAHKC,GAA6B,EAAH,QAE9BR,GAAwBS,EAAWD,GAA4BjJ,IAChE,WACsB4I,GAAwBO,cAAa,SAA9C,KAARC,GAAW,EAAH,MACDzK,OAAQ,CAAF,iBACgB,OAA3B0K,GAAgBD,GAAS,GAAE,WACEC,GAAcC,iBAAgB,SAAvC,OAApBC,GAAuB,EAAH,gBACSjD,EAA6BiD,IAAqB,SACpC,OAD3CC,GAAuB,EAAH,KAC1BzH,OAAO0H,OAAO/D,EAAa8D,IAAsB,WACbH,GAAcjD,kBAAiB,SAAxC,OAArBC,GAAwB,EAAH,gBACSC,EAA6BD,IAAsB,SAGvF,OAHME,GAAwB,EAAH,KAC3BxE,OAAO0H,OAAO9D,EAAcY,IACtBmD,GAAeN,GAASA,GAASzK,OAAS,GAChD,sBAgBmC+K,GAAaC,wBAAuB,SAA7C,OAAlBC,GAAqB,EAAH,gBACQF,GAAaG,uBAAsB,SAA7DC,GAAoB,EAAH,KACvBtE,EACEsE,IAAqBb,IAClBA,IAA8BW,GACjC,wDAEE,MAAalG,SAAS,qEACxB9B,QAAQmI,KACN,kMAGFnI,QAAQmI,KAAK,EAAD,MACb,SA0BN,OArBGC,QAAS,EAEXA,GADE/C,EACU,0BAEZE,IAAmBrG,EAAemJ,YACjC5C,IAAiBzG,EAAasJ,mBAC7B7C,IAAiBzG,EAAauJ,gCAChC5C,KAAgB5G,EAAYyJ,mBAC3B3C,KAAqB5G,EAA8BwJ,cAClD5C,KAAqB5G,EAA8ByJ,uBACnD7C,KAAqB5G,EAA8B0J,oCAIzC,0BAFA,4BAKVtF,EACFQ,EAAaR,GACHA,GAAUU,EAAa6E,eACjC/E,EAAaE,EAAa6E,cAC3B,WACuBpG,EAAMqG,UAAS,SAAjCC,GAAY,EAAH,KACf3J,EAAoB2J,IAAa,CAC/B7E,SACAZ,SACAQ,aACAP,WACA+B,qBACAE,iBACAE,eACAE,eACAE,oBACAU,qBACAI,kCACAD,iCACAE,+BACAC,yBACAzD,KACAgF,aACA5B,uBACAT,mBACAxC,qBACAC,kBACAC,cACAC,WACAC,SACAG,cACAC,eACAH,wBACA,wDAEF5D,QAAQC,MAAM,EAAD,KAAI,SAtMmBoC,EAAc0G,OAAM,6HAyM5D,gCAEK5J,GAAmB,2CAC3B,gBAlU0B,gDA6UrB6F,EAAmB,SAACgE,EAAM5K,GAC9B,IAAQ6K,EAA2CD,EAA3CC,KAAMC,EAAqCF,EAArCE,MAAOC,EAA8BH,EAA9BG,IAAKC,EAAyBJ,EAAzBI,KAAMC,EAAmBL,EAAnBK,OAAQC,EAAWN,EAAXM,OAGxC,OAFU,IAAIC,KAAKA,KAAKC,IAAIP,EAAMC,EAAQ,EAAGC,EAAKC,EAAMC,EAAQC,IAEvDG,mBAAmBrL,EAAgBsL,QAAQ,IAAK,KAAM,CAC7DT,KAAM,UACNC,MAAO,OACPS,QAAS,OACTR,IAAK,UACLC,KAAM,UACNC,OAAQ,UACRO,aAAc,WAWZtC,EAAa,SAACuC,EAAWzL,GAC7B,IAAM4K,EAAO,IAAIO,KAAK,GAKtB,OAFAP,EAAKc,cAAcD,GAEZb,EAAKS,mBAAmBrL,EAAgBsL,QAAQ,IAAK,KAAM,CAChET,KAAM,UACNC,MAAO,OACPS,QAAS,OACTR,IAAK,UACLC,KAAM,UACNC,OAAQ,UACRO,aAAc,WAoBZlF,EAA4B,6BAAG,WAAOqF,GAA2B,qFAC3C,OAApBC,EAAkB,GAAE,SACUD,EAA4BE,4BAA2B,OAArFC,EAAwB,EAAH,SACaA,GAAqB,yDAAzB,OAAzBC,EAAyB,kBACHA,EAA0BC,sBAAqB,QAAxD,OAAhBC,EAAmB,EAAH,eACJA,EAAiBC,cAAa,QAAvC,OAAHC,EAAM,EAAH,eACWJ,EAA0BK,iBAAgB,QAAxDC,EAAQ,EAAH,KACXT,EAAgBU,EAAkCH,IAAQE,EAAM,wKAE3DT,GAAe,gEACvB,gBAViC,sCAiC5BU,EAAoC,SAACC,GAsBzC,MArByB,CACvB,YAAa,eACb,YAAa,YACb,YAAa,gBACb,YAAa,iBACb,YAAa,wBACb,YAAa,kBACb,aAAc,qBACd,aAAc,2BAQd,yBAA0B,kBAE8B,iBAA5BA,EAC1BA,EACAC,KAAKC,UAAUF,KCpgBNG,G,QAFC,WAAH,OAAS,yBAAKC,UAAU,c,kmCCMrC,ICPeC,EDOO,SAAH,GAAiB,IAAXC,EAAI,EAAJA,KACgB,IAAfC,oBAAS,GAAM,GAAhCC,EAAI,KAAEC,EAAO,KAyBpB,OAvBAC,qBAAU,WACR,IAAMC,EAAsBC,IAAKC,uBAC3BC,EAAe,WACnBL,GAAQ,IAIV,OADAE,EAAoBzK,iBAAiB,SAAU4K,GACxC,kBAAMH,EAAoBI,oBAAoB,SAAUD,OAGjEJ,qBAAU,WACJJ,IACFU,YAAW,WAETP,GAAQ,KACP,IAEHO,YAAW,WACTP,GAAQ,KACP,QAEJ,CAACH,IAGFE,GACAS,IAASC,aACP,yBACEC,MAAO,CACLC,SAAU,WACVC,IAAKf,EAAKgB,GACVC,KAAMjB,EAAKkB,GACXC,MAAOnB,EAAKoB,GAAKpB,EAAKkB,GACtBG,OAAQrB,EAAKsB,GAAKtB,EAAKgB,GAEvBO,OAAQ,oBAERC,OAAQ,MAGZC,cAAcC,cAAc,U,kkCElC3B,IAAMC,EAAyB,SAAH,GAK7B,IACA3Q,EALJuH,EAAe,EAAfA,gBACAvE,EAA6B,EAA7BA,8BACA4G,EAAgB,EAAhBA,iBACAgH,EAAS,EAATA,UAIA,OAAQhH,GACN,KAAK5G,EAA8B6N,oCACjC7Q,EAAU4Q,EACR,gFAEF,MACF,KAAK5N,EAA8ByJ,sBACjCzM,EAAU4Q,EACR,mEAEF,MACF,KAAK5N,EAA8BwJ,aACjCxM,EAAU,GAAH,OAAM4Q,EAAU,4DAA2D,YAE9EA,EADArJ,EACU,yCACA,uCAAsC,KAEpD,MACF,KAAKvE,EAA8B0J,oCACjC1M,EAAU4Q,EACR,iFAEF,MACF,KAAK5N,EAA8B8N,wBACjC9Q,EAAU4Q,EACR,qEAKN,OAAO,2BAAI5Q,IAGP+Q,EAAY,CAChBtL,KAAMuL,IAAUC,OAAOC,WACvBC,QAASH,IAAUI,KACnB7K,MAAOyK,IAAUK,WAAW9Q,OAAOqC,KAAK0O,YAAYC,MAAM7K,QAGtD8K,EAAa,SAAH,GAAwB,IAuE9BC,EAmRFzR,EA1VcyF,EAAI,EAAJA,KAAMc,EAAK,EAALA,MACpB/D,EAAqBkP,aAAY,SAACC,GAAK,OAAKC,IAAUrP,sBAAsBoP,EAAOlM,MACrC,IAAdwJ,mBAAS,MAAK,GAA7C4C,EAAW,KAAEC,EAAc,KAC6C,IAAf7C,oBAAS,GAAM,GAAxE8C,EAAwB,KAAEC,EAA2B,KAC5D,EAAoDzR,OAAOqC,KAAKD,OAAxDE,EAAkB,EAAlBA,mBACAoP,EADuC,EAAnB5O,oBACpB4O,SACAjP,EAAkCH,EAAlCG,8BACD4N,EAA6B,EAAhBsB,cAAgB,GAApB,GAGdlK,EAcExF,EAdFwF,OACAX,EAaE7E,EAbF6E,SACA+B,EAYE5G,EAZF4G,mBACAQ,EAWEpH,EAXFoH,iBACAU,EAUE9H,EAVF8H,kBACAI,EASElI,EATFkI,+BACAC,EAQEnI,EARFmI,4BACAC,EAOEpI,EAPFoI,sBACAuB,EAME3J,EANF2J,UACA5E,EAKE/E,EALF+E,gBACAC,EAIEhF,EAJFgF,YACAC,EAGEjF,EAHFiF,SACAC,EAEElF,EAFFkF,OACAE,EACEpF,EADFoF,WAGItF,EAAW6P,cAwCXC,EAAmB,YA9BJ,SAAC7L,GACpB,GAAKA,EAAM8L,QAAQvR,OAAnB,CAGA,IAAMwR,EAAS/L,EAAM8L,QAAQ,GAC7B/C,IAAKiD,iBAAiBD,GAEtB,MAAkChD,IAAKC,uBAA/BiD,EAAU,EAAVA,WAAYC,EAAS,EAATA,UACdzD,EAAOsD,EAAOI,UACdC,EAAgBrD,IACnBsD,uBACAC,aAAa,CAAEC,EAAG9D,EAAKkB,GAAI6C,EAAG/D,EAAKgB,IAAMsC,EAAOU,YAC7CC,EAAoB3D,IACvBsD,uBACAC,aAAa,CAAEC,EAAG9D,EAAKoB,GAAI2C,EAAG/D,EAAKsB,IAAMgC,EAAOU,YAEnDlB,EAAe,CACb5B,GAAIyC,EAAcG,EAAIN,EACtBxC,GAAI2C,EAAcI,EAAIN,EACtBrC,GAAI6C,EAAkBH,EAAIN,EAC1BlC,GAAI2C,EAAkBF,EAAIN,KAW5BS,CAAa3M,IA+PT4M,EAA8BC,aALT,WACzB9Q,EAASG,IAAQ4Q,sCAAsC5N,IACvDnD,EAASG,IAAQ6Q,YAAY,gCAqC/B,OACE,yBAAKxE,UAAU,yBACZ9G,EACC,kBAAC,IAAMuL,SAAQ,KACb,kBAACC,EAAA,EAAa,CACZC,aAfJzT,EACA4Q,EADUrJ,EACA,yCACA,uCACdvH,GAAW,IAAJ,OAAQ4Q,EAAU,mCAAkC,YAAIhJ,GAAcgJ,EAAU,kCACnFvJ,IACFrH,GAAW,IAAJ,OAAQ4Q,EAAU,mCAAkC,YAAIvJ,IAE1DrH,GASC0T,UAAWvH,EACXwH,cAAc,EACdxC,QAASiB,EACTwB,UAAWxB,GAEX,yBACEtD,UAAU,sBACV+E,UAAW,GAEX,yBAAK/E,UAAU,WA5SnB2C,EACFb,EADqBrJ,EACX,6CACA,0CAEZ,yBAAKuH,UAAU,SACb,2BAGQ8B,EADJxH,EACc,wDACA,yDADyD,CAAEqI,wBA2SrE,wBAAI3C,UAAU,QAnSG,WAC7B,IAAI9O,EAEJ,OAAQ4J,GACN,KAAK5G,EAA8B6N,oCACjC7Q,EAAU4Q,EACR,gFAEF,MACF,KAAK5N,EAA8ByJ,sBACjCzM,EAAU4Q,EACR,mEAEF,MACF,KAAK5N,EAA8BwJ,aACjCxM,EAAU,GAAH,OAAM4Q,EAAU,4DAA2D,YAE9EA,EADArJ,EACU,yCACA,uCAAsC,KAEpD,MACF,KAAKvE,EAA8B0J,oCACjC1M,EAAU4Q,EACR,iFAEF,MACF,KAAK5N,EAA8B8N,wBACjC9Q,EAAU4Q,EACR,qEAKN,OACE,4BACE,2BACG5Q,IAiQS2Q,GA1PTrG,EAAkBN,KAAI,gBAAGG,EAAM,EAANA,OAAQC,EAAI,EAAJA,KAAI,OAC1C,wBAAIkE,IAAKnE,GACP,2BAEIyG,EACE,gDACA,CAAExG,OAAMD,gBAQY,WAC9B,IAAI2J,EACJ,OAAQnJ,GACN,KAAMsH,EAAS8B,UACbD,EACElD,EAAU,0DAEZ,MACF,KAAMqB,EAAS+B,UACbF,EACElD,EAAU,0DAEZ,MACF,KAAMqB,EAASgC,YACbH,EACElD,EAAU,4DAEZ,MACF,QACE7M,QAAQmI,KAAK,2DAAD,OACiDvB,IAGjE,OACE,oCACE,4BACE,2BAEIiG,EACElG,EACI,+DACA,wEA4BZ,4BACE,2BACGE,IAGL,4BACE,2BACGkJ,KAkLMI,GA3Db,4BACE,4BACEC,eAAA,8BAAqC1O,GACrC0L,QAASgC,EACTU,SAAU,EACV/E,UAAU,2BACVsF,aAAW,mCAEX,uBAAGtF,UAAU,kBACV8B,EAAU,0DAuDX,yBAAK9B,UAAU,4BA9KlBtH,GAAgBC,GAAaC,EAIhC,yBACEoH,UAAU,mBACV+E,UAAW,GAEX,yBAAK/E,UAAU,qBACb,kBAACuF,EAAA,EAAM,CACLC,IAAI,qBACJxF,UAAWyF,IAAW,CACpBC,OAAO,EACPC,SAAU1C,IAEZ2C,KAAK,SACLC,aAAc5C,EACd6C,SAAU7C,EACV8C,UAAWjE,EAAU,kEACrBO,QACE,kBAAMa,GAA6BD,MAGvC,2BAEInB,EACE,oEAMNmB,GAEE,wBAAIjD,UAAU,QACZ,4BACE,uBAAGA,UAAU,QAAM,UAEZ8B,EAAU,oEAAmE,MAGpF,uBAAG9B,UAAU,qBAETtH,GACGoJ,EAAU,wEAInB,4BACE,uBAAG9B,UAAU,QAAM,UAEZ8B,EAAU,0DAAyD,MAG1E,uBAAG9B,UAAU,qBAETrH,GACGmJ,EAAU,8DAInB,4BACE,uBAAG9B,UAAU,QAAM,UAEZ8B,EAAU,wDAAuD,MAGxE,uBAAG9B,UAAU,qBAETpH,GACGkJ,EAAU,4DAKjBvJ,GACE,4BACE,uBAAGyH,UAAU,QAAM,UAEZ8B,EAAU,6DAA4D,MAG7E,uBAAG9B,UAAU,qBAETzH,GACGuJ,EAAU,mEApFxB,QAmLL,kBAAC4C,EAAA,EAAa,CACZC,YAAa7C,EAAU,sDAAuD,CAAE/D,UAAWtG,EAAMd,OACjGiO,UAAU,0BACVC,cAAc,EACdxC,QAASiB,EACTwB,UAAWxB,IAGf,kBAAC,EAAa,CAACpD,KAAM6C,MAK3BL,EAAWT,UAAYA,EAERS,IC5dAA,ED4dAA,E,2wBE7df,8lGAAApR,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,+kBAAAA,GAAA,gEAAAA,GAAA,0JAAAA,EAAA,6FAAAA,GAAA,mIAAAA,IAAA,IAAAA,IAAA,ygBAAAA,EAAA,iBAAAA,EAAA,EAAAA,IAAA,EAAAA,GAAA,EAAAA,GAAA,SAeA,IAiMe0U,EAjMQ,WACrB,IAAMxS,EAAW6P,cACuB,IAAZlD,mBAAS,IAAG,GAAjC8F,EAAM,KAAEC,EAAS,KAC6B,IAAf/F,oBAAS,GAAM,GAA9CgG,EAAW,KAAEC,EAAc,KACwC,IAAZjG,mBAAS,IAAG,GAAnEkG,EAAuB,KAAEC,EAA0B,KACE,IAA5BnG,mBAASK,IAAK+F,eAAc,GAArD5U,EAAQ,KAAE6U,EAAW,KAe1B,IAPE5D,aAAY,SAACC,GAAK,MAAK,CACzBC,IAAU2D,kBAAkB5D,EAAO,kBACnCC,IAAU4D,gBAAgB7D,GAC1BC,IAAU6D,cAAc9D,GACxBC,IAAU8D,mBAAmB/D,GAC7BC,IAAU+D,+BAA+BhE,GACzCC,IAAUgE,yBAAyBjE,OACnC,GAbAkE,EAAU,KACVhS,EAAW,KACX3B,EAAU,KACVC,EAAe,KACfC,EAAkB,KAClBC,EAAqB,KAShBuO,EAA6B,EAAhBsB,cAAgB,GAApB,GAEV4D,EAAgB,6BAAG,8EACvBR,EAAYhG,IAAK+F,eAAe,2CACjC,kBAFqB,mCAIhBU,EAAqBC,uBAAY,WACrCd,GAAe,GACf5S,EAASG,IAAQC,sBAAsB,OACtC,CAACwS,EAAgB5S,IAEd2T,EAAuB,SAACC,EAAaC,GACzC,IAAMC,EAAuB9G,IAAK+G,uBAAuBC,8BAA8BC,4BAExE,QAAXJ,EACFK,EAA8BlH,IAAK+G,uBAAuBI,sBACtC,WAAXN,GAAuBC,GAChCM,EAAkBR,IAIhBS,EAAqB,WACzB,IAAMC,EAAqBtH,IAAK+G,uBAAuBI,qBACvDD,EAA8BI,IAG1BJ,EAAgC,SAACN,GACrC,IAAMW,EAAgB,GACtBX,EAAY9U,SAAQ,SAAC0V,GACfA,aAAsBvW,OAAOqC,KAAK0O,YAAYyF,2BAChDF,EAAcvV,KAAKwV,EAAWE,eAGlC,IAAMC,EAAS,IAAIC,IAAIL,GACvB7B,EAAU,EAAIiC,KAGVP,EAAoB,SAACR,GACzBA,EAAY9U,SAAQ,SAAC0V,GACnBK,EAAqBL,MAGvBH,KAGIQ,EAAuB,SAACL,GAE5B,GADiBA,aAAsBvW,OAAOqC,KAAK0O,YAAY8F,iBACjD,CACZ,IAAMC,EAAoB/H,IAAK+G,uBAEzBiB,EADiBD,EAAkBZ,qBACHc,QAAO,SAACC,GAC5C,OAAOA,EAAmBC,cAAc,8BAAgCX,EAAWY,MAErFL,EAAkBM,kBAAkBL,KAIlCM,EAAc,WAClB5C,EAAU,IACV2B,KAsDF,GAnDAvH,qBAAU,WAQR,OALAE,IAAK1K,iBAAiB,iBAAkBkR,GACxCxG,IAAK1K,iBAAiB,mBAAoBmR,GAC1CzG,IAAK1K,iBAAiB,oBAAqBqR,GAC3C3G,IAAK1K,iBAAiB,+BAAgCgT,GACtDtI,IAAK1K,iBAAiB,6BAA8BgT,GAC7C,WACLtI,IAAKG,oBAAoB,iBAAkBqG,GAC3CxG,IAAKG,oBAAoB,mBAAoBsG,GAC7CzG,IAAKG,oBAAoB,oBAAqBwG,GAC9C3G,IAAKG,oBAAoB,+BAAgCmI,GACzDtI,IAAKG,oBAAoB,6BAA8BmI,MAExD,CAAC7B,IAEJ3G,qBAAU,WAIJ3O,EAGF6O,IAAKuI,8BAA8BC,MAAK,WACtC5C,GAAe,GACfxS,EAAsBjC,EAAUoD,EAAa3B,EAAYC,EAAiBC,EAAoBC,EAAuBC,GAClHwV,KAAI,6BAAC,WAAOtV,GAAkB,uEACvBuV,EAAezI,IAAK+G,uBAAuB2B,kBACjDhD,EAAU9Q,OAAO+T,KAAKzV,GAAoBwH,KAAI,SAAC6C,GAAS,OAAKkL,EAAaf,SAASnK,OACnFuI,EAA2B,IAAI,2CAChC,mDAJI,IAIH,OACK,SAACvQ,GACFA,GAAKA,EAAEqT,QACT9C,EAA2BvQ,EAAEqT,SAE7BnU,QAAQC,MAAMa,MAGjBiT,MAAK,WACJnB,OACA,SACO,WACPzB,GAAe,SAIrBA,GAAe,KAEhB,CAACrR,EAAapD,EAAU6B,EAAUH,IAEjC0T,EACF,OAAO,KAkCT,OACE,yBACE/G,UAAU,uBACVqF,eAAa,kBA7Ba,WAC5B,IAAInP,EACJ,GAAIiQ,EACFjQ,EAAS,kBAAC,EAAO,WACZ,GAAgC,wCAA5BmQ,EACTnQ,EAAS4L,EAAU,yEACd,GAAgC,oBAA5BuE,EACTnQ,EAAS4L,EAAU,2EACd,IAAKmE,EAAOjU,OAOjB,OAAO,KANPkE,EAAS4L,EAAU,gEASrB,OACE,yBAAK9B,UAAU,yBACb,kBAACqJ,EAAA,EAAI,CAACrJ,UAAU,aAAasJ,MAAOC,IAAUC,IAAWC,WAAWC,OACpE,yBAAK1J,UAAU,iBAAiB9J,IAUjCyT,IAEExD,GAAeF,EAAOjU,OAAS,GAC9BiU,EAAO/K,KAAI,SAACzD,EAAOmS,GACjB,OACE,kBAAC,EAAU,CACTpK,IAAKoK,EACLjT,KAAMc,EAAMd,KACZkT,aAAW,EACXpS,MAAOA,SCnMRuO,a,qBCJf,IAAI/U,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEzB5B,EAAO2B,QAAU,EAAQ,GAAR,EAAqE,IAKxFR,KAAK,CAACnB,EAAOC,EAAI,kIAAmI,M,qBCL5J,IAAIL,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,8tNAA+tN,KAGxvN0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qOCHjBgP,G,QAAY,CAChB6H,MAAO5H,IAAUC,OACjB4H,KAAM7H,IAAUC,SAGZ6H,EAAgB,SAAH,OAAMF,EAAK,EAALA,MAAK,IAAEC,YAAI,IAAG,WAAQ,SAC7C,yBAAK/J,UAAU,kBACZ8J,GAAS,kBAACT,EAAA,EAAI,CAACC,MAAOQ,EAAO9J,UAAS,gBAAW+J,OAItDC,EAAc/H,UAAYA,EAEX+H,IClBAA,EDkBAA,E,yjCEAf,IAseeC,EAtekB,WAC/B,IAAOnI,EAA6B,EAAhBsB,cAAgB,GAApB,GAWf,IAToCR,aACnC,SAACC,GACC,IAAQqH,EAA8BrH,EAAMsH,2BAApCD,0BACR,MAAO,CACLpH,IAAUsH,cAAcvH,EAAOwH,IAAaC,4BAC5CxH,IAAUrP,sBAAsBoP,EAAOqH,MAG3CK,KACD,GATMC,EAAM,KAAE9W,EAAkB,KAW3BF,EAAW6P,cACXoH,EAAaC,aAAgB,WACjClX,EAASG,IAAQgX,cAAc,CAACN,IAAaC,iCAG/ChK,qBAAU,WACJkK,GACFhX,EACEG,IAAQgX,cAAc,CACpBN,IAAaO,gBACbP,IAAaQ,cACbR,IAAaS,YACbT,IAAaU,YACbV,IAAaW,oBAIlB,CAACxX,EAAUgX,IAwBd,IAgFQS,EA/EN5N,EAaE3J,EAbF2J,UACA/C,EAYE5G,EAZF4G,mBACAQ,EAWEpH,EAXFoH,iBACArC,EAUE/E,EAVF+E,gBACAD,EASE9E,EATF8E,mBACAmD,EAQEjI,EARFiI,8BACAE,EAOEnI,EAPFmI,4BACAC,EAMEpI,EANFoI,sBACAd,EAKEtH,EALFsH,gBACAN,EAIEhH,EAJFgH,aACAF,EAGE9G,EAHF8G,eACAI,EAEElH,EAFFkH,YACA9B,EACEpF,EADFoF,WAEF,EAKIrH,OAAOqC,KAAKD,OAJdqX,EAAe,EAAfA,gBACAnT,EAAqB,EAArBA,sBACAxD,EAAmB,EAAnBA,oBACAR,EAAkB,EAAlBA,mBAGAG,EAIEH,EAJFG,8BACAF,EAGED,EAHFC,YACAC,EAEEF,EAFFE,aACAE,EACEJ,EADFI,eAEMgP,EAAa5O,EAAb4O,SA0XR,OACE,yBACEnD,UAAWyF,IAAW,CACpB0F,OAAO,EACPlB,0BAA0B,EAC1BmB,KAAMZ,EACNa,QAASb,IAEXnF,eAAcgF,IAAaC,4BAE3B,kBAACgB,EAAA,EAAY,CACXC,OAhVEN,EACFnJ,EADoBrJ,EACV,sCACA,mCAEPqJ,EAAU,8BAA+B,CAAExG,KAAM2P,KA6UpDO,aAAcf,EACdgB,aAAchB,EACdD,OAAQA,EACRkB,cAAY,GAEZ,yBAAK1L,UAAU,aAzYU,WAC7B,IAAI2L,EACJ,OAAQtO,GACN,IAAK,0BACHsO,EAAS7J,EAAU,+BACnB,MACF,IAAK,4BACH6J,EAAS7J,EAAU,iCACnB,MACF,IAAK,0BACH6J,EAAS7J,EAAU,iCACnB,MACF,QACE6J,EAAS7J,EAAU,iCAGvB,IAAMxG,EACFwG,EADSrJ,EACC,sCACA,mCAEd,OACE,6BACE,yBAAKuH,UAAU,eACb,kBAAC,EAAa,CAAC8J,MAAOzM,EAAW0M,KAAK,WACtC,6BAEIjI,EACE,2CACA,CACExG,OACAqQ,WAKU,4BAAdtO,EACIyE,EACA,4CACA,CACEnL,KAAMmC,GAAcgJ,EAAU,iCAC9B8J,cAAe,CAAEC,aAAa,KAE9B,MAgWTC,QAlE2B,IAAvBxR,EAEP,yBAAK0F,UAAU,UACb,kBAAC,UAAO,OAKZ,oCACE,yBAAKA,UAAU,QACb,yBAAKA,UAAU,WA1CrB,uBACEA,UAAU,UAET8B,EAAU,mDA0CHD,iCAAuB,CACrBpJ,kBACAvE,gCACA4G,mBACAgH,cA1RmB,WAC/B,GAAKtJ,EAAL,CAIA,IAAItH,EAAU,GACR6a,EAAStT,EAAkB,YAAc,SAE/C,OAAQD,GACN,KAAKT,EAAsBiU,oBAAoBC,qBAC7C/a,GAAW4Q,EAAU,4DAA6D,CAAEiK,WACpF,MACF,KAAKhU,EAAsBiU,oBAAoBE,8BAC7Chb,GAAW4Q,EAAU,qEAAsE,CAAEiK,WAC7F,MACF,KAAKhU,EAAsBiU,oBAAoBG,yCAC7Cjb,GAAW4Q,EAAU,+EAAgF,CAAEiK,WACvG,MACF,KAAKhU,EAAsBiU,oBAAoBI,eAC7Clb,GAAW4Q,EAAU,wDAAyD,CAAEiK,WAIpF,OAAO,2BAAI7a,IAsQFmb,IAEH,yBAAKrM,UAAU,WA9CrB,uBACEA,UAAU,UAET8B,EAAU,iDA7CW,WACxB,IAGI5Q,EAHEyR,EACFb,EADqBrJ,EACX,yCACA,uCAGd,OAAQmC,GACN,KAAK5G,EAAY0H,iBACfxK,EAAU4Q,EACR,yDACA,CAAEa,qBAEJ,MACF,KAAK3O,EAAYsY,YACfpb,EAAU4Q,EACR,sDAEF,MACF,KAAK9N,EAAYuY,8BACfrb,EAAU4Q,EACR,sEAEF,MACF,KAAK9N,EAAYyJ,kBACfvM,EAAU4Q,EACR,0DAKN,OAAO,2BAAI5Q,GA4DFsb,GAzPqB,WAC9B,IAAK7Q,EACH,OACE,2BAAImG,EAAU,iDAIlB,IAAI5Q,EAAU,GACd,OAAQ2K,GACN,KAAMsH,EAAS8B,UACb/T,GAAW4Q,EACT,kDACA,CAAEhG,0BAEJ,MACF,KAAMqH,EAAS+B,UACbhU,GAAW4Q,EACT,kDACA,CAAEhG,0BAEJ,MACF,KAAMqH,EAASgC,YACbjU,GAAW4Q,EACT,oDACA,CAAEhG,0BAKR,OAAO,2BAAI5K,GA6NFkU,IAEH,yBAAKpF,UAAU,WA3CrB,uBACEA,UAAU,UAET8B,EAAU,+CA9Hc,WAC3B,IAAI5Q,EAEJ,OAAQsJ,GACN,KAAKrG,EAAemJ,WAClBpM,EAAU4Q,EACR,uDAEF,MACF,KAAK3N,EAAesY,eAClBvb,EAAU4Q,EACR,2DAEF,MACF,KAAK3N,EAAeuY,WAClBxb,EAAU4Q,EACR,wDAEF,MACF,KAAK3N,EAAewY,iBAClBzb,EAAU4Q,EACR,6DAEF,MACF,KAAK3N,EAAeyY,iCAClB1b,EAAU4Q,EACR,4EAKN,OAAO,2BAAI5Q,GAyIF2b,IAEH,yBAAK7M,UAAU,WAvCrB,uBACEA,UAAU,UAET8B,EAAU,8CApGY,WACzB,IAAI5Q,EAEJ,OAAQwJ,GACN,KAAKzG,EAAa6Y,iBAChB5b,EAAU4Q,EACR,2DAEF,MACF,KAAK7N,EAAasJ,kBAChBrM,EAAU4Q,EACR,4DAEF,MACF,KAAK7N,EAAauJ,+BAChBtM,EAAU4Q,EACR,wEAEF,MACF,KAAK7N,EAAa8Y,8CAChB7b,EAAU4Q,EACR,oFAEF,MACF,KAAK7N,EAAa+Y,mBAChB9b,EAAU4Q,EACR,4DAEF,MACF,KAAK7N,EAAagZ,uBAChB/b,EAAU4Q,EACR,iEAKN,OAAO,2BAAI5Q,GAsGFgc,GA7NmB,WAC5B,IAAIhc,EAAU4Q,EAAU,kDAExB,OAAQ9G,GACN,KAAKkQ,EAAgBrT,KAAKsV,OACxBjc,GAAW,QACX,MACF,KAAKga,EAAgBrT,KAAKuV,SACxBlc,GAAW,UACX,MACF,KAAKga,EAAgBrT,KAAKwV,SACxBnc,GAAW,UACX,MACF,KAAKga,EAAgBrT,KAAKyV,SACxBpc,GAAW,UACX,MACF,KAAKga,EAAgBrT,KAAK0V,YACxBrc,GAAW,aACX,MACF,KAAKga,EAAgBrT,KAAK2V,2BACxBtc,EAAU4Q,EAAU,iDAIxB,OAAO,2BAAI5Q,GAsMFuc,KAGL,yBAAKzN,UAAU,gBACb,kBAACuF,EAAA,EAAM,CACLvF,UAAU,qBACVqC,QAASoI,EACTiD,MAAO5L,EAAU,wBCvddmI", "file": "chunks/chunk.35.js", "sourcesContent": ["var api = require(\"!../../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../../node_modules/css-loader/index.js!../../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../../node_modules/sass-loader/dist/cjs.js!./Spinner.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".SignaturePanel .spinner{margin:10px;border:5px solid #ddd;border-top-color:#aaa;border-radius:50%;width:40px;height:40px;animation:spin 1.2s ease infinite}@keyframes spin{0%{transform:rotate(0deg)}to{transform:rotate(1turn)}}\", \"\"]);\n\n// exports\n", "var api = require(\"!../../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../../node_modules/css-loader/index.js!../../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../../node_modules/sass-loader/dist/cjs.js!./WidgetInfo.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".signature-widget-info{--widget-header-indent:49px;--widget-body-indent:22px;--arrow-width:12px;padding:4px;display:flex;flex-direction:column;font-size:13px;margin-bottom:10px;margin-left:5px;box-sizing:border-box;border:1px solid transparent;cursor:pointer;--border-radius-amount:4px;-moz-border-radius-topleft:var(--border-radius-amount);-moz-border-radius-topright:var(--border-radius-amount);-moz-border-radius-bottomright:var(--border-radius-amount);-moz-border-radius-bottomleft:var(--border-radius-amount);border-radius:var(--border-radius-amount)}.signature-widget-info p+p{margin:1em 0 0}.signature-widget-info p.result-for-header{margin-top:0}.signature-widget-info p.bold{font-weight:700;margin-bottom:4px}.signature-widget-info p.underline{text-decoration:underline}.signature-widget-info .signatureProperties{padding:0;margin:0}.signature-widget-info .signatureProperties:focus,.signature-widget-info .signatureProperties:hover{color:var(--blue-5)}.signature-widget-info .link{cursor:pointer;outline:none;border:0;background-color:transparent;white-space:nowrap}.signature-widget-info .link.focus-visible,.signature-widget-info .link:focus-visible{outline:var(--focus-visible-outline)}.signature-widget-info .link p{margin:0;padding:0}.signature-widget-info .panel-list-text-container{height:100%}.signature-widget-info .panel-list-text-container .panel-list-label-header .Button span{text-align:left;overflow:visible;white-space:normal;text-overflow:inherit}.signature-widget-info .panel-list-icon-container .Icon{width:20px;height:20px}.signature-widget-info .title{padding-left:4px;font-weight:700;display:flex;align-items:center;min-height:32px;margin-top:-5px;margin-bottom:-5px;overflow:hidden;border:0;background-color:transparent}.signature-widget-info .title button+*,.signature-widget-info .title div+*{margin-left:2px}.signature-widget-info .title .arrow{min-width:var(--arrow-width);transition:transform .1s ease;margin-top:0;background-color:transparent;border:none;padding:0;display:flex;justify-content:center;align-items:center}.signature-widget-info .title .arrow.expanded{transform:rotate(90deg)}.signature-widget-info .title .arrow .Icon{width:var(--arrow-width);height:var(--arrow-width)}.signature-widget-info .title .arrow.hidden{visibility:hidden;padding:23px}.signature-widget-info .title .arrow:hover .Icon{color:var(--blue-6)}.signature-widget-info .title .arrow.focus-visible,.signature-widget-info .title .arrow:focus-visible{outline:var(--focus-visible-outline)}.signature-widget-info .title .signature-icon{margin-right:5px}.signature-widget-info .title.focus-visible,.signature-widget-info .title:focus-visible{outline:var(--focus-visible-outline)}.signature-widget-info .header{margin-left:32px}.signature-widget-info .header ul{padding-left:24px}.signature-widget-info .header .body>div:first-child>p:first-child,.signature-widget-info .header .body>p:first-child{margin-top:.5em}.signature-widget-info .header .body>div:last-child{margin-bottom:.5em}.signature-widget-info .header-with-arrow{margin-left:0}.signature-widget-info .header-with-arrow ul{margin-left:var(--arrow-width)}.signature-widget-info .header-with-arrow ul li{margin-left:18px}.signature-widget-info .panel-list-label-header Button{font-weight:700;font-size:13px}.signature-widget-info .signatureDetails,.signature-widget-info .verificationDetails{cursor:default;padding:0;background-color:transparent;border:none;text-align:left}\", \"\"]);\n\n// exports\n", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./SignaturePanel.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.SignaturePanel{z-index:65;transition:transform .3s ease,visibility 0s ease .3s}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignaturePanel{top:0;width:100%;height:100%}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SignaturePanel{top:0;width:100%;height:100%}}.open.SignaturePanel{transform:none;visibility:visible;transition:transform .3s ease,visibility 0s ease 0s}.SignaturePanel .empty-panel-container{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%;width:100%;padding:36px;grid-gap:8px;gap:8px}.SignaturePanel .empty-panel-container .empty-icon{width:60px;height:60px;color:var(--gray-6);fill:var(--gray-6)}.SignaturePanel .empty-panel-container .empty-icon svg{width:60px;height:60px}.SignaturePanel .empty-panel-container .empty-message{text-align:center;max-width:131px;font-size:13px}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignaturePanel .empty-panel-container .empty-message{line-height:15px}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .SignaturePanel .empty-panel-container .empty-message{line-height:15px}}.SignaturePanel{margin:0 8px 8px;display:flex;flex-direction:column}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignaturePanel{width:auto;margin:16px;flex-grow:1;overflow-y:auto}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SignaturePanel{width:auto;margin:16px;flex-grow:1;overflow-y:auto}}.SignaturePanel .center{display:flex;justify-content:center;align-items:center}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignaturePanel .signature-widget-info .title .arrow{min-width:auto;padding:3px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SignaturePanel .signature-widget-info .title .arrow{min-width:auto;padding:3px}}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import actions from 'actions';\n\nconst DEFAULT_REVOCATION_PROXY_PREFIX = 'https://proxy.pdftron.com';\n\n/**\n * Side-effect function that sets the verification status of the document.\n * One of three possible results can happen:\n *\n * 1. Valid: All signatures have been successfully verified\n * 2. Unknown: One or more signatures has issues, but the verification API\n * cannot definitively state the signatures are valid or invalid (i.e. a\n * required Public Key Certificate is missing)\n * 3. Invalid: The document has been digitally signed, but has one or more\n * invalid signatures (i.e. because the document was modified after it was\n * signed, and one or more signature field did not allow for this to occur)\n *\n * The above results are dictated by the return values of:\n *\n * PDFNet.VerificationResult.getDocumentStatus:\n * https://docs.apryse.com/api/web/Core.PDFNet.VerificationResult.html#getDocumentStatus__anchor\n *\n * PDFNet.VerificationResult.getTrustStatus:\n * https://docs.apryse.com/api/web/Core.PDFNet.VerificationResult.html#getTrustStatus__anchor\n *\n * PDFNet.VerificationResult.getDigestStatus:\n * https://docs.apryse.com/api/web/Core.PDFNet.VerificationResult.html#getDigestStatus__anchor\n *\n * PDFNet.VerificationResult.getPermissionsStatus:\n * https://docs.apryse.com/api/web/Core.PDFNet.VerificationResult.html#getPermissionsStatus__anchor\n *\n * Valid:\n * DocumentStatus.e_no_error && TrustStatus.e_trust_verified\n *\n * Unknown:\n * DocumentStatus.e_no_error\n * && (\n *  DigestStatus.e_digest_verified\n *  || DigestStatus.e_digest_verification_disabled\n * ) && trustStatus !== TrustStatus.e_no_trust_status\n * && (\n *   ModificationPermissionsStatus.e_unmodified\n *   || ModificationPermissionsStatus.e_has_allowed_changes\n *   || ModificationPermissionsStatus.e_permissions_verification_disabled\n * )\n *\n * Any other combinations will cause the signature field to be considered\n * Invalid.\n * @ignore\n */\nexport default async (doc, certificates, trustLists, currentLanguage, revocationChecking, revocationProxyPrefix, dispatch) => {\n  const verificationResult = await getVerificationResult(doc, certificates, trustLists, currentLanguage, revocationChecking, revocationProxyPrefix);\n  dispatch(actions.setVerificationResult(verificationResult));\n  return verificationResult;\n};\n\n/**\n * Iterates through each signature widget in the document, retrieves and returns\n * all pertinent information pertaining to Digital Signature Verification\n *\n * @param {Core.Document} doc The document with signatures to verify\n * with the given certificate\n * @param {Array<File | string>} certificates The certificate files to be used\n * for verification. Can be passed as a File object, or a URL in the form\n * of a string, in which a GET call will be made to retrieve the certificate\n * @param {\n *   Array<Blob | ArrayBuffer | Int8Array | Uint8Array | Uint8ClampedArray>\n * } trustLists The Trust Lists to load for verification.\n * @param {string} currentLanguage Current UI language\n * @param {boolean} revocationChecking Determines if the PDFNet API\n * VerificationOptions.enableOnlineCRLRevocationChecking is invoked to enable\n * Online Certification Revocation List (CRL) Revocation Checking is done\n * within the PDFNet logic\n * @param {string} revocationProxyPrefix The URL of a proxy server that should\n * be used to avoid CORS related issues when contacting Certificate Revocation\n * List (CRL) servers\n * @returns {object} An object mapping the field name of each signature widget\n * to their verification results\n * @ignore\n */\nconst getVerificationResult = async (doc, certificates, trustLists, currentLanguage, revocationChecking, revocationProxyPrefix) => {\n  const { PDFNet } = window.Core;\n  const { VerificationResult } = PDFNet;\n  const {\n    TrustStatus,\n    DigestStatus,\n    ModificationPermissionsStatus,\n    DocumentStatus,\n  } = VerificationResult;\n  const verificationResults = {};\n\n\n  await PDFNet.runWithCleanup(async () => {\n    /**\n     * @todo Remove re-assignment of argument from original code?\n     */\n    doc = await doc.getPDFDoc();\n    const opts = await PDFNet.VerificationOptions.create(\n      PDFNet.VerificationOptions.SecurityLevel.e_compatibility_and_archiving\n    );\n\n    if (revocationChecking) {\n      await opts.enableOnlineCRLRevocationChecking(true);\n    }\n\n    if (revocationProxyPrefix === null) {\n      await opts.setRevocationProxyPrefix(DEFAULT_REVOCATION_PROXY_PREFIX);\n    } else if (revocationProxyPrefix !== undefined) {\n      await opts.setRevocationProxyPrefix(revocationProxyPrefix);\n    }\n\n    for (const certificate of certificates) {\n      if (typeof certificate === 'string') {\n        try {\n          await opts.addTrustedCertificateFromURL(certificate);\n        } catch {\n          console.error(\n            `Error encountered when trying to load certificate from URL: ${certificate}\\n`\n            + 'Certificate will not be used as part of verification process.'\n          );\n          continue;\n        }\n      } else if (\n        certificate instanceof File ||\n        Object.prototype.toString.call(certificate) === '[object File]'\n      ) {\n        const fileReader = new FileReader();\n        const arrayBufferPromise = new Promise((resolve, reject) => {\n          fileReader.addEventListener('load', async (e) => {\n            resolve(new Uint8Array(e.target.result));\n          });\n          fileReader.addEventListener('error', () => {\n            reject('Error reading the local certificate');\n          });\n\n          fileReader.readAsArrayBuffer(certificate);\n        });\n        try {\n          await opts.addTrustedCertificate(await arrayBufferPromise);\n        } catch (error) {\n          console.error(\n            `Error encountered when trying to load certificate: ${error}`\n            + 'Certificate will not be used as part of the verification process.'\n          );\n          continue;\n        }\n      } else if (\n        certificate instanceof ArrayBuffer\n        || certificate instanceof Int8Array\n        || certificate instanceof Uint8Array\n        || certificate instanceof Uint8ClampedArray\n      ) {\n        try {\n          await opts.addTrustedCertificate(certificate);\n        } catch (error) {\n          console.error(\n            `Error encountered when trying to load certificate: ${error}`\n            + 'Certificate will not be used as part of the verification process.'\n          );\n          continue;\n        }\n      }\n    }\n\n    for (const trustList of trustLists) {\n      const trustListDataStructure = trustList.constructor.name;\n      const supportedDataStructures = [\n        'ArrayBuffer',\n        'Int8Array',\n        'Uint8Array',\n        'Uint8ClampedArray',\n      ];\n      let fdfDocBuffer;\n      if (trustListDataStructure === 'Blob') {\n        fdfDocBuffer = await trustList.arrayBuffer();\n      } else if (supportedDataStructures.includes(trustListDataStructure)) {\n        fdfDocBuffer = trustList;\n      } else {\n        console.error(\n          'The provided TrustList is an unsupported data-structure. '\n          + 'Please ensure the TrustList is formatted as one of the following '\n          + `data-structures: ${[...supportedDataStructures, 'Blob'].join('|')}`\n        );\n        continue;\n      }\n      try {\n        const fdf = await PDFNet.FDFDoc.createFromMemoryBuffer(fdfDocBuffer);\n        await opts.loadTrustList(fdf);\n      } catch (error) {\n        console.error(\n          `Error encountered when trying to load certificate: ${error}. `\n          + 'Certificate will not be used as part of the verification process.'\n        );\n        continue;\n      }\n    }\n\n    const fieldIterator = await doc.getFieldIteratorBegin();\n    for (; (await fieldIterator.hasNext()); fieldIterator.next()) {\n      const field = await fieldIterator.current();\n      if (\n        !(await field.isValid())\n        || await field.getType() !== PDFNet.Field.Type.e_signature\n      ) {\n        continue;\n      }\n      const digitalSigField = await PDFNet.DigitalSignatureField.createFromField(field);\n      try {\n        const result = await digitalSigField.verify(opts);\n        const id = await (await digitalSigField.getSDFObj()).getObjNum();\n\n        let signer;\n        let signTime;\n        let documentPermission;\n        let isCertification;\n        let contactInfo;\n        let location;\n        let reason;\n        let validAtTimeOfSigning;\n        let signerName;\n        const issuerField = {};\n        const subjectField = {};\n\n        const signed = await digitalSigField.hasCryptographicSignature();\n        if (signed) {\n          const subFilter = await digitalSigField.getSubFilter();\n          if (subFilter === PDFNet.DigitalSignatureField.SubFilterType.e_adbe_pkcs7_detached) {\n            const signerCert = await digitalSigField.getSignerCertFromCMS();\n            /**\n             * @note \"Issuer\" refers to the Certificate Authority that issued the\n             * certificate\n             * \"Subject\" refers to the organization/person that the Certificate\n             * Auhority issued this certificate to\n             *\n             * It is likely that future UI iterations will leverage Issuer\n             * information, so the code has been commented out for now, but will\n             * be uncommented in future feature implementations\n             */\n            // const retrievedIssuerField = await signerCert.getIssuerField();\n            // const processedIssuerField = await processX501DistinguishedName(retrievedIssuerField) || {};\n            const retrievedSubjectField = await signerCert.getSubjectField();\n            const processedSubjectField = await processX501DistinguishedName(retrievedSubjectField) || {};\n            signer = processedSubjectField['e_commonName'];\n          }\n          // Getter functions cannot be called on Digital Signature fields using\n          // e_ETSI_RFC3161\n          if (subFilter !== PDFNet.DigitalSignatureField.SubFilterType.e_ETSI_RFC3161) {\n            if (!signer) {\n              signer = (\n                await digitalSigField.getSignatureName()\n                || await digitalSigField.getContactInfo()\n              );\n            }\n            signTime = await digitalSigField.getSigningTime();\n\n            if (await signTime.isValid()) {\n              signTime = formatPDFNetDate(signTime, currentLanguage);\n            } else {\n              signTime = null;\n            }\n\n            contactInfo = await digitalSigField.getContactInfo();\n            location = await digitalSigField.getLocation();\n            reason = await digitalSigField.getReason();\n          }\n\n          documentPermission = await digitalSigField.getDocumentPermissions();\n          isCertification = await digitalSigField.isCertification();\n        }\n\n        const verificationStatus = await result.getVerificationStatus();\n        const documentStatus = await result.getDocumentStatus();\n        const digestStatus = await result.getDigestStatus();\n        const trustStatus = await result.getTrustStatus();\n        const permissionStatus = await result.getPermissionsStatus();\n        const digestAlgorithm = await result.getDigestAlgorithm();\n        const disallowedChanges = await Promise.all(\n          (await result.getDisallowedChanges()).map(async (change) => ({\n            objnum: await change.getObjNum(),\n            type: await change.getTypeAsString(),\n          }))\n        );\n        const validSignerIdentity = trustStatus === TrustStatus.e_trust_verified;\n\n        let trustVerificationResultString;\n        let trustVerificationResultBoolean;\n        let timeOfTrustVerificationEnum;\n        let trustVerificationTime;\n        const hasTrustVerificationResult = await result.hasTrustVerificationResult();\n        if (hasTrustVerificationResult) {\n          const trustVerificationResult = await result.getTrustVerificationResult();\n\n          trustVerificationResultBoolean = await trustVerificationResult.wasSuccessful();\n          trustVerificationResultString = await trustVerificationResult.getResultString();\n          timeOfTrustVerificationEnum = await trustVerificationResult.getTimeOfTrustVerificationEnum();\n\n          const epochTrustVerificationTime = await trustVerificationResult.getTimeOfTrustVerification();\n          if (epochTrustVerificationTime) {\n            trustVerificationTime = formatDate(epochTrustVerificationTime, currentLanguage);\n          }\n          const certPath = await trustVerificationResult.getCertPath();\n          if (certPath.length) {\n            const firstX509Cert = certPath[0];\n            const retrievedIssuerField = await firstX509Cert.getIssuerField();\n            const processedIssuerField = await processX501DistinguishedName(retrievedIssuerField);\n            Object.assign(issuerField, processedIssuerField);\n            const retrievedSubjectField = await firstX509Cert.getSubjectField();\n            const processedSubjectField = await processX501DistinguishedName(retrievedSubjectField);\n            Object.assign(subjectField, processedSubjectField);\n            const lastX509Cert = certPath[certPath.length - 1];\n            /**\n             * @todo @colim @rdjericpdftron 2022-05-30\n             * Using the pdftron::PDF::VerificationOptions::LoadTrustList API\n             * in combination with\n             * pdftron::Crypto::X509Certificate::GetNotBeforeEpochTime\n             * or\n             * pdftron::Crypto::X509Certificate::GetNotAfterEpochTime\n             * Results in the following fatal error being thrown:\n             *\n             * calendar_point::to_std_timepoint() does not support years after\n             * 2037 on this system\n             *\n             * @rdjericpdftron Mentions that this should be addressed in a\n             * future release of PDFNet when the Botan library has been patched\n             */\n            try {\n              const notBeforeEpochTime = await lastX509Cert.getNotBeforeEpochTime();\n              const notAfterEpochTime = await lastX509Cert.getNotAfterEpochTime();\n              validAtTimeOfSigning = (\n                notAfterEpochTime >= epochTrustVerificationTime\n                && epochTrustVerificationTime >= notBeforeEpochTime\n              );\n            } catch (dateBugError) {\n              if (dateBugError.includes('calendar_point::to_std_timepoint() does not support years after')) {\n                console.warn(\n                  'The following error is a known issue with Botan, and aims to be addressed in a future release of '\n                  + 'PDFNet. This currently does not impact PDFTron\\'s Digital Signature Verification capabilities.'\n                );\n                console.warn(dateBugError);\n              }\n            }\n          }\n        }\n\n        let badgeIcon;\n        if (verificationStatus) {\n          badgeIcon = 'digital_signature_valid';\n        } else if (\n          documentStatus === DocumentStatus.e_no_error &&\n          (digestStatus === DigestStatus.e_digest_verified ||\n            digestStatus === DigestStatus.e_digest_verification_disabled) &&\n          trustStatus !== TrustStatus.e_no_trust_status &&\n          (permissionStatus === ModificationPermissionsStatus.e_unmodified ||\n            permissionStatus === ModificationPermissionsStatus.e_has_allowed_changes ||\n            permissionStatus === ModificationPermissionsStatus.e_permissions_verification_disabled)\n        ) {\n          badgeIcon = 'digital_signature_warning';\n        } else {\n          badgeIcon = 'digital_signature_error';\n        }\n\n        if (signer) {\n          signerName = signer;\n        } else if (!signer && subjectField.e_commonName) {\n          signerName = subjectField.e_commonName;\n        }\n        const fieldName = await field.getName();\n        verificationResults[fieldName] = {\n          signed,\n          signer,\n          signerName,\n          signTime,\n          verificationStatus,\n          documentStatus,\n          digestStatus,\n          trustStatus,\n          permissionStatus,\n          disallowedChanges,\n          trustVerificationResultBoolean,\n          trustVerificationResultString,\n          timeOfTrustVerificationEnum,\n          trustVerificationTime,\n          id,\n          badgeIcon,\n          validSignerIdentity,\n          digestAlgorithm,\n          documentPermission,\n          isCertification,\n          contactInfo,\n          location,\n          reason,\n          issuerField,\n          subjectField,\n          validAtTimeOfSigning,\n        };\n      } catch (e) {\n        console.error(e);\n      }\n    }\n  });\n\n  return verificationResults;\n};\n\n/**\n * Retrieves the relevant information from the given object, containing date\n * information that originates from epoch time, and returns a string containing\n * the date and time information in a human readable string\n *\n * @param {object} date The date object that is returned from PDFNet\n * @returns {string} Human readable formatted date and time\n * @ignore\n */\nconst formatPDFNetDate = (date, currentLanguage) => {\n  const { year, month, day, hour, minute, second } = date;\n  const d = new Date(Date.UTC(year, month - 1, day, hour, minute, second));\n\n  return d.toLocaleDateString(currentLanguage.replace('_', '-'), {\n    year: 'numeric',\n    month: 'long',\n    weekday: 'long',\n    day: 'numeric',\n    hour: 'numeric',\n    minute: 'numeric',\n    timeZoneName: 'short',\n  });\n};\n\n/**\n * Converts an epoch time input to date in string\n *\n * @param {number} epochTime The epoch time to be converted\n * @returns {string} The converted epoch time\n * @ignore\n */\nconst formatDate = (epochTime, currentLanguage) => {\n  const date = new Date(0);\n  // Values greater than 59 are converted into their parent values\n  // (i.e. seconds -> minutes -> hours -> day etc.)\n  date.setUTCSeconds(epochTime);\n\n  return date.toLocaleDateString(currentLanguage.replace('_', '-'), {\n    year: 'numeric',\n    month: 'long',\n    weekday: 'long',\n    day: 'numeric',\n    hour: 'numeric',\n    minute: 'numeric',\n    timeZoneName: 'short',\n  });\n};\n\n/**\n * Processes an instance of the PDFNet.X501DistinguishedName class into a\n * Javascript object that is human readable\n *\n * Intended to process the objects returned from invoking\n * PDFNet.X509Certificate.GetIssuerField and\n * PDFNet.X509Certificate.GetSubjectField\n *\n * @param {PDFNet.X501DistinguishedName} x501DistinguishedNameObject An instance\n * of the PDFNet.X501DistinguishedName class, to be processed into a Javascript\n * object\n * @returns {object} Maps human readable keys (as opposed to the Botan Crpyto\n * OIDs, represented as Array<int> in PDFTron Core) to the corresponding values\n * they map to\n * @ignore\n */\nconst processX501DistinguishedName = async (x501DistinguishedNameObject) => {\n  const processedObject = {};\n  const allAttributeAndValues = await x501DistinguishedNameObject.getAllAttributesAndValues();\n  for (const x501AttributeTypeAndValue of allAttributeAndValues) {\n    const objectIdentifier = await x501AttributeTypeAndValue.getAttributeTypeOID();\n    const key = await objectIdentifier.getRawValue();\n    const value = await x501AttributeTypeAndValue.getStringValue();\n    processedObject[translateObjectIdentifierBotanOID(key)] = value;\n  }\n  return processedObject;\n};\n\n/**\n * Takes an Array<Number> argument (or its string representation from\n * JSON.stringify) and returns the enum it is supposed to represent based on\n * its OID representation in the Botan crypto C++ library\n *\n * PDFTron Core represents the key from the original Map<string, string>\n * data-structre in the form of an array\n *\n * @example The key of the object\n * { \"2.5.4.3\", \"X520.CommonName\" }\n * Is represented as [2,5,4,3] in PDFTron Core\n *\n * Source: https://botan.randombit.net/doxygen/oid__maps_8cpp_source.html\n *\n * @param {string | Array<Number>} objectIdentifierOIDenum The array returned\n * from the invocation of PDFNet.ObjectIdentifier.getRawValue, which can be\n * accepted as the Array input (which the body of the function will convert to a\n * string), or a string representation of the array\n * @returns {string} The human readable enum that the array represents\n * @ignore\n */\nconst translateObjectIdentifierBotanOID = (objectIdentifierOIDenum) => {\n  const botanArrayToEnum = {\n    '[2,5,4,3]': 'e_commonName',\n    '[2,5,4,4]': 'e_surname',\n    '[2,5,4,6]': 'e_countryName',\n    '[2,5,4,7]': 'e_localityName',\n    '[2,5,4,8]': 'e_stateOrProvinceName',\n    '[2,5,4,9]': 'e_streetAddress',\n    '[2,5,4,10]': 'e_organizationName',\n    '[2,5,4,11]': 'e_organizationalUnitName',\n    /**\n     * @note Added by @CorreyL, enum is not in PDFTronCore codebase as of\n     * PDFNetJS8.1\n     *\n     * Listed as { \"1.2.840.113549.1.9.1\", \"PKCS9.EmailAddress\" }\n     * in load_oid2str_map()\n     */\n    '[1,2,840,113549,1,9,1]': 'e_emailAddress',\n  };\n  const arrayAsString = (typeof objectIdentifierOIDenum === 'string')\n    ? objectIdentifierOIDenum\n    : JSON.stringify(objectIdentifierOIDenum);\n  return botanArrayToEnum[arrayAsString];\n};\n", "import React from 'react';\nimport './Spinner.scss';\n\nconst Spinner = () => <div className=\"spinner\" />;\n\nexport default Spinner;\n", "import React, {\n  useEffect,\n  useState,\n} from 'react';\nimport ReactDOM from 'react-dom';\n\nimport core from 'core';\nimport getRootNode from 'helpers/getRootNode';\n\nconst WidgetLocator = ({ rect }) => {\n  const [show, setShow] = useState(false);\n\n  useEffect(() => {\n    const scrollViewContainer = core.getScrollViewElement();\n    const handleScroll = () => {\n      setShow(false);\n    };\n\n    scrollViewContainer.addEventListener('scroll', handleScroll);\n    return () => scrollViewContainer.removeEventListener('scroll', handleScroll);\n  });\n\n  useEffect(() => {\n    if (rect) {\n      setTimeout(() => {\n        // so that the locator won't disappear because of the scroll\n        setShow(true);\n      }, 50);\n\n      setTimeout(() => {\n        setShow(false);\n      }, 700);\n    }\n  }, [rect]);\n\n  return (\n    show &&\n    ReactDOM.createPortal(\n      <div\n        style={{\n          position: 'absolute',\n          top: rect.y1,\n          left: rect.x1,\n          width: rect.x2 - rect.x1,\n          height: rect.y2 - rect.y1,\n          // eslint-disable-next-line custom/no-hex-colors\n          border: '1px solid #00a5e4',\n          // don't appear above the signature properties modal\n          zIndex: 99,\n        }}\n      />,\n      getRootNode().querySelector('#app')\n    )\n  );\n};\n\nexport default WidgetLocator;\n", "import WidgetLocator from './WidgetLocator';\n\nexport default WidgetLocator;\n", "import React, { useState } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport PropTypes from 'prop-types';\nimport { useTranslation } from 'react-i18next';\n\nimport actions from 'actions';\nimport classNames from 'classnames';\nimport core from 'core';\nimport selectors from 'selectors';\n\nimport WidgetLocator from '../WidgetLocator';\nimport useFocusHandler from 'hooks/useFocusHandler';\nimport PanelListItem from 'src/components/PanelListItem';\nimport Button from 'src/components/Button';\n\nimport './WidgetInfo.scss';\n\nexport const renderPermissionStatus = ({\n  isCertification,\n  ModificationPermissionsStatus,\n  permissionStatus,\n  translate,\n}) => {\n  let content;\n\n  switch (permissionStatus) {\n    case ModificationPermissionsStatus.e_invalidated_by_disallowed_changes:\n      content = translate(\n        'digitalSignatureVerification.permissionStatus.invalidatedByDisallowedChanges'\n      );\n      break;\n    case ModificationPermissionsStatus.e_has_allowed_changes:\n      content = translate(\n        'digitalSignatureVerification.permissionStatus.hasAllowedChanges'\n      );\n      break;\n    case ModificationPermissionsStatus.e_unmodified:\n      content = `${translate('digitalSignatureVerification.permissionStatus.unmodified')\n      } ${isCertification\n        ? translate('digitalSignatureVerification.certified')\n        : translate('digitalSignatureVerification.signed')\n      }.`;\n      break;\n    case ModificationPermissionsStatus.e_permissions_verification_disabled:\n      content = translate(\n        'digitalSignatureVerification.permissionStatus.permissionsVerificationDisabled'\n      );\n      break;\n    case ModificationPermissionsStatus.e_no_permissions_status:\n      content = translate(\n        'digitalSignatureVerification.permissionStatus.noPermissionsStatus'\n      );\n      break;\n  }\n\n  return <p>{content}</p>;\n};\n\nconst propTypes = {\n  name: PropTypes.string.isRequired,\n  onClick: PropTypes.func,\n  field: PropTypes.instanceOf(window.Core.Annotations.Forms.Field),\n};\n\nconst WidgetInfo = ({ name, field }) => {\n  const verificationResult = useSelector((state) => selectors.getVerificationResult(state, name));\n  const [locatorRect, setLocatorRect] = useState(null);\n  const [signatureDetailsExpanded, setSignatureDetailsExpanded] = useState(false);\n  const { VerificationResult, VerificationOptions } = window.Core.PDFNet;\n  const { TimeMode } = VerificationOptions;\n  const { ModificationPermissionsStatus } = VerificationResult;\n  const [translate] = useTranslation();\n\n  const {\n    signed,\n    signTime,\n    verificationStatus,\n    permissionStatus,\n    disallowedChanges,\n    trustVerificationResultBoolean,\n    timeOfTrustVerificationEnum,\n    trustVerificationTime,\n    badgeIcon,\n    isCertification,\n    contactInfo,\n    location,\n    reason,\n    signerName,\n  } = verificationResult;\n\n  const dispatch = useDispatch();\n\n  /**\n   * Side-effect function that highlights the SignatureWidgetAnnotation\n   * pertaining to the text element that was clicked by using core code to find\n   * the coordinates of the widget on the page it is placed on\n   *\n   * @param {Annotations.Forms.Field} field The field pertaining\n   * to the text element clicked in the Signature Panel\n   */\n  const jumpToWidget = (field) => {\n    if (!field.widgets.length) {\n      return;\n    }\n    const widget = field.widgets[0];\n    core.jumpToAnnotation(widget);\n\n    const { scrollLeft, scrollTop } = core.getScrollViewElement();\n    const rect = widget.getRect();\n    const windowTopLeft = core\n      .getDisplayModeObject()\n      .pageToWindow({ x: rect.x1, y: rect.y1 }, widget.PageNumber);\n    const windowBottomRight = core\n      .getDisplayModeObject()\n      .pageToWindow({ x: rect.x2, y: rect.y2 }, widget.PageNumber);\n\n    setLocatorRect({\n      x1: windowTopLeft.x - scrollLeft,\n      y1: windowTopLeft.y - scrollTop,\n      x2: windowBottomRight.x - scrollLeft,\n      y2: windowBottomRight.y - scrollTop,\n    });\n  };\n\n  /**\n   * Function that invokes the necessary methods when a user interacts with\n   * the title of the widget\n   *\n   * interaction\n   */\n  const titleInteraction = () => {\n    jumpToWidget(field);\n  };\n\n  const renderVerificationStatus = () => {\n    const verificationType = isCertification\n      ? translate('digitalSignatureVerification.Certification')\n      : translate('digitalSignatureVerification.Signature');\n    return (\n      <div className=\"title\">\n        <p>\n          {\n            verificationStatus\n              ? translate('digitalSignatureVerification.verificationStatus.valid', { verificationType })\n              : translate('digitalSignatureVerification.verificationStatus.failed', { verificationType })\n          }\n        </p>\n      </div>\n    );\n  };\n\n  const renderPermissionStatus = () => {\n    let content;\n\n    switch (permissionStatus) {\n      case ModificationPermissionsStatus.e_invalidated_by_disallowed_changes:\n        content = translate(\n          'digitalSignatureVerification.permissionStatus.invalidatedByDisallowedChanges'\n        );\n        break;\n      case ModificationPermissionsStatus.e_has_allowed_changes:\n        content = translate(\n          'digitalSignatureVerification.permissionStatus.hasAllowedChanges'\n        );\n        break;\n      case ModificationPermissionsStatus.e_unmodified:\n        content = `${translate('digitalSignatureVerification.permissionStatus.unmodified')\n        } ${isCertification\n          ? translate('digitalSignatureVerification.certified')\n          : translate('digitalSignatureVerification.signed')\n        }.`;\n        break;\n      case ModificationPermissionsStatus.e_permissions_verification_disabled:\n        content = translate(\n          'digitalSignatureVerification.permissionStatus.permissionsVerificationDisabled'\n        );\n        break;\n      case ModificationPermissionsStatus.e_no_permissions_status:\n        content = translate(\n          'digitalSignatureVerification.permissionStatus.noPermissionsStatus'\n        );\n        break;\n    }\n\n    return (\n      <li>\n        <p>\n          {content}\n        </p>\n      </li>\n    );\n  };\n\n  const renderDisallowedChanges = () => {\n    return disallowedChanges.map(({ objnum, type }) => (\n      <li key={objnum}>\n        <p>\n          {\n            translate(\n              'digitalSignatureVerification.disallowedChange',\n              { type, objnum }\n            )\n          }\n        </p>\n      </li>\n    ));\n  };\n\n  const renderTrustVerification = () => {\n    let verificationTimeMessage;\n    switch (timeOfTrustVerificationEnum) {\n      case (TimeMode.e_current):\n        verificationTimeMessage = (\n          translate('digitalSignatureVerification.trustVerification.current')\n        );\n        break;\n      case (TimeMode.e_signing):\n        verificationTimeMessage = (\n          translate('digitalSignatureVerification.trustVerification.signing')\n        );\n        break;\n      case (TimeMode.e_timestamp):\n        verificationTimeMessage = (\n          translate('digitalSignatureVerification.trustVerification.timestamp')\n        );\n        break;\n      default:\n        console.warn(\n          `Unexpected pdftron::PDF::VerificationOptions::TimeMode: ${timeOfTrustVerificationEnum}`\n        );\n    }\n    return (\n      <>\n        <li>\n          <p>\n            {\n              translate(\n                trustVerificationResultBoolean\n                  ? 'digitalSignatureVerification.trustVerification.verifiedTrust'\n                  : 'digitalSignatureVerification.trustVerification.noTrustVerification'\n              )\n            }\n          </p>\n        </li>\n        {\n          /**\n           * @todo Chat with @rastko when he is available to determine what\n           * content from `trustVerificationResultString` could potentially\n           * be important to show to users\n           *\n           * <div>\n           *   <p className='bold'>Trust verification result:</p>\n           *   {\n           *     trustVerificationResultString.split('\\n').map((line, idx) => {\n           *       return (\n           *         <p\n           *           key={`trustVerificationResultString-${idx}`}\n           *           className={line[0] === '\\t' ? '' : 'bold'}\n           *         >\n           *           {line}\n           *         </p>\n           *       );\n           *     })\n           *   }\n           * </div>\n           */\n        }\n        <li>\n          <p>\n            {trustVerificationTime}\n          </p>\n        </li>\n        <li>\n          <p>\n            {verificationTimeMessage}\n          </p>\n        </li>\n      </>\n    );\n  };\n\n  const renderSignatureDetails = () => {\n    // No additional signature details to render\n    if (!contactInfo && !location && !reason) {\n      return null;\n    }\n    return (\n      <div\n        className='signatureDetails'\n        tabIndex={-1}\n      >\n        <div className=\"title collapsible\">\n          <Button\n            img=\"icon-chevron-right\"\n            className={classNames({\n              arrow: true,\n              expanded: signatureDetailsExpanded,\n            })}\n            role=\"button\"\n            ariaExpanded={signatureDetailsExpanded}\n            isActive={signatureDetailsExpanded}\n            ariaLabel={translate('digitalSignatureVerification.signatureDetails.signatureDetails')}\n            onClick={\n              () => setSignatureDetailsExpanded(!signatureDetailsExpanded)\n            }\n          />\n          <p>\n            {\n              translate(\n                'digitalSignatureVerification.signatureDetails.signatureDetails'\n              )\n            }\n          </p>\n        </div>\n        {\n          signatureDetailsExpanded\n          && (\n            <ul className=\"body\">\n              <li>\n                <p className=\"bold\">\n                  {\n                    `${translate('digitalSignatureVerification.signatureDetails.contactInformation')}:`\n                  }\n                </p>\n                <p className=\"result-for-header\">\n                  {\n                    contactInfo\n                    || translate('digitalSignatureVerification.signatureDetails.noContactInformation')\n                  }\n                </p>\n              </li>\n              <li>\n                <p className=\"bold\">\n                  {\n                    `${translate('digitalSignatureVerification.signatureDetails.location')}:`\n                  }\n                </p>\n                <p className=\"result-for-header\">\n                  {\n                    location\n                    || translate('digitalSignatureVerification.signatureDetails.noLocation')\n                  }\n                </p>\n              </li>\n              <li>\n                <p className=\"bold\">\n                  {\n                    `${translate('digitalSignatureVerification.signatureDetails.reason')}:`\n                  }\n                </p>\n                <p className=\"result-for-header\">\n                  {\n                    reason\n                    || translate('digitalSignatureVerification.signatureDetails.noReason')\n                  }\n                </p>\n              </li>\n              {\n                signTime && (\n                  <li>\n                    <p className=\"bold\">\n                      {\n                        `${translate('digitalSignatureVerification.signatureDetails.signingTime')}:`\n                      }\n                    </p>\n                    <p className=\"result-for-header\">\n                      {\n                        signTime\n                        || translate('digitalSignatureVerification.signatureDetails.noSigningTime')\n                      }\n                    </p>\n                  </li>\n                )\n              }\n            </ul>\n          )\n        }\n      </div>\n    );\n  };\n\n  const openSignatureModal = () => {\n    dispatch(actions.setSignatureValidationModalWidgetName(name));\n    dispatch(actions.openElement('signatureValidationModal'));\n  };\n\n  const openSignatureModalWithFocus = useFocusHandler(openSignatureModal);\n\n  /**\n   * Renders a button to open the signature modal\n   */\n  const renderSignaturePropertiesButton = () => {\n    return (\n      <li>\n        <button\n          data-element={`signatureProperties-${name}`}\n          onClick={openSignatureModalWithFocus}\n          tabIndex={0}\n          className=\"signatureProperties link\"\n          aria-label='Open signature properties modal'\n        >\n          <p className=\"bold underline\">\n            {translate('digitalSignatureVerification.signatureProperties')}\n          </p>\n        </button>\n      </li>\n    );\n  };\n\n  const getRenderTitle = () => {\n    let content = isCertification\n      ? translate('digitalSignatureVerification.Certified')\n      : translate('digitalSignatureVerification.Signed');\n    content += ` ${translate('digitalSignatureVerification.by')} ${signerName || translate('digitalSignatureModal.unknown')}`;\n    if (signTime) {\n      content += ` ${translate('digitalSignatureVerification.on')} ${signTime}`;\n    }\n    return content;\n  };\n\n  return (\n    <div className=\"signature-widget-info\">\n      {signed ? (\n        <React.Fragment>\n          <PanelListItem\n            labelHeader={getRenderTitle()}\n            iconGlyph={badgeIcon}\n            useI18String={false}\n            onClick={titleInteraction}\n            onKeyDown={titleInteraction}\n          >\n            <div\n              className='verificationDetails'\n              tabIndex={-1}\n            >\n              <div className=\"header\">\n                {\n                  renderVerificationStatus({\n                    isCertification,\n                    verificationStatus,\n                  })\n                }\n                <ul className=\"body\">\n                  {\n                    renderPermissionStatus({\n                      isCertification,\n                      ModificationPermissionsStatus,\n                      permissionStatus,\n                      translate,\n                    })\n                  }\n                  {renderDisallowedChanges()}\n                  {renderTrustVerification()}\n                  {renderSignaturePropertiesButton()}\n                </ul>\n              </div>\n            </div>\n            <div className=\"header header-with-arrow\">\n              {renderSignatureDetails()}\n            </div>\n          </PanelListItem>\n        </React.Fragment>\n      ) : (\n        <PanelListItem\n          labelHeader={translate('digitalSignatureVerification.unsignedSignatureField', { fieldName: field.name })}\n          iconGlyph='digital_signature_empty'\n          useI18String={false}\n          onClick={titleInteraction}\n          onKeyDown={titleInteraction}\n        />\n      )}\n      <WidgetLocator rect={locatorRect} />\n    </div>\n  );\n};\n\nWidgetInfo.propTypes = propTypes;\n\nexport default WidgetInfo;\n", "import WidgetInfo, { renderPermissionStatus } from './WidgetInfo';\n\nexport default WidgetInfo;\nexport {\n  renderPermissionStatus\n};\n", "import React, { useCallback, useEffect, useState, } from 'react';\nimport { useDispatch, useSelector, } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\n\nimport actions from 'actions';\nimport core from 'core';\nimport selectors from 'selectors';\nimport setVerificationResult from 'helpers/setVerificationResult';\n\nimport Spinner from './Spinner';\nimport WidgetInfo from './WidgetInfo';\n\nimport './SignaturePanel.scss';\nimport Icon from 'components/Icon';\nimport { panelData, panelNames } from 'constants/panel';\n\nconst SignaturePanel = () => {\n  const dispatch = useDispatch();\n  const [fields, setFields] = useState([]);\n  const [showSpinner, setShowSpinner] = useState(false);\n  const [certificateErrorMessage, setCertificateErrorMessage] = useState('');\n  const [document, setDocument] = useState(core.getDocument());\n  const [\n    isDisabled,\n    certificate,\n    trustLists,\n    currentLanguage,\n    revocationChecking,\n    revocationProxyPrefix,\n  ] = useSelector((state) => [\n    selectors.isElementDisabled(state, 'signaturePanel'),\n    selectors.getCertificates(state),\n    selectors.getTrustLists(state),\n    selectors.getCurrentLanguage(state),\n    selectors.getIsRevocationCheckingEnabled(state),\n    selectors.getRevocationProxyPrefix(state),\n  ]);\n  const [translate] = useTranslation();\n\n  const onDocumentLoaded = async () => {\n    setDocument(core.getDocument());\n  };\n\n  const onDocumentUnloaded = useCallback(() => {\n    setShowSpinner(true);\n    dispatch(actions.setVerificationResult({}));\n  }, [setShowSpinner, dispatch]);\n\n  const onAnnotationChanged = ((annotations, action) => {\n    const isInFormCreationMode = core.getAnnotationManager().getFormFieldCreationManager().isInFormFieldCreationMode();\n\n    if (action === 'add') {\n      addSignatureWidgetAnnotations(core.getAnnotationManager().getAnnotationsList());\n    } else if (action === 'delete' && isInFormCreationMode) {\n      removeAnnotations(annotations);\n    }\n  });\n\n  const addNonSignedFields = () => {\n    const currentAnnotations = core.getAnnotationManager().getAnnotationsList();\n    addSignatureWidgetAnnotations(currentAnnotations);\n  };\n\n  const addSignatureWidgetAnnotations = (annotations) => {\n    const currentFields = [];\n    annotations.forEach((annotation) => {\n      if (annotation instanceof window.Core.Annotations.SignatureWidgetAnnotation) {\n        currentFields.push(annotation.getField());\n      }\n    });\n    const newSet = new Set(currentFields);\n    setFields([...newSet]);\n  };\n\n  const removeAnnotations = (annotations) => {\n    annotations.forEach((annotation) => {\n      removeMatchingWidget(annotation);\n    });\n\n    addNonSignedFields();\n  };\n\n  const removeMatchingWidget = (annotation) => {\n    const isWidget = annotation instanceof window.Core.Annotations.WidgetAnnotation;\n    if (isWidget) {\n      const annotationManager = core.getAnnotationManager();\n      const annotationList = annotationManager.getAnnotationsList();\n      const widgetToDelete = annotationList.filter((annotationToFilter) => {\n        return annotationToFilter.getCustomData('trn-editing-rectangle-id') === annotation.Id;\n      });\n      annotationManager.deleteAnnotations(widgetToDelete);\n    }\n  };\n\n  const resetFields = () => {\n    setFields([]);\n    addNonSignedFields();\n  };\n\n  useEffect(() => {\n    // This ensures that when the document loads, the state of this component is\n    // updated accordingly\n    core.addEventListener('documentLoaded', onDocumentLoaded);\n    core.addEventListener('documentUnloaded', onDocumentUnloaded);\n    core.addEventListener('annotationChanged', onAnnotationChanged);\n    core.addEventListener('formFieldCreationModeStarted', resetFields);\n    core.addEventListener('formFieldCreationModeEnded', resetFields);\n    return () => {\n      core.removeEventListener('documentLoaded', onDocumentLoaded);\n      core.removeEventListener('documentUnloaded', onDocumentUnloaded);\n      core.removeEventListener('annotationChanged', onAnnotationChanged);\n      core.removeEventListener('formFieldCreationModeStarted', resetFields);\n      core.removeEventListener('formFieldCreationModeEnded', resetFields);\n    };\n  }, [onDocumentUnloaded]);\n\n  useEffect(() => {\n    // Need certificates for PDFNet to verify against, and for the document\n    // to be loaded in order to iterate through the signature fields in the\n    // document\n    if (document) {\n      // We need to wait for the annotationsLoaded event, otherwise the\n      // Field will not exist in the document\n      core.getAnnotationsLoadedPromise().then(() => {\n        setShowSpinner(true);\n        setVerificationResult(document, certificate, trustLists, currentLanguage, revocationChecking, revocationProxyPrefix, dispatch)\n          .then(async (verificationResult) => {\n            const fieldManager = core.getAnnotationManager().getFieldManager();\n            setFields(Object.keys(verificationResult).map((fieldName) => fieldManager.getField(fieldName)));\n            setCertificateErrorMessage('');\n          })\n          .catch((e) => {\n            if (e && e.message) {\n              setCertificateErrorMessage(e.message);\n            } else {\n              console.error(e);\n            }\n          })\n          .then(() => {\n            addNonSignedFields();\n          })\n          .finally(() => {\n            setShowSpinner(false);\n          });\n      });\n    } else {\n      setShowSpinner(true);\n    }\n  }, [certificate, document, dispatch, currentLanguage]);\n\n  if (isDisabled) {\n    return null;\n  }\n\n  /**\n   * Returns a JSX element if document loading is not complete, or an error\n   * occurs, otherwise nothing is returned, indicating that information about\n   * one or more signature will be returned from this component\n   */\n  const renderLoadingOrErrors = () => {\n    let result;\n    if (showSpinner) {\n      result = <Spinner />;\n    } else if (certificateErrorMessage === 'Error reading the local certificate') {\n      result = translate('digitalSignatureVerification.panelMessages.localCertificateError');\n    } else if (certificateErrorMessage === 'Download Failed') {\n      result = translate('digitalSignatureVerification.panelMessages.certificateDownloadError');\n    } else if (!fields.length) {\n      result = translate('digitalSignatureVerification.panelMessages.noSignatureFields');\n    } else {\n      /**\n       * If document has completed loading, there are no errors, and there are\n       * signature fields, this function does not need to return anything\n       */\n      return null;\n    }\n\n    return (\n      <div className=\"empty-panel-container\">\n        <Icon className=\"empty-icon\" glyph={panelData[panelNames.SIGNATURE].icon}/>\n        <div className=\"empty-message\">{result}</div>\n      </div>\n    );\n  };\n\n  return (\n    <div\n      className=\"Panel SignaturePanel\"\n      data-element=\"signaturePanel\"\n    >\n      {renderLoadingOrErrors()}\n      {\n        !showSpinner && fields.length > 0 && (\n          fields.map((field, index) => {\n            return (\n              <WidgetInfo\n                key={index}\n                name={field.name}\n                collapsible\n                field={field}\n              />\n            );\n          })\n        )\n      }\n    </div>\n  );\n};\n\nexport default SignaturePanel;\n", "import SignaturePanel from './SignaturePanel';\nimport Spinner from './Spinner';\nimport { renderPermissionStatus } from './WidgetInfo';\n\nexport default SignaturePanel;\n\nexport {\n  renderPermissionStatus,\n  SignaturePanel,\n  Spinner\n};\n", "var api = require(\"!../../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../../node_modules/css-loader/index.js!../../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../../node_modules/sass-loader/dist/cjs.js!./SignatureIcon.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".signature-icon .badge{color:#fff}.signature-icon .medium{width:18px;height:18px}.signature-icon .small{width:16px;height:16px}\", \"\"]);\n\n// exports\n", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./SignatureValidationModal.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".open.SignatureValidationModal{visibility:visible}.closed.SignatureValidationModal{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.SignatureValidationModal .footer .modal-button.confirm:hover{background:var(--primary-button-hover);border-color:var(--primary-button-hover);color:var(--gray-0)}.SignatureValidationModal .footer .modal-button.confirm{background:var(--primary-button);border-color:var(--primary-button);color:var(--primary-button-text)}.SignatureValidationModal .footer .modal-button.confirm.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.SignatureValidationModal .footer .modal-button.confirm.disabled span{color:var(--primary-button-text)}.SignatureValidationModal .footer .modal-button.cancel:hover,.SignatureValidationModal .footer .modal-button.secondary-btn-custom:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6)}.SignatureValidationModal .footer .modal-button.cancel,.SignatureValidationModal .footer .modal-button.secondary-btn-custom{border:none;box-shadow:inset 0 0 0 1px var(--primary-button);color:var(--primary-button)}.SignatureValidationModal .footer .modal-button.cancel.disabled,.SignatureValidationModal .footer .modal-button.secondary-btn-custom.disabled{cursor:default;border:none;box-shadow:inset 0 0 0 1px rgba(43,115,171,.5);color:rgba(43,115,171,.5)}.SignatureValidationModal .footer .modal-button.cancel.disabled span,.SignatureValidationModal .footer .modal-button.secondary-btn-custom.disabled span{color:rgba(43,115,171,.5)}.SignatureValidationModal{position:fixed;left:0;bottom:0;z-index:100;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background:var(--modal-negative-space)}.SignatureValidationModal .modal-container .wrapper .modal-content{padding:10px}.SignatureValidationModal .footer{display:flex;flex-direction:row;justify-content:flex-end;width:100%;margin-top:13px}.SignatureValidationModal .footer.modal-footer{padding:16px;margin:0;border-top:1px solid var(--divider)}.SignatureValidationModal .footer .modal-button{display:flex;justify-content:center;align-items:center;padding:6px 18px;margin:8px 0 0;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;height:30px;cursor:pointer}.SignatureValidationModal .footer .modal-button.confirm{margin-left:4px}.SignatureValidationModal .footer .modal-button.secondary-btn-custom{border-radius:4px;padding:2px 20px 4px;cursor:pointer}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignatureValidationModal .footer .modal-button{padding:23px 8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SignatureValidationModal .footer .modal-button{padding:23px 8px}}.SignatureValidationModal .swipe-indicator{background:var(--swipe-indicator-bg);border-radius:2px;height:4px;width:38px;position:absolute;top:12px;margin-left:auto;margin-right:auto;left:0;right:0}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignatureValidationModal .swipe-indicator{display:none}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .SignatureValidationModal .swipe-indicator{display:none}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignatureValidationModal .swipe-indicator{width:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SignatureValidationModal .swipe-indicator{width:32px}}.SignatureValidationModal .container{display:flex;flex-direction:column;justify-content:space-around;border-radius:4px;min-width:350px;padding:20px;max-width:630px}.SignatureValidationModal .validation-header{position:relative;display:flex;justify-content:left;align-items:center;padding-bottom:20px;font-size:16px}.SignatureValidationModal .validation-header [data-element=signatureValidationModalCloseButton]{position:absolute;right:0}.SignatureValidationModal .validation-header [data-element=signatureValidationModalCloseButton]:hover{background:none}.SignatureValidationModal .summary-box{position:relative;display:flex;border:1px solid;border-color:var(--gray-4);border-radius:5px;padding:16px;font-weight:700}.SignatureValidationModal .summary-box>:not(:first-child){margin-left:8px}.SignatureValidationModal .validation-header-valid{background-color:#8dd88d}.SignatureValidationModal .validation-header-warning{background-color:#e2b719}.SignatureValidationModal .validation-header-error{background-color:#ff7979}.SignatureValidationModal .validation-header-unknown{background-color:#ddd}.SignatureValidationModal .body{margin-top:16px}.SignatureValidationModal div.body>div.section:first-child{margin-top:0}.SignatureValidationModal div.body>div.section{margin:16px;padding-bottom:16px;border-bottom:1px solid var(--gray-5)}.SignatureValidationModal div.body>div.section:last-child{margin-bottom:0}.SignatureValidationModal div.body>div.section>p{font-size:13px;margin:8px 0}.SignatureValidationModal div.body>div.section>p:last-child{margin:0}.SignatureValidationModal .header{font-weight:700}.SignatureValidationModal .modal-footer{display:flex;justify-content:flex-end;margin-top:32px}.SignatureValidationModal .modal-footer .close-modal-button{background:var(--primary-button);border:1px;border-color:var(--primary-button);border-radius:4px;padding:0 16px;height:32px;width:-moz-fit-content;width:fit-content;display:flex;align-items:center;justify-content:center;position:relative;color:var(--primary-button-text);cursor:pointer}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignatureValidationModal .modal-footer .close-modal-button{height:40px;width:128px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SignatureValidationModal .modal-footer .close-modal-button{height:40px;width:128px}}.SignatureValidationModal .modal-footer .close-modal-button:hover{background:var(--primary-button-hover)}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React from 'react';\nimport PropTypes from 'prop-types';\n\nimport Icon from 'components/Icon';\n\nimport './SignatureIcon.scss';\n\nconst propTypes = {\n  badge: PropTypes.string,\n  size: PropTypes.string,\n};\n\nconst SignatureIcon = ({ badge, size = 'medium' }) => (\n  <div className=\"signature-icon\">\n    {badge && <Icon glyph={badge} className={`badge ${size}`} />}\n  </div>\n);\n\nSignatureIcon.propTypes = propTypes;\n\nexport default SignatureIcon;\n", "import SignatureIcon from './SignatureIcon';\n\nexport default SignatureIcon;\n", "import React, { useEffect } from 'react';\nimport { useDispatch, useSelector, shallowEqual } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\nimport classNames from 'classnames';\nimport selectors from 'selectors';\nimport DataElements from 'constants/dataElement';\nimport Button from 'components/Button';\nimport ModalWrapper from 'components/ModalWrapper';\n\nimport {\n  renderPermissionStatus,\n  Spinner,\n} from 'components/SignaturePanel';\nimport SignatureIcon from 'components/SignaturePanel/SignatureIcon';\n\nimport actions from 'actions';\n\nimport './SignatureValidationModal.scss';\nimport useFocusOnClose from 'src/hooks/useFocusOnClose';\n\nconst SignatureValidationModal = () => {\n  const [translate] = useTranslation();\n\n  const [isOpen, verificationResult] = useSelector(\n    (state) => {\n      const { validationModalWidgetName } = state.digitalSignatureValidation;\n      return [\n        selectors.isElementOpen(state, DataElements.SIGNATURE_VALIDATION_MODAL),\n        selectors.getVerificationResult(state, validationModalWidgetName),\n      ];\n    },\n    shallowEqual,\n  );\n\n  const dispatch = useDispatch();\n  const closeModal = useFocusOnClose(() => {\n    dispatch(actions.closeElements([DataElements.SIGNATURE_VALIDATION_MODAL]));\n  });\n\n  useEffect(() => {\n    if (isOpen) {\n      dispatch(\n        actions.closeElements([\n          DataElements.SIGNATURE_MODAL,\n          DataElements.LOADING_MODAL,\n          DataElements.PRINT_MODAL,\n          DataElements.ERROR_MODAL,\n          DataElements.PASSWORD_MODAL,\n        ])\n      );\n    }\n  }, [dispatch, isOpen]);\n\n  /**\n   * @todo Figure out if this useEffect is still needed? Component appears to be\n   * operating normally without it.\n   */\n  /*\n  useEffect(() => {\n    const onDigitalSignatureAvailable = widget => {\n      setWidgetName(widget.getField().name);\n      dispatch(actions.openElements(['signatureValidationModal']));\n    };\n\n    core.addEventListener(\n      'digitalSignatureAvailable',\n      onDigitalSignatureAvailable,\n    );\n    return () => core.removeEventListener(\n      'digitalSignatureAvailable',\n      onDigitalSignatureAvailable,\n    );\n  }, [dispatch]);\n  */\n\n  const {\n    badgeIcon,\n    verificationStatus,\n    permissionStatus,\n    isCertification,\n    documentPermission,\n    trustVerificationResultString,\n    timeOfTrustVerificationEnum,\n    trustVerificationTime,\n    digestAlgorithm,\n    digestStatus,\n    documentStatus,\n    trustStatus,\n    signerName,\n  } = verificationResult;\n  const {\n    DigestAlgorithm,\n    DigitalSignatureField,\n    VerificationOptions,\n    VerificationResult,\n  } = window.Core.PDFNet;\n  const {\n    ModificationPermissionsStatus,\n    TrustStatus,\n    DigestStatus,\n    DocumentStatus,\n  } = VerificationResult;\n  const { TimeMode } = VerificationOptions;\n\n  const renderSignatureSummary = () => {\n    let status;\n    switch (badgeIcon) {\n      case 'digital_signature_valid':\n        status = translate('digitalSignatureModal.valid');\n        break;\n      case 'digital_signature_warning':\n        status = translate('digitalSignatureModal.unknown');\n        break;\n      case 'digital_signature_error':\n        status = translate('digitalSignatureModal.invalid');\n        break;\n      default:\n        status = translate('digitalSignatureModal.unknown');\n    }\n\n    const type = isCertification\n      ? translate('digitalSignatureModal.certification')\n      : translate('digitalSignatureModal.signature');\n\n    return (\n      <div>\n        <div className=\"summary-box\">\n          <SignatureIcon badge={badgeIcon} size=\"medium\" />\n          <div>\n            {\n              translate(\n                'digitalSignatureModal.summaryBox.summary',\n                {\n                  type,\n                  status,\n                }\n              )\n            }\n            {\n              badgeIcon === 'digital_signature_valid'\n                ? translate(\n                  'digitalSignatureModal.summaryBox.signedBy',\n                  {\n                    name: signerName || translate('digitalSignatureModal.unknown'),\n                    interpolation: { escapeValue: false }\n                  },\n                ) : ''\n            }\n          </div>\n        </div>\n      </div>\n    );\n  };\n\n  const renderHeaderTitle = () => {\n    const typeCapitalized = isCertification\n      ? translate('digitalSignatureModal.Certification')\n      : translate('digitalSignatureModal.Signature');\n\n    return translate('digitalSignatureModal.title', { type: typeCapitalized });\n  };\n\n  /**\n   * Returns a message in a <p> tag corresponding to the enum value of\n   * documentPermission, which originates from the invocation of\n   * PDFNet.DigitalSignatureField.getDocumentPermissions\n   */\n  const renderDocumentPermission = () => {\n    if (!documentPermission) {\n      return;\n    }\n\n    let content = '';\n    const editor = isCertification ? 'certifier' : 'signer';\n\n    switch (documentPermission) {\n      case DigitalSignatureField.DocumentPermissions.e_no_changes_allowed:\n        content += translate('digitalSignatureModal.documentPermission.noChangesAllowed', { editor });\n        break;\n      case DigitalSignatureField.DocumentPermissions.e_formfilling_signing_allowed:\n        content += translate('digitalSignatureModal.documentPermission.formfillingSigningAllowed', { editor });\n        break;\n      case DigitalSignatureField.DocumentPermissions.e_annotating_formfilling_signing_allowed:\n        content += translate('digitalSignatureModal.documentPermission.annotatingFormfillingSigningAllowed', { editor });\n        break;\n      case DigitalSignatureField.DocumentPermissions.e_unrestricted:\n        content += translate('digitalSignatureModal.documentPermission.unrestricted', { editor });\n        break;\n    }\n\n    return <p>{content}</p>;\n  };\n\n  /**\n   * Returns a message in a <p> tag corresponding to the signature's trust\n   * verification result.\n   *\n   * If trustVerificationResultString is a falsy value (i.e. undefined, null or\n   * empty string), originating from the invocation of\n   * PDFNet.TrustVerificationResult.getResultString, then a message indicating\n   * no trust verification result is rendered.\n   *\n   * If a trust verification result is available, then the based on\n   * timeOfTrustVerificationEnum, which originates from the invocation of\n   * PDFNet.TrustVerificationResult.getTimeOfTrustVerificationEnum, an\n   * appropriate message is rendered\n   */\n  const renderTrustVerification = () => {\n    if (!trustVerificationResultString) {\n      return (\n        <p>{translate('digitalSignatureModal.trustVerification.none')}</p>\n      );\n    }\n\n    let content = '';\n    switch (timeOfTrustVerificationEnum) {\n      case (TimeMode.e_current):\n        content += translate(\n          'digitalSignatureModal.trustVerification.current',\n          { trustVerificationTime }\n        );\n        break;\n      case (TimeMode.e_signing):\n        content += translate(\n          'digitalSignatureModal.trustVerification.signing',\n          { trustVerificationTime }\n        );\n        break;\n      case (TimeMode.e_timestamp):\n        content += translate(\n          'digitalSignatureModal.trustVerification.timestamp',\n          { trustVerificationTime }\n        );\n        break;\n    }\n\n    return <p>{content}</p>;\n  };\n\n  /**\n   * Returns a message in a <p> tag corresponding to the signature's digest\n   * algorithm, which originates from the invocation of\n   * PDFNet.verificationResult.getDigestAlgorithm\n   */\n  const renderDigestAlgorithm = () => {\n    let content = translate('digitalSignatureModal.digestAlgorithm.preamble');\n\n    switch (digestAlgorithm) {\n      case DigestAlgorithm.Type.e_SHA1:\n        content += 'SHA1.';\n        break;\n      case DigestAlgorithm.Type.e_SHA256:\n        content += 'SHA256.';\n        break;\n      case DigestAlgorithm.Type.e_SHA384:\n        content += 'SHA384.';\n        break;\n      case DigestAlgorithm.Type.e_SHA512:\n        content += 'SHA512.';\n        break;\n      case DigestAlgorithm.Type.e_RIPEMD160:\n        content += 'RIPEMD160.';\n        break;\n      case DigestAlgorithm.Type.e_unknown_digest_algorithm:\n        content = translate('digitalSignatureModal.digestAlgorithm.unknown');\n        break;\n    }\n\n    return <p>{content}</p>;\n  };\n\n  /**\n   * Returns a message in a <p> tag corresponding to the boolean trust status of\n   * the signature, which originates from evaluating whether or not the value of\n   * PDFNet.verificationResult.getTrustStatus is set to the enum\n   * PDFNet.verificationResult.trustStatus.e_trust_verified\n   *\n   * Intentionally an un-used function, as @rdjericpdftron has noted that it\n   * is superfluous, but the method has been left behind in-case it could be\n   * leveraged in the future\n   */\n  /*\n  const renderSignerIdentityValidity = () => {\n    let content = translate('digitalSignatureModal.signerIdentity.preamble');\n    if (validSignerIdentity) {\n      content += translate('digitalSignatureModal.signerIdentity.valid');\n    } else {\n      content += translate('digitalSignatureModal.signerIdentity.unknown');\n    }\n    return <p>{content}</p>;\n  };\n  */\n\n  const renderDocumentStatus = () => {\n    let content;\n\n    switch (documentStatus) {\n      case DocumentStatus.e_no_error:\n        content = translate(\n          'digitalSignatureVerification.documentStatus.noError'\n        );\n        break;\n      case DocumentStatus.e_corrupt_file:\n        content = translate(\n          'digitalSignatureVerification.documentStatus.corruptFile'\n        );\n        break;\n      case DocumentStatus.e_unsigned:\n        content = translate(\n          'digitalSignatureVerification.documentStatus.unsigned'\n        );\n        break;\n      case DocumentStatus.e_bad_byteranges:\n        content = translate(\n          'digitalSignatureVerification.documentStatus.badByteRanges'\n        );\n        break;\n      case DocumentStatus.e_corrupt_cryptographic_contents:\n        content = translate(\n          'digitalSignatureVerification.documentStatus.corruptCryptographicContents'\n        );\n        break;\n    }\n\n    return <p>{content}</p>;\n  };\n\n  const renderDigestStatus = () => {\n    let content;\n\n    switch (digestStatus) {\n      case DigestStatus.e_digest_invalid:\n        content = translate(\n          'digitalSignatureVerification.digestStatus.digestInvalid'\n        );\n        break;\n      case DigestStatus.e_digest_verified:\n        content = translate(\n          'digitalSignatureVerification.digestStatus.digestVerified'\n        );\n        break;\n      case DigestStatus.e_digest_verification_disabled:\n        content = translate(\n          'digitalSignatureVerification.digestStatus.digestVerificationDisabled'\n        );\n        break;\n      case DigestStatus.e_weak_digest_algorithm_but_digest_verifiable:\n        content = translate(\n          'digitalSignatureVerification.digestStatus.weakDigestAlgorithmButDigestVerifiable'\n        );\n        break;\n      case DigestStatus.e_no_digest_status:\n        content = translate(\n          'digitalSignatureVerification.digestStatus.noDigestStatus'\n        );\n        break;\n      case DigestStatus.e_unsupported_encoding:\n        content = translate(\n          'digitalSignatureVerification.digestStatus.unsupportedEncoding'\n        );\n        break;\n    }\n\n    return <p>{content}</p>;\n  };\n\n  const renderTrustStatus = () => {\n    const verificationType = isCertification\n      ? translate('digitalSignatureVerification.certifier')\n      : translate('digitalSignatureVerification.signer');\n    let content;\n\n    switch (trustStatus) {\n      case TrustStatus.e_trust_verified:\n        content = translate(\n          'digitalSignatureVerification.trustStatus.trustVerified',\n          { verificationType },\n        );\n        break;\n      case TrustStatus.e_untrusted:\n        content = translate(\n          'digitalSignatureVerification.trustStatus.untrusted'\n        );\n        break;\n      case TrustStatus.e_trust_verification_disabled:\n        content = translate(\n          'digitalSignatureVerification.trustStatus.trustVerificationDisabled'\n        );\n        break;\n      case TrustStatus.e_no_trust_status:\n        content = translate(\n          'digitalSignatureVerification.trustStatus.noTrustStatus'\n        );\n        break;\n    }\n\n    return <p>{content}</p>;\n  };\n\n  const renderDocumentIntegrityHeader = () => (\n    <p\n      className=\"header\"\n    >\n      {translate('digitalSignatureModal.header.documentIntegrity')}\n    </p>\n  );\n\n  const renderIdentitiesTrustHeader = () => (\n    <p\n      className=\"header\"\n    >\n      {translate('digitalSignatureModal.header.identitiesTrust')}\n    </p>\n  );\n\n  const renderGeneralErrorsHeader = () => (\n    <p\n      className=\"header\"\n    >\n      {translate('digitalSignatureModal.header.generalErrors')}\n    </p>\n  );\n\n  const renderDigestStatusHeader = () => (\n    <p\n      className=\"header\"\n    >\n      {translate('digitalSignatureModal.header.digestStatus')}\n    </p>\n  );\n\n  const renderModalBody = () => {\n    if (typeof verificationStatus === 'undefined') {\n      return (\n        <div className=\"center\">\n          <Spinner />\n        </div>\n      );\n    }\n    return (\n      <>\n        <div className=\"body\">\n          <div className=\"section\">\n            {renderDocumentIntegrityHeader()}\n            {\n              renderPermissionStatus({\n                isCertification,\n                ModificationPermissionsStatus,\n                permissionStatus,\n                translate,\n              })\n            }\n            {renderDocumentPermission()}\n          </div>\n          <div className=\"section\">\n            {renderIdentitiesTrustHeader()}\n            {renderTrustStatus()}\n            {renderTrustVerification()}\n          </div>\n          <div className=\"section\">\n            {renderGeneralErrorsHeader()}\n            {renderDocumentStatus()}\n          </div>\n          <div className=\"section\">\n            {renderDigestStatusHeader()}\n            {renderDigestStatus()}\n            {renderDigestAlgorithm()}\n          </div>\n        </div>\n        <div className=\"modal-footer\">\n          <Button\n            className=\"close-modal-button\"\n            onClick={closeModal}\n            label={translate('action.close')}\n          />\n        </div>\n      </>\n    );\n  };\n\n  return (\n    <div\n      className={classNames({\n        Modal: true,\n        SignatureValidationModal: true,\n        open: isOpen,\n        closed: !isOpen,\n      })}\n      data-element={DataElements.SIGNATURE_VALIDATION_MODAL}\n    >\n      <ModalWrapper\n        title={renderHeaderTitle()}\n        closeHandler={closeModal}\n        onCloseClick={closeModal}\n        isOpen={isOpen}\n        swipeToClose\n      >\n        <div className=\"container\">\n          {renderSignatureSummary()}\n          {renderModalBody()}\n        </div>\n      </ModalWrapper>\n    </div>\n  );\n};\n\nexport default SignatureValidationModal;\n", "import SignatureValidationModal from './SignatureValidationModal';\n\nexport default SignatureValidationModal;\n"], "sourceRoot": ""}