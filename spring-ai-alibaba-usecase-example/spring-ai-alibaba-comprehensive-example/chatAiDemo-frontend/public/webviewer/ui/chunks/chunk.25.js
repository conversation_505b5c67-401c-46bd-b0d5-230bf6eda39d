(window.webpackJsonp=window.webpackJsonp||[]).push([[25,6,7,11,17,81],{1476:function(e,t,n){"use strict";var o=n(0),r=n.n(o).a.createContext();t.a=r},1494:function(e,t,n){"use strict";n(23),n(8),n(24),n(343),n(435),n(437);t.a=function(e,t){var n={},o=e.getContents().ops,r=0;o.forEach((function(e){var t,o=e.attributes,i=null===(t=e.insert)||void 0===t?void 0:t.mention,a=e.insert;if(i){var l=e.insert.mention;a=l.denotationChar+l.value}var c={};(null==o?void 0:o.bold)&&(c["font-weight"]="bold"),(null==o?void 0:o.italic)&&(c["font-style"]="italic"),(null==o?void 0:o.color)&&(c.color=o.color),(null==o?void 0:o.underline)&&(c["text-decoration"]="word"),(null==o?void 0:o.strike)&&(c["text-decoration"]?c["text-decoration"]="".concat(c["text-decoration"]," line-through"):c["text-decoration"]="line-through"),(null==o?void 0:o.size)&&(c["font-size"]=o.size),(null==o?void 0:o.font)&&(c["font-family"]=o.font),n[r]=c,r+=a.length})),t.setRichTextStyle(n)}},1497:function(e,t,n){"use strict";n(18),n(106),n(151),n(88),n(19),n(11),n(13),n(8),n(14),n(10),n(9),n(12),n(16),n(15),n(20),n(26),n(27),n(25),n(22),n(29),n(28),n(45),n(23),n(24),n(48),n(46);var o=n(0),r=n.n(o),i=n(4),a=n.n(i),l=n(6),c=n(123),s=n.n(c),u=n(1476),d=n(3),p=(n(83),n(38),n(35),n(40),n(47),n(39),n(82),n(36),n(57),n(63),n(64),n(65),n(66),n(62),n(1588)),h=n.n(p),f=(n(1589),n(339)),m=n(44),g=n(428),v=n(5),y=n(21);n(1552),n(1554);function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function w(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){P(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function x(e){return(x="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function S(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,s=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){s=!0,r=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw r}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return _(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return _(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}function C(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */C=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},r="function"==typeof Symbol?Symbol:{},i=r.iterator||"@@iterator",a=r.asyncIterator||"@@asyncIterator",l=r.toStringTag||"@@toStringTag";function c(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,n){return e[t]=n}}function s(e,t,n,r){var i=t&&t.prototype instanceof p?t:p,a=Object.create(i.prototype),l=new E(r||[]);return o(a,"_invoke",{value:S(e,n,l)}),a}function u(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=s;var d={};function p(){}function h(){}function f(){}var m={};c(m,i,(function(){return this}));var g=Object.getPrototypeOf,v=g&&g(g(k([])));v&&v!==t&&n.call(v,i)&&(m=v);var y=f.prototype=p.prototype=Object.create(m);function b(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function w(e,t){var r;o(this,"_invoke",{value:function(o,i){function a(){return new t((function(r,a){!function o(r,i,a,l){var c=u(e[r],e,i);if("throw"!==c.type){var s=c.arg,d=s.value;return d&&"object"==x(d)&&n.call(d,"__await")?t.resolve(d.__await).then((function(e){o("next",e,a,l)}),(function(e){o("throw",e,a,l)})):t.resolve(d).then((function(e){s.value=e,a(s)}),(function(e){return o("throw",e,a,l)}))}l(c.arg)}(o,i,r,a)}))}return r=r?r.then(a,a):a()}})}function S(e,t,n){var o="suspendedStart";return function(r,i){if("executing"===o)throw new Error("Generator is already running");if("completed"===o){if("throw"===r)throw i;return T()}for(n.method=r,n.arg=i;;){var a=n.delegate;if(a){var l=_(a,n);if(l){if(l===d)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===o)throw o="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o="executing";var c=u(e,t,n);if("normal"===c.type){if(o=n.done?"completed":"suspendedYield",c.arg===d)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o="completed",n.method="throw",n.arg=c.arg)}}}function _(e,t){var n=t.method,o=e.iterator[n];if(void 0===o)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,_(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),d;var r=u(o,e.iterator,t.arg);if("throw"===r.type)return t.method="throw",t.arg=r.arg,t.delegate=null,d;var i=r.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,d):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,d)}function O(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function q(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function E(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(O,this),this.reset(!0)}function k(e){if(e){var t=e[i];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,r=function t(){for(;++o<e.length;)if(n.call(e,o))return t.value=e[o],t.done=!1,t;return t.value=void 0,t.done=!0,t};return r.next=r}}return{next:T}}function T(){return{value:void 0,done:!0}}return h.prototype=f,o(y,"constructor",{value:f,configurable:!0}),o(f,"constructor",{value:h,configurable:!0}),h.displayName=c(f,l,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===h||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,c(e,l,"GeneratorFunction")),e.prototype=Object.create(y),e},e.awrap=function(e){return{__await:e}},b(w.prototype),c(w.prototype,a,(function(){return this})),e.AsyncIterator=w,e.async=function(t,n,o,r,i){void 0===i&&(i=Promise);var a=new w(s(t,n,o,r),i);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},b(y),c(y,l,"Generator"),c(y,i,(function(){return this})),c(y,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var o in t)n.push(o);return n.reverse(),function e(){for(;n.length;){var o=n.pop();if(o in t)return e.value=o,e.done=!1,e}return e.done=!0,e}},e.values=k,E.prototype={constructor:E,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(q),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function o(n,o){return a.type="throw",a.arg=e,t.next=n,o&&(t.method="next",t.arg=void 0),!!o}for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r],a=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(l&&c){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(e,t){for(var o=this.tryEntries.length-1;o>=0;--o){var r=this.tryEntries[o];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,d):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),d},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),q(n),d}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var r=o.arg;q(n)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:k(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),d}},e}function O(e,t,n,o,r,i,a){try{var l=e[i](a),c=l.value}catch(e){return void n(e)}l.done?t(c):Promise.resolve(c).then(o,r)}function q(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,N(o.key),o)}}function E(e,t,n){return t&&q(e.prototype,t),n&&q(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function k(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function T(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&R(e,t)}function R(e,t){return(R=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function I(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,o=j(e);if(t){var r=j(this).constructor;n=Reflect.construct(o,arguments,r)}else n=o.apply(this,arguments);return A(this,n)}}function A(e,t){if(t&&("object"===x(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function j(e){return(j=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function P(e,t,n){return(t=N(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function N(e){var t=function(e,t){if("object"!==x(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!==x(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===x(t)?t:String(t)}var L=[],M=["background","bold","color","font","code","italic","link","size","strike","script","underline","blockquote","header","indent","list","align","direction","code-block","formula","mention"],z=p.Quill.import("modules/keyboard"),H=function(e){T(n,e);var t=I(n);function n(){return k(this,n),t.apply(this,arguments)}return E(n)}(z);P(H,"DEFAULTS",w(w({},z.DEFAULTS),{},{bindings:w(w({},z.DEFAULTS.bindings),{},{"list autofill":void 0})})),p.Quill.register("modules/keyboard",H,!0);var D=p.Quill.import("modules/clipboard"),G=window.Core.quillShadowDOMWorkaround,F=function(e){T(n,e);var t=I(n);function n(e,o){return k(this,n),G(e),t.call(this,e,o)}return E(n)}(D);p.Quill.register("modules/clipboard",F,!0);var W={mention:{allowedChars:/^[A-Za-z\sÅÄÖåäö0-9\-_]*$/,mentionDenotationChars:["@","#"],mentionContainerClass:"mention__element",mentionListClass:"mention__suggestions__list",listItemClass:"mention__suggestions__item",renderItem:function(e){var t=document.createElement("div");if(t.innerText=e.value,e.email){var n=document.createElement("p");n.innerText=e.email,n.className="email",t.appendChild(n)}return t},source:function(e,t){return(n=C().mark((function n(){var o,r;return C().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return o=f.a.getMentionLookupCallback(),n.next=3,o(L,e);case 3:r=n.sent,t(r,e);case 5:case"end":return n.stop()}}),n)})),function(){var e=this,t=arguments;return new Promise((function(o,r){var i=n.apply(e,t);function a(e){O(i,o,r,a,l,"next",e)}function l(e){O(i,o,r,a,l,"throw",e)}a(void 0)}))})();var n}}},U=r.a.forwardRef((function(e,t){var n=e.value,o=void 0===n?"":n,i=e.onChange,a=e.onKeyDown,c=e.onBlur,s=e.onFocus,u=e.userData,p=e.isReply,f=S(Object(g.a)(),1)[0],b=Object(l.e)((function(e){return d.a.isElementDisabled(e,v.a.NotesPanel.ADD_REPLY_ATTACHMENT_BUTTON)}));L=u;if(o&&o.split("\n").length>1){var w=o.split("\n");o=w.map((function(e){var t=document.createElement("p");return t.innerText=e||"\n",t.outerHTML})).join("")}return r.a.createElement("div",{className:"comment-textarea",onBlur:c,onFocus:s,onClick:function(e){e.preventDefault(),e.stopPropagation()},onScroll:function(e){e.preventDefault(),e.stopPropagation()}},r.a.createElement(h.a,{className:"comment-textarea ql-container ql-editor",style:{overflowY:"visible"},ref:function(e){return e&&(e.getEditor().root.ariaLabel="".concat(f(p?"action.reply":"action.comment"))),t(e)},modules:u&&u.length>0?W:{},theme:"snow",value:o,placeholder:"".concat(f(p?"action.reply":"action.comment"),"..."),onChange:i,onKeyDown:a,formats:M}),p&&!b&&r.a.createElement(m.a,{className:"add-attachment",dataElement:v.a.NotesPanel.ADD_REPLY_ATTACHMENT_BUTTON,img:"ic_fileattachment_24px",title:"".concat(f("action.add")," ").concat(f("option.type.fileattachment")),onClick:function(){var e;null===(e=Object(y.a)().querySelector("#reply-attachment-picker"))||void 0===e||e.click()}}))}));U.displayName="CommentTextarea";var B=U;function V(e){return(V="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Y(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function K(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Y(Object(n),!0).forEach((function(t){$(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Y(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function $(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==V(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!==V(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===V(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function X(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,s=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){s=!0,r=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw r}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Z(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Z(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Z(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}var J={value:a.a.string,placeholder:a.a.string,onChange:a.a.func.isRequired,onBlur:a.a.func,onFocus:a.a.func,onSubmit:a.a.func},Q=r.a.forwardRef((function(e,t){var n=X(Object(l.e)((function(e){return[d.a.getUserData(e),d.a.isNoteSubmissionWithEnterEnabled(e),d.a.getAutoFocusNoteOnAnnotationSelection(e),d.a.getIsNoteEditing(e)]}),l.c),2),i=n[0],a=n[1],c=Object(o.useContext)(u.a).resize,p=Object(o.useRef)(),h=Object(o.useRef)();Object(o.useLayoutEffect)((function(){var e,t,n=null===(e=p.current)||void 0===e||null===(t=e.editor)||void 0===t?void 0:t.container.firstElementChild,o=(null==n?void 0:n.getBoundingClientRect())||{};h.current&&h.current!==o.height&&c(),h.current=o.height}),[e.value,c]);var m=K(K({},e),{},{ref:function(e){p.current=e,t(e)},onChange:s()((function(t,n,o,r){if(t=t.replace(/&nbsp;/g," "),p.current){var i="";if(r&&""===r.getText().trim()&&"<p><br></p>"===t||(i=t.target?t.target.value:t),e.onChange(i),f.a.doesDeltaContainMention(n.ops)){var a,l=f.a.getFormattedTextFromDeltas(n.ops),c=f.a.extractMentionDataFromStr(l),s=r.getText().length+c.plainTextValue.length,u=null===(a=p.current)||void 0===a?void 0:a.editor;setTimeout((function(){return null==u?void 0:u.setSelection(s,s)}),1)}}}),100),onKeyDown:function(t){if(13===t.which){var n=a,o=t.metaKey||t.ctrlKey;(n||o)&&e.onSubmit(t)}},userData:i});return r.a.createElement(r.a.Fragment,null,r.a.createElement(B,m))}));Q.displayName="NoteTextarea",Q.propTypes=J;var ee=Q;t.a=ee},1498:function(e,t,n){"use strict";var o=n(1);t.a=function(e){var t=window.Core.Annotations.FreeTextAnnotation;if(e instanceof t&&e.getAutoSizeType()!==t.AutoSizeTypes.NONE){var n=o.a.getDocument(),r=e.PageNumber,i=n.getPageInfo(r),a=n.getPageMatrix(r),l=n.getPageRotation(r);e.fitText(i,a,l)}}},1505:function(e,t,n){"use strict";n(151),n(83);t.a=function(e,t){return[e,t.isOpen?"open":"closed"].join(" ").trim()}},1514:function(e,t,n){"use strict";(function(e){function o(t,n){var o,r,i,a=void 0!==(o=void 0!==n?n:"undefined"!=typeof window?window:"undefined"!=typeof self?self:e).document&&o.document.attachEvent;if(!a){var l=(i=o.requestAnimationFrame||o.mozRequestAnimationFrame||o.webkitRequestAnimationFrame||function(e){return o.setTimeout(e,20)},function(e){return i(e)}),c=(r=o.cancelAnimationFrame||o.mozCancelAnimationFrame||o.webkitCancelAnimationFrame||o.clearTimeout,function(e){return r(e)}),s=function(e){var t=e.__resizeTriggers__,n=t.firstElementChild,o=t.lastElementChild,r=n.firstElementChild;o.scrollLeft=o.scrollWidth,o.scrollTop=o.scrollHeight,r.style.width=n.offsetWidth+1+"px",r.style.height=n.offsetHeight+1+"px",n.scrollLeft=n.scrollWidth,n.scrollTop=n.scrollHeight},u=function(e){if(!(e.target.className&&"function"==typeof e.target.className.indexOf&&e.target.className.indexOf("contract-trigger")<0&&e.target.className.indexOf("expand-trigger")<0)){var t=this;s(this),this.__resizeRAF__&&c(this.__resizeRAF__),this.__resizeRAF__=l((function(){(function(e){return e.offsetWidth!=e.__resizeLast__.width||e.offsetHeight!=e.__resizeLast__.height})(t)&&(t.__resizeLast__.width=t.offsetWidth,t.__resizeLast__.height=t.offsetHeight,t.__resizeListeners__.forEach((function(n){n.call(t,e)})))}))}},d=!1,p="",h="animationstart",f="Webkit Moz O ms".split(" "),m="webkitAnimationStart animationstart oAnimationStart MSAnimationStart".split(" "),g=o.document.createElement("fakeelement");if(void 0!==g.style.animationName&&(d=!0),!1===d)for(var v=0;v<f.length;v++)if(void 0!==g.style[f[v]+"AnimationName"]){p="-"+f[v].toLowerCase()+"-",h=m[v],d=!0;break}var y="resizeanim",b="@"+p+"keyframes "+y+" { from { opacity: 0; } to { opacity: 0; } } ",w=p+"animation: 1ms "+y+"; "}return{addResizeListener:function(e,n){if(a)e.attachEvent("onresize",n);else{if(!e.__resizeTriggers__){var r=e.ownerDocument,i=o.getComputedStyle(e);i&&"static"==i.position&&(e.style.position="relative"),function(e){if(!e.getElementById("detectElementResize")){var n=(b||"")+".resize-triggers { "+(w||"")+'visibility: hidden; opacity: 0; } .resize-triggers, .resize-triggers > div, .contract-trigger:before { content: " "; display: block; position: absolute; top: 0; left: 0; height: 100%; width: 100%; overflow: hidden; z-index: -1; } .resize-triggers > div { background: #eee; overflow: auto; } .contract-trigger:before { width: 200%; height: 200%; }',o=e.head||e.getElementsByTagName("head")[0],r=e.createElement("style");r.id="detectElementResize",r.type="text/css",null!=t&&r.setAttribute("nonce",t),r.styleSheet?r.styleSheet.cssText=n:r.appendChild(e.createTextNode(n)),o.appendChild(r)}}(r),e.__resizeLast__={},e.__resizeListeners__=[],(e.__resizeTriggers__=r.createElement("div")).className="resize-triggers";var l='<div class="expand-trigger"><div></div></div><div class="contract-trigger"></div>';if(window.trustedTypes){var c=trustedTypes.createPolicy("react-virtualized-auto-sizer",{createHTML:function(){return l}});e.__resizeTriggers__.innerHTML=c.createHTML("")}else e.__resizeTriggers__.innerHTML=l;e.appendChild(e.__resizeTriggers__),s(e),e.addEventListener("scroll",u,!0),h&&(e.__resizeTriggers__.__animationListener__=function(t){t.animationName==y&&s(e)},e.__resizeTriggers__.addEventListener(h,e.__resizeTriggers__.__animationListener__))}e.__resizeListeners__.push(n)}},removeResizeListener:function(e,t){if(a)e.detachEvent("onresize",t);else if(e.__resizeListeners__.splice(e.__resizeListeners__.indexOf(t),1),!e.__resizeListeners__.length){e.removeEventListener("scroll",u,!0),e.__resizeTriggers__.__animationListener__&&(e.__resizeTriggers__.removeEventListener(h,e.__resizeTriggers__.__animationListener__),e.__resizeTriggers__.__animationListener__=null);try{e.__resizeTriggers__=!e.removeChild(e.__resizeTriggers__)}catch(e){}}}}}n.d(t,"a",(function(){return o}))}).call(this,n(118))},1521:function(e,t,n){"use strict";var o=n(1);t.a=function(e,t,n){var r=e,i=r.getCalculatedFontSize();n?r.FontSize=i:r.switchToAutoFontSize(),t(!n),o.a.getAnnotationManager().redrawAnnotation(r)}},1523:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));n(35);var o=n(30),r=n(1);function i(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,i=new window.Core.Annotations.StickyAnnotation;i.InReplyTo=e.Id,i.X=e.X,i.Y=e.Y,i.PageNumber=e.PageNumber,i.Subject="Sticky Note",i.Author=r.a.getCurrentUser(),i.State=t,i.StateModel="Marked"===t||"Unmarked"===t?"Marked":"Review",i.Hidden=!0,i.enableSkipAutoLink();var a=r.a.getDisplayAuthor(i.Author,n),l=o.a.t("option.state.".concat(t.toLowerCase())),c="".concat(l," ").concat(o.a.t("option.state.setBy")," ").concat(a);return i.setContents(c),i}},1524:function(e,t,n){"use strict";var o=n(0),r=n.n(o),i=n(4),a=n.n(i),l=(n(1534),{renderContent:a.a.func,children:a.a.node}),c=function(e){var t=e.renderContent?e.renderContent():e.children;return r.a.createElement("h4",{className:"ListSeparator"},t)};c.propTypes=l;var s=r.a.memo(c);t.a=s},1525:function(e,t,n){"use strict";n(35),n(38),n(26),n(27),n(11),n(13),n(8),n(25),n(22),n(29),n(28),n(45),n(23),n(24),n(48),n(46),n(14),n(10),n(9),n(12);var o=n(0),r=n(6),i=n(4),a=n.n(i),l=n(5),c=n(2),s=n(3);function u(e){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function d(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function p(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?d(Object(n),!0).forEach((function(t){h(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):d(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function h(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==u(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!==u(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===u(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var f=function(e,t,n){return{icon:t,label:"option.state.".concat(e.toLowerCase()),title:"option.state.".concat(e.toLowerCase()),option:e,dataElement:n}},m=[f("Accepted","icon-annotation-status-accepted","noteStateFlyoutAcceptedOption"),f("Rejected","icon-annotation-status-rejected","noteStateFlyoutRejectedOption"),f("Cancelled","icon-annotation-status-cancelled","noteStateFlyoutCancelledOption"),f("Completed","icon-annotation-status-completed","noteStateFlyoutCompletedOption"),f("None","icon-annotation-status-none","noteStateFlyoutNoneOption"),f("Marked","icon-annotation-status-marked","noteStateFlyoutMarkedOption"),f("Unmarked","icon-annotation-status-unmarked","noteStateFlyoutUnmarkedOption")],g=function(e){var t=e.noteId,n=e.handleStateChange,i=void 0===n?function(){}:n,a=e.isMultiSelectMode,u=void 0!==a&&a,d=Object(r.d)(),h=u?"":"-".concat(t),f="".concat(l.a.NOTE_STATE_FLYOUT).concat(h),g=Object(r.e)((function(e){return s.a.getFlyout(e,f)}));return Object(o.useLayoutEffect)((function(){var e={dataElement:f,className:"NoteStateFlyout",items:m.map((function(e){return p(p({},e),{},{onClick:function(){return t=e.option,void i(t);var t}})}))};d(g?c.a.updateFlyout(e.dataElement,e):c.a.addFlyout(e))}),[i]),null};g.propTypes={noteId:a.a.string,handleStateChange:a.a.func,isMultiSelectMode:a.a.bool};var v=g;t.a=v},1529:function(e,t,n){"use strict";n(18),n(106),n(19),n(11),n(13),n(8),n(14),n(10),n(9),n(12),n(16),n(15),n(20);var o=n(0),r=n.n(o),i=n(428),a=n(17),l=n.n(a);n(1560);function c(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,s=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){s=!0,r=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw r}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return s(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return s(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}t.a=function(e){var t=e.children.replace(/\n$/,""),n=e.panelWidth,a=e.linesToBreak,s=e.renderRichText,u=e.richTextStyle,d=e.resize,p=e.style,h=e.comment,f=void 0!==h&&h,m=e.beforeContent,g=void 0===m?function(){}:m,v=c(Object(o.useState)(!1),2),y=v[0],b=v[1],w=c(Object(o.useState)(null),2),x=w[0],S=w[1],_=c(Object(o.useState)(null),2),C=_[0],O=_[1],q=c(Object(o.useState)(!1),2),E=q[0],k=q[1],T=r.a.useRef(null),R=Object(i.a)().t,I=y?t:t.substring(0,C*a),A=R(y?"action.showLess":"action.showMore"),j=l()("note-text-preview",{"preview-comment":f});return Object(o.useEffect)((function(){var e=T.current.clientWidth;S(e)}),[n]),Object(o.useLayoutEffect)((function(){var e=function(e){var t=document.createElement("canvas").getContext("2d");return t.font="13px sans-serif",t.measureText(e).width}(t),n=e/t.length,o=Math.floor(x/n);O(o),k(e/x>a)}),[t,x]),r.a.createElement("div",{className:j,ref:T,style:p,"aria-live":"polite"},g(),s&&u?s(I,u,0):I," ",E&&r.a.createElement("button",{className:"note-text-preview-prompt",onClick:function(e){e.stopPropagation(),b(!y),d&&d()}},A))}},1534:function(e,t,n){var o=n(32),r=n(1535);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.i,r,""]]);var i={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach(e=>o.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))}),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const r=t[o];if(0===o)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};o(r,i);e.exports=r.locals||{}},1535:function(e,t,n){(t=e.exports=n(33)(!1)).push([e.i,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.ListSeparator{margin-top:16px;margin-bottom:8px;font-family:Lato;font-weight:500;color:var(--list-separator-color);-webkit-user-select:none;-moz-user-select:none;user-select:none}",""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1536:function(e,t,n){"use strict";n.d(t,"a",(function(){return ae})),n.d(t,"b",(function(){return le})),n.d(t,"c",(function(){return Oe}));var o=n(431),r=n.n(o),i=n(432),a=n.n(i),l=n(1471),c=n.n(l),s=n(1470),u=n.n(s),d=n(1472),p=n.n(d),h=n(1469),f=n.n(h),m=n(213),g=n.n(m),v=n(0),y=n(1481),b=n(1475),w=n.n(b),x=n(571);function S(e){var t=e.cellCount,n=e.cellSize,o=e.computeMetadataCallback,r=e.computeMetadataCallbackProps,i=e.nextCellsCount,a=e.nextCellSize,l=e.nextScrollToIndex,c=e.scrollToIndex,s=e.updateScrollOffsetForScrollToIndex;t===i&&("number"!=typeof n&&"number"!=typeof a||n===a)||(o(r),c>=0&&c===l&&s())}var _=n(433),C=n.n(_),O=function(){function e(t){var n=t.cellCount,o=t.cellSizeGetter,i=t.estimatedCellSize;r()(this,e),g()(this,"_cellSizeAndPositionData",{}),g()(this,"_lastMeasuredIndex",-1),g()(this,"_lastBatchedIndex",-1),g()(this,"_cellCount",void 0),g()(this,"_cellSizeGetter",void 0),g()(this,"_estimatedCellSize",void 0),this._cellSizeGetter=o,this._cellCount=n,this._estimatedCellSize=i}return a()(e,[{key:"areOffsetsAdjusted",value:function(){return!1}},{key:"configure",value:function(e){var t=e.cellCount,n=e.estimatedCellSize,o=e.cellSizeGetter;this._cellCount=t,this._estimatedCellSize=n,this._cellSizeGetter=o}},{key:"getCellCount",value:function(){return this._cellCount}},{key:"getEstimatedCellSize",value:function(){return this._estimatedCellSize}},{key:"getLastMeasuredIndex",value:function(){return this._lastMeasuredIndex}},{key:"getOffsetAdjustment",value:function(){return 0}},{key:"getSizeAndPositionOfCell",value:function(e){if(e<0||e>=this._cellCount)throw Error("Requested index ".concat(e," is outside of range 0..").concat(this._cellCount));if(e>this._lastMeasuredIndex)for(var t=this.getSizeAndPositionOfLastMeasuredCell(),n=t.offset+t.size,o=this._lastMeasuredIndex+1;o<=e;o++){var r=this._cellSizeGetter({index:o});if(void 0===r||isNaN(r))throw Error("Invalid size returned for cell ".concat(o," of value ").concat(r));null===r?(this._cellSizeAndPositionData[o]={offset:n,size:0},this._lastBatchedIndex=e):(this._cellSizeAndPositionData[o]={offset:n,size:r},n+=r,this._lastMeasuredIndex=e)}return this._cellSizeAndPositionData[e]}},{key:"getSizeAndPositionOfLastMeasuredCell",value:function(){return this._lastMeasuredIndex>=0?this._cellSizeAndPositionData[this._lastMeasuredIndex]:{offset:0,size:0}}},{key:"getTotalSize",value:function(){var e=this.getSizeAndPositionOfLastMeasuredCell();return e.offset+e.size+(this._cellCount-this._lastMeasuredIndex-1)*this._estimatedCellSize}},{key:"getUpdatedOffsetForIndex",value:function(e){var t=e.align,n=void 0===t?"auto":t,o=e.containerSize,r=e.currentOffset,i=e.targetIndex;if(o<=0)return 0;var a,l=this.getSizeAndPositionOfCell(i),c=l.offset,s=c-o+l.size;switch(n){case"start":a=c;break;case"end":a=s;break;case"center":a=c-(o-l.size)/2;break;default:a=Math.max(s,Math.min(c,r))}var u=this.getTotalSize();return Math.max(0,Math.min(u-o,a))}},{key:"getVisibleCellRange",value:function(e){var t=e.containerSize,n=e.offset;if(0===this.getTotalSize())return{};var o=n+t,r=this._findNearestCell(n),i=this.getSizeAndPositionOfCell(r);n=i.offset+i.size;for(var a=r;n<o&&a<this._cellCount-1;)a++,n+=this.getSizeAndPositionOfCell(a).size;return{start:r,stop:a}}},{key:"resetCell",value:function(e){this._lastMeasuredIndex=Math.min(this._lastMeasuredIndex,e-1)}},{key:"_binarySearch",value:function(e,t,n){for(;t<=e;){var o=t+Math.floor((e-t)/2),r=this.getSizeAndPositionOfCell(o).offset;if(r===n)return o;r<n?t=o+1:r>n&&(e=o-1)}return t>0?t-1:0}},{key:"_exponentialSearch",value:function(e,t){for(var n=1;e<this._cellCount&&this.getSizeAndPositionOfCell(e).offset<t;)e+=n,n*=2;return this._binarySearch(Math.min(e,this._cellCount-1),Math.floor(e/2),t)}},{key:"_findNearestCell",value:function(e){if(isNaN(e))throw Error("Invalid offset ".concat(e," specified"));e=Math.max(0,e);var t=this.getSizeAndPositionOfLastMeasuredCell(),n=Math.max(0,this._lastMeasuredIndex);return t.offset>=e?this._binarySearch(n,0,e):this._exponentialSearch(n,e)}}]),e}(),q=function(){return"undefined"!=typeof window&&window.chrome?16777100:15e5},E=function(){function e(t){var n=t.maxScrollSize,o=void 0===n?q():n,i=C()(t,["maxScrollSize"]);r()(this,e),g()(this,"_cellSizeAndPositionManager",void 0),g()(this,"_maxScrollSize",void 0),this._cellSizeAndPositionManager=new O(i),this._maxScrollSize=o}return a()(e,[{key:"areOffsetsAdjusted",value:function(){return this._cellSizeAndPositionManager.getTotalSize()>this._maxScrollSize}},{key:"configure",value:function(e){this._cellSizeAndPositionManager.configure(e)}},{key:"getCellCount",value:function(){return this._cellSizeAndPositionManager.getCellCount()}},{key:"getEstimatedCellSize",value:function(){return this._cellSizeAndPositionManager.getEstimatedCellSize()}},{key:"getLastMeasuredIndex",value:function(){return this._cellSizeAndPositionManager.getLastMeasuredIndex()}},{key:"getOffsetAdjustment",value:function(e){var t=e.containerSize,n=e.offset,o=this._cellSizeAndPositionManager.getTotalSize(),r=this.getTotalSize(),i=this._getOffsetPercentage({containerSize:t,offset:n,totalSize:r});return Math.round(i*(r-o))}},{key:"getSizeAndPositionOfCell",value:function(e){return this._cellSizeAndPositionManager.getSizeAndPositionOfCell(e)}},{key:"getSizeAndPositionOfLastMeasuredCell",value:function(){return this._cellSizeAndPositionManager.getSizeAndPositionOfLastMeasuredCell()}},{key:"getTotalSize",value:function(){return Math.min(this._maxScrollSize,this._cellSizeAndPositionManager.getTotalSize())}},{key:"getUpdatedOffsetForIndex",value:function(e){var t=e.align,n=void 0===t?"auto":t,o=e.containerSize,r=e.currentOffset,i=e.targetIndex;r=this._safeOffsetToOffset({containerSize:o,offset:r});var a=this._cellSizeAndPositionManager.getUpdatedOffsetForIndex({align:n,containerSize:o,currentOffset:r,targetIndex:i});return this._offsetToSafeOffset({containerSize:o,offset:a})}},{key:"getVisibleCellRange",value:function(e){var t=e.containerSize,n=e.offset;return n=this._safeOffsetToOffset({containerSize:t,offset:n}),this._cellSizeAndPositionManager.getVisibleCellRange({containerSize:t,offset:n})}},{key:"resetCell",value:function(e){this._cellSizeAndPositionManager.resetCell(e)}},{key:"_getOffsetPercentage",value:function(e){var t=e.containerSize,n=e.offset,o=e.totalSize;return o<=t?0:n/(o-t)}},{key:"_offsetToSafeOffset",value:function(e){var t=e.containerSize,n=e.offset,o=this._cellSizeAndPositionManager.getTotalSize(),r=this.getTotalSize();if(o===r)return n;var i=this._getOffsetPercentage({containerSize:t,offset:n,totalSize:o});return Math.round(i*(r-t))}},{key:"_safeOffsetToOffset",value:function(e){var t=e.containerSize,n=e.offset,o=this._cellSizeAndPositionManager.getTotalSize(),r=this.getTotalSize();if(o===r)return n;var i=this._getOffsetPercentage({containerSize:t,offset:n,totalSize:r});return Math.round(i*(o-t))}}]),e}();function k(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t={};return function(n){var o=n.callback,r=n.indices,i=Object.keys(r),a=!e||i.every((function(e){var t=r[e];return Array.isArray(t)?t.length>0:t>=0})),l=i.length!==Object.keys(t).length||i.some((function(e){var n=t[e],o=r[e];return Array.isArray(o)?n.join(",")!==o.join(","):n!==o}));t=r,a&&l&&o(r)}}function T(e){var t=e.cellSize,n=e.cellSizeAndPositionManager,o=e.previousCellsCount,r=e.previousCellSize,i=e.previousScrollToAlignment,a=e.previousScrollToIndex,l=e.previousSize,c=e.scrollOffset,s=e.scrollToAlignment,u=e.scrollToIndex,d=e.size,p=e.sizeJustIncreasedFromZero,h=e.updateScrollIndexCallback,f=n.getCellCount(),m=u>=0&&u<f;m&&(d!==l||p||!r||"number"==typeof t&&t!==r||s!==i||u!==a)?h(u):!m&&f>0&&(d<l||f<o)&&c>n.getTotalSize()-d&&h(f-1)}var R,I,A,j=n(1504),P=(R="undefined"!=typeof window?window:"undefined"!=typeof self?self:{}).requestAnimationFrame||R.webkitRequestAnimationFrame||R.mozRequestAnimationFrame||R.oRequestAnimationFrame||R.msRequestAnimationFrame||function(e){return R.setTimeout(e,1e3/60)},N=R.cancelAnimationFrame||R.webkitCancelAnimationFrame||R.mozCancelAnimationFrame||R.oCancelAnimationFrame||R.msCancelAnimationFrame||function(e){R.clearTimeout(e)},L=P,M=N,z=function(e){return M(e.id)},H=function(e,t){var n;Promise.resolve().then((function(){n=Date.now()}));var o={id:L((function r(){Date.now()-n>=t?e.call():o.id=L(r)}))};return o};function D(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function G(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?D(n,!0).forEach((function(t){g()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):D(n).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var F="observed",W="requested",U=(A=I=function(e){function t(e){var n;r()(this,t),n=c()(this,u()(t).call(this,e)),g()(p()(n),"_onGridRenderedMemoizer",k()),g()(p()(n),"_onScrollMemoizer",k(!1)),g()(p()(n),"_deferredInvalidateColumnIndex",null),g()(p()(n),"_deferredInvalidateRowIndex",null),g()(p()(n),"_recomputeScrollLeftFlag",!1),g()(p()(n),"_recomputeScrollTopFlag",!1),g()(p()(n),"_horizontalScrollBarSize",0),g()(p()(n),"_verticalScrollBarSize",0),g()(p()(n),"_scrollbarPresenceChanged",!1),g()(p()(n),"_scrollingContainer",void 0),g()(p()(n),"_childrenToDisplay",void 0),g()(p()(n),"_columnStartIndex",void 0),g()(p()(n),"_columnStopIndex",void 0),g()(p()(n),"_rowStartIndex",void 0),g()(p()(n),"_rowStopIndex",void 0),g()(p()(n),"_renderedColumnStartIndex",0),g()(p()(n),"_renderedColumnStopIndex",0),g()(p()(n),"_renderedRowStartIndex",0),g()(p()(n),"_renderedRowStopIndex",0),g()(p()(n),"_initialScrollTop",void 0),g()(p()(n),"_initialScrollLeft",void 0),g()(p()(n),"_disablePointerEventsTimeoutId",void 0),g()(p()(n),"_styleCache",{}),g()(p()(n),"_cellCache",{}),g()(p()(n),"_debounceScrollEndedCallback",(function(){n._disablePointerEventsTimeoutId=null,n.setState({isScrolling:!1,needToResetStyleCache:!1})})),g()(p()(n),"_invokeOnGridRenderedHelper",(function(){var e=n.props.onSectionRendered;n._onGridRenderedMemoizer({callback:e,indices:{columnOverscanStartIndex:n._columnStartIndex,columnOverscanStopIndex:n._columnStopIndex,columnStartIndex:n._renderedColumnStartIndex,columnStopIndex:n._renderedColumnStopIndex,rowOverscanStartIndex:n._rowStartIndex,rowOverscanStopIndex:n._rowStopIndex,rowStartIndex:n._renderedRowStartIndex,rowStopIndex:n._renderedRowStopIndex}})})),g()(p()(n),"_setScrollingContainerRef",(function(e){n._scrollingContainer=e})),g()(p()(n),"_onScroll",(function(e){e.target===n._scrollingContainer&&n.handleScrollEvent(e.target)}));var o=new E({cellCount:e.columnCount,cellSizeGetter:function(n){return t._wrapSizeGetter(e.columnWidth)(n)},estimatedCellSize:t._getEstimatedColumnSize(e)}),i=new E({cellCount:e.rowCount,cellSizeGetter:function(n){return t._wrapSizeGetter(e.rowHeight)(n)},estimatedCellSize:t._getEstimatedRowSize(e)});return n.state={instanceProps:{columnSizeAndPositionManager:o,rowSizeAndPositionManager:i,prevColumnWidth:e.columnWidth,prevRowHeight:e.rowHeight,prevColumnCount:e.columnCount,prevRowCount:e.rowCount,prevIsScrolling:!0===e.isScrolling,prevScrollToColumn:e.scrollToColumn,prevScrollToRow:e.scrollToRow,scrollbarSize:0,scrollbarSizeMeasured:!1},isScrolling:!1,scrollDirectionHorizontal:1,scrollDirectionVertical:1,scrollLeft:0,scrollTop:0,scrollPositionChangeReason:null,needToResetStyleCache:!1},e.scrollToRow>0&&(n._initialScrollTop=n._getCalculatedScrollTop(e,n.state)),e.scrollToColumn>0&&(n._initialScrollLeft=n._getCalculatedScrollLeft(e,n.state)),n}return f()(t,e),a()(t,[{key:"getOffsetForCell",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.alignment,n=void 0===t?this.props.scrollToAlignment:t,o=e.columnIndex,r=void 0===o?this.props.scrollToColumn:o,i=e.rowIndex,a=void 0===i?this.props.scrollToRow:i,l=G({},this.props,{scrollToAlignment:n,scrollToColumn:r,scrollToRow:a});return{scrollLeft:this._getCalculatedScrollLeft(l),scrollTop:this._getCalculatedScrollTop(l)}}},{key:"getTotalRowsHeight",value:function(){return this.state.instanceProps.rowSizeAndPositionManager.getTotalSize()}},{key:"getTotalColumnsWidth",value:function(){return this.state.instanceProps.columnSizeAndPositionManager.getTotalSize()}},{key:"handleScrollEvent",value:function(e){var t=e.scrollLeft,n=void 0===t?0:t,o=e.scrollTop,r=void 0===o?0:o;if(!(r<0)){this._debounceScrollEnded();var i=this.props,a=i.autoHeight,l=i.autoWidth,c=i.height,s=i.width,u=this.state.instanceProps,d=u.scrollbarSize,p=u.rowSizeAndPositionManager.getTotalSize(),h=u.columnSizeAndPositionManager.getTotalSize(),f=Math.min(Math.max(0,h-s+d),n),m=Math.min(Math.max(0,p-c+d),r);if(this.state.scrollLeft!==f||this.state.scrollTop!==m){var g={isScrolling:!0,scrollDirectionHorizontal:f!==this.state.scrollLeft?f>this.state.scrollLeft?1:-1:this.state.scrollDirectionHorizontal,scrollDirectionVertical:m!==this.state.scrollTop?m>this.state.scrollTop?1:-1:this.state.scrollDirectionVertical,scrollPositionChangeReason:F};a||(g.scrollTop=m),l||(g.scrollLeft=f),g.needToResetStyleCache=!1,this.setState(g)}this._invokeOnScrollMemoizer({scrollLeft:f,scrollTop:m,totalColumnsWidth:h,totalRowsHeight:p})}}},{key:"invalidateCellSizeAfterRender",value:function(e){var t=e.columnIndex,n=e.rowIndex;this._deferredInvalidateColumnIndex="number"==typeof this._deferredInvalidateColumnIndex?Math.min(this._deferredInvalidateColumnIndex,t):t,this._deferredInvalidateRowIndex="number"==typeof this._deferredInvalidateRowIndex?Math.min(this._deferredInvalidateRowIndex,n):n}},{key:"measureAllCells",value:function(){var e=this.props,t=e.columnCount,n=e.rowCount,o=this.state.instanceProps;o.columnSizeAndPositionManager.getSizeAndPositionOfCell(t-1),o.rowSizeAndPositionManager.getSizeAndPositionOfCell(n-1)}},{key:"recomputeGridSize",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.columnIndex,n=void 0===t?0:t,o=e.rowIndex,r=void 0===o?0:o,i=this.props,a=i.scrollToColumn,l=i.scrollToRow,c=this.state.instanceProps;c.columnSizeAndPositionManager.resetCell(n),c.rowSizeAndPositionManager.resetCell(r),this._recomputeScrollLeftFlag=a>=0&&(1===this.state.scrollDirectionHorizontal?n<=a:n>=a),this._recomputeScrollTopFlag=l>=0&&(1===this.state.scrollDirectionVertical?r<=l:r>=l),this._styleCache={},this._cellCache={},this.forceUpdate()}},{key:"scrollToCell",value:function(e){var t=e.columnIndex,n=e.rowIndex,o=this.props.columnCount,r=this.props;o>1&&void 0!==t&&this._updateScrollLeftForScrollToColumn(G({},r,{scrollToColumn:t})),void 0!==n&&this._updateScrollTopForScrollToRow(G({},r,{scrollToRow:n}))}},{key:"componentDidMount",value:function(){var e=this.props,n=e.getScrollbarSize,o=e.height,r=e.scrollLeft,i=e.scrollToColumn,a=e.scrollTop,l=e.scrollToRow,c=e.width,s=this.state.instanceProps;if(this._initialScrollTop=0,this._initialScrollLeft=0,this._handleInvalidatedGridSize(),s.scrollbarSizeMeasured||this.setState((function(e){var t=G({},e,{needToResetStyleCache:!1});return t.instanceProps.scrollbarSize=n(),t.instanceProps.scrollbarSizeMeasured=!0,t})),"number"==typeof r&&r>=0||"number"==typeof a&&a>=0){var u=t._getScrollToPositionStateUpdate({prevState:this.state,scrollLeft:r,scrollTop:a});u&&(u.needToResetStyleCache=!1,this.setState(u))}this._scrollingContainer&&(this._scrollingContainer.scrollLeft!==this.state.scrollLeft&&(this._scrollingContainer.scrollLeft=this.state.scrollLeft),this._scrollingContainer.scrollTop!==this.state.scrollTop&&(this._scrollingContainer.scrollTop=this.state.scrollTop));var d=o>0&&c>0;i>=0&&d&&this._updateScrollLeftForScrollToColumn(),l>=0&&d&&this._updateScrollTopForScrollToRow(),this._invokeOnGridRenderedHelper(),this._invokeOnScrollMemoizer({scrollLeft:r||0,scrollTop:a||0,totalColumnsWidth:s.columnSizeAndPositionManager.getTotalSize(),totalRowsHeight:s.rowSizeAndPositionManager.getTotalSize()}),this._maybeCallOnScrollbarPresenceChange()}},{key:"componentDidUpdate",value:function(e,t){var n=this,o=this.props,r=o.autoHeight,i=o.autoWidth,a=o.columnCount,l=o.height,c=o.rowCount,s=o.scrollToAlignment,u=o.scrollToColumn,d=o.scrollToRow,p=o.width,h=this.state,f=h.scrollLeft,m=h.scrollPositionChangeReason,g=h.scrollTop,v=h.instanceProps;this._handleInvalidatedGridSize();var y=a>0&&0===e.columnCount||c>0&&0===e.rowCount;m===W&&(!i&&f>=0&&(f!==this._scrollingContainer.scrollLeft||y)&&(this._scrollingContainer.scrollLeft=f),!r&&g>=0&&(g!==this._scrollingContainer.scrollTop||y)&&(this._scrollingContainer.scrollTop=g));var b=(0===e.width||0===e.height)&&l>0&&p>0;if(this._recomputeScrollLeftFlag?(this._recomputeScrollLeftFlag=!1,this._updateScrollLeftForScrollToColumn(this.props)):T({cellSizeAndPositionManager:v.columnSizeAndPositionManager,previousCellsCount:e.columnCount,previousCellSize:e.columnWidth,previousScrollToAlignment:e.scrollToAlignment,previousScrollToIndex:e.scrollToColumn,previousSize:e.width,scrollOffset:f,scrollToAlignment:s,scrollToIndex:u,size:p,sizeJustIncreasedFromZero:b,updateScrollIndexCallback:function(){return n._updateScrollLeftForScrollToColumn(n.props)}}),this._recomputeScrollTopFlag?(this._recomputeScrollTopFlag=!1,this._updateScrollTopForScrollToRow(this.props)):T({cellSizeAndPositionManager:v.rowSizeAndPositionManager,previousCellsCount:e.rowCount,previousCellSize:e.rowHeight,previousScrollToAlignment:e.scrollToAlignment,previousScrollToIndex:e.scrollToRow,previousSize:e.height,scrollOffset:g,scrollToAlignment:s,scrollToIndex:d,size:l,sizeJustIncreasedFromZero:b,updateScrollIndexCallback:function(){return n._updateScrollTopForScrollToRow(n.props)}}),this._invokeOnGridRenderedHelper(),f!==t.scrollLeft||g!==t.scrollTop){var w=v.rowSizeAndPositionManager.getTotalSize(),x=v.columnSizeAndPositionManager.getTotalSize();this._invokeOnScrollMemoizer({scrollLeft:f,scrollTop:g,totalColumnsWidth:x,totalRowsHeight:w})}this._maybeCallOnScrollbarPresenceChange()}},{key:"componentWillUnmount",value:function(){this._disablePointerEventsTimeoutId&&z(this._disablePointerEventsTimeoutId)}},{key:"render",value:function(){var e=this.props,t=e.autoContainerWidth,n=e.autoHeight,o=e.autoWidth,r=e.className,i=e.containerProps,a=e.containerRole,l=e.containerStyle,c=e.height,s=e.id,u=e.noContentRenderer,d=e.role,p=e.style,h=e.tabIndex,f=e.width,m=this.state,g=m.instanceProps,y=m.needToResetStyleCache,b=this._isScrolling(),S={boxSizing:"border-box",direction:"ltr",height:n?"auto":c,position:"relative",width:o?"auto":f,WebkitOverflowScrolling:"touch",willChange:"transform"};y&&(this._styleCache={}),this.state.isScrolling||this._resetStyleCache(),this._calculateChildrenToRender(this.props,this.state);var _=g.columnSizeAndPositionManager.getTotalSize(),C=g.rowSizeAndPositionManager.getTotalSize(),O=C>c?g.scrollbarSize:0,q=_>f?g.scrollbarSize:0;q===this._horizontalScrollBarSize&&O===this._verticalScrollBarSize||(this._horizontalScrollBarSize=q,this._verticalScrollBarSize=O,this._scrollbarPresenceChanged=!0),S.overflowX=_+O<=f?"hidden":"auto",S.overflowY=C+q<=c?"hidden":"auto";var E=this._childrenToDisplay,k=0===E.length&&c>0&&f>0;return v.createElement("div",w()({ref:this._setScrollingContainerRef},i,{"aria-label":this.props["aria-label"],"aria-readonly":this.props["aria-readonly"],className:Object(x.default)("ReactVirtualized__Grid",r),id:s,onScroll:this._onScroll,role:d,style:G({},S,{},p),tabIndex:h}),E.length>0&&v.createElement("div",{className:"ReactVirtualized__Grid__innerScrollContainer",role:a,style:G({width:t?"auto":_,height:C,maxWidth:_,maxHeight:C,overflow:"hidden",pointerEvents:b?"none":"",position:"relative"},l)},E),k&&u())}},{key:"_calculateChildrenToRender",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.state,n=e.cellRenderer,o=e.cellRangeRenderer,r=e.columnCount,i=e.deferredMeasurementCache,a=e.height,l=e.overscanColumnCount,c=e.overscanIndicesGetter,s=e.overscanRowCount,u=e.rowCount,d=e.width,p=e.isScrollingOptOut,h=t.scrollDirectionHorizontal,f=t.scrollDirectionVertical,m=t.instanceProps,g=this._initialScrollTop>0?this._initialScrollTop:t.scrollTop,v=this._initialScrollLeft>0?this._initialScrollLeft:t.scrollLeft,y=this._isScrolling(e,t);if(this._childrenToDisplay=[],a>0&&d>0){var b=m.columnSizeAndPositionManager.getVisibleCellRange({containerSize:d,offset:v}),w=m.rowSizeAndPositionManager.getVisibleCellRange({containerSize:a,offset:g}),x=m.columnSizeAndPositionManager.getOffsetAdjustment({containerSize:d,offset:v}),S=m.rowSizeAndPositionManager.getOffsetAdjustment({containerSize:a,offset:g});this._renderedColumnStartIndex=b.start,this._renderedColumnStopIndex=b.stop,this._renderedRowStartIndex=w.start,this._renderedRowStopIndex=w.stop;var _=c({direction:"horizontal",cellCount:r,overscanCellsCount:l,scrollDirection:h,startIndex:"number"==typeof b.start?b.start:0,stopIndex:"number"==typeof b.stop?b.stop:-1}),C=c({direction:"vertical",cellCount:u,overscanCellsCount:s,scrollDirection:f,startIndex:"number"==typeof w.start?w.start:0,stopIndex:"number"==typeof w.stop?w.stop:-1}),O=_.overscanStartIndex,q=_.overscanStopIndex,E=C.overscanStartIndex,k=C.overscanStopIndex;if(i){if(!i.hasFixedHeight())for(var T=E;T<=k;T++)if(!i.has(T,0)){O=0,q=r-1;break}if(!i.hasFixedWidth())for(var R=O;R<=q;R++)if(!i.has(0,R)){E=0,k=u-1;break}}this._childrenToDisplay=o({cellCache:this._cellCache,cellRenderer:n,columnSizeAndPositionManager:m.columnSizeAndPositionManager,columnStartIndex:O,columnStopIndex:q,deferredMeasurementCache:i,horizontalOffsetAdjustment:x,isScrolling:y,isScrollingOptOut:p,parent:this,rowSizeAndPositionManager:m.rowSizeAndPositionManager,rowStartIndex:E,rowStopIndex:k,scrollLeft:v,scrollTop:g,styleCache:this._styleCache,verticalOffsetAdjustment:S,visibleColumnIndices:b,visibleRowIndices:w}),this._columnStartIndex=O,this._columnStopIndex=q,this._rowStartIndex=E,this._rowStopIndex=k}}},{key:"_debounceScrollEnded",value:function(){var e=this.props.scrollingResetTimeInterval;this._disablePointerEventsTimeoutId&&z(this._disablePointerEventsTimeoutId),this._disablePointerEventsTimeoutId=H(this._debounceScrollEndedCallback,e)}},{key:"_handleInvalidatedGridSize",value:function(){if("number"==typeof this._deferredInvalidateColumnIndex&&"number"==typeof this._deferredInvalidateRowIndex){var e=this._deferredInvalidateColumnIndex,t=this._deferredInvalidateRowIndex;this._deferredInvalidateColumnIndex=null,this._deferredInvalidateRowIndex=null,this.recomputeGridSize({columnIndex:e,rowIndex:t})}}},{key:"_invokeOnScrollMemoizer",value:function(e){var t=this,n=e.scrollLeft,o=e.scrollTop,r=e.totalColumnsWidth,i=e.totalRowsHeight;this._onScrollMemoizer({callback:function(e){var n=e.scrollLeft,o=e.scrollTop,a=t.props,l=a.height;(0,a.onScroll)({clientHeight:l,clientWidth:a.width,scrollHeight:i,scrollLeft:n,scrollTop:o,scrollWidth:r})},indices:{scrollLeft:n,scrollTop:o}})}},{key:"_isScrolling",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.state;return Object.hasOwnProperty.call(e,"isScrolling")?Boolean(e.isScrolling):Boolean(t.isScrolling)}},{key:"_maybeCallOnScrollbarPresenceChange",value:function(){if(this._scrollbarPresenceChanged){var e=this.props.onScrollbarPresenceChange;this._scrollbarPresenceChanged=!1,e({horizontal:this._horizontalScrollBarSize>0,size:this.state.instanceProps.scrollbarSize,vertical:this._verticalScrollBarSize>0})}}},{key:"scrollToPosition",value:function(e){var n=e.scrollLeft,o=e.scrollTop,r=t._getScrollToPositionStateUpdate({prevState:this.state,scrollLeft:n,scrollTop:o});r&&(r.needToResetStyleCache=!1,this.setState(r))}},{key:"_getCalculatedScrollLeft",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.state;return t._getCalculatedScrollLeft(e,n)}},{key:"_updateScrollLeftForScrollToColumn",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.state,o=t._getScrollLeftForScrollToColumnStateUpdate(e,n);o&&(o.needToResetStyleCache=!1,this.setState(o))}},{key:"_getCalculatedScrollTop",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.state;return t._getCalculatedScrollTop(e,n)}},{key:"_resetStyleCache",value:function(){var e=this._styleCache,t=this._cellCache,n=this.props.isScrollingOptOut;this._cellCache={},this._styleCache={};for(var o=this._rowStartIndex;o<=this._rowStopIndex;o++)for(var r=this._columnStartIndex;r<=this._columnStopIndex;r++){var i="".concat(o,"-").concat(r);this._styleCache[i]=e[i],n&&(this._cellCache[i]=t[i])}}},{key:"_updateScrollTopForScrollToRow",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.state,o=t._getScrollTopForScrollToRowStateUpdate(e,n);o&&(o.needToResetStyleCache=!1,this.setState(o))}}],[{key:"getDerivedStateFromProps",value:function(e,n){var o={};0===e.columnCount&&0!==n.scrollLeft||0===e.rowCount&&0!==n.scrollTop?(o.scrollLeft=0,o.scrollTop=0):(e.scrollLeft!==n.scrollLeft&&e.scrollToColumn<0||e.scrollTop!==n.scrollTop&&e.scrollToRow<0)&&Object.assign(o,t._getScrollToPositionStateUpdate({prevState:n,scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}));var r,i,a=n.instanceProps;return o.needToResetStyleCache=!1,e.columnWidth===a.prevColumnWidth&&e.rowHeight===a.prevRowHeight||(o.needToResetStyleCache=!0),a.columnSizeAndPositionManager.configure({cellCount:e.columnCount,estimatedCellSize:t._getEstimatedColumnSize(e),cellSizeGetter:t._wrapSizeGetter(e.columnWidth)}),a.rowSizeAndPositionManager.configure({cellCount:e.rowCount,estimatedCellSize:t._getEstimatedRowSize(e),cellSizeGetter:t._wrapSizeGetter(e.rowHeight)}),0!==a.prevColumnCount&&0!==a.prevRowCount||(a.prevColumnCount=0,a.prevRowCount=0),e.autoHeight&&!1===e.isScrolling&&!0===a.prevIsScrolling&&Object.assign(o,{isScrolling:!1}),S({cellCount:a.prevColumnCount,cellSize:"number"==typeof a.prevColumnWidth?a.prevColumnWidth:null,computeMetadataCallback:function(){return a.columnSizeAndPositionManager.resetCell(0)},computeMetadataCallbackProps:e,nextCellsCount:e.columnCount,nextCellSize:"number"==typeof e.columnWidth?e.columnWidth:null,nextScrollToIndex:e.scrollToColumn,scrollToIndex:a.prevScrollToColumn,updateScrollOffsetForScrollToIndex:function(){r=t._getScrollLeftForScrollToColumnStateUpdate(e,n)}}),S({cellCount:a.prevRowCount,cellSize:"number"==typeof a.prevRowHeight?a.prevRowHeight:null,computeMetadataCallback:function(){return a.rowSizeAndPositionManager.resetCell(0)},computeMetadataCallbackProps:e,nextCellsCount:e.rowCount,nextCellSize:"number"==typeof e.rowHeight?e.rowHeight:null,nextScrollToIndex:e.scrollToRow,scrollToIndex:a.prevScrollToRow,updateScrollOffsetForScrollToIndex:function(){i=t._getScrollTopForScrollToRowStateUpdate(e,n)}}),a.prevColumnCount=e.columnCount,a.prevColumnWidth=e.columnWidth,a.prevIsScrolling=!0===e.isScrolling,a.prevRowCount=e.rowCount,a.prevRowHeight=e.rowHeight,a.prevScrollToColumn=e.scrollToColumn,a.prevScrollToRow=e.scrollToRow,a.scrollbarSize=e.getScrollbarSize(),void 0===a.scrollbarSize?(a.scrollbarSizeMeasured=!1,a.scrollbarSize=0):a.scrollbarSizeMeasured=!0,o.instanceProps=a,G({},o,{},r,{},i)}},{key:"_getEstimatedColumnSize",value:function(e){return"number"==typeof e.columnWidth?e.columnWidth:e.estimatedColumnSize}},{key:"_getEstimatedRowSize",value:function(e){return"number"==typeof e.rowHeight?e.rowHeight:e.estimatedRowSize}},{key:"_getScrollToPositionStateUpdate",value:function(e){var t=e.prevState,n=e.scrollLeft,o=e.scrollTop,r={scrollPositionChangeReason:W};return"number"==typeof n&&n>=0&&(r.scrollDirectionHorizontal=n>t.scrollLeft?1:-1,r.scrollLeft=n),"number"==typeof o&&o>=0&&(r.scrollDirectionVertical=o>t.scrollTop?1:-1,r.scrollTop=o),"number"==typeof n&&n>=0&&n!==t.scrollLeft||"number"==typeof o&&o>=0&&o!==t.scrollTop?r:{}}},{key:"_wrapSizeGetter",value:function(e){return"function"==typeof e?e:function(){return e}}},{key:"_getCalculatedScrollLeft",value:function(e,t){var n=e.columnCount,o=e.height,r=e.scrollToAlignment,i=e.scrollToColumn,a=e.width,l=t.scrollLeft,c=t.instanceProps;if(n>0){var s=n-1,u=i<0?s:Math.min(s,i),d=c.rowSizeAndPositionManager.getTotalSize(),p=c.scrollbarSizeMeasured&&d>o?c.scrollbarSize:0;return c.columnSizeAndPositionManager.getUpdatedOffsetForIndex({align:r,containerSize:a-p,currentOffset:l,targetIndex:u})}return 0}},{key:"_getScrollLeftForScrollToColumnStateUpdate",value:function(e,n){var o=n.scrollLeft,r=t._getCalculatedScrollLeft(e,n);return"number"==typeof r&&r>=0&&o!==r?t._getScrollToPositionStateUpdate({prevState:n,scrollLeft:r,scrollTop:-1}):{}}},{key:"_getCalculatedScrollTop",value:function(e,t){var n=e.height,o=e.rowCount,r=e.scrollToAlignment,i=e.scrollToRow,a=e.width,l=t.scrollTop,c=t.instanceProps;if(o>0){var s=o-1,u=i<0?s:Math.min(s,i),d=c.columnSizeAndPositionManager.getTotalSize(),p=c.scrollbarSizeMeasured&&d>a?c.scrollbarSize:0;return c.rowSizeAndPositionManager.getUpdatedOffsetForIndex({align:r,containerSize:n-p,currentOffset:l,targetIndex:u})}return 0}},{key:"_getScrollTopForScrollToRowStateUpdate",value:function(e,n){var o=n.scrollTop,r=t._getCalculatedScrollTop(e,n);return"number"==typeof r&&r>=0&&o!==r?t._getScrollToPositionStateUpdate({prevState:n,scrollLeft:-1,scrollTop:r}):{}}}]),t}(v.PureComponent),g()(I,"propTypes",null),A);g()(U,"defaultProps",{"aria-label":"grid","aria-readonly":!0,autoContainerWidth:!1,autoHeight:!1,autoWidth:!1,cellRangeRenderer:function(e){for(var t=e.cellCache,n=e.cellRenderer,o=e.columnSizeAndPositionManager,r=e.columnStartIndex,i=e.columnStopIndex,a=e.deferredMeasurementCache,l=e.horizontalOffsetAdjustment,c=e.isScrolling,s=e.isScrollingOptOut,u=e.parent,d=e.rowSizeAndPositionManager,p=e.rowStartIndex,h=e.rowStopIndex,f=e.styleCache,m=e.verticalOffsetAdjustment,g=e.visibleColumnIndices,v=e.visibleRowIndices,y=[],b=o.areOffsetsAdjusted()||d.areOffsetsAdjusted(),w=!c&&!b,x=p;x<=h;x++)for(var S=d.getSizeAndPositionOfCell(x),_=r;_<=i;_++){var C=o.getSizeAndPositionOfCell(_),O=_>=g.start&&_<=g.stop&&x>=v.start&&x<=v.stop,q="".concat(x,"-").concat(_),E=void 0;w&&f[q]?E=f[q]:a&&!a.has(x,_)?E={height:"auto",left:0,position:"absolute",top:0,width:"auto"}:(E={height:S.size,left:C.offset+l,position:"absolute",top:S.offset+m,width:C.size},f[q]=E);var k={columnIndex:_,isScrolling:c,isVisible:O,key:q,parent:u,rowIndex:x,style:E},T=void 0;!s&&!c||l||m?T=n(k):(t[q]||(t[q]=n(k)),T=t[q]),null!=T&&!1!==T&&y.push(T)}return y},containerRole:"rowgroup",containerStyle:{},estimatedColumnSize:100,estimatedRowSize:30,getScrollbarSize:j.default,noContentRenderer:function(){return null},onScroll:function(){},onScrollbarPresenceChange:function(){},onSectionRendered:function(){},overscanColumnCount:0,overscanIndicesGetter:function(e){var t=e.cellCount,n=e.overscanCellsCount,o=e.scrollDirection,r=e.startIndex,i=e.stopIndex;return 1===o?{overscanStartIndex:Math.max(0,r),overscanStopIndex:Math.min(t-1,i+n)}:{overscanStartIndex:Math.max(0,r-n),overscanStopIndex:Math.min(t-1,i)}},overscanRowCount:10,role:"grid",scrollingResetTimeInterval:150,scrollToAlignment:"auto",scrollToColumn:-1,scrollToRow:-1,style:{},tabIndex:0,isScrollingOptOut:!1}),Object(y.polyfill)(U);var B=U;function V(e){var t=e.cellCount,n=e.overscanCellsCount,o=e.scrollDirection,r=e.startIndex,i=e.stopIndex;return n=Math.max(1,n),1===o?{overscanStartIndex:Math.max(0,r-1),overscanStopIndex:Math.min(t-1,i+n)}:{overscanStartIndex:Math.max(0,r-n),overscanStopIndex:Math.min(t-1,i+1)}}var Y,K;function $(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}var X=(K=Y=function(e){function t(){var e,n;r()(this,t);for(var o=arguments.length,i=new Array(o),a=0;a<o;a++)i[a]=arguments[a];return n=c()(this,(e=u()(t)).call.apply(e,[this].concat(i))),g()(p()(n),"state",{scrollToColumn:0,scrollToRow:0,instanceProps:{prevScrollToColumn:0,prevScrollToRow:0}}),g()(p()(n),"_columnStartIndex",0),g()(p()(n),"_columnStopIndex",0),g()(p()(n),"_rowStartIndex",0),g()(p()(n),"_rowStopIndex",0),g()(p()(n),"_onKeyDown",(function(e){var t=n.props,o=t.columnCount,r=t.disabled,i=t.mode,a=t.rowCount;if(!r){var l=n._getScrollState(),c=l.scrollToColumn,s=l.scrollToRow,u=n._getScrollState(),d=u.scrollToColumn,p=u.scrollToRow;switch(e.key){case"ArrowDown":p="cells"===i?Math.min(p+1,a-1):Math.min(n._rowStopIndex+1,a-1);break;case"ArrowLeft":d="cells"===i?Math.max(d-1,0):Math.max(n._columnStartIndex-1,0);break;case"ArrowRight":d="cells"===i?Math.min(d+1,o-1):Math.min(n._columnStopIndex+1,o-1);break;case"ArrowUp":p="cells"===i?Math.max(p-1,0):Math.max(n._rowStartIndex-1,0)}d===c&&p===s||(e.preventDefault(),n._updateScrollState({scrollToColumn:d,scrollToRow:p}))}})),g()(p()(n),"_onSectionRendered",(function(e){var t=e.columnStartIndex,o=e.columnStopIndex,r=e.rowStartIndex,i=e.rowStopIndex;n._columnStartIndex=t,n._columnStopIndex=o,n._rowStartIndex=r,n._rowStopIndex=i})),n}return f()(t,e),a()(t,[{key:"setScrollIndexes",value:function(e){var t=e.scrollToColumn,n=e.scrollToRow;this.setState({scrollToRow:n,scrollToColumn:t})}},{key:"render",value:function(){var e=this.props,t=e.className,n=e.children,o=this._getScrollState(),r=o.scrollToColumn,i=o.scrollToRow;return v.createElement("div",{className:t,onKeyDown:this._onKeyDown},n({onSectionRendered:this._onSectionRendered,scrollToColumn:r,scrollToRow:i}))}},{key:"_getScrollState",value:function(){return this.props.isControlled?this.props:this.state}},{key:"_updateScrollState",value:function(e){var t=e.scrollToColumn,n=e.scrollToRow,o=this.props,r=o.isControlled,i=o.onScrollToChange;"function"==typeof i&&i({scrollToColumn:t,scrollToRow:n}),r||this.setState({scrollToColumn:t,scrollToRow:n})}}],[{key:"getDerivedStateFromProps",value:function(e,t){return e.isControlled?{}:e.scrollToColumn!==t.instanceProps.prevScrollToColumn||e.scrollToRow!==t.instanceProps.prevScrollToRow?function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?$(n,!0).forEach((function(t){g()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):$(n).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},t,{scrollToColumn:e.scrollToColumn,scrollToRow:e.scrollToRow,instanceProps:{prevScrollToColumn:e.scrollToColumn,prevScrollToRow:e.scrollToRow}}):{}}}]),t}(v.PureComponent),g()(Y,"propTypes",null),K);g()(X,"defaultProps",{disabled:!1,isControlled:!1,mode:"edges",scrollToColumn:0,scrollToRow:0}),Object(y.polyfill)(X);var Z,J,Q=n(1514);function ee(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function te(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ee(n,!0).forEach((function(t){g()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ee(n).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var ne=(J=Z=function(e){function t(){var e,n;r()(this,t);for(var o=arguments.length,i=new Array(o),a=0;a<o;a++)i[a]=arguments[a];return n=c()(this,(e=u()(t)).call.apply(e,[this].concat(i))),g()(p()(n),"state",{height:n.props.defaultHeight||0,width:n.props.defaultWidth||0}),g()(p()(n),"_parentNode",void 0),g()(p()(n),"_autoSizer",void 0),g()(p()(n),"_window",void 0),g()(p()(n),"_detectElementResize",void 0),g()(p()(n),"_onResize",(function(){var e=n.props,t=e.disableHeight,o=e.disableWidth,r=e.onResize;if(n._parentNode){var i=n._parentNode.offsetHeight||0,a=n._parentNode.offsetWidth||0,l=(n._window||window).getComputedStyle(n._parentNode)||{},c=parseInt(l.paddingLeft,10)||0,s=parseInt(l.paddingRight,10)||0,u=parseInt(l.paddingTop,10)||0,d=parseInt(l.paddingBottom,10)||0,p=i-u-d,h=a-c-s;(!t&&n.state.height!==p||!o&&n.state.width!==h)&&(n.setState({height:i-u-d,width:a-c-s}),r({height:i,width:a}))}})),g()(p()(n),"_setRef",(function(e){n._autoSizer=e})),n}return f()(t,e),a()(t,[{key:"componentDidMount",value:function(){var e=this.props.nonce;this._autoSizer&&this._autoSizer.parentNode&&this._autoSizer.parentNode.ownerDocument&&this._autoSizer.parentNode.ownerDocument.defaultView&&this._autoSizer.parentNode instanceof this._autoSizer.parentNode.ownerDocument.defaultView.HTMLElement&&(this._parentNode=this._autoSizer.parentNode,this._window=this._autoSizer.parentNode.ownerDocument.defaultView,this._detectElementResize=Object(Q.a)(e,this._window),this._detectElementResize.addResizeListener(this._parentNode,this._onResize),this._onResize())}},{key:"componentWillUnmount",value:function(){this._detectElementResize&&this._parentNode&&this._detectElementResize.removeResizeListener(this._parentNode,this._onResize)}},{key:"render",value:function(){var e=this.props,t=e.children,n=e.className,o=e.disableHeight,r=e.disableWidth,i=e.style,a=this.state,l=a.height,c=a.width,s={overflow:"visible"},u={};return o||(s.height=0,u.height=l),r||(s.width=0,u.width=c),v.createElement("div",{className:n,ref:this._setRef,style:te({},s,{},i)},t(u))}}]),t}(v.Component),g()(Z,"propTypes",null),J);g()(ne,"defaultProps",{onResize:function(){},disableHeight:!1,disableWidth:!1,style:{}});var oe,re,ie=n(116),ae=(re=oe=function(e){function t(){var e,n;r()(this,t);for(var o=arguments.length,i=new Array(o),a=0;a<o;a++)i[a]=arguments[a];return n=c()(this,(e=u()(t)).call.apply(e,[this].concat(i))),g()(p()(n),"_child",void 0),g()(p()(n),"_measure",(function(){var e=n.props,t=e.cache,o=e.columnIndex,r=void 0===o?0:o,i=e.parent,a=e.rowIndex,l=void 0===a?n.props.index||0:a,c=n._getCellMeasurements(),s=c.height,u=c.width;s===t.getHeight(l,r)&&u===t.getWidth(l,r)||(t.set(l,r,u,s),i&&"function"==typeof i.recomputeGridSize&&i.recomputeGridSize({columnIndex:r,rowIndex:l}))})),g()(p()(n),"_registerChild",(function(e){!e||e instanceof Element||console.warn("CellMeasurer registerChild expects to be passed Element or null"),n._child=e,e&&n._maybeMeasureCell()})),n}return f()(t,e),a()(t,[{key:"componentDidMount",value:function(){this._maybeMeasureCell()}},{key:"componentDidUpdate",value:function(){this._maybeMeasureCell()}},{key:"render",value:function(){var e=this.props.children;return"function"==typeof e?e({measure:this._measure,registerChild:this._registerChild}):e}},{key:"_getCellMeasurements",value:function(){var e=this.props.cache,t=this._child||Object(ie.findDOMNode)(this);if(t&&t.ownerDocument&&t.ownerDocument.defaultView&&t instanceof t.ownerDocument.defaultView.HTMLElement){var n=t.style.width,o=t.style.height;e.hasFixedWidth()||(t.style.width="auto"),e.hasFixedHeight()||(t.style.height="auto");var r=Math.ceil(t.offsetHeight),i=Math.ceil(t.offsetWidth);return n&&(t.style.width=n),o&&(t.style.height=o),{height:r,width:i}}return{height:0,width:0}}},{key:"_maybeMeasureCell",value:function(){var e=this.props,t=e.cache,n=e.columnIndex,o=void 0===n?0:n,r=e.parent,i=e.rowIndex,a=void 0===i?this.props.index||0:i;if(!t.has(a,o)){var l=this._getCellMeasurements(),c=l.height,s=l.width;t.set(a,o,s,c),r&&"function"==typeof r.invalidateCellSizeAfterRender&&r.invalidateCellSizeAfterRender({columnIndex:o,rowIndex:a})}}}]),t}(v.PureComponent),g()(oe,"propTypes",null),re);g()(ae,"__internalCellMeasurerFlag",!1);var le=function(){function e(){var t=this,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};r()(this,e),g()(this,"_cellHeightCache",{}),g()(this,"_cellWidthCache",{}),g()(this,"_columnWidthCache",{}),g()(this,"_rowHeightCache",{}),g()(this,"_defaultHeight",void 0),g()(this,"_defaultWidth",void 0),g()(this,"_minHeight",void 0),g()(this,"_minWidth",void 0),g()(this,"_keyMapper",void 0),g()(this,"_hasFixedHeight",void 0),g()(this,"_hasFixedWidth",void 0),g()(this,"_columnCount",0),g()(this,"_rowCount",0),g()(this,"columnWidth",(function(e){var n=e.index,o=t._keyMapper(0,n);return void 0!==t._columnWidthCache[o]?t._columnWidthCache[o]:t._defaultWidth})),g()(this,"rowHeight",(function(e){var n=e.index,o=t._keyMapper(n,0);return void 0!==t._rowHeightCache[o]?t._rowHeightCache[o]:t._defaultHeight}));var o=n.defaultHeight,i=n.defaultWidth,a=n.fixedHeight,l=n.fixedWidth,c=n.keyMapper,s=n.minHeight,u=n.minWidth;this._hasFixedHeight=!0===a,this._hasFixedWidth=!0===l,this._minHeight=s||0,this._minWidth=u||0,this._keyMapper=c||ce,this._defaultHeight=Math.max(this._minHeight,"number"==typeof o?o:30),this._defaultWidth=Math.max(this._minWidth,"number"==typeof i?i:100)}return a()(e,[{key:"clear",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=this._keyMapper(e,t);delete this._cellHeightCache[n],delete this._cellWidthCache[n],this._updateCachedColumnAndRowSizes(e,t)}},{key:"clearAll",value:function(){this._cellHeightCache={},this._cellWidthCache={},this._columnWidthCache={},this._rowHeightCache={},this._rowCount=0,this._columnCount=0}},{key:"hasFixedHeight",value:function(){return this._hasFixedHeight}},{key:"hasFixedWidth",value:function(){return this._hasFixedWidth}},{key:"getHeight",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(this._hasFixedHeight)return this._defaultHeight;var n=this._keyMapper(e,t);return void 0!==this._cellHeightCache[n]?Math.max(this._minHeight,this._cellHeightCache[n]):this._defaultHeight}},{key:"getWidth",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(this._hasFixedWidth)return this._defaultWidth;var n=this._keyMapper(e,t);return void 0!==this._cellWidthCache[n]?Math.max(this._minWidth,this._cellWidthCache[n]):this._defaultWidth}},{key:"has",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=this._keyMapper(e,t);return void 0!==this._cellHeightCache[n]}},{key:"set",value:function(e,t,n,o){var r=this._keyMapper(e,t);t>=this._columnCount&&(this._columnCount=t+1),e>=this._rowCount&&(this._rowCount=e+1),this._cellHeightCache[r]=o,this._cellWidthCache[r]=n,this._updateCachedColumnAndRowSizes(e,t)}},{key:"_updateCachedColumnAndRowSizes",value:function(e,t){if(!this._hasFixedWidth){for(var n=0,o=0;o<this._rowCount;o++)n=Math.max(n,this.getWidth(o,t));var r=this._keyMapper(0,t);this._columnWidthCache[r]=n}if(!this._hasFixedHeight){for(var i=0,a=0;a<this._columnCount;a++)i=Math.max(i,this.getHeight(e,a));var l=this._keyMapper(e,0);this._rowHeightCache[l]=i}}},{key:"defaultHeight",get:function(){return this._defaultHeight}},{key:"defaultWidth",get:function(){return this._defaultWidth}}]),e}();function ce(e,t){return"".concat(e,"-").concat(t)}function se(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function ue(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?se(n,!0).forEach((function(t){g()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):se(n).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var de="observed",pe="requested",he=function(e){function t(){var e,n;r()(this,t);for(var o=arguments.length,i=new Array(o),a=0;a<o;a++)i[a]=arguments[a];return n=c()(this,(e=u()(t)).call.apply(e,[this].concat(i))),g()(p()(n),"state",{isScrolling:!1,scrollLeft:0,scrollTop:0}),g()(p()(n),"_calculateSizeAndPositionDataOnNextUpdate",!1),g()(p()(n),"_onSectionRenderedMemoizer",k()),g()(p()(n),"_onScrollMemoizer",k(!1)),g()(p()(n),"_invokeOnSectionRenderedHelper",(function(){var e=n.props,t=e.cellLayoutManager,o=e.onSectionRendered;n._onSectionRenderedMemoizer({callback:o,indices:{indices:t.getLastRenderedIndices()}})})),g()(p()(n),"_setScrollingContainerRef",(function(e){n._scrollingContainer=e})),g()(p()(n),"_updateScrollPositionForScrollToCell",(function(){var e=n.props,t=e.cellLayoutManager,o=e.height,r=e.scrollToAlignment,i=e.scrollToCell,a=e.width,l=n.state,c=l.scrollLeft,s=l.scrollTop;if(i>=0){var u=t.getScrollPositionForCell({align:r,cellIndex:i,height:o,scrollLeft:c,scrollTop:s,width:a});u.scrollLeft===c&&u.scrollTop===s||n._setScrollPosition(u)}})),g()(p()(n),"_onScroll",(function(e){if(e.target===n._scrollingContainer){n._enablePointerEventsAfterDelay();var t=n.props,o=t.cellLayoutManager,r=t.height,i=t.isScrollingChange,a=t.width,l=n._scrollbarSize,c=o.getTotalSize(),s=c.height,u=c.width,d=Math.max(0,Math.min(u-a+l,e.target.scrollLeft)),p=Math.max(0,Math.min(s-r+l,e.target.scrollTop));if(n.state.scrollLeft!==d||n.state.scrollTop!==p){var h=e.cancelable?de:pe;n.state.isScrolling||i(!0),n.setState({isScrolling:!0,scrollLeft:d,scrollPositionChangeReason:h,scrollTop:p})}n._invokeOnScrollMemoizer({scrollLeft:d,scrollTop:p,totalWidth:u,totalHeight:s})}})),n._scrollbarSize=Object(j.default)(),void 0===n._scrollbarSize?(n._scrollbarSizeMeasured=!1,n._scrollbarSize=0):n._scrollbarSizeMeasured=!0,n}return f()(t,e),a()(t,[{key:"recomputeCellSizesAndPositions",value:function(){this._calculateSizeAndPositionDataOnNextUpdate=!0,this.forceUpdate()}},{key:"componentDidMount",value:function(){var e=this.props,t=e.cellLayoutManager,n=e.scrollLeft,o=e.scrollToCell,r=e.scrollTop;this._scrollbarSizeMeasured||(this._scrollbarSize=Object(j.default)(),this._scrollbarSizeMeasured=!0,this.setState({})),o>=0?this._updateScrollPositionForScrollToCell():(n>=0||r>=0)&&this._setScrollPosition({scrollLeft:n,scrollTop:r}),this._invokeOnSectionRenderedHelper();var i=t.getTotalSize(),a=i.height,l=i.width;this._invokeOnScrollMemoizer({scrollLeft:n||0,scrollTop:r||0,totalHeight:a,totalWidth:l})}},{key:"componentDidUpdate",value:function(e,t){var n=this.props,o=n.height,r=n.scrollToAlignment,i=n.scrollToCell,a=n.width,l=this.state,c=l.scrollLeft,s=l.scrollPositionChangeReason,u=l.scrollTop;s===pe&&(c>=0&&c!==t.scrollLeft&&c!==this._scrollingContainer.scrollLeft&&(this._scrollingContainer.scrollLeft=c),u>=0&&u!==t.scrollTop&&u!==this._scrollingContainer.scrollTop&&(this._scrollingContainer.scrollTop=u)),o===e.height&&r===e.scrollToAlignment&&i===e.scrollToCell&&a===e.width||this._updateScrollPositionForScrollToCell(),this._invokeOnSectionRenderedHelper()}},{key:"componentWillUnmount",value:function(){this._disablePointerEventsTimeoutId&&clearTimeout(this._disablePointerEventsTimeoutId)}},{key:"render",value:function(){var e=this.props,t=e.autoHeight,n=e.cellCount,o=e.cellLayoutManager,r=e.className,i=e.height,a=e.horizontalOverscanSize,l=e.id,c=e.noContentRenderer,s=e.style,u=e.verticalOverscanSize,d=e.width,p=this.state,h=p.isScrolling,f=p.scrollLeft,m=p.scrollTop;(this._lastRenderedCellCount!==n||this._lastRenderedCellLayoutManager!==o||this._calculateSizeAndPositionDataOnNextUpdate)&&(this._lastRenderedCellCount=n,this._lastRenderedCellLayoutManager=o,this._calculateSizeAndPositionDataOnNextUpdate=!1,o.calculateSizeAndPositionData());var g=o.getTotalSize(),y=g.height,b=g.width,w=Math.max(0,f-a),S=Math.max(0,m-u),_=Math.min(b,f+d+a),C=Math.min(y,m+i+u),O=i>0&&d>0?o.cellRenderers({height:C-S,isScrolling:h,width:_-w,x:w,y:S}):[],q={boxSizing:"border-box",direction:"ltr",height:t?"auto":i,position:"relative",WebkitOverflowScrolling:"touch",width:d,willChange:"transform"},E=y>i?this._scrollbarSize:0,k=b>d?this._scrollbarSize:0;return q.overflowX=b+E<=d?"hidden":"auto",q.overflowY=y+k<=i?"hidden":"auto",v.createElement("div",{ref:this._setScrollingContainerRef,"aria-label":this.props["aria-label"],className:Object(x.default)("ReactVirtualized__Collection",r),id:l,onScroll:this._onScroll,role:"grid",style:ue({},q,{},s),tabIndex:0},n>0&&v.createElement("div",{className:"ReactVirtualized__Collection__innerScrollContainer",style:{height:y,maxHeight:y,maxWidth:b,overflow:"hidden",pointerEvents:h?"none":"",width:b}},O),0===n&&c())}},{key:"_enablePointerEventsAfterDelay",value:function(){var e=this;this._disablePointerEventsTimeoutId&&clearTimeout(this._disablePointerEventsTimeoutId),this._disablePointerEventsTimeoutId=setTimeout((function(){(0,e.props.isScrollingChange)(!1),e._disablePointerEventsTimeoutId=null,e.setState({isScrolling:!1})}),150)}},{key:"_invokeOnScrollMemoizer",value:function(e){var t=this,n=e.scrollLeft,o=e.scrollTop,r=e.totalHeight,i=e.totalWidth;this._onScrollMemoizer({callback:function(e){var n=e.scrollLeft,o=e.scrollTop,a=t.props,l=a.height;(0,a.onScroll)({clientHeight:l,clientWidth:a.width,scrollHeight:r,scrollLeft:n,scrollTop:o,scrollWidth:i})},indices:{scrollLeft:n,scrollTop:o}})}},{key:"_setScrollPosition",value:function(e){var t=e.scrollLeft,n=e.scrollTop,o={scrollPositionChangeReason:pe};t>=0&&(o.scrollLeft=t),n>=0&&(o.scrollTop=n),(t>=0&&t!==this.state.scrollLeft||n>=0&&n!==this.state.scrollTop)&&this.setState(o)}}],[{key:"getDerivedStateFromProps",value:function(e,t){return 0!==e.cellCount||0===t.scrollLeft&&0===t.scrollTop?e.scrollLeft!==t.scrollLeft||e.scrollTop!==t.scrollTop?{scrollLeft:null!=e.scrollLeft?e.scrollLeft:t.scrollLeft,scrollTop:null!=e.scrollTop?e.scrollTop:t.scrollTop,scrollPositionChangeReason:pe}:null:{scrollLeft:0,scrollTop:0,scrollPositionChangeReason:pe}}}]),t}(v.PureComponent);g()(he,"defaultProps",{"aria-label":"grid",horizontalOverscanSize:0,noContentRenderer:function(){return null},onScroll:function(){return null},onSectionRendered:function(){return null},scrollToAlignment:"auto",scrollToCell:-1,style:{},verticalOverscanSize:0}),he.propTypes={},Object(y.polyfill)(he);var fe=he,me=function(){function e(t){var n=t.height,o=t.width,i=t.x,a=t.y;r()(this,e),this.height=n,this.width=o,this.x=i,this.y=a,this._indexMap={},this._indices=[]}return a()(e,[{key:"addCellIndex",value:function(e){var t=e.index;this._indexMap[t]||(this._indexMap[t]=!0,this._indices.push(t))}},{key:"getCellIndices",value:function(){return this._indices}},{key:"toString",value:function(){return"".concat(this.x,",").concat(this.y," ").concat(this.width,"x").concat(this.height)}}]),e}(),ge=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:100;r()(this,e),this._sectionSize=t,this._cellMetadata=[],this._sections={}}return a()(e,[{key:"getCellIndices",value:function(e){var t=e.height,n=e.width,o=e.x,r=e.y,i={};return this.getSections({height:t,width:n,x:o,y:r}).forEach((function(e){return e.getCellIndices().forEach((function(e){i[e]=e}))})),Object.keys(i).map((function(e){return i[e]}))}},{key:"getCellMetadata",value:function(e){var t=e.index;return this._cellMetadata[t]}},{key:"getSections",value:function(e){for(var t=e.height,n=e.width,o=e.x,r=e.y,i=Math.floor(o/this._sectionSize),a=Math.floor((o+n-1)/this._sectionSize),l=Math.floor(r/this._sectionSize),c=Math.floor((r+t-1)/this._sectionSize),s=[],u=i;u<=a;u++)for(var d=l;d<=c;d++){var p="".concat(u,".").concat(d);this._sections[p]||(this._sections[p]=new me({height:this._sectionSize,width:this._sectionSize,x:u*this._sectionSize,y:d*this._sectionSize})),s.push(this._sections[p])}return s}},{key:"getTotalSectionCount",value:function(){return Object.keys(this._sections).length}},{key:"toString",value:function(){var e=this;return Object.keys(this._sections).map((function(t){return e._sections[t].toString()}))}},{key:"registerCell",value:function(e){var t=e.cellMetadatum,n=e.index;this._cellMetadata[n]=t,this.getSections(t).forEach((function(e){return e.addCellIndex({index:n})}))}}]),e}();function ve(e){var t=e.align,n=void 0===t?"auto":t,o=e.cellOffset,r=e.cellSize,i=e.containerSize,a=e.currentOffset,l=o,c=l-i+r;switch(n){case"start":return l;case"end":return c;case"center":return l-(i-r)/2;default:return Math.max(c,Math.min(l,a))}}var ye=function(e){function t(e,n){var o;return r()(this,t),(o=c()(this,u()(t).call(this,e,n)))._cellMetadata=[],o._lastRenderedCellIndices=[],o._cellCache=[],o._isScrollingChange=o._isScrollingChange.bind(p()(o)),o._setCollectionViewRef=o._setCollectionViewRef.bind(p()(o)),o}return f()(t,e),a()(t,[{key:"forceUpdate",value:function(){void 0!==this._collectionView&&this._collectionView.forceUpdate()}},{key:"recomputeCellSizesAndPositions",value:function(){this._cellCache=[],this._collectionView.recomputeCellSizesAndPositions()}},{key:"render",value:function(){var e=w()({},this.props);return v.createElement(fe,w()({cellLayoutManager:this,isScrollingChange:this._isScrollingChange,ref:this._setCollectionViewRef},e))}},{key:"calculateSizeAndPositionData",value:function(){var e=this.props,t=function(e){for(var t=e.cellCount,n=e.cellSizeAndPositionGetter,o=e.sectionSize,r=[],i=new ge(o),a=0,l=0,c=0;c<t;c++){var s=n({index:c});if(null==s.height||isNaN(s.height)||null==s.width||isNaN(s.width)||null==s.x||isNaN(s.x)||null==s.y||isNaN(s.y))throw Error("Invalid metadata returned for cell ".concat(c,":\n        x:").concat(s.x,", y:").concat(s.y,", width:").concat(s.width,", height:").concat(s.height));a=Math.max(a,s.y+s.height),l=Math.max(l,s.x+s.width),r[c]=s,i.registerCell({cellMetadatum:s,index:c})}return{cellMetadata:r,height:a,sectionManager:i,width:l}}({cellCount:e.cellCount,cellSizeAndPositionGetter:e.cellSizeAndPositionGetter,sectionSize:e.sectionSize});this._cellMetadata=t.cellMetadata,this._sectionManager=t.sectionManager,this._height=t.height,this._width=t.width}},{key:"getLastRenderedIndices",value:function(){return this._lastRenderedCellIndices}},{key:"getScrollPositionForCell",value:function(e){var t=e.align,n=e.cellIndex,o=e.height,r=e.scrollLeft,i=e.scrollTop,a=e.width,l=this.props.cellCount;if(n>=0&&n<l){var c=this._cellMetadata[n];r=ve({align:t,cellOffset:c.x,cellSize:c.width,containerSize:a,currentOffset:r,targetIndex:n}),i=ve({align:t,cellOffset:c.y,cellSize:c.height,containerSize:o,currentOffset:i,targetIndex:n})}return{scrollLeft:r,scrollTop:i}}},{key:"getTotalSize",value:function(){return{height:this._height,width:this._width}}},{key:"cellRenderers",value:function(e){var t=this,n=e.height,o=e.isScrolling,r=e.width,i=e.x,a=e.y,l=this.props,c=l.cellGroupRenderer,s=l.cellRenderer;return this._lastRenderedCellIndices=this._sectionManager.getCellIndices({height:n,width:r,x:i,y:a}),c({cellCache:this._cellCache,cellRenderer:s,cellSizeAndPositionGetter:function(e){var n=e.index;return t._sectionManager.getCellMetadata({index:n})},indices:this._lastRenderedCellIndices,isScrolling:o})}},{key:"_isScrollingChange",value:function(e){e||(this._cellCache=[])}},{key:"_setCollectionViewRef",value:function(e){this._collectionView=e}}]),t}(v.PureComponent);g()(ye,"defaultProps",{"aria-label":"grid",cellGroupRenderer:function(e){var t=e.cellCache,n=e.cellRenderer,o=e.cellSizeAndPositionGetter,r=e.indices,i=e.isScrolling;return r.map((function(e){var r=o({index:e}),a={index:e,isScrolling:i,key:e,style:{height:r.height,left:r.x,position:"absolute",top:r.y,width:r.width}};return i?(e in t||(t[e]=n(a)),t[e]):n(a)})).filter((function(e){return!!e}))}}),ye.propTypes={};var be=function(e){function t(e,n){var o;return r()(this,t),(o=c()(this,u()(t).call(this,e,n)))._registerChild=o._registerChild.bind(p()(o)),o}return f()(t,e),a()(t,[{key:"componentDidUpdate",value:function(e){var t=this.props,n=t.columnMaxWidth,o=t.columnMinWidth,r=t.columnCount,i=t.width;n===e.columnMaxWidth&&o===e.columnMinWidth&&r===e.columnCount&&i===e.width||this._registeredChild&&this._registeredChild.recomputeGridSize()}},{key:"render",value:function(){var e=this.props,t=e.children,n=e.columnMaxWidth,o=e.columnMinWidth,r=e.columnCount,i=e.width,a=o||1,l=n?Math.min(n,i):i,c=i/r;return c=Math.max(a,c),c=Math.min(l,c),c=Math.floor(c),t({adjustedWidth:Math.min(i,c*r),columnWidth:c,getColumnWidth:function(){return c},registerChild:this._registerChild})}},{key:"_registerChild",value:function(e){if(e&&"function"!=typeof e.recomputeGridSize)throw Error("Unexpected child type registered; only Grid/MultiGrid children are supported.");this._registeredChild=e,this._registeredChild&&this._registeredChild.recomputeGridSize()}}]),t}(v.PureComponent);be.propTypes={};var we=n(1522),xe=n.n(we),Se=function(e){function t(e,n){var o;return r()(this,t),(o=c()(this,u()(t).call(this,e,n)))._loadMoreRowsMemoizer=k(),o._onRowsRendered=o._onRowsRendered.bind(p()(o)),o._registerChild=o._registerChild.bind(p()(o)),o}return f()(t,e),a()(t,[{key:"resetLoadMoreRowsCache",value:function(e){this._loadMoreRowsMemoizer=k(),e&&this._doStuff(this._lastRenderedStartIndex,this._lastRenderedStopIndex)}},{key:"render",value:function(){return(0,this.props.children)({onRowsRendered:this._onRowsRendered,registerChild:this._registerChild})}},{key:"_loadUnloadedRanges",value:function(e){var t=this,n=this.props.loadMoreRows;e.forEach((function(e){var o=n(e);o&&o.then((function(){var n,o,r,i,a;n={lastRenderedStartIndex:t._lastRenderedStartIndex,lastRenderedStopIndex:t._lastRenderedStopIndex,startIndex:e.startIndex,stopIndex:e.stopIndex},o=n.lastRenderedStartIndex,r=n.lastRenderedStopIndex,i=n.startIndex,a=n.stopIndex,i>r||a<o||t._registeredChild&&function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n="function"==typeof e.recomputeGridSize?e.recomputeGridSize:e.recomputeRowHeights;n?n.call(e,t):e.forceUpdate()}(t._registeredChild,t._lastRenderedStartIndex)}))}))}},{key:"_onRowsRendered",value:function(e){var t=e.startIndex,n=e.stopIndex;this._lastRenderedStartIndex=t,this._lastRenderedStopIndex=n,this._doStuff(t,n)}},{key:"_doStuff",value:function(e,t){var n,o=this,r=this.props,i=r.isRowLoaded,a=r.minimumBatchSize,l=r.rowCount,c=r.threshold,s=function(e){for(var t=e.isRowLoaded,n=e.minimumBatchSize,o=e.rowCount,r=e.startIndex,i=e.stopIndex,a=[],l=null,c=null,s=r;s<=i;s++){t({index:s})?null!==c&&(a.push({startIndex:l,stopIndex:c}),l=c=null):(c=s,null===l&&(l=s))}if(null!==c){for(var u=Math.min(Math.max(c,l+n-1),o-1),d=c+1;d<=u&&!t({index:d});d++)c=d;a.push({startIndex:l,stopIndex:c})}if(a.length)for(var p=a[0];p.stopIndex-p.startIndex+1<n&&p.startIndex>0;){var h=p.startIndex-1;if(t({index:h}))break;p.startIndex=h}return a}({isRowLoaded:i,minimumBatchSize:a,rowCount:l,startIndex:Math.max(0,e-c),stopIndex:Math.min(l-1,t+c)}),u=(n=[]).concat.apply(n,xe()(s.map((function(e){return[e.startIndex,e.stopIndex]}))));this._loadMoreRowsMemoizer({callback:function(){o._loadUnloadedRanges(s)},indices:{squashedUnloadedRanges:u}})}},{key:"_registerChild",value:function(e){this._registeredChild=e}}]),t}(v.PureComponent);g()(Se,"defaultProps",{minimumBatchSize:10,rowCount:0,threshold:15}),Se.propTypes={};var _e,Ce,Oe=(Ce=_e=function(e){function t(){var e,n;r()(this,t);for(var o=arguments.length,i=new Array(o),a=0;a<o;a++)i[a]=arguments[a];return n=c()(this,(e=u()(t)).call.apply(e,[this].concat(i))),g()(p()(n),"Grid",void 0),g()(p()(n),"_cellRenderer",(function(e){var t=e.parent,o=e.rowIndex,r=e.style,i=e.isScrolling,a=e.isVisible,l=e.key,c=n.props.rowRenderer,s=Object.getOwnPropertyDescriptor(r,"width");return s&&s.writable&&(r.width="100%"),c({index:o,style:r,isScrolling:i,isVisible:a,key:l,parent:t})})),g()(p()(n),"_setRef",(function(e){n.Grid=e})),g()(p()(n),"_onScroll",(function(e){var t=e.clientHeight,o=e.scrollHeight,r=e.scrollTop;(0,n.props.onScroll)({clientHeight:t,scrollHeight:o,scrollTop:r})})),g()(p()(n),"_onSectionRendered",(function(e){var t=e.rowOverscanStartIndex,o=e.rowOverscanStopIndex,r=e.rowStartIndex,i=e.rowStopIndex;(0,n.props.onRowsRendered)({overscanStartIndex:t,overscanStopIndex:o,startIndex:r,stopIndex:i})})),n}return f()(t,e),a()(t,[{key:"forceUpdateGrid",value:function(){this.Grid&&this.Grid.forceUpdate()}},{key:"getOffsetForRow",value:function(e){var t=e.alignment,n=e.index;return this.Grid?this.Grid.getOffsetForCell({alignment:t,rowIndex:n,columnIndex:0}).scrollTop:0}},{key:"invalidateCellSizeAfterRender",value:function(e){var t=e.columnIndex,n=e.rowIndex;this.Grid&&this.Grid.invalidateCellSizeAfterRender({rowIndex:n,columnIndex:t})}},{key:"measureAllRows",value:function(){this.Grid&&this.Grid.measureAllCells()}},{key:"recomputeGridSize",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.columnIndex,n=void 0===t?0:t,o=e.rowIndex,r=void 0===o?0:o;this.Grid&&this.Grid.recomputeGridSize({rowIndex:r,columnIndex:n})}},{key:"recomputeRowHeights",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;this.Grid&&this.Grid.recomputeGridSize({rowIndex:e,columnIndex:0})}},{key:"scrollToPosition",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;this.Grid&&this.Grid.scrollToPosition({scrollTop:e})}},{key:"scrollToRow",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;this.Grid&&this.Grid.scrollToCell({columnIndex:0,rowIndex:e})}},{key:"render",value:function(){var e=this.props,t=e.className,n=e.noRowsRenderer,o=e.scrollToIndex,r=e.width,i=Object(x.default)("ReactVirtualized__List",t);return v.createElement(B,w()({},this.props,{autoContainerWidth:!0,cellRenderer:this._cellRenderer,className:i,columnWidth:r,columnCount:1,noContentRenderer:n,onScroll:this._onScroll,onSectionRendered:this._onSectionRendered,ref:this._setRef,scrollToRow:o}))}}]),t}(v.PureComponent),g()(_e,"propTypes",null),Ce);g()(Oe,"defaultProps",{autoHeight:!1,estimatedRowSize:30,onScroll:function(){},noRowsRenderer:function(){return null},onRowsRendered:function(){},overscanIndicesGetter:V,overscanRowCount:10,scrollToAlignment:"auto",scrollToIndex:-1,style:{}});var qe=n(285),Ee=n.n(qe);var ke={ge:function(e,t,n,o,r){return"function"==typeof n?function(e,t,n,o,r){for(var i=n+1;t<=n;){var a=t+n>>>1;r(e[a],o)>=0?(i=a,n=a-1):t=a+1}return i}(e,void 0===o?0:0|o,void 0===r?e.length-1:0|r,t,n):function(e,t,n,o){for(var r=n+1;t<=n;){var i=t+n>>>1;e[i]>=o?(r=i,n=i-1):t=i+1}return r}(e,void 0===n?0:0|n,void 0===o?e.length-1:0|o,t)},gt:function(e,t,n,o,r){return"function"==typeof n?function(e,t,n,o,r){for(var i=n+1;t<=n;){var a=t+n>>>1;r(e[a],o)>0?(i=a,n=a-1):t=a+1}return i}(e,void 0===o?0:0|o,void 0===r?e.length-1:0|r,t,n):function(e,t,n,o){for(var r=n+1;t<=n;){var i=t+n>>>1;e[i]>o?(r=i,n=i-1):t=i+1}return r}(e,void 0===n?0:0|n,void 0===o?e.length-1:0|o,t)},lt:function(e,t,n,o,r){return"function"==typeof n?function(e,t,n,o,r){for(var i=t-1;t<=n;){var a=t+n>>>1;r(e[a],o)<0?(i=a,t=a+1):n=a-1}return i}(e,void 0===o?0:0|o,void 0===r?e.length-1:0|r,t,n):function(e,t,n,o){for(var r=t-1;t<=n;){var i=t+n>>>1;e[i]<o?(r=i,t=i+1):n=i-1}return r}(e,void 0===n?0:0|n,void 0===o?e.length-1:0|o,t)},le:function(e,t,n,o,r){return"function"==typeof n?function(e,t,n,o,r){for(var i=t-1;t<=n;){var a=t+n>>>1;r(e[a],o)<=0?(i=a,t=a+1):n=a-1}return i}(e,void 0===o?0:0|o,void 0===r?e.length-1:0|r,t,n):function(e,t,n,o){for(var r=t-1;t<=n;){var i=t+n>>>1;e[i]<=o?(r=i,t=i+1):n=i-1}return r}(e,void 0===n?0:0|n,void 0===o?e.length-1:0|o,t)},eq:function(e,t,n,o,r){return"function"==typeof n?function(e,t,n,o,r){for(;t<=n;){var i=t+n>>>1,a=r(e[i],o);if(0===a)return i;a<=0?t=i+1:n=i-1}return-1}(e,void 0===o?0:0|o,void 0===r?e.length-1:0|r,t,n):function(e,t,n,o){for(;t<=n;){var r=t+n>>>1,i=e[r];if(i===o)return r;i<=o?t=r+1:n=r-1}return-1}(e,void 0===n?0:0|n,void 0===o?e.length-1:0|o,t)}};function Te(e,t,n,o,r){this.mid=e,this.left=t,this.right=n,this.leftPoints=o,this.rightPoints=r,this.count=(t?t.count:0)+(n?n.count:0)+o.length}var Re=Te.prototype;function Ie(e,t){e.mid=t.mid,e.left=t.left,e.right=t.right,e.leftPoints=t.leftPoints,e.rightPoints=t.rightPoints,e.count=t.count}function Ae(e,t){var n=Ge(t);e.mid=n.mid,e.left=n.left,e.right=n.right,e.leftPoints=n.leftPoints,e.rightPoints=n.rightPoints,e.count=n.count}function je(e,t){var n=e.intervals([]);n.push(t),Ae(e,n)}function Pe(e,t){var n=e.intervals([]),o=n.indexOf(t);return o<0?0:(n.splice(o,1),Ae(e,n),1)}function Ne(e,t,n){for(var o=0;o<e.length&&e[o][0]<=t;++o){var r=n(e[o]);if(r)return r}}function Le(e,t,n){for(var o=e.length-1;o>=0&&e[o][1]>=t;--o){var r=n(e[o]);if(r)return r}}function Me(e,t){for(var n=0;n<e.length;++n){var o=t(e[n]);if(o)return o}}function ze(e,t){return e-t}function He(e,t){var n=e[0]-t[0];return n||e[1]-t[1]}function De(e,t){var n=e[1]-t[1];return n||e[0]-t[0]}function Ge(e){if(0===e.length)return null;for(var t=[],n=0;n<e.length;++n)t.push(e[n][0],e[n][1]);t.sort(ze);var o=t[t.length>>1],r=[],i=[],a=[];for(n=0;n<e.length;++n){var l=e[n];l[1]<o?r.push(l):o<l[0]?i.push(l):a.push(l)}var c=a,s=a.slice();return c.sort(He),s.sort(De),new Te(o,Ge(r),Ge(i),c,s)}function Fe(e){this.root=e}Re.intervals=function(e){return e.push.apply(e,this.leftPoints),this.left&&this.left.intervals(e),this.right&&this.right.intervals(e),e},Re.insert=function(e){var t=this.count-this.leftPoints.length;if(this.count+=1,e[1]<this.mid)this.left?4*(this.left.count+1)>3*(t+1)?je(this,e):this.left.insert(e):this.left=Ge([e]);else if(e[0]>this.mid)this.right?4*(this.right.count+1)>3*(t+1)?je(this,e):this.right.insert(e):this.right=Ge([e]);else{var n=ke.ge(this.leftPoints,e,He),o=ke.ge(this.rightPoints,e,De);this.leftPoints.splice(n,0,e),this.rightPoints.splice(o,0,e)}},Re.remove=function(e){var t=this.count-this.leftPoints;if(e[1]<this.mid)return this.left?4*(this.right?this.right.count:0)>3*(t-1)?Pe(this,e):2===(i=this.left.remove(e))?(this.left=null,this.count-=1,1):(1===i&&(this.count-=1),i):0;if(e[0]>this.mid)return this.right?4*(this.left?this.left.count:0)>3*(t-1)?Pe(this,e):2===(i=this.right.remove(e))?(this.right=null,this.count-=1,1):(1===i&&(this.count-=1),i):0;if(1===this.count)return this.leftPoints[0]===e?2:0;if(1===this.leftPoints.length&&this.leftPoints[0]===e){if(this.left&&this.right){for(var n=this,o=this.left;o.right;)n=o,o=o.right;if(n===this)o.right=this.right;else{var r=this.left,i=this.right;n.count-=o.count,n.right=o.left,o.left=r,o.right=i}Ie(this,o),this.count=(this.left?this.left.count:0)+(this.right?this.right.count:0)+this.leftPoints.length}else this.left?Ie(this,this.left):Ie(this,this.right);return 1}for(r=ke.ge(this.leftPoints,e,He);r<this.leftPoints.length&&this.leftPoints[r][0]===e[0];++r)if(this.leftPoints[r]===e){this.count-=1,this.leftPoints.splice(r,1);for(i=ke.ge(this.rightPoints,e,De);i<this.rightPoints.length&&this.rightPoints[i][1]===e[1];++i)if(this.rightPoints[i]===e)return this.rightPoints.splice(i,1),1}return 0},Re.queryPoint=function(e,t){if(e<this.mid){if(this.left)if(n=this.left.queryPoint(e,t))return n;return Ne(this.leftPoints,e,t)}if(e>this.mid){var n;if(this.right)if(n=this.right.queryPoint(e,t))return n;return Le(this.rightPoints,e,t)}return Me(this.leftPoints,t)},Re.queryInterval=function(e,t,n){var o;if(e<this.mid&&this.left&&(o=this.left.queryInterval(e,t,n)))return o;if(t>this.mid&&this.right&&(o=this.right.queryInterval(e,t,n)))return o;return t<this.mid?Ne(this.leftPoints,t,n):e>this.mid?Le(this.rightPoints,e,n):Me(this.leftPoints,n)};var We=Fe.prototype;We.insert=function(e){this.root?this.root.insert(e):this.root=new Te(e[0],null,null,[e],[e])},We.remove=function(e){if(this.root){var t=this.root.remove(e);return 2===t&&(this.root=null),0!==t}return!1},We.queryPoint=function(e,t){if(this.root)return this.root.queryPoint(e,t)},We.queryInterval=function(e,t,n){if(e<=t&&this.root)return this.root.queryInterval(e,t,n)},Object.defineProperty(We,"count",{get:function(){return this.root?this.root.count:0}}),Object.defineProperty(We,"intervals",{get:function(){return this.root?this.root.intervals([]):[]}});var Ue,Be,Ve=function(){function e(){var t;r()(this,e),g()(this,"_columnSizeMap",{}),g()(this,"_intervalTree",t&&0!==t.length?new Fe(Ge(t)):new Fe(null)),g()(this,"_leftMap",{})}return a()(e,[{key:"estimateTotalHeight",value:function(e,t,n){var o=e-this.count;return this.tallestColumnSize+Math.ceil(o/t)*n}},{key:"range",value:function(e,t,n){var o=this;this._intervalTree.queryInterval(e,e+t,(function(e){var t=Ee()(e,3),r=t[0],i=(t[1],t[2]);return n(i,o._leftMap[i],r)}))}},{key:"setPosition",value:function(e,t,n,o){this._intervalTree.insert([n,n+o,e]),this._leftMap[e]=t;var r=this._columnSizeMap,i=r[t];r[t]=void 0===i?n+o:Math.max(i,n+o)}},{key:"count",get:function(){return this._intervalTree.count}},{key:"shortestColumnSize",get:function(){var e=this._columnSizeMap,t=0;for(var n in e){var o=e[n];t=0===t?o:Math.min(t,o)}return t}},{key:"tallestColumnSize",get:function(){var e=this._columnSizeMap,t=0;for(var n in e){var o=e[n];t=Math.max(t,o)}return t}}]),e}();function Ye(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function Ke(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ye(n,!0).forEach((function(t){g()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ye(n).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var $e=(Be=Ue=function(e){function t(){var e,n;r()(this,t);for(var o=arguments.length,i=new Array(o),a=0;a<o;a++)i[a]=arguments[a];return n=c()(this,(e=u()(t)).call.apply(e,[this].concat(i))),g()(p()(n),"state",{isScrolling:!1,scrollTop:0}),g()(p()(n),"_debounceResetIsScrollingId",void 0),g()(p()(n),"_invalidateOnUpdateStartIndex",null),g()(p()(n),"_invalidateOnUpdateStopIndex",null),g()(p()(n),"_positionCache",new Ve),g()(p()(n),"_startIndex",null),g()(p()(n),"_startIndexMemoized",null),g()(p()(n),"_stopIndex",null),g()(p()(n),"_stopIndexMemoized",null),g()(p()(n),"_debounceResetIsScrollingCallback",(function(){n.setState({isScrolling:!1})})),g()(p()(n),"_setScrollingContainerRef",(function(e){n._scrollingContainer=e})),g()(p()(n),"_onScroll",(function(e){var t=n.props.height,o=e.currentTarget.scrollTop,r=Math.min(Math.max(0,n._getEstimatedTotalHeight()-t),o);o===r&&(n._debounceResetIsScrolling(),n.state.scrollTop!==r&&n.setState({isScrolling:!0,scrollTop:r}))})),n}return f()(t,e),a()(t,[{key:"clearCellPositions",value:function(){this._positionCache=new Ve,this.forceUpdate()}},{key:"invalidateCellSizeAfterRender",value:function(e){var t=e.rowIndex;null===this._invalidateOnUpdateStartIndex?(this._invalidateOnUpdateStartIndex=t,this._invalidateOnUpdateStopIndex=t):(this._invalidateOnUpdateStartIndex=Math.min(this._invalidateOnUpdateStartIndex,t),this._invalidateOnUpdateStopIndex=Math.max(this._invalidateOnUpdateStopIndex,t))}},{key:"recomputeCellPositions",value:function(){var e=this._positionCache.count-1;this._positionCache=new Ve,this._populatePositionCache(0,e),this.forceUpdate()}},{key:"componentDidMount",value:function(){this._checkInvalidateOnUpdate(),this._invokeOnScrollCallback(),this._invokeOnCellsRenderedCallback()}},{key:"componentDidUpdate",value:function(e,t){this._checkInvalidateOnUpdate(),this._invokeOnScrollCallback(),this._invokeOnCellsRenderedCallback(),this.props.scrollTop!==e.scrollTop&&this._debounceResetIsScrolling()}},{key:"componentWillUnmount",value:function(){this._debounceResetIsScrollingId&&z(this._debounceResetIsScrollingId)}},{key:"render",value:function(){var e,t=this,n=this.props,o=n.autoHeight,r=n.cellCount,i=n.cellMeasurerCache,a=n.cellRenderer,l=n.className,c=n.height,s=n.id,u=n.keyMapper,d=n.overscanByPixels,p=n.role,h=n.style,f=n.tabIndex,m=n.width,y=n.rowDirection,b=this.state,w=b.isScrolling,S=b.scrollTop,_=[],C=this._getEstimatedTotalHeight(),O=this._positionCache.shortestColumnSize,q=this._positionCache.count,E=0;if(this._positionCache.range(Math.max(0,S-d),c+2*d,(function(n,o,r){var l;void 0===e?(E=n,e=n):(E=Math.min(E,n),e=Math.max(e,n)),_.push(a({index:n,isScrolling:w,key:u(n),parent:t,style:(l={height:i.getHeight(n)},g()(l,"ltr"===y?"left":"right",o),g()(l,"position","absolute"),g()(l,"top",r),g()(l,"width",i.getWidth(n)),l)}))})),O<S+c+d&&q<r)for(var k=Math.min(r-q,Math.ceil((S+c+d-O)/i.defaultHeight*m/i.defaultWidth)),T=q;T<q+k;T++)e=T,_.push(a({index:T,isScrolling:w,key:u(T),parent:this,style:{width:i.getWidth(T)}}));return this._startIndex=E,this._stopIndex=e,v.createElement("div",{ref:this._setScrollingContainerRef,"aria-label":this.props["aria-label"],className:Object(x.default)("ReactVirtualized__Masonry",l),id:s,onScroll:this._onScroll,role:p,style:Ke({boxSizing:"border-box",direction:"ltr",height:o?"auto":c,overflowX:"hidden",overflowY:C<c?"hidden":"auto",position:"relative",width:m,WebkitOverflowScrolling:"touch",willChange:"transform"},h),tabIndex:f},v.createElement("div",{className:"ReactVirtualized__Masonry__innerScrollContainer",style:{width:"100%",height:C,maxWidth:"100%",maxHeight:C,overflow:"hidden",pointerEvents:w?"none":"",position:"relative"}},_))}},{key:"_checkInvalidateOnUpdate",value:function(){if("number"==typeof this._invalidateOnUpdateStartIndex){var e=this._invalidateOnUpdateStartIndex,t=this._invalidateOnUpdateStopIndex;this._invalidateOnUpdateStartIndex=null,this._invalidateOnUpdateStopIndex=null,this._populatePositionCache(e,t),this.forceUpdate()}}},{key:"_debounceResetIsScrolling",value:function(){var e=this.props.scrollingResetTimeInterval;this._debounceResetIsScrollingId&&z(this._debounceResetIsScrollingId),this._debounceResetIsScrollingId=H(this._debounceResetIsScrollingCallback,e)}},{key:"_getEstimatedTotalHeight",value:function(){var e=this.props,t=e.cellCount,n=e.cellMeasurerCache,o=e.width,r=Math.max(1,Math.floor(o/n.defaultWidth));return this._positionCache.estimateTotalHeight(t,r,n.defaultHeight)}},{key:"_invokeOnScrollCallback",value:function(){var e=this.props,t=e.height,n=e.onScroll,o=this.state.scrollTop;this._onScrollMemoized!==o&&(n({clientHeight:t,scrollHeight:this._getEstimatedTotalHeight(),scrollTop:o}),this._onScrollMemoized=o)}},{key:"_invokeOnCellsRenderedCallback",value:function(){this._startIndexMemoized===this._startIndex&&this._stopIndexMemoized===this._stopIndex||((0,this.props.onCellsRendered)({startIndex:this._startIndex,stopIndex:this._stopIndex}),this._startIndexMemoized=this._startIndex,this._stopIndexMemoized=this._stopIndex)}},{key:"_populatePositionCache",value:function(e,t){for(var n=this.props,o=n.cellMeasurerCache,r=n.cellPositioner,i=e;i<=t;i++){var a=r(i),l=a.left,c=a.top;this._positionCache.setPosition(i,l,c,o.getHeight(i))}}}],[{key:"getDerivedStateFromProps",value:function(e,t){return void 0!==e.scrollTop&&t.scrollTop!==e.scrollTop?{isScrolling:!0,scrollTop:e.scrollTop}:null}}]),t}(v.PureComponent),g()(Ue,"propTypes",null),Be);function Xe(){}g()($e,"defaultProps",{autoHeight:!1,keyMapper:function(e){return e},onCellsRendered:Xe,onScroll:Xe,overscanByPixels:20,role:"grid",scrollingResetTimeInterval:150,style:{},tabIndex:0,rowDirection:"ltr"});Object(y.polyfill)($e);var Ze=function(){function e(){var t=this,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};r()(this,e),g()(this,"_cellMeasurerCache",void 0),g()(this,"_columnIndexOffset",void 0),g()(this,"_rowIndexOffset",void 0),g()(this,"columnWidth",(function(e){var n=e.index;t._cellMeasurerCache.columnWidth({index:n+t._columnIndexOffset})})),g()(this,"rowHeight",(function(e){var n=e.index;t._cellMeasurerCache.rowHeight({index:n+t._rowIndexOffset})}));var o=n.cellMeasurerCache,i=n.columnIndexOffset,a=void 0===i?0:i,l=n.rowIndexOffset,c=void 0===l?0:l;this._cellMeasurerCache=o,this._columnIndexOffset=a,this._rowIndexOffset=c}return a()(e,[{key:"clear",value:function(e,t){this._cellMeasurerCache.clear(e+this._rowIndexOffset,t+this._columnIndexOffset)}},{key:"clearAll",value:function(){this._cellMeasurerCache.clearAll()}},{key:"hasFixedHeight",value:function(){return this._cellMeasurerCache.hasFixedHeight()}},{key:"hasFixedWidth",value:function(){return this._cellMeasurerCache.hasFixedWidth()}},{key:"getHeight",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return this._cellMeasurerCache.getHeight(e+this._rowIndexOffset,t+this._columnIndexOffset)}},{key:"getWidth",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return this._cellMeasurerCache.getWidth(e+this._rowIndexOffset,t+this._columnIndexOffset)}},{key:"has",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return this._cellMeasurerCache.has(e+this._rowIndexOffset,t+this._columnIndexOffset)}},{key:"set",value:function(e,t,n,o){this._cellMeasurerCache.set(e+this._rowIndexOffset,t+this._columnIndexOffset,n,o)}},{key:"defaultHeight",get:function(){return this._cellMeasurerCache.defaultHeight}},{key:"defaultWidth",get:function(){return this._cellMeasurerCache.defaultWidth}}]),e}();function Je(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function Qe(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Je(n,!0).forEach((function(t){g()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Je(n).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var et=function(e){function t(e,n){var o;r()(this,t),o=c()(this,u()(t).call(this,e,n)),g()(p()(o),"state",{scrollLeft:0,scrollTop:0,scrollbarSize:0,showHorizontalScrollbar:!1,showVerticalScrollbar:!1}),g()(p()(o),"_deferredInvalidateColumnIndex",null),g()(p()(o),"_deferredInvalidateRowIndex",null),g()(p()(o),"_bottomLeftGridRef",(function(e){o._bottomLeftGrid=e})),g()(p()(o),"_bottomRightGridRef",(function(e){o._bottomRightGrid=e})),g()(p()(o),"_cellRendererBottomLeftGrid",(function(e){var t=e.rowIndex,n=C()(e,["rowIndex"]),r=o.props,i=r.cellRenderer,a=r.fixedRowCount;return t===r.rowCount-a?v.createElement("div",{key:n.key,style:Qe({},n.style,{height:20})}):i(Qe({},n,{parent:p()(o),rowIndex:t+a}))})),g()(p()(o),"_cellRendererBottomRightGrid",(function(e){var t=e.columnIndex,n=e.rowIndex,r=C()(e,["columnIndex","rowIndex"]),i=o.props,a=i.cellRenderer,l=i.fixedColumnCount,c=i.fixedRowCount;return a(Qe({},r,{columnIndex:t+l,parent:p()(o),rowIndex:n+c}))})),g()(p()(o),"_cellRendererTopRightGrid",(function(e){var t=e.columnIndex,n=C()(e,["columnIndex"]),r=o.props,i=r.cellRenderer,a=r.columnCount,l=r.fixedColumnCount;return t===a-l?v.createElement("div",{key:n.key,style:Qe({},n.style,{width:20})}):i(Qe({},n,{columnIndex:t+l,parent:p()(o)}))})),g()(p()(o),"_columnWidthRightGrid",(function(e){var t=e.index,n=o.props,r=n.columnCount,i=n.fixedColumnCount,a=n.columnWidth,l=o.state,c=l.scrollbarSize;return l.showHorizontalScrollbar&&t===r-i?c:"function"==typeof a?a({index:t+i}):a})),g()(p()(o),"_onScroll",(function(e){var t=e.scrollLeft,n=e.scrollTop;o.setState({scrollLeft:t,scrollTop:n});var r=o.props.onScroll;r&&r(e)})),g()(p()(o),"_onScrollbarPresenceChange",(function(e){var t=e.horizontal,n=e.size,r=e.vertical,i=o.state,a=i.showHorizontalScrollbar,l=i.showVerticalScrollbar;if(t!==a||r!==l){o.setState({scrollbarSize:n,showHorizontalScrollbar:t,showVerticalScrollbar:r});var c=o.props.onScrollbarPresenceChange;"function"==typeof c&&c({horizontal:t,size:n,vertical:r})}})),g()(p()(o),"_onScrollLeft",(function(e){var t=e.scrollLeft;o._onScroll({scrollLeft:t,scrollTop:o.state.scrollTop})})),g()(p()(o),"_onScrollTop",(function(e){var t=e.scrollTop;o._onScroll({scrollTop:t,scrollLeft:o.state.scrollLeft})})),g()(p()(o),"_rowHeightBottomGrid",(function(e){var t=e.index,n=o.props,r=n.fixedRowCount,i=n.rowCount,a=n.rowHeight,l=o.state,c=l.scrollbarSize;return l.showVerticalScrollbar&&t===i-r?c:"function"==typeof a?a({index:t+r}):a})),g()(p()(o),"_topLeftGridRef",(function(e){o._topLeftGrid=e})),g()(p()(o),"_topRightGridRef",(function(e){o._topRightGrid=e}));var i=e.deferredMeasurementCache,a=e.fixedColumnCount,l=e.fixedRowCount;return o._maybeCalculateCachedStyles(!0),i&&(o._deferredMeasurementCacheBottomLeftGrid=l>0?new Ze({cellMeasurerCache:i,columnIndexOffset:0,rowIndexOffset:l}):i,o._deferredMeasurementCacheBottomRightGrid=a>0||l>0?new Ze({cellMeasurerCache:i,columnIndexOffset:a,rowIndexOffset:l}):i,o._deferredMeasurementCacheTopRightGrid=a>0?new Ze({cellMeasurerCache:i,columnIndexOffset:a,rowIndexOffset:0}):i),o}return f()(t,e),a()(t,[{key:"forceUpdateGrids",value:function(){this._bottomLeftGrid&&this._bottomLeftGrid.forceUpdate(),this._bottomRightGrid&&this._bottomRightGrid.forceUpdate(),this._topLeftGrid&&this._topLeftGrid.forceUpdate(),this._topRightGrid&&this._topRightGrid.forceUpdate()}},{key:"invalidateCellSizeAfterRender",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.columnIndex,n=void 0===t?0:t,o=e.rowIndex,r=void 0===o?0:o;this._deferredInvalidateColumnIndex="number"==typeof this._deferredInvalidateColumnIndex?Math.min(this._deferredInvalidateColumnIndex,n):n,this._deferredInvalidateRowIndex="number"==typeof this._deferredInvalidateRowIndex?Math.min(this._deferredInvalidateRowIndex,r):r}},{key:"measureAllCells",value:function(){this._bottomLeftGrid&&this._bottomLeftGrid.measureAllCells(),this._bottomRightGrid&&this._bottomRightGrid.measureAllCells(),this._topLeftGrid&&this._topLeftGrid.measureAllCells(),this._topRightGrid&&this._topRightGrid.measureAllCells()}},{key:"recomputeGridSize",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.columnIndex,n=void 0===t?0:t,o=e.rowIndex,r=void 0===o?0:o,i=this.props,a=i.fixedColumnCount,l=i.fixedRowCount,c=Math.max(0,n-a),s=Math.max(0,r-l);this._bottomLeftGrid&&this._bottomLeftGrid.recomputeGridSize({columnIndex:n,rowIndex:s}),this._bottomRightGrid&&this._bottomRightGrid.recomputeGridSize({columnIndex:c,rowIndex:s}),this._topLeftGrid&&this._topLeftGrid.recomputeGridSize({columnIndex:n,rowIndex:r}),this._topRightGrid&&this._topRightGrid.recomputeGridSize({columnIndex:c,rowIndex:r}),this._leftGridWidth=null,this._topGridHeight=null,this._maybeCalculateCachedStyles(!0)}},{key:"componentDidMount",value:function(){var e=this.props,t=e.scrollLeft,n=e.scrollTop;if(t>0||n>0){var o={};t>0&&(o.scrollLeft=t),n>0&&(o.scrollTop=n),this.setState(o)}this._handleInvalidatedGridSize()}},{key:"componentDidUpdate",value:function(){this._handleInvalidatedGridSize()}},{key:"render",value:function(){var e=this.props,t=e.onScroll,n=e.onSectionRendered,o=(e.onScrollbarPresenceChange,e.scrollLeft,e.scrollToColumn),r=(e.scrollTop,e.scrollToRow),i=C()(e,["onScroll","onSectionRendered","onScrollbarPresenceChange","scrollLeft","scrollToColumn","scrollTop","scrollToRow"]);if(this._prepareForRender(),0===this.props.width||0===this.props.height)return null;var a=this.state,l=a.scrollLeft,c=a.scrollTop;return v.createElement("div",{style:this._containerOuterStyle},v.createElement("div",{style:this._containerTopStyle},this._renderTopLeftGrid(i),this._renderTopRightGrid(Qe({},i,{onScroll:t,scrollLeft:l}))),v.createElement("div",{style:this._containerBottomStyle},this._renderBottomLeftGrid(Qe({},i,{onScroll:t,scrollTop:c})),this._renderBottomRightGrid(Qe({},i,{onScroll:t,onSectionRendered:n,scrollLeft:l,scrollToColumn:o,scrollToRow:r,scrollTop:c}))))}},{key:"_getBottomGridHeight",value:function(e){return e.height-this._getTopGridHeight(e)}},{key:"_getLeftGridWidth",value:function(e){var t=e.fixedColumnCount,n=e.columnWidth;if(null==this._leftGridWidth)if("function"==typeof n){for(var o=0,r=0;r<t;r++)o+=n({index:r});this._leftGridWidth=o}else this._leftGridWidth=n*t;return this._leftGridWidth}},{key:"_getRightGridWidth",value:function(e){return e.width-this._getLeftGridWidth(e)}},{key:"_getTopGridHeight",value:function(e){var t=e.fixedRowCount,n=e.rowHeight;if(null==this._topGridHeight)if("function"==typeof n){for(var o=0,r=0;r<t;r++)o+=n({index:r});this._topGridHeight=o}else this._topGridHeight=n*t;return this._topGridHeight}},{key:"_handleInvalidatedGridSize",value:function(){if("number"==typeof this._deferredInvalidateColumnIndex){var e=this._deferredInvalidateColumnIndex,t=this._deferredInvalidateRowIndex;this._deferredInvalidateColumnIndex=null,this._deferredInvalidateRowIndex=null,this.recomputeGridSize({columnIndex:e,rowIndex:t}),this.forceUpdate()}}},{key:"_maybeCalculateCachedStyles",value:function(e){var t=this.props,n=t.columnWidth,o=t.enableFixedColumnScroll,r=t.enableFixedRowScroll,i=t.height,a=t.fixedColumnCount,l=t.fixedRowCount,c=t.rowHeight,s=t.style,u=t.styleBottomLeftGrid,d=t.styleBottomRightGrid,p=t.styleTopLeftGrid,h=t.styleTopRightGrid,f=t.width,m=e||i!==this._lastRenderedHeight||f!==this._lastRenderedWidth,g=e||n!==this._lastRenderedColumnWidth||a!==this._lastRenderedFixedColumnCount,v=e||l!==this._lastRenderedFixedRowCount||c!==this._lastRenderedRowHeight;(e||m||s!==this._lastRenderedStyle)&&(this._containerOuterStyle=Qe({height:i,overflow:"visible",width:f},s)),(e||m||v)&&(this._containerTopStyle={height:this._getTopGridHeight(this.props),position:"relative",width:f},this._containerBottomStyle={height:i-this._getTopGridHeight(this.props),overflow:"visible",position:"relative",width:f}),(e||u!==this._lastRenderedStyleBottomLeftGrid)&&(this._bottomLeftGridStyle=Qe({left:0,overflowX:"hidden",overflowY:o?"auto":"hidden",position:"absolute"},u)),(e||g||d!==this._lastRenderedStyleBottomRightGrid)&&(this._bottomRightGridStyle=Qe({left:this._getLeftGridWidth(this.props),position:"absolute"},d)),(e||p!==this._lastRenderedStyleTopLeftGrid)&&(this._topLeftGridStyle=Qe({left:0,overflowX:"hidden",overflowY:"hidden",position:"absolute",top:0},p)),(e||g||h!==this._lastRenderedStyleTopRightGrid)&&(this._topRightGridStyle=Qe({left:this._getLeftGridWidth(this.props),overflowX:r?"auto":"hidden",overflowY:"hidden",position:"absolute",top:0},h)),this._lastRenderedColumnWidth=n,this._lastRenderedFixedColumnCount=a,this._lastRenderedFixedRowCount=l,this._lastRenderedHeight=i,this._lastRenderedRowHeight=c,this._lastRenderedStyle=s,this._lastRenderedStyleBottomLeftGrid=u,this._lastRenderedStyleBottomRightGrid=d,this._lastRenderedStyleTopLeftGrid=p,this._lastRenderedStyleTopRightGrid=h,this._lastRenderedWidth=f}},{key:"_prepareForRender",value:function(){this._lastRenderedColumnWidth===this.props.columnWidth&&this._lastRenderedFixedColumnCount===this.props.fixedColumnCount||(this._leftGridWidth=null),this._lastRenderedFixedRowCount===this.props.fixedRowCount&&this._lastRenderedRowHeight===this.props.rowHeight||(this._topGridHeight=null),this._maybeCalculateCachedStyles(),this._lastRenderedColumnWidth=this.props.columnWidth,this._lastRenderedFixedColumnCount=this.props.fixedColumnCount,this._lastRenderedFixedRowCount=this.props.fixedRowCount,this._lastRenderedRowHeight=this.props.rowHeight}},{key:"_renderBottomLeftGrid",value:function(e){var t=e.enableFixedColumnScroll,n=e.fixedColumnCount,o=e.fixedRowCount,r=e.rowCount,i=e.hideBottomLeftGridScrollbar,a=this.state.showVerticalScrollbar;if(!n)return null;var l=a?1:0,c=this._getBottomGridHeight(e),s=this._getLeftGridWidth(e),u=this.state.showVerticalScrollbar?this.state.scrollbarSize:0,d=i?s+u:s,p=v.createElement(B,w()({},e,{cellRenderer:this._cellRendererBottomLeftGrid,className:this.props.classNameBottomLeftGrid,columnCount:n,deferredMeasurementCache:this._deferredMeasurementCacheBottomLeftGrid,height:c,onScroll:t?this._onScrollTop:void 0,ref:this._bottomLeftGridRef,rowCount:Math.max(0,r-o)+l,rowHeight:this._rowHeightBottomGrid,style:this._bottomLeftGridStyle,tabIndex:null,width:d}));return i?v.createElement("div",{className:"BottomLeftGrid_ScrollWrapper",style:Qe({},this._bottomLeftGridStyle,{height:c,width:s,overflowY:"hidden"})},p):p}},{key:"_renderBottomRightGrid",value:function(e){var t=e.columnCount,n=e.fixedColumnCount,o=e.fixedRowCount,r=e.rowCount,i=e.scrollToColumn,a=e.scrollToRow;return v.createElement(B,w()({},e,{cellRenderer:this._cellRendererBottomRightGrid,className:this.props.classNameBottomRightGrid,columnCount:Math.max(0,t-n),columnWidth:this._columnWidthRightGrid,deferredMeasurementCache:this._deferredMeasurementCacheBottomRightGrid,height:this._getBottomGridHeight(e),onScroll:this._onScroll,onScrollbarPresenceChange:this._onScrollbarPresenceChange,ref:this._bottomRightGridRef,rowCount:Math.max(0,r-o),rowHeight:this._rowHeightBottomGrid,scrollToColumn:i-n,scrollToRow:a-o,style:this._bottomRightGridStyle,width:this._getRightGridWidth(e)}))}},{key:"_renderTopLeftGrid",value:function(e){var t=e.fixedColumnCount,n=e.fixedRowCount;return t&&n?v.createElement(B,w()({},e,{className:this.props.classNameTopLeftGrid,columnCount:t,height:this._getTopGridHeight(e),ref:this._topLeftGridRef,rowCount:n,style:this._topLeftGridStyle,tabIndex:null,width:this._getLeftGridWidth(e)})):null}},{key:"_renderTopRightGrid",value:function(e){var t=e.columnCount,n=e.enableFixedRowScroll,o=e.fixedColumnCount,r=e.fixedRowCount,i=e.scrollLeft,a=e.hideTopRightGridScrollbar,l=this.state,c=l.showHorizontalScrollbar,s=l.scrollbarSize;if(!r)return null;var u=c?1:0,d=this._getTopGridHeight(e),p=this._getRightGridWidth(e),h=c?s:0,f=d,m=this._topRightGridStyle;a&&(f=d+h,m=Qe({},this._topRightGridStyle,{left:0}));var g=v.createElement(B,w()({},e,{cellRenderer:this._cellRendererTopRightGrid,className:this.props.classNameTopRightGrid,columnCount:Math.max(0,t-o)+u,columnWidth:this._columnWidthRightGrid,deferredMeasurementCache:this._deferredMeasurementCacheTopRightGrid,height:f,onScroll:n?this._onScrollLeft:void 0,ref:this._topRightGridRef,rowCount:r,scrollLeft:i,style:m,tabIndex:null,width:p}));return a?v.createElement("div",{className:"TopRightGrid_ScrollWrapper",style:Qe({},this._topRightGridStyle,{height:d,width:p,overflowX:"hidden"})},g):g}}],[{key:"getDerivedStateFromProps",value:function(e,t){return e.scrollLeft!==t.scrollLeft||e.scrollTop!==t.scrollTop?{scrollLeft:null!=e.scrollLeft&&e.scrollLeft>=0?e.scrollLeft:t.scrollLeft,scrollTop:null!=e.scrollTop&&e.scrollTop>=0?e.scrollTop:t.scrollTop}:null}}]),t}(v.PureComponent);g()(et,"defaultProps",{classNameBottomLeftGrid:"",classNameBottomRightGrid:"",classNameTopLeftGrid:"",classNameTopRightGrid:"",enableFixedColumnScroll:!1,enableFixedRowScroll:!1,fixedColumnCount:0,fixedRowCount:0,scrollToColumn:-1,scrollToRow:-1,style:{},styleBottomLeftGrid:{},styleBottomRightGrid:{},styleTopLeftGrid:{},styleTopRightGrid:{},hideTopRightGridScrollbar:!1,hideBottomLeftGridScrollbar:!1}),et.propTypes={},Object(y.polyfill)(et);var tt=function(e){function t(e,n){var o;return r()(this,t),(o=c()(this,u()(t).call(this,e,n))).state={clientHeight:0,clientWidth:0,scrollHeight:0,scrollLeft:0,scrollTop:0,scrollWidth:0},o._onScroll=o._onScroll.bind(p()(o)),o}return f()(t,e),a()(t,[{key:"render",value:function(){var e=this.props.children,t=this.state,n=t.clientHeight,o=t.clientWidth,r=t.scrollHeight,i=t.scrollLeft,a=t.scrollTop,l=t.scrollWidth;return e({clientHeight:n,clientWidth:o,onScroll:this._onScroll,scrollHeight:r,scrollLeft:i,scrollTop:a,scrollWidth:l})}},{key:"_onScroll",value:function(e){var t=e.clientHeight,n=e.clientWidth,o=e.scrollHeight,r=e.scrollLeft,i=e.scrollTop,a=e.scrollWidth;this.setState({clientHeight:t,clientWidth:n,scrollHeight:o,scrollLeft:r,scrollTop:i,scrollWidth:a})}}]),t}(v.PureComponent);tt.propTypes={};function nt(e){var t=e.className,n=e.columns,o=e.style;return v.createElement("div",{className:t,role:"row",style:o},n)}nt.propTypes=null;var ot={ASC:"ASC",DESC:"DESC"};function rt(e){var t=e.sortDirection,n=Object(x.default)("ReactVirtualized__Table__sortableHeaderIcon",{"ReactVirtualized__Table__sortableHeaderIcon--ASC":t===ot.ASC,"ReactVirtualized__Table__sortableHeaderIcon--DESC":t===ot.DESC});return v.createElement("svg",{className:n,width:18,height:18,viewBox:"0 0 24 24"},t===ot.ASC?v.createElement("path",{d:"M7 14l5-5 5 5z"}):v.createElement("path",{d:"M7 10l5 5 5-5z"}),v.createElement("path",{d:"M0 0h24v24H0z",fill:"none"}))}function it(e){var t=e.dataKey,n=e.label,o=e.sortBy,r=e.sortDirection,i=o===t,a=[v.createElement("span",{className:"ReactVirtualized__Table__headerTruncatedText",key:"label",title:"string"==typeof n?n:null},n)];return i&&a.push(v.createElement(rt,{key:"SortIndicator",sortDirection:r})),a}function at(e){var t=e.className,n=e.columns,o=e.index,r=e.key,i=e.onRowClick,a=e.onRowDoubleClick,l=e.onRowMouseOut,c=e.onRowMouseOver,s=e.onRowRightClick,u=e.rowData,d=e.style,p={"aria-rowindex":o+1};return(i||a||l||c||s)&&(p["aria-label"]="row",p.tabIndex=0,i&&(p.onClick=function(e){return i({event:e,index:o,rowData:u})}),a&&(p.onDoubleClick=function(e){return a({event:e,index:o,rowData:u})}),l&&(p.onMouseOut=function(e){return l({event:e,index:o,rowData:u})}),c&&(p.onMouseOver=function(e){return c({event:e,index:o,rowData:u})}),s&&(p.onContextMenu=function(e){return s({event:e,index:o,rowData:u})})),v.createElement("div",w()({},p,{className:t,key:r,role:"row",style:d}),n)}rt.propTypes={},it.propTypes=null,at.propTypes=null;var lt=function(e){function t(){return r()(this,t),c()(this,u()(t).apply(this,arguments))}return f()(t,e),t}(v.Component);function ct(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function st(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ct(n,!0).forEach((function(t){g()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ct(n).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}g()(lt,"defaultProps",{cellDataGetter:function(e){var t=e.dataKey,n=e.rowData;return"function"==typeof n.get?n.get(t):n[t]},cellRenderer:function(e){var t=e.cellData;return null==t?"":String(t)},defaultSortDirection:ot.ASC,flexGrow:0,flexShrink:1,headerRenderer:it,style:{}}),lt.propTypes={};var ut=function(e){function t(e){var n;return r()(this,t),(n=c()(this,u()(t).call(this,e))).state={scrollbarWidth:0},n._createColumn=n._createColumn.bind(p()(n)),n._createRow=n._createRow.bind(p()(n)),n._onScroll=n._onScroll.bind(p()(n)),n._onSectionRendered=n._onSectionRendered.bind(p()(n)),n._setRef=n._setRef.bind(p()(n)),n}return f()(t,e),a()(t,[{key:"forceUpdateGrid",value:function(){this.Grid&&this.Grid.forceUpdate()}},{key:"getOffsetForRow",value:function(e){var t=e.alignment,n=e.index;return this.Grid?this.Grid.getOffsetForCell({alignment:t,rowIndex:n}).scrollTop:0}},{key:"invalidateCellSizeAfterRender",value:function(e){var t=e.columnIndex,n=e.rowIndex;this.Grid&&this.Grid.invalidateCellSizeAfterRender({rowIndex:n,columnIndex:t})}},{key:"measureAllRows",value:function(){this.Grid&&this.Grid.measureAllCells()}},{key:"recomputeGridSize",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.columnIndex,n=void 0===t?0:t,o=e.rowIndex,r=void 0===o?0:o;this.Grid&&this.Grid.recomputeGridSize({rowIndex:r,columnIndex:n})}},{key:"recomputeRowHeights",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;this.Grid&&this.Grid.recomputeGridSize({rowIndex:e})}},{key:"scrollToPosition",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;this.Grid&&this.Grid.scrollToPosition({scrollTop:e})}},{key:"scrollToRow",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;this.Grid&&this.Grid.scrollToCell({columnIndex:0,rowIndex:e})}},{key:"getScrollbarWidth",value:function(){if(this.Grid){var e=Object(ie.findDOMNode)(this.Grid),t=e.clientWidth||0;return(e.offsetWidth||0)-t}return 0}},{key:"componentDidMount",value:function(){this._setScrollbarWidth()}},{key:"componentDidUpdate",value:function(){this._setScrollbarWidth()}},{key:"render",value:function(){var e=this,t=this.props,n=t.children,o=t.className,r=t.disableHeader,i=t.gridClassName,a=t.gridStyle,l=t.headerHeight,c=t.headerRowRenderer,s=t.height,u=t.id,d=t.noRowsRenderer,p=t.rowClassName,h=t.rowStyle,f=t.scrollToIndex,m=t.style,g=t.width,y=this.state.scrollbarWidth,b=r?s:s-l,S="function"==typeof p?p({index:-1}):p,_="function"==typeof h?h({index:-1}):h;return this._cachedColumnStyles=[],v.Children.toArray(n).forEach((function(t,n){var o=e._getFlexStyleForColumn(t,t.props.style);e._cachedColumnStyles[n]=st({overflow:"hidden"},o)})),v.createElement("div",{"aria-label":this.props["aria-label"],"aria-labelledby":this.props["aria-labelledby"],"aria-colcount":v.Children.toArray(n).length,"aria-rowcount":this.props.rowCount,className:Object(x.default)("ReactVirtualized__Table",o),id:u,role:"grid",style:m},!r&&c({className:Object(x.default)("ReactVirtualized__Table__headerRow",S),columns:this._getHeaderColumns(),style:st({height:l,overflow:"hidden",paddingRight:y,width:g},_)}),v.createElement(B,w()({},this.props,{"aria-readonly":null,autoContainerWidth:!0,className:Object(x.default)("ReactVirtualized__Table__Grid",i),cellRenderer:this._createRow,columnWidth:g,columnCount:1,height:b,id:void 0,noContentRenderer:d,onScroll:this._onScroll,onSectionRendered:this._onSectionRendered,ref:this._setRef,role:"rowgroup",scrollbarWidth:y,scrollToRow:f,style:st({},a,{overflowX:"hidden"})})))}},{key:"_createColumn",value:function(e){var t=e.column,n=e.columnIndex,o=e.isScrolling,r=e.parent,i=e.rowData,a=e.rowIndex,l=this.props.onColumnClick,c=t.props,s=c.cellDataGetter,u=c.cellRenderer,d=c.className,p=c.columnData,h=c.dataKey,f=c.id,m=u({cellData:s({columnData:p,dataKey:h,rowData:i}),columnData:p,columnIndex:n,dataKey:h,isScrolling:o,parent:r,rowData:i,rowIndex:a}),g=this._cachedColumnStyles[n],y="string"==typeof m?m:null;return v.createElement("div",{"aria-colindex":n+1,"aria-describedby":f,className:Object(x.default)("ReactVirtualized__Table__rowColumn",d),key:"Row"+a+"-Col"+n,onClick:function(e){l&&l({columnData:p,dataKey:h,event:e})},role:"gridcell",style:g,title:y},m)}},{key:"_createHeader",value:function(e){var t,n,o,r,i,a=e.column,l=e.index,c=this.props,s=c.headerClassName,u=c.headerStyle,d=c.onHeaderClick,p=c.sort,h=c.sortBy,f=c.sortDirection,m=a.props,g=m.columnData,y=m.dataKey,b=m.defaultSortDirection,w=m.disableSort,S=m.headerRenderer,_=m.id,C=m.label,O=!w&&p,q=Object(x.default)("ReactVirtualized__Table__headerColumn",s,a.props.headerClassName,{ReactVirtualized__Table__sortableHeaderColumn:O}),E=this._getFlexStyleForColumn(a,st({},u,{},a.props.headerStyle)),k=S({columnData:g,dataKey:y,disableSort:w,label:C,sortBy:h,sortDirection:f});if(O||d){var T=h!==y?b:f===ot.DESC?ot.ASC:ot.DESC,R=function(e){O&&p({defaultSortDirection:b,event:e,sortBy:y,sortDirection:T}),d&&d({columnData:g,dataKey:y,event:e})};i=a.props["aria-label"]||C||y,r="none",o=0,t=R,n=function(e){"Enter"!==e.key&&" "!==e.key||R(e)}}return h===y&&(r=f===ot.ASC?"ascending":"descending"),v.createElement("div",{"aria-label":i,"aria-sort":r,className:q,id:_,key:"Header-Col"+l,onClick:t,onKeyDown:n,role:"columnheader",style:E,tabIndex:o},k)}},{key:"_createRow",value:function(e){var t=this,n=e.rowIndex,o=e.isScrolling,r=e.key,i=e.parent,a=e.style,l=this.props,c=l.children,s=l.onRowClick,u=l.onRowDoubleClick,d=l.onRowRightClick,p=l.onRowMouseOver,h=l.onRowMouseOut,f=l.rowClassName,m=l.rowGetter,g=l.rowRenderer,y=l.rowStyle,b=this.state.scrollbarWidth,w="function"==typeof f?f({index:n}):f,S="function"==typeof y?y({index:n}):y,_=m({index:n}),C=v.Children.toArray(c).map((function(e,r){return t._createColumn({column:e,columnIndex:r,isScrolling:o,parent:i,rowData:_,rowIndex:n,scrollbarWidth:b})})),O=Object(x.default)("ReactVirtualized__Table__row",w),q=st({},a,{height:this._getRowHeight(n),overflow:"hidden",paddingRight:b},S);return g({className:O,columns:C,index:n,isScrolling:o,key:r,onRowClick:s,onRowDoubleClick:u,onRowRightClick:d,onRowMouseOver:p,onRowMouseOut:h,rowData:_,style:q})}},{key:"_getFlexStyleForColumn",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n="".concat(e.props.flexGrow," ").concat(e.props.flexShrink," ").concat(e.props.width,"px"),o=st({},t,{flex:n,msFlex:n,WebkitFlex:n});return e.props.maxWidth&&(o.maxWidth=e.props.maxWidth),e.props.minWidth&&(o.minWidth=e.props.minWidth),o}},{key:"_getHeaderColumns",value:function(){var e=this,t=this.props,n=t.children;return(t.disableHeader?[]:v.Children.toArray(n)).map((function(t,n){return e._createHeader({column:t,index:n})}))}},{key:"_getRowHeight",value:function(e){var t=this.props.rowHeight;return"function"==typeof t?t({index:e}):t}},{key:"_onScroll",value:function(e){var t=e.clientHeight,n=e.scrollHeight,o=e.scrollTop;(0,this.props.onScroll)({clientHeight:t,scrollHeight:n,scrollTop:o})}},{key:"_onSectionRendered",value:function(e){var t=e.rowOverscanStartIndex,n=e.rowOverscanStopIndex,o=e.rowStartIndex,r=e.rowStopIndex;(0,this.props.onRowsRendered)({overscanStartIndex:t,overscanStopIndex:n,startIndex:o,stopIndex:r})}},{key:"_setRef",value:function(e){this.Grid=e}},{key:"_setScrollbarWidth",value:function(){var e=this.getScrollbarWidth();this.setState({scrollbarWidth:e})}}]),t}(v.PureComponent);g()(ut,"defaultProps",{disableHeader:!1,estimatedRowSize:30,headerHeight:0,headerStyle:{},noRowsRenderer:function(){return null},onRowsRendered:function(){return null},onScroll:function(){return null},overscanIndicesGetter:V,overscanRowCount:10,rowRenderer:at,headerRowRenderer:nt,rowStyle:{},scrollToAlignment:"auto",scrollToIndex:-1,style:{}}),ut.propTypes={};var dt=[],pt=null,ht=null;function ft(){ht&&(ht=null,document.body&&null!=pt&&(document.body.style.pointerEvents=pt),pt=null)}function mt(){ft(),dt.forEach((function(e){return e.__resetIsScrolling()}))}function gt(e){e.currentTarget===window&&null==pt&&document.body&&(pt=document.body.style.pointerEvents,document.body.style.pointerEvents="none"),function(){ht&&z(ht);var e=0;dt.forEach((function(t){e=Math.max(e,t.props.scrollingResetTimeInterval)})),ht=H(mt,e)}(),dt.forEach((function(t){t.props.scrollElement===e.currentTarget&&t.__handleWindowScrollEvent()}))}function vt(e,t){dt.some((function(e){return e.props.scrollElement===t}))||t.addEventListener("scroll",gt),dt.push(e)}function yt(e,t){(dt=dt.filter((function(t){return t!==e}))).length||(t.removeEventListener("scroll",gt),ht&&(z(ht),ft()))}var bt,wt,xt=function(e){return e===window},St=function(e){return e.getBoundingClientRect()};function _t(e,t){if(e){if(xt(e)){var n=window,o=n.innerHeight,r=n.innerWidth;return{height:"number"==typeof o?o:0,width:"number"==typeof r?r:0}}return St(e)}return{height:t.serverHeight,width:t.serverWidth}}function Ct(e,t){if(xt(t)&&document.documentElement){var n=document.documentElement,o=St(e),r=St(n);return{top:o.top-r.top,left:o.left-r.left}}var i=Ot(t),a=St(e),l=St(t);return{top:a.top+i.top-l.top,left:a.left+i.left-l.left}}function Ot(e){return xt(e)&&document.documentElement?{top:"scrollY"in window?window.scrollY:document.documentElement.scrollTop,left:"scrollX"in window?window.scrollX:document.documentElement.scrollLeft}:{top:e.scrollTop,left:e.scrollLeft}}function qt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function Et(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?qt(n,!0).forEach((function(t){g()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):qt(n).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var kt=function(){return"undefined"!=typeof window?window:void 0},Tt=(wt=bt=function(e){function t(){var e,n;r()(this,t);for(var o=arguments.length,i=new Array(o),a=0;a<o;a++)i[a]=arguments[a];return n=c()(this,(e=u()(t)).call.apply(e,[this].concat(i))),g()(p()(n),"_window",kt()),g()(p()(n),"_isMounted",!1),g()(p()(n),"_positionFromTop",0),g()(p()(n),"_positionFromLeft",0),g()(p()(n),"_detectElementResize",void 0),g()(p()(n),"_child",void 0),g()(p()(n),"state",Et({},_t(n.props.scrollElement,n.props),{isScrolling:!1,scrollLeft:0,scrollTop:0})),g()(p()(n),"_registerChild",(function(e){!e||e instanceof Element||console.warn("WindowScroller registerChild expects to be passed Element or null"),n._child=e,n.updatePosition()})),g()(p()(n),"_onChildScroll",(function(e){var t=e.scrollTop;if(n.state.scrollTop!==t){var o=n.props.scrollElement;o&&("function"==typeof o.scrollTo?o.scrollTo(0,t+n._positionFromTop):o.scrollTop=t+n._positionFromTop)}})),g()(p()(n),"_registerResizeListener",(function(e){e===window?window.addEventListener("resize",n._onResize,!1):n._detectElementResize.addResizeListener(e,n._onResize)})),g()(p()(n),"_unregisterResizeListener",(function(e){e===window?window.removeEventListener("resize",n._onResize,!1):e&&n._detectElementResize.removeResizeListener(e,n._onResize)})),g()(p()(n),"_onResize",(function(){n.updatePosition()})),g()(p()(n),"__handleWindowScrollEvent",(function(){if(n._isMounted){var e=n.props.onScroll,t=n.props.scrollElement;if(t){var o=Ot(t),r=Math.max(0,o.left-n._positionFromLeft),i=Math.max(0,o.top-n._positionFromTop);n.setState({isScrolling:!0,scrollLeft:r,scrollTop:i}),e({scrollLeft:r,scrollTop:i})}}})),g()(p()(n),"__resetIsScrolling",(function(){n.setState({isScrolling:!1})})),n}return f()(t,e),a()(t,[{key:"updatePosition",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props.scrollElement,t=this.props.onResize,n=this.state,o=n.height,r=n.width,i=this._child||ie.findDOMNode(this);if(i instanceof Element&&e){var a=Ct(i,e);this._positionFromTop=a.top,this._positionFromLeft=a.left}var l=_t(e,this.props);o===l.height&&r===l.width||(this.setState({height:l.height,width:l.width}),t({height:l.height,width:l.width}))}},{key:"componentDidMount",value:function(){var e=this.props.scrollElement;this._detectElementResize=Object(Q.a)(),this.updatePosition(e),e&&(vt(this,e),this._registerResizeListener(e)),this._isMounted=!0}},{key:"componentDidUpdate",value:function(e,t){var n=this.props.scrollElement,o=e.scrollElement;o!==n&&null!=o&&null!=n&&(this.updatePosition(n),yt(this,o),vt(this,n),this._unregisterResizeListener(o),this._registerResizeListener(n))}},{key:"componentWillUnmount",value:function(){var e=this.props.scrollElement;e&&(yt(this,e),this._unregisterResizeListener(e)),this._isMounted=!1}},{key:"render",value:function(){var e=this.props.children,t=this.state,n=t.isScrolling,o=t.scrollTop,r=t.scrollLeft,i=t.height,a=t.width;return e({onChildScroll:this._onChildScroll,registerChild:this._registerChild,height:i,isScrolling:n,scrollLeft:r,scrollTop:o,width:a})}}]),t}(v.PureComponent),g()(bt,"propTypes",null),wt);g()(Tt,"defaultProps",{onResize:function(){},onScroll:function(){},scrollingResetTimeInterval:150,scrollElement:kt(),serverHeight:0,serverWidth:0})},1548:function(e,t,n){var o=n(32),r=n(1549);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.i,r,""]]);var i={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach(e=>o.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))}),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const r=t[o];if(0===o)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};o(r,i);e.exports=r.locals||{}},1549:function(e,t,n){(t=e.exports=n(33)(!1)).push([e.i,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}@keyframes bottom-up{0%{transform:translateY(100%)}to{transform:translateY(0)}}@keyframes up-bottom{0%{transform:translateY(0)}to{transform:translateY(100%)}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .AnnotationStylePopup{width:100%;margin:0;position:relative;max-width:none;border-radius:4px 4px 0 0;border:0;padding-bottom:24px;box-shadow:none}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .AnnotationStylePopup{width:100%;margin:0;position:relative;max-width:none;border-radius:4px 4px 0 0;border:0;padding-bottom:24px;box-shadow:none}}.AnnotationStylePopup .back-to-menu-container{margin-top:var(--padding-medium);margin-right:var(--padding-medium);margin-left:var(--padding-medium);padding-bottom:var(--padding-small);border-bottom:1px solid var(--border)}.AnnotationStylePopup .Button.back-to-menu-button{margin:0;width:100%;height:32px;border-radius:0;justify-content:flex-start}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .AnnotationStylePopup .Button.back-to-menu-button{width:100%;height:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .AnnotationStylePopup .Button.back-to-menu-button{width:100%;height:32px}}",""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1552:function(e,t,n){var o=n(32),r=n(1553);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.i,r,""]]);var i={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach(e=>o.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))}),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const r=t[o];if(0===o)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};o(r,i);e.exports=r.locals||{}},1553:function(e,t,n){(e.exports=n(33)(!1)).push([e.i,'.ql-container{box-sizing:border-box;font-family:Helvetica,Arial,sans-serif;font-size:13px;height:100%;margin:0;position:relative}.ql-container.ql-disabled .ql-tooltip{visibility:hidden}.ql-container.ql-disabled .ql-editor ul[data-checked]>li:before{pointer-events:none}.ql-clipboard{left:-100000px;height:1px;overflow-y:hidden;position:absolute;top:50%}.ql-clipboard p{margin:0;padding:0}.ql-container .ql-editor{box-sizing:border-box;line-height:1.42;height:100%;outline:none;overflow-y:auto;padding:12px 15px;-o-tab-size:4;tab-size:4;-moz-tab-size:4;text-align:left;white-space:pre-wrap;word-wrap:break-word;-webkit-user-select:text;-moz-user-select:text;user-select:text}.ql-editor>*{cursor:text}.ql-editor blockquote,.ql-editor h1,.ql-editor h2,.ql-editor h3,.ql-editor h4,.ql-editor h5,.ql-editor h6,.ql-editor ol,.ql-editor p,.ql-editor pre,.ql-editor ul{counter-reset:list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9}.ql-editor p{margin:0;padding:0}.ql-editor ol,.ql-editor ul{padding-left:1.5em}.ql-editor ol>li,.ql-editor ul>li{list-style-type:none}.ql-editor ul>li:before{content:"\\2022"}.ql-editor ul[data-checked=false],.ql-editor ul[data-checked=true]{pointer-events:none}.ql-editor ul[data-checked=false]>li *,.ql-editor ul[data-checked=true]>li *{pointer-events:all}.ql-editor ul[data-checked=false]>li:before,.ql-editor ul[data-checked=true]>li:before{color:#777;cursor:pointer;pointer-events:all}.ql-editor ul[data-checked=true]>li:before{content:"\\2611"}.ql-editor ul[data-checked=false]>li:before{content:"\\2610"}.ql-editor li:before{display:inline-block;white-space:nowrap;width:1.2em}.ql-editor li:not(.ql-direction-rtl):before{margin-left:-1.5em;margin-right:.3em;text-align:right}.ql-editor li.ql-direction-rtl:before{margin-left:.3em;margin-right:-1.5em}.ql-editor ol li:not(.ql-direction-rtl),.ql-editor ul li:not(.ql-direction-rtl){padding-left:1.5em}.ql-editor ol li.ql-direction-rtl,.ql-editor ul li.ql-direction-rtl{padding-right:1.5em}.ql-editor ol li{counter-reset:list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;counter-increment:list-0}.ql-editor ol li:before{content:counter(list-0,decimal) ". "}.ql-editor ol li.ql-indent-1{counter-increment:list-1}.ql-editor ol li.ql-indent-1:before{content:counter(list-1,lower-alpha) ". "}.ql-editor ol li.ql-indent-1{counter-reset:list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-2{counter-increment:list-2}.ql-editor ol li.ql-indent-2:before{content:counter(list-2,lower-roman) ". "}.ql-editor ol li.ql-indent-2{counter-reset:list-3 list-4 list-5 list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-3{counter-increment:list-3}.ql-editor ol li.ql-indent-3:before{content:counter(list-3,decimal) ". "}.ql-editor ol li.ql-indent-3{counter-reset:list-4 list-5 list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-4{counter-increment:list-4}.ql-editor ol li.ql-indent-4:before{content:counter(list-4,lower-alpha) ". "}.ql-editor ol li.ql-indent-4{counter-reset:list-5 list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-5{counter-increment:list-5}.ql-editor ol li.ql-indent-5:before{content:counter(list-5,lower-roman) ". "}.ql-editor ol li.ql-indent-5{counter-reset:list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-6{counter-increment:list-6}.ql-editor ol li.ql-indent-6:before{content:counter(list-6,decimal) ". "}.ql-editor ol li.ql-indent-6{counter-reset:list-7 list-8 list-9}.ql-editor ol li.ql-indent-7{counter-increment:list-7}.ql-editor ol li.ql-indent-7:before{content:counter(list-7,lower-alpha) ". "}.ql-editor ol li.ql-indent-7{counter-reset:list-8 list-9}.ql-editor ol li.ql-indent-8{counter-increment:list-8}.ql-editor ol li.ql-indent-8:before{content:counter(list-8,lower-roman) ". "}.ql-editor ol li.ql-indent-8{counter-reset:list-9}.ql-editor ol li.ql-indent-9{counter-increment:list-9}.ql-editor ol li.ql-indent-9:before{content:counter(list-9,decimal) ". "}.ql-editor .ql-indent-1:not(.ql-direction-rtl){padding-left:3em}.ql-editor li.ql-indent-1:not(.ql-direction-rtl){padding-left:4.5em}.ql-editor .ql-indent-1.ql-direction-rtl.ql-align-right{padding-right:3em}.ql-editor li.ql-indent-1.ql-direction-rtl.ql-align-right{padding-right:4.5em}.ql-editor .ql-indent-2:not(.ql-direction-rtl){padding-left:6em}.ql-editor li.ql-indent-2:not(.ql-direction-rtl){padding-left:7.5em}.ql-editor .ql-indent-2.ql-direction-rtl.ql-align-right{padding-right:6em}.ql-editor li.ql-indent-2.ql-direction-rtl.ql-align-right{padding-right:7.5em}.ql-editor .ql-indent-3:not(.ql-direction-rtl){padding-left:9em}.ql-editor li.ql-indent-3:not(.ql-direction-rtl){padding-left:10.5em}.ql-editor .ql-indent-3.ql-direction-rtl.ql-align-right{padding-right:9em}.ql-editor li.ql-indent-3.ql-direction-rtl.ql-align-right{padding-right:10.5em}.ql-editor .ql-indent-4:not(.ql-direction-rtl){padding-left:12em}.ql-editor li.ql-indent-4:not(.ql-direction-rtl){padding-left:13.5em}.ql-editor .ql-indent-4.ql-direction-rtl.ql-align-right{padding-right:12em}.ql-editor li.ql-indent-4.ql-direction-rtl.ql-align-right{padding-right:13.5em}.ql-editor .ql-indent-5:not(.ql-direction-rtl){padding-left:15em}.ql-editor li.ql-indent-5:not(.ql-direction-rtl){padding-left:16.5em}.ql-editor .ql-indent-5.ql-direction-rtl.ql-align-right{padding-right:15em}.ql-editor li.ql-indent-5.ql-direction-rtl.ql-align-right{padding-right:16.5em}.ql-editor .ql-indent-6:not(.ql-direction-rtl){padding-left:18em}.ql-editor li.ql-indent-6:not(.ql-direction-rtl){padding-left:19.5em}.ql-editor .ql-indent-6.ql-direction-rtl.ql-align-right{padding-right:18em}.ql-editor li.ql-indent-6.ql-direction-rtl.ql-align-right{padding-right:19.5em}.ql-editor .ql-indent-7:not(.ql-direction-rtl){padding-left:21em}.ql-editor li.ql-indent-7:not(.ql-direction-rtl){padding-left:22.5em}.ql-editor .ql-indent-7.ql-direction-rtl.ql-align-right{padding-right:21em}.ql-editor li.ql-indent-7.ql-direction-rtl.ql-align-right{padding-right:22.5em}.ql-editor .ql-indent-8:not(.ql-direction-rtl){padding-left:24em}.ql-editor li.ql-indent-8:not(.ql-direction-rtl){padding-left:25.5em}.ql-editor .ql-indent-8.ql-direction-rtl.ql-align-right{padding-right:24em}.ql-editor li.ql-indent-8.ql-direction-rtl.ql-align-right{padding-right:25.5em}.ql-editor .ql-indent-9:not(.ql-direction-rtl){padding-left:27em}.ql-editor li.ql-indent-9:not(.ql-direction-rtl){padding-left:28.5em}.ql-editor .ql-indent-9.ql-direction-rtl.ql-align-right{padding-right:27em}.ql-editor li.ql-indent-9.ql-direction-rtl.ql-align-right{padding-right:28.5em}.ql-editor .ql-video{display:block;max-width:100%}.ql-editor .ql-video.ql-align-center{margin:0 auto}.ql-editor .ql-video.ql-align-right{margin:0 0 0 auto}.ql-editor .ql-bg-black{background-color:#000}.ql-editor .ql-bg-red{background-color:#e60000}.ql-editor .ql-bg-orange{background-color:#f90}.ql-editor .ql-bg-yellow{background-color:#ff0}.ql-editor .ql-bg-green{background-color:#008a00}.ql-editor .ql-bg-blue{background-color:#06c}.ql-editor .ql-bg-purple{background-color:#93f}.ql-editor .ql-color-white{color:#fff}.ql-editor .ql-color-red{color:#e60000}.ql-editor .ql-color-orange{color:#f90}.ql-editor .ql-color-yellow{color:#ff0}.ql-editor .ql-color-green{color:#008a00}.ql-editor .ql-color-blue{color:#06c}.ql-editor .ql-color-purple{color:#93f}.ql-editor .ql-font-serif{font-family:Georgia,Times New Roman,serif}.ql-editor .ql-font-monospace{font-family:Monaco,Courier New,monospace}.ql-editor .ql-size-small{font-size:.75em}.ql-editor .ql-size-large{font-size:1.5em}.ql-editor .ql-size-huge{font-size:2.5em}.ql-editor .ql-direction-rtl{direction:rtl;text-align:inherit}.ql-editor .ql-align-center{text-align:center}.ql-editor .ql-align-justify{text-align:justify}.ql-editor .ql-align-right{text-align:right}.ql-editor.ql-blank:before{color:rgba(0,0,0,.6);content:attr(data-placeholder);font-style:italic;left:15px;pointer-events:none;position:absolute;right:15px}.ql-snow.ql-toolbar:after,.ql-snow .ql-toolbar:after{clear:both;content:"";display:table}.ql-snow.ql-toolbar button,.ql-snow .ql-toolbar button{background:none;border:none;cursor:pointer;display:inline-block;float:left;height:24px;padding:3px 5px;width:28px}.ql-snow.ql-toolbar button svg,.ql-snow .ql-toolbar button svg{float:left;height:100%}.ql-snow.ql-toolbar button:active:hover,.ql-snow .ql-toolbar button:active:hover{outline:none}.ql-snow.ql-toolbar input.ql-image[type=file],.ql-snow .ql-toolbar input.ql-image[type=file]{display:none}.ql-snow.ql-toolbar .ql-picker-item.ql-selected,.ql-snow .ql-toolbar .ql-picker-item.ql-selected,.ql-snow.ql-toolbar .ql-picker-item:hover,.ql-snow .ql-toolbar .ql-picker-item:hover,.ql-snow.ql-toolbar .ql-picker-label.ql-active,.ql-snow .ql-toolbar .ql-picker-label.ql-active,.ql-snow.ql-toolbar .ql-picker-label:hover,.ql-snow .ql-toolbar .ql-picker-label:hover,.ql-snow.ql-toolbar button.ql-active,.ql-snow .ql-toolbar button.ql-active,.ql-snow.ql-toolbar button:focus,.ql-snow .ql-toolbar button:focus,.ql-snow.ql-toolbar button:hover,.ql-snow .ql-toolbar button:hover{color:#06c}.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-fill,.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-fill,.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill,.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill,.ql-snow.ql-toolbar .ql-picker-item:hover .ql-fill,.ql-snow .ql-toolbar .ql-picker-item:hover .ql-fill,.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-fill,.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-fill,.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,.ql-snow.ql-toolbar .ql-picker-label:hover .ql-fill,.ql-snow .ql-toolbar .ql-picker-label:hover .ql-fill,.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,.ql-snow.ql-toolbar button.ql-active .ql-fill,.ql-snow .ql-toolbar button.ql-active .ql-fill,.ql-snow.ql-toolbar button.ql-active .ql-stroke.ql-fill,.ql-snow .ql-toolbar button.ql-active .ql-stroke.ql-fill,.ql-snow.ql-toolbar button:focus .ql-fill,.ql-snow .ql-toolbar button:focus .ql-fill,.ql-snow.ql-toolbar button:focus .ql-stroke.ql-fill,.ql-snow .ql-toolbar button:focus .ql-stroke.ql-fill,.ql-snow.ql-toolbar button:hover .ql-fill,.ql-snow .ql-toolbar button:hover .ql-fill,.ql-snow.ql-toolbar button:hover .ql-stroke.ql-fill,.ql-snow .ql-toolbar button:hover .ql-stroke.ql-fill{fill:#06c}.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke,.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke,.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter,.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter,.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke,.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke,.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke-miter,.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke-miter,.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke,.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke,.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke,.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke,.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke-miter,.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke-miter,.ql-snow.ql-toolbar button.ql-active .ql-stroke,.ql-snow .ql-toolbar button.ql-active .ql-stroke,.ql-snow.ql-toolbar button.ql-active .ql-stroke-miter,.ql-snow .ql-toolbar button.ql-active .ql-stroke-miter,.ql-snow.ql-toolbar button:focus .ql-stroke,.ql-snow .ql-toolbar button:focus .ql-stroke,.ql-snow.ql-toolbar button:focus .ql-stroke-miter,.ql-snow .ql-toolbar button:focus .ql-stroke-miter,.ql-snow.ql-toolbar button:hover .ql-stroke,.ql-snow .ql-toolbar button:hover .ql-stroke,.ql-snow.ql-toolbar button:hover .ql-stroke-miter,.ql-snow .ql-toolbar button:hover .ql-stroke-miter{stroke:#06c}@media(pointer:coarse){.ql-snow.ql-toolbar button:hover:not(.ql-active),.ql-snow .ql-toolbar button:hover:not(.ql-active){color:#444}.ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-fill,.ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-fill,.ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke.ql-fill,.ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke.ql-fill{fill:#444}.ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke,.ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke,.ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke-miter,.ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke-miter{stroke:#444}}.ql-snow,.ql-snow *{box-sizing:border-box}.ql-snow .ql-hidden{display:none}.ql-snow .ql-out-bottom,.ql-snow .ql-out-top{visibility:hidden}.ql-snow .ql-tooltip{position:absolute;transform:translateY(10px)}.ql-snow .ql-tooltip a{cursor:pointer;text-decoration:none}.ql-snow .ql-tooltip.ql-flip{transform:translateY(-10px)}.ql-snow .ql-formats{display:inline-block;vertical-align:middle}.ql-snow .ql-formats:after{clear:both;content:"";display:table}.ql-snow .ql-stroke{fill:none;stroke:#444;stroke-linecap:round;stroke-linejoin:round;stroke-width:2}.ql-snow .ql-stroke-miter{fill:none;stroke:#444;stroke-miterlimit:10;stroke-width:2}.ql-snow .ql-fill,.ql-snow .ql-stroke.ql-fill{fill:#444}.ql-snow .ql-empty{fill:none}.ql-snow .ql-even{fill-rule:evenodd}.ql-snow .ql-stroke.ql-thin,.ql-snow .ql-thin{stroke-width:1}.ql-snow .ql-transparent{opacity:.4}.ql-snow .ql-direction svg:last-child{display:none}.ql-snow .ql-direction.ql-active svg:last-child{display:inline}.ql-snow .ql-direction.ql-active svg:first-child{display:none}.ql-snow .ql-editor h1{font-size:2em}.ql-snow .ql-editor h2{font-size:1.5em}.ql-snow .ql-editor h3{font-size:1.17em}.ql-snow .ql-editor h4{font-size:1em}.ql-snow .ql-editor h5{font-size:.83em}.ql-snow .ql-editor h6{font-size:.67em}.ql-snow .ql-editor a{text-decoration:underline}.ql-snow .ql-editor blockquote{border-left:4px solid #ccc;margin-bottom:5px;margin-top:5px;padding-left:16px}.ql-snow .ql-editor code,.ql-snow .ql-editor pre{background-color:#f0f0f0;border-radius:3px}.ql-snow .ql-editor pre{white-space:pre-wrap;margin-bottom:5px;margin-top:5px;padding:5px 10px}.ql-snow .ql-editor code{font-size:85%;padding:2px 4px}.ql-snow .ql-editor pre.ql-syntax{background-color:#23241f;color:#f8f8f2;overflow:visible}.ql-snow .ql-editor img{max-width:100%}.ql-snow .ql-picker{color:#444;display:inline-block;float:left;font-size:14px;font-weight:500;height:24px;position:relative;vertical-align:middle}.ql-snow .ql-picker-label{cursor:pointer;display:inline-block;height:100%;padding-left:8px;padding-right:2px;position:relative;width:100%}.ql-snow .ql-picker-label:before{display:inline-block;line-height:22px}.ql-snow .ql-picker-options{background-color:#fff;display:none;min-width:100%;padding:4px 8px;position:absolute;white-space:nowrap}.ql-snow .ql-picker-options .ql-picker-item{cursor:pointer;display:block;padding-bottom:5px;padding-top:5px}.ql-snow .ql-picker.ql-expanded .ql-picker-label{color:#ccc;z-index:2}.ql-snow .ql-picker.ql-expanded .ql-picker-label .ql-fill{fill:#ccc}.ql-snow .ql-picker.ql-expanded .ql-picker-label .ql-stroke{stroke:#ccc}.ql-snow .ql-picker.ql-expanded .ql-picker-options{display:block;margin-top:-1px;top:100%;z-index:1}.ql-snow .ql-color-picker,.ql-snow .ql-icon-picker{width:28px}.ql-snow .ql-color-picker .ql-picker-label,.ql-snow .ql-icon-picker .ql-picker-label{padding:2px 4px}.ql-snow .ql-color-picker .ql-picker-label svg,.ql-snow .ql-icon-picker .ql-picker-label svg{right:4px}.ql-snow .ql-icon-picker .ql-picker-options{padding:4px 0}.ql-snow .ql-icon-picker .ql-picker-item{height:24px;width:24px;padding:2px 4px}.ql-snow .ql-color-picker .ql-picker-options{padding:3px 5px;width:152px}.ql-snow .ql-color-picker .ql-picker-item{border:1px solid transparent;float:left;height:16px;margin:2px;padding:0;width:16px}.ql-snow .ql-picker:not(.ql-color-picker):not(.ql-icon-picker) svg{position:absolute;margin-top:-9px;right:0;top:50%;width:18px}.ql-snow .ql-picker.ql-font .ql-picker-item[data-label]:not([data-label=""]):before,.ql-snow .ql-picker.ql-font .ql-picker-label[data-label]:not([data-label=""]):before,.ql-snow .ql-picker.ql-header .ql-picker-item[data-label]:not([data-label=""]):before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-label]:not([data-label=""]):before,.ql-snow .ql-picker.ql-size .ql-picker-item[data-label]:not([data-label=""]):before,.ql-snow .ql-picker.ql-size .ql-picker-label[data-label]:not([data-label=""]):before{content:attr(data-label)}.ql-snow .ql-picker.ql-header{width:98px}.ql-snow .ql-picker.ql-header .ql-picker-item:before,.ql-snow .ql-picker.ql-header .ql-picker-label:before{content:"Normal"}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="1"]:before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="1"]:before{content:"Heading 1"}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="2"]:before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="2"]:before{content:"Heading 2"}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="3"]:before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="3"]:before{content:"Heading 3"}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="4"]:before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="4"]:before{content:"Heading 4"}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="5"]:before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="5"]:before{content:"Heading 5"}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="6"]:before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="6"]:before{content:"Heading 6"}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="1"]:before{font-size:2em}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="2"]:before{font-size:1.5em}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="3"]:before{font-size:1.17em}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="4"]:before{font-size:1em}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="5"]:before{font-size:.83em}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="6"]:before{font-size:.67em}.ql-snow .ql-picker.ql-font{width:108px}.ql-snow .ql-picker.ql-font .ql-picker-item:before,.ql-snow .ql-picker.ql-font .ql-picker-label:before{content:"Sans Serif"}.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=serif]:before,.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=serif]:before{content:"Serif"}.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=monospace]:before,.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=monospace]:before{content:"Monospace"}.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=serif]:before{font-family:Georgia,Times New Roman,serif}.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=monospace]:before{font-family:Monaco,Courier New,monospace}.ql-snow .ql-picker.ql-size{width:98px}.ql-snow .ql-picker.ql-size .ql-picker-item:before,.ql-snow .ql-picker.ql-size .ql-picker-label:before{content:"Normal"}.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=small]:before,.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=small]:before{content:"Small"}.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=large]:before,.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=large]:before{content:"Large"}.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=huge]:before,.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=huge]:before{content:"Huge"}.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=small]:before{font-size:10px}.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=large]:before{font-size:18px}.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=huge]:before{font-size:32px}.ql-snow .ql-color-picker.ql-background .ql-picker-item{background-color:#fff}.ql-snow .ql-color-picker.ql-color .ql-picker-item{background-color:#000}.ql-toolbar.ql-snow{border:1px solid #ccc;box-sizing:border-box;font-family:Helvetica Neue,Helvetica,Arial,sans-serif;padding:8px}.ql-toolbar.ql-snow .ql-formats{margin-right:15px}.ql-toolbar.ql-snow .ql-picker-label{border:1px solid transparent}.ql-toolbar.ql-snow .ql-picker-options{border:1px solid transparent;box-shadow:0 2px 8px rgba(0,0,0,.2)}.ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label,.ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options{border-color:#ccc}.ql-toolbar.ql-snow .ql-color-picker .ql-picker-item.ql-selected,.ql-toolbar.ql-snow .ql-color-picker .ql-picker-item:hover{border-color:#000}.ql-toolbar.ql-snow+.ql-container.ql-snow{border-top:0}.ql-snow .ql-tooltip{background-color:#fff;border:1px solid #ccc;box-shadow:0 0 5px #ddd;color:#444;padding:5px 12px;white-space:nowrap}.ql-snow .ql-tooltip:before{content:"Visit URL:";line-height:26px;margin-right:8px}.ql-snow .ql-tooltip input[type=text]{display:none;border:1px solid #ccc;font-size:13px;height:26px;margin:0;padding:3px 5px;width:170px}.ql-snow .ql-tooltip a.ql-preview{display:inline-block;max-width:200px;overflow-x:hidden;text-overflow:ellipsis;vertical-align:top}.ql-snow .ql-tooltip a.ql-action:after{border-right:1px solid #ccc;content:"Edit";margin-left:16px;padding-right:8px}.ql-snow .ql-tooltip a.ql-remove:before{content:"Remove";margin-left:8px}.ql-snow .ql-tooltip a{line-height:26px}.ql-snow .ql-tooltip.ql-editing a.ql-preview,.ql-snow .ql-tooltip.ql-editing a.ql-remove{display:none}.ql-snow .ql-tooltip.ql-editing input[type=text]{display:inline-block}.ql-snow .ql-tooltip.ql-editing a.ql-action:after{border-right:0;content:"Save";padding-right:0}.ql-snow .ql-tooltip[data-mode=link]:before{content:"Enter link:"}.ql-snow .ql-tooltip[data-mode=formula]:before{content:"Enter formula:"}.ql-snow .ql-tooltip[data-mode=video]:before{content:"Enter video:"}.ql-snow a{color:#06c}.ql-container.ql-snow{border:1px solid #ccc}',""])},1554:function(e,t,n){var o=n(32),r=n(1555);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.i,r,""]]);var i={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach(e=>o.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))}),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const r=t[o];if(0===o)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};o(r,i);e.exports=r.locals||{}},1555:function(e,t,n){(t=e.exports=n(33)(!1)).push([e.i,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.comment-textarea{position:relative}.comment-textarea .ql-toolbar{display:none}.comment-textarea .ql-container{border:none}.comment-textarea .ql-container .ql-editor{width:100%;padding:4px 6px;border:1px solid var(--border);border-radius:4px;color:var(--text-color);resize:none;overflow:hidden;box-sizing:border-box}.comment-textarea .ql-container .ql-editor:focus{outline:none;border:1px solid var(--focus-border)}.comment-textarea .ql-container .ql-editor.ql-blank:before{left:8px;list-style-type:none;font-style:normal;color:var(--placeholder-text)}.comment-textarea .ql-container .ql-editor p{margin:0;word-break:break-word}.comment-textarea .ql-container .ql-editor ul>li:before{content:none!important}.comment-textarea .ql-container.ql-snow{border:none}.comment-textarea .add-attachment{position:absolute;bottom:2px;right:2px;width:24px;height:24px}.comment-textarea .add-attachment:hover{background-color:var(--blue-1)}.comment-textarea .add-attachment .Icon{padding:3px}.ql-editor ul>li:before{content:none!important}.mention__element{width:170px;z-index:9001!important;max-height:200px;overflow-y:auto;overflow-y:overlay;overflow-x:hidden;background-color:var(--component-background);border:1px solid var(--border);border-radius:4px}.mention__suggestions__list{width:100%;font-size:14px;margin-top:0;padding-left:0!important;list-style:none;word-wrap:break-word;border-radius:4px}.mention__suggestions__item{background-color:var(--component-background);white-space:nowrap;padding-left:0;text-overflow:clip;padding:7px 5px 1px!important;margin:0;width:100%;cursor:pointer}.mention__suggestions__item .email{margin-top:2px;font-size:12px;white-space:normal;color:var(--faded-text)}.mention__suggestions__item.selected{background-color:var(--view-header-button-active)!important}",""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1556:function(e,t,n){var o=n(32),r=n(1557);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.i,r,""]]);var i={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach(e=>o.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))}),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const r=t[o];if(0===o)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};o(r,i);e.exports=r.locals||{}},1557:function(e,t,n){(t=e.exports=n(33)(!1)).push([e.i,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.NotePopup .options.modular-ui .option:hover{cursor:pointer;background:var(--primary-button-hover);color:var(--gray-0)}.NotePopup{flex-grow:0;display:flex;justify-content:flex-end;-webkit-user-select:none;-moz-user-select:none;user-select:none;width:28px;height:28px;position:relative}.NotePopup .Button.overflow{width:28px;height:28px;border-radius:4px;display:flex;justify-content:center;align-items:center}.NotePopup .Button.overflow .Icon{width:24px;height:24px}.NotePopup .Button.overflow:hover{background:var(--blue-1)}.NotePopup .Button.overflow.active{background:var(--popup-button-active)}.NotePopup .options{display:flex;flex-direction:column;box-shadow:0 0 3px 0 var(--box-shadow);z-index:80;position:absolute;border-radius:4px;background:var(--component-background);top:40px;width:-moz-max-content;width:max-content}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.NotePopup .options{right:0}}.NotePopup .options .note-popup-option{padding:0;border:none;background-color:transparent;align-items:flex-start}:host(:not([data-tabbing=true])) .NotePopup .options .note-popup-option,html:not([data-tabbing=true]) .NotePopup .options .note-popup-option{outline:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .NotePopup .options .note-popup-option{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .NotePopup .options .note-popup-option{font-size:13px}}.NotePopup .options.options-reply{top:30px}.NotePopup .options .option{display:flex;flex-direction:column;justify-content:center;height:28px;padding-left:8px;padding-right:17px;border-radius:0}.NotePopup .options .option:hover{background-color:var(--popup-button-hover)}.NotePopup .options .option:first-child{border-top-right-radius:4px;border-top-left-radius:4px}.NotePopup .options .option:last-child{border-bottom-right-radius:4px}.NotePopup .Button{height:28px}.NotePopup .Button.active{background:var(--popup-button-active)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .NotePopup .Button.note-popup-toggle-trigger{width:28px;height:28px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .NotePopup .Button.note-popup-toggle-trigger{width:28px;height:28px}}.NotePopupFlyout{min-width:unset!important;max-width:unset!important}.NotePopupFlyout .flyout-item-container{height:unset!important;margin:unset!important}",""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1558:function(e,t,n){var o=n(32),r=n(1559);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.i,r,""]]);var i={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach(e=>o.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))}),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const r=t[o];if(0===o)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};o(r,i);e.exports=r.locals||{}},1559:function(e,t,n){(t=e.exports=n(33)(!1)).push([e.i,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.NoteHeader{padding-right:12px;position:relative;flex:1;color:var(--text-color);display:flex}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.NoteHeader{flex:none}}.NoteHeader .type-icon{margin:2px;width:24px;height:24px}.NoteHeader .type-icon-container{padding-right:13px}.NoteHeader .type-icon-container .unread-notification{position:absolute;width:13px;height:13px;right:-2px;top:-2px;border-radius:10000px;border:2px solid var(--component-background);background:#00a5e4}.NoteHeader .author-and-date{flex:1;min-width:0;position:relative}.NoteHeader .author-and-date.isReply{padding-left:0;padding-top:0;font-size:10px}.NoteHeader .author-and-date .author-and-overflow{display:flex;justify-content:space-between}.NoteHeader .author-and-date .author-and-overflow .author-and-time{display:flex;flex-direction:column;word-break:break-word}.NoteHeader .author-and-date .author-and-overflow .author-and-time .author{font-weight:700;font-size:13px}.NoteHeader .author-and-date .author-and-overflow .author-and-time .date-and-num-replies{display:flex}.NoteHeader .author-and-date .author-and-overflow .author-and-time .date-and-num-replies .date-and-time{font-size:10px;color:var(--faded-text)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .NoteHeader .author-and-date .author-and-overflow .author-and-time .date-and-num-replies .date-and-time{font-size:10px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .NoteHeader .author-and-date .author-and-overflow .author-and-time .date-and-num-replies .date-and-time{font-size:10px}}.NoteHeader .author-and-date .author-and-overflow .author-and-time .date-and-num-replies .num-replies-container{display:flex;flex-grow:1;padding-left:10px}.NoteHeader .author-and-date .author-and-overflow .author-and-time .date-and-num-replies .num-replies-container .num-reply-icon{height:12px;width:12px}.NoteHeader .author-and-date .author-and-overflow .author-and-time .date-and-num-replies .num-replies-container .num-replies{font-size:10px;color:var(--gray-7)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .NoteHeader .author-and-date .author-and-overflow .author-and-time .date-and-num-replies .num-replies-container .num-replies{font-size:12px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .NoteHeader .author-and-date .author-and-overflow .author-and-time .date-and-num-replies .num-replies-container .num-replies{font-size:12px}}.NoteHeader .author-and-date .author-and-overflow .state-and-overflow{display:flex;flex:1;justify-content:flex-end}.NoteHeader .author-and-date .author-and-overflow .state-and-overflow>*{pointer-events:auto}.NoteHeader .author-and-date .author-and-overflow .state-and-overflow .tracked-change-icon-wrapper .tracked-change-icon{margin:2px;width:24px;height:24px}.NoteHeader .author-and-date .author-and-overflow .state-and-overflow .tracked-change-icon-wrapper:hover.accept{background-color:#d5f5ca}.NoteHeader .author-and-date .author-and-overflow .state-and-overflow .tracked-change-icon-wrapper:hover.reject{background-color:#ffe8e8}.NoteHeader .author-and-date .author-and-overflow .state-and-overflow .ToggleElementButton button{width:28px;height:28px;margin:0 8px 0 13px}.NoteHeader .author-and-date .author-and-overflow .state-and-overflow .ToggleElementButton button .Icon{width:20px;height:20px}.NoteHeader .author-name{font-weight:700}.NoteHeader .note-popup-toggle-trigger{padding:0;margin-right:0!important;margin-left:0!important;min-width:28px!important}.NoteHeader .note-popup-toggle-trigger .Icon{width:24px!important;height:24px!important}.parent{padding-left:12px;padding-top:12px}",""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1560:function(e,t,n){var o=n(32),r=n(1561);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.i,r,""]]);var i={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach(e=>o.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))}),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const r=t[o];if(0===o)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};o(r,i);e.exports=r.locals||{}},1561:function(e,t,n){(e.exports=n(33)(!1)).push([e.i,".note-text-preview{font-size:13px;color:var(--gray-7);padding-right:var(--note-content-right-padding-width);-webkit-user-select:text!important;-moz-user-select:text!important;user-select:text!important;cursor:text;height:-moz-fit-content;height:fit-content;width:calc(100% - var(--note-content-right-padding-width));overflow:hidden}.preview-comment{color:var(--text-color)}.note-text-preview-prompt{cursor:pointer;color:var(--primary-button);text-decoration:underline;position:relative;pointer-events:auto;background:transparent;border:none;padding:0}.note-text-preview-prompt:hover{color:var(--primary-button-hover)}.trackedChangePopup .note-text-preview{max-height:400px;overflow-y:auto;width:calc(100% + var(--note-content-right-padding-width))}",""])},1562:function(e,t,n){var o=n(32),r=n(1563);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.i,r,""]]);var i={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach(e=>o.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))}),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const r=t[o];if(0===o)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};o(r,i);e.exports=r.locals||{}},1563:function(e,t,n){(e.exports=n(33)(!1)).push([e.i,".reply-attachment-list{display:flex;flex-direction:column;cursor:default}.reply-attachment-list .reply-attachment{background-color:var(--gray-1);border-radius:4px;cursor:pointer;pointer-events:auto}.reply-attachment-list .reply-attachment:not(:last-child){margin-bottom:8px}.reply-attachment-list .reply-attachment .reply-attachment-preview{width:100%;max-height:80px;display:flex;justify-content:center}.reply-attachment-list .reply-attachment .reply-attachment-preview.dirty{position:relative;margin-bottom:15px}.reply-attachment-list .reply-attachment .reply-attachment-preview img{max-width:100%;max-height:100%;-o-object-fit:contain;object-fit:contain}.reply-attachment-list .reply-attachment .reply-attachment-preview .reply-attachment-preview-message{font-size:11px;color:var(--error-text-color);position:absolute;bottom:-15px;left:10px}.reply-attachment-list .reply-attachment .reply-attachment-info{display:flex;align-items:center;height:40px;padding:8px}.reply-attachment-list .reply-attachment .reply-attachment-info .reply-attachment-icon{height:24px;min-height:24px;width:24px;min-width:24px}.reply-attachment-list .reply-attachment .reply-attachment-info .reply-file-name{height:16px;width:100%;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;margin-left:8px;margin-right:8px}.reply-attachment-list .reply-attachment .reply-attachment-info .attachment-button{height:24px;min-height:24px;width:24px;min-width:24px}.reply-attachment-list .reply-attachment .reply-attachment-info .attachment-button:hover{background-color:var(--blue-1)}.reply-attachment-list .reply-attachment .reply-attachment-info .attachment-button .Icon{height:16px;width:16px}",""])},1564:function(e,t,n){var o=n(32),r=n(1565);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.i,r,""]]);var i={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach(e=>o.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))}),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const r=t[o];if(0===o)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};o(r,i);e.exports=r.locals||{}},1565:function(e,t,n){(t=e.exports=n(33)(!1)).push([e.i,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.NoteContent.modular-ui .edit-content .edit-buttons .save-button.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.NoteContent.modular-ui .edit-content .edit-buttons .save-button.disabled span{color:var(--primary-button-text)}.NoteContent{--note-content-right-padding-width:12px;position:relative;display:flex;flex-direction:column;align-items:left;flex:1;color:var(--text-color);padding-bottom:12px;pointer-events:none}.NoteContent.isReply{padding-bottom:0}.NoteContent.unread.isReply{background:rgba(0,165,228,.08)}.NoteContent.unread.clicked .author-and-time span{font-weight:400}.NoteContent.unread .author-and-time span{font-weight:700}.NoteContent .container{padding-left:52px;padding-right:var(--note-content-right-padding-width)}.NoteContent .container,.NoteContent .container-reply{margin-top:8px;overflow:hidden;white-space:pre-wrap;word-wrap:break-word;-webkit-user-select:text;-moz-user-select:text;user-select:text;cursor:text}.NoteContent .edit-content{margin-top:7px;display:flex;flex-direction:column;position:relative;flex:1;padding-left:52px;padding-right:12px;padding-bottom:12px;pointer-events:auto}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.NoteContent .edit-content{flex:none}}.NoteContent .edit-content textarea{width:100%;padding-left:8px;border:1px solid var(--border);border-radius:4px;color:var(--text-color);padding-top:4px;padding-bottom:4px;resize:none;overflow:hidden;box-sizing:border-box}.NoteContent .edit-content textarea:focus{outline:none;border:1px solid var(--focus-border)}.NoteContent .edit-content textarea::-moz-placeholder{color:var(--placeholder-text)}.NoteContent .edit-content textarea::placeholder{color:var(--placeholder-text)}.NoteContent .edit-content .edit-buttons{display:flex;flex-direction:row;justify-content:flex-end;margin-top:8px}.NoteContent .edit-content .edit-buttons>div{margin-right:4px}.NoteContent .edit-content .edit-buttons .save-button{background-color:transparent;cursor:pointer;flex-shrink:0;background:var(--primary-button);border-radius:4px;width:-moz-fit-content;width:fit-content;border:none;height:28px;display:flex;align-items:center;justify-content:center;position:relative;color:var(--primary-button-text);padding:0 13px}:host(:not([data-tabbing=true])) .NoteContent .edit-content .edit-buttons .save-button,html:not([data-tabbing=true]) .NoteContent .edit-content .edit-buttons .save-button{outline:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .NoteContent .edit-content .edit-buttons .save-button{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .NoteContent .edit-content .edit-buttons .save-button{font-size:13px}}.NoteContent .edit-content .edit-buttons .save-button:hover{background:var(--primary-button-hover);color:var(--primary-button-text)}.NoteContent .edit-content .edit-buttons .save-button.disabled{background:var(--gray-6)!important;border-color:var(--gray-6)!important;cursor:not-allowed}.NoteContent .edit-content .edit-buttons .save-button.disabled span{color:var(--primary-button-text)}.NoteContent .edit-content .edit-buttons .cancel-button{border:none;background-color:transparent;color:var(--secondary-button-text);padding:0 10px;width:-moz-fit-content;width:fit-content;height:28px;display:flex;align-items:center;justify-content:center;cursor:pointer;margin-right:2px}:host(:not([data-tabbing=true])) .NoteContent .edit-content .edit-buttons .cancel-button,html:not([data-tabbing=true]) .NoteContent .edit-content .edit-buttons .cancel-button{outline:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .NoteContent .edit-content .edit-buttons .cancel-button{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .NoteContent .edit-content .edit-buttons .cancel-button{font-size:13px}}.NoteContent .edit-content .edit-buttons .cancel-button:hover{color:var(--secondary-button-hover)}.NoteContent .reply-content{padding-left:0}.NoteContent .contents{white-space:pre-wrap;color:var(--text-color);margin-right:5px;padding:0;word-break:normal;word-wrap:break-word;-webkit-user-select:text;-moz-user-select:text;user-select:text}.NoteContent .contents .highlight{background:#fffc95;color:#333}.NoteContent .highlight{background:#fffc95}.NoteContent .selected-text-preview{padding-left:52px;padding-top:8px}.NoteContent .reply-attachment-list{margin-bottom:8px}.NoteContent.modular-ui .highlight{font-weight:700;color:var(--blue-5);background:none}.NoteContent.modular-ui .edit-content .edit-buttons .save-button.disabled{border:none}",""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1566:function(e,t,n){var o=n(32),r=n(1567);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.i,r,""]]);var i={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach(e=>o.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))}),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const r=t[o];if(0===o)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};o(r,i);e.exports=r.locals||{}},1567:function(e,t,n){(t=e.exports=n(33)(!1)).push([e.i,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.reply-area-container{border-top:1px solid var(--divider);display:flex;flex-direction:column;margin-bottom:0}.reply-area-container .reply-attachment-list{margin:12px 12px 0}.reply-area-container .reply-area-with-button{display:flex}.reply-area-container .reply-area-with-button .reply-area{position:relative;flex:1;margin:12px 17px 12px 12px;border-radius:4px;align-items:center}.reply-area-container .reply-area-with-button .reply-area.unread{background:rgba(0,165,228,.08)}.reply-area-container .reply-area-with-button .reply-area .comment-textarea .ql-container .ql-editor.ql-blank{padding:4px}.reply-area-container .reply-area-with-button .reply-area .comment-textarea .ql-container .ql-editor.ql-blank:before{left:4px}.reply-area-container .reply-area-with-button .reply-button-container{display:flex;flex-direction:column;justify-content:flex-end}.reply-area-container .reply-area-with-button .reply-button-container .reply-button{width:28px;height:28px;padding:0;border:none;background-color:transparent;right:10px;bottom:12px}:host(:not([data-tabbing=true])) .reply-area-container .reply-area-with-button .reply-button-container .reply-button,html:not([data-tabbing=true]) .reply-area-container .reply-area-with-button .reply-button-container .reply-button{outline:none}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.reply-area-container .reply-area-with-button .reply-button-container .reply-button{width:80px}}.reply-area-container .reply-area-with-button .reply-button-container .reply-button:hover{background:var(--blue-1)}.reply-area-container .reply-area-with-button .reply-button-container .reply-button.disabled{cursor:not-allowed}",""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1568:function(e,t,n){var o=n(32),r=n(1569);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.i,r,""]]);var i={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach(e=>o.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))}),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const r=t[o];if(0===o)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};o(r,i);e.exports=r.locals||{}},1569:function(e,t,n){(t=e.exports=n(33)(!1)).push([e.i,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}@keyframes bottom-up{0%{transform:translateY(100%)}to{transform:translateY(0)}}@keyframes up-bottom{0%{transform:translateY(0)}to{transform:translateY(100%)}}@media print{#line-connector-root{opacity:0}}#line-connector-root{position:relative;z-index:69}.horizontalLine{height:2px}.horizontalLine,.verticalLine{background-color:rgba(30,120,235,.5);position:fixed}.verticalLine{width:2px}.arrowHead{position:absolute;top:0;left:0;margin:auto;width:0;height:0;border-top:6px solid transparent;border-bottom:6px solid transparent;border-right:7px solid rgba(30,120,235,.5);transform:translateX(-100%) translateY(-50%) translateY(1px)}",""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1570:function(e,t,n){var o=n(32),r=n(1571);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.i,r,""]]);var i={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach(e=>o.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))}),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const r=t[o];if(0===o)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};o(r,i);e.exports=r.locals||{}},1571:function(e,t,n){(t=e.exports=n(33)(!1)).push([e.i,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.Note{padding:0;border:none;background-color:transparent;display:block;text-align:left;border-radius:4px;box-shadow:0 0 3px 0 var(--note-box-shadow);margin-bottom:8px;margin-left:2px;background:var(--component-background);cursor:pointer;position:relative}:host(:not([data-tabbing=true])) .Note,html:not([data-tabbing=true]) .Note{outline:none}.Note.unread{border:1.2px solid #00a5e4}.Note.expanded{box-shadow:0 4px 16px var(--note-box-shadow-expanded),0 0 4px 0 var(--note-box-shadow)}.Note.is-multi-selected{box-shadow:0 4px 16px rgba(134,142,150,.24),0 0 4px 0 var(--note-box-shadow)}.Note.disabled{opacity:.5;pointer-events:none}.Note .note-button{position:absolute;width:100%;height:100%;top:0;left:0}.Note .mark-all-read-button{background:#00a5e4;text-align:center;color:#fff;height:16px;font-size:12px;width:100%;border-radius:0}.Note .divider{height:1px;width:100%;background:var(--divider)}.Note .reply-divider{background:var(--reply-divider);height:1px;width:100%}.Note .replies{margin-left:52px;padding-bottom:12px}.Note .reply{padding-left:12px;padding-bottom:24px;border-left:1px solid var(--reply-divider)}.Note .reply:last-of-type{padding-bottom:0}.Note .group-section{margin-left:52px;padding-bottom:12px;display:flex;flex-direction:column;grid-row-gap:4px;row-gap:4px;padding-right:12px}.Note .group-section.modular-ui .group-child:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);background:var(--faded-component-background);border-radius:4px}.Note .text-button{color:var(--secondary-button-text);display:flex;position:relative;width:auto;height:auto;flex-direction:row-reverse;justify-content:flex-end}.Note .text-button .Icon{color:var(--secondary-button-text);height:18px;width:18px}.Note .group-child{position:relative;width:auto;height:auto;display:block;text-align:left;padding-top:4px;padding-bottom:4px}.Note .group-child:hover{background:var(--view-header-button-hover)}.Note .group-child .NoteContent{padding-bottom:0}.Note:focus{outline:none}.Note.focus-visible,.Note:focus-visible{outline:var(--focus-visible-outline)}",""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1572:function(e,t,n){"use strict";n(15),n(8),n(57),n(22),n(11),n(13),n(14),n(10),n(9),n(12),n(63),n(64),n(65),n(66),n(36),n(39),n(23),n(24),n(40),n(62),n(16);var o=n(0),r=n.n(o),i=n(6),a=n(3);function l(e){return(l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function c(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */c=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},r="function"==typeof Symbol?Symbol:{},i=r.iterator||"@@iterator",a=r.asyncIterator||"@@asyncIterator",s=r.toStringTag||"@@toStringTag";function u(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,n){return e[t]=n}}function d(e,t,n,r){var i=t&&t.prototype instanceof f?t:f,a=Object.create(i.prototype),l=new E(r||[]);return o(a,"_invoke",{value:_(e,n,l)}),a}function p(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=d;var h={};function f(){}function m(){}function g(){}var v={};u(v,i,(function(){return this}));var y=Object.getPrototypeOf,b=y&&y(y(k([])));b&&b!==t&&n.call(b,i)&&(v=b);var w=g.prototype=f.prototype=Object.create(v);function x(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function S(e,t){var r;o(this,"_invoke",{value:function(o,i){function a(){return new t((function(r,a){!function o(r,i,a,c){var s=p(e[r],e,i);if("throw"!==s.type){var u=s.arg,d=u.value;return d&&"object"==l(d)&&n.call(d,"__await")?t.resolve(d.__await).then((function(e){o("next",e,a,c)}),(function(e){o("throw",e,a,c)})):t.resolve(d).then((function(e){u.value=e,a(u)}),(function(e){return o("throw",e,a,c)}))}c(s.arg)}(o,i,r,a)}))}return r=r?r.then(a,a):a()}})}function _(e,t,n){var o="suspendedStart";return function(r,i){if("executing"===o)throw new Error("Generator is already running");if("completed"===o){if("throw"===r)throw i;return T()}for(n.method=r,n.arg=i;;){var a=n.delegate;if(a){var l=C(a,n);if(l){if(l===h)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===o)throw o="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o="executing";var c=p(e,t,n);if("normal"===c.type){if(o=n.done?"completed":"suspendedYield",c.arg===h)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o="completed",n.method="throw",n.arg=c.arg)}}}function C(e,t){var n=t.method,o=e.iterator[n];if(void 0===o)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,C(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),h;var r=p(o,e.iterator,t.arg);if("throw"===r.type)return t.method="throw",t.arg=r.arg,t.delegate=null,h;var i=r.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,h):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,h)}function O(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function q(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function E(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(O,this),this.reset(!0)}function k(e){if(e){var t=e[i];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,r=function t(){for(;++o<e.length;)if(n.call(e,o))return t.value=e[o],t.done=!1,t;return t.value=void 0,t.done=!0,t};return r.next=r}}return{next:T}}function T(){return{value:void 0,done:!0}}return m.prototype=g,o(w,"constructor",{value:g,configurable:!0}),o(g,"constructor",{value:m,configurable:!0}),m.displayName=u(g,s,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===m||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,g):(e.__proto__=g,u(e,s,"GeneratorFunction")),e.prototype=Object.create(w),e},e.awrap=function(e){return{__await:e}},x(S.prototype),u(S.prototype,a,(function(){return this})),e.AsyncIterator=S,e.async=function(t,n,o,r,i){void 0===i&&(i=Promise);var a=new S(d(t,n,o,r),i);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},x(w),u(w,s,"Generator"),u(w,i,(function(){return this})),u(w,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var o in t)n.push(o);return n.reverse(),function e(){for(;n.length;){var o=n.pop();if(o in t)return e.value=o,e.done=!1,e}return e.done=!0,e}},e.values=k,E.prototype={constructor:E,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(q),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function o(n,o){return a.type="throw",a.arg=e,t.next=n,o&&(t.method="next",t.arg=void 0),!!o}for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r],a=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(l&&c){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(e,t){for(var o=this.tryEntries.length-1;o>=0;--o){var r=this.tryEntries[o];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,h):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),h},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),q(n),h}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var r=o.arg;q(n)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:k(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),h}},e}function s(e,t,n,o,r,i,a){try{var l=e[i](a),c=l.value}catch(e){return void n(e)}l.done?t(c):Promise.resolve(c).then(o,r)}t.a=function(e){var t=e.annotationId,n=e.addAttachments,o=Object(i.e)((function(e){return a.a.getReplyAttachmentHandler(e)})),l=function(){var e,r=(e=c().mark((function e(r){var i,a,l;return c().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(i=r.target.files[0])){e.next=9;break}if(a=i,!o){e.next=8;break}return e.next=6,o(i);case 6:l=e.sent,a={url:l,name:i.name,size:i.size,type:i.type};case 8:n(t,[a]);case 9:case"end":return e.stop()}}),e)})),function(){var t=this,n=arguments;return new Promise((function(o,r){var i=e.apply(t,n);function a(e){s(i,o,r,a,l,"next",e)}function l(e){s(i,o,r,a,l,"throw",e)}a(void 0)}))});return function(e){return r.apply(this,arguments)}}();return r.a.createElement("input",{id:"reply-attachment-picker",type:"file",style:{display:"none"},onChange:l,onClick:function(e){e.target.value=""}})}},1582:function(e,t,n){"use strict";n(10),n(8),n(152),n(9),n(12),n(127),n(23),n(24),n(28),n(88),n(99),n(98),n(38),n(19),n(11),n(13),n(14),n(16),n(15),n(20),n(18),n(57),n(22),n(63),n(64),n(65),n(66),n(36),n(39),n(40),n(62),n(26),n(27),n(25),n(29),n(45),n(48),n(46);var o=n(0),r=n.n(o),i=n(17),a=n.n(i),l=n(4),c=n.n(l),s=n(6),u=n(428),d=n(1476),p=(n(1587),n(94),n(105),n(49),n(53),n(106),n(78),n(151),n(439),n(1595)),h=n(104),f=n.n(h),m=n(290),g=n.n(m),v=n(292),y=n.n(v),b=n(1497),w=(n(35),n(59),n(47),n(1)),x=n(3),S=n(5),_=n(2),C=(n(1556),n(93));function O(e){return(O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function q(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function E(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?q(Object(n),!0).forEach((function(t){k(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):q(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function k(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==O(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!==O(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===O(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function T(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,s=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){s=!0,r=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw r}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return R(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return R(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function R(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}var I=function(e,t,n){return{icon:t,label:"action.".concat(e.toLowerCase()),title:"action.".concat(e.toLowerCase()),option:e,dataElement:n}},A=[I("Edit","","notePopupEdit"),I("Delete","","notePopupDelete")],j={handleEdit:c.a.func,handleDelete:c.a.func,isEditable:c.a.bool,isDeletable:c.a.bool,noteId:c.a.string};function P(){}function N(e){var t=e.handleEdit,n=void 0===t?P:t,o=e.handleDelete,i=void 0===o?P:o,l=e.isEditable,c=e.isDeletable,d=e.isReply,p=e.noteId,h=Object(s.e)((function(e){var t;return null===(t=x.a.getFeatureFlags(e))||void 0===t?void 0:t.customizableUI})),f="".concat(S.a.NOTE_POPUP_FLYOUT,"-").concat(p),m=T(Object(u.a)(),1)[0];if(!l&&!c)return null;var g=a()("overflow note-popup-toggle-trigger"),v=a()("NotePopup options note-popup-options",{"options-reply":d,"modular-ui":h});return r.a.createElement("div",{className:v},r.a.createElement(C.a,{dataElement:"notePopup-".concat(p),className:g,img:"icon-tools-more",title:m("formField.formFieldPopup.options"),toggleElement:f,disabled:!1}),r.a.createElement(L,{flyoutSelector:f,handleClick:function(e){"Edit"===e?n():"Delete"===e&&i()},isEditable:l,isDeletable:c}))}var L=function(e){var t=e.flyoutSelector,n=e.handleClick,r=e.isEditable,i=e.isDeletable,a=Object(s.d)(),l=Object(s.e)((function(e){return x.a.getFlyout(e,t)})),c=T(Object(u.a)(),1)[0];return Object(o.useLayoutEffect)((function(){var e=A;r?i||(e=e.filter((function(e){return"Delete"!==e.option}))):e=e.filter((function(e){return"Edit"!==e.option}));var o={dataElement:t,className:"NotePopupFlyout",items:e.map((function(e){return E(E({},e),{},{label:c(e.label),title:c(e.title),onClick:function(){return n(e.option)}})}))};a(l?_.a.updateFlyout(o.dataElement,o):_.a.addFlyout(o))}),[r,i]),null};L.propTypes={flyoutSelector:c.a.string,handleClick:c.a.func,isEditable:c.a.bool,isDeletable:c.a.bool},N.propTypes=j;var M=N;function z(){return(z=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(this,arguments)}function H(e){return function(e){if(Array.isArray(e))return F(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||G(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function D(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,s=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){s=!0,r=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw r}}return l}}(e,t)||G(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function G(e,t){if(e){if("string"==typeof e)return F(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?F(e,t):void 0}}function F(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}var W=function(e){var t=D(Object(s.e)((function(e){return[x.a.getActiveDocumentViewerKey(e)]})),1)[0],n=e.annotation,o=e.setIsEditing,i=e.noteIndex,a=D(r.a.useState(w.a.canModify(n)),2),l=a[0],c=a[1],u=D(r.a.useState(w.a.canModifyContents(n)),2),d=u[0],p=u[1];r.a.useEffect((function(){function e(){c(w.a.canModify(n,t)),p(w.a.canModifyContents(n,t))}return e(),w.a.addEventListener("updateAnnotationPermission",e,void 0,t),function(){return w.a.removeEventListener("updateAnnotationPermission",e,t)}}),[n,t]);var h={handleEdit:r.a.useCallback((function(){n instanceof window.Core.Annotations.FreeTextAnnotation&&w.a.getAnnotationManager(t).isFreeTextEditingEnabled()?w.a.getAnnotationManager(t).trigger("annotationDoubleClicked",n):o(!0,i)}),[n,o,i]),handleDelete:r.a.useCallback((function(){w.a.deleteAnnotations([n].concat(H(n.getGroupedChildren())),void 0,t)}),[n]),isEditable:d,isDeletable:l&&!(null!=n&&n.NoDelete),noteId:n?n.Id:""};return r.a.createElement(M,z({},e,h))},U=n(1525);function B(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,s=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){s=!0,r=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw r}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return V(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return V(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function V(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}var Y={annotation:c.a.object.isRequired,handleStateChange:c.a.func};function K(e){var t=e.annotation,n=e.handleStateChange,o=void 0===n?function(){}:n,i=B(Object(u.a)(),1)[0],a=t.getStatus(),l="icon-annotation-status-".concat(""===a?"none":a.toLowerCase()),c=t.Id;return r.a.createElement(r.a.Fragment,null,r.a.createElement(C.a,{dataElement:"noteState-".concat(c),title:i("option.notesOrder.status"),img:l,toggleElement:"".concat(S.a.NOTE_STATE_FLYOUT,"-").concat(c)}),r.a.createElement(U.a,{noteId:c,handleStateChange:o}))}K.propTypes=Y;var $=K,X=n(1523),Z=n(172);function J(){return(J=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(this,arguments)}var Q={annotation:c.a.object};function ee(e){var t=Object(s.e)((function(e){return x.a.getActiveDocumentViewerKey(e)})),n=Object(s.e)((function(e){return x.a.isElementDisabled(e,"noteState")})),i=e.annotation,a=Object(Z.a)(Object(o.useCallback)((function(e){var n=Object(X.a)(i,e,t);i.addReply(n);var o=w.a.getAnnotationManager(t);o.addAnnotation(n),o.trigger("addReply",[n,i,o.getRootAnnotation(i)])}),[i,t]));return!n&&r.a.createElement("div",null,r.a.createElement($,J({handleStateChange:a},e)))}ee.propTypes=Q;var te=ee,ne=n(42),oe=n(122);function re(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,s=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){s=!0,r=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw r}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return ie(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ie(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ie(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}var ae={annotationId:c.a.string,ariaLabel:c.a.string,pendingEditTextMap:c.a.object,pendingReplyMap:c.a.object,pendingAttachmentMap:c.a.object},le=function(e){var t=e.annotationId,n=e.ariaLabel,i=e.pendingEditTextMap,a=e.pendingReplyMap,l=e.pendingAttachmentMap,c=Object(u.a)().t,s=re(Object(o.useState)(!1),2),d=s[0],p=s[1],h=re(Object(o.useState)(!1),2),f=h[0],m=h[1],g=re(Object(o.useState)(!1),2),v=g[0],y=g[1];return Object(o.useEffect)((function(){var e,n,o;p((null===(e=i[t])||void 0===e?void 0:e.length)>0),m((null===(n=a[t])||void 0===n?void 0:n.length)>0),y((null===(o=l[t])||void 0===o?void 0:o.length)>0)}),[i,a,l]),d||f||v?r.a.createElement("div",{"data-element":"unpostedCommentIndicator"},r.a.createElement(oe.a,{content:c("message.unpostedComment")},r.a.createElement("div",null,r.a.createElement(ne.a,{className:"type-icon",glyph:"icon-unposted-comment",ariaLabel:n})))):null};le.propTypes=ae;var ce=le,se={annotationId:c.a.string,ariaLabel:c.a.string},ue=function(e){var t=e.annotationId,n=e.ariaLabel,o=Object(s.e)((function(e){return x.a.isElementDisabled(e,"unpostedCommentIndicator")})),i=r.a.useContext(d.a),a=i.pendingEditTextMap,l=i.pendingReplyMap,c=i.pendingAttachmentMap;return o?null:r.a.createElement(ce,{annotationId:t,ariaLabel:n,pendingEditTextMap:a,pendingReplyMap:l,pendingAttachmentMap:c})};ue.propTypes=se;var de=ue,pe=n(153),he=n(44),fe=n(217),me=n(167),ge=n(244),ve=n(173),ye=n(216),be=n(41),we=n(101);n(1558);function xe(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,s=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){s=!0,r=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw r}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Se(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Se(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Se(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}var _e={icon:c.a.string,iconColor:c.a.string,color:c.a.string,fillColor:c.a.string,annotation:c.a.object,language:c.a.string,noteDateFormat:c.a.string,isSelected:c.a.bool,setIsEditing:c.a.func,notesShowLastUpdatedDate:c.a.bool,isUnread:c.a.bool,renderAuthorName:c.a.func,isNoteStateDisabled:c.a.bool,isEditing:c.a.bool,noteIndex:c.a.number,sortStrategy:c.a.string,activeTheme:c.a.string,isMultiSelected:c.a.bool,isMultiSelectMode:c.a.bool,handleMultiSelect:c.a.func,isGroupMember:c.a.bool,showAnnotationNumbering:c.a.bool,isTrackedChange:c.a.bool};function Ce(e){var t,n,o,i=e.icon,l=e.iconColor,c=e.annotation,s=e.language,d=e.noteDateFormat,p=e.isSelected,h=e.setIsEditing,m=e.notesShowLastUpdatedDate,g=e.isReply,v=e.isUnread,y=e.renderAuthorName,b=e.isNoteStateDisabled,x=e.isEditing,S=e.noteIndex,_=e.sortStrategy,C=e.activeTheme,O=e.isMultiSelected,q=e.isMultiSelectMode,E=e.handleMultiSelect,k=e.isGroupMember,T=e.showAnnotationNumbering,R=e.timezone,I=e.isTrackedChange,A=xe(Object(u.a)(),1)[0],j=_===ve.a.MODIFIED_DATE||m&&_!==ve.a.CREATED_DATE?Object(fe.a)(c):c.DateCreated;if(R&&j){var P=j.toLocaleString("en-US",{timeZone:R});o=new Date(P)}else o=j;var N=o?f()(o).locale(s).format(d):A("option.notesPanel.noteContent.noDate"),L=c.getReplies().length,M=null===(t=c[l])||void 0===t||null===(n=t.toHexString)||void 0===n?void 0:n.call(t);C===ye.a.DARK&&M&&Object(ge.c)(M)?M=we.b.white:C===ye.a.LIGHT&&M&&Object(ge.d)(M)&&(M=we.b.black);var z=Object(me.a)(c.FillColor),H=c.getAssociatedNumber(),D="#".concat(H," - "),G=a()("author-and-date",{isReply:g}),F=a()("NoteHeader",{parent:!g&&!k});return r.a.createElement("div",{className:F},!g&&r.a.createElement("div",{className:"type-icon-container"},v&&r.a.createElement("div",{className:"unread-notification"}),r.a.createElement(ne.a,{className:"type-icon",glyph:i,color:M,fillColor:z})),r.a.createElement("div",{className:G},r.a.createElement("div",{className:"author-and-overflow"},r.a.createElement("div",{className:"author-and-time"},r.a.createElement("div",{className:"author"},T&&void 0!==H&&r.a.createElement("span",{className:"annotation-number"},D),y(c)),r.a.createElement("div",{className:"date-and-num-replies"},r.a.createElement("div",{className:"date-and-time"},N,k&&" (Page ".concat(c.PageNumber,")")),L>0&&!p&&r.a.createElement("div",{className:"num-replies-container"},r.a.createElement(ne.a,{className:"num-reply-icon",glyph:"icon-chat-bubble"}),r.a.createElement("div",{className:"num-replies"},L)))),r.a.createElement("div",{className:"state-and-overflow"},q&&!k&&!g&&r.a.createElement(pe.a,{id:"note-multi-select-toggle_".concat(c.Id),"aria-label":"".concat(y(c)," ").concat(A("option.notesPanel.toggleMultiSelect")),checked:O,onClick:function(e){e.preventDefault(),e.stopPropagation(),E(!O)}}),r.a.createElement(de,{annotationId:c.Id,ariaLabel:"Unposted Comment, ".concat(y(c),", ").concat(N)}),!b&&!g&&!q&&!k&&!I&&r.a.createElement(te,{annotation:c,isSelected:p}),!x&&p&&!q&&!k&&!I&&r.a.createElement(W,{noteIndex:S,annotation:c,setIsEditing:h,isReply:g}),p&&I&&!q&&r.a.createElement(r.a.Fragment,null,r.a.createElement(he.a,{title:A("officeEditor.accept"),img:"icon-menu-checkmark",className:"tracked-change-icon-wrapper accept",onClick:function(){return e=c.getCustomData(be.m),void w.a.getOfficeEditor().acceptTrackedChange(e);var e},iconClassName:"tracked-change-icon"}),r.a.createElement(he.a,{title:A("officeEditor.reject"),img:"icon-close",className:"tracked-change-icon-wrapper reject",onClick:function(){return e=c.getCustomData(be.m),void w.a.getOfficeEditor().rejectTrackedChange(e);var e},iconClassName:"tracked-change-icon"}))))))}Ce.propTypes=_e;var Oe=Ce,qe=n(1529);function Ee(){return(Ee=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(this,arguments)}function ke(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,s=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){s=!0,r=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw r}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Te(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Te(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Te(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}var Re=function(e){var t=ke(Object(s.e)((function(e){return[x.a.getNotesPanelWidth(e)]}),s.c),1)[0];return r.a.createElement(qe.a,Ee({},e,{panelWidth:t}))},Ie=(n(345),n(346),n(163));n(215);function Ae(e){return(Ae="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function je(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */je=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},r="function"==typeof Symbol?Symbol:{},i=r.iterator||"@@iterator",a=r.asyncIterator||"@@asyncIterator",l=r.toStringTag||"@@toStringTag";function c(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,n){return e[t]=n}}function s(e,t,n,r){var i=t&&t.prototype instanceof p?t:p,a=Object.create(i.prototype),l=new O(r||[]);return o(a,"_invoke",{value:x(e,n,l)}),a}function u(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=s;var d={};function p(){}function h(){}function f(){}var m={};c(m,i,(function(){return this}));var g=Object.getPrototypeOf,v=g&&g(g(q([])));v&&v!==t&&n.call(v,i)&&(m=v);var y=f.prototype=p.prototype=Object.create(m);function b(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function w(e,t){var r;o(this,"_invoke",{value:function(o,i){function a(){return new t((function(r,a){!function o(r,i,a,l){var c=u(e[r],e,i);if("throw"!==c.type){var s=c.arg,d=s.value;return d&&"object"==Ae(d)&&n.call(d,"__await")?t.resolve(d.__await).then((function(e){o("next",e,a,l)}),(function(e){o("throw",e,a,l)})):t.resolve(d).then((function(e){s.value=e,a(s)}),(function(e){return o("throw",e,a,l)}))}l(c.arg)}(o,i,r,a)}))}return r=r?r.then(a,a):a()}})}function x(e,t,n){var o="suspendedStart";return function(r,i){if("executing"===o)throw new Error("Generator is already running");if("completed"===o){if("throw"===r)throw i;return E()}for(n.method=r,n.arg=i;;){var a=n.delegate;if(a){var l=S(a,n);if(l){if(l===d)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===o)throw o="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o="executing";var c=u(e,t,n);if("normal"===c.type){if(o=n.done?"completed":"suspendedYield",c.arg===d)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o="completed",n.method="throw",n.arg=c.arg)}}}function S(e,t){var n=t.method,o=e.iterator[n];if(void 0===o)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,S(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),d;var r=u(o,e.iterator,t.arg);if("throw"===r.type)return t.method="throw",t.arg=r.arg,t.delegate=null,d;var i=r.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,d):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,d)}function _(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function C(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function O(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(_,this),this.reset(!0)}function q(e){if(e){var t=e[i];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,r=function t(){for(;++o<e.length;)if(n.call(e,o))return t.value=e[o],t.done=!1,t;return t.value=void 0,t.done=!0,t};return r.next=r}}return{next:E}}function E(){return{value:void 0,done:!0}}return h.prototype=f,o(y,"constructor",{value:f,configurable:!0}),o(f,"constructor",{value:h,configurable:!0}),h.displayName=c(f,l,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===h||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,c(e,l,"GeneratorFunction")),e.prototype=Object.create(y),e},e.awrap=function(e){return{__await:e}},b(w.prototype),c(w.prototype,a,(function(){return this})),e.AsyncIterator=w,e.async=function(t,n,o,r,i){void 0===i&&(i=Promise);var a=new w(s(t,n,o,r),i);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},b(y),c(y,l,"Generator"),c(y,i,(function(){return this})),c(y,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var o in t)n.push(o);return n.reverse(),function e(){for(;n.length;){var o=n.pop();if(o in t)return e.value=o,e.done=!1,e}return e.done=!0,e}},e.values=q,O.prototype={constructor:O,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(C),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function o(n,o){return a.type="throw",a.arg=e,t.next=n,o&&(t.method="next",t.arg=void 0),!!o}for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r],a=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(l&&c){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(e,t){for(var o=this.tryEntries.length-1;o>=0;--o){var r=this.tryEntries[o];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,d):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),d},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),C(n),d}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var r=o.arg;C(n)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:q(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),d}},e}function Pe(e,t,n,o,r,i,a){try{var l=e[i](a),c=l.value}catch(e){return void n(e)}l.done?t(c):Promise.resolve(c).then(o,r)}function Ne(e){return function(){var t=this,n=arguments;return new Promise((function(o,r){var i=e.apply(t,n);function a(e){Pe(i,o,r,a,l,"next",e)}function l(e){Pe(i,o,r,a,l,"throw",e)}a(void 0)}))}}var Le="ic-file-pdf",Me="ic-file-img",ze="ic-file-cad",He="ic-file-doc",De="ic-file-ppt",Ge="ic-file-xls",Fe="ic-file-etc",We=window.Core.Annotations.FileAttachmentUtils;function Ue(e){return Be.apply(this,arguments)}function Be(){return(Be=Ne(je().mark((function e(t){return je().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",We.decompressWithFlateDecode(t.content,t.type));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function Ve(e){return Ye.apply(this,arguments)}function Ye(){return(Ye=Ne(je().mark((function e(t){var n,o=arguments;return je().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=o.length>1&&void 0!==o[1]?o[1]:[],e.next=3,t.setAttachments(n);case 3:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function Ke(e){return!(!e.type||!e.type.startsWith("image/"))}function $e(e){var t;if(Ke(e))return Me;switch(null===(t=e.name)||void 0===t?void 0:t.split(".").pop().toLowerCase()){case"pdf":return Le;case"cad":return ze;case"doc":case"docx":return He;case"ppt":case"pptx":return De;case"xls":case"xlsx":return Ge;default:return Fe}}var Xe=n(1590),Ze=n.n(Xe);function Je(e){return(Je="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Qe(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */Qe=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},r="function"==typeof Symbol?Symbol:{},i=r.iterator||"@@iterator",a=r.asyncIterator||"@@asyncIterator",l=r.toStringTag||"@@toStringTag";function c(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,n){return e[t]=n}}function s(e,t,n,r){var i=t&&t.prototype instanceof p?t:p,a=Object.create(i.prototype),l=new O(r||[]);return o(a,"_invoke",{value:x(e,n,l)}),a}function u(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=s;var d={};function p(){}function h(){}function f(){}var m={};c(m,i,(function(){return this}));var g=Object.getPrototypeOf,v=g&&g(g(q([])));v&&v!==t&&n.call(v,i)&&(m=v);var y=f.prototype=p.prototype=Object.create(m);function b(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function w(e,t){var r;o(this,"_invoke",{value:function(o,i){function a(){return new t((function(r,a){!function o(r,i,a,l){var c=u(e[r],e,i);if("throw"!==c.type){var s=c.arg,d=s.value;return d&&"object"==Je(d)&&n.call(d,"__await")?t.resolve(d.__await).then((function(e){o("next",e,a,l)}),(function(e){o("throw",e,a,l)})):t.resolve(d).then((function(e){s.value=e,a(s)}),(function(e){return o("throw",e,a,l)}))}l(c.arg)}(o,i,r,a)}))}return r=r?r.then(a,a):a()}})}function x(e,t,n){var o="suspendedStart";return function(r,i){if("executing"===o)throw new Error("Generator is already running");if("completed"===o){if("throw"===r)throw i;return E()}for(n.method=r,n.arg=i;;){var a=n.delegate;if(a){var l=S(a,n);if(l){if(l===d)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===o)throw o="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o="executing";var c=u(e,t,n);if("normal"===c.type){if(o=n.done?"completed":"suspendedYield",c.arg===d)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o="completed",n.method="throw",n.arg=c.arg)}}}function S(e,t){var n=t.method,o=e.iterator[n];if(void 0===o)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,S(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),d;var r=u(o,e.iterator,t.arg);if("throw"===r.type)return t.method="throw",t.arg=r.arg,t.delegate=null,d;var i=r.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,d):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,d)}function _(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function C(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function O(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(_,this),this.reset(!0)}function q(e){if(e){var t=e[i];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,r=function t(){for(;++o<e.length;)if(n.call(e,o))return t.value=e[o],t.done=!1,t;return t.value=void 0,t.done=!0,t};return r.next=r}}return{next:E}}function E(){return{value:void 0,done:!0}}return h.prototype=f,o(y,"constructor",{value:f,configurable:!0}),o(f,"constructor",{value:h,configurable:!0}),h.displayName=c(f,l,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===h||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,c(e,l,"GeneratorFunction")),e.prototype=Object.create(y),e},e.awrap=function(e){return{__await:e}},b(w.prototype),c(w.prototype,a,(function(){return this})),e.AsyncIterator=w,e.async=function(t,n,o,r,i){void 0===i&&(i=Promise);var a=new w(s(t,n,o,r),i);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},b(y),c(y,l,"Generator"),c(y,i,(function(){return this})),c(y,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var o in t)n.push(o);return n.reverse(),function e(){for(;n.length;){var o=n.pop();if(o in t)return e.value=o,e.done=!1,e}return e.done=!0,e}},e.values=q,O.prototype={constructor:O,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(C),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function o(n,o){return a.type="throw",a.arg=e,t.next=n,o&&(t.method="next",t.arg=void 0),!!o}for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r],a=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(l&&c){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(e,t){for(var o=this.tryEntries.length-1;o>=0;--o){var r=this.tryEntries[o];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,d):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),d},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),C(n),d}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var r=o.arg;C(n)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:q(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),d}},e}function et(e,t,n,o,r,i,a){try{var l=e[i](a),c=l.value}catch(e){return void n(e)}l.done?t(c):Promise.resolve(c).then(o,r)}var tt=function(e){return new Promise((function(t){if(void 0===e.size)t(e.toString("utf-8"));else{var n=new FileReader;n.onload=function(){return t(n.result)},n.readAsText(e)}}))},nt=function(e){return"image/svg+xml"===e.type},ot=function(){var e,t=(e=Qe().mark((function e(t){var n,o,r,i;return Qe().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,tt(t);case 2:if(n=e.sent){e.next=5;break}return e.abrupt("return",{svg:t});case 5:return o=[],Ze.a.addHook("uponSanitizeElement",(function(e,t){var n=t.tagName;t.allowedTags[n]||o.push(n)})),r=Ze.a.sanitize(n),i=new Blob([r],{type:"image/svg+xml"}),e.abrupt("return",{svg:i,isDirty:o.length>0});case 10:case"end":return e.stop()}}),e)})),function(){var t=this,n=arguments;return new Promise((function(o,r){var i=e.apply(t,n);function a(e){et(i,o,r,a,l,"next",e)}function l(e){et(i,o,r,a,l,"throw",e)}a(void 0)}))});return function(e){return t.apply(this,arguments)}}();n(1562);function rt(e){return(rt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function it(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */it=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},r="function"==typeof Symbol?Symbol:{},i=r.iterator||"@@iterator",a=r.asyncIterator||"@@asyncIterator",l=r.toStringTag||"@@toStringTag";function c(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,n){return e[t]=n}}function s(e,t,n,r){var i=t&&t.prototype instanceof p?t:p,a=Object.create(i.prototype),l=new O(r||[]);return o(a,"_invoke",{value:x(e,n,l)}),a}function u(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=s;var d={};function p(){}function h(){}function f(){}var m={};c(m,i,(function(){return this}));var g=Object.getPrototypeOf,v=g&&g(g(q([])));v&&v!==t&&n.call(v,i)&&(m=v);var y=f.prototype=p.prototype=Object.create(m);function b(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function w(e,t){var r;o(this,"_invoke",{value:function(o,i){function a(){return new t((function(r,a){!function o(r,i,a,l){var c=u(e[r],e,i);if("throw"!==c.type){var s=c.arg,d=s.value;return d&&"object"==rt(d)&&n.call(d,"__await")?t.resolve(d.__await).then((function(e){o("next",e,a,l)}),(function(e){o("throw",e,a,l)})):t.resolve(d).then((function(e){s.value=e,a(s)}),(function(e){return o("throw",e,a,l)}))}l(c.arg)}(o,i,r,a)}))}return r=r?r.then(a,a):a()}})}function x(e,t,n){var o="suspendedStart";return function(r,i){if("executing"===o)throw new Error("Generator is already running");if("completed"===o){if("throw"===r)throw i;return E()}for(n.method=r,n.arg=i;;){var a=n.delegate;if(a){var l=S(a,n);if(l){if(l===d)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===o)throw o="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o="executing";var c=u(e,t,n);if("normal"===c.type){if(o=n.done?"completed":"suspendedYield",c.arg===d)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o="completed",n.method="throw",n.arg=c.arg)}}}function S(e,t){var n=t.method,o=e.iterator[n];if(void 0===o)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,S(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),d;var r=u(o,e.iterator,t.arg);if("throw"===r.type)return t.method="throw",t.arg=r.arg,t.delegate=null,d;var i=r.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,d):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,d)}function _(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function C(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function O(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(_,this),this.reset(!0)}function q(e){if(e){var t=e[i];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,r=function t(){for(;++o<e.length;)if(n.call(e,o))return t.value=e[o],t.done=!1,t;return t.value=void 0,t.done=!0,t};return r.next=r}}return{next:E}}function E(){return{value:void 0,done:!0}}return h.prototype=f,o(y,"constructor",{value:f,configurable:!0}),o(f,"constructor",{value:h,configurable:!0}),h.displayName=c(f,l,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===h||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,c(e,l,"GeneratorFunction")),e.prototype=Object.create(y),e},e.awrap=function(e){return{__await:e}},b(w.prototype),c(w.prototype,a,(function(){return this})),e.AsyncIterator=w,e.async=function(t,n,o,r,i){void 0===i&&(i=Promise);var a=new w(s(t,n,o,r),i);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},b(y),c(y,l,"Generator"),c(y,i,(function(){return this})),c(y,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var o in t)n.push(o);return n.reverse(),function e(){for(;n.length;){var o=n.pop();if(o in t)return e.value=o,e.done=!1,e}return e.done=!0,e}},e.values=q,O.prototype={constructor:O,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(C),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function o(n,o){return a.type="throw",a.arg=e,t.next=n,o&&(t.method="next",t.arg=void 0),!!o}for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r],a=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(l&&c){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(e,t){for(var o=this.tryEntries.length-1;o>=0;--o){var r=this.tryEntries[o];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,d):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),d},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),C(n),d}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var r=o.arg;C(n)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:q(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),d}},e}function at(e,t,n,o,r,i,a){try{var l=e[i](a),c=l.value}catch(e){return void n(e)}l.done?t(c):Promise.resolve(c).then(o,r)}function lt(e){return function(){var t=this,n=arguments;return new Promise((function(o,r){var i=e.apply(t,n);function a(e){at(i,o,r,a,l,"next",e)}function l(e){at(i,o,r,a,l,"throw",e)}a(void 0)}))}}function ct(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,s=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){s=!0,r=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw r}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return st(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return st(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function st(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}var ut=function(e){var t=e.file,n=ct(Object(u.a)(),1)[0],i=ct(Object(o.useState)(),2),l=i[0],c=i[1],s=ct(Object(o.useState)(!1),2),d=s[0],p=s[1];return Object(o.useEffect)((function(){(function(){var e=lt(it().mark((function e(){var n,o,r,i,a;return it().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(p(!1),n=t,!(o=!(t instanceof File||t.url))){e.next=7;break}return e.next=6,Ue(t);case 6:n=e.sent;case 7:if(!(t instanceof File||o)){e.next=19;break}if(!nt(t)){e.next=18;break}return e.next=11,ot(n);case 11:r=e.sent,i=r.svg,a=r.isDirty,c(URL.createObjectURL(i)),p(a),e.next=19;break;case 18:c(URL.createObjectURL(n));case 19:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}})()()}),[t]),r.a.createElement("div",{className:a()({"reply-attachment-preview":!0,dirty:d})},r.a.createElement("img",{src:l}),d&&r.a.createElement("span",{className:"reply-attachment-preview-message"},n("message.svgMalicious")))},dt=function(e){var t=e.files,n=e.isEditing,o=e.fileDeleted,i=ct(Object(s.e)((function(e){return[x.a.getTabManager(e),x.a.isReplyAttachmentPreviewEnabled(e)]})),2),a=i[0],l=i[1],c=ct(Object(u.a)(),1)[0],d=function(){var e=lt(it().mark((function e(t,n){var o;return it().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.preventDefault(),t.stopPropagation(),a){e.next=4;break}return e.abrupt("return",console.warn("Can't open attachment in non-multi-tab mode"));case 4:if(!(n instanceof File)){e.next=8;break}o=n,e.next=15;break;case 8:if(!n.url){e.next=12;break}o=n.url,e.next=15;break;case 12:return e.next=14,Ue(n);case 14:o=e.sent;case 15:o&&a.addTab(o,{filename:n.name,setActive:!0,saveCurrentActiveTabState:!0});case 16:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),p=function(){var e=lt(it().mark((function e(t,n){var o;return it().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.preventDefault(),t.stopPropagation(),!n.url){e.next=6;break}e.t0=n.url,e.next=9;break;case 6:return e.next=8,Ue(n);case 8:e.t0=e.sent;case 9:o=e.t0,Object(Ie.saveAs)(o,n.name);case 11:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}();return r.a.createElement("div",{className:"reply-attachment-list"},t.map((function(e,t){return r.a.createElement("div",{className:"reply-attachment",key:t,onClick:function(t){return d(t,e)}},l&&Ke(e)&&r.a.createElement(ut,{file:e}),r.a.createElement("div",{className:"reply-attachment-info"},r.a.createElement(ne.a,{className:"reply-attachment-icon",glyph:$e(e)}),r.a.createElement(oe.a,{content:e.name},r.a.createElement("div",{className:"reply-file-name"},e.name)),n?r.a.createElement(he.a,{className:"attachment-button",title:"".concat(c("action.delete")," ").concat(c("option.type.fileattachment")),img:"icon-close",onClick:function(t){return function(e,t){e.preventDefault(),e.stopPropagation(),o(t)}(t,e)}}):r.a.createElement(he.a,{className:"attachment-button",title:"".concat(c("action.download")," ").concat(c("option.type.fileattachment")),img:"icon-download",onClick:function(t){return p(t,e)}})))})))},pt=n(339),ht=n(1494),ft=function(e){var t={};if(e["font-weight"]&&"normal"!==e["font-weight"]&&(t.bold=!0),e["font-style"]&&"normal"!==e["font-style"]&&(t.italic=!0),e.color&&(t.color=e.color),e["text-decoration"]){var n=e["text-decoration"].split(" ");n.includes("line-through")&&(t.strike=!0),n.includes("word")&&(t.underline=!0)}return t},mt=function(e,t){for(var n=e.getRichTextStyle(),o=Object.keys(n),r=pt.a.getFormattedTextFromDeltas(t.getContents()),i=pt.a.extractMentionDataFromStr(r).plainTextValue,a=[],l=0;l<o.length;l++){var c=n[o[l]],s=ft(c);if(!isNaN(o[l])){var u=isNaN(o[l+1])?i.length:o[l+1],d=i.slice(o[l],u);a.push({insert:d,attributes:s})}}t.setContents(a),t.setSelection(i.length,0)},gt=n(37),vt=n(50),yt=n(214),bt=n(76);n(1564);function wt(e){return(wt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function xt(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */xt=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},r="function"==typeof Symbol?Symbol:{},i=r.iterator||"@@iterator",a=r.asyncIterator||"@@asyncIterator",l=r.toStringTag||"@@toStringTag";function c(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,n){return e[t]=n}}function s(e,t,n,r){var i=t&&t.prototype instanceof p?t:p,a=Object.create(i.prototype),l=new O(r||[]);return o(a,"_invoke",{value:x(e,n,l)}),a}function u(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=s;var d={};function p(){}function h(){}function f(){}var m={};c(m,i,(function(){return this}));var g=Object.getPrototypeOf,v=g&&g(g(q([])));v&&v!==t&&n.call(v,i)&&(m=v);var y=f.prototype=p.prototype=Object.create(m);function b(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function w(e,t){var r;o(this,"_invoke",{value:function(o,i){function a(){return new t((function(r,a){!function o(r,i,a,l){var c=u(e[r],e,i);if("throw"!==c.type){var s=c.arg,d=s.value;return d&&"object"==wt(d)&&n.call(d,"__await")?t.resolve(d.__await).then((function(e){o("next",e,a,l)}),(function(e){o("throw",e,a,l)})):t.resolve(d).then((function(e){s.value=e,a(s)}),(function(e){return o("throw",e,a,l)}))}l(c.arg)}(o,i,r,a)}))}return r=r?r.then(a,a):a()}})}function x(e,t,n){var o="suspendedStart";return function(r,i){if("executing"===o)throw new Error("Generator is already running");if("completed"===o){if("throw"===r)throw i;return E()}for(n.method=r,n.arg=i;;){var a=n.delegate;if(a){var l=S(a,n);if(l){if(l===d)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===o)throw o="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o="executing";var c=u(e,t,n);if("normal"===c.type){if(o=n.done?"completed":"suspendedYield",c.arg===d)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o="completed",n.method="throw",n.arg=c.arg)}}}function S(e,t){var n=t.method,o=e.iterator[n];if(void 0===o)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,S(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),d;var r=u(o,e.iterator,t.arg);if("throw"===r.type)return t.method="throw",t.arg=r.arg,t.delegate=null,d;var i=r.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,d):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,d)}function _(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function C(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function O(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(_,this),this.reset(!0)}function q(e){if(e){var t=e[i];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,r=function t(){for(;++o<e.length;)if(n.call(e,o))return t.value=e[o],t.done=!1,t;return t.value=void 0,t.done=!0,t};return r.next=r}}return{next:E}}function E(){return{value:void 0,done:!0}}return h.prototype=f,o(y,"constructor",{value:f,configurable:!0}),o(f,"constructor",{value:h,configurable:!0}),h.displayName=c(f,l,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===h||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,c(e,l,"GeneratorFunction")),e.prototype=Object.create(y),e},e.awrap=function(e){return{__await:e}},b(w.prototype),c(w.prototype,a,(function(){return this})),e.AsyncIterator=w,e.async=function(t,n,o,r,i){void 0===i&&(i=Promise);var a=new w(s(t,n,o,r),i);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},b(y),c(y,l,"Generator"),c(y,i,(function(){return this})),c(y,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var o in t)n.push(o);return n.reverse(),function e(){for(;n.length;){var o=n.pop();if(o in t)return e.value=o,e.done=!1,e}return e.done=!0,e}},e.values=q,O.prototype={constructor:O,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(C),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function o(n,o){return a.type="throw",a.arg=e,t.next=n,o&&(t.method="next",t.arg=void 0),!!o}for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r],a=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(l&&c){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(e,t){for(var o=this.tryEntries.length-1;o>=0;--o){var r=this.tryEntries[o];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,d):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),d},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),C(n),d}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var r=o.arg;C(n)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:q(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),d}},e}function St(e,t,n,o,r,i,a){try{var l=e[i](a),c=l.value}catch(e){return void n(e)}l.done?t(c):Promise.resolve(c).then(o,r)}function _t(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,s=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){s=!0,r=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw r}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Ct(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Ct(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ct(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}f.a.extend(g.a);var Ot={annotation:c.a.object.isRequired,isEditing:c.a.bool,setIsEditing:c.a.func,noteIndex:c.a.number,isUnread:c.a.bool,isNonReplyNoteRead:c.a.bool,onReplyClicked:c.a.func,isMultiSelected:c.a.bool,isMultiSelectMode:c.a.bool,handleMultiSelect:c.a.func,isGroupMember:c.a.bool},qt=function(e){var t,n=e.annotation,i=e.isEditing,l=e.setIsEditing,c=e.noteIndex,h=e.isUnread,f=e.isNonReplyNoteRead,m=e.onReplyClicked,g=e.isMultiSelected,v=e.isMultiSelectMode,b=e.handleMultiSelect,S=e.isGroupMember,C=Object(s.e)((function(e){return x.a.getNoteDateFormat(e)})),O=Object(s.e)((function(e){return x.a.getIconColor(e,Object(vt.g)(n),s.c)})),q=Object(s.e)((function(e){return x.a.isElementDisabled(e,"noteStateFlyout")})),E=Object(s.e)((function(e){return x.a.getCurrentLanguage(e)})),k=Object(s.e)((function(e){return x.a.notesShowLastUpdatedDate(e)})),T=Object(s.e)((function(e){return x.a.isNotesPanelTextCollapsingEnabled(e)})),R=Object(s.e)((function(e){return x.a.isNotesPanelRepliesCollapsingEnabled(e)})),I=Object(s.e)((function(e){return x.a.getActiveTheme(e)})),A=Object(s.e)((function(e){return x.a.getTimezone(e)})),j=Object(s.e)((function(e){var t;return null===(t=x.a.getFeatureFlags(e))||void 0===t?void 0:t.customizableUI})),P=Object(o.useContext)(d.a),N=P.isSelected,L=P.searchInput,M=P.resize,z=P.pendingEditTextMap,H=P.onTopNoteContentClicked,D=P.sortStrategy,G=P.showAnnotationNumbering,F=P.setPendingEditText,W=Object(s.d)(),U=_t(Object(u.a)(),1)[0],B=n.isReply(),V=Object(vt.g)(n)===vt.c.TRACKED_CHANGE,Y=_t(Object(o.useState)([]),2),K=Y[0],$=Y[1];Object(o.useEffect)((function(){$(n.getAttachments())}),[n]),Object(o.useEffect)((function(){var e=function(e,t){"modify"===t&&e.forEach((function(e){e.Id===n.Id&&$(e.getAttachments())}))};return w.a.addEventListener("annotationChanged",e),function(){w.a.removeEventListener("annotationChanged",e)}}),[n]),Object(yt.a)((function(){i||W(_.a.finishNoteEditing()),M()}),[i]);var X,Z=Object(o.useCallback)((function(e){var t=w.a.getDisplayAuthor(e.Author);return t?It(t,L):U("option.notesPanel.noteContent.noName")}),[L]),J=n.getSkipAutoLink&&n.getSkipAutoLink(),Q=Object(o.useCallback)((function(e,t,o,i){var a=[];if(i||p.a.link(e,{stripPrefix:!1,stripTrailingSlash:!1,replaceFn:function(e){var t=e.getAnchorHref(),n=e.getAnchorText(),o=e.getOffset();switch(e.getType()){case"url":case"email":case"phone":a.push({href:t,text:n,start:o,end:o+e.getMatchedText().length})}}}),!a.length){var l=It(e,L,t);if(!L&&(!B&&T||B&&R)){return r.a.createElement(Re,{linesToBreak:3,comment:!0,renderRichText:Rt,richTextStyle:t,resize:M,style:o,beforeContent:function(){if(!V)return null;var e=1===n.TrackedChangeType?U("officeEditor.added"):U("officeEditor.deleted");return r.a.createElement("span",{style:{color:n.FillColor.toString(),fontWeight:700}},e)}},e)}return l}var c=[],s=0;return a.forEach((function(n,o){var i=n.start,a=n.end,l=n.href;s<i&&c.push(r.a.createElement("span",{key:"span_".concat(o)},It(e,L,t,s,i))),c.push(r.a.createElement("a",{href:l,target:"_blank",rel:"noopener noreferrer",key:"a_".concat(o)},It(e,L,t,i,a))),s=a})),s<e.length-1&&c.push(It(e,L,t,s)),c}),[L]),ee=Object(vt.e)(Object(vt.g)(n)).icon;try{X=JSON.parse(n.getCustomData("trn-mention"))}catch(e){X=n.getCustomData("trn-mention")}var te=(null===(t=X)||void 0===t?void 0:t.contents)||n.getContents(),ne=n.getContents(),oe=n.getRichTextStyle(),re=n.TextColor;if(I===ye.a.DARK)re&&Object(ge.c)(re.toHexString())&&(re=new window.Core.Annotations.Color(255,255,255,1)),oe&&Object.keys(oe).forEach((function(e){oe[e].color&&Object(ge.c)(oe[e].color)&&(oe[e].color=we.b.white)}));else if(I===ye.a.LIGHT){if(re&&Object(ge.d)(re.toHexString())&&(re=new window.Core.Annotations.Color(0,0,0,1)),oe)Object.keys(oe).forEach((function(e){oe[e].color&&Object(ge.d)(oe[e].color)&&(oe[e].color=we.b.black)}))}var ie,ae=void 0===z[n.Id];ie=te&&ae?te:z[n.Id];var le=function(e){var t;null!==(t=window.getSelection())&&void 0!==t&&t.toString()&&(null==e||e.stopPropagation())},ce=a()({NoteContent:!0,isReply:B,unread:h,clicked:f,"modular-ui":j}),se=Object(o.useMemo)((function(){var e={};return re&&(e.color=re.toHexString()),r.a.createElement(r.a.Fragment,null,i&&N?r.a.createElement(kt,{annotation:n,noteIndex:c,setIsEditing:l,textAreaValue:ie,onTextAreaValueChange:F,pendingText:z[n.Id]}):ne&&r.a.createElement("div",{className:a()("container",{"reply-content":B}),onClick:le},B&&K.length>0&&r.a.createElement(dt,{files:K,isEditing:!1}),Q(ne,oe,e,J)))}),[n,N,i,l,te,Q,ie,F,K]),ue=n.getCustomData("trn-annot-preview"),de=Object(o.useMemo)((function(){if(""===ue)return null;var e=It(ue,L),t=!B&&T;return y()(e)&&t?r.a.createElement(bt.a,{className:"selected-text-preview",dataElement:"notesSelectedTextPreview"},r.a.createElement(Re,{linesToBreak:3},'"'.concat(e,'"'))):r.a.createElement("div",{className:"selected-text-preview",style:{paddingRight:"12px"}},e)}),[ue,L]),pe=Object(o.useMemo)((function(){return r.a.createElement(Oe,{icon:ee,iconColor:O,annotation:n,language:E,noteDateFormat:C,isSelected:N,setIsEditing:l,notesShowLastUpdatedDate:k,isReply:B,isUnread:h,renderAuthorName:Z,isNoteStateDisabled:q,isEditing:i,noteIndex:c,sortStrategy:D,activeTheme:I,handleMultiSelect:b,isMultiSelected:g,isMultiSelectMode:v,isGroupMember:S,showAnnotationNumbering:G,timezone:A,isTrackedChange:V})}),[ee,O,n,E,C,N,l,k,B,h,Z,w.a.getDisplayAuthor(n.Author),q,i,c,Object(fe.a)(n),D,b,g,v,S,A,V]);return r.a.createElement("div",{className:ce,onClick:function(){S||(B?m(n):i||H())}},pe,de,se)};qt.propTypes=Ot;var Et=qt,kt=function(e){var t=e.annotation,n=e.noteIndex,i=e.setIsEditing,l=e.textAreaValue,c=e.onTextAreaValueChange,p=e.pendingText,h=_t(Object(s.e)((function(e){return[x.a.getAutoFocusNoteOnAnnotationSelection(e),x.a.getIsMentionEnabled(e),x.a.isElementDisabled(e,S.a.INLINE_COMMENT_POPUP),x.a.isElementOpen(e,S.a.INLINE_COMMENT_POPUP),x.a.isElementOpen(e,S.a.NOTES_PANEL),x.a.getActiveDocumentViewerKey(e),x.a.isAnyCustomPanelOpen(e)]})),7),f=h[0],m=h[1],g=h[2],v=h[3],y=h[4],_=h[5],C=h[6],O=_t(Object(u.a)(),1)[0],q=Object(o.useRef)(),E=t.isReply(),k=Object(o.useContext)(d.a),T=k.setCurAnnotId,R=k.pendingAttachmentMap,I=k.deleteAttachment,A=k.clearAttachments,j=k.addAttachments,P=!g&&v&&Object(gt.k)();Object(o.useEffect)((function(){if(C||(y||v)&&q.current){var e=q.current.getEditor();t&&t instanceof window.Core.Annotations.FreeTextAnnotation&&e.setText(""),p?Object(ht.a)(e,t):e.getContents()&&setTimeout((function(){if(m){l=pt.a.getFormattedTextFromDeltas(e.getContents());var n=pt.a.extractMentionDataFromStr(l),o=n.plainTextValue;n.ids.length&&e.setText(o)}var r;P||f&&(null===(r=q.current)||void 0===r||r.focus(),t.getRichTextStyle()&&mt(t,e))}),100);var n=e.getLength()-1;if(P)return;setTimeout((function(){n&&e.setSelection(n,n)}),100)}}),[y,v,P]),Object(o.useEffect)((function(){if(E&&0===M.length){var e=t.getAttachments();j(t.Id,e)}}),[]);var N=function(){var e,o=(e=xt().mark((function e(o){var r,a,s,u,d;return xt().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return o.preventDefault(),r=q.current.getEditor(),l=pt.a.getFormattedTextFromDeltas(r.getContents()),Object(ht.a)(r,t),l.length>1&&"\n"===l[l.length-1]&&(l=l.slice(0,l.length-1)),t.getSkipAutoLink&&t.getSkipAutoLink()&&t.disableSkipAutoLink(),m?(a=pt.a.extractMentionDataFromStr(l),s=a.plainTextValue,u=a.ids,pt.a.extractMentionDataFromAnnot(t).mentions.forEach((function(e){s.includes(e.value)&&u.push(e.id)})),t.setCustomData("trn-mention",JSON.stringify({contents:l,ids:u})),t.setContents(s)):t.setContents(l),e.next=11,Ve(t,R[t.Id]);case 11:d=t instanceof window.Core.Annotations.FreeTextAnnotation?"textChanged":"noteChanged",w.a.getAnnotationManager(_).trigger("annotationChanged",[[t],"modify",{source:d}]),t instanceof window.Core.Annotations.FreeTextAnnotation&&w.a.drawAnnotationsFromList([t]),i(!1,n),""!==l&&c(void 0,t.Id),A(t.Id);case 17:case"end":return e.stop()}}),e)})),function(){var t=this,n=arguments;return new Promise((function(o,r){var i=e.apply(t,n);function a(e){St(i,o,r,a,l,"next",e)}function l(e){St(i,o,r,a,l,"throw",e)}a(void 0)}))});return function(e){return o.apply(this,arguments)}}(),L=a()("edit-content",{"reply-content":E}),M=R[t.Id]||[];return r.a.createElement("div",{className:L},E&&M.length>0&&r.a.createElement(dt,{files:M,isEditing:!0,fileDeleted:function(e){return I(t.Id,e)}}),r.a.createElement(b.a,{ref:function(e){q.current=e},value:l,onChange:function(e){return c(e,t.Id)},onSubmit:N,isReply:E,onBlur:function(e){var t,n;null!==(t=e.relatedTarget)&&void 0!==t&&null!==(n=t.getAttribute("data-element"))&&void 0!==n&&n.includes("annotationCommentButton")?e.target.focus():T(void 0)},onFocus:function(){T(t.Id)}}),r.a.createElement("div",{className:"edit-buttons"},r.a.createElement(he.a,{className:"cancel-button",label:O("action.cancel"),onClick:function(e){e.stopPropagation(),i(!1,n),c(void 0,t.Id),A(t.Id)}}),r.a.createElement(he.a,{className:"save-button".concat(l?"":" disabled"),disabled:!l,label:O("action.save"),onClick:function(e){e.stopPropagation(),N(e)}})))};kt.propTypes={noteIndex:c.a.number.isRequired,annotation:c.a.object.isRequired,setIsEditing:c.a.func.isRequired,textAreaValue:c.a.string,onTextAreaValueChange:c.a.func.isRequired,pendingText:c.a.string};var Tt=function(e,t,n){var o={fontWeight:t["font-weight"],fontStyle:t["font-style"],textDecoration:t["text-decoration"],color:t.color};return o.textDecoration&&(o.textDecoration=o.textDecoration.replace("word","underline")),r.a.createElement("span",{style:o,key:n},e)},Rt=function(e,t,n){if(!t||!e)return e;for(var o={},r=Object.keys(t).map(Number).sort((function(e,t){return e-t})),i=0;i<r.length;i++){var a=r[i]-n;if(o[a=Math.min(Math.max(a,0),e.length)]=t[r[i]],a===e.length)break}for(var l=[],c=Object.keys(o).map(Number).sort((function(e,t){return e-t})),s=1;s<c.length;s++)l.push(Tt(e.slice(c[s-1],c[s]),o[c[s-1]],"richtext_span_".concat(s)));return l},It=function(e,t,n){var o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:e.length,a=e.slice(o,i),l=a.toLowerCase(),c=t.toLowerCase();n&&(n[0]=n[0]||{},n[e.length]=n[e.length]||{});var s=l.indexOf(c);if(!c.trim()||-1===s)return Rt(a,n,o);var u=[],d=[s],p=c.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");if(new RegExp("(".concat(p,")"),"gi").test(l))for(;-1!==s;)-1!==(s=l.indexOf(c,s+c.length))&&d.push(s);return d.forEach((function(e,t){0===t&&0!==e&&u.push(Rt(a.substring(0,e),n,o)),u.push(r.a.createElement("span",{className:"highlight",key:"highlight_span_".concat(t)},Rt(a.substring(e,e+c.length),n,o+e))),e+c.length<l.length&&e+c.length!==d[t+1]&&u.push(Rt(a.substring(e+c.length,d[t+1]),n,o+e+c.length))})),u},At=Et;n(1566);function jt(e){return(jt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Pt(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */Pt=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},r="function"==typeof Symbol?Symbol:{},i=r.iterator||"@@iterator",a=r.asyncIterator||"@@asyncIterator",l=r.toStringTag||"@@toStringTag";function c(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,n){return e[t]=n}}function s(e,t,n,r){var i=t&&t.prototype instanceof p?t:p,a=Object.create(i.prototype),l=new O(r||[]);return o(a,"_invoke",{value:x(e,n,l)}),a}function u(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=s;var d={};function p(){}function h(){}function f(){}var m={};c(m,i,(function(){return this}));var g=Object.getPrototypeOf,v=g&&g(g(q([])));v&&v!==t&&n.call(v,i)&&(m=v);var y=f.prototype=p.prototype=Object.create(m);function b(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function w(e,t){var r;o(this,"_invoke",{value:function(o,i){function a(){return new t((function(r,a){!function o(r,i,a,l){var c=u(e[r],e,i);if("throw"!==c.type){var s=c.arg,d=s.value;return d&&"object"==jt(d)&&n.call(d,"__await")?t.resolve(d.__await).then((function(e){o("next",e,a,l)}),(function(e){o("throw",e,a,l)})):t.resolve(d).then((function(e){s.value=e,a(s)}),(function(e){return o("throw",e,a,l)}))}l(c.arg)}(o,i,r,a)}))}return r=r?r.then(a,a):a()}})}function x(e,t,n){var o="suspendedStart";return function(r,i){if("executing"===o)throw new Error("Generator is already running");if("completed"===o){if("throw"===r)throw i;return E()}for(n.method=r,n.arg=i;;){var a=n.delegate;if(a){var l=S(a,n);if(l){if(l===d)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===o)throw o="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o="executing";var c=u(e,t,n);if("normal"===c.type){if(o=n.done?"completed":"suspendedYield",c.arg===d)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o="completed",n.method="throw",n.arg=c.arg)}}}function S(e,t){var n=t.method,o=e.iterator[n];if(void 0===o)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,S(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),d;var r=u(o,e.iterator,t.arg);if("throw"===r.type)return t.method="throw",t.arg=r.arg,t.delegate=null,d;var i=r.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,d):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,d)}function _(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function C(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function O(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(_,this),this.reset(!0)}function q(e){if(e){var t=e[i];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,r=function t(){for(;++o<e.length;)if(n.call(e,o))return t.value=e[o],t.done=!1,t;return t.value=void 0,t.done=!0,t};return r.next=r}}return{next:E}}function E(){return{value:void 0,done:!0}}return h.prototype=f,o(y,"constructor",{value:f,configurable:!0}),o(f,"constructor",{value:h,configurable:!0}),h.displayName=c(f,l,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===h||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,c(e,l,"GeneratorFunction")),e.prototype=Object.create(y),e},e.awrap=function(e){return{__await:e}},b(w.prototype),c(w.prototype,a,(function(){return this})),e.AsyncIterator=w,e.async=function(t,n,o,r,i){void 0===i&&(i=Promise);var a=new w(s(t,n,o,r),i);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},b(y),c(y,l,"Generator"),c(y,i,(function(){return this})),c(y,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var o in t)n.push(o);return n.reverse(),function e(){for(;n.length;){var o=n.pop();if(o in t)return e.value=o,e.done=!1,e}return e.done=!0,e}},e.values=q,O.prototype={constructor:O,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(C),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function o(n,o){return a.type="throw",a.arg=e,t.next=n,o&&(t.method="next",t.arg=void 0),!!o}for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r],a=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(l&&c){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(e,t){for(var o=this.tryEntries.length-1;o>=0;--o){var r=this.tryEntries[o];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,d):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),d},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),C(n),d}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var r=o.arg;C(n)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:q(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),d}},e}function Nt(e,t,n,o,r,i,a){try{var l=e[i](a),c=l.value}catch(e){return void n(e)}l.done?t(c):Promise.resolve(c).then(o,r)}function Lt(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,s=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){s=!0,r=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw r}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Mt(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Mt(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Mt(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}var zt={annotation:c.a.object.isRequired},Ht=function(e){var t=e.annotation,n=e.isUnread,i=e.onPendingReplyChange,l=Lt(Object(s.e)((function(e){var n;return[x.a.getAutoFocusNoteOnAnnotationSelection(e),x.a.isDocumentReadOnly(e),x.a.isElementDisabled(e,"noteReply"),null===(n=x.a.getIsReplyDisabled(e))||void 0===n?void 0:n(t),x.a.getIsMentionEnabled(e),x.a.getIsNoteEditing(e),x.a.isElementDisabled(e,S.a.INLINE_COMMENT_POPUP),x.a.isElementOpen(e,S.a.INLINE_COMMENT_POPUP),x.a.getActiveDocumentViewerKey(e)]}),s.c),9),c=l[0],u=l[1],p=l[2],h=l[3],f=l[4],m=l[5],g=l[6],v=l[7],y=l[8],C=Object(o.useContext)(d.a),O=C.isContentEditable,q=C.isSelected,E=C.pendingReplyMap,k=C.setPendingReply,T=C.isExpandedFromSearch,R=C.scrollToSelectedAnnot,I=C.setCurAnnotId,A=C.pendingAttachmentMap,j=C.clearAttachments,P=C.deleteAttachment,N=Lt(Object(o.useState)(!1),2),L=N[0],M=N[1],z=Object(s.d)(),H=Object(o.useRef)(),D=!g&&v&&Object(gt.k)();Object(yt.a)((function(){L||z(_.a.finishNoteEditing())}),[L]),Object(o.useEffect)((function(){D||m&&q&&!O&&c&&H&&H.current&&H.current.focus()}),[O,m,q,D]),Object(o.useEffect)((function(){if(!T&&R&&setTimeout((function(){H&&H.current&&c&&H.current.focus()}),100),H&&H.current){if(D)return;var e=H.current.getEditor().getLength()-1;setTimeout((function(){H.current&&H.current.editor.setSelection(e,e)}),100)}}),[]);var G=function(){var e,n=(e=Pt().mark((function e(n){var o,r,i,a;return Pt().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n.preventDefault(),n.stopPropagation(),o=H.current.getEditor(),(r=pt.a.getFormattedTextFromDeltas(o.getContents())).trim()){e.next=6;break}return e.abrupt("return");case 6:if(!f){e.next=14;break}return i=pt.a.createMentionReply(t,r),Object(ht.a)(o,i),e.next=11,Ve(i,A[t.Id]);case 11:w.a.addAnnotations([i],y),e.next=19;break;case 14:return a=w.a.createAnnotationReply(t,r),Object(ht.a)(o,a),e.next=18,Ve(a,A[t.Id]);case 18:w.a.getAnnotationManager(y).trigger("annotationChanged",[[a],"modify",{}]);case 19:k("",t.Id),j(t.Id);case 21:case"end":return e.stop()}}),e)})),function(){var t=this,n=arguments;return new Promise((function(o,r){var i=e.apply(t,n);function a(e){Nt(i,o,r,a,l,"next",e)}function l(e){Nt(i,o,r,a,l,"throw",e)}a(void 0)}))});return function(e){return n.apply(this,arguments)}}(),F=u||p||h,W=a()({"reply-area":!0,unread:n}),U=A[t.Id]||[];return F||!q?null:r.a.createElement("form",{onSubmit:G,className:"reply-area-container"},U.length>0&&r.a.createElement(dt,{files:U,isEditing:!0,fileDeleted:function(e){return P(t.Id,e)}}),r.a.createElement("div",{className:"reply-area-with-button"},r.a.createElement("div",{className:W,onMouseDown:function(e){return e.stopPropagation()}},r.a.createElement(b.a,{ref:function(e){H.current=e},value:E[t.Id],onChange:function(e){return function(e){k(e,t.Id),i&&i()}(e)},onSubmit:G,onBlur:function(){M(!1),I(void 0)},onFocus:function(){M(!0),I(t.Id)},isReply:!0})),r.a.createElement("div",{className:"reply-button-container"},r.a.createElement(he.a,{img:"icon-post-reply",className:"reply-button",title:"action.submit",disabled:!E[t.Id],onClick:G,isSubmitType:!0}))))};Ht.propTypes=zt;var Dt=Ht;function Gt(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,s=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){s=!0,r=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw r}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Ft(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Ft(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ft(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}var Wt={groupAnnotations:c.a.array.isRequired,isMultiSelectMode:c.a.bool.isRequired},Ut=function(e){var t=e.groupAnnotations,n=e.isMultiSelectMode,i=Gt(Object(u.a)(),1)[0],l=Object(s.d)(),c=Gt(Object(o.useState)(!1),2),d=c[0],p=c[1],h=Object(s.e)((function(e){var t;return null===(t=x.a.getFeatureFlags(e))||void 0===t?void 0:t.customizableUI})),f=r.a.createElement(he.a,{onClick:function(e){e.preventDefault(),e.stopPropagation(),p(!0)},className:"text-button",ariaLabel:i("component.noteGroupSection.open"),label:i("component.noteGroupSection.open"),img:"ic_chevron_down_black_24px"}),m=r.a.createElement(he.a,{onClick:function(e){e.preventDefault(),e.stopPropagation(),p(!1)},className:"text-button",ariaLabel:i("component.noteGroupSection.close"),label:i("component.noteGroupSection.close"),img:"ic_chevron_up_black_24px"});return r.a.createElement("div",{className:a()({"group-section":!0,"modular-ui":h})},d?m:f,d&&t.map((function(e,t){return 0===t?null:r.a.createElement(he.a,{key:e.Id,className:"group-child",onClick:function(t){t.preventDefault(),t.stopPropagation(),w.a.selectAnnotation(e),w.a.jumpToAnnotation(e),l(_.a.openElement("annotationPopup"))}},r.a.createElement(At,{key:e.Id,annotation:e,isUnread:!1,isGroupMember:!0,isMultiSelectMode:n}))})))};Ut.propTypes=Wt;var Bt=Ut,Vt=n(116),Yt=n(284),Kt=n(21);n(1568);function $t(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,s=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){s=!0,r=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw r}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Xt(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Xt(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Xt(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}var Zt=function(e){var t=e.children,n=Object(Kt.a)().querySelector("#line-connector-root"),r=document.createElement("div");return r.setAttribute("data-element",S.a.ANNOTATION_NOTE_CONNECTOR_LINE),Object(o.useEffect)((function(){return n.appendChild(r),function(){return n.removeChild(r)}}),[r,n]),Object(Vt.createPortal)(t,r)},Jt=function(e){var t=e.annotation,n=e.noteContainerRef,i=e.isCustomPanelOpen,a=$t(Object(s.e)((function(e){return[x.a.getNotesPanelWidth(e),x.a.isElementOpen(e,S.a.ANNOTATION_NOTE_CONNECTOR_LINE),x.a.isElementOpen(e,S.a.NOTES_PANEL),x.a.isElementDisabled(e,S.a.ANNOTATION_NOTE_CONNECTOR_LINE),x.a.getDocumentContainerWidth(e),x.a.getDocumentContainerHeight(e),x.a.getActiveDocumentViewerKey(e)]}),s.c),7),l=a[0],c=a[1],u=a[2],d=a[3],p=a[4],h=a[5],f=a[6],m=Object(s.d)(),g=$t(Object(o.useState)(0),2),v=g[0],y=g[1],b=$t(Object(o.useState)(0),2),C=b[0],O=b[1],q=$t(Object(o.useState)(0),2),E=q[0],k=q[1],T=$t(Object(o.useState)(0),2),R=T[0],I=T[1],A=$t(Object(o.useState)(0),2),j=A[0],P=A[1],N=$t(Object(o.useState)(0),2),L=N[0],M=N[1],z=Object(Yt.d)(t,f),H=z.bottomRight,D=z.topLeft,G=Object(o.useCallback)((function(){return"Note"===t.Subject?4:15}),[t]);if(Object(o.useEffect)((function(){var e=w.a.getScrollViewElement(f),o=e.scrollTop,r=e.scrollLeft;if(!(H&&D))return function(){m(_.a.closeElement(S.a.ANNOTATION_NOTE_CONNECTOR_LINE))};var i=H.x-D.x,a=H.y-D.y,c=window.isApryseWebViewerWebComponent?Object(Kt.a)().host.clientWidth:window.innerWidth,s=window.isApryseWebViewerWebComponent?Object(Kt.a)().host.offsetTop:0;k(l-16),O(n.current.getBoundingClientRect().top-s);var u=c-l-D.x+16+r-i;y(.75*u);var d=t.getNoZoomReferencePoint(),p=t.NoZoom&&d.x?d.x*a:0;I(u-v-G()+p),M(l-16+v);var h=t.NoZoom&&d.y?d.y*a:0;P(D.y+a/2-o-h);var g=function(){m(_.a.closeElement(S.a.ANNOTATION_NOTE_CONNECTOR_LINE))};return w.a.addEventListener("pageNumberUpdated",g,void 0,f),function(){w.a.removeEventListener("pageNumberUpdated",g,f)}}),[n,l,H,D,p,h,m,f]),c&&(u||i)&&!d){var F=Math.abs(C-j),W=C>j?j+2:C;return r.a.createElement(Zt,null,r.a.createElement("div",{className:"horizontalLine",style:{width:v,right:E,top:C}}),r.a.createElement("div",{className:"verticalLine",style:{height:F,top:W,right:E+v}}),r.a.createElement("div",{className:"horizontalLine",style:{width:R,right:L,top:j}},r.a.createElement("div",{className:"arrowHead"})))}return null};n(1570);function Qt(e){return(Qt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function en(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function tn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?en(Object(n),!0).forEach((function(t){nn(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):en(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function nn(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Qt(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!==Qt(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Qt(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function on(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */on=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},r="function"==typeof Symbol?Symbol:{},i=r.iterator||"@@iterator",a=r.asyncIterator||"@@asyncIterator",l=r.toStringTag||"@@toStringTag";function c(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,n){return e[t]=n}}function s(e,t,n,r){var i=t&&t.prototype instanceof p?t:p,a=Object.create(i.prototype),l=new O(r||[]);return o(a,"_invoke",{value:x(e,n,l)}),a}function u(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=s;var d={};function p(){}function h(){}function f(){}var m={};c(m,i,(function(){return this}));var g=Object.getPrototypeOf,v=g&&g(g(q([])));v&&v!==t&&n.call(v,i)&&(m=v);var y=f.prototype=p.prototype=Object.create(m);function b(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function w(e,t){var r;o(this,"_invoke",{value:function(o,i){function a(){return new t((function(r,a){!function o(r,i,a,l){var c=u(e[r],e,i);if("throw"!==c.type){var s=c.arg,d=s.value;return d&&"object"==Qt(d)&&n.call(d,"__await")?t.resolve(d.__await).then((function(e){o("next",e,a,l)}),(function(e){o("throw",e,a,l)})):t.resolve(d).then((function(e){s.value=e,a(s)}),(function(e){return o("throw",e,a,l)}))}l(c.arg)}(o,i,r,a)}))}return r=r?r.then(a,a):a()}})}function x(e,t,n){var o="suspendedStart";return function(r,i){if("executing"===o)throw new Error("Generator is already running");if("completed"===o){if("throw"===r)throw i;return E()}for(n.method=r,n.arg=i;;){var a=n.delegate;if(a){var l=S(a,n);if(l){if(l===d)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===o)throw o="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o="executing";var c=u(e,t,n);if("normal"===c.type){if(o=n.done?"completed":"suspendedYield",c.arg===d)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o="completed",n.method="throw",n.arg=c.arg)}}}function S(e,t){var n=t.method,o=e.iterator[n];if(void 0===o)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,S(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),d;var r=u(o,e.iterator,t.arg);if("throw"===r.type)return t.method="throw",t.arg=r.arg,t.delegate=null,d;var i=r.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,d):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,d)}function _(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function C(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function O(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(_,this),this.reset(!0)}function q(e){if(e){var t=e[i];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,r=function t(){for(;++o<e.length;)if(n.call(e,o))return t.value=e[o],t.done=!1,t;return t.value=void 0,t.done=!0,t};return r.next=r}}return{next:E}}function E(){return{value:void 0,done:!0}}return h.prototype=f,o(y,"constructor",{value:f,configurable:!0}),o(f,"constructor",{value:h,configurable:!0}),h.displayName=c(f,l,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===h||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,c(e,l,"GeneratorFunction")),e.prototype=Object.create(y),e},e.awrap=function(e){return{__await:e}},b(w.prototype),c(w.prototype,a,(function(){return this})),e.AsyncIterator=w,e.async=function(t,n,o,r,i){void 0===i&&(i=Promise);var a=new w(s(t,n,o,r),i);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},b(y),c(y,l,"Generator"),c(y,i,(function(){return this})),c(y,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var o in t)n.push(o);return n.reverse(),function e(){for(;n.length;){var o=n.pop();if(o in t)return e.value=o,e.done=!1,e}return e.done=!0,e}},e.values=q,O.prototype={constructor:O,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(C),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function o(n,o){return a.type="throw",a.arg=e,t.next=n,o&&(t.method="next",t.arg=void 0),!!o}for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r],a=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(l&&c){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(e,t){for(var o=this.tryEntries.length-1;o>=0;--o){var r=this.tryEntries[o];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,d):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),d},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),C(n),d}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var r=o.arg;C(n)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:q(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),d}},e}function rn(e,t,n,o,r,i,a){try{var l=e[i](a),c=l.value}catch(e){return void n(e)}l.done?t(c):Promise.resolve(c).then(o,r)}function an(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,s=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){s=!0,r=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw r}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return ln(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ln(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ln(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}var cn={annotation:c.a.object.isRequired,isMultiSelected:c.a.bool,isMultiSelectMode:c.a.bool,isInNotesPanel:c.a.bool,handleMultiSelect:c.a.func},sn=0,un=function(e){var t=e.annotation,n=e.isMultiSelected,i=e.isMultiSelectMode,l=e.isInNotesPanel,c=e.handleMultiSelect,p=e.isCustomPanelOpen,h=e.shouldHideConnectorLine,f=Object(o.useContext)(d.a),m=f.isSelected,g=f.resize,v=f.pendingEditTextMap,y=f.isContentEditable,b=f.isDocumentReadOnly,C=f.isExpandedFromSearch,O=f.setCurAnnotId,q=Object(o.useRef)(),E=Object(o.useRef)(),k=an(Object(o.useState)({}),2),T=k[0],R=k[1],I=Object(o.useRef)([]),A=Object(s.d)(),j=an(Object(u.a)(),1)[0],P=new Set,N=an(Object(s.e)((function(e){return[x.a.getNoteTransformFunction(e),x.a.getCustomNoteSelectionFunction(e),x.a.getUnreadAnnotationIdSet(e),x.a.isCommentThreadExpansionEnabled(e),x.a.isRightClickAnnotationPopupEnabled(e),x.a.getActiveDocumentViewerKey(e),x.a.getIsOfficeEditorMode(e),x.a.getOfficeEditorEditMode(e)]}),s.c),8),L=N[0],M=N[1],z=N[2],H=N[3],D=N[4],G=N[5],F=N[6],W=N[7],U=t.getReplies().sort((function(e,t){return e.DateCreated-t.DateCreated}));U.filter((function(e){return z.has(e.Id)})).forEach((function(e){return P.add(e.Id)})),Object(o.useEffect)((function(){var e=function(e,t){"delete"===t&&e.forEach((function(e){z.has(e.Id)&&A(_.a.setAnnotationReadState({isRead:!0,annotationId:e.Id}))}))};return w.a.addEventListener("annotationChanged",e,void 0,G),function(){w.a.removeEventListener("annotationChanged",e,G)}}),[z]),Object(o.useEffect)((function(){var e=E.current,t=q.current.getBoundingClientRect().height;E.current=t,e&&Math.round(e)!==Math.round(t)&&g()})),Object(o.useEffect)((function(){if(L){var e=Object(Kt.a)().querySelectorAll(".NotesPanel")[0];I.current.forEach((function(t){var n=e.querySelector("[data-webviewer-custom-element=".concat(t,"]"));n&&n.parentNode.removeChild(n)})),I.current=[];var n={annotation:t,isSelected:m};L(q.current,n,(function(){var e,t=(e=document).createElement.apply(e,arguments),n="custom-element-".concat(sn);return sn++,I.current.push(n),t.setAttribute("data-webviewer-custom-element",n),t.addEventListener("mousedown",(function(e){e.stopPropagation()})),t}))}})),Object(o.useEffect)((function(){""!==v[t.Id]&&y&&!b&&J(!0,0)}),[b,y,J,t,i]),Object(yt.a)((function(){!b&&y||J(!1,0)}),[b,y,J]);var B=function(){var e,o=(e=on().mark((function e(o){var r;return on().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(o&&o.stopPropagation(),!i){e.next=4;break}return c(!n),e.abrupt("return");case 4:if(z.has(t.Id)&&A(_.a.setAnnotationReadState({isRead:!0,annotationId:t.Id})),M&&M(t),m||(w.a.deselectAllAnnotations(G),setTimeout((function(){return A(_.a.openElement(S.a.ANNOTATION_NOTE_CONNECTOR_LINE))}),300)),!l||F&&W===be.p.PREVIEW){e.next=17;break}if(w.a.selectAnnotation(t,G),O(t.Id),w.a.jumpToAnnotation(t,G),D||A(_.a.openElement(S.a.ANNOTATION_POPUP)),!F){e.next=17;break}return r=t.getCustomData(be.m),e.next=16,w.a.getOfficeEditor().moveCursorToTrackedChange(r);case 16:w.a.getOfficeEditor().freezeMainCursor();case 17:case"end":return e.stop()}}),e)})),function(){var t=this,n=arguments;return new Promise((function(o,r){var i=e.apply(t,n);function a(e){rn(i,o,r,a,l,"next",e)}function l(e){rn(i,o,r,a,l,"throw",e)}a(void 0)}))});return function(e){return o.apply(this,arguments)}}(),V=P.size>0,Y=a()({Note:!0,expanded:m,"is-multi-selected":n,unread:z.has(t.Id)||V,disabled:F&&W===be.p.PREVIEW}),K=a()({replies:!0,hidden:!m});Object(o.useEffect)((function(){i||U.forEach((function(e,t){var n=v[e.Id];""!==n&&void 0!==n&&m&&J(!0,1+t)}))}),[m,i]),Object(o.useEffect)((function(){i&&J(!1,0)}),[i]);var $=!Object.values(T).some((function(e){return e})),X=function(e){P.has(e.Id)&&(A(_.a.setAnnotationReadState({isRead:!0,annotationId:e.Id})),w.a.getAnnotationManager(G).selectAnnotation(e))},Z=function(){if(P.size>0){var e=U.filter((function(e){return P.has(e.Id)}));w.a.getAnnotationManager(G).selectAnnotations(e),e.forEach((function(e){return A(_.a.setAnnotationReadState({isRead:!0,annotationId:e.Id}))}))}},J=Object(o.useCallback)((function(e,t){R((function(n){return tn(tn({},n),{},nn({},t,e))}))}),[R]),Q=w.a.getGroupAnnotations(t,G),ee=Q.length>1,te=Object(vt.g)(t)===vt.c.TRACKED_CHANGE,ne=U.length>0?U[U.length-1].Id:null;return r.a.createElement("div",{ref:q,className:Y,id:"note_".concat(t.Id)},r.a.createElement(he.a,{className:"note-button",onClick:function(e){return B(e)},ariaLabelledby:"note_".concat(t.Id),ariaCurrent:m,dataElement:"expandNoteButton"}),r.a.createElement(At,{noteIndex:0,annotation:t,setIsEditing:J,isEditing:T[0],isNonReplyNoteRead:!z.has(t.Id),isUnread:z.has(t.Id)||V,handleMultiSelect:function(e){O(t.Id),c(e)},isMultiSelected:n,isMultiSelectMode:i}),(m||C||H)&&!te&&r.a.createElement(r.a.Fragment,null,U.length>0&&r.a.createElement("div",{className:K},V&&r.a.createElement(he.a,{dataElement:"markAllReadButton",className:"mark-all-read-button",label:j("action.markAllRead"),onClick:Z}),U.map((function(e,t){return r.a.createElement("div",{className:"reply",id:"note_reply_".concat(e.Id),key:"note_reply_".concat(e.Id)},r.a.createElement(At,{noteIndex:t+1,key:e.Id,annotation:e,setIsEditing:J,isEditing:T[t+1],onReplyClicked:X,isUnread:z.has(e.Id),handleMultiSelect:c,isMultiSelected:n,isMultiSelectMode:i}))}))),ee&&r.a.createElement(Bt,{groupAnnotations:Q,isMultiSelectMode:i}),$&&!i&&r.a.createElement(Dt,{isUnread:ne&&z.has(ne),onPendingReplyChange:Z,annotation:t})),m&&(l||p)&&!h&&r.a.createElement(Jt,{annotation:t,noteContainerRef:q,isCustomPanelOpen:p}))};un.propTypes=cn;var dn=un;t.a=dn},1583:function(e,t,n){"use strict";n(23),n(8),n(24),n(19),n(11),n(13),n(14),n(10),n(9),n(12),n(16),n(15),n(20),n(18),n(26),n(27),n(25),n(22);var o=n(0),r=n.n(o),i=n(4),a=n.n(i),l=n(6),c=n(191),s=n(428),u=n(293),d=n(84),p=n(1),h=n(1505),f=n(193),m=n(37),g=n(1498),v=n(2),y=n(3),b=n(5),w=n(1521);n(1548);function x(e){return(x="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function S(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==x(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!==x(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===x(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,s=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){s=!0,r=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw r}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return C(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return C(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function C(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}var O={annotations:a.a.array.isRequired,style:a.a.object.isRequired,properties:a.a.object.isRequired,isRedaction:a.a.bool,isFreeText:a.a.bool,isEllipse:a.a.bool,hasBackToMenu:a.a.bool,onBackToMenu:a.a.func},q=function(e){var t=e.annotations,n=e.style,i=e.isRedaction,a=e.isFreeText,x=e.isEllipse,C=e.isMeasure,O=e.colorMapKey,q=e.showLineStyleOptions,E=e.properties,k=e.hideSnapModeCheckbox,T=e.onResize,R=e.hasBackToMenu,I=e.onBackToMenu,A=_(Object(l.e)((function(e){return[y.a.isElementDisabled(e,b.a.ANNOTATION_STYLE_POPUP),y.a.isToolDefaultStyleUpdateFromAnnotationPopupEnabled(e),y.a.getActiveDocumentViewerKey(e)]})),3),j=A[0],P=A[1],N=A[2],L=Object(l.d)(),M=_(Object(s.a)(),1)[0],z=_(Object(o.useState)(E.isAutoSizeFont),2),H=z[0],D=z[1],G=function(e,n){var o=p.a.getAnnotationManager(N);t.forEach((function(t){t[e]=n,"StrokeThickness"===e&&Object(g.a)(t),o.redrawAnnotation(t),t instanceof window.Core.Annotations.WidgetAnnotation&&t.refresh()}))},F=function(e,n){t.forEach((function(t){p.a.setAnnotationStyles(t,S({},e,n),N),P&&Object(f.a)(t.ToolName,e,n),"FontSize"!==e&&"Font"!==e||Object(g.a)(t)}))},W=function(e,n){t.forEach((function(t){p.a.setAnnotationStyles(t,S({},e,n),N),P&&Object(f.a)(t.ToolName,e,n)}))},U=function(e,n){t.forEach((function(t){p.a.updateAnnotationRichTextStyle(t,S({},e,n),N)}))},B=function(e,n){t.forEach((function(t){var o="";if("start"===e)t.setStartStyle(n),o="StartLineStyle";else if("end"===e)t.setEndStyle(n),o="EndLineStyle";else if("middle"===e){var r=n.split(","),i=r.shift();t.Style=i,t.Dashes=r,o="StrokeStyle"}P&&Object(f.a)(t.ToolName,o,n),p.a.getAnnotationManager(N).redrawAnnotation(t)})),p.a.getAnnotationManager(N).trigger("annotationChanged",[t,"modify",{}])},V=function(e){Object(m.k)()&&e.target===e.currentTarget&&L(v.a.closeElement(b.a.ANNOTATION_POPUP))},Y=Object(h.a)("Popup AnnotationStylePopup",e);return j?null:r.a.createElement(c.a,{onResize:function(){T&&T()}},(function(e){var o=e.measureRef;return r.a.createElement("div",{className:Y,"data-element":b.a.ANNOTATION_STYLE_POPUP,onClick:V,ref:o},R&&r.a.createElement("div",{className:"back-to-menu-container","data-element":b.a.ANNOTATION_STYLE_POPUP_BACK_BUTTON_CONTAINER},r.a.createElement(d.a,{className:"back-to-menu-button",dataElement:b.a.ANNOTATION_STYLE_POPUP_BACK_BUTTON,label:M("action.backToMenu"),img:"icon-chevron-left",onClick:I})),r.a.createElement(u.a,{hideSnapModeCheckbox:k,colorMapKey:O,style:n,isFreeText:a,isFreeTextAutoSize:H,onFreeTextSizeToggle:function(){return Object(w.a)(t[0],D,H)},isEllipse:x,isMeasure:C,onStyleChange:W,onSliderChange:G,onPropertyChange:F,disableSeparator:!0,properties:E,onRichTextStyleChange:U,isRedaction:i,showLineStyleOptions:q,onLineStyleChange:B}))}))};q.propTypes=O;var E=q;t.a=E},1628:function(e,t,n){var o=n(32),r=n(1629);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.i,r,""]]);var i={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach(e=>o.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))}),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const r=t[o];if(0===o)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};o(r,i);e.exports=r.locals||{}},1629:function(e,t,n){(t=e.exports=n(33)(!1)).push([e.i,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.multi-reply-place-holder{height:100px}.reply-area-multi-container{border-top:1px solid var(--divider);display:flex;flex-direction:column;width:100%;position:absolute;bottom:0;z-index:100;background:var(--panel-background)}.reply-area-multi-container form{width:100%;display:flex}.reply-area-multi-container .reply-area-multi-header{display:flex;flex-direction:row;margin-left:16px;margin-top:28px;position:relative;justify-content:space-between}.reply-area-multi-container .reply-area-multi-header .title{font-size:13px;font-weight:700;display:flex;align-items:center}.reply-area-multi-container .reply-area-multi-header .close-icon{display:flex;align-items:center;padding-right:12px;cursor:pointer;position:relative;top:1px}.reply-area-multi-container .reply-area-multi-header .close-icon .close-icon{width:22px;height:22px}.reply-area-multi-container .reply-button-container{display:flex;justify-content:flex-end;flex-direction:column}.reply-area-multi-container .reply-button{width:28px;height:28px;padding:0;border:none;background-color:transparent;right:10px;bottom:12px}:host(:not([data-tabbing=true])) .reply-area-multi-container .reply-button,html:not([data-tabbing=true]) .reply-area-multi-container .reply-button{outline:none}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.reply-area-multi-container .reply-button{width:80px}}.reply-area-multi-container .reply-button:hover{background:var(--blue-1)}.reply-area-multi-container .reply-button.disabled{cursor:not-allowed}.reply-area-multi-container .reply-area{position:relative;flex:1;margin:13px 17px 12px 16px;border-radius:4px;align-items:center;background:var(--component-background)}.reply-area-multi-container .reply-area.unread{background:rgba(0,165,228,.08)}.reply-area-multi-container .reply-area .comment-textarea .ql-container .ql-editor.ql-blank{padding:4px}.reply-area-multi-container .reply-area .comment-textarea .ql-container .ql-editor.ql-blank:before{left:4px}.reply-area-multi-container .add-attachment{display:none}.reply-area-multi-container.modular-ui{padding:16px 16px 24px}.reply-area-multi-container.modular-ui form{margin-bottom:0}.reply-area-multi-container.modular-ui .reply-area{margin:13px 16px 24px 0}.reply-area-multi-container.modular-ui .reply-area-multi-header{margin-left:0;margin-top:0}.reply-area-multi-container.modular-ui .reply-area-multi-header .close-icon{width:28px;height:28px}.reply-area-multi-container.modular-ui .close-icon{padding-right:0;margin-right:16px}.reply-area-multi-container.modular-ui .reply-button-container{margin-right:16px;margin-bottom:12px}.reply-area-multi-container.modular-ui .reply-button{left:0;right:0}",""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1630:function(e,t,n){var o=n(32),r=n(1631);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.i,r,""]]);var i={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach(e=>o.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))}),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const r=t[o];if(0===o)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};o(r,i);e.exports=r.locals||{}},1631:function(e,t,n){(e.exports=n(33)(!1)).push([e.i,".multi-style-container{border-radius:4px;box-shadow:0 0 3px 0 var(--document-box-shadow);background:var(--component-background);position:relative;right:20px;bottom:52px;pointer-events:auto}.multi-style-container *{box-sizing:border-box}",""])},1632:function(e,t,n){var o=n(32),r=n(1633);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.i,r,""]]);var i={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach(e=>o.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))}),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const r=t[o];if(0===o)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};o(r,i);e.exports=r.locals||{}},1633:function(e,t,n){(e.exports=n(33)(!1)).push([e.i,".notes-panel-container .buttons-container{display:flex;grid-column-gap:8px;-moz-column-gap:8px;column-gap:8px;justify-content:space-between}.notes-panel-container .buttons-container .Button{height:28px;width:28px}.notes-panel-container .buttons-container .Button:hover:enabled{background:var(--view-header-button-hover)}.notes-panel-container .buttons-container .Button.active{background:var(--view-header-button-active)}.notes-panel-container .buttons-container .Button.active .Icon{color:var(--view-header-icon-active-fill)}.notes-panel-container .multi-select-footer{background:var(--panel-background);display:flex;border-top:1px solid var(--gray-5);margin-top:auto;margin-bottom:16px;height:64px;padding:24px 16px;align-items:center;justify-content:center;position:absolute;bottom:0;width:100%;z-index:100}.notes-panel-container .multi-select-footer .close-container{margin-top:auto;right:26px;position:absolute}",""])},1634:function(e,t,n){var o=n(32),r=n(1635);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.i,r,""]]);var i={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach(e=>o.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))}),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const r=t[o];if(0===o)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};o(r,i);e.exports=r.locals||{}},1635:function(e,t,n){(t=e.exports=n(33)(!1)).push([e.i,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.comments-counter{height:19px;margin-top:24px;margin-bottom:12px;font-size:16px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .comments-counter{margin-top:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .comments-counter{margin-top:16px}}.comments-counter span{font-weight:700}.comments-counter h2{font-weight:400;font-size:16px}[data-element=notesPanelHeader]{padding-right:18px}[data-element=notesPanelHeader] .buttons-container{display:flex;grid-column-gap:8px;-moz-column-gap:8px;column-gap:8px;justify-content:space-between}[data-element=notesPanelHeader] .buttons-container .Button{height:28px;width:28px;cursor:pointer}[data-element=notesPanelHeader] .buttons-container .Button:hover:enabled{background:var(--view-header-button-hover)}[data-element=notesPanelHeader] .buttons-container .Button.active{background:var(--view-header-button-active)}[data-element=notesPanelHeader] .buttons-container .Button.active .Icon{color:var(--view-header-icon-active-fill)}[data-element=notesPanelHeader] .buttons-container .Button:disabled{cursor:default}[data-element=notesPanelHeader] .sort-row{display:flex;justify-content:space-between;margin-bottom:16px}[data-element=notesPanelHeader] .sort-row .sort-container{display:flex;flex-direction:row;align-items:center;justify-content:flex-end;align-self:flex-end}[data-element=notesPanelHeader] .sort-row .sort-container .label{margin-right:8px}[data-element=notesPanelHeader] .sort-row .sort-container .picked-option{text-align:left}[data-element=notesPanelHeader] .input-container{display:flex;position:relative;flex:1}[data-element=notesPanelHeader] .input-container input{width:100%;border:1px solid var(--border);border-radius:4px;color:var(--text-color);padding:4px 8px 6px}[data-element=notesPanelHeader] .input-container.modular-ui-input{box-sizing:border-box;border:1px solid var(--border);border-radius:4px;height:28px;align-items:center;color:var(--text-color);padding:6px 2px 6px 6px}[data-element=notesPanelHeader] .input-container.modular-ui-input[focus-within]{outline:none;border:1px solid var(--focus-border)}[data-element=notesPanelHeader] .input-container.modular-ui-input:focus-within{outline:none;border:1px solid var(--focus-border)}[data-element=notesPanelHeader] .input-container.modular-ui-input input{padding-left:8px;padding-right:26px;height:20px;border:none;background:transparent}[data-element=notesPanelHeader] .input-button{cursor:pointer;background:var(--primary-button);border-radius:4px;height:100%;width:40px;display:flex;align-items:center;justify-content:center;position:absolute;bottom:0;right:0}[data-element=notesPanelHeader] .input-button .Icon{width:20px;height:20px}[data-element=notesPanelHeader] .input-button svg{color:var(--primary-button-text)}[data-element=notesPanelHeader] .input-button:hover{background:var(--primary-button-hover)}[data-element=notesPanelHeader] .divider{height:1px;width:100%;background:var(--divider);margin:16px 0}.modular-ui-header .input-container.modular-ui-input .Icon{width:16px;height:16px}",""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1636:function(e,t,n){var o=n(32),r=n(1637);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.i,r,""]]);var i={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach(e=>o.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))}),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const r=t[o];if(0===o)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};o(r,i);e.exports=r.locals||{}},1637:function(e,t,n){(t=e.exports=n(33)(!1)).push([e.i,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.notes-panel-container{z-index:65;display:flex;flex-direction:row;position:relative;overflow-y:visible}.notes-panel-container.office-editor{height:100%}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .notes-panel-container{z-index:95}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .notes-panel-container{z-index:95}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .notes-panel-container{border-left:1px solid var(--side-panel-border)}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .notes-panel-container{border-left:1px solid var(--side-panel-border)}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .notes-panel-container{top:0;right:0;height:100%;width:100%}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .notes-panel-container{top:0;right:0;height:100%;width:100%}}.notes-panel-container .NotesPanel{width:100%;padding-left:16px;padding-bottom:0;display:flex;flex-direction:column;position:relative;padding-top:16px}.notes-panel-container .NotesPanel .close-container{display:flex;align-items:center;justify-content:flex-end}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .notes-panel-container .NotesPanel .close-container{height:28px;padding-right:16px;margin-bottom:8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .notes-panel-container .NotesPanel .close-container{height:28px;padding-right:16px;margin-bottom:8px}}.notes-panel-container .NotesPanel .close-container .close-icon-container{display:flex;align-items:center;cursor:pointer}.notes-panel-container .NotesPanel .close-container .close-icon-container .close-icon{width:24px;height:24px}.notes-panel-container .NotesPanel .multi-select-place-holder{height:72px}.notes-panel-container .NotesPanel .preview-all-changes{height:57px;display:flex;flex-direction:column;justify-content:center;position:relative;margin-right:16px}.notes-panel-container .NotesPanel .preview-all-changes .divider{height:1px;width:100%;background:var(--divider);position:absolute;top:0}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.notes-panel-container .NotesPanel .reply-area-container .reply-button{width:28px}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .notes-panel-container .NotesPanel{width:100%;min-width:100%;padding-top:0}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .notes-panel-container .NotesPanel .normal-notes-container{padding-bottom:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .notes-panel-container .NotesPanel{width:100%;min-width:100%;padding-top:0}.App.is-web-component:not(.is-in-desktop-only-mode) .notes-panel-container .NotesPanel .normal-notes-container{padding-bottom:16px}}.notes-panel-container .NotesPanel .no-annotations{display:flex;flex-direction:column;align-items:center}.notes-panel-container .NotesPanel .no-annotations .msg{text-align:center}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .notes-panel-container .NotesPanel .no-annotations .msg{line-height:15px;width:146px}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .notes-panel-container .NotesPanel .no-annotations .msg{line-height:15px;width:146px}}.notes-panel-container .NotesPanel .no-annotations .empty-icon,.notes-panel-container .NotesPanel .no-annotations .empty-icon svg{width:65px;height:83px}.notes-panel-container .NotesPanel .no-annotations .empty-icon *{fill:var(--gray-6);color:var(--gray-6)}.notes-panel-container .NotesPanel .normal-notes-container,.notes-panel-container .NotesPanel .virtualized-notes-container{margin-top:10px;flex:1;padding-right:18px}.notes-panel-container .NotesPanel .virtualized-notes-container{overflow:hidden}.notes-panel-container .NotesPanel .normal-notes-container{overflow:auto;overflow:overlay}.notes-panel-container .NotesPanel .note-wrapper:first-child .ListSeparator{margin-top:0;word-break:break-word}.notes-panel-container .NotesPanel .no-results{display:flex;flex-direction:column;align-items:center;padding-right:18px}.notes-panel-container .NotesPanel .no-results .msg{text-align:center}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .notes-panel-container .NotesPanel .no-results .msg{line-height:15px;width:92px}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .notes-panel-container .NotesPanel .no-results .msg{line-height:15px;width:92px}}.notes-panel-container .NotesPanel .no-results .empty-icon,.notes-panel-container .NotesPanel .no-results .empty-icon svg{width:65px;height:83px}.notes-panel-container .NotesPanel .no-results .empty-icon *{fill:var(--border);color:var(--border)}",""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1660:function(e,t,n){"use strict";n.r(t);n(28),n(8),n(23),n(24),n(90),n(19),n(11),n(13),n(14),n(10),n(9),n(12),n(16),n(15),n(20),n(18),n(26),n(27),n(25),n(22),n(29),n(45),n(48),n(46),n(59),n(47);var o=n(0),r=n.n(o),i=n(1),a=(n(49),n(53),n(35),n(99),n(88),n(78),n(124),n(98),n(121),n(17)),l=n.n(a),c=n(6),s=n(428),u=n(4),d=n.n(u),p=n(191),h=n(1536);function f(e){return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function m(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function g(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?m(Object(n),!0).forEach((function(t){v(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):m(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function v(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==f(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!==f(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===f(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function y(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,s=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){s=!0,r=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw r}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return b(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return b(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function b(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}var w={notes:d.a.array.isRequired,children:d.a.func.isRequired,onScroll:d.a.func.isRequired,initialScrollTop:d.a.number.isRequired,selectedIndex:d.a.number,sortStrategy:d.a.string},x=new h.b({defaultHeight:50,fixedWidth:!0}),S=r.a.forwardRef((function(e,t){var n=e.notes,i=e.children,a=e.onScroll,l=e.initialScrollTop,c=e.selectedIndex,s=e.sortStrategy,u=Object(o.useRef)(),d=y(Object(o.useState)(0),2),f=d[0],m=d[1],v=y(Object(o.useState)({width:0,height:0}),2),b=v[0],w=v[1],S=window.innerHeight;Object(o.useImperativeHandle)(t,(function(){return{scrollToPosition:function(e){u.current.scrollToPosition(e)},scrollToRow:function(e){u.current.scrollToRow(e)}}})),Object(o.useEffect)((function(){u.current.scrollToPosition(l)}),[l]),Object(o.useEffect)((function(){var e,t;(x.clearAll(),null==u||null===(e=u.current)||void 0===e||e.measureAllRows(),-1!==c)&&(null===(t=u.current)||void 0===t||t.scrollToRow(c))}),[c]),Object(o.useEffect)((function(){var e,t;x.clearAll(),null==u||null===(e=u.current)||void 0===e||e.measureAllRows(),null==u||null===(t=u.current)||void 0===t||t.forceUpdateGrid()}),[n.length,s]),Object(o.useEffect)((function(){var e=function(){var e=window.innerHeight-S;e&&(window.innerHeight<S&&m(e),S=window.innerHeight)};return window.addEventListener("resize",e),function(){window.removeEventListener("resize",e)}}));var _=function(e){var t=e.scrollTop;a(t)},C=function(e){var t=e.index,o=e.key,a=e.parent,l=e.style,c=n[t];return r.a.createElement(h.a,{key:"".concat(o).concat(c.Id),cache:x,columnIndex:0,parent:a,rowIndex:t},(function(e){var o=e.measure;return r.a.createElement("div",{style:g(g({},l),{},{paddingRight:"12px"})},i(n,t,(function(){!function(e){var t;x.clear(e),null===(t=u.current)||void 0===t||t.recomputeRowHeights(e)}(t),o()})))}))};return r.a.createElement(p.a,{bounds:!0,offset:!0,onResize:function(e){var t=e.bounds;w(g(g({},t),{},{height:t.height+2*f})),m(0)}},(function(e){var t=e.measureRef;return r.a.createElement("div",{ref:t,className:"virtualized-notes-container"},r.a.createElement(h.c,{deferredMeasurementCache:x,style:{outline:"none"},height:b.height-f,width:b.width,overscanRowCount:10,ref:u,rowCount:n.length,rowHeight:x.rowHeight,rowRenderer:C,onScroll:_,"aria-label":"presentation",role:"presentation"}))}))}));S.displayName="VirtualizedList",S.propTypes=w;var _=S,C=(n(38),{notes:d.a.array.isRequired,children:d.a.func.isRequired,onScroll:d.a.func.isRequired,initialScrollTop:d.a.number.isRequired}),O=r.a.forwardRef((function(e,t){var n=e.notes,i=e.children,a=e.onScroll,l=e.initialScrollTop,c=Object(o.useRef)();Object(o.useImperativeHandle)(t,(function(){return{scrollToPosition:function(e){c.current.scrollTop=e},scrollToRow:function(e){var t=c.current,n=t.children[e];if(n){var o=t.getBoundingClientRect(),r=n.getBoundingClientRect();r.top>=o.top&&r.top<=o.top+t.clientHeight||(t.scrollTop=r.top+t.scrollTop-o.top)}}}})),Object(o.useEffect)((function(){c.current.scrollTop=l}),[l]);return r.a.createElement("div",{ref:c,className:"normal-notes-container",onScroll:function(e){a(e.target.scrollTop)},role:"list"},n.map((function(e,t){return r.a.createElement(r.a.Fragment,{key:"".concat(t,"_").concat(e.Id)},i(n,t))})))}));O.displayName="NormalList",O.propTypes=C;var q=O,E=n(1582),k=n(42),T=n(1476),R=n(1524),I=(n(152),n(44)),A=(n(151),n(1497)),j=n(339),P=n(1494),N=n(3);n(1628);function L(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,s=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){s=!0,r=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw r}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return M(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return M(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function M(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}var z=function(e){var t=e.annotations,n=e.onSubmit,a=e.onClose,u=L(Object(c.e)((function(e){var t;return[N.a.getIsMentionEnabled(e),N.a.getActiveDocumentViewerKey(e),null===(t=N.a.getFeatureFlags(e))||void 0===t?void 0:t.customizableUI]}),c.c),3),d=u[0],p=u[1],h=u[2],f=L(Object(o.useState)(""),2),m=f[0],g=f[1],v=L(Object(s.a)(),1)[0],y=Object(o.useRef)();Object(o.useEffect)((function(){y.current.focus()}),[]);var b=l()({"reply-area":!0});return r.a.createElement("div",{className:l()({"reply-area-multi-container":!0,"modular-ui":h})},r.a.createElement("div",{className:"reply-area-multi-header"},r.a.createElement("div",{className:"title"},v("action.multiReplyAnnotations",{count:t.length})),r.a.createElement(I.a,{className:"close-icon",onClick:a,img:"ic_close_black_24px"})),r.a.createElement("form",{onSubmit:function(e){e.preventDefault();var o=y.current.getEditor(),r=j.a.getFormattedTextFromDeltas(o.getContents());r.trim()&&(t.forEach((function(e){if(d){var t=j.a.createMentionReply(e,r);i.a.addAnnotations([t],p),Object(P.a)(o,t)}else{var n=i.a.createAnnotationReply(e,r,p);Object(P.a)(o,n)}})),n())}},r.a.createElement("div",{className:b,onMouseDown:function(e){return e.stopPropagation()}},r.a.createElement(A.a,{ref:function(e){y.current=e},value:m,onChange:function(e){return function(e){g(e)}(e)},isReply:!0})),r.a.createElement("div",{className:"reply-button-container"},r.a.createElement(I.a,{img:"icon-post-reply",className:"reply-button",title:"action.submit",isSubmitType:!0}))))},H=n(228),D=n(116),G=n(21);var F=function(e){var t=Object(o.useRef)(null);return Object(o.useEffect)((function(){var n,o,r=document.querySelector("#".concat(e)),i=r||function(e){var t=document.createElement("div");return t.setAttribute("id",e),t}(e);return r||(n=i,(o=window.isApryseWebViewerWebComponent?Object(G.a)():document.body).insertBefore(n,o.lastElementChild.nextElementSibling)),i.appendChild(t.current),function(){t.current.remove(),i.childElementCount||i.remove()}}),[e]),t.current||(t.current=document.createElement("div")),t.current},W=function(e){var t=e.id,n=e.position,o=e.children,r=F(t);return r.style.position="absolute",r.style.top="auto"===n.top?n.top:"".concat(n.top,"px"),r.style.left="auto"===n.left?n.left:"".concat(n.left,"px"),r.style.right="auto"===n.right?n.right:"".concat(n.right,"px"),r.style.pointerEvents="none",r.style.zIndex=999,Object(D.createPortal)(o,r)},U=n(1583),B=n(135),V=n(377),Y=n(203),K=n(2),$=n(50);n(1630);function X(e){return(X="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Z(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function J(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Z(Object(n),!0).forEach((function(t){Q(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Z(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Q(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==X(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!==X(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===X(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ee(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,s=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){s=!0,r=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw r}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return te(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return te(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function te(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}var ne=function(e){var t=e.annotations,n=e.triggerElementName,i=e.onClose,a=void 0===i?function(){}:i,l=Object(c.d)(),s=Object(o.useRef)(),u=ee(Object(o.useState)([]),2),d=u[0],p=u[1],h=ee(Object(o.useState)({left:"auto",right:"auto",top:"auto"}),2),f=h[0],m=h[1];Object(B.a)(s,(function(e){var t=document.querySelector("[data-element=".concat(n,"]")).contains(e.target),o=Object(Y.d)(),r=Object(Y.c)(),i=Object(Y.b)();t||o||r||i||a()}));var g={};t.forEach((function(e){g=J(J({},g),Object(V.a)(e))}));var v=t.find((function(e){return e instanceof window.Core.Annotations.FreeTextAnnotation&&(e.getIntent()===window.Core.Annotations.FreeTextAnnotation.Intent.FreeText||e.getIntent()===window.Core.Annotations.FreeTextAnnotation.Intent.FreeTextCallout)})),y={};if(v){var b,w,x,S,_,C,O,q,E,k,T,R=v.getRichTextStyle();y={Font:v.Font,FontSize:v.FontSize,TextAlign:v.TextAlign,TextVerticalAlign:v.TextVerticalAlign,bold:"bold"===(null!==(b=null==R||null===(w=R[0])||void 0===w?void 0:w["font-weight"])&&void 0!==b?b:""),italic:"italic"===(null!==(x=null==R||null===(S=R[0])||void 0===S?void 0:S["font-style"])&&void 0!==x?x:""),underline:(null==R||null===(_=R[0])||void 0===_||null===(C=_["text-decoration"])||void 0===C?void 0:C.includes("underline"))||(null==R||null===(O=R[0])||void 0===O||null===(q=O["text-decoration"])||void 0===q?void 0:q.includes("word")),strikeout:null!==(E=null==R||null===(k=R[0])||void 0===k||null===(T=k["text-decoration"])||void 0===T?void 0:T.includes("line-through"))&&void 0!==E&&E}}Object(o.useEffect)((function(){var e=[];if(g.TextColor&&e.push($.b.TEXT_COLOR),g.StrokeColor&&e.push($.b.STROKE_COLOR),g.FillColor&&e.push($.b.FILL_COLOR),Object($.l)("MultiStyle",e,e[0])){var t=Object($.d)("currentStyleTab","iconColor");l(K.a.setColorMap(t));var n=Object($.e)("MultiStyle").styleTabs;p(n)}}),[t]);return 0===d.length?null:r.a.createElement(W,{id:"multi-style-popup-portal",position:f},r.a.createElement("div",{className:"multi-style-container",ref:s},r.a.createElement(U.a,{annotations:t,style:g,isOpen:!0,onResize:function(){var e=Object(H.a)(n,s);m(e)},isFreeText:!!v,colorMapKey:"MultiStyle",properties:y,isRedaction:!1,isMeasure:!1,showLineStyleOptions:!1,hideSnapModeCheckbox:!1})))};ne.propTypes={annotations:d.a.array.isRequired,triggerElementName:d.a.string.isRequired,onClose:d.a.func};var oe=ne,re=n(1525),ie=n(93),ae=n(1523),le=n(5);n(1632);function ce(e){return(ce="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function se(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function ue(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?se(Object(n),!0).forEach((function(t){de(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):se(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function de(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==ce(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!==ce(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===ce(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function pe(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,s=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){s=!0,r=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw r}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return he(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return he(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function he(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}var fe={showMultiReply:d.a.bool.isRequired,setShowMultiReply:d.a.func.isRequired,setShowMultiState:d.a.func.isRequired,showMultiStyle:d.a.bool.isRequired,setShowMultiStyle:d.a.func.isRequired,setMultiSelectMode:d.a.func.isRequired,isMultiSelectedMap:d.a.object.isRequired,setIsMultiSelectedMap:d.a.func.isRequired,multiSelectedAnnotations:d.a.array.isRequired},me=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,n=new Set;return e.forEach((function(e){if(e.isGrouped()){var o=i.a.getAnnotationById(e.InReplyTo,t);o&&n.add(o)}else n.add(e)})),Array.from(n)},ge=function(e){var t=e.showMultiReply,n=e.setShowMultiReply,a=e.setShowMultiState,l=e.showMultiStyle,u=e.setShowMultiStyle,d=e.setMultiSelectMode,p=e.isMultiSelectedMap,h=e.setIsMultiSelectedMap,f=e.multiSelectedAnnotations,m=pe(Object(o.useState)([]),2),g=m[0],v=m[1],y=Object(c.d)(),b=pe(Object(s.a)(),1)[0],w=Object(c.e)(N.a.getActiveDocumentViewerKey),x=Object(c.e)(N.a.isDocumentReadOnly);Object(o.useEffect)((function(){var e=function(e){var n=ue({},p);return e.forEach((function(e){t(e,n)})),n},t=function(e,t){var n=i.a.getGroupAnnotations(e,w);n.some((function(e){return p[e.Id]}))&&n.forEach((function(e){t[e.Id]=e}))},n=function(e){var t=Object.keys(p),n=Object.keys(e);return t.some((function(e){return!n.includes(e)}))},o=function(t,o){"delete"===o?function(e){var t=ue({},p);e.forEach((function(e){delete t[e.Id]})),h(t)}(t):"modify"===o&&function(t){var o=e(t);n(o)&&h(o)}(t)};return i.a.addEventListener("annotationChanged",o,void 0,w),function(){i.a.removeEventListener("annotationChanged",o,w)}}),[p,w]),Object(o.useEffect)((function(){return function(){n(!1),h({})}}),[]),Object(o.useEffect)((function(){var e=f.filter((function(e){return i.a.canModify(e,w)}));v(e)}),[f]);var S=i.a.getNumberOfGroups(g,w)>1,_=!S&&(g.length>2||g.length>0&&i.a.getGroupAnnotations(g[0],w).length>1),C=Object(o.useCallback)((function(e){me(f,w).forEach((function(t){var n=Object(ae.a)(t,e,w);t.addReply(n);var o=i.a.getAnnotationManager(w);o.addAnnotation(n),o.trigger("addReply",[n,t,o.getRootAnnotation(t)])})),a(!1)}),[f,w]),O=Object(o.useMemo)((function(){return{resize:function(){}}}));return t?r.a.createElement(T.a.Provider,{value:O},r.a.createElement(z,{annotations:me(f,w),onSubmit:function(){return n(!1)},onClose:function(){return n(!1)}})):r.a.createElement("div",{className:"multi-select-footer"},r.a.createElement("div",{className:"buttons-container"},r.a.createElement(I.a,{dataElement:le.a.NOTE_MULTI_REPLY_BUTTON,disabled:x||0===f.length,img:"icon-header-chat-line",onClick:function(){n(!0)},title:"action.comment"}),r.a.createElement(ie.a,{dataElement:le.a.NOTE_MULTI_STATE_BUTTON,title:b("option.notesOrder.status"),img:"icon-annotation-status-none",toggleElement:le.a.NOTE_STATE_FLYOUT,disabled:x||0===g.length}),r.a.createElement(re.a,{isMultiSelectMode:!0,handleStateChange:C}),r.a.createElement(I.a,{dataElement:le.a.NOTE_MULTI_STYLE_BUTTON,img:"icon-menu-style-line",disabled:x||0===g.length,onClick:function(){u(!l)},title:"action.style"}),l&&r.a.createElement(oe,{annotations:g,triggerElementName:"multiStyleButton",onClose:function(){u(!1)}}),!_&&r.a.createElement(I.a,{dataElement:le.a.NOTE_MULTI_GROUP_BUTTON,disabled:x||!S,img:"group-annotations-icon",onClick:function(){i.a.groupAnnotations(f[0],f,w)},title:"action.group"}),_&&r.a.createElement(I.a,{dataElement:le.a.NOTE_MULTI_UNGROUP_BUTTON,img:"ungroup-annotations-icon",onClick:function(){i.a.ungroupAnnotations(f,w)},title:"action.ungroup"}),r.a.createElement(I.a,{dataElement:le.a.NOTE_MULTI_DELETE_BUTTON,disabled:x||0===g.length,img:"icon-delete-line",onClick:function(){var e={title:b("warning.multiDeleteAnnotation.title"),message:b("warning.multiDeleteAnnotation.message"),confirmBtnText:b("action.delete"),onConfirm:function(){i.a.deleteAnnotations(g,void 0,w)}};y(K.a.showWarningMessage(e))},title:"action.delete"})),r.a.createElement("div",{className:"close-container"},r.a.createElement(I.a,{className:"close-icon-container",onClick:function(){d(!1)},img:"ic_close_black_24px",ariaLabel:b("option.documentControls.closeTooltip")})))};ge.propTypes=fe;var ve=ge,ye=n(174),be=n(110),we=n.n(be),xe=n(80),Se=n(76),_e=n(54),Ce=n(173),Oe=n(41),qe=n(100);n(1634);function Ee(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,s=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){s=!0,r=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw r}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return ke(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ke(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ke(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}var Te={notes:d.a.array.isRequired,disableFilterAnnotation:d.a.bool,setSearchInputHandler:d.a.func.isRequired,isMultiSelectMode:d.a.bool,toggleMultiSelectMode:d.a.func,isMultiSelectEnabled:d.a.bool};function Re(e){var t=e.notes,n=e.disableFilterAnnotation,a=e.setSearchInputHandler,u=e.isMultiSelectMode,d=e.toggleMultiSelectMode,p=e.isMultiSelectEnabled,h=Ee(Object(c.e)((function(e){var t;return[N.a.getSortStrategy(e),N.a.isElementDisabled(e,"sortContainer"),N.a.getNotesPanelCustomHeaderOptions(e),N.a.getAnnotationFilters(e),N.a.getIsOfficeEditorMode(e),N.a.getOfficeEditorEditMode(e),null===(t=N.a.getFeatureFlags(e))||void 0===t?void 0:t.customizableUI]}),c.c),7),f=h[0],m=h[1],g=h[2],v=h[3],y=h[4],b=h[5],w=h[6],x=Ee(Object(s.a)(),1)[0],S=Object(c.d)(),_=Ee(Object(o.useState)(!1),2),C=_[0],O=_[1],q=Ee(Object(o.useState)(!1),2),E=q[0],T=q[1],R=Ee(Object(o.useState)(""),2),A=R[0],j=R[1];Object(o.useEffect)((function(){var e=v.authorFilter,t=v.colorFilter,n=v.statusFilter,o=v.typeFilter;((null==e?void 0:e.length)>0||(null==t?void 0:t.length)>0||(null==n?void 0:n.length)>0||(null==o?void 0:o.length)>0)&&O(!0);var r=function(e){var t=e.detail,n=t.types,o=t.authors,r=t.colors,i=t.statuses;n.length>0||o.length>0||r.length>0||i.length>0?O(!0):O(!1)};return window.addEventListener(_e.a.ANNOTATION_FILTER_CHANGED,r),function(){window.removeEventListener(_e.a.ANNOTATION_FILTER_CHANGED,r)}}),[]),Object(o.useEffect)((function(){y&&b===Oe.p.PREVIEW?(T(!0),a(""),j("")):T(!1)}),[y,b]);var P=we()((function(e){i.a.deselectAllAnnotations(),a(e)}),500),L=r.a.createElement("div",{className:"sort-container","data-element":"sortContainer"},r.a.createElement("div",{className:"label",id:"notesSortLabel"},"".concat(x("message.sort"),":")),r.a.createElement(xe.a,{id:"notesOrderDropdown",labelledById:"notesSortLabel",dataElement:"notesOrderDropdown",disabled:0===t.length||E,ariaLabel:"".concat(x("message.sortBy")," ").concat(f),items:Object.keys(Object(Ce.c)()),translationPrefix:"option.notesOrder",currentSelectionKey:f,onClickItem:function(e){S(K.a.setNotesPanelSortStrategy(e))}})),M=Object(qe.a)((function(){return S(K.a.openElement("filterModal"))})),z=x(y?"message.searchSuggestionsPlaceholder":"message.searchCommentsPlaceholder"),H=r.a.createElement(Se.a,{className:l()({header:!0,"modular-ui-header":w}),dataElement:"notesPanelHeader"},r.a.createElement(Se.a,{className:l()({"input-container":!0,"modular-ui-input":w}),dataElement:le.a.NotesPanel.DefaultHeader.INPUT_CONTAINER},w&&r.a.createElement(k.a,{glyph:"icon-header-search"}),r.a.createElement("input",{disabled:E,type:"text",placeholder:w?"":z,"aria-label":z,onChange:function(e){j(e.target.value),P(e.target.value)},id:"NotesPanel__input",value:A})),r.a.createElement(Se.a,{className:"comments-counter",dataElement:le.a.NotesPanel.DefaultHeader.COMMENTS_COUNTER},r.a.createElement("h2",{className:"main-comment"},x(y?"officeEditor.reviewing":"component.notesPanel")," ","(".concat(t.length,")"))),r.a.createElement(Se.a,{className:"sort-row",dataElement:le.a.NotesPanel.DefaultHeader.SORT_ROW},m?r.a.createElement("div",{className:"sort-container"}):L,r.a.createElement("div",{className:"buttons-container"},p&&!y&&r.a.createElement(I.a,{dataElement:le.a.NOTE_MULTI_SELECT_MODE_BUTTON,className:l()({active:u}),disabled:0===t.length,img:"icon-annotation-select-multiple",onClick:function(){i.a.deselectAllAnnotations(),d()},title:x("component.multiSelectButton"),ariaPressed:u}),r.a.createElement(I.a,{dataElement:le.a.NotesPanel.DefaultHeader.FILTER_ANNOTATION_BUTTON,className:l()({active:C}),disabled:n,img:"icon-comments-filter",onClick:M,title:x("component.filter"),ariaPressed:C}))));return r.a.createElement(r.a.Fragment,null,g&&r.a.createElement(ye.a,{render:g.render,renderArguments:[t]}),(!g||!g.overwriteDefaultHeader)&&H)}Re.propTypes=Te;var Ie=Re,Ae=n(153),je=n(60),Pe=n(37),Ne=n(1572);n(1636);function Le(e){return(Le="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Me(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function ze(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Me(Object(n),!0).forEach((function(t){He(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Me(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function He(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Le(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!==Le(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Le(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function De(e){return function(e){if(Array.isArray(e))return We(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||Fe(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ge(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,s=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){s=!0,r=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw r}}return l}}(e,t)||Fe(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Fe(e,t){if(e){if("string"==typeof e)return We(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?We(e,t):void 0}}function We(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}var Ue=function(e){var t=e.currentLeftPanelWidth,n=e.notes,a=e.selectedNoteIds,u=e.setSelectedNoteIds,d=e.searchInput,p=e.setSearchInput,h=e.isMultiSelectMode,f=e.setMultiSelectMode,m=e.isMultiSelectedMap,g=e.setIsMultiSelectedMap,v=e.scrollToSelectedAnnot,y=e.setScrollToSelectedAnnot,b=e.isCustomPanel,w=e.isCustomPanelOpen,x=e.isLeftSide,S=e.parentDataElement,C=Object(c.e)(N.a.getSortStrategy),O=Object(c.e)((function(e){return N.a.isElementOpen(e,le.a.NOTES_PANEL)})),I=Object(c.e)((function(e){return N.a.isElementDisabled(e,le.a.NOTES_PANEL)})),A=Object(c.e)(N.a.getPageLabels,c.c),j=Object(c.e)(N.a.getCustomNoteFilter,c.c),P=Object(c.e)(N.a.getInternalNoteFilter,c.c),L=Object(c.e)((function(e){return S?N.a.getPanelWidth(e,S):N.a.getNotesPanelWidth(e)}),c.c),M=Object(c.e)(N.a.getNotesInLeftPanel),z=Object(c.e)(N.a.isDocumentReadOnly),H=Object(c.e)(N.a.isAnnotationNumberingEnabled),D=Object(c.e)(N.a.getEnableNotesPanelVirtualizedList),G=Object(c.e)(N.a.isInDesktopOnlyMode),F=Object(c.e)(N.a.getNotesPanelCustomEmptyPanel,c.c),W=Object(c.e)(N.a.getIsNotesPanelMultiSelectEnabled),U=Object(c.e)(N.a.getActiveDocumentViewerKey),B=Object(c.e)(N.a.getIsOfficeEditorMode),V=Object(c.e)(N.a.getOfficeEditorEditMode),Y=Object(c.d)(),$=Ge(Object(s.a)(),1)[0],X=t||L,Z=Object(je.b)(),J=Ge(Object(o.useState)([]),2),Q=J[0],ee=J[1],te=Ge(Object(o.useState)(!1),2),ne=te[0],oe=te[1],re=Ge(Object(o.useState)(!1),2),ie=re[0],ae=re[1],ce=Ge(Object(o.useState)(!1),2),se=ce[0],ue=ce[1],de=Ge(Object(o.useState)(void 0),2),pe=de[0],he=de[1],fe=Object(o.useRef)(),me=Object(o.useRef)(0),ge=D?Pe.e?25:100:1/0;Object(o.useEffect)((function(){var e=function(e){Y(K.a.setAnnotationNumbering(e))},t=function(e,t){"selected"===t&&he(e[0].Id)};return i.a.addEventListener("annotationNumberingUpdated",e),i.a.addEventListener("annotationSelected",t),function(){i.a.removeEventListener("annotationNumberingUpdated",e),i.a.removeEventListener("annotationSelected",t)}}),[]);var be=-1,we=function(e){e&&(me.current=e),Y(K.a.closeElement("annotationNoteConnectorLine"))},xe=function(e){var t=e.getContents(),n=i.a.getDisplayAuthor(e.Author),o=e.getCustomData("trn-annot-preview");return(null==t?void 0:t.toLowerCase().includes(d.toLowerCase()))||(null==n?void 0:n.toLowerCase().includes(d.toLowerCase()))||(null==o?void 0:o.toLowerCase().includes(d.toLowerCase()))},Se=Object(Ce.c)()[C].getSortedNotes(n).filter((function(e){var t=!0;if(j&&(t=t&&j(e)),P&&(t=t&&P(e)),d){var n=e.getReplies(),o=[e].concat(De(n));t=t&&o.some(xe)}return t}));Object(o.useEffect)((function(){Object.keys(a).length&&-1!==be&&setTimeout((function(){var e;null===(e=fe.current)||void 0===e||e.scrollToRow(be)}),0)}),[a]);var _e=function(e){return!Object.keys(a).length&&(d&&Se.filter((function(e){return e.getReplies().some(xe)})).some((function(t){return t.Id===e.Id})))},qe=Ge(Object(o.useState)({}),2),Ee=qe[0],ke=qe[1],Te=Object(o.useCallback)((function(e,t){ke((function(n){return ze(ze({},n),{},He({},t,e))}))}),[ke]),Re=Ge(Object(o.useState)({}),2),Le=Re[0],Me=Re[1],Fe=Object(o.useCallback)((function(e,t){Me((function(n){return ze(ze({},n),{},He({},t,e))}))}),[Me]),We=Ge(Object(o.useState)({}),2),Ue=We[0],Be=We[1],Ve=function(e,t){Be((function(n){return ze(ze({},n),{},He({},e,[].concat(De(n[e]||[]),De(t))))}))},Ye=function(e){Be((function(t){return ze(ze({},t),{},He({},e,[]))}))},Ke=function(e,t){var n=Ue[e];if((null==n?void 0:n.length)>0){var o=n.indexOf(t);o>-1&&(n.splice(o,1),Be((function(t){return ze(ze({},t),{},He({},e,De(n)))})))}};Object(o.useEffect)((function(){ee(Object.values(m))}),[m]);var $e=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(){},o=null,l=Object(Ce.c)()[C],c=l.shouldRenderSeparator,s=l.getSeparatorContent,p=0===t?null:e[t-1],f=e[t];c&&s&&(!p||c(p,f))&&(o=r.a.createElement(R.a,{renderContent:function(){return s(p,f,{pageLabels:A})}}));var b=function(){!h&&a[f.Id]&&(u((function(e){var t=ze({},e);return delete t[f.Id],t})),i.a.deselectAnnotation(f,U))},S={searchInput:d,resize:n,isSelected:a[f.Id],isContentEditable:i.a.canModifyContents(f,U)&&!f.getContents(),pendingEditTextMap:Ee,setPendingEditText:Te,pendingReplyMap:Le,setPendingReply:Fe,isDocumentReadOnly:z,onTopNoteContentClicked:b,isExpandedFromSearch:_e(f),scrollToSelectedAnnot:v,sortStrategy:C,showAnnotationNumbering:H,setCurAnnotId:he,pendingAttachmentMap:Ue,clearAttachments:Ye,deleteAttachment:Ke,addAttachments:Ve,documentViewerKey:U};return t===be&&setTimeout((function(){y(!1),Y(K.a.openElement("annotationNoteConnectorLine"))}),0),r.a.createElement("div",{role:"listitem",className:"note-wrapper"},o,r.a.createElement(T.a.Provider,{value:S},r.a.createElement(E.a,{isCustomPanelOpen:w,shouldHideConnectorLine:x,annotation:f,isMultiSelected:!!m[f.Id],isMultiSelectMode:h,isMultiSelectEnabled:W,isInNotesPanel:!0,handleMultiSelect:function(e){if(e){var t=ze({},m),n=i.a.getGroupAnnotations(f,U);n.forEach((function(e){t[e.Id]=e})),g(t),i.a.selectAnnotations(n)}else{var o=ze({},m),r=i.a.getGroupAnnotations(f,U);r.forEach((function(e){delete o[e.Id]})),g(o),i.a.deselectAnnotations([f].concat(De(r)))}}})))},Xe=r.a.createElement("div",{className:"no-results"},r.a.createElement("div",null,r.a.createElement(k.a,{className:"empty-icon",glyph:"illustration - empty state - outlines"})),r.a.createElement("p",{className:"msg no-margin"},$("message.noResults"))),Ze=null!=F&&F.icon?F.icon:B?"ic-edit-page":"illustration - empty state - outlines",Je=null!=F&&F.message?F.message:$(B?"message.noRevisions":"message.noAnnotations"),Qe=F&&F.readOnlyMessage?F.readOnlyMessage:$("message.noAnnotationsReadOnly"),et=F&&!F.hideIcon||!F,tt=F&&F.render,nt=r.a.createElement("div",{className:"no-annotations"},tt?r.a.createElement(ye.a,{render:F.render}):r.a.createElement(r.a.Fragment,null,et&&r.a.createElement("div",null,r.a.createElement(k.a,{className:"empty-icon",glyph:Ze})),r.a.createElement("div",{className:"msg"},z?Qe:Je))),ot=r.a.createElement("div",{className:"multi-select-place-holder"}),rt=r.a.createElement("div",{className:"multi-reply-place-holder"}),it=Object.keys(a);if(1===it.length)be=Se.findIndex((function(e){return e.Id===it[0]}));else if(it.length){Se.filter((function(e){return a[e.Id]})).length&&(be=Se.findIndex((function(e){return e.Id===pe})))}var at,lt={};return b||!G&&Z||(lt={width:"".concat(X,"px"),minWidth:"".concat(X,"px")}),!I&&(O||M||b)?r.a.createElement("div",{className:l()({"notes-panel-container":!0,"office-editor":B})},r.a.createElement("div",{className:l()({Panel:!0,NotesPanel:!0}),style:lt,"data-element":"notesPanel",onMouseUp:function(){return i.a.deselectAllAnnotations}},!G&&Z&&!M&&r.a.createElement("div",{className:"close-container"},r.a.createElement("div",{className:"close-icon-container",onClick:function(){Y(K.a.closeElements([le.a.NOTES_PANEL]))}},r.a.createElement(k.a,{glyph:"ic_close_black_24px",className:"close-icon"}))),r.a.createElement(r.a.Fragment,null,r.a.createElement(Ie,{notes:Se,disableFilterAnnotation:0===n.length,setSearchInputHandler:p,isMultiSelectMode:h,toggleMultiSelectMode:function(){f(!h)},isMultiSelectEnabled:W}),0===Se.length?0===n.length?nt:Xe:Se.length<=ge?r.a.createElement(q,{ref:fe,notes:Se,onScroll:we,initialScrollTop:me.current},$e):r.a.createElement(_,{ref:fe,notes:Se,sortStrategy:C,onScroll:we,initialScrollTop:me.current,selectedIndex:be},$e),h?ne?rt:ot:null,B&&!h&&Se.length>0&&r.a.createElement("div",{className:"preview-all-changes"},r.a.createElement("div",{className:"divider"}),r.a.createElement(Ae.a,{isSwitch:!0,checked:V===Oe.p.PREVIEW,label:$("officeEditor.previewAllChanges"),onChange:function(e){return i.a.getOfficeEditor().setEditMode(e.target.checked?Oe.p.PREVIEW:Oe.p.REVIEWING)}})))),h&&r.a.createElement(ve,{showMultiReply:ne,setShowMultiReply:oe,showMultiState:ie,setShowMultiState:ae,showMultiStyle:se,setShowMultiStyle:ue,setMultiSelectMode:f,isMultiSelectedMap:m,setIsMultiSelectedMap:g,multiSelectedAnnotations:Q}),r.a.createElement(Ne.a,{annotationId:pe,addAttachments:Ve}),(at=$(B?"officeEditor.reviewing":"component.notesPanel"),r.a.createElement("p",{"aria-live":"assertive",style:{position:"absolute",left:"-9999px"}},Se.length>0?"".concat(at," ").concat(Se.length):$("message.noResults")))):null};function Be(e){return(Be="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Ve(){return(Ve=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(this,arguments)}function Ye(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function Ke(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ye(Object(n),!0).forEach((function(t){$e(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ye(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function $e(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Be(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!==Be(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Be(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Xe(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i,a,l=[],c=!0,s=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){s=!0,r=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw r}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Ze(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Ze(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ze(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}var Je=function(e){var t=e.isCustomPanelOpen,n=e.parentDataElement,a=void 0===n?void 0:n,l=e.dataElement,s=Xe(Object(c.e)((function(e){return[N.a.isElementOpen(e,a||l||le.a.NOTES_PANEL),N.a.getNotesInLeftPanel(e),N.a.getIsNotesPanelMultiSelectEnabled(e),N.a.isMultiViewerMode(e),N.a.getActiveDocumentViewerKey(e),N.a.getIsOfficeEditorMode(e)]}),c.c),6),u=s[0],d=s[1],p=s[2],h=s[3],f=s[4],m=s[5],g=Xe(Object(o.useState)(""),2),v=g[0],y=g[1],b=Xe(Object(o.useState)(!1),2),w=b[0],x=b[1],S=Xe(Object(o.useState)(!1),2),_=S[0],C=S[1],O=Xe(Object(o.useState)({1:[],2:[]}),2),q=O[0],E=O[1],k=Object(o.useCallback)((function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:f;q[t]=e,E(Ke({},q))}),[f,q[1],q[2],E]),T=q[f]||q[1],R=Xe(Object(o.useState)({1:{},2:{}}),2),I=R[0],A=R[1],j=Object(o.useCallback)((function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:f;I[t]=e,A(Ke({},I))}),[f,I[1],I[2],A]),P=I[f]||I[1],L=Xe(Object(o.useState)({1:{},2:{}}),2),M=L[0],z=L[1],H=Object(o.useCallback)((function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:f;M[t]=e,z(Ke({},M))}),[f,M[1],M[2],z]),D=M[f]||M[1];function G(e){var t=e.find((function(e){return e.isGrouped()})),n=[];return t&&e.forEach((function(e){t.InReplyTo!==e.InReplyTo&&t.InReplyTo!==e.Id||n.push(e)})),n}Object(o.useEffect)((function(){var e=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:f;return function(){k([],e),j({}),y("")}},t=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:f;return function(){var t=i.a.getSelectedAnnotations(e),n=G(t);w&&n.length===t.length&&x(!1),k(i.a.getAnnotationsList(e).filter((function(e){return e.Listable&&!e.isReply()&&!e.Hidden&&!e.isGrouped()&&e.ToolName!==window.Core.Tools.ToolNames.CROP&&!e.isContentEditPlaceholder()&&(!m||Object($.g)(e)===$.c.TRACKED_CHANGE)})),e)}},n=e(1);i.a.addEventListener("documentUnloaded",n);var o,r,a=t(1);return i.a.addEventListener("annotationChanged",a),i.a.addEventListener("annotationHidden",a),i.a.addEventListener("updateAnnotationPermission",a),a(),h&&(o=e(2),i.a.addEventListener("documentUnloaded",o,void 0,2),r=t(2),i.a.addEventListener("annotationChanged",r,void 0,2),i.a.addEventListener("annotationHidden",r,void 0,2),i.a.addEventListener("updateAnnotationPermission",r,void 0,2),r()),function(){h&&(i.a.removeEventListener("documentUnloaded",o,2),i.a.removeEventListener("annotationChanged",r,2),i.a.removeEventListener("annotationHidden",r,2),i.a.removeEventListener("updateAnnotationPermission",r,2)),i.a.removeEventListener("documentUnloaded",n),i.a.removeEventListener("annotationChanged",a),i.a.removeEventListener("annotationHidden",a),i.a.removeEventListener("updateAnnotationPermission",a)}}),[h,m]),Object(o.useEffect)((function(){var e,n=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:f;return function(n,o){var r=i.a.getSelectedAnnotations(e),a={};r.forEach((function(e){a[e.Id]=!0})),(t||u||d)&&(j(a,e),C(!0));var l=G(r),c=r.length>1&&l.length!==r.length||w;p&&"selected"===o&&c?(x(!0),r.forEach((function(e){D[e.Id]=e})),H(Ke({},D),e)):"deselected"===o&&(n.forEach((function(e){delete D[e.Id]})),H(Ke({},D),e))}},o=n(1);return o(),i.a.addEventListener("annotationSelected",o),h&&((e=n(2))(),i.a.addEventListener("annotationSelected",e,void 0,2)),function(){i.a.removeEventListener("annotationSelected",o),h&&i.a.removeEventListener("annotationSelected",e,2)}}),[t,u,d,w,D,p,h]);var F={notes:T,selectedNoteIds:P,setSelectedNoteIds:j,searchInput:v,setSearchInput:y,isMultiSelectMode:w,setMultiSelectMode:x,isMultiSelectedMap:D,setIsMultiSelectedMap:H,scrollToSelectedAnnot:_,setScrollToSelectedAnnot:C};return r.a.createElement(Ue,Ve({},e,F))};t.default=Je}}]);
//# sourceMappingURL=chunk.25.js.map