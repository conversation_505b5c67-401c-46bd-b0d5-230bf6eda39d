(window.webpackJsonp=window.webpackJsonp||[]).push([[89],{1962:function(e,t,a){"use strict";a.r(t);a(11),a(13),a(35),a(16),a(59),a(47);var n=a(0),o=a.n(n),r=a(84),s=a(4),i=a.n(s),l=a(6),c=a(5),p=a(2),m=a(67),d=a(71);function u(){return(u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e}).apply(this,arguments)}var y={formatType:i.a.string,isFlyoutItem:i.a.bool,description:i.a.string,style:i.a.object,className:i.a.string},f=Object(n.forwardRef)((function(e,t){var a=e.isFlyoutItem,n=e.formatType,s=e.description,i=e.style,y=e.className,f=Object(l.d)(),b="".concat(n.charAt(0).toLowerCase()).concat(n.slice(1)),O=d.b[b],g=O.dataElement,E=O.icon,w=O.title,T=function(){g===c.a.CELL_FORMAT_MORE_BUTTON&&(f(p.a.setFlyoutToggleElement(g)),f(p.a.toggleElement(c.a.CELL_FORMAT_MORE_FLYOUT)))};return a?o.a.createElement(m.a,u({},e,{ref:t,onClick:T,secondaryLabel:s,additionalClass:""})):o.a.createElement(r.a,{key:n,isActive:!1,onClick:T,dataElement:g,title:w,img:E,ariaPressed:!1,style:i,className:y})}));f.propTypes=y,f.displayName="CellFormatButton",t.default=f}}]);
//# sourceMappingURL=chunk.89.js.map