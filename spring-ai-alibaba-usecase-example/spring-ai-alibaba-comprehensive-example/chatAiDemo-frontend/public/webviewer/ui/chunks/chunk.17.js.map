{"version": 3, "sources": ["webpack:///./src/ui/src/components/NoteTextPreview/NoteTextPreview.js", "webpack:///./src/ui/src/components/NoteTextPreview/NoteTextPreview.scss?2f3f", "webpack:///./src/ui/src/components/NoteTextPreview/NoteTextPreview.scss"], "names": ["NoteTextPreview", "props", "text", "children", "replace", "panelWidth", "linesToBreak", "renderRichText", "richTextStyle", "resize", "style", "comment", "beforeContent", "useState", "expanded", "setExpand", "previewElementWidth", "setPreviewWidth", "charsPerLine", "setCharsperLine", "showPrompt", "setShowPrompt", "ref", "React", "useRef", "t", "useTranslation", "textToDisplay", "substring", "prompt", "noteTextPreviewClass", "classNames", "useEffect", "textNodeWidth", "current", "clientWidth", "useLayoutEffect", "textWidth", "context", "document", "createElement", "getContext", "font", "measureText", "width", "getTextWidth", "averageCharWidth", "length", "Math", "floor", "className", "aria-live", "onClick", "event", "stopPropagation", "api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals"], "mappings": "8vCAqEeA,IA/Df,SAAyBC,GAEvB,IAAMC,EAAOD,EAAME,SAASC,QAAQ,MAAO,IAEzCC,EASEJ,EATFI,WACAC,EAQEL,EARFK,aACAC,EAOEN,EAPFM,eACAC,EAMEP,EANFO,cACAC,EAKER,EALFQ,OACAC,EAIET,EAJFS,MAAK,EAIHT,EAFFU,eAAO,IAAG,GAAK,IAEbV,EADFW,qBAAa,IAAG,eAAQ,EAEmB,IAAfC,oBAAS,GAAM,GAAtCC,EAAQ,KAAEC,EAAS,KACmC,IAAdF,mBAAS,MAAK,GAAtDG,EAAmB,KAAEC,EAAe,KACW,IAAdJ,mBAAS,MAAK,GAA/CK,EAAY,KAAEC,EAAe,KACe,IAAfN,oBAAS,GAAM,GAA5CO,EAAU,KAAEC,EAAa,KAC1BC,EAAMC,IAAMC,OAAO,MACjBC,EAAMC,cAAND,EAQFE,EAAgBb,EAAWZ,EAAOA,EAAK0B,UAAU,EAAGV,EAAeZ,GACnEuB,EAAoBJ,EAAXX,EAAa,kBAAuB,mBAC7CgB,EAAuBC,IAAW,oBAAqB,CAAE,kBAAmBpB,IA0BlF,OAxBAqB,qBAAU,WACR,IAAMC,EAAgBX,EAAIY,QAAQC,YAClClB,EAAgBgB,KACf,CAAC5B,IAGJ+B,2BAAgB,WASd,IAAMC,EARN,SAAsBnC,GACpB,IACMoC,EADSC,SAASC,cAAc,UACfC,WAAW,MAGlC,OAFAH,EAAQI,KAAO,kBAERJ,EAAQK,YAAYzC,GAAM0C,MAGjBC,CAAa3C,GACzB4C,EAAmBT,EAAYnC,EAAK6C,OACpC7B,EAAe8B,KAAKC,MAAMjC,EAAsB8B,GACtD3B,EAAgBD,GAGhBG,EADmBgB,EAAYrB,EACJV,KAC1B,CAACJ,EAAMc,IAGR,yBAAKkC,UAAWpB,EAAsBR,IAAKA,EAAKZ,MAAOA,EAAOyC,YAAU,UACrEvC,IACAL,GAAkBC,EAAgBD,EAAeoB,EAAenB,EAAe,GAAKmB,EAAc,IAAEP,GAAc,4BAAQ8B,UAAU,2BAA2BE,QArC7I,SAACC,GACtBA,EAAMC,kBACNvC,GAAWD,GACXL,GAAUA,MAkCkLoB,M,qBChEhM,IAAI0B,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAzB,SAAS0B,KAAKC,YAAYJ,GAI5B,IAAIK,EAEJA,EAAgB5B,SAAS6B,qBAAqB,oBAEzCD,EAAcpB,SACjBoB,EAzBF,SAASE,EAAwBC,EAASC,EAAOhC,UAC/C,MAAMiC,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIlB,EAAI,EAAGA,EAAIO,EAAcpB,OAAQa,IAAK,CAC7C,MAAMmB,EAAeZ,EAAcP,GACnC,GAAU,IAANA,EACFmB,EAAaF,WAAWX,YAAYJ,GACpCA,EAASkB,OAAS,WACZF,EAAgB/B,OAAS,GAC3B+B,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYpB,EAASoB,iBAIhC,CACL,MAAMD,EAAYnB,EAASqB,WAAU,GACrCJ,EAAaF,WAAWX,YAAYe,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP1B,EAAIC,EAASK,GAI1BF,EAAOyB,QAAU5B,EAAQ6B,QAAU,I,sBClEzB1B,EAAOyB,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACjB,EAAOC,EAAI,+uBAAgvB", "file": "chunks/chunk.17.js", "sourcesContent": ["import React, { useState, useEffect, useLayoutEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport classNames from 'classnames';\n\nimport './NoteTextPreview.scss';\n\nfunction NoteTextPreview(props) {\n  /* This replace is to remove the break line that the React Quill component add into the text */\n  const text = props.children.replace(/\\n$/, '');\n  const {\n    panelWidth,\n    linesToBreak,\n    renderRichText,\n    richTextStyle,\n    resize,\n    style,\n    /* If text being previewed is a comment it gets a darker font color */\n    comment = false,\n    beforeContent = () => {},\n  } = props;\n  const [expanded, setExpand] = useState(false);\n  const [previewElementWidth, setPreviewWidth] = useState(null);\n  const [charsPerLine, setCharsperLine] = useState(null);\n  const [showPrompt, setShowPrompt] = useState(false);\n  const ref = React.useRef(null);// Must import this way in order to mock it properly\n  const { t } = useTranslation();\n\n  const onClickHandler = (event) => {\n    event.stopPropagation();\n    setExpand(!expanded);\n    resize && resize();\n  };\n\n  const textToDisplay = expanded ? text : text.substring(0, charsPerLine * linesToBreak);\n  const prompt = expanded ? t('action.showLess') : t('action.showMore');\n  const noteTextPreviewClass = classNames('note-text-preview', { 'preview-comment': comment });\n\n  useEffect(() => {\n    const textNodeWidth = ref.current.clientWidth;\n    setPreviewWidth(textNodeWidth);\n  }, [panelWidth]);\n\n  // useLayoutEffect to avoid a flicker before we get the final text prop\n  useLayoutEffect(() => {\n    function getTextWidth(text) {\n      const canvas = document.createElement('canvas');\n      const context = canvas.getContext('2d');\n      context.font = '13px sans-serif';\n\n      return context.measureText(text).width;\n    }\n\n    const textWidth = getTextWidth(text);\n    const averageCharWidth = textWidth / text.length;\n    const charsPerLine = Math.floor(previewElementWidth / averageCharWidth);\n    setCharsperLine(charsPerLine);\n\n    const totalLines = textWidth / previewElementWidth;\n    setShowPrompt(totalLines > linesToBreak);\n  }, [text, previewElementWidth]);\n\n  return (\n    <div className={noteTextPreviewClass} ref={ref} style={style} aria-live=\"polite\">\n      {beforeContent()}\n      {renderRichText && richTextStyle ? renderRichText(textToDisplay, richTextStyle, 0) : textToDisplay} {showPrompt && <button className=\"note-text-preview-prompt\" onClick={onClickHandler}>{prompt}</button>}\n    </div>\n  );\n}\n\nexport default NoteTextPreview;", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./NoteTextPreview.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".note-text-preview{font-size:13px;color:var(--gray-7);padding-right:var(--note-content-right-padding-width);-webkit-user-select:text!important;-moz-user-select:text!important;user-select:text!important;cursor:text;height:-moz-fit-content;height:fit-content;width:calc(100% - var(--note-content-right-padding-width));overflow:hidden}.preview-comment{color:var(--text-color)}.note-text-preview-prompt{cursor:pointer;color:var(--primary-button);text-decoration:underline;position:relative;pointer-events:auto;background:transparent;border:none;padding:0}.note-text-preview-prompt:hover{color:var(--primary-button-hover)}.trackedChangePopup .note-text-preview{max-height:400px;overflow-y:auto;width:calc(100% + var(--note-content-right-padding-width))}\", \"\"]);\n\n// exports\n"], "sourceRoot": ""}