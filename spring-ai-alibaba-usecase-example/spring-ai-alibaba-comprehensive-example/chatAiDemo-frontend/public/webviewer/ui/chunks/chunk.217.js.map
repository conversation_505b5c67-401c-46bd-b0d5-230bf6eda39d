{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/sv.js"], "names": ["module", "exports", "e", "a", "default", "t", "d", "name", "weekdays", "split", "weekdaysShort", "weekdaysMin", "months", "monthsShort", "weekStart", "yearStart", "ordinal", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "lll", "llll", "relativeTime", "future", "past", "s", "m", "mm", "h", "hh", "dd", "M", "MM", "y", "yy", "locale"], "mappings": "gFAAoEA,EAAOC,QAA6K,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAGI,EAAE,CAACC,KAAK,KAAKC,SAAS,oDAAoDC,MAAM,KAAKC,cAAc,8BAA8BD,MAAM,KAAKE,YAAY,uBAAuBF,MAAM,KAAKG,OAAO,wFAAwFH,MAAM,KAAKI,YAAY,kDAAkDJ,MAAM,KAAKK,UAAU,EAAEC,UAAU,EAAEC,QAAQ,SAASd,GAAG,IAAIG,EAAEH,EAAE,GAAG,MAAM,IAAIA,GAAG,IAAIG,GAAG,IAAIA,EAAE,IAAI,KAAK,KAAKY,QAAQ,CAACC,GAAG,QAAQC,IAAI,WAAWC,EAAE,aAAaC,GAAG,cAAcC,IAAI,0BAA0BC,KAAK,+BAA+BC,IAAI,mBAAmBC,KAAK,wBAAwBC,aAAa,CAACC,OAAO,QAAQC,KAAK,eAAeC,EAAE,iBAAiBC,EAAE,WAAWC,GAAG,aAAaC,EAAE,WAAWC,GAAG,YAAY3B,EAAE,SAAS4B,GAAG,WAAWC,EAAE,WAAWC,GAAG,aAAaC,EAAE,SAASC,GAAG,UAAU,OAAOnC,EAAEC,QAAQmC,OAAOjC,EAAE,MAAK,GAAIA,EAAroCD,CAAE,EAAQ", "file": "chunks/chunk.217.js", "sourcesContent": ["!function(e,t){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=t(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],t):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_sv=t(e.dayjs)}(this,(function(e){\"use strict\";function t(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var a=t(e),d={name:\"sv\",weekdays:\"söndag_måndag_tisdag_onsdag_torsdag_fredag_lördag\".split(\"_\"),weekdaysShort:\"sön_mån_tis_ons_tor_fre_lör\".split(\"_\"),weekdaysMin:\"sö_må_ti_on_to_fr_lö\".split(\"_\"),months:\"januari_februari_mars_april_maj_juni_juli_augusti_september_oktober_november_december\".split(\"_\"),monthsShort:\"jan_feb_mar_apr_maj_jun_jul_aug_sep_okt_nov_dec\".split(\"_\"),weekStart:1,yearStart:4,ordinal:function(e){var t=e%10;return\"[\"+e+(1===t||2===t?\"a\":\"e\")+\"]\"},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"YYYY-MM-DD\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY [kl.] HH:mm\",LLLL:\"dddd D MMMM YYYY [kl.] HH:mm\",lll:\"D MMM YYYY HH:mm\",llll:\"ddd D MMM YYYY HH:mm\"},relativeTime:{future:\"om %s\",past:\"för %s sedan\",s:\"några sekunder\",m:\"en minut\",mm:\"%d minuter\",h:\"en timme\",hh:\"%d timmar\",d:\"en dag\",dd:\"%d dagar\",M:\"en månad\",MM:\"%d månader\",y:\"ett år\",yy:\"%d år\"}};return a.default.locale(d,null,!0),d}));"], "sourceRoot": ""}