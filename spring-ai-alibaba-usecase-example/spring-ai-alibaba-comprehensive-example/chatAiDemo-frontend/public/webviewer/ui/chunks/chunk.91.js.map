{"version": 3, "sources": ["webpack:///./src/ui/src/components/ModularComponents/PresetButton/buttons/SheetEditor/CopyPasteCutButton.js"], "names": ["propTypes", "actionType", "PropTypes", "oneOf", "Object", "values", "CELL_ACTION_OPTIONS", "isRequired", "isFlyoutItem", "bool", "style", "object", "className", "string", "CopyPasteCutButton", "forwardRef", "props", "ref", "key", "char<PERSON>t", "toUpperCase", "slice", "menuItems", "dataElement", "icon", "title", "handleClick", "onClick", "additionalClass", "isActive", "img", "ariaPressed", "displayName"], "mappings": "yaAOA,IAAMA,EAAY,CAChBC,WAAYC,IAAUC,MAAMC,OAAOC,OAAOC,MAAsBC,WAChEC,aAAcN,IAAUO,KACxBC,MAAOR,IAAUS,OACjBC,UAAWV,IAAUW,QAGjBC,EAAqBC,sBAAW,SAACC,EAAOC,GAC5C,IAAQT,EAA+CQ,EAA/CR,aAAcP,EAAiCe,EAAjCf,WAAYS,EAAqBM,EAArBN,MAAOE,EAAcI,EAAdJ,UAGnCM,EAAM,OAAH,OAAUjB,EAAWkB,OAAO,GAAGC,eAAa,OAAGnB,EAAWoB,MAAM,IACzE,EAAqCC,IAAUJ,GAAvCK,EAAW,EAAXA,YAAaC,EAAI,EAAJA,KAAMC,EAAK,EAALA,MAErBC,EAAc,aAIpB,OACElB,EACE,kBAAC,IAAmB,KACdQ,EAAK,CACTC,IAAKA,EACLU,QAASD,EACTE,gBAAuC,MAGvC,kBAAC,IAAY,CACXV,IAAKjB,EACL4B,UApBS,EAqBTF,QAASD,EACTH,YAAaA,EACbE,MAAOA,EACPK,IAAKN,EACLO,aAzBS,EA0BTrB,MAAOA,EACPE,UAAWA,OAMrBE,EAAmBd,UAAYA,EAC/Bc,EAAmBkB,YAAc,qBAElBlB", "file": "chunks/chunk.91.js", "sourcesContent": ["import React, { forwardRef } from 'react';\nimport ActionButton from 'components/ActionButton';\nimport PropTypes from 'prop-types';\nimport FlyoutItemContainer from '../../../FlyoutItemContainer';\nimport { menuItems } from '../../../Helpers/menuItems';\nimport { CELL_ACTION_OPTIONS } from 'src/constants/spreadsheetEditor';\n\nconst propTypes = {\n  actionType: PropTypes.oneOf(Object.values(CELL_ACTION_OPTIONS)).isRequired,\n  isFlyoutItem: PropTypes.bool,\n  style: PropTypes.object,\n  className: PropTypes.string,\n};\n\nconst CopyPasteCutButton = forwardRef((props, ref) => {\n  const { isFlyoutItem, actionType, style, className } = props;\n  const isActive = false;\n\n  const key = `cell${actionType.charAt(0).toUpperCase()}${actionType.slice(1)}`;\n  const { dataElement, icon, title } = menuItems[key];\n\n  const handleClick = () => {\n    // handle button click\n  };\n\n  return (\n    isFlyoutItem ?\n      <FlyoutItemContainer\n        {...props}\n        ref={ref}\n        onClick={handleClick}\n        additionalClass={isActive ? 'active' : ''}\n      />\n      : (\n        <ActionButton\n          key={actionType}\n          isActive={isActive}\n          onClick={handleClick}\n          dataElement={dataElement}\n          title={title}\n          img={icon}\n          ariaPressed={isActive}\n          style={style}\n          className={className}\n        />\n      )\n  );\n});\n\nCopyPasteCutButton.propTypes = propTypes;\nCopyPasteCutButton.displayName = 'CopyPasteCutButton';\n\nexport default CopyPasteCutButton;"], "sourceRoot": ""}