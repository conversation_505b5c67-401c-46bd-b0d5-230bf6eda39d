(window.webpackJsonp=window.webpackJsonp||[]).push([[33],{1480:function(e,t,n){var o=n(32),r=n(1531);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.i,r,""]]);var i={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach(e=>o.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))}),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const r=t[o];if(0===o)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};o(r,i);e.exports=r.locals||{}},1486:function(e,t,n){"use strict";n.d(t,"c",(function(){return o})),n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return i}));var o={OUTLINE:"outline",PORTFOLIO:"portfolio"},r={ON_TARGET_HORIZONTAL_MIDPOINT:"onTargetHorizontalMidPoint",ABOVE_TARGET:"aboveTarget",BELOW_TARGET:"belowTarget",INITIAL:"initial"},i=8},1493:function(e,t,n){"use strict";var o=n(0),r=n.n(o),i=n(4),a=n.n(i),l=n(44),u=(n(1579),{img:a.a.string,label:a.a.oneOfType([a.a.string,a.a.number]),dataElement:a.a.string,onClick:a.a.func,ariaLabel:a.a.string,ariaControls:a.a.string,role:a.a.string,disabled:a.a.bool}),c=function(e){var t=e.img,n=e.dataElement,o=e.onClick,i=e.label,a=e.ariaLabel,u=e.ariaControls,c=e.role,s=e.disabled;return r.a.createElement(l.a,{className:"TextButton",img:t,label:i,dataElement:n,onClick:o,ariaLabel:a,ariaControls:u,role:c,disabled:s})};c.propTypes=u;var s=r.a.memo(c);t.a=s},1513:function(e,t,n){"use strict";var o=n(0),r=n.n(o).a.createContext();t.a=r},1531:function(e,t,n){(e.exports=n(33)(!1)).push([e.i,".bookmark-outline-panel{display:flex;padding-left:var(--padding);padding-right:var(--padding-small)}.bookmark-outline-control-button{width:auto}.bookmark-outline-control-button span{color:inherit}.bookmark-outline-control-button,.bookmark-outline-control-button.disabled,.bookmark-outline-control-button[disabled]{color:var(--secondary-button-text)}.bookmark-outline-control-button.disabled,.bookmark-outline-control-button[disabled]{opacity:.5}.bookmark-outline-control-button.disabled span,.bookmark-outline-control-button[disabled] span{color:inherit}.bookmark-outline-control-button:not(.disabled):active,.bookmark-outline-control-button:not(.disabled):hover,.bookmark-outline-control-button:not([disabled]):active,.bookmark-outline-control-button:not([disabled]):hover{color:var(--secondary-button-hover)}.bookmark-outline-panel-header{display:flex;flex-flow:row nowrap;justify-content:space-between;align-items:center;padding:var(--padding-tiny);border-bottom:1px solid var(--divider)}.bookmark-outline-panel-header .header-title{font-size:16px}.bookmark-outline-row{flex-grow:1;overflow-y:auto}.msg-no-bookmark-outline{color:var(--placeholder-text);text-align:center}.bookmark-outline-single-container{display:flex;flex-flow:row nowrap;align-items:flex-start;border-radius:4px;margin-left:2px;margin-right:2px}.bookmark-outline-single-container.default{padding:var(--padding-tiny);border:1px solid transparent}.bookmark-outline-single-container.default.hover,.bookmark-outline-single-container.default:hover,.bookmark-outline-single-container.default[focus-within]{cursor:pointer}.bookmark-outline-single-container.default.hover,.bookmark-outline-single-container.default:focus-within,.bookmark-outline-single-container.default:hover{cursor:pointer}.bookmark-outline-single-container.default.hover .bookmark-outline-more-button,.bookmark-outline-single-container.default:hover .bookmark-outline-more-button,.bookmark-outline-single-container.default[focus-within] .bookmark-outline-more-button{display:flex;background-color:transparent}.bookmark-outline-single-container.default.hover .bookmark-outline-more-button,.bookmark-outline-single-container.default:focus-within .bookmark-outline-more-button,.bookmark-outline-single-container.default:hover .bookmark-outline-more-button{display:flex;background-color:transparent}.bookmark-outline-single-container.default[focus-within]{border-color:transparent}.bookmark-outline-single-container.default:focus-within{border-color:transparent}.bookmark-outline-single-container.default .bookmark-outline-label-row{overflow:hidden}.bookmark-outline-single-container.default.focus-visible,.bookmark-outline-single-container.default:focus-visible{outline:var(--focus-visible-outline)!important}.bookmark-outline-single-container.editing{background-color:var(--faded-component-background);padding:var(--padding-medium) 20px}.bookmark-outline-single-container.editing.focus-visible,.bookmark-outline-single-container.editing:focus-visible{outline:var(--focus-visible-outline)!important}.bookmark-outline-single-container.preview{display:inline-flex;margin-top:0;padding:var(--padding-small);background-color:var(--component-background);box-shadow:0 0 3px var(--note-box-shadow)}.bookmark-outline-single-container .bookmark-outline-checkbox{flex-grow:0;flex-shrink:0;margin-top:2px;margin-bottom:2px;margin-right:var(--padding-small)}.bookmark-outline-single-container .bookmark-outline-label-row{flex-grow:1;flex-shrink:1;display:flex;flex-flow:row wrap;align-items:flex-start;position:relative;overflow:hidden}.bookmark-outline-single-container .bookmark-outline-label{font-weight:600;flex-grow:1;flex-shrink:1;margin-bottom:var(--padding-small)}.bookmark-outline-single-container .bookmark-outline-input,.bookmark-outline-single-container .bookmark-outline-text{flex-grow:1;flex-shrink:1;flex-basis:calc(100% - 22px);margin-top:2px;margin-bottom:2px}.bookmark-outline-single-container .bookmark-text-input{margin-left:var(--padding-large)}.bookmark-outline-single-container .bookmark-outline-input{color:var(--text-color);width:calc(100% - var(--padding-large));padding:var(--padding-small);border:1px solid var(--border)}.bookmark-outline-single-container .bookmark-outline-input:focus{border-color:var(--outline-color)}.bookmark-outline-single-container .bookmark-outline-input::-moz-placeholder{color:var(--placeholder-text)}.bookmark-outline-single-container .bookmark-outline-input::placeholder{color:var(--placeholder-text)}.bookmark-outline-single-container .bookmark-outline-more-button{display:none;flex-grow:0;flex-shrink:0;width:16px;height:16px;margin:2px 2px 2px var(--padding-tiny)}.bookmark-outline-single-container .bookmark-outline-more-button .Icon{width:14px;height:14px}.bookmark-outline-single-container .bookmark-outline-more-button.icon-only:hover:not(:disabled):not(.disabled){box-shadow:none;outline:solid 1px var(--hover-border)}.bookmark-outline-single-container .bookmark-outline-more-button[focus-within].icon-only{border:none;box-shadow:none}.bookmark-outline-single-container .bookmark-outline-more-button:focus-within.icon-only{border:none;box-shadow:none}.bookmark-outline-single-container .bookmark-outline-more-button[focus-within] .Icon{color:var(--focus-border)}.bookmark-outline-single-container .bookmark-outline-more-button:focus-within .Icon{color:var(--focus-border)}.bookmark-outline-single-container .bookmark-outline-editing-controls{padding:2px;flex-basis:100%;display:flex;flex-flow:row wrap;justify-content:flex-end;align-items:center;margin-top:var(--padding-medium)}.bookmark-outline-single-container .bookmark-outline-cancel-button,.bookmark-outline-single-container .bookmark-outline-save-button{width:auto;padding:6px var(--padding)}.bookmark-outline-single-container .bookmark-outline-cancel-button{color:var(--secondary-button-text)}.bookmark-outline-single-container .bookmark-outline-cancel-button:hover{color:var(--secondary-button-hover)}.bookmark-outline-single-container .bookmark-outline-save-button{color:var(--primary-button-text);background-color:var(--primary-button);margin-left:var(--padding-tiny);border-radius:4px}.bookmark-outline-single-container .bookmark-outline-save-button:hover{background-color:var(--primary-button-hover)}.bookmark-outline-single-container .bookmark-outline-save-button.disabled,.bookmark-outline-single-container .bookmark-outline-save-button:disabled{background-color:var(--primary-button)!important;opacity:.5}.bookmark-outline-single-container .bookmark-outline-save-button.disabled span,.bookmark-outline-single-container .bookmark-outline-save-button:disabled span{color:var(--primary-button-text)}.bookmark-outline-footer{border-top:1.5px solid var(--gray-4);padding-top:var(--padding-medium);padding-bottom:var(--padding-medium);display:flex;justify-content:center;align-items:center}.bookmark-outline-footer .add-new-button .Icon{width:14px;height:14px;margin-right:var(--padding-tiny);color:inherit;fill:currentColor}.bookmark-outline-footer .add-new-button.disabled .Icon.disabled,.bookmark-outline-footer .add-new-button.disabled .Icon.disabled path,.bookmark-outline-footer .add-new-button[disabled] .Icon.disabled,.bookmark-outline-footer .add-new-button[disabled] .Icon.disabled path{color:inherit;fill:currentColor}.bookmark-outline-footer .multi-selection-button{width:auto;padding:7px}.bookmark-outline-footer .multi-selection-button .Icon{width:18px;height:18px}.bookmark-outline-footer .multi-selection-button:not(:first-child){margin-left:var(--padding-tiny)}.bookmark-outline-footer .multi-selection-button:hover{background-color:transparent}.bookmark-outline-footer .multi-selection-button.disabled:hover,.bookmark-outline-footer .multi-selection-button:disabled:hover{box-shadow:none}",""])},1545:function(e,t,n){"use strict";n(10),n(8),n(57),n(9),n(12),n(49),n(53),n(151),n(35),n(38),n(19),n(11),n(13),n(14),n(16),n(15),n(20),n(18),n(26),n(27),n(25),n(22),n(29),n(28),n(45),n(23),n(24),n(48),n(46),n(63),n(64),n(65),n(66),n(36),n(39),n(40),n(62);var o=n(0),r=n.n(o),i=n(428),a=n(6),l=n(4),u=n.n(l),c=n(1),s=n(3),d=n(44),p=n(1493),f=n(1487),m=n(1513),b=(n(1609),n(1480),n(5)),g=n(1502),h=n(438);function v(e){return(v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function k(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */k=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},r="function"==typeof Symbol?Symbol:{},i=r.iterator||"@@iterator",a=r.asyncIterator||"@@asyncIterator",l=r.toStringTag||"@@toStringTag";function u(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,n){return e[t]=n}}function c(e,t,n,r){var i=t&&t.prototype instanceof p?t:p,a=Object.create(i.prototype),l=new A(r||[]);return o(a,"_invoke",{value:x(e,n,l)}),a}function s(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=c;var d={};function p(){}function f(){}function m(){}var b={};u(b,i,(function(){return this}));var g=Object.getPrototypeOf,h=g&&g(g(N([])));h&&h!==t&&n.call(h,i)&&(b=h);var y=m.prototype=p.prototype=Object.create(b);function w(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function O(e,t){var r;o(this,"_invoke",{value:function(o,i){function a(){return new t((function(r,a){!function o(r,i,a,l){var u=s(e[r],e,i);if("throw"!==u.type){var c=u.arg,d=c.value;return d&&"object"==v(d)&&n.call(d,"__await")?t.resolve(d.__await).then((function(e){o("next",e,a,l)}),(function(e){o("throw",e,a,l)})):t.resolve(d).then((function(e){c.value=e,a(c)}),(function(e){return o("throw",e,a,l)}))}l(u.arg)}(o,i,r,a)}))}return r=r?r.then(a,a):a()}})}function x(e,t,n){var o="suspendedStart";return function(r,i){if("executing"===o)throw new Error("Generator is already running");if("completed"===o){if("throw"===r)throw i;return j()}for(n.method=r,n.arg=i;;){var a=n.delegate;if(a){var l=E(a,n);if(l){if(l===d)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===o)throw o="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o="executing";var u=s(e,t,n);if("normal"===u.type){if(o=n.done?"completed":"suspendedYield",u.arg===d)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(o="completed",n.method="throw",n.arg=u.arg)}}}function E(e,t){var n=t.method,o=e.iterator[n];if(void 0===o)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,E(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),d;var r=s(o,e.iterator,t.arg);if("throw"===r.type)return t.method="throw",t.arg=r.arg,t.delegate=null,d;var i=r.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,d):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,d)}function T(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function L(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function A(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(T,this),this.reset(!0)}function N(e){if(e){var t=e[i];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,r=function t(){for(;++o<e.length;)if(n.call(e,o))return t.value=e[o],t.done=!1,t;return t.value=void 0,t.done=!0,t};return r.next=r}}return{next:j}}function j(){return{value:void 0,done:!0}}return f.prototype=m,o(y,"constructor",{value:m,configurable:!0}),o(m,"constructor",{value:f,configurable:!0}),f.displayName=u(m,l,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===f||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,m):(e.__proto__=m,u(e,l,"GeneratorFunction")),e.prototype=Object.create(y),e},e.awrap=function(e){return{__await:e}},w(O.prototype),u(O.prototype,a,(function(){return this})),e.AsyncIterator=O,e.async=function(t,n,o,r,i){void 0===i&&(i=Promise);var a=new O(c(t,n,o,r),i);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},w(y),u(y,l,"Generator"),u(y,i,(function(){return this})),u(y,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var o in t)n.push(o);return n.reverse(),function e(){for(;n.length;){var o=n.pop();if(o in t)return e.value=o,e.done=!1,e}return e.done=!0,e}},e.values=N,A.prototype={constructor:A,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(L),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function o(n,o){return a.type="throw",a.arg=e,t.next=n,o&&(t.method="next",t.arg=void 0),!!o}for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r],a=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),u=n.call(i,"finallyLoc");if(l&&u){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(e,t){for(var o=this.tryEntries.length-1;o>=0;--o){var r=this.tryEntries[o];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,d):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),d},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),L(n),d}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var r=o.arg;L(n)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:N(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),d}},e}function y(e,t,n,o,r,i,a){try{var l=e[i](a),u=l.value}catch(e){return void n(e)}l.done?t(u):Promise.resolve(u).then(o,r)}function w(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function O(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?w(Object(n),!0).forEach((function(t){x(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):w(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function x(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==v(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!==v(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===v(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function E(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i,a,l=[],u=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;u=!1}else for(;!(u=(o=i.call(n)).done)&&(l.push(o.value),l.length!==t);u=!0);}catch(e){c=!0,r=e}finally{try{if(!u&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw r}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return T(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return T(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function T(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}var L=Object(o.lazy)((function(){return Promise.resolve().then(n.bind(null,1651))})),A={text:u.a.string.isRequired,outlinePath:u.a.string,isAdding:u.a.bool,isExpanded:u.a.bool,setIsExpanded:u.a.func,isOutlineRenaming:u.a.bool,setOutlineRenaming:u.a.func,isOutlineChangingDest:u.a.bool,setOutlineChangingDest:u.a.func,onCancel:u.a.func,textColor:u.a.string,children:u.a.array,setMultiSelected:u.a.func,moveOutlineInward:u.a.func,moveOutlineBeforeTarget:u.a.func,moveOutlineAfterTarget:u.a.func},N=function(e){var t=e.text,n=e.outlinePath,l=e.isAdding,u=e.isExpanded,v=e.setIsExpanded,w=e.isOutlineRenaming,x=e.setOutlineRenaming,T=e.isOutlineChangingDest,A=e.setOutlineChangingDest,N=e.onCancel,j=e.textColor,I=e.children,P=e.setMultiSelected,S=e.moveOutlineInward,_=e.moveOutlineBeforeTarget,C=e.moveOutlineAfterTarget,D=Object(o.useContext)(m.a),R=D.currentDestPage,M=D.currentDestText,B=D.editingOutlines,F=D.setEditingOutlines,G=D.isMultiSelectMode,H=D.isOutlineEditable,U=D.addNewOutline,W=D.renameOutline,z=D.updateOutlineDest,V=D.selectedOutlines,q=D.updateOutlines,Y=D.removeOutlines,K=Object(a.e)((function(e){return s.a.getFeatureFlags(e)}),a.c).customizableUI,Z=E(Object(i.a)(),1)[0],$=E(Object(o.useState)(!1),2),J=$[0],X=$[1],Q=E(Object(o.useState)(t),2),ee=Q[0],te=Q[1],ne=E(Object(o.useState)(!1),2),oe=ne[0],re=ne[1],ie=Object(o.useRef)(),ae=(null==V?void 0:V.includes(n))||!1,le=function(){U(""===ee.trim()?"":ee)},ue=function(){x(!1),re(!1),W(n,ee)},ce=function(){q(),w&&(x(!1),re(!1),te(t)),T&&A(!1),l&&N()},se=function(){return!ee||t===ee};Object(o.useEffect)((function(){ee!==t&&te(t)}),[t]),Object(o.useEffect)((function(){(l||w)&&(ie.current.focus(),ie.current.select()),X(!l&&!w&&!T)}),[w,T]),Object(o.useEffect)((function(){var e=O({},B),t=w||T;t?e[n]=t:delete e[n],F(O({},e))}),[w,T]);var de={color:j||"auto"},pe=function(){var e,t=(e=k().mark((function e(t){return k().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:e.t0=t,e.next=e.t0===f.b.RENAME?3:e.t0===f.b.SETDEST?6:e.t0===f.b.DELETE?9:e.t0===f.b.MOVE_UP?11:e.t0===f.b.MOVE_DOWN?15:e.t0===f.b.MOVE_LEFT?19:e.t0===f.b.MOVE_RIGHT?23:27;break;case 3:return x(!0),re(!0),e.abrupt("break",28);case 6:return A(!0),c.a.setToolMode("OutlineDestinationCreateTool"),e.abrupt("break",28);case 9:return Y([n]),e.abrupt("break",28);case 11:return e.next=13,h.a.moveOutlineUp(n);case 13:return q(),e.abrupt("break",28);case 15:return e.next=17,h.a.moveOutlineDown(n);case 17:return q(),e.abrupt("break",28);case 19:return e.next=21,h.a.moveOutlineOutward(n);case 21:return q(),e.abrupt("break",28);case 23:return e.next=25,h.a.moveOutlineInward(n);case 25:return q(),e.abrupt("break",28);case 27:return e.abrupt("break",28);case 28:case"end":return e.stop()}}),e)})),function(){var t=this,n=arguments;return new Promise((function(o,r){var i=e.apply(t,n);function a(e){y(i,o,r,a,l,"next",e)}function l(e){y(i,o,r,a,l,"throw",e)}a(void 0)}))});return function(e){return t.apply(this,arguments)}}(),fe="".concat(b.a.BOOKMARK_OUTLINE_FLYOUT,"-").concat(n),me={shouldHideDeleteButton:!1,currentFlyout:Object(a.e)((function(e){return s.a.getFlyout(e,fe)})),flyoutSelector:fe,type:"outline",handleOnClick:pe},be={flyoutToggleElement:"bookmarkOutlineFlyout-".concat(n),moreOptionsDataElement:"outline-more-button-".concat(n)},ge={id:"outline-checkbox-".concat(n),checked:ae,onChange:function(e){P(n,e.target.checked)},ariaLabel:t,disabled:!G};return r.a.createElement("div",{className:"bookmark-outline-label-row"},l&&r.a.createElement("div",{className:"bookmark-outline-label"},Z("component.newOutlineTitle")),oe&&r.a.createElement("div",{className:"bookmark-outline-label"},Z("component.outlineTitle")),J&&r.a.createElement(g.a,{key:n,labelHeader:t,textColor:j,enableMoreOptionsContextMenuFlyout:!0,onDoubleClick:function(){H&&(x(!0),re(!0))},checkboxOptions:ge,contentMenuFlyoutOptions:me,contextMenuMoreButtonOptions:be,expanded:u,setIsExpandedHandler:v},I.map((function(e){return function(e){return r.a.createElement(L,{key:h.a.getOutlineId(e),outline:e,setMultiSelected:P,moveOutlineInward:S,moveOutlineBeforeTarget:_,moveOutlineAfterTarget:C})}(e)}))),T&&r.a.createElement("div",{className:"bookmark-outline-text outline-text",style:de},t),(l||w)&&r.a.createElement("input",{type:"text",name:"outline",ref:ie,className:"bookmark-outline-input",placeholder:K?"":Z("component.outlineTitle"),"aria-label":Z("component.newOutlineTitle"),value:ee,onKeyDown:function(e){"Enter"===e.key&&(e.stopPropagation(),l&&le(),w&&!se()&&ue()),"Escape"===e.key&&ce()},onChange:function(e){return te(e.target.value)}}),(l||oe||T)&&r.a.createElement("div",{className:"outline-destination"},Z("component.destination"),": ",Z("component.bookmarkPage")," ",R,",",r.a.createElement("span",{style:{fontStyle:"italic"}}," “",M,"”")),(l||w||T)&&r.a.createElement("div",{className:"bookmark-outline-editing-controls"},r.a.createElement(p.a,{className:"bookmark-outline-cancel-button",label:Z("action.cancel"),ariaLabel:"".concat(Z("action.cancel")," ").concat(Z("component.outlineTitle")),onClick:ce}),l&&r.a.createElement(d.a,{className:"bookmark-outline-save-button",label:Z("action.add"),isSubmitType:!0,onClick:le}),w&&r.a.createElement(d.a,{className:"bookmark-outline-save-button",label:Z("action.save"),isSubmitType:!0,disabled:se(),onClick:ue}),T&&r.a.createElement(d.a,{className:"bookmark-outline-save-button",label:Z("action.save"),isSubmitType:!0,onClick:function(){A(!1),z(n)}})))};N.propTypes=A;var j=N;t.a=j},1579:function(e,t,n){var o=n(32),r=n(1580);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.i,r,""]]);var i={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach(e=>o.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))}),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const r=t[o];if(0===o)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};o(r,i);e.exports=r.locals||{}},1580:function(e,t,n){(e.exports=n(33)(!1)).push([e.i,".TextButton{color:var(--secondary-button-text);width:-moz-fit-content;width:fit-content;padding-left:8px;padding-right:8px}.TextButton .Icon{display:flex;align-items:center}.TextButton svg{color:var(--secondary-button-text);height:14px;width:14px}.TextButton:hover{box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6);background-color:var(--faded-component-background)}",""])},1609:function(e,t,n){var o=n(32),r=n(1610);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.i,r,""]]);var i={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach(e=>o.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))}),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const r=t[o];if(0===o)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};o(r,i);e.exports=r.locals||{}},1610:function(e,t,n){(e.exports=n(33)(!1)).push([e.i,".outline-destination,.outline-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.outline-destination{flex-basis:100%;font-size:10px;color:var(--faded-text);margin-top:var(--padding-small)}.bookmark-outline-label-row .ToggleElementButton .Button{padding:0;min-width:16px}",""])},1611:function(e,t,n){var o=n(32),r=n(1612);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.i,r,""]]);var i={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach(e=>o.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))}),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const r=t[o];if(0===o)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};o(r,i);e.exports=r.locals||{}},1612:function(e,t,n){(t=e.exports=n(33)(!1)).push([e.i,':host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.outline-drag-container{border-radius:4px}.outline-drag-container.isNesting>.bookmark-outline-single-container{background-color:var(--popup-button-active);border-color:var(--focus-border)}.outline-treeview-toggle{flex-grow:0;flex-shrink:0;margin-right:var(--padding-small);margin-top:2px;margin-bottom:2px;min-width:14px;transition:transform .1s ease}.outline-treeview-toggle.expanded{transform:rotate(90deg)}.outline-treeview-toggle .Button{width:auto;height:auto}.outline-treeview-toggle .Button .Icon{width:16px;height:16px}.outline-drag-line{margin-left:var(--padding);margin-right:var(--padding);border-top:1px solid var(--focus-border);position:relative}.outline-drag-line:before{content:"";display:block;position:absolute;width:5px;height:5px;top:-3px;left:0;background-color:var(--focus-border);border-radius:50%}',""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1613:function(e,t,n){var o=n(32),r=n(1614);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.i,r,""]]);var i={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach(e=>o.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))}),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const r=t[o];if(0===o)r.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};o(r,i);e.exports=r.locals||{}},1614:function(e,t,n){(t=e.exports=n(33)(!1)).push([e.i,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .OutlinesPanel{margin:16px;padding:0;overflow-y:auto}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .OutlinesPanel{margin:16px;padding:0;overflow-y:auto}}.OutlinesPanel .bookmark-outline-single-container:not(.editing){height:-moz-fit-content;height:fit-content}.OutlinesPanel .bookmark-outline-row{padding-top:6px}.OutlinesPanel .msg-no-bookmark-outline{margin-top:6px}.OutlinesPanel.modular-ui-panel .bookmark-outline-single-container .panel-list-label-header .Button{width:-moz-fit-content;width:fit-content;height:100%;padding:0 8px;justify-content:start}.OutlinesPanel.modular-ui-panel .bookmark-outline-single-container .panel-list-label-header .Button:hover{cursor:pointer;border:none;border-radius:4px;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6);background-color:var(--faded-component-background)}.OutlinesPanel.modular-ui-panel .bookmark-outline-single-container .panel-list-label-header .Button:focus{border-radius:4px;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6);background-color:var(--faded-component-background)}.OutlinesPanel.modular-ui-panel .bookmark-outline-single-container .bookmark-outline-more-button.active{display:flex}.OutlinesPanel.modular-ui-panel .bookmark-outline-single-container .bookmark-outline-more-button.active .Icon{color:var(--blue-5)}.OutlinesPanel.modular-ui-panel .bookmark-outline-single-container.default.hover:not(.selected),.OutlinesPanel.modular-ui-panel .bookmark-outline-single-container.default:hover:not(.selected){background:none}.OutlinesPanel.modular-ui-panel .bookmark-outline-single-container.focus-visible,.OutlinesPanel.modular-ui-panel .bookmark-outline-single-container:focus-visible{outline:var(--focus-visible-outline)!important}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .OutlinesPanel.modular-ui-panel .bookmark-outline-single-container .bookmark-outline-input{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .OutlinesPanel.modular-ui-panel .bookmark-outline-single-container .bookmark-outline-input{font-size:13px}}.OutlinesPanel.modular-ui-panel .bookmark-outline-single-container .bookmark-outline-input:active{border-color:var(--focus-border)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .OutlinesPanel .bookmark-outline-cancel-button,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .OutlinesPanel .bookmark-outline-control-button,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .OutlinesPanel .bookmark-outline-save-button{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .OutlinesPanel .bookmark-outline-cancel-button,.App.is-web-component:not(.is-in-desktop-only-mode) .OutlinesPanel .bookmark-outline-control-button,.App.is-web-component:not(.is-in-desktop-only-mode) .OutlinesPanel .bookmark-outline-save-button{font-size:13px}}.Panel.OutlinesPanel{overflow:auto;-webkit-overflow-scrolling:touch;flex:1;flex-direction:column}",""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1651:function(e,t,n){"use strict";n.r(t);n(215),n(35),n(88),n(19),n(11),n(13),n(8),n(14),n(10),n(9),n(12),n(16),n(15),n(20),n(18);var o=n(0),r=n.n(o),i=n(17),a=n.n(i),l=n(4),u=n.n(l),c=n(6),s=n(1767),d=n(1768),p=n(576),f=n(1486),m=n(1),b=n(2),g=n(3),h=n(54),v=n(61),k=n(438),y=n(37),w=n(1513),O=n(1545),x=n(76);n(1611),n(1480);function E(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i,a,l=[],u=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;u=!1}else for(;!(u=(o=i.call(n)).done)&&(l.push(o.value),l.length!==t);u=!0);}catch(e){c=!0,r=e}finally{try{if(!u&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw r}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return T(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return T(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function T(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}var L={outline:u.a.object.isRequired,setMultiSelected:u.a.func,moveOutlineInward:u.a.func.isRequired,moveOutlineBeforeTarget:u.a.func.isRequired,moveOutlineAfterTarget:u.a.func.isRequired,connectDragSource:u.a.func,connectDragPreview:u.a.func,connectDropTarget:u.a.func,isDragging:u.a.bool,isDraggedUpwards:u.a.bool,isDraggedDownwards:u.a.bool},A=Object(o.forwardRef)((function(e,t){var n=e.outline,i=e.setMultiSelected,l=e.isDragging,u=e.isDraggedUpwards,s=e.isDraggedDownwards,d=e.connectDragSource,f=e.connectDragPreview,h=e.connectDropTarget,v=e.moveOutlineInward,T=e.moveOutlineBeforeTarget,L=e.moveOutlineAfterTarget,A=Object(c.e)((function(e){return g.a.getOutlines(e)})),N=Object(o.useContext)(w.a),j=N.setActiveOutlinePath,I=N.activeOutlinePath,P=N.isOutlineActive,S=N.setAddingNewOutline,_=N.isAddingNewOutline,C=N.isMultiSelectMode,D=N.shouldAutoExpandOutlines,R=N.isOutlineEditable,M=N.updateOutlines,B=k.a.getPath(n),F=E(Object(o.useState)(D),2),G=F[0],H=F[1],U=E(Object(o.useState)(!1),2),W=U[0],z=U[1],V=E(Object(o.useState)(!1),2),q=V[0],Y=V[1],K=E(Object(o.useState)(void 0),2),Z=K[0],$=K[1],J=Object(c.d)(),X=Object(o.useRef)(null);d(X),f(Object(p.a)(),{captureDraggingState:!0}),h(X);var Q=l?.5:1;Object(o.useImperativeHandle)(t,(function(){return{getNode:function(){return X.current}}})),Object(o.useEffect)((function(){null!==I&&I!==B&&I.startsWith(B)&&H(!0)}),[I,_,n]),Object(o.useLayoutEffect)((function(){H(D)}),[D]),Object(o.useLayoutEffect)((function(){z(!1),Y(!1),_&&I===B&&H(!0)}),[A]);var ee,te,ne,oe,re=Object(o.useCallback)((function(){m.a.goToOutline(n),j(B===I?null:B),_&&(S(!1),M()),Object(y.k)()&&J(b.a.closeElement("leftPanel"))}),[J,j,I,_,n]),ie=P(n);return r.a.createElement("div",{ref:!_&&C&&R?X:null,className:"outline-drag-container",style:{opacity:Q}},r.a.createElement("div",{className:"outline-drag-line",style:{opacity:u?1:0}}),r.a.createElement(x.a,{className:a()({"bookmark-outline-single-container":!0,editing:W||q,default:!W&&!q,selected:ie}),tabIndex:0,onKeyDown:function(e){"Enter"===e.key&&re(),e.stopPropagation()},onClick:function(e){e.stopPropagation(),W||q||1!==e.detail||$(setTimeout(re,300))},onDoubleClick:function(){W||q||clearTimeout(Z)}},r.a.createElement(O.a,{text:n.getName(),outlinePath:B,isOutlineRenaming:W,setOutlineRenaming:z,isOutlineChangingDest:q,setOutlineChangingDest:Y,textColor:n.color?(ee=n.color,te=255*ee.r,ne=255*ee.g,oe=255*ee.b,"rgb(".concat(te,", ").concat(ne,", ").concat(oe,")")):null,setMultiSelected:i,isExpanded:G,setIsExpanded:H,moveOutlineInward:v,moveOutlineBeforeTarget:T,moveOutlineAfterTarget:L},n.getChildren())),r.a.createElement("div",{className:"outline-drag-line",style:{opacity:s?1:0}}),_&&ie&&r.a.createElement(x.a,{className:"bookmark-outline-single-container editing"},r.a.createElement("div",{className:"outline-treeview-toggle",style:{marginLeft:12*k.a.getNestedLevel(n)}}),r.a.createElement(O.a,{isAdding:!0,text:"",onCancel:function(){return S(!1)}})))}));A.propTypes=L;var N=Object(s.a)(f.c.OUTLINE,{hover:function(e,t,n){if(n){var o=t.getItem();if(o){var r=o.dragOutline,i=o.dragSourceNode,a=e.outline,l=n.getNode();if(i&&l){if(i.contains(l))return o.dropTargetNode=void 0,void(o.dropLocation=f.b.INITIAL);o.dropTargetNode=l;var u=r.index,c=a.index;if(r.parent!==a.parent||u!==c){var s=l.getBoundingClientRect(),d=(s.bottom-s.top)/2,p=t.getClientOffset().y-s.top;switch(!0){case p<=d+f.a&&p>=d-f.a:o.dropLocation=f.b.ON_TARGET_HORIZONTAL_MIDPOINT,t.isOver({shallow:!0})&&l.classList.add("isNesting"),setTimeout((function(){(null==o?void 0:o.dropTargetNode)!==l&&l.classList.remove("isNesting")}),100);break;case p>d+f.a:o.dropLocation=f.b.BELOW_TARGET,l.classList.remove("isNesting");break;case p<d-f.a:o.dropLocation=f.b.ABOVE_TARGET,l.classList.remove("isNesting");break;default:o.dropLocation=f.b.INITIAL,l.classList.remove("isNesting")}Object(v.a)(h.a.DRAG_OUTLINE,{targetOutline:a,draggedOutline:o.dragOutline,dropLocation:o.dropLocation})}}}}},drop:function(e,t,n){if(n){var o=t.getItem(),r=o.dragOutline,i=o.dropTargetNode,a=e.outline,l=e.moveOutlineInward,u=e.moveOutlineBeforeTarget,c=e.moveOutlineAfterTarget;if(i){switch(o.dropLocation){case f.b.ON_TARGET_HORIZONTAL_MIDPOINT:l(r,a);break;case f.b.ABOVE_TARGET:u(r,a);break;case f.b.BELOW_TARGET:c(r,a)}i.classList.remove("isNesting"),Object(v.a)(h.a.DROP_OUTLINE,{targetOutline:a,draggedOutline:r,dropLocation:o.dropLocation}),o.dropLocation=f.b.INITIAL}}}},(function(e,t){var n,o;return{connectDropTarget:e.dropTarget(),isDraggedUpwards:t.isOver({shallow:!0})&&(null===(n=t.getItem())||void 0===n?void 0:n.dropLocation)===f.b.ABOVE_TARGET,isDraggedDownwards:t.isOver({shallow:!0})&&(null===(o=t.getItem())||void 0===o?void 0:o.dropLocation)===f.b.BELOW_TARGET}}))(Object(d.a)(f.c.OUTLINE,{beginDrag:function(e,t,n){return{sourceId:t.sourceId,dragOutline:e.outline,dragSourceNode:n.getNode(),dropLocation:f.b.INITIAL}},canDrag:function(){return y.e?(console.warn("Drag and drop outlines for IE11 is not supported"),!1):!!m.a.isFullPDFEnabled()||(console.warn("Full API must be enabled to drag and drop outlines"),!1)}},(function(e,t){return{connectDragSource:e.dragSource(),connectDragPreview:e.dragPreview(),isDragging:t.isDragging()}}))(A));N.propTypes=L;var j=N;t.default=j},1663:function(e,t,n){"use strict";n.r(t);n(99),n(8),n(98),n(49),n(16),n(62),n(127),n(35),n(38),n(90),n(28),n(19),n(11),n(13),n(14),n(10),n(9),n(12),n(15),n(20),n(18),n(57),n(22),n(63),n(64),n(65),n(66),n(36),n(39),n(23),n(24),n(40);var o=n(0),r=n.n(o),i=n(6),a=n(428),l=n(1466),u=n(37),c=n(434),s=n(1542),d=n(1651),p=n(1513),f=n(44),m=n(1493),b=n(1545),g=n(76),h=n(1),v=n(438),k=n(92),y=n(5),w=n(72),O=n(2),x=n(3),E=(n(1480),n(1613),n(1757)),T=n(1486),L={position:"fixed",pointerEvents:"none",zIndex:99999,left:0,top:0,width:"100%",height:"100%"},A=function(e,t){if(!e||!t)return{display:"none"};var n=t.x,o=t.y,r="translate(calc(".concat(n,"px - 50%), calc(").concat(o,"px - 100%))");return{transform:r,WebkitTransform:r}},N=function(){var e=Object(E.a)((function(e){return{itemType:e.getItemType(),item:e.getItem(),isDragging:e.isDragging(),initialOffset:e.getInitialSourceClientOffset(),currentOffset:e.getClientOffset()}})),t=e.itemType,n=e.item,o=e.isDragging,i=e.initialOffset,a=e.currentOffset;return o?r.a.createElement("div",{style:L},r.a.createElement("div",{className:"bookmark-outline-single-container preview",style:A(i,a)},function(){if(!n)return null;var e=n.dragOutline;switch(t){case T.c.OUTLINE:return r.a.createElement(r.a.Fragment,null,e.getName());default:return null}}())):null},j=n(17),I=n.n(j);function P(e){return(P="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function S(e){return function(e){if(Array.isArray(e))return B(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||M(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */_=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},r="function"==typeof Symbol?Symbol:{},i=r.iterator||"@@iterator",a=r.asyncIterator||"@@asyncIterator",l=r.toStringTag||"@@toStringTag";function u(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,n){return e[t]=n}}function c(e,t,n,r){var i=t&&t.prototype instanceof p?t:p,a=Object.create(i.prototype),l=new T(r||[]);return o(a,"_invoke",{value:w(e,n,l)}),a}function s(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=c;var d={};function p(){}function f(){}function m(){}var b={};u(b,i,(function(){return this}));var g=Object.getPrototypeOf,h=g&&g(g(L([])));h&&h!==t&&n.call(h,i)&&(b=h);var v=m.prototype=p.prototype=Object.create(b);function k(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function y(e,t){var r;o(this,"_invoke",{value:function(o,i){function a(){return new t((function(r,a){!function o(r,i,a,l){var u=s(e[r],e,i);if("throw"!==u.type){var c=u.arg,d=c.value;return d&&"object"==P(d)&&n.call(d,"__await")?t.resolve(d.__await).then((function(e){o("next",e,a,l)}),(function(e){o("throw",e,a,l)})):t.resolve(d).then((function(e){c.value=e,a(c)}),(function(e){return o("throw",e,a,l)}))}l(u.arg)}(o,i,r,a)}))}return r=r?r.then(a,a):a()}})}function w(e,t,n){var o="suspendedStart";return function(r,i){if("executing"===o)throw new Error("Generator is already running");if("completed"===o){if("throw"===r)throw i;return A()}for(n.method=r,n.arg=i;;){var a=n.delegate;if(a){var l=O(a,n);if(l){if(l===d)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===o)throw o="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o="executing";var u=s(e,t,n);if("normal"===u.type){if(o=n.done?"completed":"suspendedYield",u.arg===d)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(o="completed",n.method="throw",n.arg=u.arg)}}}function O(e,t){var n=t.method,o=e.iterator[n];if(void 0===o)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,O(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),d;var r=s(o,e.iterator,t.arg);if("throw"===r.type)return t.method="throw",t.arg=r.arg,t.delegate=null,d;var i=r.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,d):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,d)}function x(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function E(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function T(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(x,this),this.reset(!0)}function L(e){if(e){var t=e[i];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,r=function t(){for(;++o<e.length;)if(n.call(e,o))return t.value=e[o],t.done=!1,t;return t.value=void 0,t.done=!0,t};return r.next=r}}return{next:A}}function A(){return{value:void 0,done:!0}}return f.prototype=m,o(v,"constructor",{value:m,configurable:!0}),o(m,"constructor",{value:f,configurable:!0}),f.displayName=u(m,l,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===f||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,m):(e.__proto__=m,u(e,l,"GeneratorFunction")),e.prototype=Object.create(v),e},e.awrap=function(e){return{__await:e}},k(y.prototype),u(y.prototype,a,(function(){return this})),e.AsyncIterator=y,e.async=function(t,n,o,r,i){void 0===i&&(i=Promise);var a=new y(c(t,n,o,r),i);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},k(v),u(v,l,"Generator"),u(v,i,(function(){return this})),u(v,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var o in t)n.push(o);return n.reverse(),function e(){for(;n.length;){var o=n.pop();if(o in t)return e.value=o,e.done=!1,e}return e.done=!0,e}},e.values=L,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(E),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function o(n,o){return a.type="throw",a.arg=e,t.next=n,o&&(t.method="next",t.arg=void 0),!!o}for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r],a=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var l=n.call(i,"catchLoc"),u=n.call(i,"finallyLoc");if(l&&u){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(e,t){for(var o=this.tryEntries.length-1;o>=0;--o){var r=this.tryEntries[o];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,d):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),d},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),E(n),d}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var r=o.arg;E(n)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:L(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),d}},e}function C(e,t,n,o,r,i,a){try{var l=e[i](a),u=l.value}catch(e){return void n(e)}l.done?t(u):Promise.resolve(u).then(o,r)}function D(e){return function(){var t=this,n=arguments;return new Promise((function(o,r){var i=e.apply(t,n);function a(e){C(i,o,r,a,l,"next",e)}function l(e){C(i,o,r,a,l,"throw",e)}a(void 0)}))}}function R(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i,a,l=[],u=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;u=!1}else for(;!(u=(o=i.call(n)).done)&&(l.push(o.value),l.length!==t);u=!0);}catch(e){c=!0,r=e}finally{try{if(!u&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw r}}return l}}(e,t)||M(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function M(e,t){if(e){if("string"==typeof e)return B(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?B(e,t):void 0}}function B(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}var F=function(){var e=R(Object(i.e)((function(e){return[x.a.isElementDisabled(e,y.a.OUTLINE_PANEL),x.a.getOutlines(e),x.a.getOutlineEditingEnabled(e),x.a.shouldAutoExpandOutlines(e),x.a.getCurrentPage(e),x.a.getPageLabels(e),x.a.getFeatureFlags(e)]}),i.c),7),t=e[0],n=e[1],E=e[2],T=e[3],L=e[4],A=e[5],j=e[6],P="Full Page",C="Area Selection",M={x:0,y:0},B=R(Object(o.useState)(P),2),F=B[0],G=B[1],H=R(Object(o.useState)(M),2),U=H[0],W=H[1],z=R(Object(o.useState)(L),2),V=z[0],q=z[1],Y=R(Object(o.useState)(!1),2),K=Y[0],Z=Y[1],$=R(Object(o.useState)(null),2),J=$[0],X=$[1],Q=R(Object(o.useState)(!1),2),ee=Q[0],te=Q[1],ne=R(Object(o.useState)({}),2),oe=ne[0],re=ne[1],ie=R(Object(o.useState)(!1),2),ae=ie[0],le=ie[1],ue=R(Object(o.useState)(!1),2),ce=ue[0],se=ue[1],de=R(Object(o.useState)([]),2),pe=de[0],fe=de[1],me=j.customizableUI,be=R(Object(a.a)(),1)[0],ge=Object(i.d)(),he=Object(o.useRef)(null),ve="OutlineDestinationCreateTool",ke=h.a.getTool(ve),ye=Object(o.useRef)();Object(o.useLayoutEffect)((function(){te(!1),null!==he.current&&(X(he.current),he.current=null),0===n.length&&se(!1)}),[n]),Object(o.useEffect)((function(){Z(h.a.isFullPDFEnabled()&&E)}),[E]),Object(o.useEffect)((function(){var e=Object.values(oe).some((function(e){return e}));le(e)}),[oe,n]),Object(o.useEffect)((function(){var e=function(e){G(e.IsText?e.getCustomData("trn-annot-preview"):C),W({x:e.X,y:e.Y}),q(e.PageNumber)},t=function(){h.a.getOutlines((function(e){ge(O.a.setOutlines(e))}))},n=function(){X(null)};return h.a.addEventListener("outlineSetDestination",e),window.addEventListener("outlineBookmarksChanged",t),h.a.addEventListener("documentLoaded",n),function(){h.a.removeEventListener("outlineSetDestination",e),window.removeEventListener("outlineBookmarksChanged",t),h.a.removeEventListener("documentLoaded",n)}}),[]),Object(o.useEffect)((function(){var e=function(e){e.target.classList.contains("bookmark-outline-row")&&(X(null),fe([]))};return ye.current&&ye.current.addEventListener("click",e),function(){ye.current&&ye.current.removeEventListener("click",e)}}),[]);var we=function(e,t){var n=t.x,o=t.y;return h.a.getDocumentViewer().getDocument().getViewerCoordinates(e,n,o)},Oe=function(){var e=D(_().mark((function e(t){var o,r,i,a,l,u,c;return _().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(o=we(V,U),r=o.x,i=o.y,a=t,[P,C].includes(F)||t?t||(a=be("message.untitled")):a=F.slice(0,40),l=h.a.getDocumentViewer().getDocument(),(u=l.getPageRotation(V)/90)!==window.Core.PageRotation.E_90&&u!==window.Core.PageRotation.E_270||(c=r,r=i,i=c),0!==n.length){e.next=11;break}return e.next=9,v.a.addRootOutline(a,V,r,i,0);case 9:e.next=13;break;case 11:return e.next=13,v.a.addNewOutline(a,J,V,r,i,0);case 13:xe();case 14:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),xe=function(){Object(k.e)(),h.a.getOutlines((function(e){ge(O.a.setOutlines(e))})),Te(),re({})},Ee=function(e){return v.a.getPath(e)},Te=function(){h.a.setToolMode(w.a),G(P),W(M),q(L),ke.clearOutlineDestination()},Le=function(){var e=D(_().mark((function e(t){var n,o,r,i,a,l;return _().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=we(V,U),o=n.x,r=n.y,i=h.a.getDocumentViewer().getDocument(),(a=i.getPageRotation(V)/90)!==window.Core.PageRotation.E_90&&a!==window.Core.PageRotation.E_270||(l=o,o=r,r=l),e.next=6,v.a.setOutlineDestination(t,V,o,r,0);case 6:he.current=t,xe();case 8:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();Object(o.useEffect)((function(){F===P&&q(L)}),[F,L]),Object(o.useEffect)((function(){ee?h.a.setToolMode(ve):Te()}),[ee]);var Ae=function(e,t,n){var o=Ee(e),r=Ee(t);fe([]),n.call(v.a,o,r).then((function(e){xe(),he.current=e})),h.a.goToOutline(e)},Ne=function(e,t){Ae(e,t,v.a.moveOutlineAfterTarget)},je=function(e,t){Ae(e,t,v.a.moveOutlineBeforeTarget)},Ie=function(e,t){Ae(e,t,v.a.moveOutlineInTarget)},Pe=function(){var e=D(_().mark((function e(t,n){return _().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,v.a.setOutlineName(t,n);case 2:xe();case 3:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),Se=function(){var e=D(_().mark((function e(t){var n;return _().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t.sort().reverse(),n={message:be("warning.deleteOutline.message"),title:be("warning.deleteOutline.title"),confirmBtnText:be("action.delete"),onConfirm:function(){var e=D(_().mark((function e(){var n,o;return _().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n=0;case 1:if(!(n<t.length)){e.next=8;break}return o=t[n],e.next=5,v.a.deleteOutline(o);case 5:n++,e.next=1;break;case 8:xe(),X(null),fe([]);case 11:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}()},ge(O.a.showWarningMessage(n));case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();return t?null:r.a.createElement("div",{className:I()("Panel OutlinesPanel bookmark-outline-panel",{"modular-ui-panel":me}),"data-element":y.a.OUTLINE_PANEL,ref:ye},r.a.createElement("div",{className:"bookmark-outline-panel-header"},r.a.createElement("h2",{className:"header-title"},be("component.outlinesPanel")),K&&(ce?r.a.createElement(m.a,{className:"bookmark-outline-control-button",dataElement:y.a.OUTLINE_MULTI_SELECT,label:be("option.bookmarkOutlineControls.done"),disabled:ee,onClick:function(){return se(!1)},ariaLabel:"".concat(be("option.bookmarkOutlineControls.done")," ").concat(be("action.edit"))}):r.a.createElement(m.a,{className:"bookmark-outline-control-button",dataElement:y.a.OUTLINE_MULTI_SELECT,label:be("option.bookmarkOutlineControls.edit"),disabled:ee||0===n.length,onClick:function(){se(!0),fe([])},ariaLabel:"".concat(be("action.edit")," ").concat(be("component.outlinesPanel"))}))),r.a.createElement(p.a.Provider,{value:{currentDestPage:A[V-1],currentDestText:F,setActiveOutlinePath:X,activeOutlinePath:J,isOutlineActive:function(e){return Ee(e)===J},setAddingNewOutline:te,isAddingNewOutline:ee,setEditingOutlines:re,editingOutlines:oe,selectedOutlines:pe,isAnyOutlineRenaming:ae,isMultiSelectMode:ce,shouldAutoExpandOutlines:T,isOutlineEditable:K,addNewOutline:Oe,updateOutlines:xe,renameOutline:Pe,updateOutlineDest:Le,removeOutlines:Se}},r.a.createElement(l.a,{backend:u.l?s.a:c.a},r.a.createElement(N,null),r.a.createElement("div",{className:"bookmark-outline-row"},!ee&&0===n.length&&r.a.createElement("div",{className:"msg msg-no-bookmark-outline"},be("message.noOutlines")),n.map((function(e){return r.a.createElement(d.default,{key:v.a.getOutlineId(e),outline:e,setMultiSelected:function(e,t){pe.find((function(t){return t===e}))?t||fe(pe.filter((function(t){return t!==e}))):t&&fe([].concat(S(pe),[e]))},moveOutlineInward:Ie,moveOutlineBeforeTarget:je,moveOutlineAfterTarget:Ne})})),ee&&null===J&&r.a.createElement(g.a,{className:"bookmark-outline-single-container editing"},r.a.createElement(b.a,{isAdding:!0,text:"",onCancel:function(){return te(!1)}})))),K&&r.a.createElement(g.a,{className:"bookmark-outline-footer",dataElement:y.a.OUTLINE_ADD_NEW_BUTTON_CONTAINER},ce?r.a.createElement(r.a.Fragment,null,r.a.createElement(f.a,{className:"multi-selection-button",img:"icon-menu-add",ariaLabel:"".concat(be("action.add")," ").concat(be("component.outlinesPanel")),disabled:pe.length>0||ee||ae,onClick:function(){return te(!0)}}),r.a.createElement(f.a,{className:"multi-selection-button",img:"icon-delete-line",disabled:0===pe.length||ae,onClick:function(){return Se(pe)}})):r.a.createElement(m.a,{className:"bookmark-outline-control-button add-new-button",img:"icon-menu-add",dataElement:y.a.OUTLINE_ADD_NEW_BUTTON,disabled:ee||ae,label:"".concat(be("action.add")," ").concat(be("component.outlinePanel")),onClick:function(){return te(!0)},ariaLabel:"".concat(be("action.add")," ").concat(be("component.outlinesPanel"))}))))},G=r.a.memo(F);t.default=G}}]);
//# sourceMappingURL=chunk.33.js.map