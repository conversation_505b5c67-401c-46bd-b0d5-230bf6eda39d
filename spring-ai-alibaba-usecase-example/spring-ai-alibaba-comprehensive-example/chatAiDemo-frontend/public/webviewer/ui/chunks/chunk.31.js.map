{"version": 3, "sources": ["webpack:///./src/ui/src/components/Spinner/Spinner.js", "webpack:///./src/ui/src/components/Spinner/index.js", "webpack:///./src/ui/src/components/Spinner/Spinner.scss?ed1d", "webpack:///./src/ui/src/components/Spinner/Spinner.scss", "webpack:///./src/ui/src/components/ReactSelectWebComponentProvider/ReactSelectWebComponentProvider.js", "webpack:///./src/ui/src/components/ReactSelectWebComponentProvider/index.js", "webpack:///./src/ui/src/helpers/applyRedactions.js", "webpack:///./src/ui/src/components/RedactionPanel/RedactionPanel.scss?ba2d", "webpack:///./src/ui/src/components/RedactionPanel/RedactionPanel.scss", "webpack:///./src/ui/src/components/RedactionPageGroup/RedactionItem/RedactionItem.scss?b17d", "webpack:///./src/ui/src/components/RedactionPageGroup/RedactionItem/RedactionItem.scss", "webpack:///./src/ui/src/components/RedactionPageGroup/RedactionPageGroup.scss?4f71", "webpack:///./src/ui/src/components/RedactionPageGroup/RedactionPageGroup.scss", "webpack:///./src/ui/src/components/CreatableMultiSelect/CreatableMultiSelect.scss?ed7b", "webpack:///./src/ui/src/components/CreatableMultiSelect/CreatableMultiSelect.scss", "webpack:///./src/ui/src/components/RedactionSearchOverlay/RedactionSearchMultiSelect/RedactionSearchMultiSelect.scss?fa75", "webpack:///./src/ui/src/components/RedactionSearchOverlay/RedactionSearchMultiSelect/RedactionSearchMultiSelect.scss", "webpack:///./src/ui/src/components/RedactionSearchOverlay/RedactionSearchOverlay.scss?d5bb", "webpack:///./src/ui/src/components/RedactionSearchOverlay/RedactionSearchOverlay.scss", "webpack:///./src/ui/src/components/RedactionSearchResultGroup/RedactionSearchResult/RedactionSearchResult.scss?16f3", "webpack:///./src/ui/src/components/RedactionSearchResultGroup/RedactionSearchResult/RedactionSearchResult.scss", "webpack:///./src/ui/src/components/RedactionSearchResultGroup/RedactionSearchResultGroup.scss?84f9", "webpack:///./src/ui/src/components/RedactionSearchResultGroup/RedactionSearchResultGroup.scss", "webpack:///./src/ui/src/components/RedactionSearchResults/RedactionSearchResults.scss?4fbd", "webpack:///./src/ui/src/components/RedactionSearchResults/RedactionSearchResults.scss", "webpack:///./src/ui/src/components/RedactionPanel/RedactionPanelContext.js", "webpack:///./src/ui/src/components/RedactionTextPreview/RedactionTextPreviewContainer.js", "webpack:///./src/ui/src/components/RedactionTextPreview/index.js", "webpack:///./src/ui/src/components/RedactionPageGroup/RedactionItem/RedactionItem.js", "webpack:///./src/ui/src/components/RedactionPageGroup/RedactionItem/RedactionItemContainer.js", "webpack:///./src/ui/src/components/RedactionPageGroup/RedactionItem/index.js", "webpack:///./src/ui/src/components/RedactionPageGroup/RedactionPageGroup.js", "webpack:///./src/ui/src/components/RedactionPageGroup/RedactionPageGroupContainer.js", "webpack:///./src/ui/src/components/RedactionPageGroup/index.js", "webpack:///./src/ui/src/components/RedactionPanel/RedactionPanel.js", "webpack:///./src/ui/src/components/CreatableMultiSelect/CreatableMultiSelect.js", "webpack:///./src/ui/src/components/CreatableMultiSelect/index.js", "webpack:///./src/ui/src/components/RedactionSearchOverlay/RedactionSearchMultiSelect/RedactionSearchMultiSelect.js", "webpack:///./src/ui/src/components/RedactionSearchOverlay/RedactionSearchMultiSelect/index.js", "webpack:///./src/ui/src/components/RedactionSearchOverlay/RedactionSearchOverlay.js", "webpack:///./src/ui/src/helpers/multiSearch.js", "webpack:///./src/ui/src/components/RedactionSearchOverlay/RedactionSearchOverlayContainer.js", "webpack:///./src/ui/src/components/RedactionSearchOverlay/index.js", "webpack:///./src/ui/src/components/RedactionSearchResultGroup/RedactionSearchResult/RedactionSearchResult.js", "webpack:///./src/ui/src/components/RedactionSearchResultGroup/RedactionSearchResult/index.js", "webpack:///./src/ui/src/components/RedactionSearchResultGroup/RedactionSearchResult/RedactionSearchResultContainer.js", "webpack:///./src/ui/src/components/RedactionSearchResultGroup/RedactionSearchResultGroup.js", "webpack:///./src/ui/src/components/RedactionSearchResultGroup/index.js", "webpack:///./src/ui/src/constants/searchStatus.js", "webpack:///./src/ui/src/components/RedactionSearchResults/RedactionSearchResults.js", "webpack:///./src/ui/src/components/RedactionSearchResults/RedactionSearchResultsContainer.js", "webpack:///./src/ui/src/components/RedactionSearchResults/index.js", "webpack:///./src/ui/src/components/RedactionSearchPanel/RedactionSearchPanel.js", "webpack:///./src/ui/src/hooks/useOnRedactionSearchCompleted/useOnRedactionSearchCompleted.js", "webpack:///./src/ui/src/hooks/useOnRedactionSearchCompleted/index.js", "webpack:///./src/ui/src/components/RedactionSearchPanel/index.js", "webpack:///./src/ui/src/components/RedactionSearchPanel/RedactionSearchPanelContainer.js", "webpack:///./src/ui/src/components/RedactionPanel/RedactionPanelContainer.js", "webpack:///./src/ui/src/components/RedactionPanel/index.js"], "names": ["Spinner", "height", "width", "spinnerStyle", "className", "style", "api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "ReactSelectWebComponentProvider", "children", "emotionCache", "useMemo", "container", "getRootNode", "createCache", "key", "value", "noop", "annotations", "onRedactionCompleted", "activeDocumentViewerKey", "dispatch", "core", "isWebViewerServerDocument", "webViewerServerApply", "webViewerApply", "applyRedactions", "then", "results", "url", "downloadPdf", "filename", "includeAnnotations", "externalURL", "console", "warn", "warning", "message", "i18next", "t", "title", "confirmBtnText", "onConfirm", "err", "fireError", "Promise", "resolve", "actions", "showWarningMessage", "RedactionPanelContext", "React", "createContext", "RedactionPanelProvider", "useState", "selectedRedactionItemId", "setSelectedRedactionItemId", "isRedactionSearchActive", "setIsRedactionSearchActive", "activeSearchResultIndex", "setActiveSearchResultIndex", "useEffect", "onAnnotationSelected", "action", "redactionAnnotations", "filter", "annotation", "Subject", "selectedAnnotationId", "Id", "activeSearchResultChanged", "newActiveSearchResult", "newActiveSearchResultIndex", "getPageSearchResults", "findIndex", "searchResult", "isSearchResultEqual", "addEventListener", "removeEventListener", "Provider", "RedactionTextPreviewContainer", "props", "redactionPanelWidth", "useSelector", "state", "selectors", "getRedactionPanelWidth", "shallowEqual", "NoteTextPreview", "panelWidth", "comment", "RedactionItem", "isCustomUI", "getFeatureFlags", "customizableUI", "iconColor", "author", "dateFormat", "language", "onRedactionItemDelete", "onRedactionItemSelection", "textPreview", "isSelected", "timezone", "useTranslation", "date", "getLatestActivityDate", "datetimeStr", "toLocaleString", "timeZone", "Date", "redactionPreview", "formattedDate", "dayjs", "locale", "format", "dateAndAuthor", "classNames", "label", "icon", "redactionType", "redactionTypeMap", "RedactionTextPreview", "linesToBreak", "getContents", "<PERSON><PERSON>", "onClick", "aria<PERSON><PERSON><PERSON>", "aria<PERSON>urrent", "Icon", "glyph", "color", "OverlayText", "marginLeft", "img", "memo", "RedactionItemContainer", "useContext", "getNoteDateFormat", "getCurrentLanguage", "getCustomNoteSelectionFunction", "getTimezone", "customNoteSelectionFunction", "getCustomData", "StrokeColor", "toString", "getDisplayAuthor", "useCallback", "deselectAllAnnotations", "selectAnnotation", "jumpToAnnotation", "deleteAnnotations", "RedactionPageGroup", "pageNumber", "redactionItems", "CollapsibleSection", "header", "expansionDescription", "headingLevel", "map", "redactionItem", "propTypes", "PropTypes", "oneOfType", "number", "string", "array", "RedactionPageGroupContainer", "sortedRedactionItems", "setSortedRedactionItems", "getSortStrategies", "getSortedNotes", "RedactionPanel", "applyAllRedactions", "deleteAllRedactionAnnotations", "redactionTypesDictionary", "redactionPageMap", "setRedactionPageMap", "redactionPageNumbers", "setRedactionPageNumbers", "isTestMode", "mapAnnotationToRedactionType", "PageNumber", "undefined", "Object", "keys", "testModeProps", "noRedactionAnnotations", "redactAllButtonClassName", "disabled", "clearAllButtonClassName", "applyAllRedactionsWithFocusHandler", "useFocusHandler", "initialItemCount", "data", "itemContent", "index", "dataElement", "DataElements", "REDACT_ALL_MARKED_BUTTON", "CreatableMultiSelect", "id", "rest", "htmlFor", "onTouchEndCapture", "e", "stopPropagation", "is<PERSON><PERSON><PERSON>", "inputId", "defaultProps", "getColorForMode", "isDarkMode", "darkModeColor", "lightModeColor", "isFocused", "RedactionOption", "Option", "object", "isRequired", "MultiValueLabel", "tabIndex", "display", "CustomControl", "Control", "MultiValueRemove", "updatedProps", "innerProps", "onKeyDown", "event", "node", "RedactionSearchMultiSelect", "activeTheme", "redactionSearchOptions", "redactionGroup", "styles", "groupHeading", "base", "textTransform", "fontSize", "fontWeight", "COMMON_COLORS", "CUSTOM_UI_VARS", "paddingBottom", "paddingLeft", "paddingTop", "group", "padding", "menu", "borderRadius", "overflowY", "margin", "menuList", "backgroundColor", "multiValue", "whiteSpace", "multiValueRemove", "boxShadow", "option", "outline", "noOptionsMessage", "valueContainer", "maxHeight", "isMobileSize", "control", "minHeight", "borderColor", "placeholder", "input", "getStyles", "components", "IndicatorsContainer", "formatCreateLabel", "buildSearchOptions", "searchTerms", "textSearch", "caseSensitive", "searchTerm", "type", "regex", "ignoreCase", "RedactionSearchOverlay", "setSearchTerms", "executeRedactionSearch", "translatedOptions", "DataElementWrapper", "onFocus", "onCreateOption", "newValue", "textTerm", "updatedSearchTerms", "onChange", "multiSearch", "store", "getState", "redactionSearchPatterns", "getRedactionSearchPatterns", "searchOptionsMap", "reduce", "searchArray", "searchType", "searchRegex", "source", "searchString", "join", "searchTextFullFactory", "searchTextFull", "clearSearchResults", "multiSearchFactory", "RedactionSearchOverlayContainer", "useStore", "getActiveTheme", "values", "pattern", "RedactionSearchResult", "isChecked", "onClickResult", "isActive", "ambientStr", "displayResult", "resultStrStart", "resultStrEnd", "resultStr", "searchValue", "slice", "textBeforeSearchValue", "textAfterSearchValue", "displayRedactionSearchResult", "searchResultClassname", "active", "aria-label", "aria-current", "paddingRight", "Choice", "checked", "bool", "func", "RedactionSearchResultContainer", "checkResult", "setActiveSearchResult", "RedactionSearchResultGroup", "searchResults", "selectedSearchResultIndexes", "setSelectedSearchResultIndexes", "groupResultIndexes", "result", "allItemsChecked", "setAllItemsChecked", "allResultsSelected", "allSelected", "currentIndex", "checkAllResults", "target", "resultIndex", "RedactionSearchResults", "redactionSearchResults", "searchStatus", "onCancelSearch", "isProcessingRedactionResults", "markSelectedResultsForRedaction", "redactSelectedResults", "redactionSearchResultPageMap", "setRedactionSearchResultPageMap", "selectedSearchResultIndexesMap", "setSelectedSearchResultIndexesMap", "selectedIndexes", "setSelectedIndexes", "pageNum", "selectedIndexesMap", "redactionSearchResult", "noResults", "aria-live", "role", "isEmptyList", "resultsContainerClass", "emptyList", "redactAllButtonClass", "markAllForRedactionButtonClass", "shouldShowResultsCounterOptions", "SearchStatus", "flexGrow", "searchResultIndexMap", "resultGroupPageNumbers", "renderSearchResults", "ToolNames", "Core", "Tools", "defaultRedactionStyles", "Annotations", "Color", "TextColor", "Font", "createRedactionAnnotations", "activeToolStyles", "FillColor", "FontSize", "TextAlign", "redaction", "RedactionAnnotation", "page_num", "Quads", "quads", "quad", "getPoints", "Utilities", "calculateAutoFontSize", "setContents", "result_str", "Author", "getCurrentUser", "setCustomData", "RedactionSearchResultsContainer", "useDispatch", "getActiveToolStyles", "getActiveToolName", "activeToolName", "tool", "getTool", "REDACTION", "alternativeDefaultStyles", "defaults", "includes", "getAnnotationManager", "addAnnotations", "RedactionSearchPanel", "clearRedactionSearchResults", "isMobile", "closeElement", "useOnRedactionSearchCompleted", "setSearchStatus", "setRedactionSearchResults", "setIsProcessingRedactionResults", "searchPatterns", "mapResultToType", "resultType", "find", "test", "onSearchResultsChanged", "mappedResults", "searchInProgressEventHandler", "isSearching", "setTimeout", "ReactionSearchPanelContainer", "RedactionPanelContainer", "isElementOpen", "isElementDisabled", "isInDesktopOnlyMode", "getCustomApplyRedactionsHandler", "isOpen", "isDisabled", "customApplyRedactionsHandler", "redactionAnnotationsList", "isCustomPanel", "defaultRedactionTypes", "closeRedactionPanel", "tempDataElement", "min<PERSON><PERSON><PERSON>", "renderNull", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timeout", "clearTimeout", "dataElementToUse", "originalApplyRedactions", "callOnRedactionCompleted", "RedactionPanelContainerWithProvider"], "mappings": "gHAceA,G,QAXC,SAAH,GAA4C,QAAtCC,cAAM,IAAG,SAAM,MAAEC,MAC5BC,EAAe,CACnBF,SACAC,WAHqC,IAAG,SAAM,GAMhD,OACE,yBAAKE,UAAU,UAAUC,MAAOF,MCRrBH,O,qBCFf,IAAIM,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,goCAAioC,KAG1pC0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,kFCeRC,EApByB,SAAH,GAAqB,IAAfC,EAAQ,EAARA,SAEnCC,EAAeC,mBAAQ,WAC3B,IAAMC,EAAYC,cAClB,OAAOC,YAAY,CACjBC,IAAK,0BACLH,gBAED,IAGH,OAAO7B,OAAOC,8BACZ,kBAAC,IAAa,CAACgC,MAAON,GACnBD,GAGHA,GCnBWD,O,wFCKf,SAASS,KAEM,aAACC,GAAW,IAAEC,EAAuB,UAAH,6CAAGF,EAAMG,EAA0B,UAAH,6CAAG,EAAC,OAAK,SAACC,GACzF,OAAIC,IAAKC,4BAEAC,EAAqBN,EAAaG,EAAUD,GAE9CK,EAAeP,EAAaC,EAAsBE,EAAUD,KAGrE,IAAMI,EAAuB,SAACN,EAAaG,EAAUD,GAAuB,OAAKE,IAAKI,gBAAgBR,EAAaE,GAAyBO,MAAK,SAACC,GAChJ,GAAIA,GAAWA,EAAQC,IACrB,OAAOC,YAAYT,EAAU,CAC3BU,SAAU,eACVC,oBAAoB,EACpBC,YAAaL,EAAQC,MAGzBK,QAAQC,KAAK,sDAGTV,EAAiB,SAACP,EAAaC,EAAsBE,EAAUD,GACnE,IAIMgB,EAAU,CACdC,QALcC,IAAQC,EAAE,kCAMxBC,MALYF,IAAQC,EAAE,+BAMtBE,eALqBH,IAAQC,EAAE,gBAM/BG,UAAW,WAMT,OALApB,IAAKI,gBAAgBR,EAAaE,GAC/BO,MAAK,WACJR,OACA,OACK,SAACwB,GAAG,OAAKC,YAAUD,MACrBE,QAAQC,YAInB,OAAOzB,EAAS0B,IAAQC,mBAAmBZ,M,qBC/C7C,IAAI7D,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,s5QAAu5Q,KAGh7Q0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAIhC,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEzB5B,EAAO2B,QAAU,EAAQ,GAAR,EAAqE,IAKxFR,KAAK,CAACnB,EAAOC,EAAI,6+BAA8+B,M,qBCLvgC,IAAIL,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,mkEAAokE,KAG7lE0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAIhC,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEzB5B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,yGAA0G,M,qBCLnI,IAAIL,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAqE,IAKxFR,KAAK,CAACnB,EAAOC,EAAI,o7CAAq7C,KAG98C0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAIhC,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,uhDAAwhD,KAGjjD0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAIhC,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEzB5B,EAAO2B,QAAU,EAAQ,GAAR,EAAqE,IAKxFR,KAAK,CAACnB,EAAOC,EAAI,2vBAA4vB,M,qBCLrxB,IAAIL,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEzB5B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,8cAA+c,M,qBCLxe,IAAIL,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,m+KAAs+K,KAG//K0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,i1CCNvB,IAAM0C,EAAwBC,IAAMC,gBAE9BC,EAAyB,SAAH,GAAqB,IAAf3C,EAAQ,EAARA,SAC4C,IAAd4C,mBAAS,MAAK,GAArEC,EAAuB,KAAEC,EAA0B,KACmB,IAAfF,oBAAS,GAAM,GAAtEG,EAAuB,KAAEC,EAA0B,KACgB,IAAZJ,oBAAU,GAAE,GAAnEK,EAAuB,KAAEC,EAA0B,KAE1DC,qBAAU,WACR,IAAMC,EAAuB,SAAC3C,EAAa4C,GACzC,GAAe,aAAXA,EAAuB,CACzB,IAAMC,EAAuB7C,EAAY8C,QAAO,SAACC,GAAU,MAA4B,WAAvBA,EAAWC,WAErEC,EAAuBJ,EAAqBzE,OAAS,EAAIyE,EAAqB,GAAGK,GAAK,KAC5Fb,EAA2BY,QAE3BZ,EAA2B,OAIzBc,EAA4B,SAACC,GACjC,GAAKA,EAAL,CAGA,IACMC,GADoBjD,IAAKkD,wBAA0B,IACJC,WAAU,SAACC,GAC9D,OAAOpD,IAAKqD,oBAAoBD,EAAcJ,MAEhDX,EAA2BY,KAM7B,OAHAjD,IAAKsD,iBAAiB,qBAAsBf,GAC5CvC,IAAKsD,iBAAiB,4BAA6BP,GAE5C,WACL/C,IAAKuD,oBAAoB,qBAAsBhB,GAC/CvC,IAAKuD,oBAAoB,4BAA6BR,MAEvD,IAEH,IAAMrD,EAAQ,CACZsC,0BACAC,6BACAC,0BACAC,6BACAC,2BAGF,OAAO,kBAACT,EAAsB6B,SAAQ,CAAC9D,MAAOA,GAAQP,I,62CC9CxD,ICHesE,EDGuB,SAACC,GACrC,IAAOC,EAKN,EAL6BC,aAC5B,SAACC,GAAK,MAAK,CACTC,IAAUC,uBAAuBF,MAEnCG,KACD,GALyB,GAO1B,OACE,kBAACC,EAAA,EAAe,KAAKP,EAAK,CAAEQ,WAAYP,EAAqBQ,SAAO,M,+hCEDxE,IAAMC,EAAgB,SAACV,GAErB,IAAOW,EAIN,EAJoBT,aACnB,SAACC,GAAK,YAAK,CACuB,QADvB,EACTC,IAAUQ,gBAAgBT,UAAM,aAAhC,EAAkCU,mBAErC,GAJgB,GAMfC,EAUEd,EAVFc,UACA7B,EASEe,EATFf,WACA8B,EAQEf,EARFe,OACAC,EAOEhB,EAPFgB,WACAC,EAMEjB,EANFiB,SACAC,EAKElB,EALFkB,sBACAC,EAIEnB,EAJFmB,yBACAC,EAGEpB,EAHFoB,YACAC,EAEErB,EAFFqB,WACAC,EACEtB,EADFsB,SAEM/D,EAAMgE,cAANhE,EAEJiE,EAAOC,YAAsBxC,GAEjC,GAAIqC,EAAU,CACZ,IAAMI,EAAcF,EAAKG,eAAe,QAAS,CAAEC,SAAUN,IAC7DE,EAAO,IAAIK,KAAKH,GAGlB,IASII,EATEC,EAAgBP,EAAOQ,IAAMR,GAAMS,OAAOhB,GAAUiB,OAAOlB,GAAczD,EAAE,wCAC3E4E,EAAgB,GAAH,OAAMpB,EAAM,cAAMgB,GAC/B1I,EAAY+I,IAAW,iBAAkB,CAAE,0BAA2Bf,GAAc,CAAE,aAAcV,IAExG0B,EAGEpD,EAHFoD,MAAK,EAGHpD,EAFFqD,YAAI,IAAG,yBAAsB,EAC7BC,EACEtD,EADFsD,cAsBF,OAhBET,EADES,IAAkBC,IAAuB,KAEzC,kBAACC,EAAoB,CAACC,aAAc,GACjCtB,GAGLmB,IAAkBC,IAA4B,WAC3CD,IAAkBC,IAAmC,kBACrDD,IAAkBC,IAAyB,QAC3CD,IAAkBC,IAAkC,iBACpDD,IAAkBC,IAA6C,2BAE/CjF,EAAE8E,GAEFpD,EAAW0D,cAI9B,wBAAItJ,UAAWA,GACb,kBAACuJ,EAAA,EAAM,CACLvJ,UAAU,wBACVwJ,QAAS1B,EACT2B,UAAS,UAAKhB,EAAgB,YAAIK,EAAa,YAAI5E,EAAE,kBACrDwF,YAAa1B,IAEf,yBAAKhI,UAAU,4BACb,kBAAC2J,EAAA,EAAI,CAACC,MAAOX,EAAMY,MAAOpC,KAE5B,yBAAKzH,UAAU,uBACb,yBAAKA,UAAU,0BACZyI,GAGD7C,EAAWkE,YACT,yBAAK9J,UAAU,6BACZkE,EAAE,+BAA+B,KAAG0B,EAAWkE,aACzC,KAEb,yBAAK9J,UAAU,8BACZ8I,IAGL,kBAACS,EAAA,EAAM,CACLvJ,UAAU,wBACVC,MAAO,CAAE8J,WAAY,QACrBC,IAAK,aACLR,QAAS3B,EACT4B,UAAS,UAAKhB,EAAgB,YAAIK,EAAa,YAAI5E,EAAE,sBAM9CW,MAAMoF,KAAK5C,G,+hCClG1B,ICLe6C,EDKgB,SAACvD,GAC9B,IAAQf,EAAee,EAAff,WAER,EAAgEuE,qBAAWvF,GAAnEK,EAAuB,EAAvBA,wBAAyBC,EAA0B,EAA1BA,2BAehC,IARG2B,aACF,SAACC,GAAK,MAAK,CACTC,IAAUqD,kBAAkBtD,GAC5BC,IAAUsD,mBAAmBvD,GAC7BC,IAAUuD,+BAA+BxD,GACzCC,IAAUwD,YAAYzD,MAExBG,KACD,GAZCU,EAAU,KACVC,EAAQ,KACR4C,EAA2B,KAC3BvC,EAAQ,KAWJF,EAAcnC,EAAW6E,cAAc,qBACvChD,EAAY7B,EAAW8E,YAAYC,WACnCjD,EAASzE,IAAK2H,iBAAiBhF,EAAmB,QAElDkC,EAA2B+C,uBAAY,WAC3CL,GAA+BA,EAA4B5E,GAC3D3C,IAAK6H,yBACL7H,IAAK8H,iBAAiBnF,GACtB3C,IAAK+H,iBAAiBpF,GACtBV,EAA2BU,EAAWG,MACrC,CAACH,IAEEiC,EAAwBgD,uBAAY,WACxC5H,IAAKgI,kBAAkB,CAACrF,MACvB,CAACA,IAEJ,OACE,kBAAC,EAAa,CACZ+B,WAAYA,EACZC,SAAUA,EACVK,SAAUA,EACVP,OAAQA,EACR9B,WAAYA,EACZ6B,UAAWA,EACXM,YAAaA,EACbF,sBAAuBA,EACvBC,yBAA0BA,EAC1BE,WAAY/C,IAA4BW,EAAWG,M,SE9CnDmF,G,QAAqB,SAACvE,GAC1B,IACEwE,EAEExE,EAFFwE,WACAC,EACEzE,EADFyE,eAGMlH,EAAMgE,cAANhE,EAQR,OACE,kBAACmH,EAAA,EAAkB,CACjBrL,UAAU,uBACVsL,OATW,WACb,MAAO,GAAP,OACKpH,EAAE,sBAAqB,YAAIiH,IAQ9BI,qBAAoB,UAAKrH,EAAE,sBAAqB,YAAIiH,EAAU,YAAIjH,EAAE,kCACpEsH,aAAc,GAEd,wBAAIxL,UAAU,mBACXoL,EAAeK,KAAI,SAACC,GAAa,OAChC,kBAAC,EAAa,CACZ9F,WAAY8F,EACZhJ,IAAG,UAAKgJ,EAAc3F,GAAE,YAAIoF,YAQxCD,EAAmBS,UAAY,CAC7BR,WAAYS,IAAUC,UAAU,CAACD,IAAUE,OAAQF,IAAUG,SAC7DX,eAAgBQ,IAAUI,OAGbd,Q,2wCC1Cf,ICFee,EDEqB,SAACtF,GAGnC,IAAQyE,EAAmBzE,EAAnByE,eAE4D,IAAZpG,mBAAS,IAAG,GAA7DkH,EAAoB,KAAEC,EAAuB,KAIpD,OAHA5G,qBAAU,WACR4G,EAAwBC,cAA8B,SAAEC,eAAejB,MACtE,CAACA,IAEF,kBAAC,EAAkB,GACjBA,eAAgBc,GACZvF,K,mnDEFV,IAsGe2F,EAtGQ,SAAC3F,GACtB,IACEjB,EAIEiB,EAJFjB,qBACA6G,EAGE5F,EAHF4F,mBACAC,EAEE7F,EAFF6F,8BACAC,EACE9F,EADF8F,yBAGMvI,EAAMgE,cAANhE,EACoD,IAAZc,mBAAS,IAAG,GAArD0H,EAAgB,KAAEC,EAAmB,KACwB,IAAZ3H,mBAAS,IAAG,GAA7D4H,EAAoB,KAAEC,EAAuB,KAG5CC,EAAe3C,qBAAWvF,GAA1BkI,WAERvH,qBAAU,WACR,IAAMmH,EAAmB,GACzBhH,EAAqBnE,SAAQ,SAACqE,GAC5B,IAAMsD,EAAgB6D,YAA6BnH,GACnD,EAAwB6G,EAAyBvD,IAAkB,CACjED,KAAM,2BACND,MAAO,gDAFDA,EAAK,EAALA,MAAOC,EAAI,EAAJA,KAIfrD,EAAWoD,MAAQA,EACnBpD,EAAWqD,KAAOA,EAClBrD,EAAWsD,cAAgBA,EAE3B,IAAMiC,EAAavF,EAAWoH,gBACOC,IAAjCP,EAAiBvB,GACnBuB,EAAiBvB,GAAc,CAACvF,GAEhC8G,EAAiBvB,GAAc,CAACvF,GAAU,SAAK8G,EAAiBvB,QAIpEwB,EAAoBD,GACpBG,EAAwBK,OAAOC,KAAKT,MACnC,CAAChH,IAEJ,IAGQ0H,EAmBFC,EACJ,yBAAKrN,UAAU,wBACb,6BACE,kBAAC2J,EAAA,EAAI,CAAC3J,UAAU,aAAa4J,MAAM,+BAErC,yBAAK5J,UAAU,OAAOkE,EAAE,uCAItBoJ,EAA2BvE,IAAW,oBAAqB,CAAEwE,SAA0C,IAAhC7H,EAAqBzE,SAC5FuM,EAA0BzE,IAAW,mBAAoB,CAAEwE,SAA0C,IAAhC7H,EAAqBzE,SAE1FwM,EAAqCC,YAAgBnB,GAC3D,OACE,oCACE,wBAAIvM,UAAU,4BACXkE,EAAE,mCACH,0CACQwB,EAAqBzE,OAAM,OAGpC2L,EAAqB3L,OAAS,GAxC3BmM,EAAgBN,EAAa,CAAEa,iBAAkBf,EAAqB3L,QAAW,GAErF,yBAAKjB,UAAU,6BACb,kBAAC,IAAQ,GACP4N,KAAMhB,EACNiB,YAAa,SAACC,EAAO3C,GACnB,OACE,kBAAC,EAAkB,CACjBzI,IAAKoL,EACL3C,WAAYA,EACZC,eAAgBsB,EAAiBvB,OAGnCiC,MA2ByDC,EACjE,yBAAKrN,UAAU,4BACb,kBAACuJ,EAAA,EAAM,CACLgE,SAA0C,IAAhC7H,EAAqBzE,OAC/BjB,UAAWwN,EACXhE,QAASgD,EACTxD,MAAO9E,EAAE,gCAEX,kBAACqF,EAAA,EAAM,CACLgE,SAA0C,IAAhC7H,EAAqBzE,OAC/BjB,UAAWsN,EACX9D,QAASiE,EACTM,YAAaC,IAAaC,yBAC1BjF,MAAO9E,EAAE,uC,wsBCvGnB,IAAMgK,GAAuB,SAAH,GAA+B,IAAzBC,EAAE,EAAFA,GAAInF,EAAK,EAALA,MAAUoF,EAAI,SAChD,OACE,kBAACjM,GAAA,EAA+B,KAC9B,2BAAOkM,QAASF,EAAInO,UAAU,gCAAgCgJ,GAC9D,yBAAKsF,kBAAmB,SAACC,GAAC,OAAKA,EAAEC,oBAC/B,kBAAC,KAAe,IACdC,SAAO,GACHL,EAAI,CACRM,QAASP,QAOnBD,GAAqBvC,UAAY,CAC/BwC,GAAIvC,IAAUG,OACd/C,MAAO4C,IAAUG,QAGnBmC,GAAqBS,aAAe,CAClCR,GAAI,GACJnF,MAAO,IAGMkF,IC7BAA,GD6BAA,G,42DErBf,IAAMU,GAAkB,SAACC,EAAYC,EAAeC,GAAqC,IAArBC,IAAY,UAAH,+CAC3E,OAAIA,EACKH,EAAaC,EAAgBC,EAE/B,eA0HHE,GAAkB,SAACtI,GACvB,IAAQiH,EAASjH,EAATiH,KACR,OACE,kBAAC,KAAWsB,OAAWvI,EACpBiH,EAAK3E,MAAQ,kBAACU,EAAA,EAAI,CAACC,MAAOgE,EAAK3E,OAC/B2E,EAAK5E,QAKZiG,GAAgBtD,UAAY,CAC1BiC,KAAMhC,IAAUuD,OAAOC,YAGzB,IAAMC,GAAkB,SAAH,GAAiB,IAAXzB,EAAI,EAAJA,KACzB,OACE,yBAAK0B,SAAU,EAAGrP,MAAO,CAAEsP,QAAS,OAAQ1P,OAAQ,SACjD+N,EAAK3E,MAAQ,kBAACU,EAAA,EAAI,CAACC,MAAOgE,EAAK3E,OAC/B2E,EAAK5E,QAKZqG,GAAgB1D,UAAY,CAC1BiC,KAAMhC,IAAUuD,OAAOC,YAGzB,IAAMI,GAAgB,SAAH,OAAMpN,EAAQ,EAARA,SAAauE,EAAK,gBACzC,kBAAC,KAAW8I,QAAY9I,EACtB,yBAAK3G,UAAU,uDACb,kBAAC2J,EAAA,EAAI,CAAC3J,UAAU,4CAA4C4J,MAAM,wBAEnExH,IAICsN,GAAmB,SAAC/I,GACxB,IAAQzC,EAAMgE,cAANhE,EACF8E,EAAQrC,EAAMiH,KAAK5E,MAUnB2G,EAAe,GAAH,MACbhJ,GAAK,IACRiJ,WAAY,GAAF,MACLjJ,EAAMiJ,YAAU,IACnB,aAAc,GAAF,OAAK1L,EAAE,iBAAgB,YAAI8E,GACvCsG,SAAU,EACVO,UAdkB,SAACC,GACH,UAAdA,EAAMpN,KAAiC,MAAdoN,EAAMpN,MAEjCoN,EAAMtB,kBACN7H,EAAMiJ,WAAWpG,gBAcrB,OACE,kBAAC,KAAWkG,iBAAqBC,IAIrCH,GAAc7D,UAAY,CACxBvJ,SAAUwJ,IAAUmE,MAGtB,IAAMC,GAA6B,SAACrJ,GAClC,IAAQzC,EAAMgE,cAANhE,EACA+L,EAAwCtJ,EAAxCsJ,YAAaC,EAA2BvJ,EAA3BuJ,uBAEfC,EAAiB,CACrB,CACEnH,MAAO9E,EAAE,iCACT1D,QAAS0P,IAKPE,EA/LU,SAACvB,GAAU,MAAM,CACjCwB,aAAc,SAACC,GAAI,gBACdA,GAAI,IACPC,cAAe,OACfC,SAAU,OACVC,WAAY,OACZ5G,MAAO+E,GAAgBC,EAAY6B,KAAqB,MAAGC,KAAe,eAC1EC,cAAe,MACfC,YAAa,MACbC,WAAY,UAEdC,MAAO,SAACT,GAAI,gBACPA,GAAI,IACPU,QAAS,SAEXC,KAAM,SAACX,GAAI,gBACNA,GAAI,IACPU,QAAS,kBACTE,aAAc,MACdC,UAAW,UACXC,OAAQ,OAEVC,SAAU,SAACf,GAAI,gBACVA,GAAI,IACPU,QAAS,MACTM,gBAAiB1C,GAAgBC,EAAY6B,KAAqB,MAAGA,KAAqB,OAC1FS,UAAW,UACXD,aAAc,SAEhBK,WAAY,SAACjB,GAAI,gBACZA,GAAI,IACPgB,gBAAiB1C,GAAgBC,EAAY6B,KAA6B,cAAGA,KAAqB,OAClGM,QAAS,UACTR,SAAU,OACVU,aAAc,MACdC,UAAW,SACXK,WAAY,SACZ3H,MAAO+E,GAAgBC,EAAY6B,KAAqB,MAAGC,KAAe,kBAE5Ec,iBAAkB,SAACnB,GAAI,gBAClBA,GAAI,IACPzG,MAAO6G,KAAqB,MAC5BQ,aAAc,MACdnH,WAAY,MACZiH,QAAS,MACT,UAAW,CACTM,gBAAiBZ,KAAqB,MACtCgB,UAAW,mBAAF,OAAqBhB,KAAqB,OACnD7G,MAAO6G,KAAqB,OAE9B,IAAO,CACL7G,MAAO6G,KAAqB,MAC5B7Q,OAAQ,OACRC,MAAO,WAGX6R,OAAQ,SAACrB,EAAM,GAAF,IAAItB,EAAS,EAATA,UAAS,gBACrBsB,GAAI,IACPf,QAAS,OACTiB,SAAU,OACVQ,QAAS,YACTY,QAAS5C,EAAY,oCAAiC/B,EACtD,UAAW,CACTqE,gBAAiB1C,GAAgBC,EAAY6B,KAA6B,cAAGC,KAAe,yBAC5F9G,MAAO6G,KAAqB,OAE9BY,gBAAiB1C,GAAgBC,EAAY6B,KAA6B,cAAGA,KAAqB,OAClGS,UAAW,UACXK,WAAY,SACZ,eAAgB,CACdN,aAAc,cACdN,cAAe,UAGnBiB,iBAAkB,SAACvB,GAAI,gBAClBA,GAAI,IACPzG,MAAO8G,KAAe,iBAExBmB,eAAgB,SAACxB,GAAI,gBAChBA,GAAI,IACPU,QAAS,MACTe,UAvFEC,eACK,OAEF,OAqFLb,UAAW,YAEbc,QAAS,SAAC3B,GAAI,gBACTA,GAAI,IACPgB,gBAAiB1C,GAAgBC,EAAY6B,KAAsB,OAAGA,KAAqB,OAC3FwB,UAAW,OACXC,YAAavD,GAAgBC,EAAY6B,KAAqB,MAAGA,KAAqB,OACtF,iBAAkB,CAChByB,YAAavD,GAAgBC,EAAY6B,KAAqB,MAAGA,KAAqB,QAGxF,UAAW,CACTyB,YAAavD,GAAgBC,EAAY6B,KAAqB,MAAGA,KAAqB,QAExFgB,UAAW,qBAEbU,YAAa,SAAC9B,GAAI,gBACbA,GAAI,IACPE,SAAU,OACV3G,MAAO+E,GAAgBC,EAAY6B,KAAqB,MAAGA,KAAqB,OAChFG,YAAa,SAEfwB,MAAO,SAAC/B,GAAI,gBACPA,GAAI,IACPE,SAAU,OACV3G,MAAO+E,GAAgBC,EAAY6B,KAAqB,MAAGC,KAAe,eAC1EE,YAAa,UAmFAyB,CADoB,SAAhBrC,GAGnB,OACE,kBAAC,GAAoB,IACnBzP,QAAS2P,EACTC,OAAQA,EACRmC,WAAY,CAAErD,OAAQD,GAAiBI,mBAAiBmD,oBAAqB,kBAAM,MAAM/C,QAASD,GAAeE,qBACjH0C,YAAa,GACbK,kBAAmB,SAAC9P,GAAK,gBAAQuB,EAAE,yBAAwB,YAAIvB,IAC/DwL,GAAG,gCACHnF,MAAO9E,EAAE,8CACLyC,KAKVqJ,GAA2BrE,UAAY,CACrCsE,YAAarE,IAAUG,OAAOqD,WAC9Bc,uBAAwBtE,IAAUI,MAAMoD,YAG3BY,IC1OAA,GD0OAA,G,+nFErOf,IAAM0C,GAAqB,SAACC,GAC1B,IAAMnS,EAAU,CACdoS,WAAY,GACZC,eAAe,GAGjB,OAAKF,GAILA,EAAYpR,SAAQ,SAACuR,GACnB,IAAQC,EAASD,EAATC,KACJA,IAAS5J,IAAuB,KAClC3I,EAAQoS,WAAWnR,KAAKqR,EAAW9J,OAEnCxI,EAAQuS,IAAQ,EAEdD,EAAWE,QACbxS,EAAQqS,cAAgBrS,EAAQqS,gBAAkBC,EAAWE,MAAMC,eAIhEzS,GAfEA,GA0EI0S,GAxDgB,SAACvM,GAC9B,IACEvB,EAMEuB,EANFvB,2BACAuN,EAKEhM,EALFgM,YACAQ,EAIExM,EAJFwM,eACAC,EAGEzM,EAHFyM,uBACAnD,EAEEtJ,EAFFsJ,YACAC,EACEvJ,EADFuJ,uBAEKhM,EAAqB,GAAhBgE,cAAgB,GAApB,GAEFmL,EAAoBnD,EAAuBzE,KAAI,SAACkG,GAAM,gBACvDA,GAAM,IACT3I,MAAO9E,EAAEyN,EAAO3I,YAwBlB,OACE,kBAACsK,GAAA,EAAkB,CACjBtT,UAAU,yBACV+N,YAAY,0BAEZ,kBAAC,GAA0B,CACzBwF,QAAS,kBAAMnO,GAA2B,IAC1CzC,MAAOgQ,EACPa,eAvBe,SAACC,GACpB,IAAMC,EAAW,CACf1K,MAAOyK,EACP9Q,MAAO8Q,EACPV,KAAM5J,IAAuB,MAIzBwK,EAAqB,GAAH,UADGhB,GAAe,IACO,CAAEe,IACnDP,EAAeQ,GACf,IAAMnT,EAAUkS,GAAmBiB,GACnCnT,EAAQqS,eAAgB,EACxBO,EAAuB5S,IAYnBoT,SA9Be,SAACD,GACpBR,EAAeQ,GACf,IAAMnT,EAAUkS,GAAmBiB,GACnCP,EAAuB5S,IA4BnByP,YAAaA,EACbC,uBAAwBmD,M,oxBChCjBQ,OA3Cf,SAAqBC,GACnB,OAAO,SAAqBnB,GAC1B,IACM7L,GAAQiN,EADOD,EAAbC,YAEFC,EAA0BjN,IAAUkN,2BAA2BnN,GAE/DoN,EAAmBhH,OAAOC,KAAK6G,GAAyBG,QAAO,SAAC1I,EAAK/I,GACzE,MAAwBsR,EAAwBtR,GAAxCsQ,EAAK,EAALA,MAER,OADAvH,EADmB,EAAJsH,MACHC,EACLvH,IACN,IAGGjL,EAAU,CACdwS,OAAO,EACPH,cAAeF,EAAYE,eAIvBuB,EAAc,GADGzB,EAAfC,YAIR1F,OAAOC,KAAKwF,GAAapR,SAAQ,SAAC8S,GAChC,IAAMC,EAAcJ,EAAiBG,GACjCC,GACFF,EAAY3S,KAAK6S,EAAYC,WAIjC,IAAMC,EAAeJ,EAAYK,KAAK,KAIjB,KAAjBD,EAKmBE,cACvBC,CAAeH,EAAchU,GAL3ByC,IAAK2R,uB,m9CC9BX,SAASxB,GAAuB5S,EAASsT,GACnBe,GAAmBf,EACvCD,CAAYrT,GAGd,ICZesU,GDYyB,SAACnO,GACvC,IAAQvB,EAA+B+E,qBAAWvF,GAA1CQ,2BACF0O,EAAQiB,cACR9E,EAAcpJ,aAAY,SAACC,GAAK,OAAKC,IAAUiO,eAAelO,MAC9DkN,EAA0BnN,aAAY,SAACC,GAAK,OAAKC,IAAUkN,2BAA2BnN,KAAQG,KAC9FiJ,EAAyBhD,OAAO+H,OAAOjB,GAAyBvI,KAAI,SAACyJ,GAAO,gBAC7EA,GAAO,IACVvS,MAAOuS,EAAQnC,UAGjB,OACE,kBAAC,GAAsB,IACrB3N,2BAA4BA,EAC5BgO,uBAAwB,eAAC5S,EAAU,UAAH,6CAAG,GAAE,OAAK4S,GAAuB5S,EAASsT,IAC1E7D,YAAaA,EACbC,uBAAwBA,GACpBvJ,K,yBEJJwO,I,QAAwB,SAACxO,GAC7B,IACEyO,EAMEzO,EANFyO,UACAxB,EAKEjN,EALFiN,SACAyB,EAIE1O,EAJF0O,cACAC,EAGE3O,EAHF2O,SACArM,EAEEtC,EAFFsC,KACAsM,EACE5O,EADF4O,WAGIC,EA3B6B,SAAC7O,GACpC,IAAQ4O,EAA8D5O,EAA9D4O,WAAYE,EAAkD9O,EAAlD8O,eAAgBC,EAAkC/O,EAAlC+O,aAAcC,EAAoBhP,EAApBgP,UAClD,GADsEhP,EAAToM,OAChD5J,IAAuB,KAAG,CACrC,IAAMyM,EAA6B,KAAfL,EAAoBI,EAAYJ,EAAWM,MAAMJ,EAAgBC,GAC/EI,EAAwBP,EAAWM,MAAM,EAAGJ,GAC5CM,EAAuBR,EAAWM,MAAMH,GAC9C,OACE,oCACGI,EACD,0BAAM9V,UAAU,gBAAgB4V,GAC/BG,GAIP,OAAOJ,EAaeK,CAA6BrP,GAC7CsP,EAAwBlN,IAAW,0BAA2B,CAAEmN,OAAQZ,IAE9E,OACE,wBAAItV,UAAWiW,GACb,4BACEjW,UAAU,iCACVwJ,QAAS6L,EACTc,aAAYZ,EACZa,eAAcd,IAEhB,yBAAKrV,MAAO,CAAEoW,aAAc,SAC1B,kBAACC,GAAA,EAAM,CACLH,aAAA,UAAeZ,GACfgB,QAASnB,EACTxB,SAAUA,KAGd,yBAAK3T,MAAO,CAAEoW,aAAc,SAC1B,kBAAC1M,EAAA,EAAI,CAACC,MAAOX,KAEf,yBAAKjJ,UAAU,gCACZwV,MAMTL,GAAsBxJ,UAAY,CAChCyJ,UAAWxJ,IAAU4K,KACrB5C,SAAUhI,IAAU6K,KACpBpB,cAAezJ,IAAU6K,KACzBnB,SAAU1J,IAAU4K,KACpBvN,KAAM2C,IAAUG,OAChBwJ,WAAY3J,IAAUG,QAGTlH,WAAMoF,KAAKkL,ICvEXuB,GCGwB,SAAC/P,GACtC,IACEN,EAGEM,EAHFN,aACAkQ,EAEE5P,EAFF4P,QACAI,EACEhQ,EADFgQ,YAGMtR,EAA4B8E,qBAAWvF,GAAvCS,wBAEAkQ,EAA2ElP,EAA3EkP,WAAYE,EAA+DpP,EAA/DoP,eAAgBC,EAA+CrP,EAA/CqP,aAAcC,EAAiCtP,EAAjCsP,UAAW1M,EAAsB5C,EAAtB4C,KAAM6E,EAAgBzH,EAAhByH,MAAOiF,EAAS1M,EAAT0M,KAEpEa,EAAW/I,uBAAY,SAACiF,GAC5B6G,EAAY7G,EAAOhC,KAClB,CAACA,EAAO6I,IAELtB,EAAgBxK,uBAAY,WAChC5H,IAAK2T,sBAAsBvQ,KAC1B,CAACA,IAEJ,OACE,kBAAC,GAAqB,CACpBkP,WAAYA,EACZE,eAAgBA,EAChBC,aAAcA,EACdC,UAAWA,EACX1M,KAAMA,EACN8J,KAAMA,EACNqC,UAAWmB,EACX3C,SAAUA,EACVyB,cAAeA,EACfC,SAAUjQ,IAA4ByI,K,yxEC5B5C,ICLe+I,GDKoB,SAAClQ,GAClC,IACEwE,EAIExE,EAJFwE,WACA2L,EAGEnQ,EAHFmQ,cACAC,EAEEpQ,EAFFoQ,4BACAC,EACErQ,EADFqQ,+BAGM9S,EAAMgE,cAANhE,EACF+S,EAAqBH,EAAcrL,KAAI,SAACyL,GAAM,OAAKA,EAAOpJ,SACH,KAAf9I,oBAAS,GAAM,GAAtDmS,EAAe,KAAEC,EAAkB,KAE1C7R,qBAAU,WACR,IAAM8R,EAAqBJ,EAAmB9C,QAAO,SAACmD,EAAaC,GACjE,OAAOR,EAA4BQ,IAAiBD,KACnD,GAEHF,EAAmBC,KAClB,CAACN,EAA6BE,IAEjC,IAAMO,EAAkB3M,uBAAY,SAACiF,GACnC,IAAMyG,EAAUzG,EAAM2H,OAAOlB,QAC7BU,EAAmB1V,SAAQ,SAACmW,GAC1BX,EAA4BW,GAAenB,KAE7Ca,EAAmBb,GACnBS,EAA+B,MAAKD,MACnC,CAACA,EAA6BE,IAE3BN,EAAc9L,uBAAY,SAACiF,EAAOhC,GACtC,IAAMyI,EAAUzG,EAAM2H,OAAOlB,QAC7BQ,EAA4BjJ,GAASyI,EACrCS,EAA+B,MAAKD,MACnC,CAACA,IAYJ,OACE,yBAAK/W,UAAU,wCACb,kBAACsW,GAAA,EAAM,CACLtW,UAAU,gDACVmW,aAAA,UAAejS,EAAE,sBAAqB,YAAIiH,GAC1CoL,QAASY,EACTvD,SAAU,SAAC9D,GACTA,EAAMtB,kBACNgJ,EAAgB1H,MAGpB,kBAACzE,EAAA,EAAkB,CAACC,OArBT,WACb,MAAO,GAAP,OACKpH,EAAE,sBAAqB,YAAIiH,IAmBMlL,MAf1B,CACZH,MAAO,QAc6CyL,qBAAoB,UAAKrH,EAAE,sBAAqB,YAAIiH,IACpG,wBAAInL,UAAU,4BACX8W,EAAcrL,KAAI,SAACpF,EAAcyH,GAAK,OACrC,kBAAC,GAAqB,CACpByI,QAASQ,EAA4B1Q,EAAayH,OAClD6I,YAAaA,EACbtQ,aAAcA,EACd3D,IAAG,UAAKoL,EAAK,YAAI3C,Y,WEtEhB,I,QACS,wBADT,GAEO,qBAFP,GAGA,c,8mDCuMAwM,OA/Lf,SAAgChR,GAC9B,IACEiR,EAMEjR,EANFiR,uBACAC,EAKElR,EALFkR,aACAC,EAIEnR,EAJFmR,eACAC,EAGEpR,EAHFoR,6BACAC,EAEErR,EAFFqR,gCACAC,EACEtR,EADFsR,sBAGM/T,EAAMgE,cAANhE,EAC4E,KAAZc,mBAAS,IAAG,GAA7EkT,EAA4B,KAAEC,EAA+B,KACoB,KAAZnT,mBAAS,IAAG,GAAjFoT,EAA8B,KAAEC,EAAiC,KACd,KAAZrT,mBAAS,IAAG,GAAnDsT,EAAe,KAAEC,EAAkB,KAGlCzL,EAAe3C,qBAAWvF,GAA1BkI,WAGRvH,qBAAU,WACR,IAAM2S,EAA+B,GACrCN,EAAuBrW,SAAQ,SAAC2V,EAAQpJ,GACtC,IAAM3C,EAAa+L,EAAOsB,QAC1BtB,EAAOpJ,MAAQA,OACkCb,IAA7CiL,EAA6B/M,GAC/B+M,EAA6B/M,GAAc,CAAC+L,GAE5CgB,EAA6B/M,GAAc,GAAH,UAAO+M,EAA6B/M,IAAW,CAAE+L,OAI7FiB,EAAgCD,GAEhC,IAAMO,EAAqB,GAC3Bb,EAAuBrW,SAAQ,SAACoB,EAAOmL,GACrC2K,EAAmB3K,IAAS,KAE9BuK,EAAkCI,KACjC,CAACb,IAGJrS,qBAAU,WACR,IAAM+S,EAAkBV,EAAuBjS,QAAO,SAAC+S,EAAuB5K,GAC5E,OAAOsK,EAA+BtK,MAGxCyK,EAAmBD,KAClB,CAACF,IAGJ,IA6BMO,EACJ,yBAAKxC,aAAYjS,EAAE,sBACjB,uBAAG0U,YAAU,YAAYC,KAAK,QAAQ7Y,UAAU,aAAakE,EAAE,uBAwC7D4U,EAAgD,IAAlClB,EAAuB3W,OAErC8X,EAAwBhQ,IAAW,qCAAsC,CAAEiQ,UAAWF,IACtFG,EAAuBlQ,IAAW,sBAAuB,CAAEwE,SAAqC,IAA3B+K,EAAgBrX,SACrFiY,EAAiCnQ,IAAW,oBAAqB,CAAEwE,SAAqC,IAA3B+K,EAAgBrX,SAC7FkY,EAAmCtB,IAAiBuB,KAAgCrB,GAAiCF,IAAiBuB,GAE5I,OACE,oCACE,yBAAKpZ,UAAU,qCACZ6X,IAAiBuB,IAChB,yBAAKnZ,MAAO,CAAEoZ,SAAU,IACtB,kBAACzZ,GAAA,EAAO,CAACC,OAAO,OAAOC,MAAM,UAEhCqZ,GACC,oCACE,yBAAKnZ,UAAU,oCACb,wBAAI4Y,YAAU,YAAYC,KAAK,QAAQ7Y,UAAU,aAC9CkE,EAAE,gCACH,0CAAY0T,EAAuB3W,OAAM,QAG7C,kBAACsI,EAAA,EAAM,CACLvJ,UAAW+I,IAAW,CACpB,SAAYuP,EAAgBrX,OAAS,IAEvCuI,QAnDa,WACvB,IAAM8P,EAAuB,GAC7B1B,EAAuBrW,SAAQ,SAACoB,EAAOmL,GACrCwL,EAAqBxL,IAAS,KAEhCuK,EAAkCiB,IA+CxB/L,SAAUuL,EACV9P,MAAO9E,EAAE,sBAEX,kBAACqF,EAAA,EAAM,CACLvJ,UAAW+I,IAAW,CACpB,SAAYuP,EAAgBrX,OAAS,IAEvCsM,SAAUuL,EACVtP,QApDe,WACzB,IAAM8P,EAAuB,GAC7B1B,EAAuBrW,SAAQ,SAACoB,EAAOmL,GACrCwL,EAAqBxL,IAAS,KAEhCuK,EAAkCiB,IAgDxBtQ,MAAO9E,EAAE,uBAIjB,yBAAKlE,UAAW+Y,EAAuBF,KAAK,QACzChB,IAAiBuB,IAxFtB,yBAAKjD,aAAYjS,EAAE,gCAChBA,EAAE,gCAwFC2T,IAAiBuB,IAAsCN,GAAef,GA7E5E,6BACE,kBAACnY,GAAA,EAAO,CAACC,OAAO,OAAOC,MAAM,UA6E1B+X,IAAiBuB,IAA+BN,IAAgBf,GAAgCY,GAC/Fd,IAAiBuB,IAAsCvB,IAAiBuB,KAnHpD,WAC1B,IAAMG,EAAyBrM,OAAOC,KAAK+K,GAC3C,GAAIqB,EAAuBtY,OAAS,EAAG,CAErC,IAAMmM,EAAgBN,EAAa,CAAEa,iBAAkB4L,EAAuBtY,QAAW,GACzF,OACE,kBAAC,IAAQ,IACP2M,KAAM2L,EACN1L,YAAa,SAACC,EAAO3C,GACnB,OACE,kBAAC,GAA0B,CACzBzI,IAAKoL,EACL3C,WAAYA,EACZ2L,cAAeoB,EAA6B/M,GAC5C4L,4BAA6BqB,EAC7BpB,+BAAgCqB,MAGlCjL,KAiGoGoM,IAE5G,yBAAKxZ,UAAU,mCACb,kBAACuJ,EAAA,EAAM,CACLC,QA9EgB,WACtB2O,EAAgC,IAChCL,KA6EM9O,MAAO9E,EAAE,iBACTlE,UAAU,WAEZ,kBAACuJ,EAAA,EAAM,CACLgE,SAAqC,IAA3B+K,EAAgBrX,OAC1B+H,MAAO9E,EAAE,qBACTlE,UAAWiZ,EACXzP,QA5DwB,WAC9ByO,EAAsBK,MA6DlB,kBAAC/O,EAAA,EAAM,CACLgE,SAAqC,IAA3B+K,EAAgBrX,OAC1B+H,MAAO9E,EAAE,kBACTuF,UAAWvF,EAAE,kBACblE,UAAWkZ,EACX1P,QAxEsB,WAC5BwO,EAAgCM,GAChCR,U,wwCCtHJ,IAAQ2B,GAAc/Y,OAAOgZ,KAAKC,MAA1BF,UAEKG,GAAyB,CACpC9P,YAAa,GACbY,YAAa,IAAIhK,OAAOgZ,KAAKG,YAAYC,MAAM,IAAK,EAAG,GACvDC,UAAW,IAAIrZ,OAAOgZ,KAAKG,YAAYC,MAAM,IAAK,EAAG,EAAG,GACxDE,KAAM,aAGD,SAASC,GAA2BnD,GAA0D,IAA3CoD,EAAmB,UAAH,6CAAGN,GAEzElP,EAOEwP,EAPFxP,YACAZ,EAMEoQ,EANFpQ,YACAqQ,EAKED,EALFC,UAAS,EAKPD,EAJFF,YAAI,IAAG,cAAW,EAClBD,EAGEG,EAHFH,UACAK,EAEEF,EAFFE,SACAC,EACEH,EADFG,UAEI3U,EAAuBoR,EAAcrL,KAAI,SAACyL,GAC9C,IAAMoD,EAAY,IAAI5Z,OAAOgZ,KAAKG,YAAYU,oBAuB9C,OAtBAD,EAAUtN,WAAakK,EAAOsD,SAC9BF,EAAUG,MAAQvD,EAAOwD,MAAMjP,KAAI,SAACkP,GAAI,OAAKA,EAAKC,eAClDN,EAAU5P,YAAcA,EACxB4P,EAAUxQ,YAAcA,EACxBwQ,EAAUH,UAAYA,EACtBG,EAAUN,KAAOA,EACjBM,EAAUF,SAAWA,EACjB1Z,OAAOgZ,KAAKG,YAAYgB,UAAUC,wBACpCR,EAAUF,SAAW1Z,OAAOgZ,KAAKG,YAAYgB,UAAUC,sBAAsBR,IAE/EA,EAAUP,UAAYA,EACtBO,EAAUD,UAAYA,EACtBC,EAAUS,YAAY7D,EAAO8D,YAC7BV,EAAUvH,KAAOmE,EAAOnE,KACxBuH,EAAUW,OAAShY,IAAKiY,iBAEJ,SAAhBhE,EAAOnE,MACTuH,EAAUa,cAAc,oBAAqBjE,EAAO8D,YAGtDV,EAAUa,cAAc,qBAAsBjE,EAAOnE,MAE9CuH,KAGT,OAAO5U,EAmCM0V,ICtFAA,GDsDf,SAAyCzU,GACvC,IAAQmR,EAAmBnR,EAAnBmR,eACF9U,EAAWqY,cAMC,KAJyBxU,aACzC,SAACC,GAAK,MAAK,CACTC,IAAUuU,oBAAoBxU,GAC9BC,IAAUwU,kBAAkBzU,MAC3BG,KAAa,GAJXiT,EAAgB,KAAEsB,EAAc,KAWjCxD,EAAkCnN,uBAAY,SAACiM,GACnD,IAAM2E,EAAOxY,IAAKyY,QAAQjC,GAAUkC,WAC9BC,EAA4BH,GAAQA,EAAKI,SAAYJ,EAAKI,SAAWjC,GAErElU,EAAuBuU,GAA2BnD,EADhC0E,EAAeM,SAAS,aAAe5B,EAAmB0B,GAExD3Y,IAAK8Y,uBACbC,eAAetW,KAChC,CAACwU,EAAkBsB,IAEtB,OACE,kBAAC,GAAsB,IACrBxD,gCAAiCA,EACjCC,sBAjB0B,SAACnB,GAC7B,IAAMpR,EAAuBuU,GAA2BnD,EAAe8C,IACvE5W,EAASK,YAAgBqC,EAAsBoS,MAgBzCnR,K,miCE3EV,IAuDesV,GAvDc,SAACtV,GAC5B,IAAM3D,EAAWqY,cACiC,KAAZrW,mBAAS,IAAG,GAA3C2N,EAAW,KAAEQ,EAAc,KAClC,EAAgEhJ,qBAAWvF,GAAnEO,EAAuB,EAAvBA,wBAAyBC,EAA0B,EAA1BA,2BAQ/BwS,EAIEjR,EAJFiR,uBACAG,EAGEpR,EAHFoR,6BACAmE,EAEEvV,EAFFuV,4BACArE,EACElR,EADFkR,aAGIsE,EAAWnK,eAMjB,OACE,oCACGmK,GACC,yBACEnc,UAAU,mBAEV,4BACEA,UAAU,uBACVwJ,QAZiB,WACzBxG,EAAS0B,IAAQ0X,aAAa,qBAatB,kBAACzS,EAAA,EAAI,CACHC,MAAM,sBACN5J,UAAU,iBAIlB,kBAAC,GAAsB,CACrB2S,YAAaA,EACbQ,eAAgBA,IAEjBhO,GACC,kBAAC,GAAsB,CACrByS,uBAAwBA,EACxBE,eA1Ce,WACrB3E,EAAe,IACf+I,IACA9W,GAA2B,IAwCrByS,aAAcA,EACdE,6BAA8BA,M,yiCCqCzBsE,IC5FAA,GDKf,WACE,IAAsF,KAA9CrX,mBAASoU,IAAqC,GAA/EvB,EAAY,KAAEyE,EAAe,KACoC,KAAZtX,mBAAS,IAAG,GAAjE4S,EAAsB,KAAE2E,EAAyB,KAC+B,KAAfvX,oBAAS,GAAM,GAAhF+S,EAA4B,KAAEyE,EAA+B,KAC9DxI,EAA0BnN,aAAY,SAACC,GAAK,OAAKC,IAAUkN,2BAA2BnN,KAAQG,KAE9FwV,EAAiBna,mBAAQ,WAC7B,OAAO4K,OAAOC,KAAK6G,GAAyBG,QAAO,SAAC1I,EAAK/I,GACvD,MAA8BsR,EAAwBtR,GAA9CsQ,EAAK,EAALA,MAAOD,EAAI,EAAJA,KAAM9J,EAAI,EAAJA,KAKrB,OAJAwC,EAAIsH,GAAQ,CACVC,QACA/J,QAEKwC,IACN,MACF,CAACuI,IAEE0I,EAAkB7R,uBAAY,SAACqM,GAEnC,IAAQvB,EAAcuB,EAAdvB,UAGFgH,EAFoBzP,OAAOC,KAAKsP,GAEDG,MAAK,SAACla,GAEzC,OADkB+Z,EAAe/Z,GAAzBsQ,MACK6J,KAAKlH,MAIpBuB,EAAOnE,UAAsB9F,IAAf0P,EAA2BxT,IAAuB,KAAIwT,EAEpE,IAA2E,GAAjCF,EAAevF,EAAOnE,OAAS,IAAjE9J,YAAI,IAAG,yBAAsB,EAErC,OADAiO,EAAOjO,KAAOA,EACPiO,IACN,CAACuF,IAEEP,EAA8BrR,uBAAY,WAC9C0R,EAA0B,IAC1BtZ,IAAK2R,qBACL4H,GAAgC,MAyClC,OAtCAjX,qBAAU,WACR,SAASuX,EAAuBvZ,GAC9B,IAAMwZ,EAAgBxZ,EAAQkI,IAAIiR,GAClCF,GAAgC,GAChCD,EAA0BQ,GAI5B,OADA9Z,IAAKsD,iBAAiB,uBAAwBuW,GACvC,WACL7Z,IAAKuD,oBAAoB,uBAAwBsW,MAElD,CAACjF,IAEJtS,qBAAU,WACR,SAASyX,EAA6BC,GAChCA,QAEFX,EAAgBlD,IACP6D,EACTX,EAAgBlD,KAEhBkD,EAAgBlD,IAIhB8D,YAAW,WACTV,GAAgC,KAC/B,MAMP,OAFAvZ,IAAKsD,iBAAiB,mBAAoByW,GAEnC,WACL/Z,IAAKuD,oBAAoB,mBAAoBwW,MAE9C,IAEI,CACLpF,yBACAG,+BACAmE,8BACArE,iBExFWsF,GCEsB,WACnC,MAKId,KAJFzE,EAAsB,EAAtBA,uBACAG,EAA4B,EAA5BA,6BACAmE,EAA2B,EAA3BA,4BACArE,EAAY,EAAZA,aAGF,OACE,kBAAC,GAAoB,CACnBD,uBAAwBA,EACxBG,6BAA8BA,EAC9BmE,4BAA6BA,EAC7BrE,aAAcA,K,ixECFb,IAAMuF,GAA0B,SAACzW,GACtC,IAiBC,KAVGE,aACF,SAACC,GAAK,MAAK,CACTC,IAAUsW,cAAcvW,EAAO,kBAC/BC,IAAUuW,kBAAkBxW,EAAO,kBACnCC,IAAUC,uBAAuBF,GACjCC,IAAUwW,oBAAoBzW,GAC9BC,IAAUyW,gCAAgC1W,GAC1CC,IAAUkN,2BAA2BnN,MAEvCG,KACD,GAhBCwW,EAAM,KACNC,EAAU,KACV9W,EAAmB,KACnB2W,EAAmB,KACnBI,EAA4B,KAC5B3J,EAAuB,KAanBmI,EAAWnK,eAET4L,EAAyDjX,EAAzDiX,yBAA0BC,EAA+BlX,EAA/BkX,cAAe9P,EAAgBpH,EAAhBoH,YAE3CtB,EAA2BnK,mBAAQ,WAUvC,OAAO,SATsB4K,OAAOC,KAAK6G,GAAyBG,QAAO,SAAC1I,EAAK/I,GAC7E,MAA8BsR,EAAwBtR,GAA9CsG,EAAK,EAALA,MAAO+J,EAAI,EAAJA,KAAM9J,EAAI,EAAJA,KAKrB,OAJAwC,EAAIsH,GAAQ,CACV/J,QACAC,QAEKwC,IACN,KAEkCqS,OACpC,CAAC9J,IAMEhR,EAAWqY,cAaX0C,EAAsB,WAC1B,IAAMC,EAAkBH,EAAgB9P,EAAc,iBACtD/K,EAAS0B,IAAQ0X,aAAa4B,KAe1B/d,EAAQ4d,IAAmBN,GAAuBpB,EACpD,GACA,CAAErc,MAAO,GAAF,OAAK8G,EAAmB,MAAMqX,SAAU,GAAF,OAAKrX,EAAmB,OAEjEzB,EAA4BgF,qBAAWvF,GAAvCO,wBAE2C,KAAfH,oBAAS,GAAM,GAA5CkZ,EAAU,KAAEC,EAAa,KAWhC,GATA5Y,qBAAU,WACR,IAAM6Y,EAAUlB,YAAW,WACzBiB,GAAeV,KACd,KACH,OAAO,WACLY,aAAaD,MAEd,CAACX,IAEAC,IAAgBD,GAAUS,IAAeL,EAC3C,OAAO,KAGT,IAAMS,EAAmBT,EAAgB9P,EAAc,iBAEvD,OACE,kBAACuF,GAAA,EAAkB,CAACvF,YAAauQ,EAAkBte,UAAU,uBAAuBC,MAAOA,IACvFsd,GAAuBpB,IAnCxB0B,GACC,yBAAK7d,UAAU,mBACb,yBAAKA,UAAU,uBAAuBwJ,QAASuU,GAC7C,kBAACpU,EAAA,EAAI,CAACC,MAAM,sBAAsB5J,UAAU,iBAiClD,kBAAC,GAAoB,OACnBmF,GACA,kBAAC,EAAc,CACbO,qBAAsBkY,EACtBnR,yBAA0BA,EAC1BF,mBA5DmB,WACzB,IAAMgS,EAA0B,WAC9B,IAAMC,EAA2BX,EAAgBE,EAAsB,aACvE/a,EAASK,YAAgBua,EAA0BY,KAEjDb,EACFA,EAA6BC,EAA0BW,GAEvDA,KAqDI/R,8BAlE8B,WACpCvJ,IAAKgI,kBAAkB2S,QAwE3BR,GAAwBzR,UAAY,CAClCiS,yBAA0BhS,IAAUI,MACpC6R,cAAejS,IAAU4K,KACzBzI,YAAanC,IAAUG,QAGzBqR,GAAwBzO,aAAe,CACrCkP,eAAe,EACf9P,YAAa,IAGf,IAQe0Q,GAR6B,SAAC9X,GAC3C,OACE,kBAAC,EAAsB,KACrB,kBAAC,GAA4BA,KCzIpB8X", "file": "chunks/chunk.31.js", "sourcesContent": ["import React from 'react';\nimport './Spinner.scss';\n\nconst Spinner = ({ height = '50px', width = '54px' }) => {\n  const spinnerStyle = {\n    height,\n    width,\n  };\n\n  return (\n    <div className=\"spinner\" style={spinnerStyle}></div>\n  );\n};\n\nexport default Spinner;", "import Spinner from './Spinner';\n\nexport default Spinner;\n", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./Spinner.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.spinner{border-top:5px solid var(--border);border:5px solid var(--border);border-top-color:var(--focus-border);border-radius:50%;animation:spin 1.2s ease infinite}@keyframes spin{0%{transform:rotate(0deg)}to{transform:rotate(1turn)}}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React, { useMemo } from 'react';\nimport createCache from '@emotion/cache';\nimport { CacheProvider } from '@emotion/react'; // Emotion's CacheProvider\nimport getRootNode from 'helpers/getRootNode';\n\nconst ReactSelectWebComponentProvider = ({ children }) => {\n  // Memoize the cache so it's created only once per container (shadow DOM root)\n  const emotionCache = useMemo(() => {\n    const container = getRootNode(); // Ensure we get the root node of the shadow DOM\n    return createCache({\n      key: 'wv-react-select-emotion', // Unique key for the cache\n      container, // Set the container to the shadow DOM root for styles insertion\n    });\n  }, []);\n\n  // Render with CacheProvider for emotion when inside a web component\n  return window.isApryseWebViewerWebComponent ? (\n    <CacheProvider value={emotionCache}>\n      {children}\n    </CacheProvider>\n  ) : (\n    children\n  );\n};\n\nexport default ReactSelectWebComponentProvider;", "import ReactSelectWebComponentProvider from './ReactSelectWebComponentProvider';\n\nexport default ReactSelectWebComponentProvider;", "import core from 'core';\nimport i18next from 'i18next';\n\nimport actions from 'actions';\nimport { fireError } from 'helpers/fireEvent';\nimport downloadPdf from 'helpers/downloadPdf';\n\nfunction noop() { }\n\nexport default (annotations, onRedactionCompleted = noop, activeDocumentViewerKey = 1) => (dispatch) => {\n  if (core.isWebViewerServerDocument()) {\n    // when are using Webviewer Server, it'll download the redacted document\n    return webViewerServerApply(annotations, dispatch, activeDocumentViewerKey);\n  }\n  return webViewerApply(annotations, onRedactionCompleted, dispatch, activeDocumentViewerKey);\n};\n\nconst webViewerServerApply = (annotations, dispatch, activeDocumentViewerKey) => core.applyRedactions(annotations, activeDocumentViewerKey).then((results) => {\n  if (results && results.url) {\n    return downloadPdf(dispatch, {\n      filename: 'redacted.pdf',\n      includeAnnotations: true,\n      externalURL: results.url,\n    });\n  }\n  console.warn('WebViewer Server did not return a valid result');\n});\n\nconst webViewerApply = (annotations, onRedactionCompleted, dispatch, activeDocumentViewerKey) => {\n  const message = i18next.t('warning.redaction.applyMessage');\n  const title = i18next.t('warning.redaction.applyTile');\n  const confirmBtnText = i18next.t('action.apply');\n\n  const warning = {\n    message,\n    title,\n    confirmBtnText,\n    onConfirm: () => {\n      core.applyRedactions(annotations, activeDocumentViewerKey)\n        .then(() => {\n          onRedactionCompleted();\n        })\n        .catch((err) => fireError(err));\n      return Promise.resolve();\n    },\n  };\n\n  return dispatch(actions.showWarningMessage(warning));\n};\n", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./RedactionPanel.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.RedactionPanel{padding:16px 16px 0;display:flex;flex-direction:column}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel{width:100%;height:100%;min-width:100%;padding:8px 0 0}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .close-container{display:flex;align-items:center;justify-content:flex-end;height:32px;width:100%;padding-right:12px}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .close-container .close-icon-container{padding:0;border:none;background-color:transparent;z-index:3;cursor:pointer}:host(:not([data-tabbing=true])) .App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .close-container .close-icon-container,html:not([data-tabbing=true]) .App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .close-container .close-icon-container{outline:none}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .close-container .close-icon-container .close-icon{width:24px;height:24px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel{width:100%;height:100%;min-width:100%;padding:8px 0 0}.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .close-container{display:flex;align-items:center;justify-content:flex-end;height:32px;width:100%;padding-right:12px}.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .close-container .close-icon-container{padding:0;border:none;background-color:transparent;z-index:3;cursor:pointer}:host(:not([data-tabbing=true])) .App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .close-container .close-icon-container,html:not([data-tabbing=true]) .App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .close-container .close-icon-container{outline:none}.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .close-container .close-icon-container .close-icon{width:24px;height:24px}}.RedactionPanel .marked-redaction-counter{flex:0 1 19px;margin-top:24px;margin-bottom:12px;font-size:16px}.RedactionPanel .marked-redaction-counter span{font-weight:400}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .marked-redaction-counter{margin:16px;font-size:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .marked-redaction-counter{margin:16px;font-size:16px}}.RedactionPanel .no-marked-redactions{display:flex;flex-direction:column;align-items:center;flex:1 1 auto}.RedactionPanel .no-marked-redactions .msg{text-align:center}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .no-marked-redactions .msg{line-height:15px;width:146px}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .no-marked-redactions .msg{line-height:15px;width:146px}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .no-marked-redactions .msg{line-height:15px;width:146px;margin-bottom:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .no-marked-redactions .msg{line-height:15px;width:146px;margin-bottom:32px}}.RedactionPanel .no-marked-redactions .empty-icon,.RedactionPanel .no-marked-redactions .empty-icon svg{width:65px;height:83px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .no-marked-redactions .empty-icon,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .no-marked-redactions .empty-icon svg{width:60px;height:60px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .no-marked-redactions .empty-icon,.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .no-marked-redactions .empty-icon svg{width:60px;height:60px}}.RedactionPanel .no-marked-redactions .empty-icon *{fill:var(--gray-5);color:var(--gray-5)}.RedactionPanel .redaction-panel-controls{flex:0 0 57px;margin-left:-16px;padding-top:13px;border-top:1px solid var(--divider);display:flex;background-color:var(--component-background);width:inherit;justify-content:flex-end;padding-right:16px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .redaction-panel-controls{margin:0 0 16px;padding:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .redaction-panel-controls{margin:0 0 16px;padding:16px}}.RedactionPanel .redaction-panel-controls .Button.redact-all-marked{padding:0;border:none;background-color:transparent;background-color:var(--primary-button);color:var(--primary-button-text);border-radius:4px;height:32px;width:90px}:host(:not([data-tabbing=true])) .RedactionPanel .redaction-panel-controls .Button.redact-all-marked,html:not([data-tabbing=true]) .RedactionPanel .redaction-panel-controls .Button.redact-all-marked{outline:none}.RedactionPanel .redaction-panel-controls .Button.redact-all-marked:hover:not(.disabled){background-color:var(--primary-button-hover)}.RedactionPanel .redaction-panel-controls .Button.redact-all-marked.disabled{opacity:.5}.RedactionPanel .redaction-panel-controls .Button.redact-all-marked.disabled span{color:var(--primary-button-text)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .redaction-panel-controls .Button.redact-all-marked{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .redaction-panel-controls .Button.redact-all-marked{font-size:13px}}.RedactionPanel .redaction-panel-controls .clear-all-marked{padding:0;color:var(--secondary-button-text);background-color:transparent;border:none;height:32px;width:70px;margin-right:8px;cursor:pointer}:host(:not([data-tabbing=true])) .RedactionPanel .redaction-panel-controls .clear-all-marked,html:not([data-tabbing=true]) .RedactionPanel .redaction-panel-controls .clear-all-marked{outline:none}.RedactionPanel .redaction-panel-controls .clear-all-marked:hover:not(.disabled){color:var(--secondary-button-hover)}.RedactionPanel .redaction-panel-controls .clear-all-marked.disabled{opacity:.5;cursor:not-allowed}.RedactionPanel .redaction-panel-controls .clear-all-marked.disabled span{color:var(--secondary-button-text)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .redaction-panel-controls .clear-all-marked{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .redaction-panel-controls .clear-all-marked{font-size:13px}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .redaction-panel-controls{left:0;margin-bottom:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .redaction-panel-controls{left:0;margin-bottom:16px}}.RedactionPanel .redaction-group-container{flex:1 1 auto}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .redaction-group-container{margin-right:4px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .redaction-group-container{margin-right:4px}}.RedactionPanel button.focus-visible,.RedactionPanel button:focus-visible{outline:var(--focus-visible-outline)}.ModularPanel-container .RedactionPanel{height:100%;padding:unset}.ModularPanel-container .RedactionPanel .redaction-panel-controls{margin-right:-16px;padding-bottom:16px}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../../node_modules/css-loader/index.js!../../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../../node_modules/sass-loader/dist/cjs.js!./RedactionItem.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".redaction-item{display:flex;align-items:center;padding:12px 16px;position:relative}.redaction-item:hover{background-color:var(--view-header-button-hover);cursor:pointer}.redaction-item.focus-visible,.redaction-item:focus-visible{outline:var(--focus-visible-outline)}.redaction-item.modular-ui:hover:not(:disabled):not(.disabled){background-color:transparent;box-shadow:inset 0 0 0 1px var(--hover-border)}.redaction-item .redaction-item-button{position:absolute;width:100%;height:100%;top:0;left:0}.redaction-item-selected{background-color:var(--view-header-button-active)!important}.redaction-item-selected.modular-ui{box-shadow:inset 0 0 0 1px var(--focus-border)}.redaction-item-info{flex:1;padding-left:18px;padding-right:20px}.redaction-item-preview{font-size:13px;color:var(--text-color)}.redaction-item-date-author{font-size:10px;color:var(--faded-text)}.redaction-item-label-text{font-size:10px;margin:2px 0}.redaction-item-delete.customUI:hover{box-shadow:inset 0 0 0 1px var(--hover-border)}\", \"\"]);\n\n// exports\n", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./RedactionPageGroup.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.redaction-items{margin:8px 2px 1px;background-color:var(--component-background);box-shadow:0 0 3px 0 var(--box-shadow);border-radius:4px;padding:0}.redaction-items>:first-child{padding-top:16px;border-radius:4px 4px 0 0}.redaction-items>:last-child{padding-bottom:16px;border-radius:0 0 4px 4px}.redaction-items>:only-child{padding-top:16px;padding-bottom:16px;border-radius:4px}.redaction-page-group{padding-top:12px;padding-bottom:12px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .redaction-page-group{padding-top:8px;padding-right:4px;padding-left:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .redaction-page-group{padding-top:8px;padding-right:4px;padding-left:16px}}.redaction-page-group h2{margin:0}.redaction-page-group h2 button{margin:0;font-size:13px;font-weight:400;color:var(--faded-text)}.redaction-page-group-header{display:flex;justify-content:space-between;align-items:baseline}.expand-arrow{height:16px;width:16px;display:flex;align-items:center;cursor:pointer}.expand-arrow .Icon{width:12px;height:12px}.expand-arrow.Button.custom-ui.icon-only:hover{box-shadow:inset 0 0 0 1px var(--hover-border)}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./CreatableMultiSelect.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".creatable-multi-select-label{display:inline-block;font-weight:700;margin-bottom:var(--padding-small)}\", \"\"]);\n\n// exports\n", "var api = require(\"!../../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../../node_modules/css-loader/index.js!../../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../../node_modules/sass-loader/dist/cjs.js!./RedactionSearchMultiSelect.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .redaction-search-multi-select{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .redaction-search-multi-select{font-size:13px}}.redaction-search-multi-select-search-icon-container{height:28px;align-self:flex-start;display:flex;align-items:center;margin:0 var(--padding-tiny)}.redaction-search-multi-select-search-icon-container .Icon{width:16px;height:16px}.custom-remove-button{display:flex;align-items:center}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./RedactionSearchOverlay.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionSearchOverlay{margin-left:16px;margin-right:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionSearchOverlay{margin-left:16px;margin-right:16px}}.RedactionSearchOverlay input{width:100%;padding:6px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionSearchOverlay .creatable-multi-select-label{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionSearchOverlay .creatable-multi-select-label{font-size:13px}}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../../node_modules/css-loader/index.js!../../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../../node_modules/sass-loader/dist/cjs.js!./RedactionSearchResult.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".redaction-search-result{display:flex;align-items:center;padding:12px;background-color:var(--component-background);border:1px solid transparent;border-radius:4px;box-shadow:0 0 3px var(--document-box-shadow);margin:8px 0;position:relative}.redaction-search-result .redaction-search-result-button{position:absolute;width:100%;height:100%;top:0;left:0;background:transparent;border:none;cursor:pointer}.redaction-search-result .Icon svg{transform:scale(1.2);padding-top:2px}.redaction-search-result .search-value{word-break:break-all;color:var(--secondary-button-text);font-weight:700}.redaction-search-result.active{background-color:transparent!important;border:1px solid var(--focus-border)}.redaction-search-result-info{font-size:13px;color:var(--text-color)}\", \"\"]);\n\n// exports\n", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./RedactionSearchResultGroup.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".redaction-search-results-page-number{display:flex;align-items:start;padding:8px 12px 4px}.redaction-search-results-page-number .redaction-search-results-page-number-checkbox{position:absolute;left:24px}.redaction-search-results-page-number .collapsible-page-group-header button{font-size:13px;font-weight:400;color:var(--faded-text);margin:0 0 0 32px;width:calc(100% - 32px)}.redaction-search-results-page-number .redaction-search-results{padding:0;margin:0}\", \"\"]);\n\n// exports\n", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./RedactionSearchResults.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.redaction-search-counter-controls{display:flex;flex-direction:row;margin-top:36px;font-size:var(--font-size-default);padding:16px;border:1px solid var(--lighter-border);background-color:var(--gray-0);border-radius:4px 4px 0 0;max-height:50px;min-height:50px;grid-column-gap:var(--padding-medium);-moz-column-gap:var(--padding-medium);column-gap:var(--padding-medium)}.redaction-search-counter-controls .redaction-search-results-counter{flex:2 1 auto}.redaction-search-counter-controls .redaction-search-results-counter span{font-weight:400}.redaction-search-counter-controls .spinner{margin:auto;flex:3 1 \\\"25px\\\"}.redaction-search-counter-controls button{padding:0;background-color:transparent;flex:1 1 auto;color:var(--secondary-button-text);border:none;cursor:pointer;height:100%;white-space:nowrap}:host(:not([data-tabbing=true])) .redaction-search-counter-controls button,html:not([data-tabbing=true]) .redaction-search-counter-controls button{outline:none}.redaction-search-counter-controls button:hover:not(:disabled){color:var(--secondary-button-hover)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .redaction-search-counter-controls button{font-size:var(--font-size-default)}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .redaction-search-counter-controls button{font-size:var(--font-size-default)}}.redaction-search-counter-controls button.disabled{opacity:.5}.redaction-search-counter-controls button.disabled span{color:var(--secondary-button-text)}.redaction-search-results-container{flex:1 1 auto;background-color:var(--gray-2);color:var(--faded-text);font-size:13px;border-left:1px solid var(--lighter-border);border-right:1px solid var(--lighter-border);display:flex;flex-direction:column}.redaction-search-no-results,.redaction-search-results-container.emptyList{justify-content:center;align-items:center}.redaction-search-panel-controls{display:flex;flex-direction:row;flex:0 1 52px;padding:12px;background-color:var(--component-background);border:1px solid var(--lighter-border);margin-bottom:16px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .redaction-search-panel-controls{margin-bottom:30px;font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .redaction-search-panel-controls{margin-bottom:30px;font-size:13px}}.redaction-search-panel-controls button{border:none;background-color:transparent;height:28px;padding:0 16px;cursor:pointer}:host(:not([data-tabbing=true])) .redaction-search-panel-controls button,html:not([data-tabbing=true]) .redaction-search-panel-controls button{outline:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .redaction-search-panel-controls button{height:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .redaction-search-panel-controls button{height:32px}}.redaction-search-panel-controls .Button{white-space:nowrap}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .redaction-search-panel-controls .Button{font-size:var(--font-size-default)}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .redaction-search-panel-controls .Button{font-size:var(--font-size-default)}}.redaction-search-panel-controls .Button.cancel{flex:2 1 auto;color:var(--secondary-button-text);border:none;cursor:pointer;margin-right:20px}.redaction-search-panel-controls .Button.cancel:hover:not(.disabled) span{color:var(--secondary-button-hover)}.redaction-search-panel-controls .Button.redact-all-selected{flex:1 1 auto;border:1px solid var(--secondary-button-text);border-radius:4px;margin-right:8px}.redaction-search-panel-controls .Button.redact-all-selected span{color:var(--secondary-button-text)}.redaction-search-panel-controls .Button.redact-all-selected.disabled{opacity:.5}.redaction-search-panel-controls .Button.redact-all-selected:hover:not(.disabled){border-color:var(--secondary-button-hover)}.redaction-search-panel-controls .Button.redact-all-selected:hover:not(.disabled) span{color:var(--secondary-button-hover)}.redaction-search-panel-controls .Button.mark-all-selected{flex:2 1 auto;background-color:var(--primary-button)!important;border:1px solid var(--primary-button);border-radius:4px}.redaction-search-panel-controls .Button.mark-all-selected span{color:var(--primary-button-text)}.redaction-search-panel-controls .Button.mark-all-selected:hover:not(.disabled){border-color:var(--primary-button-hover);background-color:var(--primary-button-hover)!important}.redaction-search-panel-controls .Button.mark-all-selected.disabled{opacity:.5}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React, { useState, useEffect } from 'react';\nimport core from 'core';\n\n\nconst RedactionPanelContext = React.createContext();\n\nconst RedactionPanelProvider = ({ children }) => {\n  const [selectedRedactionItemId, setSelectedRedactionItemId] = useState(null);\n  const [isRedactionSearchActive, setIsRedactionSearchActive] = useState(false);\n  const [activeSearchResultIndex, setActiveSearchResultIndex] = useState(-1);\n\n  useEffect(() => {\n    const onAnnotationSelected = (annotations, action) => {\n      if (action === 'selected') {\n        const redactionAnnotations = annotations.filter((annotation) => annotation.Subject === 'Redact');\n        // If multiple ones selected, we only use the first one\n        const selectedAnnotationId = redactionAnnotations.length > 0 ? redactionAnnotations[0].Id : null;\n        setSelectedRedactionItemId(selectedAnnotationId);\n      } else {\n        setSelectedRedactionItemId(null);\n      }\n    };\n\n    const activeSearchResultChanged = (newActiveSearchResult) => {\n      if (!newActiveSearchResult) {\n        return;\n      }\n      const coreSearchResults = core.getPageSearchResults() || [];\n      const newActiveSearchResultIndex = coreSearchResults.findIndex((searchResult) => {\n        return core.isSearchResultEqual(searchResult, newActiveSearchResult);\n      });\n      setActiveSearchResultIndex(newActiveSearchResultIndex);\n    };\n\n    core.addEventListener('annotationSelected', onAnnotationSelected);\n    core.addEventListener('activeSearchResultChanged', activeSearchResultChanged);\n\n    return () => {\n      core.removeEventListener('annotationSelected', onAnnotationSelected);\n      core.removeEventListener('activeSearchResultChanged', activeSearchResultChanged);\n    };\n  }, []);\n\n  const value = {\n    selectedRedactionItemId,\n    setSelectedRedactionItemId,\n    isRedactionSearchActive,\n    setIsRedactionSearchActive,\n    activeSearchResultIndex\n  };\n\n  return <RedactionPanelContext.Provider value={value}>{children}</RedactionPanelContext.Provider>;\n};\n\nexport { RedactionPanelProvider, RedactionPanelContext };\n", "import React from 'react';\nimport { useSelector, shallowEqual } from 'react-redux';\nimport selectors from 'selectors';\nimport NoteTextPreview from '../NoteTextPreview/NoteTextPreview';\n\nconst RedactionTextPreviewContainer = (props) => {\n  const [redactionPanelWidth] = useSelector(\n    (state) => [\n      selectors.getRedactionPanelWidth(state),\n    ],\n    shallowEqual,\n  );\n\n  return (\n    <NoteTextPreview {...props} panelWidth={redactionPanelWidth} comment />\n  );\n};\n\nexport default RedactionTextPreviewContainer;", "import RedactionTextPreviewContainer from './RedactionTextPreviewContainer';\n\nexport default RedactionTextPreviewContainer;", "import React from 'react';\nimport { useTranslation } from 'react-i18next';\nimport Icon from 'src/components/Icon';\nimport getLatestActivityDate from 'helpers/getLatestActivityDate';\nimport dayjs from 'dayjs';\nimport Button from 'components/Button';\nimport './RedactionItem.scss';\nimport RedactionTextPreview from 'components/RedactionTextPreview';\nimport classNames from 'classnames';\nimport { redactionTypeMap } from 'constants/redactionTypes';\nimport { useSelector } from 'react-redux';\nimport selectors from 'selectors';\n\nconst RedactionItem = (props) => {\n  // Remove if we get rid of legacy UI along with stylesheet changes\n  const [isCustomUI] = useSelector(\n    (state) => [\n      selectors.getFeatureFlags(state)?.customizableUI,\n    ]\n  );\n  const {\n    iconColor,\n    annotation,\n    author,\n    dateFormat,\n    language,\n    onRedactionItemDelete,\n    onRedactionItemSelection,\n    textPreview,\n    isSelected,\n    timezone,\n  } = props;\n  const { t } = useTranslation();\n\n  let date = getLatestActivityDate(annotation);\n\n  if (timezone) {\n    const datetimeStr = date.toLocaleString('en-US', { timeZone: timezone });\n    date = new Date(datetimeStr);\n  }\n\n  const formattedDate = date ? dayjs(date).locale(language).format(dateFormat) : t('option.notesPanel.noteContent.noDate');\n  const dateAndAuthor = `${author} - ${formattedDate}`;\n  const className = classNames('redaction-item', { 'redaction-item-selected': isSelected }, { 'modular-ui': isCustomUI });\n  const {\n    label,\n    icon = 'icon-form-field-text', // Default icon if none provided\n    redactionType\n  } = annotation;\n\n  let redactionPreview;\n\n  if (redactionType === redactionTypeMap['TEXT']) {\n    redactionPreview = (\n      <RedactionTextPreview linesToBreak={2}>\n        {textPreview}\n      </RedactionTextPreview>);\n  } else if (\n    redactionType === redactionTypeMap['FULL_PAGE']\n    || redactionType === redactionTypeMap['FULL_VIDEO_FRAME']\n    || redactionType === redactionTypeMap['REGION']\n    || redactionType === redactionTypeMap['AUDIO_REDACTION']\n    || redactionType === redactionTypeMap['FULL_VIDEO_FRAME_AND_AUDIO']\n  ) {\n    redactionPreview = t(label);\n  } else {\n    redactionPreview = annotation.getContents();\n  }\n\n  return (\n    <li className={className}>\n      <Button\n        className='redaction-item-button'\n        onClick={onRedactionItemSelection}\n        ariaLabel={`${redactionPreview} ${dateAndAuthor} ${t('action.select')}`}\n        ariaCurrent={isSelected}\n      />\n      <div className=\"redaction-icon-container\">\n        <Icon glyph={icon} color={iconColor} />\n      </div>\n      <div className=\"redaction-item-info\">\n        <div className=\"redaction-item-preview\">\n          {redactionPreview}\n        </div>\n        {\n          annotation.OverlayText ?\n            <div className=\"redaction-item-label-text\">\n              {t('option.stylePopup.labelText')}: {annotation.OverlayText}\n            </div> : null\n        }\n        <div className=\"redaction-item-date-author\">\n          {dateAndAuthor}\n        </div>\n      </div>\n      <Button\n        className='redaction-item-delete'\n        style={{ marginLeft: 'auto' }}\n        img={'icon-close'}\n        onClick={onRedactionItemDelete}\n        ariaLabel={`${redactionPreview} ${dateAndAuthor} ${t('action.delete')}`}\n      />\n    </li>\n  );\n};\n\nexport default React.memo(RedactionItem);", "import React, { useCallback, useContext } from 'react';\nimport { shallowEqual, useSelector } from 'react-redux';\nimport selectors from 'selectors';\nimport RedactionItem from './RedactionItem';\nimport core from 'core';\nimport { RedactionPanelContext } from '../../RedactionPanel/RedactionPanelContext';\n\nconst RedactionItemContainer = (props) => {\n  const { annotation } = props;\n\n  const { selectedRedactionItemId, setSelectedRedactionItemId } = useContext(RedactionPanelContext);\n\n  const [\n    dateFormat,\n    language,\n    customNoteSelectionFunction,\n    timezone,\n  ] = useSelector(\n    (state) => [\n      selectors.getNoteDateFormat(state),\n      selectors.getCurrentLanguage(state),\n      selectors.getCustomNoteSelectionFunction(state),\n      selectors.getTimezone(state)\n    ],\n    shallowEqual,\n  );\n\n  const textPreview = annotation.getCustomData('trn-annot-preview');\n  const iconColor = annotation.StrokeColor.toString();\n  const author = core.getDisplayAuthor(annotation['Author']);\n\n  const onRedactionItemSelection = useCallback(() => {\n    customNoteSelectionFunction && customNoteSelectionFunction(annotation);\n    core.deselectAllAnnotations();\n    core.selectAnnotation(annotation);\n    core.jumpToAnnotation(annotation);\n    setSelectedRedactionItemId(annotation.Id);\n  }, [annotation]);\n\n  const onRedactionItemDelete = useCallback(() => {\n    core.deleteAnnotations([annotation]);\n  }, [annotation]);\n\n  return (\n    <RedactionItem\n      dateFormat={dateFormat}\n      language={language}\n      timezone={timezone}\n      author={author}\n      annotation={annotation}\n      iconColor={iconColor}\n      textPreview={textPreview}\n      onRedactionItemDelete={onRedactionItemDelete}\n      onRedactionItemSelection={onRedactionItemSelection}\n      isSelected={selectedRedactionItemId === annotation.Id}\n    />\n  );\n};\n\nexport default RedactionItemContainer;", "import RedactionItemContainer from './RedactionItemContainer';\n\nexport default RedactionItemContainer;", "import React from 'react';\nimport PropTypes from 'prop-types';\nimport RedactionItem from './RedactionItem';\nimport CollapsibleSection from 'components/CollapsibleSection';\nimport { useTranslation } from 'react-i18next';\n\nimport './RedactionPageGroup.scss';\n\nconst RedactionPageGroup = (props) => {\n  const {\n    pageNumber,\n    redactionItems,\n  } = props;\n\n  const { t } = useTranslation();\n\n  const header = () => {\n    return (\n      `${t('option.shared.page')} ${pageNumber}`\n    );\n  };\n\n  return (\n    <CollapsibleSection\n      className=\"redaction-page-group\"\n      header={header}\n      expansionDescription={`${t('option.shared.page')} ${pageNumber} ${t('redactionPanel.redactionItems')}`}\n      headingLevel={2}\n    >\n      <ul className=\"redaction-items\">\n        {redactionItems.map((redactionItem) => (\n          <RedactionItem\n            annotation={redactionItem}\n            key={`${redactionItem.Id}-${pageNumber}`}\n          />\n        ))}\n      </ul>\n    </CollapsibleSection>\n  );\n};\n\nRedactionPageGroup.propTypes = {\n  pageNumber: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  redactionItems: PropTypes.array,\n};\n\nexport default RedactionPageGroup;", "import React, { useEffect, useState } from 'react';\nimport RedactionPageGroup from './RedactionPageGroup';\nimport { getSortStrategies } from 'constants/sortStrategies';\n\nconst RedactionPageGroupContainer = (props) => {\n  // Putting this in the container in case we want to allow users to change sort strategies\n  // which are stored in the application state\n  const { redactionItems } = props;\n  // Sorting strategies can be applied to any list of annotations\n  const [sortedRedactionItems, setSortedRedactionItems] = useState([]);\n  useEffect(() => {\n    setSortedRedactionItems(getSortStrategies()['position'].getSortedNotes(redactionItems));\n  }, [redactionItems]);\n  return (\n    <RedactionPageGroup\n      redactionItems={sortedRedactionItems}\n      {...props}\n    />\n  );\n};\n\nexport default RedactionPageGroupContainer;", "import RedactionPageGroupContainer from './RedactionPageGroupContainer';\n\nexport default RedactionPageGroupContainer;", "import React, { useEffect, useState, useContext } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport classNames from 'classnames';\nimport Icon from 'components/Icon';\nimport { Virtuoso } from 'react-virtuoso';\nimport { RedactionPanelContext } from './RedactionPanelContext';\nimport { mapAnnotationToRedactionType } from 'constants/redactionTypes';\nimport Button from 'components/Button';\n\nimport './RedactionPanel.scss';\nimport RedactionPageGroup from '../RedactionPageGroup';\nimport DataElements from 'src/constants/dataElement';\nimport useFocusHandler from 'hooks/useFocusHandler';\n\nconst RedactionPanel = (props) => {\n  const {\n    redactionAnnotations,\n    applyAllRedactions,\n    deleteAllRedactionAnnotations,\n    redactionTypesDictionary,\n  } = props;\n\n  const { t } = useTranslation();\n  const [redactionPageMap, setRedactionPageMap] = useState({});\n  const [redactionPageNumbers, setRedactionPageNumbers] = useState([]);\n  // The following prop is needed only for the tests to actually render a list of results\n  // it only is ever injected in the tests\n  const { isTestMode } = useContext(RedactionPanelContext);\n\n  useEffect(() => {\n    const redactionPageMap = {};\n    redactionAnnotations.forEach((annotation) => {\n      const redactionType = mapAnnotationToRedactionType(annotation);\n      const { label, icon } = redactionTypesDictionary[redactionType] || {\n        icon: 'icon-tool-redaction-area',\n        label: 'redactionPanel.redactionItem.regionRedaction'\n      };\n      annotation.label = label;\n      annotation.icon = icon;\n      annotation.redactionType = redactionType;\n\n      const pageNumber = annotation.PageNumber;\n      if (redactionPageMap[pageNumber] === undefined) {\n        redactionPageMap[pageNumber] = [annotation];\n      } else {\n        redactionPageMap[pageNumber] = [annotation, ...redactionPageMap[pageNumber]];\n      }\n    });\n\n    setRedactionPageMap(redactionPageMap);\n    setRedactionPageNumbers(Object.keys(redactionPageMap));\n  }, [redactionAnnotations]);\n\n  const renderRedactionPageGroups = () => {\n    // Needed for the tests to actually render a list of results\n    // Not needed for the actual app; if we set it it kills performance when there are a lot of annotations\n    const testModeProps = isTestMode ? { initialItemCount: redactionPageNumbers.length } : {};\n    return (\n      <div className=\"redaction-group-container\">\n        <Virtuoso\n          data={redactionPageNumbers}\n          itemContent={(index, pageNumber) => {\n            return (\n              <RedactionPageGroup\n                key={index}\n                pageNumber={pageNumber}\n                redactionItems={redactionPageMap[pageNumber]}\n              />);\n          }}\n          {...testModeProps}\n        />\n      </div>\n    );\n  };\n\n  const noRedactionAnnotations = (\n    <div className=\"no-marked-redactions\">\n      <div>\n        <Icon className=\"empty-icon\" glyph=\"icon-no-marked-redactions\" />\n      </div>\n      <div className=\"msg\">{t('redactionPanel.noMarkedRedactions')}</div>\n    </div>\n  );\n\n  const redactAllButtonClassName = classNames('redact-all-marked', { disabled: redactionAnnotations.length === 0 });\n  const clearAllButtonClassName = classNames('clear-all-marked', { disabled: redactionAnnotations.length === 0 });\n\n  const applyAllRedactionsWithFocusHandler = useFocusHandler(applyAllRedactions);\n  return (\n    <>\n      <h2 className=\"marked-redaction-counter\">\n        {t('redactionPanel.redactionCounter')}\n        <span>\n          {` (${redactionAnnotations.length})`}\n        </span>\n      </h2>\n      {redactionPageNumbers.length > 0 ? renderRedactionPageGroups() : noRedactionAnnotations}\n      <div className=\"redaction-panel-controls\">\n        <Button\n          disabled={redactionAnnotations.length === 0}\n          className={clearAllButtonClassName}\n          onClick={deleteAllRedactionAnnotations}\n          label={t('redactionPanel.clearMarked')}\n        />\n        <Button\n          disabled={redactionAnnotations.length === 0}\n          className={redactAllButtonClassName}\n          onClick={applyAllRedactionsWithFocusHandler}\n          dataElement={DataElements.REDACT_ALL_MARKED_BUTTON}\n          label={t('redactionPanel.redactAllMarked')}\n        />\n      </div>\n    </>\n  );\n};\n\nexport default RedactionPanel;", "import React from 'react';\nimport CreatableSelect from 'react-select/creatable';\nimport ReactSelectWebComponentProvider from '../ReactSelectWebComponentProvider';\nimport './CreatableMultiSelect.scss';\nimport PropTypes from 'prop-types';\n\nconst CreatableMultiSelect = ({ id, label, ...rest }) => {\n  return (\n    <ReactSelectWebComponentProvider>\n      <label htmlFor={id} className=\"creatable-multi-select-label\">{label}</label>\n      <div onTouchEndCapture={(e) => e.stopPropagation()}>\n        <CreatableSelect\n          isMulti\n          {...rest}\n          inputId={id}\n        />\n      </div>\n    </ReactSelectWebComponentProvider>\n  );\n};\n\nCreatableMultiSelect.propTypes = {\n  id: PropTypes.string,\n  label: PropTypes.string,\n};\n\nCreatableMultiSelect.defaultProps = {\n  id: '',\n  label: '',\n};\n\nexport default CreatableMultiSelect;", "import CreatableMultiSelect from './CreatableMultiSelect';\n\nexport default CreatableMultiSelect;", "import React from 'react';\nimport { components } from 'react-select';\nimport Icon from 'components/Icon';\nimport CreatableMultiSelect from 'components/CreatableMultiSelect';\nimport { useTranslation } from 'react-i18next';\nimport { COMMON_COLORS, CUSTOM_UI_VARS } from 'constants/commonColors';\nimport PropTypes from 'prop-types';\nimport './RedactionSearchMultiSelect.scss';\nimport { isMobileSize } from 'helpers/getDeviceSize';\n\nconst getColorForMode = (isDarkMode, darkModeColor, lightModeColor, isFocused = true) => {\n  if (isFocused) {\n    return isDarkMode ? darkModeColor : lightModeColor;\n  }\n  return 'transparent';\n};\n\nconst getContainerMaxHeight = () => {\n  if (isMobileSize()) {\n    return '55px';\n  }\n  return '70px';\n};\n\nconst getStyles = (isDarkMode) => ({\n  groupHeading: (base) => ({\n    ...base,\n    textTransform: 'none',\n    fontSize: '13px',\n    fontWeight: 'bold',\n    color: getColorForMode(isDarkMode, COMMON_COLORS['white'], CUSTOM_UI_VARS['text-color']),\n    paddingBottom: '8px',\n    paddingLeft: '8px',\n    paddingTop: '10px',\n  }),\n  group: (base) => ({\n    ...base,\n    padding: '0px',\n  }),\n  menu: (base) => ({\n    ...base,\n    padding: '0px 0px 0px 0px',\n    borderRadius: '4px',\n    overflowY: 'visible',\n    margin: '0'\n  }),\n  menuList: (base) => ({\n    ...base,\n    padding: '0px',\n    backgroundColor: getColorForMode(isDarkMode, COMMON_COLORS['black'], COMMON_COLORS['gray0']),\n    overflowY: 'visible',\n    borderRadius: '4px',\n  }),\n  multiValue: (base) => ({\n    ...base,\n    backgroundColor: getColorForMode(isDarkMode, COMMON_COLORS['blue1DarkMode'], COMMON_COLORS['gray2']),\n    padding: '2px 8px',\n    fontSize: '13px',\n    borderRadius: '4px',\n    overflowY: 'hidden',\n    whiteSpace: 'nowrap',\n    color: getColorForMode(isDarkMode, COMMON_COLORS['white'], CUSTOM_UI_VARS['text-color'])\n  }),\n  multiValueRemove: (base) => ({\n    ...base,\n    color: COMMON_COLORS['gray6'],\n    borderRadius: '4px',\n    marginLeft: '4px',\n    padding: '0px',\n    '&:hover': {\n      backgroundColor: COMMON_COLORS['gray2'],\n      boxShadow: `inset 0 0 0 1px ${COMMON_COLORS['blue6']}`,\n      color: COMMON_COLORS['gray6'],\n    },\n    'svg': {\n      color: COMMON_COLORS['gray6'],\n      height: '16px',\n      width: '16px',\n    },\n  }),\n  option: (base, { isFocused }) => ({\n    ...base,\n    display: 'flex',\n    fontSize: '13px',\n    padding: '6px 8px 0',\n    outline: isFocused ? 'var(--focus-visible-outline)' : undefined,\n    '&:hover': {\n      backgroundColor: getColorForMode(isDarkMode, COMMON_COLORS['blue1DarkMode'], CUSTOM_UI_VARS['primary-button-hover']),\n      color: COMMON_COLORS['gray0'],\n    },\n    backgroundColor: getColorForMode(isDarkMode, COMMON_COLORS['blue1DarkMode'], COMMON_COLORS['gray0']),\n    overflowY: 'visible',\n    whiteSpace: 'normal',\n    '&:last-child': {\n      borderRadius: '0 0 4px 4px',\n      paddingBottom: '6px',\n    },\n  }),\n  noOptionsMessage: (base) => ({\n    ...base,\n    color: CUSTOM_UI_VARS['text-color'],\n  }),\n  valueContainer: (base) => ({\n    ...base,\n    padding: '1px',\n    maxHeight: getContainerMaxHeight(),\n    overflowY: 'scroll',\n  }),\n  control: (base) => ({\n    ...base,\n    backgroundColor: getColorForMode(isDarkMode, COMMON_COLORS['gray10'], COMMON_COLORS['white']),\n    minHeight: '28px',\n    borderColor: getColorForMode(isDarkMode, COMMON_COLORS['gray8'], COMMON_COLORS['gray6']),\n    '&:focus-within': {\n      borderColor: getColorForMode(isDarkMode, COMMON_COLORS['gray8'], COMMON_COLORS['blue5']),\n    },\n    // override the default border color on focus\n    '&:hover': {\n      borderColor: getColorForMode(isDarkMode, COMMON_COLORS['gray8'], COMMON_COLORS['gray6']),\n    },\n    boxShadow: 'none !important',\n  }),\n  placeholder: (base) => ({\n    ...base,\n    fontSize: '13px',\n    color: getColorForMode(isDarkMode, COMMON_COLORS['gray7'], COMMON_COLORS['gray5']),\n    paddingLeft: '4px',\n  }),\n  input: (base) => ({\n    ...base,\n    fontSize: '13px',\n    color: getColorForMode(isDarkMode, COMMON_COLORS['white'], CUSTOM_UI_VARS['text-color']),\n    paddingLeft: '3px',\n  }),\n});\n\nconst RedactionOption = (props) => {\n  const { data } = props;\n  return (\n    <components.Option {...props}>\n      {data.icon && <Icon glyph={data.icon} />}\n      {data.label}\n    </components.Option>\n  );\n};\n\nRedactionOption.propTypes = {\n  data: PropTypes.object.isRequired,\n};\n\nconst MultiValueLabel = ({ data }) => {\n  return (\n    <div tabIndex={0} style={{ display: 'flex', height: '18px' }}>\n      {data.icon && <Icon glyph={data.icon} />}\n      {data.label}\n    </div>\n  );\n};\n\nMultiValueLabel.propTypes = {\n  data: PropTypes.object.isRequired,\n};\n\nconst CustomControl = ({ children, ...props }) => (\n  <components.Control {...props}>\n    <div className=\"redaction-search-multi-select-search-icon-container\">\n      <Icon className=\"redaction-search-multi-select-search-icon\" glyph=\"icon-header-search\" />\n    </div>\n    {children}\n  </components.Control>\n);\n\nconst MultiValueRemove = (props) => {\n  const { t } = useTranslation();\n  const label = props.data.label;\n\n  const handleKeyDown = (event) => {\n    if (event.key === 'Enter' || event.key === ' ') {\n      // Trigger the removal action when Enter or Space is pressed\n      event.stopPropagation();\n      props.innerProps.onClick();\n    }\n  };\n\n  const updatedProps = {\n    ...props,\n    innerProps: {\n      ...props.innerProps,\n      'aria-label': `${t('action.remove')} ${label}`,\n      tabIndex: 0,\n      onKeyDown: handleKeyDown, // Add the keydown handler for accessibility\n    }\n  };\n\n  return (\n    <components.MultiValueRemove {...updatedProps} />\n  );\n};\n\nCustomControl.propTypes = {\n  children: PropTypes.node,\n};\n\nconst RedactionSearchMultiSelect = (props) => {\n  const { t } = useTranslation();\n  const { activeTheme, redactionSearchOptions } = props;\n\n  const redactionGroup = [\n    {\n      label: t('redactionPanel.search.pattern'),\n      options: redactionSearchOptions,\n    },\n  ];\n\n  const isDarkMode = activeTheme === 'dark';\n  const styles = getStyles(isDarkMode);\n\n  return (\n    <CreatableMultiSelect\n      options={redactionGroup}\n      styles={styles}\n      components={{ Option: RedactionOption, MultiValueLabel, IndicatorsContainer: () => null, Control: CustomControl, MultiValueRemove }}\n      placeholder={''}\n      formatCreateLabel={(value) => `${t('component.searchPanel')} ${value}`}\n      id=\"redaction-search-multi-select\"\n      label={t('redactionPanel.redactionSearchPlaceholder')}\n      {...props}\n    />\n  );\n};\n\nRedactionSearchMultiSelect.propTypes = {\n  activeTheme: PropTypes.string.isRequired,\n  redactionSearchOptions: PropTypes.array.isRequired,\n};\n\nexport default RedactionSearchMultiSelect;\n", "import RedactionSearchMultiSelect from './RedactionSearchMultiSelect';\n\nexport default RedactionSearchMultiSelect;", "import React from 'react';\nimport DataElementWrapper from '../DataElementWrapper';\nimport RedactionSearchMultiSelect from './RedactionSearchMultiSelect';\nimport { redactionTypeMap } from 'constants/redactionTypes';\nimport './RedactionSearchOverlay.scss';\nimport { useTranslation } from 'react-i18next';\n\nconst buildSearchOptions = (searchTerms) => {\n  const options = {\n    textSearch: [],\n    caseSensitive: true,\n  };\n\n  if (!searchTerms) {\n    return options;\n  }\n\n  searchTerms.forEach((searchTerm) => {\n    const { type } = searchTerm;\n    if (type === redactionTypeMap['TEXT']) {\n      options.textSearch.push(searchTerm.label);\n    } else {\n      options[type] = true;\n    }\n    if (searchTerm.regex) {\n      options.caseSensitive = options.caseSensitive && !searchTerm.regex.ignoreCase;\n    }\n  });\n\n  return options;\n};\n\nconst RedactionSearchOverlay = (props) => {\n  const {\n    setIsRedactionSearchActive,\n    searchTerms,\n    setSearchTerms,\n    executeRedactionSearch,\n    activeTheme,\n    redactionSearchOptions,\n  } = props;\n  const [t] = useTranslation();\n\n  const translatedOptions = redactionSearchOptions.map((option) => ({\n    ...option,\n    label: t(option.label),\n  }));\n\n  const handleChange = (updatedSearchTerms) => {\n    setSearchTerms(updatedSearchTerms);\n    const options = buildSearchOptions(updatedSearchTerms);\n    executeRedactionSearch(options);\n  };\n\n  const handleCreate = (newValue) => {\n    const textTerm = {\n      label: newValue,\n      value: newValue,\n      type: redactionTypeMap['TEXT']\n    };\n    // Initially search terms are null so we safeguard against this\n    const nonNullSearchTerms = searchTerms || [];\n    const updatedSearchTerms = [...nonNullSearchTerms, textTerm];\n    setSearchTerms(updatedSearchTerms);\n    const options = buildSearchOptions(updatedSearchTerms);\n    options.caseSensitive = false;\n    executeRedactionSearch(options);\n  };\n\n  return (\n    <DataElementWrapper\n      className=\"RedactionSearchOverlay\"\n      dataElement=\"redactionSearchOverlay\"\n    >\n      <RedactionSearchMultiSelect\n        onFocus={() => setIsRedactionSearchActive(true)}\n        value={searchTerms}\n        onCreateOption={handleCreate}\n        onChange={handleChange}\n        activeTheme={activeTheme}\n        redactionSearchOptions={translatedOptions}\n      />\n\n    </DataElementWrapper>\n\n  );\n};\n\nexport default RedactionSearchOverlay;", "import searchTextFullFactory from '../apis/searchTextFull';\nimport selectors from 'selectors';\nimport core from 'core';\n\n\nfunction multiSearch(store) {\n  return function multiSearch(searchTerms) {\n    const { getState } = store;\n    const state = getState();\n    const redactionSearchPatterns = selectors.getRedactionSearchPatterns(state);\n    // collect all regexes into an array\n    const searchOptionsMap = Object.keys(redactionSearchPatterns).reduce((map, key) => {\n      const { regex, type } = redactionSearchPatterns[key];\n      map[type] = regex;\n      return map;\n    }, {});\n\n\n    const options = {\n      regex: true,\n      caseSensitive: searchTerms.caseSensitive,\n    };\n\n    const { textSearch } = searchTerms;\n    const searchArray = [...textSearch];\n\n    // Now we can map type to regex\n    Object.keys(searchTerms).forEach((searchType) => {\n      const searchRegex = searchOptionsMap[searchType];\n      if (searchRegex) {\n        searchArray.push(searchRegex.source);\n      }\n    });\n\n    const searchString = searchArray.join('|');\n\n    // If search string is empty we return and clear searches or we send the search logic\n    // into an infinte loop\n    if (searchString === '') {\n      core.clearSearchResults();\n      return;\n    }\n\n    const searchTextFull = searchTextFullFactory();\n    searchTextFull(searchString, options);\n  };\n}\n\nexport default multiSearch;", "import React, { useContext } from 'react';\nimport { useStore, useSelector, shallowEqual } from 'react-redux';\nimport selectors from 'selectors';\n\nimport RedactionSearchOverlay from './RedactionSearchOverlay';\nimport { RedactionPanelContext } from '../RedactionPanel/RedactionPanelContext';\nimport multiSearchFactory from '../../helpers/multiSearch';\n\n\nfunction executeRedactionSearch(options, store) {\n  const multiSearch = multiSearchFactory(store);\n  multiSearch(options);\n}\n\nconst RedactionSearchOverlayContainer = (props) => {\n  const { setIsRedactionSearchActive } = useContext(RedactionPanelContext);\n  const store = useStore();\n  const activeTheme = useSelector((state) => selectors.getActiveTheme(state));\n  const redactionSearchPatterns = useSelector((state) => selectors.getRedactionSearchPatterns(state), shallowEqual);\n  const redactionSearchOptions = Object.values(redactionSearchPatterns).map((pattern) => ({\n    ...pattern,\n    value: pattern.type,\n  }));\n\n  return (\n    <RedactionSearchOverlay\n      setIsRedactionSearchActive={setIsRedactionSearchActive}\n      executeRedactionSearch={(options = {}) => executeRedactionSearch(options, store)}\n      activeTheme={activeTheme}\n      redactionSearchOptions={redactionSearchOptions}\n      {...props}\n    />);\n};\n\nexport default RedactionSearchOverlayContainer;", "import RedactionSearchOverlayContainer from './RedactionSearchOverlayContainer';\n\nexport default RedactionSearchOverlayContainer;", "import React from 'react';\nimport { Choice } from '@pdftron/webviewer-react-toolkit';\nimport Icon from 'components/Icon';\nimport './RedactionSearchResult.scss';\nimport classNames from 'classnames';\nimport { redactionTypeMap } from 'constants/redactionTypes';\nimport PropTypes from 'prop-types';\n\n// Alternatively wrap this in useCallback and declare inside component\nconst displayRedactionSearchResult = (props) => {\n  const { ambientStr, resultStrStart, resultStrEnd, resultStr, type } = props;\n  if (type === redactionTypeMap['TEXT']) {\n    const searchValue = ambientStr === '' ? resultStr : ambientStr.slice(resultStrStart, resultStrEnd);\n    const textBeforeSearchValue = ambientStr.slice(0, resultStrStart);\n    const textAfterSearchValue = ambientStr.slice(resultStrEnd);\n    return (\n      <>\n        {textBeforeSearchValue}\n        <span className=\"search-value\">{searchValue}</span>\n        {textAfterSearchValue}\n      </>\n    );\n  }\n  return resultStr;\n};\n\nconst RedactionSearchResult = (props) => {\n  const {\n    isChecked,\n    onChange,\n    onClickResult,\n    isActive,\n    icon,\n    ambientStr\n  } = props;\n\n  const displayResult = displayRedactionSearchResult(props);\n  const searchResultClassname = classNames('redaction-search-result', { active: isActive });\n\n  return (\n    <li className={searchResultClassname}>\n      <button\n        className='redaction-search-result-button'\n        onClick={onClickResult}\n        aria-label={ambientStr}\n        aria-current={isActive}\n      ></button>\n      <div style={{ paddingRight: '14px' }}>\n        <Choice\n          aria-label={`${ambientStr}`}\n          checked={isChecked}\n          onChange={onChange}\n        />\n      </div>\n      <div style={{ paddingRight: '14px' }}>\n        <Icon glyph={icon} />\n      </div>\n      <div className=\"redaction-search-result-info\">\n        {displayResult}\n      </div>\n    </li>\n  );\n};\n\nRedactionSearchResult.propTypes = {\n  isChecked: PropTypes.bool,\n  onChange: PropTypes.func,\n  onClickResult: PropTypes.func,\n  isActive: PropTypes.bool,\n  icon: PropTypes.string,\n  ambientStr: PropTypes.string,\n};\n\nexport default React.memo(RedactionSearchResult);", "import RedactionSearchResultContainer from './RedactionSearchResultContainer';\n\nexport default RedactionSearchResultContainer;", "import React, { useCallback, useContext } from 'react';\nimport RedactionSearchResult from './RedactionSearchResult';\nimport { RedactionPanelContext } from 'components/RedactionPanel/RedactionPanelContext';\nimport core from 'core';\n\nconst RedactionSearchResultContainer = (props) => {\n  const {\n    searchResult,\n    checked,\n    checkResult,\n  } = props;\n\n  const { activeSearchResultIndex } = useContext(RedactionPanelContext);\n\n  const { ambientStr, resultStrStart, resultStrEnd, resultStr, icon, index, type } = searchResult;\n\n  const onChange = useCallback((event) => {\n    checkResult(event, index);\n  }, [index, checkResult]);\n\n  const onClickResult = useCallback(() => {\n    core.setActiveSearchResult(searchResult);\n  }, [searchResult]);\n\n  return (\n    <RedactionSearchResult\n      ambientStr={ambientStr}\n      resultStrStart={resultStrStart}\n      resultStrEnd={resultStrEnd}\n      resultStr={resultStr}\n      icon={icon}\n      type={type}\n      isChecked={checked}\n      onChange={onChange}\n      onClickResult={onClickResult}\n      isActive={activeSearchResultIndex === index}\n    />\n  );\n};\n\nexport default RedactionSearchResultContainer;\n", "import React, { useCallback, useEffect, useState } from 'react';\nimport RedactionSearchResult from './RedactionSearchResult';\nimport { Choice } from '@pdftron/webviewer-react-toolkit';\nimport { useTranslation } from 'react-i18next';\nimport CollapsibleSection from 'components/CollapsibleSection';\nimport './RedactionSearchResultGroup.scss';\n\nconst RedactionSearchResultGroup = (props) => {\n  const {\n    pageNumber,\n    searchResults,\n    selectedSearchResultIndexes,\n    setSelectedSearchResultIndexes,\n  } = props;\n\n  const { t } = useTranslation();\n  const groupResultIndexes = searchResults.map((result) => result.index);\n  const [allItemsChecked, setAllItemsChecked] = useState(false);\n\n  useEffect(() => {\n    const allResultsSelected = groupResultIndexes.reduce((allSelected, currentIndex) => {\n      return selectedSearchResultIndexes[currentIndex] && allSelected;\n    }, true);\n\n    setAllItemsChecked(allResultsSelected);\n  }, [selectedSearchResultIndexes, groupResultIndexes]);\n\n  const checkAllResults = useCallback((event) => {\n    const checked = event.target.checked;\n    groupResultIndexes.forEach((resultIndex) => {\n      selectedSearchResultIndexes[resultIndex] = checked;\n    });\n    setAllItemsChecked(checked);\n    setSelectedSearchResultIndexes({ ...selectedSearchResultIndexes });\n  }, [selectedSearchResultIndexes, groupResultIndexes]);\n\n  const checkResult = useCallback((event, index) => {\n    const checked = event.target.checked;\n    selectedSearchResultIndexes[index] = checked;\n    setSelectedSearchResultIndexes({ ...selectedSearchResultIndexes });\n  }, [selectedSearchResultIndexes]);\n\n  const header = () => {\n    return (\n      `${t('option.shared.page')} ${pageNumber}`\n    );\n  };\n\n  const style = {\n    width: '100%',\n  };\n\n  return (\n    <div className=\"redaction-search-results-page-number\">\n      <Choice\n        className=\"redaction-search-results-page-number-checkbox\"\n        aria-label={`${t('option.shared.page')} ${pageNumber}`}\n        checked={allItemsChecked}\n        onChange={(event) => {\n          event.stopPropagation();\n          checkAllResults(event);\n        }}\n      />\n      <CollapsibleSection header={header} style={style} expansionDescription={`${t('option.shared.page')} ${pageNumber}`}>\n        <ul className=\"redaction-search-results\">\n          {searchResults.map((searchResult, index) => (\n            <RedactionSearchResult\n              checked={selectedSearchResultIndexes[searchResult.index]}\n              checkResult={checkResult}\n              searchResult={searchResult}\n              key={`${index}-${pageNumber}`}\n            />)\n          )}\n        </ul>\n      </CollapsibleSection>\n    </div>\n  );\n};\n\nexport default RedactionSearchResultGroup;", "import RedactionSearchResultGroup from './RedactionSearchResultGroup';\n\nexport default RedactionSearchResultGroup;", "export default {\n  SEARCH_NOT_INITIATED: 'SEARCH_NOT_INITIATED',\n  SEARCH_IN_PROGRESS: 'SEARCH_IN_PROGRESS',\n  SEARCH_DONE: 'SEARCH_DONE',\n};", "import React, { useState, useEffect, useContext } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport RedactionSearchResultGroup from 'components/RedactionSearchResultGroup';\nimport Spinner from 'components/Spinner';\nimport './RedactionSearchResults.scss';\nimport classNames from 'classnames';\nimport { Virtuoso } from 'react-virtuoso';\nimport SearchStatus from 'constants/searchStatus';\nimport { RedactionPanelContext } from '../RedactionPanel/RedactionPanelContext';\nimport Button from 'components/Button';\n\nfunction RedactionSearchResults(props) {\n  const {\n    redactionSearchResults,\n    searchStatus,\n    onCancelSearch,\n    isProcessingRedactionResults,\n    markSelectedResultsForRedaction,\n    redactSelectedResults,\n  } = props;\n\n  const { t } = useTranslation();\n  const [redactionSearchResultPageMap, setRedactionSearchResultPageMap] = useState({});\n  const [selectedSearchResultIndexesMap, setSelectedSearchResultIndexesMap] = useState({});\n  const [selectedIndexes, setSelectedIndexes] = useState([]);\n  // The following prop is needed only for the tests to actually render a list of results\n  // it only is ever injected in the tests\n  const { isTestMode } = useContext(RedactionPanelContext);\n\n\n  useEffect(() => {\n    const redactionSearchResultPageMap = {};\n    redactionSearchResults.forEach((result, index) => {\n      const pageNumber = result.pageNum;\n      result.index = index;\n      if (redactionSearchResultPageMap[pageNumber] === undefined) {\n        redactionSearchResultPageMap[pageNumber] = [result];\n      } else {\n        redactionSearchResultPageMap[pageNumber] = [...redactionSearchResultPageMap[pageNumber], result];\n      }\n    });\n\n    setRedactionSearchResultPageMap(redactionSearchResultPageMap);\n\n    const selectedIndexesMap = {};\n    redactionSearchResults.forEach((value, index) => {\n      selectedIndexesMap[index] = false;\n    });\n    setSelectedSearchResultIndexesMap(selectedIndexesMap);\n  }, [redactionSearchResults]);\n\n\n  useEffect(() => {\n    const selectedIndexes = redactionSearchResults.filter((redactionSearchResult, index) => {\n      return selectedSearchResultIndexesMap[index];\n    });\n\n    setSelectedIndexes(selectedIndexes);\n  }, [selectedSearchResultIndexesMap]);\n\n\n  const renderSearchResults = () => {\n    const resultGroupPageNumbers = Object.keys(redactionSearchResultPageMap);\n    if (resultGroupPageNumbers.length > 0) {\n      // Needed for the tests to actually render a list of results\n      const testModeProps = isTestMode ? { initialItemCount: resultGroupPageNumbers.length } : {};\n      return (\n        <Virtuoso\n          data={resultGroupPageNumbers}\n          itemContent={(index, pageNumber) => {\n            return (\n              <RedactionSearchResultGroup\n                key={index}\n                pageNumber={pageNumber}\n                searchResults={redactionSearchResultPageMap[pageNumber]}\n                selectedSearchResultIndexes={selectedSearchResultIndexesMap}\n                setSelectedSearchResultIndexes={setSelectedSearchResultIndexesMap}\n              />);\n          }}\n          {...testModeProps}\n        />);\n    }\n  };\n\n  const renderStartSearch = () => (\n    <div aria-label={t('redactionPanel.search.start')}>\n      {t('redactionPanel.search.start')}\n    </div>\n  );\n\n  const noResults = (\n    <div aria-label={t('message.noResults')}>\n      <p aria-live=\"assertive\" role=\"alert\" className=\"no-margin\">{t('message.noResults')}</p>\n    </div>\n  );\n\n  const renderSearchInProgress = () => (\n    <div >\n      <Spinner height=\"25px\" width=\"25px\" />\n    </div>\n  );\n\n  const onCancelHandler = () => {\n    setRedactionSearchResultPageMap({});\n    onCancelSearch();\n  };\n\n  const selectAllResults = () => {\n    const searchResultIndexMap = {};\n    redactionSearchResults.forEach((value, index) => {\n      searchResultIndexMap[index] = true;\n    });\n    setSelectedSearchResultIndexesMap(searchResultIndexMap);\n  };\n\n  const unselectAllResults = () => {\n    const searchResultIndexMap = {};\n    redactionSearchResults.forEach((value, index) => {\n      searchResultIndexMap[index] = false;\n    });\n    setSelectedSearchResultIndexesMap(searchResultIndexMap);\n  };\n\n  const onMarkAllForRedaction = () => {\n    markSelectedResultsForRedaction(selectedIndexes);\n    onCancelSearch();\n  };\n\n  const onRedactSelectedResults = () => {\n    redactSelectedResults(selectedIndexes);\n  };\n\n  const isEmptyList = redactionSearchResults.length === 0;\n\n  const resultsContainerClass = classNames('redaction-search-results-container', { emptyList: isEmptyList });\n  const redactAllButtonClass = classNames('redact-all-selected', { disabled: selectedIndexes.length === 0 });\n  const markAllForRedactionButtonClass = classNames('mark-all-selected', { disabled: selectedIndexes.length === 0 });\n  const shouldShowResultsCounterOptions = (searchStatus === SearchStatus['SEARCH_DONE'] && !isProcessingRedactionResults) || searchStatus === SearchStatus['SEARCH_NOT_INITIATED'];\n\n  return (\n    <>\n      <div className=\"redaction-search-counter-controls\">\n        {searchStatus === SearchStatus['SEARCH_IN_PROGRESS'] && (\n          <div style={{ flexGrow: 1 }}>\n            <Spinner height=\"18px\" width=\"18px\" />\n          </div>)}\n        {shouldShowResultsCounterOptions && (\n          <>\n            <div className=\"redaction-search-results-counter\">\n              <h4 aria-live=\"assertive\" role=\"alert\" className=\"no-margin\">\n                {t('redactionPanel.searchResults')}\n                <span>{` (${redactionSearchResults.length})`}</span>\n              </h4>\n            </div>\n            <Button\n              className={classNames({\n                'inactive': selectedIndexes.length < 1\n              })}\n              onClick={selectAllResults}\n              disabled={isEmptyList}\n              label={t('action.selectAll')}\n            />\n            <Button\n              className={classNames({\n                'inactive': selectedIndexes.length < 1\n              })}\n              disabled={isEmptyList}\n              onClick={unselectAllResults}\n              label={t('action.unselect')}\n            />\n          </>)}\n      </div>\n      <div className={resultsContainerClass} role=\"list\">\n        {searchStatus === SearchStatus['SEARCH_NOT_INITIATED'] && renderStartSearch()}\n        {(searchStatus === SearchStatus['SEARCH_IN_PROGRESS'] && isEmptyList && isProcessingRedactionResults) && renderSearchInProgress()}\n        {searchStatus === SearchStatus['SEARCH_DONE'] && isEmptyList && !isProcessingRedactionResults && noResults}\n        {(searchStatus === SearchStatus['SEARCH_IN_PROGRESS'] || searchStatus === SearchStatus['SEARCH_DONE']) && renderSearchResults()}\n      </div>\n      <div className=\"redaction-search-panel-controls\" >\n        <Button\n          onClick={onCancelHandler}\n          label={t('action.cancel')}\n          className=\"cancel\"\n        />\n        <Button\n          disabled={selectedIndexes.length === 0}\n          label={t('annotation.redact')}\n          className={redactAllButtonClass}\n          onClick={onRedactSelectedResults}\n        />\n        <Button\n          disabled={selectedIndexes.length === 0}\n          label={t('action.addMark')}\n          ariaLabel={t('action.addMark')}\n          className={markAllForRedactionButtonClass}\n          onClick={onMarkAllForRedaction}\n        />\n      </div >\n    </>\n  );\n}\n\nexport default RedactionSearchResults;\n", "import React, { useCallback } from 'react';\nimport RedactionSearchResults from './RedactionSearchResults';\nimport { useDispatch, useSelector, shallowEqual } from 'react-redux';\nimport selectors from 'selectors';\nimport applyRedactions from 'helpers/applyRedactions';\nimport core from 'core';\n\nconst { ToolNames } = window.Core.Tools;\n\nexport const defaultRedactionStyles = {\n  OverlayText: '',\n  StrokeColor: new window.Core.Annotations.Color(255, 0, 0),\n  TextColor: new window.Core.Annotations.Color(255, 0, 0, 1),\n  Font: 'Helvetica',\n};\n\nexport function createRedactionAnnotations(searchResults, activeToolStyles = defaultRedactionStyles) {\n  const {\n    StrokeColor,\n    OverlayText,\n    FillColor,\n    Font = 'Helvetica',\n    TextColor,\n    FontSize,\n    TextAlign,\n  } = activeToolStyles;\n  const redactionAnnotations = searchResults.map((result) => {\n    const redaction = new window.Core.Annotations.RedactionAnnotation();\n    redaction.PageNumber = result.page_num;\n    redaction.Quads = result.quads.map((quad) => quad.getPoints());\n    redaction.StrokeColor = StrokeColor;\n    redaction.OverlayText = OverlayText;\n    redaction.FillColor = FillColor;\n    redaction.Font = Font;\n    redaction.FontSize = FontSize;\n    if (window.Core.Annotations.Utilities.calculateAutoFontSize) {\n      redaction.FontSize = window.Core.Annotations.Utilities.calculateAutoFontSize(redaction);\n    }\n    redaction.TextColor = TextColor;\n    redaction.TextAlign = TextAlign;\n    redaction.setContents(result.result_str);\n    redaction.type = result.type;\n    redaction.Author = core.getCurrentUser();\n\n    if (result.type === 'text') {\n      redaction.setCustomData('trn-annot-preview', result.result_str);\n    }\n\n    redaction.setCustomData('trn-redaction-type', result.type);\n\n    return redaction;\n  });\n\n  return redactionAnnotations;\n}\n\nfunction RedactionSearchResultsContainer(props) {\n  const { onCancelSearch } = props;\n  const dispatch = useDispatch();\n  // activeToolStyles is an object so we do a shallowEqual to check equality\n  const [activeToolStyles, activeToolName] = useSelector(\n    (state) => [\n      selectors.getActiveToolStyles(state),\n      selectors.getActiveToolName(state)\n    ], shallowEqual);\n\n  const redactSelectedResults = (searchResults) => {\n    const redactionAnnotations = createRedactionAnnotations(searchResults, defaultRedactionStyles);\n    dispatch(applyRedactions(redactionAnnotations, onCancelSearch));\n  };\n\n  const markSelectedResultsForRedaction = useCallback((searchResults) => {\n    const tool = core.getTool(ToolNames.REDACTION);\n    const alternativeDefaultStyles = (tool && tool.defaults) ? tool.defaults : defaultRedactionStyles;\n    const redactionStyles = activeToolName.includes('Redaction') ? activeToolStyles : alternativeDefaultStyles;\n    const redactionAnnotations = createRedactionAnnotations(searchResults, redactionStyles);\n    const annotationManager = core.getAnnotationManager();\n    annotationManager.addAnnotations(redactionAnnotations);\n  }, [activeToolStyles, activeToolName]);\n\n  return (\n    <RedactionSearchResults\n      markSelectedResultsForRedaction={markSelectedResultsForRedaction}\n      redactSelectedResults={redactSelectedResults}\n      {...props}\n    />);\n}\n\nexport default RedactionSearchResultsContainer;", "import RedactionSearchResultsContainer from './RedactionSearchResultsContainer';\n\nexport default RedactionSearchResultsContainer;", "import React, { useContext, useState } from 'react';\nimport { useDispatch } from 'react-redux';\nimport actions from 'actions';\nimport RedactionSearchOverlay from 'src/components/RedactionSearchOverlay';\nimport { RedactionPanelContext } from 'components/RedactionPanel/RedactionPanelContext';\nimport RedactionSearchResults from 'components/RedactionSearchResults';\nimport Icon from 'components/Icon';\nimport { isMobileSize } from 'helpers/getDeviceSize';\n\nconst RedactionSearchPanel = (props) => {\n  const dispatch = useDispatch();\n  const [searchTerms, setSearchTerms] = useState([]);\n  const { isRedactionSearchActive, setIsRedactionSearchActive } = useContext(RedactionPanelContext);\n  const onCancelSearch = () => {\n    setSearchTerms([]);\n    clearRedactionSearchResults();\n    setIsRedactionSearchActive(false);\n  };\n\n  const {\n    redactionSearchResults,\n    isProcessingRedactionResults,\n    clearRedactionSearchResults,\n    searchStatus,\n  } = props;\n\n  const isMobile = isMobileSize();\n\n  const onCloseButtonClick = () => {\n    dispatch(actions.closeElement('redactionPanel'));\n  };\n\n  return (\n    <>\n      {isMobile &&\n        <div\n          className=\"close-container\"\n        >\n          <button\n            className=\"close-icon-container\"\n            onClick={onCloseButtonClick}\n          >\n            <Icon\n              glyph=\"ic_close_black_24px\"\n              className=\"close-icon\"\n            />\n          </button>\n        </div>}\n      <RedactionSearchOverlay\n        searchTerms={searchTerms}\n        setSearchTerms={setSearchTerms}\n      />\n      {isRedactionSearchActive &&\n        <RedactionSearchResults\n          redactionSearchResults={redactionSearchResults}\n          onCancelSearch={onCancelSearch}\n          searchStatus={searchStatus}\n          isProcessingRedactionResults={isProcessingRedactionResults}\n        />\n      }\n    </>\n  );\n};\n\nexport default RedactionSearchPanel;", "import { useEffect, useState, useCallback, useMemo } from 'react';\nimport { useSelector, shallowEqual } from 'react-redux';\nimport selectors from 'selectors';\nimport core from 'core';\nimport { redactionTypeMap } from 'constants/redactionTypes';\nimport SearchStatus from 'constants/searchStatus';\n\nfunction useOnRedactionSearchCompleted() {\n  const [searchStatus, setSearchStatus] = useState(SearchStatus['SEARCH_NOT_INITIATED']);\n  const [redactionSearchResults, setRedactionSearchResults] = useState([]);\n  const [isProcessingRedactionResults, setIsProcessingRedactionResults] = useState(false);\n  const redactionSearchPatterns = useSelector((state) => selectors.getRedactionSearchPatterns(state), shallowEqual);\n\n  const searchPatterns = useMemo(() => {\n    return Object.keys(redactionSearchPatterns).reduce((map, key) => {\n      const { regex, type, icon } = redactionSearchPatterns[key];\n      map[type] = {\n        regex,\n        icon\n      };\n      return map;\n    }, {});\n  }, [redactionSearchPatterns]);\n\n  const mapResultToType = useCallback((result) => {\n    // Iterate through the patterns and return the first match\n    const { resultStr } = result;\n    const searchPatternKeys = Object.keys(searchPatterns);\n\n    const resultType = searchPatternKeys.find((key) => {\n      const { regex } = searchPatterns[key];\n      return regex.test(resultStr);\n    });\n\n    // If it didn't match any of the patterns, return the default type which is text\n    result.type = resultType === undefined ? redactionTypeMap['TEXT'] : resultType;\n    // And also set the icon to display in the panel. If no icon provided use the text icon\n    const { icon = 'icon-form-field-text' } = searchPatterns[result.type] || {};\n    result.icon = icon;\n    return result;\n  }, [searchPatterns]);// Dependency is an object but it is memoized so it will not re-create unless the patterns change\n\n  const clearRedactionSearchResults = useCallback(() => {\n    setRedactionSearchResults([]);\n    core.clearSearchResults();\n    setIsProcessingRedactionResults(false);\n  });\n\n  useEffect(() => {\n    function onSearchResultsChanged(results) {\n      const mappedResults = results.map(mapResultToType);\n      setIsProcessingRedactionResults(true);\n      setRedactionSearchResults(mappedResults);\n    }\n\n    core.addEventListener('searchResultsChanged', onSearchResultsChanged);\n    return () => {\n      core.removeEventListener('searchResultsChanged', onSearchResultsChanged);\n    };\n  }, [searchStatus]);\n\n  useEffect(() => {\n    function searchInProgressEventHandler(isSearching) {\n      if (isSearching === undefined || isSearching === null) {\n        // if isSearching is not passed at all, we consider that to mean that search was reset to original state\n        setSearchStatus(SearchStatus['SEARCH_NOT_INITIATED']);\n      } else if (isSearching) {\n        setSearchStatus(SearchStatus['SEARCH_IN_PROGRESS']);\n      } else {\n        setSearchStatus(SearchStatus['SEARCH_DONE']);\n        // Need a timeout due to timing issue as SEARCH_DONE is fired\n        // before final call to onSearchResultsChanged, otherwise we briefly show\n        // the NO RESULTS message before showing actual results.\n        setTimeout(() => {\n          setIsProcessingRedactionResults(false);\n        }, 100);\n      }\n    }\n\n    core.addEventListener('searchInProgress', searchInProgressEventHandler);\n\n    return () => {\n      core.removeEventListener('searchInProgress', searchInProgressEventHandler);\n    };\n  }, []);\n\n  return {\n    redactionSearchResults,\n    isProcessingRedactionResults,\n    clearRedactionSearchResults,\n    searchStatus,\n  };\n}\n\nexport default useOnRedactionSearchCompleted;", "import useOnRedactionSearchCompleted from './useOnRedactionSearchCompleted';\n\nexport default useOnRedactionSearchCompleted;", "import ReactionSearchPanelContainer from './RedactionSearchPanelContainer';\n\nexport default ReactionSearchPanelContainer;", "import React from 'react';\nimport RedactionSearchPanel from './RedactionSearchPanel';\nimport useOnRedactionSearchCompleted from 'hooks/useOnRedactionSearchCompleted';\n\nconst ReactionSearchPanelContainer = () => {\n  const {\n    redactionSearchResults,\n    isProcessingRedactionResults,\n    clearRedactionSearchResults,\n    searchStatus,\n  } = useOnRedactionSearchCompleted();\n\n  return (\n    <RedactionSearchPanel\n      redactionSearchResults={redactionSearchResults}\n      isProcessingRedactionResults={isProcessingRedactionResults}\n      clearRedactionSearchResults={clearRedactionSearchResults}\n      searchStatus={searchStatus}\n    />\n  );\n};\n\nexport default ReactionSearchPanelContainer;", "import React, { useContext, useEffect, useMemo, useState } from 'react';\nimport PropTypes from 'prop-types';\nimport RedactionPanel from './RedactionPanel';\nimport { useSelector, useDispatch, shallowEqual } from 'react-redux';\nimport selectors from 'selectors';\nimport actions from 'actions';\nimport core from 'core';\nimport applyRedactions from 'helpers/applyRedactions';\nimport { RedactionPanelContext, RedactionPanelProvider } from './RedactionPanelContext';\nimport { isMobileSize } from 'helpers/getDeviceSize';\nimport DataElementWrapper from '../DataElementWrapper';\nimport Icon from 'components/Icon';\nimport RedactionSearchPanel from 'components/RedactionSearchPanel';\nimport { defaultRedactionTypes } from 'constants/redactionTypes';\n\nexport const RedactionPanelContainer = (props) => {\n  const [\n    isOpen,\n    isDisabled,\n    redactionPanelWidth,\n    isInDesktopOnlyMode,\n    customApplyRedactionsHandler,\n    redactionSearchPatterns,\n  ] = useSelector(\n    (state) => [\n      selectors.isElementOpen(state, 'redactionPanel'),\n      selectors.isElementDisabled(state, 'redactionPanel'),\n      selectors.getRedactionPanelWidth(state),\n      selectors.isInDesktopOnlyMode(state),\n      selectors.getCustomApplyRedactionsHandler(state),\n      selectors.getRedactionSearchPatterns(state),\n    ],\n    shallowEqual,\n  );\n\n  const isMobile = isMobileSize();\n\n  const { redactionAnnotationsList, isCustomPanel, dataElement } = props;\n\n  const redactionTypesDictionary = useMemo(() => {\n    const storedRedactionTypes = Object.keys(redactionSearchPatterns).reduce((map, key) => {\n      const { label, type, icon } = redactionSearchPatterns[key];\n      map[type] = {\n        label,\n        icon,\n      };\n      return map;\n    }, {});\n\n    return { ...storedRedactionTypes, ...defaultRedactionTypes };\n  }, [redactionSearchPatterns]);\n\n  const deleteAllRedactionAnnotations = () => {\n    core.deleteAnnotations(redactionAnnotationsList);\n  };\n\n  const dispatch = useDispatch();\n  const applyAllRedactions = () => {\n    const originalApplyRedactions = () => {\n      const callOnRedactionCompleted = isCustomPanel ? closeRedactionPanel : () => {};\n      dispatch(applyRedactions(redactionAnnotationsList, callOnRedactionCompleted));\n    };\n    if (customApplyRedactionsHandler) {\n      customApplyRedactionsHandler(redactionAnnotationsList, originalApplyRedactions);\n    } else {\n      originalApplyRedactions();\n    }\n  };\n\n  const closeRedactionPanel = () => {\n    const tempDataElement = isCustomPanel ? dataElement : 'redactionPanel';\n    dispatch(actions.closeElement(tempDataElement));\n  };\n\n  const renderMobileCloseButton = () => {\n    return (\n      !isCustomPanel && (\n        <div className=\"close-container\">\n          <div className=\"close-icon-container\" onClick={closeRedactionPanel}>\n            <Icon glyph=\"ic_close_black_24px\" className=\"close-icon\" />\n          </div>\n        </div>\n      )\n    );\n  };\n\n  const style = isCustomPanel || (!isInDesktopOnlyMode && isMobile)\n    ? {}\n    : { width: `${redactionPanelWidth}px`, minWidth: `${redactionPanelWidth}px` };\n\n  const { isRedactionSearchActive } = useContext(RedactionPanelContext);\n\n  const [renderNull, setRenderNull] = useState(false);\n\n  useEffect(() => {\n    const timeout = setTimeout(() => {\n      setRenderNull(!isOpen);\n    }, 500);\n    return () => {\n      clearTimeout(timeout);\n    };\n  }, [isOpen]);\n\n  if (isDisabled || (!isOpen && renderNull && !isCustomPanel)) {\n    return null;\n  }\n\n  const dataElementToUse = isCustomPanel ? dataElement : 'redactionPanel';\n\n  return (\n    <DataElementWrapper dataElement={dataElementToUse} className=\"Panel RedactionPanel\" style={style}>\n      {!isInDesktopOnlyMode && isMobile && renderMobileCloseButton()}\n      <RedactionSearchPanel />\n      {!isRedactionSearchActive && (\n        <RedactionPanel\n          redactionAnnotations={redactionAnnotationsList}\n          redactionTypesDictionary={redactionTypesDictionary}\n          applyAllRedactions={applyAllRedactions}\n          deleteAllRedactionAnnotations={deleteAllRedactionAnnotations}\n        />\n      )}\n    </DataElementWrapper>\n  );\n};\n\nRedactionPanelContainer.propTypes = {\n  redactionAnnotationsList: PropTypes.array,\n  isCustomPanel: PropTypes.bool,\n  dataElement: PropTypes.string,\n};\n\nRedactionPanelContainer.defaultProps = {\n  isCustomPanel: false,\n  dataElement: '',\n};\n\nconst RedactionPanelContainerWithProvider = (props) => {\n  return (\n    <RedactionPanelProvider>\n      <RedactionPanelContainer {...props} />\n    </RedactionPanelProvider>\n  );\n};\n\nexport default RedactionPanelContainerWithProvider;\n", "import RedactionPanelContainerWithProvider from './RedactionPanelContainer';\n\nexport default RedactionPanelContainerWithProvider;"], "sourceRoot": ""}