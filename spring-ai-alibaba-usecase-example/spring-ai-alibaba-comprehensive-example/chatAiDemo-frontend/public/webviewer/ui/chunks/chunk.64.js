(window.webpackJsonp=window.webpackJsonp||[]).push([[64],{1931:function(o,e,t){var n=t(32),a=t(1932);"string"==typeof(a=a.__esModule?a.default:a)&&(a=[[o.i,a,""]]);var r={insert:function(o){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(o);let e;e=document.getElementsByTagName("apryse-webviewer"),e.length||(e=function o(e,t=document){const n=[];return t.querySelectorAll(e).forEach(o=>n.push(o)),t.querySelectorAll("*").forEach(t=>{t.shadowRoot&&n.push(...o(e,t.shadowRoot))}),n}("apryse-webviewer"));const t=[];for(let n=0;n<e.length;n++){const a=e[n];if(0===n)a.shadowRoot.appendChild(o),o.onload=function(){t.length>0&&t.forEach(e=>{e.innerHTML=o.innerHTML})};else{const e=o.cloneNode(!0);a.shadowRoot.appendChild(e),t.push(e)}}},singleton:!1};n(a,r);o.exports=a.locals||{}},1932:function(o,e,t){(e=o.exports=t(33)(!1)).push([o.i,'.open.HeaderFooterOptionsModal{visibility:visible}.closed.HeaderFooterOptionsModal{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.HeaderFooterOptionsModal .footer .modal-button.confirm:hover{background:var(--primary-button-hover);border-color:var(--primary-button-hover);color:var(--gray-0)}.HeaderFooterOptionsModal .footer .modal-button.confirm{background:var(--primary-button);border-color:var(--primary-button);color:var(--primary-button-text)}.HeaderFooterOptionsModal .footer .modal-button.confirm.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.HeaderFooterOptionsModal .footer .modal-button.confirm.disabled span{color:var(--primary-button-text)}.HeaderFooterOptionsModal .footer .modal-button.cancel:hover,.HeaderFooterOptionsModal .footer .modal-button.secondary-btn-custom:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6)}.HeaderFooterOptionsModal .footer .modal-button.cancel,.HeaderFooterOptionsModal .footer .modal-button.secondary-btn-custom{border:none;box-shadow:inset 0 0 0 1px var(--primary-button);color:var(--primary-button)}.HeaderFooterOptionsModal .footer .modal-button.cancel.disabled,.HeaderFooterOptionsModal .footer .modal-button.secondary-btn-custom.disabled{cursor:default;border:none;box-shadow:inset 0 0 0 1px rgba(43,115,171,.5);color:rgba(43,115,171,.5)}.HeaderFooterOptionsModal .footer .modal-button.cancel.disabled span,.HeaderFooterOptionsModal .footer .modal-button.secondary-btn-custom.disabled span{color:rgba(43,115,171,.5)}.HeaderFooterOptionsModal{position:fixed;left:0;bottom:0;z-index:100;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background:var(--modal-negative-space)}.HeaderFooterOptionsModal .modal-container .wrapper .modal-content{padding:10px}.HeaderFooterOptionsModal .footer{display:flex;flex-direction:row;justify-content:flex-end;width:100%;margin-top:13px}.HeaderFooterOptionsModal .footer.modal-footer{padding:16px;margin:0;border-top:1px solid var(--divider)}.HeaderFooterOptionsModal .footer .modal-button{display:flex;justify-content:center;align-items:center;padding:6px 18px;margin:8px 0 0;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;height:30px;cursor:pointer}.HeaderFooterOptionsModal .footer .modal-button.confirm{margin-left:4px}.HeaderFooterOptionsModal .footer .modal-button.secondary-btn-custom{border-radius:4px;padding:2px 20px 4px;cursor:pointer}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .HeaderFooterOptionsModal .footer .modal-button{padding:23px 8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .HeaderFooterOptionsModal .footer .modal-button{padding:23px 8px}}.HeaderFooterOptionsModal .swipe-indicator{background:var(--swipe-indicator-bg);border-radius:2px;height:4px;width:38px;position:absolute;top:12px;margin-left:auto;margin-right:auto;left:0;right:0}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .HeaderFooterOptionsModal .swipe-indicator{display:none}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .HeaderFooterOptionsModal .swipe-indicator{display:none}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .HeaderFooterOptionsModal .swipe-indicator{width:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .HeaderFooterOptionsModal .swipe-indicator{width:32px}}.HeaderFooterOptionsModal{flex-direction:column}.HeaderFooterOptionsModal .modal-container{display:flex;flex-direction:column;height:auto;width:480px}.HeaderFooterOptionsModal .modal-container .modal-body{padding:16px;display:flex;flex-direction:column;font-size:var(--font-size-default);font-family:var(--font-family);grid-gap:16px;gap:16px}.HeaderFooterOptionsModal .modal-container .modal-body .title{line-height:16px;font-weight:var(--font-weight-bold)}.HeaderFooterOptionsModal .modal-container .modal-body .input-container{display:flex;flex-direction:column;grid-gap:8px;gap:8px}.HeaderFooterOptionsModal .modal-container .modal-body .input-container .label{color:var(--gray-12);display:block;text-align:left;font-weight:var(--font-weight-normal)}.HeaderFooterOptionsModal .modal-container .modal-body .input-container .ui__input{border-color:var(--gray-5);position:relative}.HeaderFooterOptionsModal .modal-container .modal-body .input-container .ui__input:after{content:"cm";font-size:13px;color:var(--gray-8);position:absolute;right:16px;pointer-events:none}.HeaderFooterOptionsModal .modal-container .modal-body .input-container .ui__input.ui__input--focused{box-shadow:none;border-color:var(--focus-border)}.HeaderFooterOptionsModal .modal-container .modal-body .input-container input{padding:8px 40px 8px 8px;height:32px;font-size:var(--font-size-default)}.HeaderFooterOptionsModal .modal-container .modal-body .input-container input[type=number]{-moz-appearance:textfield}.HeaderFooterOptionsModal .modal-container .modal-body .input-container input::-webkit-inner-spin-button,.HeaderFooterOptionsModal .modal-container .modal-body .input-container input::-webkit-outer-spin-button{-webkit-appearance:none;margin:0}.HeaderFooterOptionsModal .modal-container .modal-body .radio-container{display:flex;flex-direction:column;grid-gap:16px;gap:16px;margin:0}.HeaderFooterOptionsModal .modal-container .modal-body .radio-container .ui__choice--checked .ui__choice__input__check{border-color:var(--blue-5)}.HeaderFooterOptionsModal .modal-container .modal-body .radio-container .ui__choice__input__check{border-color:var(--gray-7)}.HeaderFooterOptionsModal .modal-container .footer{padding:16px;display:flex;justify-content:flex-end;border-top:1px solid var(--gray-5)}.HeaderFooterOptionsModal .modal-container .footer button{border:none;border-radius:4px;background:var(--primary-button);min-width:59px;width:auto;padding:8px 16px;height:32px;color:var(--primary-button-text)}.HeaderFooterOptionsModal .modal-container .footer button:hover{background:var(--primary-button-hover)}',""]),e.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},2010:function(o,e,t){"use strict";t.r(e);t(18),t(106),t(19),t(11),t(13),t(8),t(14),t(10),t(9),t(12),t(16),t(15),t(20);var n=t(0),a=t.n(n),r=t(6),i=t(428),d=t(3),l=t(2),p=t(17),s=t.n(p),c=t(5),m=t(44),u=t(340),b=t(1957),f=t(1312);t(1931);function h(o,e){return function(o){if(Array.isArray(o))return o}(o)||function(o,e){var t=null==o?null:"undefined"!=typeof Symbol&&o[Symbol.iterator]||o["@@iterator"];if(null!=t){var n,a,r,i,d=[],l=!0,p=!1;try{if(r=(t=t.call(o)).next,0===e){if(Object(t)!==t)return;l=!1}else for(;!(l=(n=r.call(t)).done)&&(d.push(n.value),d.length!==e);l=!0);}catch(o){p=!0,a=o}finally{try{if(!l&&null!=t.return&&(i=t.return(),Object(i)!==i))return}finally{if(p)throw a}}return d}}(o,e)||function(o,e){if(!o)return;if("string"==typeof o)return y(o,e);var t=Object.prototype.toString.call(o).slice(8,-1);"Object"===t&&o.constructor&&(t=o.constructor.name);if("Map"===t||"Set"===t)return Array.from(o);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return y(o,e)}(o,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function y(o,e){(null==e||e>o.length)&&(e=o.length);for(var t=0,n=new Array(e);t<e;t++)n[t]=o[t];return n}var x="none",g="first",w="even_odd",O="first_even_odd",v=function(){var o=h(Object(i.a)(),1)[0],e=Object(r.d)(),t=h(Object(n.useState)("0"),2),p=t[0],y=t[1],v=h(Object(n.useState)("0"),2),F=v[0],M=v[1],H=h(Object(n.useState)(x),2),E=H[0],_=H[1],k=Object(r.e)((function(o){return d.a.isElementOpen(o,c.a.HEADER_FOOTER_OPTIONS_MODAL)})),A=function(){e(l.a.closeElement(c.a.HEADER_FOOTER_OPTIONS_MODAL))},T=function(o){return!o||o<0?0:o.replace(/^0+/,"")},j=s()({HeaderFooterOptionsModal:!0});return k&&a.a.createElement("div",{className:j,"data-element":c.a.HEADER_FOOTER_OPTIONS_MODAL},a.a.createElement(u.a,{isOpen:k,title:o("officeEditor.headerFooterOptionsModal.title"),closehandler:A,onCloseClick:A,swipeToClose:!0},a.a.createElement("div",{className:"modal-body"},a.a.createElement("div",{className:"title"},o("officeEditor.headerFooterOptionsModal.margins")),a.a.createElement("div",{className:"input-container"},a.a.createElement("label",{htmlFor:"headerFromTopInput",className:"label"},o("officeEditor.headerFooterOptionsModal.headerFromTop")),a.a.createElement(b.a,{type:"number",id:"headerFromTopInput","data-testid":"headerFromTopInput",onChange:function(o){var e=T(o.target.value);y(e)},value:p,min:"0",step:"any"})),a.a.createElement("div",{className:"input-container"},a.a.createElement("label",{htmlFor:"footerFromBottomInput",className:"label"},o("officeEditor.headerFooterOptionsModal.footerFromBottom")),a.a.createElement(b.a,{type:"number",id:"footerFromBottomInput","data-testid":"footerFromBottomInput",onChange:function(o){var e=T(o.target.value);M(e)},value:F,min:"0",step:"any"})),a.a.createElement("div",{className:"title"},o("officeEditor.headerFooterOptionsModal.layouts.layout")),a.a.createElement("form",{className:"radio-container",onChange:function(o){_(o.target.value)},onSubmit:function(o){return o.preventDefault()}},a.a.createElement(f.a,{checked:E===x,radio:!0,name:"layout-option",label:o("officeEditor.headerFooterOptionsModal.layouts.noSelection"),value:x}),a.a.createElement(f.a,{checked:E===g,radio:!0,name:"layout-option",label:o("officeEditor.headerFooterOptionsModal.layouts.differentFirstPage"),value:g}),a.a.createElement(f.a,{checked:E===w,radio:!0,name:"layout-option",label:o("officeEditor.headerFooterOptionsModal.layouts.differentEvenOddPages"),value:w}),a.a.createElement(f.a,{checked:E===O,radio:!0,name:"layout-option",label:o("officeEditor.headerFooterOptionsModal.layouts.differentFirstEvenOddPages"),value:O}))),a.a.createElement("div",{className:"footer"},a.a.createElement(m.a,{onClick:function(){A()},label:o("action.save")}))))};e.default=v}}]);
//# sourceMappingURL=chunk.64.js.map