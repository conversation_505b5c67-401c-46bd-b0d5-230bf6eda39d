{"version": 3, "sources": ["webpack:///./src/ui/src/components/StylePicker/ColorPicker/ColorPicker.js", "webpack:///./src/ui/src/components/StylePicker/ColorPicker/index.js", "webpack:///./src/ui/src/components/StylePicker/ColorPicker/ColorPicker.scss?02e7", "webpack:///./src/ui/src/components/StylePicker/ColorPicker/ColorPicker.scss", "webpack:///./src/ui/src/components/RichTextStyleEditor/RichTextStyleEditor.scss?fe54", "webpack:///./src/ui/src/components/RichTextStyleEditor/RichTextStyleEditor.scss", "webpack:///./src/ui/src/components/RichTextStyleEditor/RichTextStyleEditor.js", "webpack:///./src/ui/src/components/RichTextStyleEditor/index.js"], "names": ["parseColor", "color", "parsedColor", "toHexString", "toLowerCase", "transparentIcon", "width", "height", "className", "classNames", "stroke", "x1", "y1", "x2", "y2", "strokeWidth", "strokeLinecap", "propTypes", "PropTypes", "any", "ariaTypeLabel", "string", "ColorPicker", "onColorChange", "hasTransparentColor", "activeTool", "type", "activeToolName", "Object", "values", "window", "Core", "Tools", "ToolNames", "includes", "EDIT", "store", "useStore", "t", "useTranslation", "dispatch", "useDispatch", "colors", "useSelector", "state", "selectors", "getColors", "useState", "selectedColor", "setSelectedColor", "isExpanded", "setIsExpanded", "forceExpandRef", "useRef", "useEffect", "current", "getCustomColorAndRemove", "customColor", "getCustomColor", "getState", "actions", "setCustomColor", "handleAddColor", "useCallback", "openElement", "getInstanceNode", "addEventListener", "Events", "VISIBILITY_CHANGED", "onVisibilityChanged", "e", "detail", "element", "isVisible", "newColors", "setColors", "removeEventListener", "length", "openColorPickerModalWithFocus", "useFocusHandler", "palette", "map", "push", "indexOf", "shouldHideShowMoreButton", "showCopyButtonDisabled", "isDeleteDisabled", "slice", "i", "<PERSON><PERSON><PERSON>", "content", "toUpperCase", "key", "onClick", "aria-label", "aria-current", "active", "cell", "border", "style", "backgroundColor", "<PERSON><PERSON>", "img", "title", "dataElement", "aria<PERSON><PERSON><PERSON>", "indexToDelete", "nextIndex", "splice", "disabled", "hidden", "api", "__esModule", "default", "module", "options", "styleTag", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "annotation", "object", "editor", "shape", "TextColor", "RichTextStyle", "isFreeTextAutoSize", "bool", "onFreeTextSizeToggle", "func", "onPropertyChange", "onRichTextStyleChange", "isRedaction", "isRichTextEditMode", "setIsRichTextEditMode", "isWidget", "RichTextStyleEditor", "fonts", "getFonts", "shallowEqual", "format", "setFormat", "editor<PERSON><PERSON>", "annotationRef", "propertiesRef", "oldSelectionRef", "richTextEditModeRef", "handleSelectionChange", "range", "oldRange", "setSelection", "index", "getFormat", "handleTextChange", "getSelection", "core", "disableElements", "DataElements", "ANNOTATION_STYLE_POPUP", "enableElements", "StrokeStyle", "err", "console", "error", "stylesTemp", "getRichTextStyle", "Font", "FontSize", "TextAlign", "TextVerticalAlign", "bold", "italic", "underline", "strikeout", "size", "font", "calculatedFontSize", "getCalculatedFontSize", "handleEditorBlur", "handleEditorFocus", "properties", "Annotations", "Color", "Array", "isArray", "lastSelectedColor", "prop", "undefined", "applyFormat", "formatKey", "value", "handlePropertyChange", "property", "blur", "adjustFreeTextBoundingBox", "setTimeout", "getAnnotationManager", "getEditBoxManager", "focusBox", "defaults", "strike", "quillFont", "quillFontSize", "originalSize", "commonProps", "stateless", "isFreeText", "nonWidgetProps", "propertyTranslation", "freeText", "isAutoSized", "resizeAnnotation", "newSelection", "currentFormat", "handleTextFormatChange", "widgetProps", "onMouseDown", "preventDefault", "TextStylePicker", "name", "handleColorChange", "React", "memo"], "mappings": "otDAcA,IAAMA,EAAa,SAACC,GAAU,QAC5B,IAAKA,EACH,OAAOA,EAET,IAAIC,EAAcD,EAQlB,OAPe,QAAf,EAAIC,SAAW,OAAX,EAAaC,cACfD,EAAcA,EAAYC,eAEb,QAAf,EAAID,SAAW,OAAX,EAAaE,cACfF,EAAcA,EAAYE,eAGrBF,GAMHG,EACJ,yBACEC,MAAM,OACNC,OAAO,OACPC,UAAWC,IAAW,gBAEtB,0BAAMC,OAAO,UAAUC,GAAG,IAAIC,GAAG,OAAOC,GAAG,OAAOC,GAAG,IAAIC,YAAY,IAAIC,cAAc,WAMrFC,EAAY,CAChBhB,MAAOiB,IAAUC,IACjBC,cAAeF,IAAUG,QAGrBC,EAAc,SAAH,GAOX,IANJC,EAAa,EAAbA,cAAa,IACbC,2BAAmB,IAAG,GAAK,EAC3BvB,EAAK,EAALA,MACAwB,EAAU,EAAVA,WACAC,EAAI,EAAJA,KACAN,EAAa,EAAbA,cAEMO,EAAiBC,OAAOC,OAAOC,OAAOC,KAAKC,MAAMC,WAAWC,SAAST,GAAcA,EAAaK,OAAOC,KAAKC,MAAMC,UAAUE,KAC5HC,EAAQC,cACNC,EAAMC,cAAND,EACFE,EAAWC,cACVC,EAEL,EAFeC,aAAY,SAACC,GAAK,MAAK,CACtCC,IAAUC,UAAUF,EAAOjB,EAAgBD,OAC3C,GAFW,GAGuC,IAAVqB,qBAAU,GAA7CC,EAAa,KAAEC,EAAgB,KACa,IAAfF,oBAAS,GAAM,GAA5CG,EAAU,KAAEC,EAAa,KAC1BC,EAAiBC,kBAAO,GAE9BC,qBAAU,WACRF,EAAeG,SAAU,IACxB,CAAC5B,EAAgB1B,IAEpBqD,qBAAU,WACJrD,GACFgD,EAAiBjD,EAAWC,MAE7B,CAACA,IAEJ,IAAMuD,EAA0B,WAC9B,IAAMC,EAAcZ,IAAUa,eAAetB,EAAMuB,YAEnD,OADAnB,EAASoB,IAAQC,eAAe,OACzBJ,GAGHK,EAAiBC,uBAAY,WACjCvB,EAASoB,IAAQI,YAAY,qBAmB7BC,cAAkBC,iBAAiBC,IAAOC,oBAlBd,SAAtBC,EAAuBC,GAC3B,MAA+BA,EAAEC,OAAzBC,EAAO,EAAPA,QAASC,EAAS,EAATA,UACjB,GAAgB,qBAAZD,IAAmCC,EAAW,CAChD,IAAMxE,EAAQD,EAAWwD,KACzB,GAAIvD,EACF,GAAIyC,EAAOR,SAASjC,GAClBgD,EAAiBhD,GACjBsB,EAActB,OACT,CACL,IAAMyE,EAAY,GAAH,SAAOhC,GAAM,CAAEzC,IAC9BuC,EAASoB,IAAQe,UAAUD,EAAW/C,EAAgBD,GAAM,IAC5DuB,EAAiBhD,GACjBsB,EAActB,GAGlBgE,cAAkBW,oBAAoBT,IAAOC,mBAAoBC,SAIpE,CAAC3B,aAAM,EAANA,EAAQmC,OAAQrC,EAAUS,EAAkB1B,EAAeiC,EAAyB9B,EAAMC,IAExFmD,EAAgCC,YAAgBjB,GA0BlDkB,EAAUtC,EAAOuC,KAAI,SAAChF,GAAK,OAAKA,EAAMG,iBACtCoB,GACFwD,EAAQE,KA1Gc,eA6GnBlC,GACHC,EAAiB,eAGf+B,EAAQG,QAAQnC,GAAiB,IAAME,GAAcE,EAAeG,UACtEJ,GAAc,GACdC,EAAeG,SAAU,GAG3B,IAAM6B,EAA2BJ,EAAQH,QAAU,EAC7CQ,IAA2BrC,IAAkBgC,EAAQ9C,SAASc,IAC9DsC,EAAmBN,EAAQH,QAAU,IAAMQ,EAMjD,OAJKnC,IACH8B,EAAUA,EAAQO,MAAM,EAAG,IAI3B,oCACE,yBAAK/E,UAAWC,IAAW,iBACxBuE,EAAQC,KAAI,SAAChF,GAAK,OAAKD,EAAWC,MAAQgF,KAAI,SAAChF,EAAOuF,GAAC,iBACrDvF,EAEG,kBAACwF,EAAA,EAAO,CAACC,QAAO,UAAKpD,EAAE,kCAAiC,YAAIrC,SAAkB,QAAb,EAALA,EAAO0F,mBAAW,WAAb,EAAL,OAAA1F,IAA0B2F,IAAK3F,SAAkB,QAAb,EAALA,EAAO0F,mBAAW,WAAb,EAAL,OAAA1F,IAC3F,4BACEO,UAAU,iBACVqF,QAAS,WACP5C,EAAiBhD,GACjBsB,EAActB,IAEhB6F,aAAA,UAAe1E,EAAa,YAAIkB,EAAE,kCAAiC,YAAIrC,SAAkB,QAAb,EAALA,EAAO0F,mBAAW,WAAb,EAAL,OAAA1F,IACvE8F,eAAc/F,EAAWgD,KAAmB/C,IAAWD,EAAWgD,IA5IxD,gBA4I0E/C,GAEpF,yBACEO,UAAWC,IAAW,CACpB,cAAc,EACduF,OAAQhG,EAAWgD,KAAmB/C,IAAWD,EAAWgD,IAjJtD,gBAiJwE/C,KAGhF,yBACEO,UAAWC,IAAW,CACpBwF,MAAM,EACNC,QAAQ,IAEVC,MAAO,CAAEC,gBAAiBnG,IAzJpB,gBA2JLA,GAA+BI,MAxBtC,yBAAKuF,IAAKJ,EAAGhF,UAAU,mBA+B/B,yBAAKA,UAAU,oBACb,yBAAKA,UAAU,oBACb,kBAAC6F,EAAA,EAAM,CACLC,IAAI,2BACJC,MAAOjE,EAAE,sBACTuD,QAASf,EACTtE,UAAU,iBACVgG,YAAY,iBACZC,UAAS,UAAKrF,EAAa,YAAIkB,EAAE,sBAAqB,YAAIA,EAAE,mCAE9D,kBAAC+D,EAAA,EAAM,CACLC,IAAI,mBACJC,MAAOjE,EAAE,sBACTuD,QA/FW,WACnB,IAAM5F,EAAQD,EAAWgD,GACnB0B,EAAY,EAAIhC,GAChBgE,EAAgBhC,EAAUS,QAAQlF,GACxC,GAAIyG,GAAiB,EAAG,CACtB,IAAMC,EAAYD,IAAkBhC,EAAUG,OAAS,EAAI,EAAI6B,EAAgB,EAC/EzD,EAAiBP,EAAOiE,IACxBpF,EAAcmB,EAAOiE,IACrBjC,EAAUkC,OAAOF,EAAe,GAChClE,EAASoB,IAAQe,UAAUD,EAAW/C,EAAgBD,GAAM,MAuFtDmF,SAAUvB,EACV9E,UAAU,iBACVgG,YAAY,sBACZC,UAAS,UAAKrF,EAAa,YAAIkB,EAAE,sBAAqB,YAAIU,KAE5D,kBAACqD,EAAA,EAAM,CACLC,IAAI,aACJC,MAAOjE,EAAE,4BACTuD,QA3Fc,WACtB,IAAM5F,EAAQD,EAAWgD,GACnB0B,EAAY,GAAH,SAAOhC,GAAM,CAAEzC,IAC9BuC,EAASoB,IAAQe,UAAUD,EAAW/C,EAAgBD,GAAM,KAyFpDmF,SAAUxB,EACV7E,UAAU,iBACVgG,YAAY,oBACZC,UAAS,UAAKrF,EAAa,YAAIkB,EAAE,4BAA2B,YAAIU,MAGpE,4BACExC,UAAWC,IAAW,kCAAmC,CACvDqG,OAAQ1B,IAEVS,QAhGe,WAErB1C,GADkBD,IAgGZ4C,aAAA,UAAe1E,EAAa,YAAIkB,EAAeA,EAAbY,EAAe,wBAA6B,4BAE7EZ,EAAEY,EAAa,mBAAqB,wBAO/C5B,EAAYL,UAAYA,EAETK,QC1OAA,O,qBCFf,IAAIyF,EAAM,EAAQ,IACFrB,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQsB,WAAatB,EAAQuB,QAAUvB,KAG/CA,EAAU,CAAC,CAACwB,EAAO1B,EAAIE,EAAS,MAG9C,IAAIyB,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKtF,OAAOuF,8BAEV,YADAC,SAASC,KAAKC,YAAYJ,GAI5B,IAAIK,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAc5C,SACjB4C,EAzBF,SAASE,EAAwBC,EAASC,EAAOP,UAC/C,MAAMQ,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAAS5C,KAAK+C,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGC,YACLJ,EAAS5C,QAAQyC,EAAwBC,EAASK,EAAGC,eAIlDJ,EAYSH,CAAwB,qBAG1C,MAAMQ,EAAkB,GACxB,IAAK,IAAI3C,EAAI,EAAGA,EAAIiC,EAAc5C,OAAQW,IAAK,CAC7C,MAAM4C,EAAeX,EAAcjC,GACnC,GAAU,IAANA,EACF4C,EAAaF,WAAWV,YAAYJ,GACpCA,EAASiB,OAAS,WACZF,EAAgBtD,OAAS,GAC3BsD,EAAgBH,QAASM,IAEvBA,EAAUC,UAAYnB,EAASmB,iBAIhC,CACL,MAAMD,EAAYlB,EAASoB,WAAU,GACrCJ,EAAaF,WAAWV,YAAYc,GACpCH,EAAgBjD,KAAKoD,MAIzC,WAAoB,GAEPvB,EAAIrB,EAASyB,GAI1BD,EAAOuB,QAAU/C,EAAQgD,QAAU,I,sBClEnCD,EAAUvB,EAAOuB,QAAU,EAAQ,GAAR,EAAqE,IAKxFvD,KAAK,CAACgC,EAAO1B,EAAI,yxKAA0xK,KAGnzKiD,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAI3B,EAAM,EAAQ,IACFrB,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQsB,WAAatB,EAAQuB,QAAUvB,KAG/CA,EAAU,CAAC,CAACwB,EAAO1B,EAAIE,EAAS,MAG9C,IAAIyB,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKtF,OAAOuF,8BAEV,YADAC,SAASC,KAAKC,YAAYJ,GAI5B,IAAIK,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAc5C,SACjB4C,EAzBF,SAASE,EAAwBC,EAASC,EAAOP,UAC/C,MAAMQ,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAAS5C,KAAK+C,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGC,YACLJ,EAAS5C,QAAQyC,EAAwBC,EAASK,EAAGC,eAIlDJ,EAYSH,CAAwB,qBAG1C,MAAMQ,EAAkB,GACxB,IAAK,IAAI3C,EAAI,EAAGA,EAAIiC,EAAc5C,OAAQW,IAAK,CAC7C,MAAM4C,EAAeX,EAAcjC,GACnC,GAAU,IAANA,EACF4C,EAAaF,WAAWV,YAAYJ,GACpCA,EAASiB,OAAS,WACZF,EAAgBtD,OAAS,GAC3BsD,EAAgBH,QAASM,IAEvBA,EAAUC,UAAYnB,EAASmB,iBAIhC,CACL,MAAMD,EAAYlB,EAASoB,WAAU,GACrCJ,EAAaF,WAAWV,YAAYc,GACpCH,EAAgBjD,KAAKoD,MAIzC,WAAoB,GAEPvB,EAAIrB,EAASyB,GAI1BD,EAAOuB,QAAU/C,EAAQgD,QAAU,I,sBClEnCD,EAAUvB,EAAOuB,QAAU,EAAQ,GAAR,EAAkE,IAKrFvD,KAAK,CAACgC,EAAO1B,EAAI,sxEAAuxE,KAGhzEiD,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,s0FCGvB,IAAMzH,EAAY,CAChB0H,WAAYzH,IAAU0H,OACtBC,OAAQ3H,IAAU0H,OAClBzC,MAAOjF,IAAU4H,MAAM,CACrBC,UAAW7H,IAAUG,OACrB2H,cAAe9H,IAAUC,MAE3B8H,mBAAoB/H,IAAUgI,KAC9BC,qBAAsBjI,IAAUkI,KAChCC,iBAAkBnI,IAAUkI,KAC5BE,sBAAuBpI,IAAUkI,KACjCG,YAAarI,IAAUgI,KACvBM,mBAAoBtI,IAAUgI,KAC9BO,sBAAuBvI,IAAUkI,KACjCM,SAAUxI,IAAUgI,MAGhBS,EAAsB,SAAH,GAYnB,8BAXJhB,EAAU,EAAVA,WAAYE,EAAM,EAANA,OACZ1C,EAAK,EAALA,MACA8C,EAAkB,EAAlBA,mBACAE,EAAoB,EAApBA,qBACAE,EAAgB,EAAhBA,iBACAC,EAAqB,EAArBA,sBACAE,EAAkB,EAAlBA,mBACAC,EAAqB,EAArBA,sBACAF,EAAW,EAAXA,YACAG,EAAQ,EAARA,SACAjI,EAAU,EAAVA,WAGEmI,EAMD,EALGjH,aACF,SAACC,GAAK,MAAK,CACTC,IAAUgH,SAASjH,MAErBkH,KACD,GANM,GAQiC,IAAZ/G,mBAAS,IAAG,GAAjCgH,EAAM,KAAEC,EAAS,KAClBC,EAAY5G,iBAAO,MACnB6G,EAAgB7G,iBAAO,MACvB8G,EAAgB9G,iBAAO,IACvBb,EAAWC,cACX2H,EAAkB/G,mBAClBgH,EAAsBhH,mBAC5BgH,EAAoB9G,QAAUiG,EAC9B,IAAOlH,EAAqB,EAAhBC,cAAgB,GAApB,GAERe,qBAAU,WACR,IAAMgH,EAAwB,SAACC,EAAOC,IACAD,GAASC,GAAYP,EAAU1G,SAEjE0G,EAAU1G,QAAQkH,aAAaD,EAASE,MAAOF,EAAS3F,QAEtD0F,GAASN,EAAU1G,SACrByG,EAAUW,EAAUJ,KAGlBK,EAAmB,WAAM,MAC7BZ,EAAUW,EAA2B,QAAlB,EAACV,EAAU1G,eAAO,aAAjB,EAAmBsH,kBAMzC,OAJAC,IAAK5G,iBAAiB,yBAA0BoG,GAChDQ,IAAK5G,iBAAiB,oBAAqB0G,GAE3CpI,EAASoB,IAAQmH,gBAAgB,CAACC,IAAaC,0BACxC,WACLH,IAAKlG,oBAAoB,yBAA0B0F,GACnDQ,IAAKlG,oBAAoB,oBAAqBgG,GAC9CpI,EAASoB,IAAQsH,eAAe,CAACF,IAAaC,6BAE/C,IAEH3H,qBAAU,WAAM,MAGd,GAFA2G,EAAU1G,QAAUsF,EACpBqB,EAAc3G,QAAUoF,EACpBa,GAAsBb,EAAY,iBAChCwC,EAAc,QAClB,IACEA,EAAuC,SAAxBxC,EAAkB,MAAY,UACtCA,EAAkB,MAAC,YAAIA,EAAmB,QAC7CA,EAAkB,MACtB,MAAOyC,GACPC,QAAQC,MAAMF,GAEhB,IACMG,EADiB5C,EAAW6C,mBACA,GAElCrB,EAAc5G,QAAU,CACtBkI,KAAM9C,EAAW8C,KACjBC,SAAU/C,EAAW+C,SACrBC,UAAWhD,EAAWgD,UACtBC,kBAAmBjD,EAAWiD,kBAC9BC,KAA4C,QAAxC,EAAkC,UAAhCN,aAAU,EAAVA,EAAa,uBAAyB,SAC5CO,OAA+C,QAAzC,EAAiC,YAA/BP,aAAU,EAAVA,EAAa,sBAA0B,SAC/CQ,WAAWR,SAA+B,QAArB,EAAVA,EAAa,0BAAkB,WAArB,EAAV,EAAiCrJ,SAAS,gBAChDqJ,SAA+B,QAArB,EAAVA,EAAa,0BAAkB,WAArB,EAAV,EAAiCrJ,SAAS,SAC/C8J,UAAoE,QAA3D,EAAET,SAA+B,QAArB,EAAVA,EAAa,0BAAkB,WAArB,EAAV,EAAiCrJ,SAAS,uBAAe,SACpE+J,KAAMV,aAAU,EAAVA,EAAa,aACnBW,KAAMX,aAAU,EAAVA,EAAa,eACnBJ,cACAgB,mBAAoBxD,EAAWyD,yBAInCpC,EAAUW,EAA2B,QAAlB,EAACV,EAAU1G,eAAO,aAAjB,EAAmBsH,iBAEnCT,EAAgB7G,UAClB0G,EAAU1G,QAAQkH,aAAaL,EAAgB7G,SAC/C6G,EAAgB7G,QAAU,QAE3B,CAACoF,EAAYE,EAAQW,IAExBlG,qBAAU,WACR,IAAM+I,EAAmB,WACvBpC,EAAU1G,QAAU,KACpB2G,EAAc3G,QAAU,KACxBkG,GAAsB,IAElB6C,EAAoB,WACxB7C,GAAsB,IAKxB,OAFAqB,IAAK5G,iBAAiB,aAAcmI,GACpCvB,IAAK5G,iBAAiB,cAAeoI,GAC9B,WACLxB,IAAKlG,oBAAoB,aAAcyH,GACvCvB,IAAKlG,oBAAoB,cAAe0H,MAEzC,CAAC9J,IAGJ,IAoHI+J,EApHE5B,EAAY,SAACJ,GACjB,IAAKA,EACH,MAAO,GAGT,IAAMR,EAASE,EAAU1G,QAAQoH,UAAUJ,EAAMG,MAAOH,EAAM1F,QAE9D,GAA4B,iBAAjBkF,EAAO9J,MAChB8J,EAAO9J,MAAQ,IAAI6B,OAAOC,KAAKyK,YAAYC,MAAM1C,EAAO9J,YACnD,GAAIyM,MAAMC,QAAQ5C,EAAO9J,OAAQ,CAEtC,IAAM2M,EAAoB,IAAI9K,OAAOC,KAAKyK,YAAYC,MAAM1C,EAAO9J,MAAM8J,EAAO9J,MAAM4E,OAAS,IAC/FkF,EAAO9J,MAAQ2M,OACL7C,EAAO9J,QACjB8J,EAAO9J,MAAQiK,EAAc3G,QAAQwF,WAKvC,IAFA,IAEA,MAF0B,CAAC,OAAQ,OAAQ,gBAEP,eAAE,CAAjC,IAAM8D,EAAI,KACT9C,EAAO8C,IAASH,MAAMC,QAAQ5C,EAAO8C,MACvC9C,EAAO8C,QAAQC,GAInB,OAAO/C,GAwBHgD,GAAc,SAACC,EAAWC,GACJ,MAEnB,EAFW,SAAdD,EACe,QAAjB,EAAA/C,EAAU1G,eAAO,OAAjB,EAAmBwG,OAAO,sBAAuBkD,GAEhC,QAAjB,EAAAhD,EAAU1G,eAAO,OAAjB,EAAmBwG,OAAOiD,EAAWC,GAGrB,UAAdD,IACFC,EAAQ,IAAInL,OAAOC,KAAKyK,YAAYC,MAAMQ,IAI5CjD,EAAU,EAAD,KACJD,GAAM,QACRiD,EAAYC,MAKXC,GAAuB,SAACC,EAAUF,GACtC,GAAK5C,EAAoB9G,QAAzB,CAKA,MAA0B0G,EAAU1G,QAAQsH,eAApCH,EAAK,EAALA,MAAO7F,EAAM,EAANA,OACT8D,EAAauB,EAAc3G,QACjCoF,EAAWwE,GAAYF,EACvBhD,EAAU1G,QAAQ6J,OACD,aAAbD,GAAwC,SAAbA,GAC7BE,YAA0B1E,GAE5B2E,YAAW,WACTlD,EAAgB7G,QAAU,CAAEmH,QAAO7F,UACZiG,IAAKyC,uBAAuBC,oBACpCC,SAAS9E,KACvB,QAfDU,EAAiB8D,EAAUF,IAgDvBjE,GAAkB7C,EAAlB6C,cACF0E,GAAW,CACf7B,KAAoD,QAAhD,EAA0C,UAAxC7C,UAAkB,QAAL,EAAbA,GAAgB,UAAE,WAAL,EAAb,EAAqB,uBAAyB,SACpD8C,OAAuD,QAAjD,EAAyC,YAAvC9C,UAAkB,QAAL,EAAbA,GAAgB,UAAE,WAAL,EAAb,EAAqB,sBAA0B,SACvD+C,WAAW/C,UAAkB,QAAL,EAAbA,GAAgB,UAAE,OAAqB,QAArB,EAAlB,EAAqB,0BAAkB,WAA1B,EAAb,EAAyC9G,SAAS,gBAAgB8G,UAAkB,QAAL,EAAbA,GAAgB,UAAE,OAAqB,QAArB,EAAlB,EAAqB,0BAAkB,WAA1B,EAAb,EAAyC9G,SAAS,SAC/H8J,UAA4E,QAAnE,EAAEhD,UAAkB,QAAL,EAAbA,GAAgB,UAAE,OAAqB,QAArB,EAAlB,EAAqB,0BAAkB,WAA1B,EAAb,EAAyC9G,SAAS,uBAAe,SAC5EgK,KAAMlD,UAAkB,QAAL,EAAbA,GAAgB,UAAE,WAAL,EAAb,EAAqB,eAC3BiD,KAAMjD,UAAkB,QAAL,EAAbA,GAAgB,UAAE,WAAL,EAAb,EAAqB,aAC3BmC,YAAa,SAGfoB,EAAa,EAAH,KACLpG,GACAuH,IAGDlE,GAAsBb,IACxBwB,EAAc5G,QAAQsI,KAAO9B,EAAO8B,KACpC1B,EAAc5G,QAAQuI,OAAS/B,EAAO+B,OACtC3B,EAAc5G,QAAQwI,UAAYhC,EAAOgC,UACzC5B,EAAc5G,QAAQyI,UAAYjC,EAAO4D,OACzCxD,EAAc5G,QAAQqK,UAAY7D,EAAOmC,MAAQ/B,EAAc5G,QAAQkI,KACvEtB,EAAc5G,QAAQsK,cAAgB9D,EAAO+D,cAAgB3D,EAAc5G,QAAQmI,UAGrF,IAAMqC,GAAc,CAClBnE,MAAOA,EACPP,iBAAkB6D,GAClBX,WAAYA,EACZyB,WAAW,EACXC,YAAa1E,GAGT2E,GAAiB,CACrB5E,sBA9DgC,SAAC6D,EAAUF,GAC3C,GAAK5C,EAAoB9G,QAAzB,CAKA,IAAM4K,EAAsB,CAC1B,cAAe,OACf,aAAc,SACd,UAAa,YACb,eAAgB,SAChB,cAAe,OACf,YAAa,QAEf,GAAiB,gBAAbhB,GAA2C,cAAbA,EAA0B,CAC1DJ,GAAYoB,EAAoBhB,GAAWF,GAC3C,IAAMmB,EAAWlE,EAAc3G,QAC/B,GAAI6K,EAASC,cACYvD,IAAKyC,uBAAuBC,oBACpCc,iBAAiBF,QAjFP,SAACrE,GAAM,OAAK,WACzC,MAAwBE,EAAU1G,QAAQsH,eAApCH,EAAK,EAALA,MAAO7F,EAAM,EAANA,OACb,GAAe,IAAXA,EAAc,CAChBuF,EAAgB7G,QAAU,CAAEmH,QAAO7F,UACnC,IAAM0J,EAAetE,EAAU1G,QAAQsH,eACvCH,EAAQ6D,EAAa7D,MACrB7F,EAAS0J,EAAa1J,OAExB,IAAM2J,EAAgBvE,EAAU1G,QAAQoH,UAAUD,EAAO7F,GAEzDkI,GAAYhD,GAASyE,EAAczE,KA0EjC0E,CAAuBN,EAAoBhB,GAA3CsB,QApBAnF,EAAsB6D,EAAUF,IA6DlCV,WAAY/C,EAAqBW,EAAc5G,QAAUgJ,EACzDtD,mBAAoBA,EACpBO,mBAAoBA,EACpBD,YAAaA,EACbJ,qBAAsBA,GAGlBuF,GAAc,CAClBpF,sBAAuB4D,GACvBjE,oBAAoB,EACpBO,oBAAoB,EACpBD,aAAa,EACbG,SAAUA,GAGZ,OACE,yBAAKlJ,UAAU,sBACbmO,YAAa,SAACrK,GACG,eAAXA,EAAE5C,MAAyB8H,GAC7BlF,EAAEsK,mBAIN,yBAAKpO,UAAU,cACb,kBAACqO,EAAA,EAAe,KACVd,GACCrE,EAAWgF,GAAcR,MAGlC,kBAAC5M,EAAA,EAAW,CACVC,cAAe,SAACtB,IA9II,SAAC6O,EAAM7O,GAC1BoK,EAAoB9G,QAIzBwJ,GAAY,QAAS9M,EAAME,eAHzBkJ,EAAiByF,EAAM7O,GA6InB8O,CAAkB,YAAa,IAAIjN,OAAOC,KAAKyK,YAAYC,MAAMxM,KAEnEA,MAAOuJ,EAAqBO,EAAO9J,MAAQkG,EAAiB,UAC5D1E,WAAYA,EACZC,KAAM,OACNN,cAAekB,EAAE,mCAKzBqH,EAAoB1I,UAAYA,EAEjB+N,UAAMC,KAAKtF,GCnVXA", "file": "chunks/chunk.48.js", "sourcesContent": ["import React, { useState, useCallback, useEffect, useRef } from 'react';\nimport classNames from 'classnames';\nimport './ColorPicker.scss';\nimport { useTranslation } from 'react-i18next';\nimport PropTypes from 'prop-types';\nimport actions from 'actions';\nimport { useDispatch, useStore, useSelector } from 'react-redux';\nimport Events from 'constants/events';\nimport { getInstanceNode } from 'helpers/getRootNode';\nimport selectors from 'selectors';\nimport Button from 'components/Button';\nimport useFocusHandler from 'hooks/useFocusHandler';\nimport Tooltip from 'components/Tooltip';\n\nconst parseColor = (color) => {\n  if (!color) {\n    return color;\n  }\n  let parsedColor = color;\n  if (parsedColor?.toHexString) {\n    parsedColor = parsedColor.toHexString();\n  }\n  if (parsedColor?.toLowerCase) {\n    parsedColor = parsedColor.toLowerCase();\n  }\n\n  return parsedColor;\n};\n\nconst TRANSPARENT_COLOR = 'transparent';\n\n/* eslint-disable custom/no-hex-colors */\nconst transparentIcon = (\n  <svg\n    width=\"100%\"\n    height=\"100%\"\n    className={classNames('transparent')}\n  >\n    <line stroke=\"#d82e28\" x1=\"0\" y1=\"100%\" x2=\"100%\" y2=\"0\" strokeWidth=\"2\" strokeLinecap=\"round\" />\n  </svg>\n);\n/* eslint-enable custom/no-hex-colors */\n\n\nconst propTypes = {\n  color: PropTypes.any,\n  ariaTypeLabel: PropTypes.string\n};\n\nconst ColorPicker = ({\n  onColorChange,\n  hasTransparentColor = false,\n  color,\n  activeTool,\n  type,\n  ariaTypeLabel\n}) => {\n  const activeToolName = Object.values(window.Core.Tools.ToolNames).includes(activeTool) ? activeTool : window.Core.Tools.ToolNames.EDIT;\n  const store = useStore();\n  const { t } = useTranslation();\n  const dispatch = useDispatch();\n  const [colors] = useSelector((state) => [\n    selectors.getColors(state, activeToolName, type),\n  ]);\n  const [selectedColor, setSelectedColor] = useState();\n  const [isExpanded, setIsExpanded] = useState(false);\n  const forceExpandRef = useRef(true);\n\n  useEffect(() => {\n    forceExpandRef.current = true;\n  }, [activeToolName, color]);\n\n  useEffect(() => {\n    if (color) {\n      setSelectedColor(parseColor(color));\n    }\n  }, [color]);\n\n  const getCustomColorAndRemove = () => {\n    const customColor = selectors.getCustomColor(store.getState());\n    dispatch(actions.setCustomColor(null));\n    return customColor;\n  };\n\n  const handleAddColor = useCallback(() => {\n    dispatch(actions.openElement('ColorPickerModal'));\n    const onVisibilityChanged = (e) => {\n      const { element, isVisible } = e.detail;\n      if (element === 'ColorPickerModal' && !isVisible) {\n        const color = parseColor(getCustomColorAndRemove());\n        if (color) {\n          if (colors.includes(color)) {\n            setSelectedColor(color);\n            onColorChange(color);\n          } else {\n            const newColors = [...colors, color];\n            dispatch(actions.setColors(newColors, activeToolName, type, true));\n            setSelectedColor(color);\n            onColorChange(color);\n          }\n        }\n        getInstanceNode().removeEventListener(Events.VISIBILITY_CHANGED, onVisibilityChanged);\n      }\n    };\n    getInstanceNode().addEventListener(Events.VISIBILITY_CHANGED, onVisibilityChanged);\n  }, [colors?.length, dispatch, setSelectedColor, onColorChange, getCustomColorAndRemove, type, activeToolName]);\n\n  const openColorPickerModalWithFocus = useFocusHandler(handleAddColor);\n\n  const handleDelete = () => {\n    const color = parseColor(selectedColor);\n    const newColors = [...colors];\n    const indexToDelete = newColors.indexOf(color);\n    if (indexToDelete > -1) {\n      const nextIndex = indexToDelete === newColors.length - 1 ? 0 : indexToDelete + 1;\n      setSelectedColor(colors[nextIndex]);\n      onColorChange(colors[nextIndex]);\n      newColors.splice(indexToDelete, 1);\n      dispatch(actions.setColors(newColors, activeToolName, type, true));\n    }\n  };\n\n  const handleCopyColor = () => {\n    const color = parseColor(selectedColor);\n    const newColors = [...colors, color];\n    dispatch(actions.setColors(newColors, activeToolName, type, true));\n  };\n\n  const toggleExpanded = () => {\n    const newValue = !isExpanded;\n    setIsExpanded(newValue);\n  };\n\n  let palette = colors.map((color) => color.toLowerCase());\n  if (hasTransparentColor) {\n    palette.push(TRANSPARENT_COLOR);\n  }\n\n  if (!selectedColor) {\n    setSelectedColor('transparent');\n  }\n\n  if (palette.indexOf(selectedColor) > 6 && !isExpanded && forceExpandRef.current) {\n    setIsExpanded(true);\n    forceExpandRef.current = false;\n  }\n\n  const shouldHideShowMoreButton = palette.length <= 7;\n  const showCopyButtonDisabled = !(selectedColor && !palette.includes(selectedColor));\n  const isDeleteDisabled = palette.length <= 1 || !showCopyButtonDisabled;\n\n  if (!isExpanded) {\n    palette = palette.slice(0, 7);\n  }\n\n  return (\n    <>\n      <div className={classNames('ColorPalette')}>\n        {palette.map((color) => parseColor(color)).map((color, i) => (\n          !color\n            ? <div key={i} className=\"dummy-cell\"/>\n            : <Tooltip content={`${t('option.colorPalette.colorLabel')} ${color?.toUpperCase?.()}`} key={color?.toUpperCase?.()}>\n              <button\n                className=\"cell-container\"\n                onClick={() => {\n                  setSelectedColor(color);\n                  onColorChange(color);\n                }}\n                aria-label={`${ariaTypeLabel} ${t('option.colorPalette.colorLabel')} ${color?.toUpperCase?.()}`}\n                aria-current={parseColor(selectedColor) === color || (!parseColor(selectedColor) && color === TRANSPARENT_COLOR)}\n              >\n                <div\n                  className={classNames({\n                    'cell-outer': true,\n                    active: parseColor(selectedColor) === color || (!parseColor(selectedColor) && color === TRANSPARENT_COLOR),\n                  })}\n                >\n                  <div\n                    className={classNames({\n                      cell: true,\n                      border: true,\n                    })}\n                    style={{ backgroundColor: color }}\n                  >\n                    {color === TRANSPARENT_COLOR && transparentIcon}\n                  </div>\n                </div>\n              </button>\n            </Tooltip>\n        ))}\n      </div>\n      <div className=\"palette-controls\">\n        <div className=\"button-container\">\n          <Button\n            img=\"icon-header-zoom-in-line\"\n            title={t('action.addNewColor')}\n            onClick={openColorPickerModalWithFocus}\n            className=\"control-button\"\n            dataElement=\"addCustomColor\"\n            ariaLabel={`${ariaTypeLabel} ${t('action.addNewColor')} ${t('action.fromCustomColorPicker')}`}\n          />\n          <Button\n            img=\"icon-delete-line\"\n            title={t('action.deleteColor')}\n            onClick={handleDelete}\n            disabled={isDeleteDisabled}\n            className=\"control-button\"\n            dataElement=\"deleteSelectedColor\"\n            ariaLabel={`${ariaTypeLabel} ${t('action.deleteColor')} ${selectedColor}`}\n          />\n          <Button\n            img=\"icon-copy2\"\n            title={t('action.copySelectedColor')}\n            onClick={handleCopyColor}\n            disabled={showCopyButtonDisabled}\n            className=\"control-button\"\n            dataElement=\"copySelectedColor\"\n            ariaLabel={`${ariaTypeLabel} ${t('action.copySelectedColor')} ${selectedColor}`}\n          />\n        </div>\n        <button\n          className={classNames('show-more-button control-button', {\n            hidden: shouldHideShowMoreButton,\n          })}\n          onClick={toggleExpanded}\n          aria-label={`${ariaTypeLabel} ${t(isExpanded ? t('action.showLessColors') : t('action.showMoreColors'))}`}\n        >\n          {t(isExpanded ? 'message.showLess' : 'message.showMore')}\n        </button>\n      </div>\n    </>\n  );\n};\n\nColorPicker.propTypes = propTypes;\n\nexport default ColorPicker;", "import ColorPicker from './ColorPicker';\n\nexport default ColorPicker;", "var api = require(\"!../../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../../node_modules/css-loader/index.js!../../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../../node_modules/sass-loader/dist/cjs.js!./ColorPicker.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.StylePicker .ColorPalette{display:flex;flex-wrap:wrap;display:grid;grid-template-columns:repeat(7,1fr);grid-row-gap:8px;row-gap:8px;justify-items:center}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.StylePicker .ColorPalette{width:196px}}.StylePicker .ColorPalette.padding{padding:4px 12px 8px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .StylePicker .ColorPalette{max-width:450px;width:auto}}@media(max-width:640px)and (-ms-high-contrast:active),(max-width:640px)and (-ms-high-contrast:none){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .StylePicker .ColorPalette{width:308px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .StylePicker .ColorPalette{max-width:450px;width:auto}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.App.is-web-component:not(.is-in-desktop-only-mode) .StylePicker .ColorPalette{width:308px}}}.StylePicker .ColorPalette .cell-container{padding:0;border:none;background-color:transparent;flex:1 0 14%;cursor:pointer;width:var(--cell-border-size);height:var(--cell-border-size);display:flex;align-items:center;justify-content:center}:host(:not([data-tabbing=true])) .StylePicker .ColorPalette .cell-container,html:not([data-tabbing=true]) .StylePicker .ColorPalette .cell-container{outline:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .StylePicker .ColorPalette .cell-container{width:44px;height:44px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .StylePicker .ColorPalette .cell-container{width:44px;height:44px}}.StylePicker .ColorPalette .cell-container .cell-outer.active{border:1px solid var(--color-palette-border);width:var(--cell-outer-border-size);height:var(--cell-outer-border-size);border-radius:10000000px;display:flex;align-items:center;justify-content:center}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .StylePicker .ColorPalette .cell-container .cell-outer.active{width:36px;height:36px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .StylePicker .ColorPalette .cell-container .cell-outer.active{width:36px;height:36px}}.StylePicker .ColorPalette .cell-container .cell-outer .cell{width:18px;height:18px;border-radius:10000000px}.StylePicker .ColorPalette .cell-container .cell-outer .cell .transparent{border:2px solid var(--faded-text);border-radius:10000000px}.StylePicker .ColorPalette .cell-container .cell-outer .cell.border{border:1px solid var(--white-color-palette-border)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .StylePicker .ColorPalette .cell-container .cell-outer .cell{width:24px;height:24px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .StylePicker .ColorPalette .cell-container .cell-outer .cell{width:24px;height:24px}}.StylePicker .palette-controls{padding-right:12px;padding-left:2px;display:flex;justify-content:space-between}.StylePicker .palette-controls .button-container{display:flex;grid-gap:8px;gap:8px}.StylePicker .palette-controls .control-button{display:flex;align-items:center;justify-content:center;text-align:center;min-width:32px;min-height:32px;padding:0;border:none;background-color:transparent;cursor:pointer;border-radius:4px}:host(:not([data-tabbing=true])) .StylePicker .palette-controls .control-button,html:not([data-tabbing=true]) .StylePicker .palette-controls .control-button{outline:none}.StylePicker .palette-controls .control-button.show-more-button{color:var(--ribbon-active-color)}.StylePicker .palette-controls .control-button.show-more-button:hover{background:none;color:var(--primary-button-hover)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .StylePicker .palette-controls .control-button.show-more-button{color:var(--ribbon-active-color)}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .StylePicker .palette-controls .control-button.show-more-button{color:var(--ribbon-active-color)}}.StylePicker .palette-controls .control-button:disabled{cursor:no-drop}.StylePicker .palette-controls .control-button:disabled .Icon{color:var(--disabled-icon)}.StylePicker .palette-controls .control-button.hidden{display:none}.StylePicker .palette-controls .control-button.focus-visible,.StylePicker .palette-controls .control-button:focus-visible{outline:var(--focus-visible-outline)}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./RichTextStyleEditor.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}@keyframes bottom-up{0%{transform:translateY(100%)}to{transform:translateY(0)}}@keyframes up-bottom{0%{transform:translateY(0)}to{transform:translateY(100%)}}.RichTextStyleEditor{margin-bottom:16px}.RichTextStyleEditor .menu-items{margin-bottom:8px!important}.RichTextStyleEditor .menu-items .icon-grid{padding-top:12px;grid-row-gap:12px;row-gap:12px}.RichTextStyleEditor .menu-items .icon-grid .row{padding-top:0}.RichTextStyleEditor .menu-items .icon-grid .row.isRedaction{padding-bottom:8px}.RichTextStyleEditor .menu-items .icon-grid .auto-size-checkbox{padding-top:4px;padding-bottom:8px}.RichTextStyleEditor .menu-items .icon-grid .auto-size-checkbox .ui__choice__input__check--focus{outline:var(--focus-visible-outline)}.RichTextStyleEditor .Dropdown__wrapper{width:100%}.RichTextStyleEditor .Dropdown__wrapper .Dropdown{width:100%!important}.RichTextStyleEditor .Dropdown__wrapper .Dropdown__items{right:unset;width:100%!important}.RichTextStyleEditor .FontSizeDropdown{width:100%!important}.RichTextStyleEditor .ColorPalette{padding-bottom:8px}.RichTextStyleEditor .text-size-slider{margin-top:16px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RichTextStyleEditor .icon-grid{display:flex;flex-direction:column}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RichTextStyleEditor .icon-grid{display:flex;flex-direction:column}}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React, { useState, useEffect, useRef } from 'react';\nimport { useSelector, useDispatch, shallowEqual } from 'react-redux';\nimport PropTypes from 'prop-types';\nimport ColorPicker from 'components/StylePicker/ColorPicker';\nimport core from 'core';\nimport actions from 'actions';\nimport selectors from 'selectors';\nimport './RichTextStyleEditor.scss';\nimport DataElements from 'constants/dataElement';\nimport TextStylePicker from 'components/TextStylePicker';\nimport adjustFreeTextBoundingBox from 'helpers/adjustFreeTextBoundingBox';\nimport { useTranslation } from 'react-i18next';\n\nconst propTypes = {\n  annotation: PropTypes.object,\n  editor: PropTypes.object,\n  style: PropTypes.shape({\n    TextColor: PropTypes.string,\n    RichTextStyle: PropTypes.any,\n  }),\n  isFreeTextAutoSize: PropTypes.bool,\n  onFreeTextSizeToggle: PropTypes.func,\n  onPropertyChange: PropTypes.func,\n  onRichTextStyleChange: PropTypes.func,\n  isRedaction: PropTypes.bool,\n  isRichTextEditMode: PropTypes.bool,\n  setIsRichTextEditMode: PropTypes.func,\n  isWidget: PropTypes.bool,\n};\n\nconst RichTextStyleEditor = ({\n  annotation, editor,\n  style,\n  isFreeTextAutoSize,\n  onFreeTextSizeToggle,\n  onPropertyChange,\n  onRichTextStyleChange,\n  isRichTextEditMode,\n  setIsRichTextEditMode,\n  isRedaction,\n  isWidget,\n  activeTool,\n}) => {\n  const [\n    fonts,\n  ] = useSelector(\n    (state) => [\n      selectors.getFonts(state),\n    ],\n    shallowEqual,\n  );\n\n  const [format, setFormat] = useState({});\n  const editorRef = useRef(null);\n  const annotationRef = useRef(null);\n  const propertiesRef = useRef({});\n  const dispatch = useDispatch();\n  const oldSelectionRef = useRef();\n  const richTextEditModeRef = useRef();\n  richTextEditModeRef.current = isRichTextEditMode;\n  const [t] = useTranslation();\n\n  useEffect(() => {\n    const handleSelectionChange = (range, oldRange) => {\n      const shouldRestoreLostSelection = !range && oldRange && editorRef.current;\n      if (shouldRestoreLostSelection) {\n        editorRef.current.setSelection(oldRange.index, oldRange.length);\n      }\n      if (range && editorRef.current) {\n        setFormat(getFormat(range));\n      }\n    };\n    const handleTextChange = () => {\n      setFormat(getFormat(editorRef.current?.getSelection()));\n    };\n    core.addEventListener('editorSelectionChanged', handleSelectionChange);\n    core.addEventListener('editorTextChanged', handleTextChange);\n    // Have to disable instead of closing because annotation popup will reopen itself\n    dispatch(actions.disableElements([DataElements.ANNOTATION_STYLE_POPUP]));\n    return () => {\n      core.removeEventListener('editorSelectionChanged', handleSelectionChange);\n      core.removeEventListener('editorTextChanged', handleTextChange);\n      dispatch(actions.enableElements([DataElements.ANNOTATION_STYLE_POPUP]));\n    };\n  }, []);\n\n  useEffect(() => {\n    editorRef.current = editor;\n    annotationRef.current = annotation;\n    if (isRichTextEditMode && annotation) {\n      let StrokeStyle = 'solid';\n      try {\n        StrokeStyle = (annotation['Style'] === 'dash')\n          ? `${annotation['Style']},${annotation['Dashes']}`\n          : annotation['Style'];\n      } catch (err) {\n        console.error(err);\n      }\n      const richTextStyles = annotation.getRichTextStyle();\n      const stylesTemp = richTextStyles[0];\n\n      propertiesRef.current = {\n        Font: annotation.Font,\n        FontSize: annotation.FontSize,\n        TextAlign: annotation.TextAlign,\n        TextVerticalAlign: annotation.TextVerticalAlign,\n        bold: stylesTemp?.['font-weight'] === 'bold' ?? false,\n        italic: stylesTemp?.['font-style'] === 'italic' ?? false,\n        underline: stylesTemp?.['text-decoration']?.includes('underline')\n          || stylesTemp?.['text-decoration']?.includes('word'),\n        strikeout: stylesTemp?.['text-decoration']?.includes('line-through') ?? false,\n        size: stylesTemp?.['font-size'],\n        font: stylesTemp?.['font-family'],\n        StrokeStyle,\n        calculatedFontSize: annotation.getCalculatedFontSize()\n      };\n    }\n\n    setFormat(getFormat(editorRef.current?.getSelection()));\n\n    if (oldSelectionRef.current) {\n      editorRef.current.setSelection(oldSelectionRef.current);\n      oldSelectionRef.current = null;\n    }\n  }, [annotation, editor, isRichTextEditMode]);\n\n  useEffect(() => {\n    const handleEditorBlur = () => {\n      editorRef.current = null;\n      annotationRef.current = null;\n      setIsRichTextEditMode(false);\n    };\n    const handleEditorFocus = () => {\n      setIsRichTextEditMode(true);\n    };\n\n    core.addEventListener('editorBlur', handleEditorBlur);\n    core.addEventListener('editorFocus', handleEditorFocus);\n    return () => {\n      core.removeEventListener('editorBlur', handleEditorBlur);\n      core.removeEventListener('editorFocus', handleEditorFocus);\n    };\n  }, [dispatch]);\n\n\n  const getFormat = (range) => {\n    if (!range) {\n      return {};\n    }\n\n    const format = editorRef.current.getFormat(range.index, range.length);\n\n    if (typeof format.color === 'string') {\n      format.color = new window.Core.Annotations.Color(format.color);\n    } else if (Array.isArray(format.color)) {\n      // the selection contains multiple color, so we set the current color to the last selected color\n      const lastSelectedColor = new window.Core.Annotations.Color(format.color[format.color.length - 1]);\n      format.color = lastSelectedColor;\n    } else if (!format.color) {\n      format.color = annotationRef.current.TextColor;\n    }\n\n    const propertiesToCheck = ['font', 'size', 'originalSize'];\n\n    for (const prop of propertiesToCheck) {\n      if (format[prop] && Array.isArray(format[prop])) {\n        format[prop] = undefined;\n      }\n    }\n\n    return format;\n  };\n\n  const handleTextFormatChange = (format) => () => {\n    let { index, length } = editorRef.current.getSelection();\n    if (length === 0) {\n      oldSelectionRef.current = { index, length };\n      const newSelection = editorRef.current.getSelection();\n      index = newSelection.index;\n      length = newSelection.length;\n    }\n    const currentFormat = editorRef.current.getFormat(index, length);\n\n    applyFormat(format, !currentFormat[format]);\n  };\n\n  const handleColorChange = (name, color) => {\n    if (!richTextEditModeRef.current) {\n      onPropertyChange(name, color);\n      return;\n    }\n    applyFormat('color', color.toHexString());\n  };\n\n  const applyFormat = (formatKey, value) => {\n    if (formatKey === 'size') {\n      editorRef.current?.format('applyCustomFontSize', value);\n    } else {\n      editorRef.current?.format(formatKey, value);\n    }\n\n    if (formatKey === 'color') {\n      value = new window.Core.Annotations.Color(value);\n    }\n\n    // format the entire editor doesn't trigger the editorTextChanged event, so we set the format state here\n    setFormat({\n      ...format,\n      [formatKey]: value\n    });\n  };\n\n  // onPropertyChange\n  const handlePropertyChange = (property, value) => {\n    if (!richTextEditModeRef.current) {\n      onPropertyChange(property, value);\n      return;\n    }\n\n    const { index, length } = editorRef.current.getSelection();\n    const annotation = annotationRef.current;\n    annotation[property] = value;\n    editorRef.current.blur();\n    if (property === 'FontSize' || property === 'Font') {\n      adjustFreeTextBoundingBox(annotation);\n    }\n    setTimeout(() => {\n      oldSelectionRef.current = { index, length };\n      const editBoxManager = core.getAnnotationManager().getEditBoxManager();\n      editBoxManager.focusBox(annotation);\n    }, 0);\n  };\n\n\n  // onRichTextStyleChange\n  const handleRichTextStyleChange = (property, value) => {\n    if (!richTextEditModeRef.current) {\n      onRichTextStyleChange(property, value);\n      return;\n    }\n\n    const propertyTranslation = {\n      'font-weight': 'bold',\n      'font-style': 'italic',\n      'underline': 'underline',\n      'line-through': 'strike',\n      'font-family': 'font',\n      'font-size': 'size',\n    };\n    if (property === 'font-family' || property === 'font-size') {\n      applyFormat(propertyTranslation[property], value);\n      const freeText = annotationRef.current;\n      if (freeText.isAutoSized()) {\n        const editBoxManager = core.getAnnotationManager().getEditBoxManager();\n        editBoxManager.resizeAnnotation(freeText);\n      }\n    } else {\n      handleTextFormatChange(propertyTranslation[property])();\n    }\n  };\n\n  let properties = {};\n\n  const { RichTextStyle } = style;\n  const defaults = {\n    bold: RichTextStyle?.[0]?.['font-weight'] === 'bold' ?? false,\n    italic: RichTextStyle?.[0]?.['font-style'] === 'italic' ?? false,\n    underline: RichTextStyle?.[0]?.['text-decoration']?.includes('underline') || RichTextStyle?.[0]?.['text-decoration']?.includes('word'),\n    strikeout: RichTextStyle?.[0]?.['text-decoration']?.includes('line-through') ?? false,\n    font: RichTextStyle?.[0]?.['font-family'],\n    size: RichTextStyle?.[0]?.['font-size'],\n    StrokeStyle: 'solid',\n  };\n\n  properties = {\n    ...style,\n    ...defaults,\n  };\n\n  if (isRichTextEditMode && annotation) {\n    propertiesRef.current.bold = format.bold;\n    propertiesRef.current.italic = format.italic;\n    propertiesRef.current.underline = format.underline;\n    propertiesRef.current.strikeout = format.strike;\n    propertiesRef.current.quillFont = format.font || propertiesRef.current.Font;\n    propertiesRef.current.quillFontSize = format.originalSize || propertiesRef.current.FontSize;\n  }\n\n  const commonProps = {\n    fonts: fonts,\n    onPropertyChange: handlePropertyChange,\n    properties: properties,\n    stateless: true,\n    isFreeText: !isRedaction,\n  };\n\n  const nonWidgetProps = {\n    onRichTextStyleChange: handleRichTextStyleChange,\n    properties: isRichTextEditMode ? propertiesRef.current : properties,\n    isFreeTextAutoSize: isFreeTextAutoSize,\n    isRichTextEditMode: isRichTextEditMode,\n    isRedaction: isRedaction,\n    onFreeTextSizeToggle: onFreeTextSizeToggle,\n  };\n\n  const widgetProps = {\n    onRichTextStyleChange: handlePropertyChange,\n    isFreeTextAutoSize: false,\n    isRichTextEditMode: false,\n    isRedaction: false,\n    isWidget: isWidget,\n  };\n\n  return (\n    <div className=\"RichTextStyleEditor\"\n      onMouseDown={(e) => {\n        if (e.type !== 'touchstart' && isRichTextEditMode) {\n          e.preventDefault();\n        }\n      }}\n    >\n      <div className=\"menu-items\">\n        <TextStylePicker\n          {...commonProps}\n          {...(isWidget ? widgetProps : nonWidgetProps)}\n        />\n      </div>\n      <ColorPicker\n        onColorChange={(color) => {\n          handleColorChange('TextColor', new window.Core.Annotations.Color(color));\n        }}\n        color={isRichTextEditMode ? format.color : style['TextColor']}\n        activeTool={activeTool}\n        type={'Text'}\n        ariaTypeLabel={t('option.stylePopup.textStyle')}\n      />\n    </div>\n  );\n};\nRichTextStyleEditor.propTypes = propTypes;\n\nexport default React.memo(RichTextStyleEditor);\n", "import RichTextStyleEditor from './RichTextStyleEditor';\n\nexport default RichTextStyleEditor;"], "sourceRoot": ""}