{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/eo.js"], "names": ["module", "exports", "e", "a", "default", "o", "t", "name", "weekdays", "split", "months", "weekStart", "weekdaysShort", "monthsShort", "weekdaysMin", "ordinal", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "relativeTime", "future", "past", "s", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "locale"], "mappings": "gFAAoEA,EAAOC,QAA6K,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAGI,EAAE,CAACC,KAAK,KAAKC,SAAS,qDAAqDC,MAAM,KAAKC,OAAO,6FAA6FD,MAAM,KAAKE,UAAU,EAAEC,cAAc,gCAAgCH,MAAM,KAAKI,YAAY,kDAAkDJ,MAAM,KAAKK,YAAY,uBAAuBL,MAAM,KAAKM,QAAQ,SAASb,GAAG,OAAOA,GAAGc,QAAQ,CAACC,GAAG,QAAQC,IAAI,WAAWC,EAAE,aAAaC,GAAG,sBAAsBC,IAAI,4BAA4BC,KAAK,wCAAwCC,aAAa,CAACC,OAAO,UAAUC,KAAK,WAAWC,EAAE,WAAWC,EAAE,SAASC,GAAG,aAAaC,EAAE,OAAOC,GAAG,WAAWC,EAAE,OAAOC,GAAG,WAAWC,EAAE,SAASC,GAAG,aAAaC,EAAE,OAAOC,GAAG,aAAa,OAAOjC,EAAEC,QAAQiC,OAAO/B,EAAE,MAAK,GAAIA,EAAriCD,CAAE,EAAQ", "file": "chunks/chunk.140.js", "sourcesContent": ["!function(e,o){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=o(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],o):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_eo=o(e.dayjs)}(this,(function(e){\"use strict\";function o(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var a=o(e),t={name:\"eo\",weekdays:\"dimanĉo_lundo_mardo_merkredo_ĵaŭdo_vendredo_sabato\".split(\"_\"),months:\"januaro_februaro_marto_aprilo_majo_junio_julio_aŭgusto_septembro_oktobro_novembro_decembro\".split(\"_\"),weekStart:1,weekdaysShort:\"dim_lun_mard_merk_ĵaŭ_ven_sab\".split(\"_\"),monthsShort:\"jan_feb_mar_apr_maj_jun_jul_aŭg_sep_okt_nov_dec\".split(\"_\"),weekdaysMin:\"di_lu_ma_me_ĵa_ve_sa\".split(\"_\"),ordinal:function(e){return e},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"YYYY-MM-DD\",LL:\"D[-a de] MMMM, YYYY\",LLL:\"D[-a de] MMMM, YYYY HH:mm\",LLLL:\"dddd, [la] D[-a de] MMMM, YYYY HH:mm\"},relativeTime:{future:\"post %s\",past:\"antaŭ %s\",s:\"sekundoj\",m:\"minuto\",mm:\"%d minutoj\",h:\"horo\",hh:\"%d horoj\",d:\"tago\",dd:\"%d tagoj\",M:\"monato\",MM:\"%d monatoj\",y:\"jaro\",yy:\"%d jaroj\"}};return a.default.locale(t,null,!0),t}));"], "sourceRoot": ""}