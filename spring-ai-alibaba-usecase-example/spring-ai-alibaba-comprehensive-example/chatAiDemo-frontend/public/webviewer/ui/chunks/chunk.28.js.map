{"version": 3, "sources": ["webpack:///./src/ui/src/components/Note/Context.js", "webpack:///./src/ui/src/helpers/setAnnotationRichTextStyle.js", "webpack:///./src/ui/src/components/NoteTextarea/CommentTextarea/CommentTextarea.js", "webpack:///./src/ui/src/components/NoteTextarea/NoteTextarea.js", "webpack:///./src/ui/src/components/NoteTextarea/index.js", "webpack:///./src/ui/src/helpers/NoteStateUtils.js", "webpack:///./src/ui/src/components/ModularComponents/NoteStateFlyout/NoteStateFlyout.js", "webpack:///./src/ui/src/components/ModularComponents/NoteStateFlyout/index.js", "webpack:///./src/ui/src/constants/quill.scss?ee40", "webpack:///./src/ui/src/constants/quill.scss", "webpack:///./src/ui/src/components/NoteTextarea/CommentTextarea/CommentTextarea.scss?e393", "webpack:///./src/ui/src/components/NoteTextarea/CommentTextarea/CommentTextarea.scss", "webpack:///./src/ui/src/components/NotePopup/NotePopup.scss?1d48", "webpack:///./src/ui/src/components/NotePopup/NotePopup.scss", "webpack:///./src/ui/src/components/NoteHeader/NoteHeader.scss?f278", "webpack:///./src/ui/src/components/NoteHeader/NoteHeader.scss", "webpack:///./src/ui/src/components/ReplyAttachmentList/ReplyAttachmentList.scss?6b77", "webpack:///./src/ui/src/components/ReplyAttachmentList/ReplyAttachmentList.scss", "webpack:///./src/ui/src/components/NoteContent/NoteContent.scss?62b0", "webpack:///./src/ui/src/components/NoteContent/NoteContent.scss", "webpack:///./src/ui/src/components/Note/ReplyArea/ReplyArea.scss?59a5", "webpack:///./src/ui/src/components/Note/ReplyArea/ReplyArea.scss", "webpack:///./src/ui/src/components/AnnotationNoteConnectorLine/AnnotationNoteConnectorLine.scss?7f2c", "webpack:///./src/ui/src/components/AnnotationNoteConnectorLine/AnnotationNoteConnectorLine.scss", "webpack:///./src/ui/src/components/Note/Note.scss?66f6", "webpack:///./src/ui/src/components/Note/Note.scss", "webpack:///./src/ui/src/components/NotesPanel/ReplyAttachmentPicker.js", "webpack:///./src/ui/src/components/NotePopup/NotePopup.js", "webpack:///./src/ui/src/components/NotePopup/NotePopupContainer.js", "webpack:///./src/ui/src/components/NotePopup/index.js", "webpack:///./src/ui/src/components/NoteState/NoteState.js", "webpack:///./src/ui/src/components/NoteState/NoteStateContainer.js", "webpack:///./src/ui/src/components/NoteState/index.js", "webpack:///./src/ui/src/components/NoteUnpostedCommentIndicator/NoteUnpostedCommentIndicator.js", "webpack:///./src/ui/src/components/NoteUnpostedCommentIndicator/NoteUnpostedCommentIndicatorContainer.js", "webpack:///./src/ui/src/components/NoteUnpostedCommentIndicator/index.js", "webpack:///./src/ui/src/components/NoteHeader/NoteHeader.js", "webpack:///./src/ui/src/components/NoteHeader/index.js", "webpack:///./src/ui/src/components/NoteTextPreview/NoteTextPreviewContainer.js", "webpack:///./src/ui/src/components/NoteTextPreview/index.js", "webpack:///./src/ui/src/helpers/ReplyAttachmentManager.js", "webpack:///./src/ui/src/helpers/sanitizeSVG.js", "webpack:///./src/ui/src/components/ReplyAttachmentList/ReplyAttachmentList.js", "webpack:///./src/ui/src/components/ReplyAttachmentList/index.js", "webpack:///./src/ui/src/helpers/setReactQuillContent.js", "webpack:///./src/ui/src/components/NoteContent/NoteContent.js", "webpack:///./src/ui/src/components/NoteContent/index.js", "webpack:///./src/ui/src/components/Note/ReplyArea/ReplyArea.js", "webpack:///./src/ui/src/components/Note/ReplyArea/index.js", "webpack:///./src/ui/src/components/Note/NoteGroupSection.js", "webpack:///./src/ui/src/components/AnnotationNoteConnectorLine/AnnotationNoteConnectorLine.js", "webpack:///./src/ui/src/components/AnnotationNoteConnectorLine/index.js", "webpack:///./src/ui/src/components/Note/Note.js", "webpack:///./src/ui/src/components/Note/index.js", "webpack:///./src/ui/src/components/InlineCommentingPopup/InlineCommentingPopup.scss?7354", "webpack:///./src/ui/src/components/InlineCommentingPopup/InlineCommentingPopup.scss", "webpack:///./src/ui/src/components/InlineCommentingPopup/InlineCommentingPopup.js", "webpack:///./src/ui/src/components/InlineCommentingPopup/InlineCommentingPopupContainer.js", "webpack:///./src/ui/src/components/InlineCommentingPopup/index.js"], "names": ["NoteContext", "React", "createContext", "setAnnotationRichTextStyle", "editor", "annotation", "richTextStyle", "ops", "getContents", "breakpoint", "for<PERSON>ach", "item", "attributes", "isMention", "insert", "mention", "value", "denotationChar", "cssStyle", "undefined", "bold", "italic", "color", "underline", "strike", "size", "font", "length", "setRichTextStyle", "i", "globalUserData", "formats", "Keyboard", "<PERSON><PERSON><PERSON>", "CustomKeyboard", "DEFAULTS", "bindings", "register", "Clipboard", "quillShadowDOMWorkaround", "window", "Core", "QuillPasteExtra", "quill", "options", "mentionModule", "<PERSON><PERSON><PERSON><PERSON>", "mentionDenotationChars", "mentionContainerClass", "mentionListClass", "listItemClass", "renderItem", "div", "document", "createElement", "innerText", "email", "para", "className", "append<PERSON><PERSON><PERSON>", "source", "searchTerm", "renderList", "mentionsSearchFunction", "mentions<PERSON>anager", "getMentionLookupCallback", "foundUsers", "CommentTextarea", "forwardRef", "ref", "onChange", "onKeyDown", "onBlur", "onFocus", "userData", "isReply", "t", "useTranslation", "isAddReplyAttachmentDisabled", "useSelector", "state", "selectors", "isElementDisabled", "DataElements", "NotesPanel", "ADD_REPLY_ATTACHMENT_BUTTON", "split", "contentArray", "map", "paragraph", "outerHTML", "join", "onClick", "e", "preventDefault", "stopPropagation", "onScroll", "style", "overflowY", "ele", "getEditor", "root", "aria<PERSON><PERSON><PERSON>", "modules", "theme", "placeholder", "<PERSON><PERSON>", "dataElement", "img", "title", "getRootNode", "querySelector", "click", "displayName", "propTypes", "PropTypes", "string", "func", "isRequired", "onSubmit", "NoteTextarea", "props", "forwardedRef", "getUserData", "isNoteSubmissionWithEnterEnabled", "getAutoFocusNoteOnAnnotationSelection", "getIsNoteEditing", "shallowEqual", "canSubmitByEnter", "resize", "useContext", "textareaRef", "useRef", "prevHeightRef", "useLayoutEffect", "boxDOMElement", "current", "container", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "boundingBox", "getBoundingClientRect", "height", "textareaProps", "el", "throttle", "content", "delta", "replace", "getText", "trim", "target", "doesDeltaContainMention", "formattedText", "getFormattedTextFromDeltas", "mentionData", "extractMentionDataFromStr", "totalTextLength", "plainTextValue", "textareaEditor", "setTimeout", "setSelection", "which", "isSubmittingByEnter", "isSubmittingByCtrlEnter", "metaKey", "ctrl<PERSON>ey", "createStateAnnotation", "documentViewerKey", "stateAnnotation", "Annotations", "StickyAnnotation", "core", "getCurrentUser", "enableSkipAutoLink", "displayAuthor", "getDisplayAuthor", "stateMessage", "i18next", "toLowerCase", "contents", "setContents", "createFlyoutItem", "option", "icon", "label", "noteStateFlyoutItems", "NoteStateFlyout", "noteId", "handleStateChange", "isMultiSelectMode", "dispatch", "useDispatch", "selectorSuffix", "flyoutSelector", "NOTE_STATE_FLYOUT", "currentFlyout", "getFlyout", "noteStateFlyout", "items", "noteState", "actions", "updateFlyout", "addFlyout", "bool", "api", "__esModule", "default", "module", "styleTag", "isApryseWebViewerWebComponent", "head", "webComponents", "getElementsByTagName", "findNestedWebComponents", "tagName", "elements", "querySelectorAll", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "ReplyAttachmentPicker", "annotationId", "addAttachments", "replyAttachmentHandler", "getReplyAttachmentHandler", "file", "files", "attachment", "url", "name", "type", "id", "display", "notePopupFlyoutItems", "handleEdit", "handleDelete", "isEditable", "isDeletable", "noop", "NotePopup", "customizableUI", "getFeatureFlags", "NOTE_POPUP_FLYOUT", "notePopupButtonClass", "classNames", "optionsClass", "ToggleElementButton", "toggleElement", "disabled", "handleClick", "selection", "NotePopupFlyout", "filter", "notePopupFlyout", "NotePopupContainer", "activeDocumentViewerKey", "getActiveDocumentViewerKey", "setIsEditing", "noteIndex", "useState", "canModify", "setCanModify", "canModifyContents", "setCanModifyContents", "useEffect", "onUpdateAnnotationPermission", "addEventListener", "removeEventListener", "passProps", "useCallback", "FreeTextAnnotation", "getAnnotationManager", "isFreeTextEditingEnabled", "trigger", "deleteAnnotations", "getGroupedChildren", "NoDelete", "Id", "object", "NoteState", "annotationState", "getStatus", "NoteStateContainer", "isNoteStateDisabled", "useFocusOnClose", "newValue", "addReply", "annotationManager", "addAnnotation", "getRootAnnotation", "pendingEditTextMap", "pendingReplyMap", "pendingAttachmentMap", "NoteUnpostedCommentIndicator", "hasUnpostedComment", "setHasUnpostedComment", "hasUnpostedReply", "setHasUnpostedReply", "hasUnpostedAttachment", "setHasUnpostedAttachment", "data-element", "<PERSON><PERSON><PERSON>", "Icon", "glyph", "NoteUnpostedCommentIndicatorContainer", "isDisabled", "iconColor", "fillColor", "language", "noteDateFormat", "isSelected", "notesShowLastUpdatedDate", "isUnread", "renderAuthorName", "isEditing", "number", "sortStrategy", "activeTheme", "isMultiSelected", "handleMultiSelect", "isGroupMember", "showAnnotationNumbering", "isTrackedChange", "<PERSON><PERSON><PERSON><PERSON>", "date", "timezone", "dateCreated", "NotesPanelSortStrategy", "MODIFIED_DATE", "CREATED_DATE", "getLatestActivityDate", "DateCreated", "datetimeStr", "toLocaleString", "timeZone", "Date", "noteDateAndTime", "dayjs", "locale", "format", "numberOfReplies", "getReplies", "toHexString", "Theme", "DARK", "isDarkColorHex", "COMMON_COLORS", "LIGHT", "isLightColorHex", "getColor", "FillColor", "annotationAssociatedNumber", "getAssociatedNumber", "annotationDisplayedAssociatedNumber", "authorAndDateClass", "noteHeaderClass", "parent", "PageNumber", "Choice", "aria-label", "checked", "trackedChangeId", "getCustomData", "OFFICE_EDITOR_TRACKED_CHANGE_KEY", "getOfficeEditor", "acceptTrackedChange", "iconClassName", "rejectTrackedChange", "NoteTextPreviewContainer", "notePanelWidth", "getNotesPanelWidth", "NoteTextPreview", "panelWidth", "icons", "FileAttachmentUtils", "decompressFileContent", "decompressWithFlateDecode", "setAnnotationAttachments", "setAttachments", "isImage", "startsWith", "getAttachmentIcon", "pop", "readAsText", "svg", "Promise", "resolve", "toString", "fileReader", "FileReader", "result", "isSVG", "sanitizeSVG", "svgText", "forbiddenTags", "DOMPurify", "addHook", "_", "hookEvent", "allowedTags", "clean", "sanitize", "Blob", "isDirty", "ImagePreview", "src", "setSrc", "isDirtySVG", "setIsDirtySvg", "fileToSanitize", "isImageFromPDF", "File", "URL", "createObjectURL", "processImagePreview", "ReplyAttachmentList", "fileDeleted", "getTabManager", "isReplyAttachmentPreviewEnabled", "tabManager", "previewEnabled", "console", "warn", "fileData", "addTab", "filename", "setActive", "saveCurrentActiveTabState", "onDownload", "saveAs", "key", "onDelete", "getAttributtes", "element", "attr", "decoration", "includes", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>nt", "getRichTextStyle", "indexes", "Object", "keys", "text", "isNaN", "lastIndex", "textSlice", "slice", "extend", "LocalizedFormat", "isNonReplyNoteRead", "onReplyClicked", "<PERSON><PERSON><PERSON><PERSON>", "getNoteDateFormat", "getIconColor", "mapAnnotationToKey", "getCurrentLanguage", "canCollapseTextPreview", "isNotesPanelTextCollapsingEnabled", "canCollapseReplyPreview", "isNotesPanelRepliesCollapsingEnabled", "getActiveTheme", "getTimezone", "searchInput", "onTopNoteContentClicked", "setPendingEditText", "annotationMapKeys", "TRACKED_CHANGE", "attachments", "getAttachments", "annotationChangedListener", "annotations", "action", "annot", "useDidUpdate", "finishNoteEditing", "customData", "highlightSearchInput", "skipAutoLink", "getSkipAutoLink", "renderContents", "fontColor", "autolinkerContent", "Autolinker", "link", "stripPrefix", "stripTrailingSlash", "replaceFn", "match", "href", "getAnchorHref", "anchorText", "getAnchorText", "offset", "getOffset", "getType", "start", "end", "getMatchedText", "highlightResult", "linesToBreak", "comment", "renderRichText", "beforeContent", "fontWeight", "contentToRender", "strIdx", "anchorData", "forIdx", "rel", "getDataWithKey", "JSON", "parse", "contentsToRender", "textColor", "Color", "a", "textAreaValue", "thereIsNoUnpostedEdit", "handleContentsClicked", "getSelection", "noteContentClass", "unread", "clicked", "useMemo", "contentStyle", "onTextAreaValueChange", "pendingText", "textPreview", "highlightSearchResult", "shouldCollapseAnnotationText", "isString", "DataElementWrapper", "paddingRight", "header", "ContentArea", "getIsMentionEnabled", "INLINE_COMMENT_POPUP", "isElementOpen", "NOTES_PANEL", "isAnyCustomPanelOpen", "autoFocusNoteOnAnnotationSelection", "isMentionEnabled", "isInlineCommentDisabled", "isInlineCommentOpen", "isNotesPanelOpen", "setCurAnnotId", "deleteAttachment", "clearAttachments", "shouldNotFocusOnInput", "isMobile", "setText", "ids", "focus", "textLength", "<PERSON><PERSON><PERSON><PERSON>", "pendingAttachments", "disableSkipAutoLink", "extractMentionDataFromAnnot", "mentions", "setCustomData", "stringify", "drawAnnotationsFromList", "contentClassName", "relatedTarget", "getAttribute", "getRichTextSpan", "fontStyle", "textDecoration", "styles", "indices", "Number", "sort", "b", "index", "Math", "min", "max", "styleIndices", "fullText", "loweredText", "loweredSearchInput", "lastFoundInstance", "indexOf", "allFoundPositions", "regexSafeSearchInput", "RegExp", "test", "position", "idx", "substring", "ReplyArea", "onPendingReplyChange", "isDocumentReadOnly", "getIsReplyDisabled", "isReadOnly", "isReplyDisabled", "isReplyDisabledForAnnotation", "isNoteEditingTriggeredByAnnotationPopup", "isContentEditable", "setPendingReply", "isExpandedFromSearch", "scrollToSelectedAnnot", "isFocused", "setIsFocused", "postReply", "replyText", "replyAnnotation", "createMentionReply", "addAnnotations", "createAnnotationReply", "ifReplyNotAllowed", "replyAreaClass", "onMouseDown", "handleNoteTextareaChange", "isSubmitType", "groupAnnotations", "array", "NoteGroupSection", "isViewingGroupAnnots", "setIsViewingGroupAnnots", "ViewAllAnnotsButton", "CloseAllAnnotsButton", "groupAnnotation", "selectAnnotation", "jumpToAnnotation", "openElement", "LineConnectorPortal", "children", "mount", "setAttribute", "ANNOTATION_NOTE_CONNECTOR_LINE", "<PERSON><PERSON><PERSON><PERSON>", "createPortal", "AnnotationNoteConnectorLine", "noteContainerRef", "isCustomPanelOpen", "getDocumentContainerWidth", "getDocumentContainerHeight", "lineIsOpen", "notePanelIsOpen", "isLineDisabled", "documentContainerWidth", "documentContainerHeight", "rightHorizontalLineWidth", "setRightHorizontalLineWidth", "rightHorizontalLineTop", "setRightHorizontalLineTop", "rightHorizontalLineRight", "setRightHorizontalLineRight", "leftHorizontalLineWidth", "setLeftHorizontalLineWidth", "leftHorizontalLineTop", "setLeftHorizontalLineTop", "leftHorizontalLineRight", "setLeftHorizontalLineRight", "getAnnotationPosition", "annotationBottomRight", "bottomRight", "annotationTopLeft", "topLeft", "getAnnotationLineOffset", "Subject", "getScrollViewElement", "scrollTop", "scrollLeft", "closeElement", "annotWidthInPixels", "x", "annotHeightInPixels", "y", "viewerWidth", "host", "clientWidth", "innerWidth", "viewerOffsetTop", "offsetTop", "top", "lineWidth", "noZoomRefPoint", "getNoZoomReferencePoint", "noZoomRefShiftX", "NoZoom", "noZoomRefShiftY", "onPageNumberUpdated", "verticalHeight", "abs", "verticalTop", "width", "right", "isInNotesPanel", "currId", "Note", "shouldHideConnectorLine", "containerRef", "containerHeightRef", "isEditingMap", "setIsEditingMap", "unreadReplyIdSet", "Set", "getNoteTransformFunction", "getCustomNoteSelectionFunction", "getUnreadAnnotationIdSet", "isCommentThreadExpansionEnabled", "isRightClickAnnotationPopupEnabled", "getIsOfficeEditorMode", "getOfficeEditorEditMode", "noteTransformFunction", "customNoteSelectionFunction", "unreadAnnotationIdSet", "shouldExpandCommentThread", "isOfficeEditorMode", "officeEditorEditMode", "replies", "r", "has", "add", "setAnnotationReadState", "isRead", "prevHeight", "currHeight", "round", "notesPanelElement", "child", "parentNode", "handleNoteClick", "deselectAllAnnotations", "OfficeEditorEditMode", "PREVIEW", "ANNOTATION_POPUP", "moveCursorToTrackedChange", "freezeMainCursor", "hasUnreadReplies", "noteClass", "expanded", "repliesClass", "hidden", "reply", "showReplyArea", "values", "some", "val", "handleReplyClicked", "markAllRepliesRead", "repliesSetToRead", "selectAnnotations", "getGroupAnnotations", "isGroup", "lastReplyId", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aria<PERSON>urrent", "isUndraggable", "isNotesPanelClosed", "popupRef", "any", "closeAndReset", "commentingAnnotation", "contextValue", "annotationForAttachment", "InlineCommentingPopup", "isExpanded", "setExpanded", "inlineCommentPopup", "Popup", "open", "trackedChangePopup", "onMouseMove", "INLINE_COMMENT_POPUP_EXPAND_BUTTON", "INLINE_COMMENT_POPUP_CLOSE_BUTTON", "Provider", "cancel", "InlineCommentingPopupContainer", "getNotesInLeftPanel", "LEFT_PANEL", "getActiveLeftPanel", "isAnnotationNumberingEnabled", "getSortStrategy", "notesInLeftPanel", "isLeftPanelOpen", "activeLeftPanel", "left", "setPosition", "isPhone", "isMobileDevice", "isIE", "isNotesPanelOpenOrActive", "useOnClickOutside", "notesPanel", "clickedInNotesPanel", "contains", "clickedInNoteStateFlyout", "datePicker", "getDatePicker", "warningModal", "getOpenedWarningModal", "colorPicker", "getOpenedColorPicker", "setPopupPosition", "getPopupPosition", "handleResize", "debounce", "setPendingAttachmentMap", "annotationID", "setAnnotationForAttachment", "setPendingEditTextMap", "setPendingReplyMap", "pendingReply", "getDocument", "workerTypes", "OFFICE_EDITOR", "attachmentList", "splice"], "mappings": "4FAAA,WAEMA,EAFN,OAEoBC,EAAMC,gBAEXF,O,wECyCAG,IA7CoB,SAACC,EAAQC,GAC1C,IAAMC,EAAgB,GAChBC,EAAMH,EAAOI,cAAcD,IAC7BE,EAAa,EACjBF,EAAIG,SAAQ,SAACC,GAAS,MACdC,EAAaD,EAAKC,WAClBC,EAAuB,QAAd,EAAGF,EAAKG,cAAM,aAAX,EAAaC,QAC3BC,EAAQL,EAAKG,OACjB,GAAID,EAAW,CACb,IAAME,EAAUJ,EAAKG,OAAOC,QAC5BC,EAAQD,EAAQE,eAAiBF,EAAQC,MAE3C,IAAME,EAAW,IACbN,aAAkDO,EAAYP,EAAWQ,QAC3EF,EAAS,eAAiB,SAExBN,aAAkDO,EAAYP,EAAWS,UAC3EH,EAAS,cAAgB,WAEvBN,aAAkDO,EAAYP,EAAWU,SAC3EJ,EAAgB,MAAIN,EAAWU,QAE7BV,aAAkDO,EAAYP,EAAWW,aAC3EL,EAAS,mBAAqB,SAE5BN,aAAkDO,EAAYP,EAAWY,UACvEN,EAAS,mBACXA,EAAS,mBAAqB,GAAH,OAAMA,EAAS,mBAAkB,iBAE5DA,EAAS,mBAAqB,iBAG9BN,aAAkDO,EAAYP,EAAWa,QAC3EP,EAAS,aAAeN,EAAWa,OAEjCb,aAAkDO,EAAYP,EAAWc,QAC3ER,EAAS,eAAiBN,EAAWc,MAGvCpB,EAAcG,GAAcS,EAC5BT,GAAcO,EAAMW,UAEtBtB,EAAWuB,iBAAiBtB,K,45ECzC9B,8lGAAAuB,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,uZAAAA,EAAA,EAAAA,EAAA,SAAAA,IAAA,SAAAA,GAAA,q+DAaA,IAAIC,EAAiB,GAIfC,EAAU,CACd,aACA,OACA,QACA,OACA,OACA,SACA,OACA,OACA,SACA,SACA,YACA,aACA,SACA,SACA,OACA,QACA,YACA,aACA,UACA,WAIIC,EAAWC,QAAK,OAAQ,oBAExBC,EAAc,iGAASF,GAAQ,EAA/BE,EAAc,kBAEbF,EAASG,UAAQ,IACpBC,SAAU,EAAF,KACHJ,EAASG,SAASC,UAAQ,IAC7B,qBAAiBjB,OAKvBc,QAAMI,SAAS,mBAAoBH,GAAgB,GAGnD,IAAMI,EAAYL,QAAK,OAAQ,qBACvBM,EAA6BC,OAAOC,KAApCF,yBAEFG,EAAe,8BACnB,WAAYC,EAAOC,GACe,OADN,UAC1BL,EAAyBI,GAAO,YAC1BA,EAAOC,GACd,YAJkB,CAASN,GAM9BL,QAAMI,SAAS,oBAAqBK,GAAiB,GAGrD,IAAMG,EAAgB,CACpB9B,QAAS,CACP+B,aAAc,4BACdC,uBAAwB,CAAC,IAAK,KAC9BC,sBAAuB,mBACvBC,iBAAkB,6BAClBC,cAAe,6BACfC,WAAU,SAACxC,GAET,IAAMyC,EAAMC,SAASC,cAAc,OAEnC,GADAF,EAAIG,UAAY5C,EAAKK,MACjBL,EAAK6C,MAAO,CACd,IAAMC,EAAOJ,SAASC,cAAc,KACpCG,EAAKF,UAAY5C,EAAK6C,MACtBC,EAAKC,UAAY,QACjBN,EAAIO,YAAYF,GAElB,OAAOL,GAEHQ,OAAM,SAACC,EAAYC,GAAY,OAvFzC,EAuFyC,gGACsC,OAAnEC,EAAyBC,IAAgBC,2BAA0B,SAChDF,EAAuBjC,EAAgB+B,GAAW,OAArEK,EAAa,EAAH,KAChBJ,EAAWI,EAAYL,GAAY,0CA1FzC,0LA+FMM,EAAkBlE,IAAMmE,YAC5B,WAUEC,GACG,QATDrD,aAAK,IAAG,KAAE,EACVsD,EAAQ,EAARA,SACAC,EAAS,EAATA,UACAC,EAAM,EAANA,OACAC,EAAO,EAAPA,QACAC,EAAQ,EAARA,SACAC,EAAO,EAAPA,QAIKC,EAAqB,EAAhBC,cAAgB,GAApB,GAEFC,EAA+BC,aAAY,SAACC,GAAK,OAAKC,IAAUC,kBAAkBF,EAAOG,IAAaC,WAAWC,gCAEvHvD,EAAiB4C,EAmBjB,GADyB1D,GAASA,EAAMsE,MAAM,MAAM3D,OAAS,EACvC,CACpB,IAAM4D,EAAevE,EAAMsE,MAAM,MACjCtE,EAAQuE,EAAaC,KAAI,SAAC7E,GACxB,IAAM8E,EAAYpC,SAASC,cAAc,KAEzC,OADAmC,EAAUlC,UAAY5C,GAAQ,KACvB8E,EAAUC,aAChBC,KAAK,IAIV,OACE,yBAAKjC,UAAU,mBAAmBc,OAAQA,EAAQC,QAASA,EAASmB,QAxBtD,SAACC,GACfA,EAAEC,iBACFD,EAAEE,mBAsBoFC,SAnBvE,SAACH,GAChBA,EAAEC,iBACFD,EAAEE,oBAkBA,kBAAC,IAAU,CACTrC,UAAU,0CACVuC,MAAO,CAAEC,UAAW,WACpB7B,IAAK,SAAC8B,GAIJ,OAHIA,IACFA,EAAIC,YAAYC,KAAKC,UAAY,GAAH,OAAgB1B,EAAVD,EAAY,eAAoB,oBAE/DN,EAAI8B,IAEbI,QAAS7B,GAAYA,EAAS/C,OAAS,EAAIkB,EAAgB,GAC3D2D,MAAM,OACNxF,MAAOA,EACPyF,YAAW,UAAe7B,EAAVD,EAAY,eAAoB,kBAAiB,OACjEL,SAAUA,EACVC,UAAWA,EACXxC,QAASA,IAEV4C,IAAYG,GACX,kBAAC4B,EAAA,EAAM,CACLhD,UAAU,iBACViD,YAAaxB,IAAaC,WAAWC,4BACrCuB,IAAI,yBACJC,MAAK,UAAKjC,EAAE,cAAa,YAAIA,EAAE,+BAC/BgB,QApDc,WAAM,MAC6B,QAAvD,EAAAkB,cAAcC,cAAc,mCAA2B,OAAvD,EAAyDC,eA0D/D7C,EAAgB8C,YAAc,kBAEf9C,Q,kwECvKf,IAAM+C,EAAY,CAEhBlG,MAAOmG,IAAUC,OAEjBX,YAAaU,IAAUC,OAEvB9C,SAAU6C,IAAUE,KAAKC,WAEzB9C,OAAQ2C,IAAUE,KAElB5C,QAAS0C,IAAUE,KAEnBE,SAAUJ,IAAUE,MAGhBG,EAAevH,IAAMmE,YAAW,SAACqD,EAAOC,GAC5C,IAWC,IARG3C,aACF,SAACC,GAAK,MAAK,CACTC,IAAU0C,YAAY3C,GACtBC,IAAU2C,iCAAiC5C,GAC3CC,IAAU4C,sCAAsC7C,GAChDC,IAAU6C,iBAAiB9C,MAE7B+C,KACD,GAVCrD,EAAQ,KACRsD,EAAgB,KAWVC,EAAWC,qBAAWlI,KAAtBiI,OACFE,EAAcC,mBACdC,EAAgBD,mBAEtBE,2BAAgB,WAAM,QAGdC,EAAmC,QAAtB,EAAGJ,EAAYK,eAAO,OAAQ,QAAR,EAAnB,EAAqBpI,cAAM,WAAR,EAAnB,EAA6BqI,UAAUC,kBACvDC,GAAcJ,aAAa,EAAbA,EAAeK,0BAA2B,GAC1DP,EAAcG,SAAWH,EAAcG,UAAYG,EAAYE,QACjEZ,IAEFI,EAAcG,QAAUG,EAAYE,SAGnC,CAACpB,EAAMzG,MAAOiH,IAGjB,IA8CMa,EAAgB,OACjBrB,GAAK,IACRpD,IAAK,SAAC0E,GACJZ,EAAYK,QAAUO,EACtBrB,EAAaqB,IAEfzE,SAAU0E,KAvCS,SAACC,EAASC,EAAOtF,EAAQxD,GAI5C,GAFA6I,EAAUA,EAAQE,QAAQ,UAAW,KAEjChB,EAAYK,QAAS,CAIvB,IACIxH,EAAQ,GAaZ,GAdgBZ,GAAsC,KAA5BA,EAAOgJ,UAAUC,QAA6B,gBAAZJ,IAI1DjI,EAAQiI,EAAQK,OAASL,EAAQK,OAAOtI,MAAQiI,GAElDxB,EAAMnD,SAAStD,GAMcgD,IAAgBuF,wBAAwBL,EAAM3I,KAEjD,OAClBiJ,EAAgBxF,IAAgByF,2BAA2BP,EAAM3I,KACjEmJ,EAAc1F,IAAgB2F,0BAA0BH,GAExDI,EADkBxJ,EAAOgJ,UACSzH,OAAS+H,EAAYG,eAAelI,OACtEmI,EAAoC,QAAtB,EAAG3B,EAAYK,eAAO,aAAnB,EAAqBpI,OAC5C2J,YAAW,kBAAMD,aAAc,EAAdA,EAAgBE,aAAaJ,EAAiBA,KAAkB,OAWpD,KACjCrF,UArDoB,SAACsB,GAGrB,GAFiB,KACOA,EAAEoE,MACL,CACnB,IAAMC,EAAsBlC,EACtBmC,EAA2BtE,EAAEuE,SAAWvE,EAAEwE,SAE5CH,GAAuBC,IACzB1C,EAAMF,SAAS1B,KA8CnBnB,aAGF,OACE,oCACE,kBAAC,EAAoBoE,OAK3BtB,EAAaP,YAAc,eAC3BO,EAAaN,UAAYA,EAEVM,SCzHAA,Q,6FCCf,SAAS8C,EAAsBjK,EAAY2E,GAA8B,IAAvBuF,EAAoB,UAAH,6CAAG,EAO9DC,EAAkB,IAAIhI,OAAOC,KAAKgI,YAAYC,iBAEpDF,EAA2B,UAAInK,EAAe,GAC9CmK,EAAmB,EAAInK,EAAc,EACrCmK,EAAmB,EAAInK,EAAc,EACrCmK,EAA4B,WAAInK,EAAuB,WACvDmK,EAAyB,QAAI,cAC7BA,EAAwB,OAAIG,IAAKC,iBACjCJ,EAAuB,MAAIxF,EAC3BwF,EAA4B,WAAc,WAAVxF,GAAgC,aAAVA,EAAuB,SAAW,SACxFwF,EAAwB,QAAI,EAC5BA,EAAgBK,qBAEhB,IAAMC,EAAgBH,IAAKI,iBAAiBP,EAAwB,OAAGD,GACjES,EAAeC,IAAQrG,EAAE,gBAAD,OAAiBI,EAAMkG,gBAC/CC,EAAW,GAAH,OAAMH,EAAY,YAAIC,IAAQrG,EAAE,sBAAqB,YAAIkG,GAGvE,OAFAN,EAAgBY,YAAYD,GAErBX,I,k7CCrBT,IAAMa,EAAmB,SAACC,EAAQC,EAAM5E,GAAW,MAAM,CACvD4E,OACAC,MAAO,gBAAF,OAAkBF,EAAOJ,eAC9BrE,MAAO,gBAAF,OAAkByE,EAAOJ,eAC9BI,SACA3E,gBAGW8E,EAAuB,CAClCJ,EAAiB,WAAY,kCAAmC,iCAChEA,EAAiB,WAAY,kCAAmC,iCAChEA,EAAiB,YAAa,mCAAoC,kCAClEA,EAAiB,YAAa,mCAAoC,kCAClEA,EAAiB,OAAQ,8BAA+B,6BACxDA,EAAiB,SAAU,gCAAiC,+BAC5DA,EAAiB,WAAY,kCAAmC,kCAG5DK,EAAkB,SAACjE,GACvB,IACEkE,EAGElE,EAHFkE,OAAM,EAGJlE,EAFFmE,yBAAiB,IAAG,eAAQ,IAE1BnE,EADFoE,yBAAiB,IAAG,GAAK,EAGrBC,EAAWC,cAEXC,EAAiBH,EAAoB,GAAK,IAAH,OAAOF,GAC9CM,EAAiB,GAAH,OAAM9G,IAAa+G,mBAAiB,OAAGF,GACrDG,EAAgBpH,aAAY,SAACC,GAAK,OAAKC,IAAUmH,UAAUpH,EAAOiH,MAyBxE,OAnBA3D,2BAAgB,WACd,IAAM+D,EAAkB,CACtB1F,YAAasF,EACbvI,UAAW,kBACX4I,MAAOb,EAAqBjG,KAAI,SAAC7E,GAC/B,OAAO,EAAP,KACKA,GAAI,IACPiF,QAAS,kBAXI2G,EAWc5L,EAAK2K,YAVtCM,EAAkBW,GADA,IAACA,SAmBjBT,EAHGK,EAGMK,IAAQC,aAAaJ,EAAgB1F,YAAa0F,GAFlDG,IAAQE,UAAUL,MAI5B,CAACT,IAEG,MAGTF,EAAgBxE,UAAY,CAC1ByE,OAAQxE,IAAUC,OAClBwE,kBAAmBzE,IAAUE,KAC7BwE,kBAAmB1E,IAAUwF,MAGhBjB,QCpEAA,O,qBCFf,IAAIkB,EAAM,EAAQ,IACF3D,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQ4D,WAAa5D,EAAQ6D,QAAU7D,KAG/CA,EAAU,CAAC,CAAC8D,EAAOlL,EAAIoH,EAAS,MAG9C,IAAIrG,EAAU,CAEd,OAAiB,SAAUoK,GAgBX,IAAKxK,OAAOyK,8BAEV,YADA5J,SAAS6J,KAAKvJ,YAAYqJ,GAI5B,IAAIG,EAEJA,EAAgB9J,SAAS+J,qBAAqB,oBAEzCD,EAAcxL,SACjBwL,EAzBF,SAASE,EAAwBC,EAASjH,EAAOhD,UAC/C,MAAMkK,EAAW,GAYjB,OATAlH,EAAKmH,iBAAiBF,GAAS5M,QAAQqI,GAAMwE,EAASE,KAAK1E,IAG3D1C,EAAKmH,iBAAiB,KAAK9M,QAAQqI,IAC7BA,EAAG2E,YACLH,EAASE,QAAQJ,EAAwBC,EAASvE,EAAG2E,eAIlDH,EAYSF,CAAwB,qBAG1C,MAAMM,EAAkB,GACxB,IAAK,IAAI9L,EAAI,EAAGA,EAAIsL,EAAcxL,OAAQE,IAAK,CAC7C,MAAM+L,EAAeT,EAActL,GACnC,GAAU,IAANA,EACF+L,EAAaF,WAAW/J,YAAYqJ,GACpCA,EAASa,OAAS,WACZF,EAAgBhM,OAAS,GAC3BgM,EAAgBjN,QAASoN,IAEvBA,EAAUC,UAAYf,EAASe,iBAIhC,CACL,MAAMD,EAAYd,EAASgB,WAAU,GACrCJ,EAAaF,WAAW/J,YAAYmK,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEPlB,EAAI3D,EAASrG,GAI1BmK,EAAOkB,QAAUhF,EAAQiF,QAAU,I,sBClEzBnB,EAAOkB,QAAU,EAAQ,GAAR,EAA+D,IAKlFR,KAAK,CAACV,EAAOlL,EAAI,khrBAA2orB,M,qBCLpqrB,IAAI+K,EAAM,EAAQ,IACF3D,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQ4D,WAAa5D,EAAQ6D,QAAU7D,KAG/CA,EAAU,CAAC,CAAC8D,EAAOlL,EAAIoH,EAAS,MAG9C,IAAIrG,EAAU,CAEd,OAAiB,SAAUoK,GAgBX,IAAKxK,OAAOyK,8BAEV,YADA5J,SAAS6J,KAAKvJ,YAAYqJ,GAI5B,IAAIG,EAEJA,EAAgB9J,SAAS+J,qBAAqB,oBAEzCD,EAAcxL,SACjBwL,EAzBF,SAASE,EAAwBC,EAASjH,EAAOhD,UAC/C,MAAMkK,EAAW,GAYjB,OATAlH,EAAKmH,iBAAiBF,GAAS5M,QAAQqI,GAAMwE,EAASE,KAAK1E,IAG3D1C,EAAKmH,iBAAiB,KAAK9M,QAAQqI,IAC7BA,EAAG2E,YACLH,EAASE,QAAQJ,EAAwBC,EAASvE,EAAG2E,eAIlDH,EAYSF,CAAwB,qBAG1C,MAAMM,EAAkB,GACxB,IAAK,IAAI9L,EAAI,EAAGA,EAAIsL,EAAcxL,OAAQE,IAAK,CAC7C,MAAM+L,EAAeT,EAActL,GACnC,GAAU,IAANA,EACF+L,EAAaF,WAAW/J,YAAYqJ,GACpCA,EAASa,OAAS,WACZF,EAAgBhM,OAAS,GAC3BgM,EAAgBjN,QAASoN,IAEvBA,EAAUC,UAAYf,EAASe,iBAIhC,CACL,MAAMD,EAAYd,EAASgB,WAAU,GACrCJ,EAAaF,WAAW/J,YAAYmK,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEPlB,EAAI3D,EAASrG,GAI1BmK,EAAOkB,QAAUhF,EAAQiF,QAAU,I,sBClEnCD,EAAUlB,EAAOkB,QAAU,EAAQ,GAAR,EAAqE,IAKxFR,KAAK,CAACV,EAAOlL,EAAI,+oFAAgpF,KAGzqFoM,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAItB,EAAM,EAAQ,IACF3D,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQ4D,WAAa5D,EAAQ6D,QAAU7D,KAG/CA,EAAU,CAAC,CAAC8D,EAAOlL,EAAIoH,EAAS,MAG9C,IAAIrG,EAAU,CAEd,OAAiB,SAAUoK,GAgBX,IAAKxK,OAAOyK,8BAEV,YADA5J,SAAS6J,KAAKvJ,YAAYqJ,GAI5B,IAAIG,EAEJA,EAAgB9J,SAAS+J,qBAAqB,oBAEzCD,EAAcxL,SACjBwL,EAzBF,SAASE,EAAwBC,EAASjH,EAAOhD,UAC/C,MAAMkK,EAAW,GAYjB,OATAlH,EAAKmH,iBAAiBF,GAAS5M,QAAQqI,GAAMwE,EAASE,KAAK1E,IAG3D1C,EAAKmH,iBAAiB,KAAK9M,QAAQqI,IAC7BA,EAAG2E,YACLH,EAASE,QAAQJ,EAAwBC,EAASvE,EAAG2E,eAIlDH,EAYSF,CAAwB,qBAG1C,MAAMM,EAAkB,GACxB,IAAK,IAAI9L,EAAI,EAAGA,EAAIsL,EAAcxL,OAAQE,IAAK,CAC7C,MAAM+L,EAAeT,EAActL,GACnC,GAAU,IAANA,EACF+L,EAAaF,WAAW/J,YAAYqJ,GACpCA,EAASa,OAAS,WACZF,EAAgBhM,OAAS,GAC3BgM,EAAgBjN,QAASoN,IAEvBA,EAAUC,UAAYf,EAASe,iBAIhC,CACL,MAAMD,EAAYd,EAASgB,WAAU,GACrCJ,EAAaF,WAAW/J,YAAYmK,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEPlB,EAAI3D,EAASrG,GAI1BmK,EAAOkB,QAAUhF,EAAQiF,QAAU,I,sBClEnCD,EAAUlB,EAAOkB,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACV,EAAOlL,EAAI,kyGAAmyG,KAG5zGoM,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAItB,EAAM,EAAQ,IACF3D,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQ4D,WAAa5D,EAAQ6D,QAAU7D,KAG/CA,EAAU,CAAC,CAAC8D,EAAOlL,EAAIoH,EAAS,MAG9C,IAAIrG,EAAU,CAEd,OAAiB,SAAUoK,GAgBX,IAAKxK,OAAOyK,8BAEV,YADA5J,SAAS6J,KAAKvJ,YAAYqJ,GAI5B,IAAIG,EAEJA,EAAgB9J,SAAS+J,qBAAqB,oBAEzCD,EAAcxL,SACjBwL,EAzBF,SAASE,EAAwBC,EAASjH,EAAOhD,UAC/C,MAAMkK,EAAW,GAYjB,OATAlH,EAAKmH,iBAAiBF,GAAS5M,QAAQqI,GAAMwE,EAASE,KAAK1E,IAG3D1C,EAAKmH,iBAAiB,KAAK9M,QAAQqI,IAC7BA,EAAG2E,YACLH,EAASE,QAAQJ,EAAwBC,EAASvE,EAAG2E,eAIlDH,EAYSF,CAAwB,qBAG1C,MAAMM,EAAkB,GACxB,IAAK,IAAI9L,EAAI,EAAGA,EAAIsL,EAAcxL,OAAQE,IAAK,CAC7C,MAAM+L,EAAeT,EAActL,GACnC,GAAU,IAANA,EACF+L,EAAaF,WAAW/J,YAAYqJ,GACpCA,EAASa,OAAS,WACZF,EAAgBhM,OAAS,GAC3BgM,EAAgBjN,QAASoN,IAEvBA,EAAUC,UAAYf,EAASe,iBAIhC,CACL,MAAMD,EAAYd,EAASgB,WAAU,GACrCJ,EAAaF,WAAW/J,YAAYmK,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEPlB,EAAI3D,EAASrG,GAI1BmK,EAAOkB,QAAUhF,EAAQiF,QAAU,I,sBClEnCD,EAAUlB,EAAOkB,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACV,EAAOlL,EAAI,whJAAyhJ,KAGljJoM,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAItB,EAAM,EAAQ,IACF3D,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQ4D,WAAa5D,EAAQ6D,QAAU7D,KAG/CA,EAAU,CAAC,CAAC8D,EAAOlL,EAAIoH,EAAS,MAG9C,IAAIrG,EAAU,CAEd,OAAiB,SAAUoK,GAgBX,IAAKxK,OAAOyK,8BAEV,YADA5J,SAAS6J,KAAKvJ,YAAYqJ,GAI5B,IAAIG,EAEJA,EAAgB9J,SAAS+J,qBAAqB,oBAEzCD,EAAcxL,SACjBwL,EAzBF,SAASE,EAAwBC,EAASjH,EAAOhD,UAC/C,MAAMkK,EAAW,GAYjB,OATAlH,EAAKmH,iBAAiBF,GAAS5M,QAAQqI,GAAMwE,EAASE,KAAK1E,IAG3D1C,EAAKmH,iBAAiB,KAAK9M,QAAQqI,IAC7BA,EAAG2E,YACLH,EAASE,QAAQJ,EAAwBC,EAASvE,EAAG2E,eAIlDH,EAYSF,CAAwB,qBAG1C,MAAMM,EAAkB,GACxB,IAAK,IAAI9L,EAAI,EAAGA,EAAIsL,EAAcxL,OAAQE,IAAK,CAC7C,MAAM+L,EAAeT,EAActL,GACnC,GAAU,IAANA,EACF+L,EAAaF,WAAW/J,YAAYqJ,GACpCA,EAASa,OAAS,WACZF,EAAgBhM,OAAS,GAC3BgM,EAAgBjN,QAASoN,IAEvBA,EAAUC,UAAYf,EAASe,iBAIhC,CACL,MAAMD,EAAYd,EAASgB,WAAU,GACrCJ,EAAaF,WAAW/J,YAAYmK,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEPlB,EAAI3D,EAASrG,GAI1BmK,EAAOkB,QAAUhF,EAAQiF,QAAU,I,sBClEzBnB,EAAOkB,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACV,EAAOlL,EAAI,woDAAyoD,M,qBCLlqD,IAAI+K,EAAM,EAAQ,IACF3D,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQ4D,WAAa5D,EAAQ6D,QAAU7D,KAG/CA,EAAU,CAAC,CAAC8D,EAAOlL,EAAIoH,EAAS,MAG9C,IAAIrG,EAAU,CAEd,OAAiB,SAAUoK,GAgBX,IAAKxK,OAAOyK,8BAEV,YADA5J,SAAS6J,KAAKvJ,YAAYqJ,GAI5B,IAAIG,EAEJA,EAAgB9J,SAAS+J,qBAAqB,oBAEzCD,EAAcxL,SACjBwL,EAzBF,SAASE,EAAwBC,EAASjH,EAAOhD,UAC/C,MAAMkK,EAAW,GAYjB,OATAlH,EAAKmH,iBAAiBF,GAAS5M,QAAQqI,GAAMwE,EAASE,KAAK1E,IAG3D1C,EAAKmH,iBAAiB,KAAK9M,QAAQqI,IAC7BA,EAAG2E,YACLH,EAASE,QAAQJ,EAAwBC,EAASvE,EAAG2E,eAIlDH,EAYSF,CAAwB,qBAG1C,MAAMM,EAAkB,GACxB,IAAK,IAAI9L,EAAI,EAAGA,EAAIsL,EAAcxL,OAAQE,IAAK,CAC7C,MAAM+L,EAAeT,EAActL,GACnC,GAAU,IAANA,EACF+L,EAAaF,WAAW/J,YAAYqJ,GACpCA,EAASa,OAAS,WACZF,EAAgBhM,OAAS,GAC3BgM,EAAgBjN,QAASoN,IAEvBA,EAAUC,UAAYf,EAASe,iBAIhC,CACL,MAAMD,EAAYd,EAASgB,WAAU,GACrCJ,EAAaF,WAAW/J,YAAYmK,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEPlB,EAAI3D,EAASrG,GAI1BmK,EAAOkB,QAAUhF,EAAQiF,QAAU,I,sBClEnCD,EAAUlB,EAAOkB,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACV,EAAOlL,EAAI,g9KAAi9K,KAG1+KoM,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAItB,EAAM,EAAQ,IACF3D,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQ4D,WAAa5D,EAAQ6D,QAAU7D,KAG/CA,EAAU,CAAC,CAAC8D,EAAOlL,EAAIoH,EAAS,MAG9C,IAAIrG,EAAU,CAEd,OAAiB,SAAUoK,GAgBX,IAAKxK,OAAOyK,8BAEV,YADA5J,SAAS6J,KAAKvJ,YAAYqJ,GAI5B,IAAIG,EAEJA,EAAgB9J,SAAS+J,qBAAqB,oBAEzCD,EAAcxL,SACjBwL,EAzBF,SAASE,EAAwBC,EAASjH,EAAOhD,UAC/C,MAAMkK,EAAW,GAYjB,OATAlH,EAAKmH,iBAAiBF,GAAS5M,QAAQqI,GAAMwE,EAASE,KAAK1E,IAG3D1C,EAAKmH,iBAAiB,KAAK9M,QAAQqI,IAC7BA,EAAG2E,YACLH,EAASE,QAAQJ,EAAwBC,EAASvE,EAAG2E,eAIlDH,EAYSF,CAAwB,qBAG1C,MAAMM,EAAkB,GACxB,IAAK,IAAI9L,EAAI,EAAGA,EAAIsL,EAAcxL,OAAQE,IAAK,CAC7C,MAAM+L,EAAeT,EAActL,GACnC,GAAU,IAANA,EACF+L,EAAaF,WAAW/J,YAAYqJ,GACpCA,EAASa,OAAS,WACZF,EAAgBhM,OAAS,GAC3BgM,EAAgBjN,QAASoN,IAEvBA,EAAUC,UAAYf,EAASe,iBAIhC,CACL,MAAMD,EAAYd,EAASgB,WAAU,GACrCJ,EAAaF,WAAW/J,YAAYmK,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEPlB,EAAI3D,EAASrG,GAI1BmK,EAAOkB,QAAUhF,EAAQiF,QAAU,I,sBClEnCD,EAAUlB,EAAOkB,QAAU,EAAQ,GAAR,EAAqE,IAKxFR,KAAK,CAACV,EAAOlL,EAAI,khFAAmhF,KAG5iFoM,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAItB,EAAM,EAAQ,IACF3D,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQ4D,WAAa5D,EAAQ6D,QAAU7D,KAG/CA,EAAU,CAAC,CAAC8D,EAAOlL,EAAIoH,EAAS,MAG9C,IAAIrG,EAAU,CAEd,OAAiB,SAAUoK,GAgBX,IAAKxK,OAAOyK,8BAEV,YADA5J,SAAS6J,KAAKvJ,YAAYqJ,GAI5B,IAAIG,EAEJA,EAAgB9J,SAAS+J,qBAAqB,oBAEzCD,EAAcxL,SACjBwL,EAzBF,SAASE,EAAwBC,EAASjH,EAAOhD,UAC/C,MAAMkK,EAAW,GAYjB,OATAlH,EAAKmH,iBAAiBF,GAAS5M,QAAQqI,GAAMwE,EAASE,KAAK1E,IAG3D1C,EAAKmH,iBAAiB,KAAK9M,QAAQqI,IAC7BA,EAAG2E,YACLH,EAASE,QAAQJ,EAAwBC,EAASvE,EAAG2E,eAIlDH,EAYSF,CAAwB,qBAG1C,MAAMM,EAAkB,GACxB,IAAK,IAAI9L,EAAI,EAAGA,EAAIsL,EAAcxL,OAAQE,IAAK,CAC7C,MAAM+L,EAAeT,EAActL,GACnC,GAAU,IAANA,EACF+L,EAAaF,WAAW/J,YAAYqJ,GACpCA,EAASa,OAAS,WACZF,EAAgBhM,OAAS,GAC3BgM,EAAgBjN,QAASoN,IAEvBA,EAAUC,UAAYf,EAASe,iBAIhC,CACL,MAAMD,EAAYd,EAASgB,WAAU,GACrCJ,EAAaF,WAAW/J,YAAYmK,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEPlB,EAAI3D,EAASrG,GAI1BmK,EAAOkB,QAAUhF,EAAQiF,QAAU,I,sBClEnCD,EAAUlB,EAAOkB,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACV,EAAOlL,EAAI,4gDAA6gD,KAGtiDoM,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAItB,EAAM,EAAQ,IACF3D,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQ4D,WAAa5D,EAAQ6D,QAAU7D,KAG/CA,EAAU,CAAC,CAAC8D,EAAOlL,EAAIoH,EAAS,MAG9C,IAAIrG,EAAU,CAEd,OAAiB,SAAUoK,GAgBX,IAAKxK,OAAOyK,8BAEV,YADA5J,SAAS6J,KAAKvJ,YAAYqJ,GAI5B,IAAIG,EAEJA,EAAgB9J,SAAS+J,qBAAqB,oBAEzCD,EAAcxL,SACjBwL,EAzBF,SAASE,EAAwBC,EAASjH,EAAOhD,UAC/C,MAAMkK,EAAW,GAYjB,OATAlH,EAAKmH,iBAAiBF,GAAS5M,QAAQqI,GAAMwE,EAASE,KAAK1E,IAG3D1C,EAAKmH,iBAAiB,KAAK9M,QAAQqI,IAC7BA,EAAG2E,YACLH,EAASE,QAAQJ,EAAwBC,EAASvE,EAAG2E,eAIlDH,EAYSF,CAAwB,qBAG1C,MAAMM,EAAkB,GACxB,IAAK,IAAI9L,EAAI,EAAGA,EAAIsL,EAAcxL,OAAQE,IAAK,CAC7C,MAAM+L,EAAeT,EAActL,GACnC,GAAU,IAANA,EACF+L,EAAaF,WAAW/J,YAAYqJ,GACpCA,EAASa,OAAS,WACZF,EAAgBhM,OAAS,GAC3BgM,EAAgBjN,QAASoN,IAEvBA,EAAUC,UAAYf,EAASe,iBAIhC,CACL,MAAMD,EAAYd,EAASgB,WAAU,GACrCJ,EAAaF,WAAW/J,YAAYmK,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEPlB,EAAI3D,EAASrG,GAI1BmK,EAAOkB,QAAUhF,EAAQiF,QAAU,I,sBClEnCD,EAAUlB,EAAOkB,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACV,EAAOlL,EAAI,+5FAAg6F,KAGz7FoM,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,skBCTvB,8lGAAArM,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,+XAoCesM,IAjCe,SAAH,GAAyC,IAAnCC,EAAY,EAAZA,aAAcC,EAAc,EAAdA,eACvCC,EAAyBvJ,aAAY,SAACC,GAAK,OAAKC,IAAUsJ,0BAA0BvJ,MAEpFV,EAAQ,eANhB,EAMgB,GANhB,EAMgB,UAAG,WAAOuB,GAAC,2EACO,KAAxB2I,EAAO3I,EAAEyD,OAAOmF,MAAM,IAClB,CAAF,eACe,GAAjBC,EAAaF,GACbF,EAAwB,CAAF,+BACNA,EAAuBE,GAAK,OAAxCG,EAAM,EAAH,KACTD,EAAa,CACXC,MACAC,KAAMJ,EAAKI,KACXnN,KAAM+M,EAAK/M,KACXoN,KAAML,EAAKK,MACX,OAEJR,EAAeD,EAAc,CAACM,IAAa,0CAnBjD,+KAqBG,gBAfa,sCAiBd,OACE,2BACEI,GAAG,0BACHD,KAAK,OACL5I,MAAO,CAAE8I,QAAS,QAClBzK,SAAUA,EACVsB,QAAS,SAACC,GACRA,EAAEyD,OAAOtI,MAAQ,Q,iyFCpBzB,IAAMqK,EAAmB,SAACC,EAAQC,EAAM5E,GAAW,MAAM,CACvD4E,OACAC,MAAO,UAAF,OAAYF,EAAOJ,eACxBrE,MAAO,UAAF,OAAYyE,EAAOJ,eACxBI,SACA3E,gBAGWqI,EAAuB,CAClC3D,EAAiB,OAAQ,GAAI,iBAC7BA,EAAiB,SAAU,GAAI,oBAG3BnE,EAAY,CAChB+H,WAAY9H,IAAUE,KACtB6H,aAAc/H,IAAUE,KACxB8H,WAAYhI,IAAUwF,KACtByC,YAAajI,IAAUwF,KACvBhB,OAAQxE,IAAUC,QAGpB,SAASiI,KAET,SAASC,EAAU7H,GACjB,MAOIA,EANFwH,kBAAU,IAAG,EAAAI,EAAI,IAMf5H,EALFyH,oBAAY,IAAG,EAAAG,EAAI,EACnBF,EAIE1H,EAJF0H,WACAC,EAGE3H,EAHF2H,YACAzK,EAEE8C,EAFF9C,QACAgH,EACElE,EADFkE,OAGI4D,EAAiBxK,aAAY,SAACC,GAAK,aAAqC,QAArC,EAAKC,IAAUuK,gBAAgBxK,UAAM,aAAhC,EAAkCuK,kBAC1EtD,EAAiB,GAAH,OAAM9G,IAAasK,kBAAiB,YAAI9D,GACrD/G,EAAqB,EAAhBC,cAAgB,GAApB,GAUR,IAAKsK,IAAeC,EAClB,OAAO,KAGT,IAAMM,EAAuBC,IAAW,sCAClCC,EAAeD,IAAW,uCAAwC,CAAE,gBAAiBhL,EAAS,aAAc4K,IAClH,OACE,yBAAK7L,UAAWkM,GACd,kBAACC,EAAA,EAAmB,CAClBlJ,YAAW,oBAAegF,GAC1BjI,UAAWgM,EACX9I,IAAI,kBACJC,MAAOjC,EAAE,oCACTkL,cAAe7D,EACf8D,UAAU,IAEZ,kBAAC,EAAe,CACd9D,eAAgBA,EAChB+D,YA1Bc,SAACC,GACD,SAAdA,EACFhB,IACuB,WAAdgB,GACTf,KAuBEC,WAAYA,EACZC,YAAaA,KAOrB,IAAMc,EAAkB,SAAH,GAKf,IAJJjE,EAAc,EAAdA,eACA+D,EAAW,EAAXA,YACAb,EAAU,EAAVA,WACAC,EAAW,EAAXA,YAEMtD,EAAWC,cACXI,EAAgBpH,aAAY,SAACC,GAAK,OAAKC,IAAUmH,UAAUpH,EAAOiH,MACjErH,EAAqB,EAAhBC,cAAgB,GAApB,GA8BR,OA5BAyD,2BAAgB,WACd,IAAIgE,EAAQ0C,EACPG,EAEOC,IACV9C,EAAQA,EAAM6D,QAAO,SAACxP,GAAI,MAAqB,WAAhBA,EAAK2K,WAFpCgB,EAAQA,EAAM6D,QAAO,SAACxP,GAAI,MAAqB,SAAhBA,EAAK2K,UAKtC,IAAM8E,EAAkB,CACtBzJ,YAAasF,EACbvI,UAAW,kBACX4I,MAAOA,EAAM9G,KAAI,SAAC7E,GAChB,OAAO,EAAP,KACKA,GAAI,IACP6K,MAAO5G,EAAEjE,EAAK6K,OACd3E,MAAOjC,EAAEjE,EAAKkG,OACdjB,QAAS,kBAAMoK,EAAYrP,EAAK2K,eAQpCQ,EAHGK,EAGMK,IAAQC,aAAa2D,EAAgBzJ,YAAayJ,GAFlD5D,IAAQE,UAAU0D,MAI5B,CAACjB,EAAYC,IAET,MAGTc,EAAgBhJ,UAAY,CAC1B+E,eAAgB9E,IAAUC,OAC1B4I,YAAa7I,IAAUE,KACvB8H,WAAYhI,IAAUwF,KACtByC,YAAajI,IAAUwF,MAGzB2C,EAAUpI,UAAYA,EAEPoI,Q,mmDC3EAe,ICxDAA,EDIf,SAA4B5I,GAC1B,IACE6I,EAGA,EAFEvL,aAAY,SAACC,GAAK,MAAK,CACzBC,IAAUsL,2BAA2BvL,OACrC,GAHuB,GAIjB3E,EAAwCoH,EAAxCpH,WAAYmQ,EAA4B/I,EAA5B+I,aAAcC,EAAchJ,EAAdgJ,UAE0C,IAA1CxQ,IAAMyQ,SAAS/F,IAAKgG,UAAUtQ,IAAY,GAArEsQ,EAAS,KAAEC,EAAY,KACsE,IAAlD3Q,IAAMyQ,SAAS/F,IAAKkG,kBAAkBxQ,IAAY,GAA7FwQ,EAAiB,KAAEC,EAAoB,KAE9C7Q,IAAM8Q,WAAU,WACd,SAASC,IACPJ,EAAajG,IAAKgG,UAAUtQ,EAAYiQ,IACxCQ,EAAqBnG,IAAKkG,kBAAkBxQ,EAAYiQ,IAK1D,OAFAU,IACArG,IAAKsG,iBAAiB,6BAA8BD,OAA8B7P,EAAWmP,GACtF,kBAAM3F,IAAKuG,oBAAoB,6BAA8BF,EAA8BV,MACjG,CAACjQ,EAAYiQ,IAEhB,IAiBMa,EAAY,CAChBlC,WAlBiBhP,IAAMmR,aAAY,WAChB/Q,aAAsBmC,OAAOC,KAAKgI,YAAY4G,oBAC/C1G,IAAK2G,qBAAqBhB,GAAyBiB,2BACnE5G,IAAK2G,qBAAqBhB,GAAyBkB,QAAQ,0BAA2BnR,GAEtFmQ,GAAa,EAAMC,KAEpB,CAACpQ,EAAYmQ,EAAcC,IAY5BvB,aAVmBjP,IAAMmR,aAAY,WACrCzG,IAAK8G,kBAAkB,CAACpR,GAAU,SAAKA,EAAWqR,4BAAuBvQ,EAAWmP,KACnF,CAACjQ,IASF8O,WAPiB0B,EAQjBzB,YAPkBuB,KAActQ,WAAYsR,UAQ5ChG,OAPctL,EAAcA,EAAWuR,GAAK,IAU9C,OACE,kBAAC,EAAS,KAAKnK,EAAW0J,K,yiCE/C9B,IAAMjK,EAAY,CAChB7G,WAAY8G,IAAU0K,OAAOvK,WAC7BsE,kBAAmBzE,IAAUE,MAG/B,SAASyK,EAAUrK,GACjB,IACEpH,EAEEoH,EAFFpH,WAAU,EAERoH,EADFmE,yBAAiB,IAAG,eAAS,EAGxBhH,EAAqB,EAAhBC,cAAgB,GAApB,GAEFkN,EAAkB1R,EAAW2R,YAC7BzG,EAAO,0BAAH,OAAiD,KAApBwG,EAAyB,OAASA,EAAgB7G,eACnF4D,EAAKzO,EAAWuR,GAEtB,OACE,oCACE,kBAAC/B,EAAA,EAAmB,CAClBlJ,YAAW,oBAAemI,GAC1BjI,MAAOjC,EAAE,4BACTgC,IAAK2E,EACLuE,cAAa,UAAK3K,IAAa+G,kBAAiB,YAAI4C,KAEtD,kBAACpD,EAAA,EAAe,CACdC,OAAQmD,EACRlD,kBAAmBA,KAM3BkG,EAAU5K,UAAYA,EAEP4K,Q,sPChCf,IAAM5K,EAAY,CAChB7G,WAAY8G,IAAU0K,QAGxB,SAASI,GAAmBxK,GAC1B,IAAM6I,EAA0BvL,aAAY,SAACC,GAAK,OAAKC,IAAUsL,2BAA2BvL,MACtFkN,EAAsBnN,aAAY,SAACC,GAAK,OAAKC,IAAUC,kBAAkBF,EAAO,gBAE9E3E,EAAeoH,EAAfpH,WAEFuL,EAAoBuG,YAAgBf,uBAAY,SAAmCgB,GACvF,IAAM5H,EAAkBF,YAAsBjK,EAAY+R,EAAU9B,GACpEjQ,EAAWgS,SAAS7H,GACpB,IAAM8H,EAAoB3H,IAAK2G,qBAAqBhB,GACpDgC,EAAkBC,cAAc/H,GAChC8H,EAAkBd,QAAQ,WAAY,CAAChH,EAAiBnK,EAAYiS,EAAkBE,kBAAkBnS,OACvG,CAACA,EAAYiQ,KAEhB,OAAS4B,GACP,6BACE,kBAAC,EAAS,GAACtG,kBAAmBA,GAAuBnE,KAK3DwK,GAAmB/K,UAAYA,EAChB+K,IClCAA,GDkCAA,G,sjCE9Bf,IAAM/K,GAAY,CAChBkH,aAAcjH,IAAUC,OACxBd,UAAWa,IAAUC,OACrBqL,mBAAoBtL,IAAU0K,OAC9Ba,gBAAiBvL,IAAU0K,OAC3Bc,qBAAsBxL,IAAU0K,QAG5Be,GAA+B,SAAH,GAA+F,IAAzFxE,EAAY,EAAZA,aAAc9H,EAAS,EAATA,UAAWmM,EAAkB,EAAlBA,mBAAoBC,EAAe,EAAfA,gBAAiBC,EAAoB,EAApBA,qBAC5F/N,EAAMC,cAAND,EAC2D,KAAf8L,oBAAS,GAAM,GAA5DmC,EAAkB,KAAEC,EAAqB,KACe,KAAfpC,oBAAS,GAAM,GAAxDqC,EAAgB,KAAEC,EAAmB,KAC6B,KAAftC,oBAAS,GAAM,GAAlEuC,EAAqB,KAAEC,EAAwB,KAQtD,OANAnC,qBAAU,WAAM,UACd+B,GAAsD,QAAhC,EAAAL,EAAmBrE,UAAa,aAAhC,EAAkCzM,QAAS,GACjEqR,GAAiD,QAA7B,EAAAN,EAAgBtE,UAAa,aAA7B,EAA+BzM,QAAS,GAC5DuR,GAA2D,QAAlC,EAAAP,EAAqBvE,UAAa,aAAlC,EAAoCzM,QAAS,KACrE,CAAC8Q,EAAoBC,EAAiBC,IAGtCE,GAAsBE,GAAoBE,EACzC,yBAAKE,eAAa,4BAChB,kBAACC,GAAA,EAAO,CAACnK,QAASrE,EAAE,4BAClB,6BACE,kBAACyO,GAAA,EAAI,CAAC3P,UAAU,YAAY4P,MAAO,wBAAyBhN,UAAWA,OAI7E,MAINsM,GAA6B1L,UAAYA,GAE1B0L,UClCT1L,GAAY,CAChBkH,aAAcjH,IAAUC,OACxBd,UAAWa,IAAUC,QAGjBmM,GAAwC,SAAH,GAAoC,IAA9BnF,EAAY,EAAZA,aAAc9H,EAAS,EAATA,UACvDkN,EAAazO,aAAY,SAACC,GAAK,OAAKC,IAAUC,kBAAkBF,EAAO,+BAC7E,EAAsE/E,IAAMiI,WAAWlI,KAA/EyS,EAAkB,EAAlBA,mBAAoBC,EAAe,EAAfA,gBAAiBC,EAAoB,EAApBA,qBAE7C,OAAIa,EACK,KAGP,kBAAC,GAA4B,CAC3BpF,aAAcA,EACd9H,UAAWA,EACXmM,mBAAoBA,EACpBC,gBAAiBA,EACjBC,qBAAsBA,KAI5BY,GAAsCrM,UAAYA,GAEnCqM,IC7BAA,GD6BAA,G,moCEPf,IAAMrM,GAAY,CAChBqE,KAAMpE,IAAUC,OAChBqM,UAAWtM,IAAUC,OACrB9F,MAAO6F,IAAUC,OACjBsM,UAAWvM,IAAUC,OACrB/G,WAAY8G,IAAU0K,OACtB8B,SAAUxM,IAAUC,OACpBwM,eAAgBzM,IAAUC,OAC1ByM,WAAY1M,IAAUwF,KACtB6D,aAAcrJ,IAAUE,KACxByM,yBAA0B3M,IAAUwF,KACpCoH,SAAU5M,IAAUwF,KACpBqH,iBAAkB7M,IAAUE,KAC5B6K,oBAAqB/K,IAAUwF,KAC/BsH,UAAW9M,IAAUwF,KACrB8D,UAAWtJ,IAAU+M,OACrBC,aAAchN,IAAUC,OACxBgN,YAAajN,IAAUC,OACvBiN,gBAAiBlN,IAAUwF,KAC3Bd,kBAAmB1E,IAAUwF,KAC7B2H,kBAAmBnN,IAAUE,KAC7BkN,cAAepN,IAAUwF,KACzB6H,wBAAyBrN,IAAUwF,KACnC8H,gBAAiBtN,IAAUwF,MAG7B,SAAS+H,GAAWjN,GAAO,QA6BrBkN,EA3BFpJ,EAuBE9D,EAvBF8D,KACAkI,EAsBEhM,EAtBFgM,UACApT,EAqBEoH,EArBFpH,WACAsT,EAoBElM,EApBFkM,SACAC,EAmBEnM,EAnBFmM,eACAC,EAkBEpM,EAlBFoM,WACArD,EAiBE/I,EAjBF+I,aACAsD,EAgBErM,EAhBFqM,yBACAnP,EAeE8C,EAfF9C,QACAoP,EAcEtM,EAdFsM,SACAC,EAaEvM,EAbFuM,iBACA9B,EAYEzK,EAZFyK,oBACA+B,EAWExM,EAXFwM,UACAxD,EAUEhJ,EAVFgJ,UACA0D,EASE1M,EATF0M,aACAC,EAQE3M,EARF2M,YACAC,EAOE5M,EAPF4M,gBACAxI,EAMEpE,EANFoE,kBACAyI,EAKE7M,EALF6M,kBACAC,EAIE9M,EAJF8M,cACAC,EAGE/M,EAHF+M,wBACAI,EAEEnN,EAFFmN,SACAH,EACEhN,EADFgN,gBAGK7P,EAAqB,GAAhBC,cAAgB,GAApB,GAGFgQ,EAAeV,IAAiBW,KAAuBC,eAAkBjB,GAA4BK,IAAiBW,KAAuBE,aAAiBC,aAAsB5U,GAAcA,EAAW6U,YACnN,GAAIN,GAAYC,EAAa,CAC3B,IAAMM,EAAcN,EAAYO,eAAe,QAAS,CAAEC,SAAUT,IACpED,EAAO,IAAIW,KAAKH,QAEhBR,EAAOE,EAGT,IAAMU,EAAkBZ,EAAOa,IAAMb,GAAMc,OAAO9B,GAAU+B,OAAO9B,GAAkBhP,EAAE,wCAEjF+Q,EAAkBtV,EAAWuV,aAAajU,OAC5CL,EAA6B,QAAxB,EAAGjB,EAAWoT,UAAU,OAAa,QAAb,EAArB,EAAuBoC,mBAAW,WAAb,EAArB,UAERzB,IAAgB0B,KAAMC,MAAQzU,GAAS0U,aAAe1U,GACxDA,EAAQ2U,KAAqB,MACpB7B,IAAgB0B,KAAMI,OAAS5U,GAAS6U,aAAgB7U,KACjEA,EAAQ2U,KAAqB,OAG/B,IAAMvC,EAAY0C,aAAS/V,EAAWgW,WAChCC,EAA6BjW,EAAWkW,sBACxCC,EAAsC,IAAH,OAAOF,EAA0B,OAEpEG,EAAqB9G,IAAW,kBAAmB,CAAEhL,YACrD+R,EAAkB/G,IAAW,aAAc,CAAEgH,QAAShS,IAAY4P,IAWxE,OACE,yBAAK7Q,UAAWgT,IACZ/R,GACA,yBAAKjB,UAAU,uBACZqQ,GACC,yBAAKrQ,UAAU,wBAEjB,kBAAC2P,GAAA,EAAI,CAAC3P,UAAU,YAAY4P,MAAO/H,EAAMjK,MAAOA,EAAOoS,UAAWA,KAGtE,yBAAKhQ,UAAW+S,GACd,yBAAK/S,UAAU,uBACb,yBAAKA,UAAU,mBACb,yBAAKA,UAAU,UACZ8Q,QAA0DrT,IAA/BmV,GAC1B,0BAAM5S,UAAU,qBAAqB8S,GAEtCxC,EAAiB3T,IAEpB,yBAAKqD,UAAU,wBACb,yBAAKA,UAAU,iBACZ6R,EACAhB,GAAiB,UAAJ,OAAclU,EAAWuW,WAAU,MAElDjB,EAAkB,IAAM9B,GACvB,yBAAKnQ,UAAU,yBACb,kBAAC2P,GAAA,EAAI,CAAC3P,UAAU,iBAAiB4P,MAAM,qBACvC,yBAAK5P,UAAU,eAAeiS,MAItC,yBAAKjS,UAAU,sBACZmI,IAAsB0I,IAAkB5P,GACvC,kBAACkS,GAAA,EAAM,CACL/H,GAAE,mCAA8BzO,EAAWuR,IAC3CkF,aAAA,UAAe9C,EAAiB3T,GAAW,YAAIuE,EAAE,wCACjDmS,QAAS1C,EACTzO,QAAS,SAACC,GACRA,EAAEC,iBACFD,EAAEE,kBACFuO,GAAmBD,MAIzB,kBAAC,GAA4B,CAC3BjG,aAAc/N,EAAWuR,GACzBtL,UAAS,4BAAuB0N,EAAiB3T,GAAW,aAAKkV,MAEjErD,IAAwBvN,IAAYkH,IAAsB0I,IAAkBE,GAC5E,kBAAC,GAAS,CACRpU,WAAYA,EACZwT,WAAYA,KAGdI,GAAaJ,IAAehI,IAAsB0I,IAAkBE,GACpE,kBAAC,EAAS,CACRhE,UAAWA,EACXpQ,WAAYA,EACZmQ,aAAcA,EACd7L,QAASA,IAGZkP,GAAcY,IAAoB5I,GACjC,oCACE,kBAACnF,GAAA,EAAM,CACLG,MAAOjC,EAAE,uBACTgC,IAAK,sBACLlD,UAAU,qCACVkC,QAAS,kBA5EjBoR,EA4E2C3W,EA5EN4W,cAAcC,WACzDvM,IAAKwM,kBAAkBC,oBAAoBJ,GAFjB,IACpBA,GA6EQK,cAAc,wBAEhB,kBAAC3Q,GAAA,EAAM,CACLG,MAAOjC,EAAE,uBACTgC,IAAK,aACLlD,UAAU,qCACVkC,QAAS,kBA/EjBoR,EA+E2C3W,EA/EN4W,cAAcC,WACzDvM,IAAKwM,kBAAkBG,oBAAoBN,GAFjB,IACpBA,GAgFQK,cAAc,6BAWhC3C,GAAWxN,UAAYA,GAERwN,IC1MAA,GD0MAA,G,mxCE1LA6C,IChBAA,GDGf,SAAkC9P,GAChC,IAAO+P,EAKN,GALwBzS,aACvB,SAACC,GAAK,MAAK,CACTC,IAAUwS,mBAAmBzS,MAE/B+C,KACD,GALoB,GAOrB,OACE,kBAAC2P,GAAA,EAAe,MAAKjQ,EAAK,CAAEkQ,WAAYH,M,0aEb5C,wuNADA,IAAMI,GACC,cADDA,GAEG,cAFHA,GAGC,cAHDA,GAIC,cAJDA,GAKC,cALDA,GAMC,cANDA,GAOK,cAGLC,GAAsBrV,OAAOC,KAAKgI,YAAYoN,oBAE7C,SAAeC,GAAsB,GAAD,gCAE1C,sCAFM,WAAqCtJ,GAAI,2FACvCqJ,GAAoBE,0BAA0BvJ,EAAKvF,QAASuF,EAAKK,OAAK,4CAC9E,sBAEM,SAAemJ,GAAyB,GAAD,gCAE7C,sCAFM,WAAwC3X,GAAU,oFAAY,OAAVoO,EAAQ,EAAH,6BAAG,GAAE,SAC7DpO,EAAW4X,eAAexJ,GAAM,4CACvC,sBAEM,SAASyJ,GAAQ1J,GACtB,SAAIA,EAAKK,OAAQL,EAAKK,KAAKsJ,WAAW,WAMjC,SAASC,GAAkB5J,GAAM,MACtC,GAAI0J,GAAQ1J,GACV,OAAOoJ,GAGT,OAD2B,QAAZ,EAAGpJ,EAAKI,YAAI,aAAT,EAAWtJ,MAAM,KAAK+S,MAAMnN,eAE5C,IAAK,MACH,OAAO0M,GACT,IAAK,MACH,OAAOA,GACT,IAAK,MACL,IAAK,OACH,OAAOA,GACT,IAAK,MACL,IAAK,OACH,OAAOA,GACT,IAAK,MACL,IAAK,OACH,OAAOA,GACT,QACE,OAAOA,I,maC9Cb,kiNACA,IAOMU,GAAa,SAACC,GAClB,OAAO,IAAIC,SAAQ,SAACC,GAClB,QANmBtX,IAMFoX,EANP9W,KAORgX,EAAQF,EAAIG,SAAS,cAChB,CACL,IAAMC,EAAa,IAAIC,WACvBD,EAAW9K,OAAS,kBAAM4K,EAAQE,EAAWE,SAC7CF,EAAWL,WAAWC,QAKfO,GAAQ,SAACtK,GACpB,MApBoB,kBAoBbA,EAAKK,MAGDkK,GAAW,eAxBxB,EAwBwB,GAxBxB,EAwBwB,WAAG,WAAOvK,GAAI,8FACd8J,GAAW9J,GAAK,OAAzB,GAAPwK,EAAU,EAAH,KACC,CAAF,wCACH,CAAET,IAAK/J,IAAM,OAYgC,OAThDyK,EAAgB,GACtBC,KAAUC,QAAQ,uBAAuB,SAACC,EAAGC,GAC3C,IAAQ/L,EAAyB+L,EAAzB/L,QAAyB+L,EAAhBC,YACAhM,IACf2L,EAAcxL,KAAKH,MAIjBiM,EAAQL,KAAUM,SAASR,GAC3BT,EAAM,IAAIkB,KAAK,CAACF,GAAQ,CAAE1K,KAtCZ,kBAsCkC,kBAC/C,CAAE0J,MAAKmB,QAAST,EAActX,OAAS,IAAG,2CAxCnD,iLAyCC,gBAjBuB,sC,iZCxBxB,gmGAAAE,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,olBAAAA,GAAA,gEAAAA,GAAA,0JAAAA,EAAA,6FAAAA,GAAA,mIAAAA,IAAA,8SAAAA,IAAA,4OAAAA,EAAA,iBAAAA,EAAA,EAAAA,IAAA,EAAAA,GAAA,EAAAA,GAAA,SAaA,IAAM8X,GAAe,SAAH,GAAiB,IAAXnL,EAAI,EAAJA,KACf5J,EAAqB,GAAhBC,cAAgB,GAApB,GAEwB,KAAV6L,qBAAU,GAAzBkJ,EAAG,KAAEC,EAAM,KACiC,KAAfnJ,oBAAS,GAAM,GAA5CoJ,EAAU,KAAEC,EAAa,KAyBhC,OAvBAhJ,qBAAU,YACiB,+BAAG,6FAIiC,GAH3DgJ,GAAc,GACVC,EAAiBxL,IAEfyL,IAAmBzL,aAAgB0L,MAAU1L,EAAKG,MACpC,CAAF,+BACOmJ,GAAsBtJ,GAAK,OAAlDwL,EAAiB,EAAH,iBAGZxL,aAAgB0L,MAAQD,GAAc,qBACpCnB,GAAMtK,GAAO,CAAF,iCACkBuK,GAAYiB,GAAe,iBAAlDzB,EAAG,EAAHA,IAAKmB,EAAO,EAAPA,QACbG,EAAOM,IAAIC,gBAAgB7B,IAC3BwB,EAAcL,GAAS,wBAEvBG,EAAOM,IAAIC,gBAAgBJ,IAAiB,4CAGjD,kBAlBwB,mCAmBzBK,KACC,CAAC7L,IAGF,yBAAK9K,UAAWiM,IAAW,CACzB,4BAA4B,EAC5B,MAASmK,KAET,yBAAKF,IAAKA,IACTE,GAAc,0BAAMpW,UAAU,oCAAoCkB,EAAE,2BC/C5D0V,GDoDa,SAAH,GAA0C,IAApC7L,EAAK,EAALA,MAAOwF,EAAS,EAATA,UAAWsG,EAAW,EAAXA,YAI7C,KAHmCxV,aAAY,SAACC,GAAK,MAAK,CAC1DC,IAAUuV,cAAcxV,GACxBC,IAAUwV,gCAAgCzV,OAC1C,GAHK0V,EAAU,KAAEC,EAAc,KAI1B/V,EAAqB,GAAhBC,cAAgB,GAApB,GAEFe,EAAO,+BAAG,WAAOC,EAAG2I,GAAI,wEAER,GADpB3I,EAAEC,iBACFD,EAAEE,kBAEG2U,EAAY,CAAF,wCACNE,QAAQC,KAAK,gDAA+C,YAIjErM,aAAgB0L,MAAI,gBACtBY,EAAWtM,EAAK,2BACPA,EAAKG,IAAK,CAAF,gBACjBmM,EAAWtM,EAAKG,IAAI,yCAEHmJ,GAAsBtJ,GAAK,QAA5CsM,EAAW,EAAH,aAGVA,GAAYJ,EAAWK,OAAOD,EAAU,CACtCE,SAAUxM,EAAKI,KACfqM,WAAW,EACXC,2BAA2B,IAC1B,4CACJ,gBAtBY,wCA+BPC,EAAU,+BAAG,WAAOtV,EAAG2I,GAAI,wEAEX,GADpB3I,EAAEC,iBACFD,EAAEE,mBAEeyI,EAAKG,IAAG,qBAAGH,EAAKG,IAAG,sCAASmJ,GAAsBtJ,GAAK,0BAAlEsM,EAAW,EAAH,GACdM,kBAAON,EAAUtM,EAAKI,MAAM,4CAC7B,gBANe,wCAQhB,OACE,yBAAKlL,UAAU,yBACZ+K,EAAMjJ,KAAI,SAACgJ,EAAM3M,GAAC,OACjB,yBACE6B,UAAU,mBACV2X,IAAKxZ,EACL+D,QAAS,SAACC,GAAC,OAAKD,EAAQC,EAAG2I,KAE1BmM,GAAkBzC,GAAQ1J,IACzB,kBAAC,GAAY,CAACA,KAAMA,IAEtB,yBAAK9K,UAAU,yBACb,kBAAC2P,GAAA,EAAI,CACH3P,UAAU,wBACV4P,MAAO8E,GAAkB5J,KAE3B,kBAAC4E,GAAA,EAAO,CAACnK,QAASuF,EAAKI,MACrB,yBAAKlL,UAAU,mBAAmB8K,EAAKI,OAExCqF,EACC,kBAACvN,GAAA,EAAM,CACLhD,UAAU,oBACVmD,MAAK,UAAKjC,EAAE,iBAAgB,YAAIA,EAAE,+BAClCgC,IAAI,aACJhB,QAAS,SAACC,GAAC,OAvCR,SAACA,EAAG2I,GACnB3I,EAAEC,iBACFD,EAAEE,kBAEFwU,EAAY/L,GAmCgB8M,CAASzV,EAAG2I,MAG9B,kBAAC9H,GAAA,EAAM,CACLhD,UAAU,oBACVmD,MAAK,UAAKjC,EAAE,mBAAkB,YAAIA,EAAE,+BACpCgC,IAAI,gBACJhB,QAAS,SAACC,GAAC,OAAKsV,EAAWtV,EAAG2I,a,qBErGxC+M,GAAiB,SAACC,GACtB,IAAMC,EAAO,GAUb,GATID,EAAQ,gBAA6C,WAA3BA,EAAQ,iBACpCC,EAAW,MAAI,GAEbD,EAAQ,eAA2C,WAA1BA,EAAQ,gBACnCC,EAAa,QAAI,GAEfD,EAAe,QACjBC,EAAY,MAAID,EAAe,OAE7BA,EAAQ,mBAAoB,CAC9B,IAAME,EAAaF,EAAQ,mBAAmBlW,MAAM,KAEhDoW,EAAWC,SAAS,kBACtBF,EAAa,QAAI,GAEfC,EAAWC,SAAS,UACtBF,EAAgB,WAAI,GAIxB,OAAOA,GAGMG,GAlDc,SAACvb,EAAYD,GAOxC,IANA,IAAME,EAAgBD,EAAWwb,mBAC3BC,EAAUC,OAAOC,KAAK1b,GACtBkJ,EAAgBxF,KAAgByF,2BAA2BrJ,EAAOI,eAClEyb,EAAOjY,KAAgB2F,0BAA0BH,GAAeK,eAChEtJ,EAAM,GAEHsB,EAAI,EAAGA,EAAIia,EAAQna,OAAQE,IAAK,CACvC,IAAM2Z,EAAUlb,EAAcwb,EAAQja,IAChC4Z,EAAOF,GAAeC,GAE5B,IAAIU,MAAMJ,EAAQja,IAAlB,CAIA,IAAMsa,EAAYD,MAAMJ,EAAQja,EAAI,IAAMoa,EAAKta,OAASma,EAAQja,EAAI,GAC9Dua,EAAYH,EAAKI,MAAMP,EAAQja,GAAIsa,GAEzC5b,EAAIkN,KAAK,CAAE3M,OAAQsb,EAAWxb,WAAY6a,KAG5Crb,EAAOgL,YAAY7K,GACnBH,EAAO4J,aAAaiS,EAAKta,OAAQ,I,sbC1BnC,gmGAAAE,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,8YAAAA,GAAA,gEAAAA,GAAA,0JAAAA,EAAA,6FAAAA,GAAA,mIAAAA,IAAA,8SAAAA,IAAA,4OAAAA,EAAA,iBAAAA,EAAA,EAAAA,IAAA,EAAAA,GAAA,EAAAA,GAAA,SAoCA2T,IAAM8G,OAAOC,KAEb,IAAMrV,GAAY,CAChB7G,WAAY8G,IAAU0K,OAAOvK,WAC7B2M,UAAW9M,IAAUwF,KACrB6D,aAAcrJ,IAAUE,KACxBoJ,UAAWtJ,IAAU+M,OACrBH,SAAU5M,IAAUwF,KACpB6P,mBAAoBrV,IAAUwF,KAC9B8P,eAAgBtV,IAAUE,KAC1BgN,gBAAiBlN,IAAUwF,KAC3Bd,kBAAmB1E,IAAUwF,KAC7B2H,kBAAmBnN,IAAUE,KAC7BkN,cAAepN,IAAUwF,MAGrB+P,GAAc,SAAH,GAYX,MAXJrc,EAAU,EAAVA,WACA4T,EAAS,EAATA,UACAzD,EAAY,EAAZA,aACAC,EAAS,EAATA,UACAsD,EAAQ,EAARA,SACAyI,EAAkB,EAAlBA,mBACAC,EAAc,EAAdA,eACApI,EAAe,EAAfA,gBACAxI,EAAiB,EAAjBA,kBACAyI,EAAiB,EAAjBA,kBACAC,EAAa,EAAbA,cAGMX,EAAiB7O,aAAY,SAACC,GAAK,OAAKC,IAAU0X,kBAAkB3X,MACpEyO,EAAY1O,aAAY,SAACC,GAAK,OAAKC,IAAU2X,aAAa5X,EAAO6X,aAAmBxc,GAAa0H,QACjGmK,EAAsBnN,aAAY,SAACC,GAAK,OAAKC,IAAUC,kBAAkBF,EAAO,sBAChF2O,EAAW5O,aAAY,SAACC,GAAK,OAAKC,IAAU6X,mBAAmB9X,MAC/D8O,EAA2B/O,aAAY,SAACC,GAAK,OAAKC,IAAU6O,yBAAyB9O,MACrF+X,EAAyBhY,aAAY,SAACC,GAAK,OAAKC,IAAU+X,kCAAkChY,MAC5FiY,EAA0BlY,aAAY,SAACC,GAAK,OAAKC,IAAUiY,qCAAqClY,MAChGoP,EAAcrP,aAAY,SAACC,GAAK,OAAKC,IAAUkY,eAAenY,MAC9D4P,EAAW7P,aAAY,SAACC,GAAK,OAAKC,IAAUmY,YAAYpY,MACxDuK,EAAiBxK,aAAY,SAACC,GAAK,aAAqC,QAArC,EAAKC,IAAUuK,gBAAgBxK,UAAM,aAAhC,EAAkCuK,kBAEhF,EASIrH,qBAAWlI,KARb6T,EAAU,EAAVA,WACAwJ,EAAW,EAAXA,YACApV,EAAM,EAANA,OACAwK,EAAkB,EAAlBA,mBACA6K,EAAuB,EAAvBA,wBACAnJ,EAAY,EAAZA,aACAK,EAAuB,EAAvBA,wBACA+I,EAAkB,EAAlBA,mBAGIzR,EAAWC,cACVnH,EAAqB,GAAhBC,cAAgB,GAApB,GAEFF,EAAUtE,EAAWsE,UACrB8P,EAAkBoI,aAAmBxc,KAAgBmd,KAAkBC,eAE3B,KAAZ/M,mBAAS,IAAG,GAA3CgN,EAAW,KAAEzF,EAAc,KAElClH,qBAAU,WACRkH,EAAe5X,EAAWsd,oBACzB,CAACtd,IAEJ0Q,qBAAU,WACR,IAAM6M,EAA4B,SAACC,EAAaC,GAC/B,WAAXA,GACFD,EAAYnd,SAAQ,SAACqd,GACfA,EAAMnM,KAAOvR,EAAWuR,IAC1BqG,EAAe8F,EAAMJ,sBAO7B,OAFAhT,IAAKsG,iBAAiB,oBAAqB2M,GAEpC,WACLjT,IAAKuG,oBAAoB,oBAAqB0M,MAE/C,CAACvd,IAEJ2d,cAAa,WACN/J,GACHnI,EAASU,IAAQyR,qBAGnBhW,MACC,CAACgM,IAEJ,IAuIIiK,EAvIElK,EAAmB5C,uBACvB,SAAC/Q,GACC,IAAMuO,EAAOjE,IAAKI,iBAAiB1K,EAAmB,QAEtD,OAAOuO,EACLuP,GAAqBvP,EAAMyO,GAE3BzY,EAAE,0CAGN,CAACyY,IAGGe,EAAe/d,EAAWge,iBAAmBhe,EAAWge,kBAExDC,EAAiBlN,uBACrB,SAACjG,EAAU7K,EAAeie,EAAWH,GACnC,IAAMI,EAAoB,GAyB1B,GAxBKJ,GACHK,IAAWC,KAAKvT,EAAU,CACxBwT,aAAa,EACbC,oBAAoB,EACpBC,UAAS,SAACC,GACR,IAAMC,EAAOD,EAAME,gBACbC,EAAaH,EAAMI,gBACnBC,EAASL,EAAMM,YAErB,OAAQN,EAAMO,WACZ,IAAK,MACL,IAAK,QACL,IAAK,QACHb,EAAkB/Q,KAAK,CACrBsR,OACA9C,KAAMgD,EACNK,MAAOH,EACPI,IAAKJ,EAASL,EAAMU,iBAAiB7d,cAO5C6c,EAAkB7c,OAAQ,CAC7B,IAAM8d,EAAkBtB,GAAqBhT,EAAUkS,EAAa/c,GAQpE,IAAK+c,KAPiC1Y,GAAWoY,GACrBpY,GAAWsY,GAMoC,CAWzE,OACE,kBAAC,GAAe,CACdyC,aAAc,EACdC,SAAO,EACPC,eAAgBA,GAChBtf,cAAeA,EACf2H,OAAQA,EACRhC,MAAOsY,EACPsB,cAlBkB,WACpB,IAAKpL,EACH,OAAO,KAET,IAAMwH,EAA2C,IAApC5b,EAA8B,kBAAUuE,EAAE,sBAAwBA,EAAE,wBACjF,OACE,0BAAMqB,MAAO,CAAE3E,MAAOjB,EAAWgW,UAAUqC,WAAYoH,WAAY,MAAQ7D,KAc1E9Q,GAIP,OAAOsU,EAET,IAAMM,EAAkB,GACpBC,EAAS,EAkDb,OA9CAxB,EAAkB9d,SAAQ,SAACuf,EAAYC,GACrC,IAAQZ,EAAqBW,EAArBX,MAAOC,EAAcU,EAAdV,IAAKR,EAASkB,EAATlB,KAChBiB,EAASV,GACXS,EAAgBtS,KACd,0BAAM4N,IAAG,eAAU6E,IAEf/B,GACEhT,EACAkS,EACA/c,EACA0f,EACAV,KAMVS,EAAgBtS,KACd,uBACEsR,KAAMA,EACNzV,OAAO,SACP6W,IAAI,sBACJ9E,IAAG,YAAO6E,IAGR/B,GACEhT,EACAkS,EACA/c,EACAgf,EACAC,KAKRS,EAAST,KAGPS,EAAS7U,EAASxJ,OAAS,GAC7Boe,EAAgBtS,KAAK0Q,GACnBhT,EACAkS,EACA/c,EACA0f,IAGGD,IAET,CAAC1C,IAGG9R,GAAO6U,aAAevD,aAAmBxc,IAAakL,KAE5D,IACE2S,EAAamC,KAAKC,MAAMjgB,EAAW4W,cAAc,gBACjD,MAAOpR,GACPqY,EAAa7d,EAAW4W,cAAc,eAExC,IAAM9L,IAAqB,QAAV,EAAA+S,SAAU,aAAV,EAAY/S,WAAY9K,EAAWG,cAC9C+f,GAAmBlgB,EAAWG,cAC9BF,GAAgBD,EAAWwb,mBAC7B2E,GAAYngB,EAAsB,UAEtC,GAAI+T,IAAgB0B,KAAMC,KACpByK,IAAaxK,aAAewK,GAAU3K,iBACxC2K,GAAY,IAAIhe,OAAOC,KAAKgI,YAAYgW,MAAM,IAAK,IAAK,IAAK,IAG3DngB,IACeyb,OAAOC,KAAK1b,IACpBI,SAAQ,SAACggB,GACZpgB,GAAcogB,GAAU,OAAK1K,aAAe1V,GAAcogB,GAAU,SACtEpgB,GAAcogB,GAAU,MAAIzK,KAAqB,eAIlD,GAAI7B,IAAgB0B,KAAMI,MAAO,CAKtC,GAJIsK,IAAarK,aAAgBqK,GAAU3K,iBACzC2K,GAAY,IAAIhe,OAAOC,KAAKgI,YAAYgW,MAAM,EAAG,EAAG,EAAG,IAGrDngB,GACeyb,OAAOC,KAAK1b,IACpBI,SAAQ,SAACggB,GACZpgB,GAAcogB,GAAU,OAAKvK,aAAgB7V,GAAcogB,GAAU,SACvEpgB,GAAcogB,GAAU,MAAIzK,KAAqB,UAQzD,IACI0K,GADEC,QAAqE,IAAtCnO,EAAmBpS,EAAWuR,IAGjE+O,GADExV,IAAYyV,GACEzV,GAEAsH,EAAmBpS,EAAWuR,IAGhD,IAWMiP,GAAwB,SAAChb,GAAM,MACV,QAAzB,EAAIrD,OAAOse,sBAAc,OAArB,EAAuBpI,aACzB7S,WAAGE,oBAIDgb,GAAmBpR,IAAW,CAClC+M,aAAa,EACb/X,UACAqc,OAAQjN,EACRkN,QAASzE,EACT,aAAcjN,IAGVtG,GAAUiY,mBACd,WACE,IAAMC,EAAe,GAKrB,OAJIX,KACFW,EAAa7f,MAAQkf,GAAU3K,eAI/B,oCACI5B,GAAaJ,EACb,kBAAC,GAAW,CACVxT,WAAYA,EACZoQ,UAAWA,EACXD,aAAcA,EACdmQ,cAAeA,GACfS,sBAAuB7D,EACvB8D,YAAa5O,EAAmBpS,EAAWuR,MAG7C2O,IACE,yBAAK7c,UAAWiM,IAAW,YAAa,CAAE,gBAAiBhL,IAAYiB,QAASib,IAC7Elc,GAAY+Y,EAAY/b,OAAS,GAChC,kBAAC,GAAmB,CAClB8M,MAAOiP,EACPzJ,WAAW,IAGdqK,EAAeiC,GAAkBjgB,GAAe6gB,EAAc/C,OAO3E,CAAC/d,EAAYwT,EAAYI,EAAWzD,EAAcrF,GAAUmT,EAAgBqC,GAAepD,EAAoBG,IAG3GzB,GAAO5b,EAAW4W,cAAc,qBAChCqK,GAAcJ,mBAClB,WACE,GAAa,KAATjF,GACF,OAAO,KAGT,IAAMsF,EAAwBpD,GAAqBlC,GAAMoB,GACnDmE,GAAgC7c,GAAWoY,EAGjD,OAAI0E,IAASF,IAA0BC,EAEnC,kBAACE,GAAA,EAAkB,CACjBhe,UAAU,wBACViD,YAAY,4BACZ,kBAAC,GAAe,CAAC+Y,aAAc,GAAE,WAC1B6B,EAAqB,OAMhC,yBAAK7d,UAAU,wBAAwBuC,MAAO,CAAE0b,aAAc,SAC3DJ,KAGJ,CAACtF,GAAMoB,IAENuE,GAASV,mBACb,WACE,OACE,kBAAC,GAAU,CACT3V,KAAMA,GACNkI,UAAWA,EACXpT,WAAYA,EACZsT,SAAUA,EACVC,eAAgBA,EAChBC,WAAYA,EACZrD,aAAcA,EACdsD,yBAA0BA,EAC1BnP,QAASA,EACToP,SAAUA,EACVC,iBAAkBA,EAClB9B,oBAAqBA,EACrB+B,UAAWA,EACXxD,UAAWA,EACX0D,aAAcA,EACdC,YAAaA,EACbE,kBAAmBA,EACnBD,gBAAiBA,EACjBxI,kBAAmBA,EACnB0I,cAAeA,EACfC,wBAAyBA,EACzBI,SAAUA,EACVH,gBAAiBA,MAGpB,CAAClJ,GAAMkI,EAAWpT,EAAYsT,EAAUC,EAAgBC,EAAYrD,EAAcsD,EAA0BnP,EAASoP,EAAUC,EAAkBrJ,IAAKI,iBAAiB1K,EAAmB,QAAI6R,EAAqB+B,EAAWxD,EAAWwE,aAAsB5U,GAAa8T,EAAcG,EAAmBD,EAAiBxI,EAAmB0I,EAAeK,EAAUH,IAG/W,OACE,yBAAK/Q,UAAWqd,GAAkBnb,QA5HH,WAC1B2O,IACC5P,EACF8X,EAAepc,GACL4T,GAEVqJ,OAuHDsE,GACAN,GACArY,KAKPyT,GAAYxV,UAAYA,GAETwV,UAGTmF,GAAc,SAAH,GAOX,IANJxhB,EAAU,EAAVA,WACAoQ,EAAS,EAATA,UACAD,EAAY,EAAZA,aACAmQ,EAAa,EAAbA,cACAS,EAAqB,EAArBA,sBACAC,EAAW,EAAXA,YAkBE,KAREtc,aAAY,SAACC,GAAK,MAAK,CACzBC,IAAU4C,sCAAsC7C,GAChDC,IAAU6c,oBAAoB9c,GAC9BC,IAAUC,kBAAkBF,EAAOG,IAAa4c,sBAChD9c,IAAU+c,cAAchd,EAAOG,IAAa4c,sBAC5C9c,IAAU+c,cAAchd,EAAOG,IAAa8c,aAC5Chd,IAAUsL,2BAA2BvL,GACrCC,IAAUid,qBAAqBld,OAC/B,GAfAmd,EAAkC,KAClCC,EAAgB,KAChBC,EAAuB,KACvBC,EAAmB,KACnBC,EAAgB,KAChBjS,EAAuB,KACvB4R,EAAoB,KAUftd,EAAqB,GAAhBC,cAAgB,GAApB,GACFsD,EAAcC,mBACdzD,EAAUtE,EAAWsE,UAC3B,EAMIuD,qBAAWlI,KALbwiB,EAAa,EAAbA,cACA7P,EAAoB,EAApBA,qBACA8P,EAAgB,EAAhBA,iBACAC,EAAgB,EAAhBA,iBACArU,EAAc,EAAdA,eAGIsU,GAAyBN,GAA2BC,GAAuBM,eAEjF7R,qBAAU,WAER,GAAImR,IAAyBK,GAAoBD,IAAwBna,EAAYK,QAAS,CAC5F,IAAMpI,EAAS+H,EAAYK,QAAQpC,YACL/F,GAAcA,aAAsBmC,OAAOC,KAAKgI,YAAY4G,oBACjEjR,EAAOyiB,QAAQ,IAMpCxB,EACFlhB,aAA2BC,EAAQC,GAC1BD,EAAOI,eAChBuJ,YAAW,WAET,GAAIqY,EAAkB,CACpBzB,EAAgB3c,KAAgByF,2BAA2BrJ,EAAOI,eAClE,MAAgCwD,KAAgB2F,0BAA0BgX,GAAlE9W,EAAc,EAAdA,eAAmB,EAAHiZ,IAEhBnhB,QACNvB,EAAOyiB,QAAQhZ,GAQqB,MAJpC8Y,GAIAR,IACiB,QAAnB,EAAAha,EAAYK,eAAO,OAAnB,EAAqBua,QACM1iB,EAAWwb,oBAEpCD,GAAqBvb,EAAYD,MAGpC,KAGL,IACM4iB,EAAa5iB,EAAO6iB,YADS,EAGnC,GAAIN,EACF,OAGF5Y,YAAW,WACLiZ,GACF5iB,EAAO4J,aAAagZ,EAAYA,KAEjC,QAEJ,CAACT,EAAkBD,EAAqBK,IAE3C5R,qBAAU,WACR,GAAIpM,GAAyC,IAA9Bue,EAAmBvhB,OAAc,CAE9C,IAAM+b,EAAcrd,EAAWsd,iBAC/BtP,EAAehO,EAAWuR,GAAI8L,MAE/B,IAEH,IAAMtS,EAAW,eAliBnB,EAkiBmB,GAliBnB,EAkiBmB,WAAG,WAAOvF,GAAC,gFAoCzB,OAlCDA,EAAEC,iBAEI1F,EAAS+H,EAAYK,QAAQpC,YACnCua,EAAgB3c,KAAgByF,2BAA2BrJ,EAAOI,eAClEL,aAA2BC,EAAQC,GAEAsgB,EAAchf,OAAS,GAAiD,OAA5Cgf,EAAcA,EAAchf,OAAS,KAElGgf,EAAgBA,EAActE,MAAM,EAAGsE,EAAchf,OAAS,IAG3CtB,EAAWge,iBAAmBhe,EAAWge,mBAE5Dhe,EAAW8iB,sBAGTf,GAAkB,EACYpe,KAAgB2F,0BAA0BgX,GAAlE9W,EAAc,EAAdA,eAAgBiZ,EAAG,EAAHA,IAGC9e,KAAgBof,4BAA4B/iB,GACpDgjB,SAAS3iB,SAAQ,SAACK,GAC7B8I,EAAe8R,SAAS5a,EAAQC,QAClC8hB,EAAIrV,KAAK1M,EAAQ+N,OAIrBzO,EAAWijB,cAAc,cAAejD,KAAKkD,UAAU,CACrDpY,SAAUwV,EACVmC,SAEFziB,EAAW+K,YAAYvB,IAEvBxJ,EAAW+K,YAAYuV,GACxB,UAEK3I,GAAyB3X,EAAYsS,EAAqBtS,EAAWuR,KAAI,QAEzEhO,EAAUvD,aAAsBmC,OAAOC,KAAKgI,YAAY4G,mBAC1D,cAAgB,cACpB1G,IAAK2G,qBAAqBhB,GAAyBkB,QAAQ,oBAAqB,CAAC,CAACnR,GAAa,SAAU,CAAE,OAAUuD,KAEjHvD,aAAsBmC,OAAOC,KAAKgI,YAAY4G,oBAChD1G,IAAK6Y,wBAAwB,CAACnjB,IAGhCmQ,GAAa,EAAOC,GAEE,KAAlBkQ,GACFS,OAAsBjgB,EAAWd,EAAWuR,IAE9C8Q,EAAiBriB,EAAWuR,IAAI,2CAvlBpC,iLAwlBG,gBAtDgB,sCAoEX6R,EAAmB9T,IAAW,eAAgB,CAAE,gBAAiBhL,IACjEue,EAAqBvQ,EAAqBtS,EAAWuR,KAAO,GAElE,OACE,yBAAKlO,UAAW+f,GACb9e,GAAWue,EAAmBvhB,OAAS,GACtC,kBAAC,GAAmB,CAClB8M,MAAOyU,EACPjP,WAAW,EACXsG,YAAa,SAAC/L,GAAI,OAAKiU,EAAiBpiB,EAAWuR,GAAIpD,MAG3D,kBAAChH,EAAA,EAAY,CACXnD,IAAK,SAAC0E,GACJZ,EAAYK,QAAUO,GAExB/H,MAAO2f,EACPrc,SAAU,SAACtD,GAAK,OAAKogB,EAAsBpgB,EAAOX,EAAWuR,KAC7DrK,SAAU6D,EACVzG,QAASA,EACTH,OAhCS,SAACqB,GAAM,QACD,QAAnB,EAAIA,EAAE6d,qBAAa,OAA8B,QAA9B,EAAf,EAAiBC,aAAa,uBAAe,OAA7C,EAA+ChI,SAAS,2BAC1D9V,EAAEyD,OAAOyZ,QAGXP,OAAcrhB,IA4BVsD,QAzBU,WACd+d,EAAcniB,EAAWuR,OA0BvB,yBAAKlO,UAAU,gBACb,kBAACgD,GAAA,EAAM,CACLhD,UAAU,gBACV8H,MAAO5G,EAAE,iBACTgB,QAAS,SAACC,GACRA,EAAEE,kBACFyK,GAAa,EAAOC,GAEpB2Q,OAAsBjgB,EAAWd,EAAWuR,IAC5C8Q,EAAiBriB,EAAWuR,OAGhC,kBAAClL,GAAA,EAAM,CACLhD,UAAS,qBAAiBid,EAA8B,GAAd,aAC1C5Q,UAAW4Q,EACXnV,MAAO5G,EAAE,eACTgB,QAAS,SAACC,GACRA,EAAEE,kBACFqF,EAAYvF,SAQxBgc,GAAY3a,UAAY,CACtBuJ,UAAWtJ,IAAU+M,OAAO5M,WAC5BjH,WAAY8G,IAAU0K,OAAOvK,WAC7BkJ,aAAcrJ,IAAUE,KAAKC,WAC7BqZ,cAAexZ,IAAUC,OACzBga,sBAAuBja,IAAUE,KAAKC,WACtC+Z,YAAala,IAAUC,QAGzB,IAAMwc,GAAkB,SAAC3H,EAAM3b,EAAe+a,GAC5C,IAAMpV,EAAQ,CACZ6Z,WAAYxf,EAAc,eAC1BujB,UAAWvjB,EAAc,cACzBwjB,eAAgBxjB,EAAc,mBAC9BgB,MAAOhB,EAAqB,OAK9B,OAHI2F,EAAM6d,iBACR7d,EAAM6d,eAAiB7d,EAAM6d,eAAe3a,QAAQ,OAAQ,cAG5D,0BAAMlD,MAAOA,EAAOoV,IAAKA,GAAMY,IAI7B2D,GAAiB,SAAC3D,EAAM3b,EAAegf,GAC3C,IAAKhf,IAAkB2b,EACrB,OAAOA,EAKT,IAFA,IAAM8H,EAAS,GACTC,EAAUjI,OAAOC,KAAK1b,GAAekF,IAAIye,QAAQC,MAAK,SAACxD,EAAGyD,GAAC,OAAKzD,EAAIyD,KACjEtiB,EAAI,EAAGA,EAAImiB,EAAQriB,OAAQE,IAAK,CACvC,IAAIuiB,EAAQJ,EAAQniB,GAAKyd,EAGzB,GADAyE,EADAK,EAAQC,KAAKC,IAAID,KAAKE,IAAIH,EAAO,GAAInI,EAAKta,SAC1BrB,EAAc0jB,EAAQniB,IAClCuiB,IAAUnI,EAAKta,OACjB,MAMJ,IAFA,IAAMoe,EAAkB,GAClByE,EAAezI,OAAOC,KAAK+H,GAAQve,IAAIye,QAAQC,MAAK,SAACxD,EAAGyD,GAAC,OAAKzD,EAAIyD,KAC/DtiB,EAAI,EAAGA,EAAI2iB,EAAa7iB,OAAQE,IACvCke,EAAgBtS,KAAKmW,GACnB3H,EAAKI,MAAMmI,EAAa3iB,EAAI,GAAI2iB,EAAa3iB,IAC7CkiB,EAAOS,EAAa3iB,EAAI,IAAG,wBACVA,KAIrB,OAAOke,GAGH5B,GAAuB,SAACsG,EAAUpH,EAAa/c,GAAoD,IAArCgf,EAAQ,UAAH,6CAAG,EAAGC,EAAM,UAAH,6CAAGkF,EAAS9iB,OACtFsa,EAAOwI,EAASpI,MAAMiD,EAAOC,GAC7BmF,EAAczI,EAAK/Q,cACnByZ,EAAqBtH,EAAYnS,cACnC5K,IACFA,EAAc,GAAOA,EAAc,IAAQ,GAC3CA,EAAcmkB,EAAS9iB,QAAUrB,EAAcmkB,EAAS9iB,SAAW,IAErE,IAAIijB,EAAoBF,EAAYG,QAAQF,GAC5C,IAAKA,EAAmBtb,SAAiC,IAAvBub,EAChC,OAAOhF,GAAe3D,EAAM3b,EAAegf,GAE7C,IAAMS,EAAkB,GAClB+E,EAAoB,CAACF,GAErBG,EAAuBJ,EAAmBxb,QAAQ,sBAAuB,QAC/E,GAAI,IAAI6b,OAAO,IAAD,OAAKD,EAAoB,KAAK,MAAME,KAAKP,GACrD,MAA8B,IAAvBE,IAEsB,KAD3BA,EAAoBF,EAAYG,QAAQF,EAAoBC,EAAoBD,EAAmBhjB,UAEjGmjB,EAAkBrX,KAAKmX,GAiC7B,OA7BAE,EAAkBpkB,SAAQ,SAACwkB,EAAUC,GAGvB,IAARA,GAA0B,IAAbD,GACfnF,EAAgBtS,KAAKmS,GAAe3D,EAAKmJ,UAAU,EAAGF,GAAW5kB,EAAegf,IAElFS,EAAgBtS,KACd,0BAAM/J,UAAU,YAAY2X,IAAG,yBAAoB8J,IAE/CvF,GACE3D,EAAKmJ,UAAUF,EAAUA,EAAWP,EAAmBhjB,QACvDrB,EACAgf,EAAQ4F,KAMdA,EAAWP,EAAmBhjB,OAAS+iB,EAAY/iB,QAEhDujB,EAAWP,EAAmBhjB,SAAWmjB,EAAkBK,EAAM,IAEpEpF,EAAgBtS,KAAKmS,GACnB3D,EAAKmJ,UAAUF,EAAWP,EAAmBhjB,OAAQmjB,EAAkBK,EAAM,IAC7E7kB,EACAgf,EAAQ4F,EAAWP,EAAmBhjB,YAIrCoe,GChwBMrD,M,iZCDf,gmGAAA7a,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,8YAAAA,GAAA,gEAAAA,GAAA,0JAAAA,EAAA,6FAAAA,GAAA,mIAAAA,IAAA,8SAAAA,IAAA,4OAAAA,EAAA,iBAAAA,EAAA,EAAAA,IAAA,EAAAA,GAAA,EAAAA,GAAA,SAmBA,IAAMqF,GAAY,CAChB7G,WAAY8G,IAAU0K,OAAOvK,YAIzB+d,GAAY,SAAH,GAAuD,IAAjDhlB,EAAU,EAAVA,WAAY0T,EAAQ,EAARA,SAAUuR,EAAoB,EAApBA,qBAwBxC,KAbGvgB,aACF,SAACC,GAAK,YAAK,CACTC,IAAU4C,sCAAsC7C,GAChDC,IAAUsgB,mBAAmBvgB,GAC7BC,IAAUC,kBAAkBF,EAAO,aACA,QADY,EAC/CC,IAAUugB,mBAAmBxgB,UAAM,aAAnC,EAAsC3E,GACtC4E,IAAU6c,oBAAoB9c,GAC9BC,IAAU6C,iBAAiB9C,GAC3BC,IAAUC,kBAAkBF,EAAOG,IAAa4c,sBAChD9c,IAAU+c,cAAchd,EAAOG,IAAa4c,sBAC5C9c,IAAUsL,2BAA2BvL,MAEvC+C,KACD,GAtBCoa,EAAkC,KAClCsD,EAAU,KACVC,EAAe,KACfC,EAA4B,KAC5BvD,EAAgB,KAChBwD,EAAuC,KACvCvD,EAAuB,KACvBC,EAAmB,KACnBhS,EAAuB,KAezB,EAWIpI,qBAAWlI,KAVb6lB,EAAiB,EAAjBA,kBACAhS,EAAU,EAAVA,WACAnB,EAAe,EAAfA,gBACAoT,EAAe,EAAfA,gBACAC,EAAoB,EAApBA,qBACAC,EAAqB,EAArBA,sBACAxD,EAAa,EAAbA,cACA7P,EAAoB,EAApBA,qBACA+P,EAAgB,EAAhBA,iBACAD,EAAgB,EAAhBA,iBAE+C,KAAf/R,oBAAS,GAAM,GAA1CuV,EAAS,KAAEC,EAAY,KACxBpa,EAAWC,cACX5D,EAAcC,mBAEdua,GAAyBN,GAA2BC,GAAuBM,eAEjF5E,cAAa,WACNiI,GACHna,EAASU,IAAQyR,uBAElB,CAACgI,IAEJlV,qBAAU,WACJ4R,GAKFiD,GACA/R,IACCgS,GACD1D,GACAha,GACAA,EAAYK,SAEZL,EAAYK,QAAQua,UAErB,CAAC8C,EAAmBD,EAAyC/R,EAAY8O,IAE5E5R,qBAAU,WAaR,IAVKgV,GAAwBC,GAE3Bjc,YAAW,WAGL5B,GAAeA,EAAYK,SAAW2Z,GACxCha,EAAYK,QAAQua,UAErB,KAED5a,GAAeA,EAAYK,QAAS,CACtC,GAAIma,EACF,OAGF,IAEMK,EAFS7a,EAAYK,QAAQpC,YAET6c,YADS,EAEnClZ,YAAW,WACL5B,EAAYK,SACdL,EAAYK,QAAQpI,OAAO4J,aAAagZ,EAAYA,KAErD,QAEJ,IAEH,IAAMmD,EAAS,eAvHjB,EAuHiB,GAvHjB,EAuHiB,WAAG,WAAOtgB,GAAC,8EAM0D,GAJlFA,EAAEC,iBACFD,EAAEE,kBAEI3F,EAAS+H,EAAYK,QAAQpC,aAC7BggB,EAAYpiB,KAAgByF,2BAA2BrJ,EAAOI,gBAErD6I,OAAQ,CAAF,oDAIjB+Y,EAAkB,CAAF,gBAEkC,OAD9CiE,EAAkBriB,KAAgBsiB,mBAAmBjmB,EAAY+lB,GACvEjmB,aAA2BC,EAAQimB,GAAiB,UAC9CrO,GAAyBqO,EAAiB1T,EAAqBtS,EAAWuR,KAAI,QACpFjH,IAAK4b,eAAe,CAACF,GAAkB/V,GAAyB,wBAGZ,OAD9C+V,EAAkB1b,IAAK6b,sBAAsBnmB,EAAY+lB,GAC/DjmB,aAA2BC,EAAQimB,GAAiB,UAC9CrO,GAAyBqO,EAAiB1T,EAAqBtS,EAAWuR,KAAI,QACpFjH,IAAK2G,qBAAqBhB,GAAyBkB,QAAQ,oBAAqB,CAAC,CAAC6U,GAAkB,SAAU,KAAK,QAGrHP,EAAgB,GAAIzlB,EAAWuR,IAC/B8Q,EAAiBriB,EAAWuR,IAAI,2CAhJpC,iLAiJG,gBA1Bc,sCA4BT6U,EACJhB,GACAC,GACAC,EAEIe,EAAiB/W,IAAW,CAChC,cAAc,EACdqR,OAAQjN,IAkBJmP,EAAqBvQ,EAAqBtS,EAAWuR,KAAO,GAElE,OAAQ6U,IAAsB5S,EAAc,KAC1C,0BAAMtM,SAAU4e,EAAWziB,UAAU,wBAClCwf,EAAmBvhB,OAAS,GAC3B,kBAAC,GAAmB,CAClB8M,MAAOyU,EACPjP,WAAW,EACXsG,YAAa,SAAC/L,GAAI,OAAKiU,EAAiBpiB,EAAWuR,GAAIpD,MAG3D,yBAAK9K,UAAU,0BACb,yBACEA,UAAWgjB,EAGXC,YAAa,SAAC9gB,GAAC,OAAKA,EAAEE,oBAEtB,kBAACyB,EAAA,EAAY,CACXnD,IAAK,SAAC0E,GACJZ,EAAYK,QAAUO,GAExB/H,MAAO0R,EAAgBrS,EAAWuR,IAClCtN,SAAU,SAACtD,GAAK,OAtCO,SAACA,GAChC8kB,EAAgB9kB,EAAOX,EAAWuR,IAClC0T,GAAwBA,IAoCKsB,CAAyB5lB,IAC9CuG,SAAU4e,EACV3hB,OAnCK,WACb0hB,GAAa,GACb1D,OAAcrhB,IAkCNsD,QA/BM,WACdyhB,GAAa,GACb1D,EAAcniB,EAAWuR,KA8BjBjN,SAAO,KAGX,yBAAKjB,UAAU,0BACb,kBAACgD,GAAA,EAAM,CACLE,IAAI,kBACJlD,UAAU,eACVmD,MAAO,gBACPkJ,UAAW2C,EAAgBrS,EAAWuR,IACtChM,QAASugB,EACTU,cAAY,QAQxBxB,GAAUne,UAAYA,GAEPme,IC1NAA,GD0NAA,G,miCEhNf,IAAMne,GAAY,CAChB4f,iBAAkB3f,IAAU4f,MAAMzf,WAClCuE,kBAAmB1E,IAAUwF,KAAKrF,YAG9B0f,GAAmB,SAAH,GAGhB,IAFJF,EAAgB,EAAhBA,iBACAjb,EAAiB,EAAjBA,kBAEOjH,EAAqB,GAAhBC,cAAgB,GAApB,GACFiH,EAAWC,cACsD,KAAf2E,oBAAS,GAAM,GAAhEuW,EAAoB,KAAEC,EAAuB,KAC9C3X,EAAiBxK,aAAY,SAACC,GAAK,aAAqC,QAArC,EAAKC,IAAUuK,gBAAgBxK,UAAM,aAAhC,EAAkCuK,kBAK1E4X,EACJ,kBAACzgB,GAAA,EAAM,CACLd,QAAS,SAACC,GACRA,EAAEC,iBACFD,EAAEE,kBACFmhB,GAAwB,IAE1BxjB,UAAU,cACV4C,UAAW1B,EAAE,mCACb4G,MAAO5G,EAAE,mCACTgC,IAZc,+BAeZwgB,EACJ,kBAAC1gB,GAAA,EAAM,CACLd,QAAS,SAACC,GACRA,EAAEC,iBACFD,EAAEE,kBACFmhB,GAAwB,IAE1BxjB,UAAU,cACV4C,UAAW1B,EAAE,oCACb4G,MAAO5G,EAAE,oCACTgC,IA1BY,6BA8BhB,OACE,yBACElD,UAAWiM,IAAW,CACpB,iBAAiB,EACjB,aAAcJ,KAGf0X,EAAuBG,EAAuBD,EAC9CF,GACCH,EAAiBthB,KAAI,SAAC6hB,EAAiBxlB,GAErC,OAAU,IAANA,EACK,KAGP,kBAAC6E,GAAA,EAAM,CACL2U,IAAKgM,EAAgBzV,GACrBlO,UAAU,cACVkC,QAAS,SAACC,GACRA,EAAEC,iBACFD,EAAEE,kBACF4E,IAAK2c,iBAAiBD,GACtB1c,IAAK4c,iBAAiBF,GACtBvb,EAASU,IAAQgb,YAAY,sBAG/B,kBAAC,GAAW,CACVnM,IAAKgM,EAAgBzV,GACrBvR,WAAYgnB,EACZtT,UAAU,EACVQ,eAAe,EACf1I,kBAAmBA,UAUnCmb,GAAiB9f,UAAYA,GAEd8f,U,wkCCvFf,IAAMS,GAAsB,SAAH,GAAqB,IAAfC,EAAQ,EAARA,SACvBC,EAAQ7gB,eAAcC,cAAc,wBACpCgC,EAAK1F,SAASC,cAAc,OAQlC,OAPAyF,EAAG6e,aAAa,eAAgBziB,IAAa0iB,gCAE7C9W,qBAAU,WAER,OADA4W,EAAMhkB,YAAYoF,GACX,kBAAM4e,EAAMG,YAAY/e,MAC9B,CAACA,EAAI4e,IAEDI,wBAAaL,EAAU3e,ICpBjBif,GDuBqB,SAAH,GAA4D,IAAtD3nB,EAAU,EAAVA,WAAY4nB,EAAgB,EAAhBA,iBAAkBC,EAAiB,EAAjBA,kBAoBlE,KAXGnjB,aACF,SAACC,GAAK,MAAK,CACTC,IAAUwS,mBAAmBzS,GAC7BC,IAAU+c,cAAchd,EAAOG,IAAa0iB,gCAC5C5iB,IAAU+c,cAAchd,EAAOG,IAAa8c,aAC5Chd,IAAUC,kBAAkBF,EAAOG,IAAa0iB,gCAChD5iB,IAAUkjB,0BAA0BnjB,GACpCC,IAAUmjB,2BAA2BpjB,GACrCC,IAAUsL,2BAA2BvL,MAEvC+C,KACD,GAlBCyP,EAAc,KACd6Q,EAAU,KACVC,EAAe,KACfC,EAAc,KACdC,EAAsB,KACtBC,EAAuB,KACvBnY,EAAuB,KAcnBxE,EAAWC,cAG0D,KAAX2E,mBAAS,GAAE,GAApEgY,EAAwB,KAAEC,EAA2B,KACW,KAAXjY,mBAAS,GAAE,GAAhEkY,EAAsB,KAAEC,EAAyB,KACmB,KAAXnY,mBAAS,GAAE,GAApEoY,EAAwB,KAAEC,EAA2B,KAGa,KAAXrY,mBAAS,GAAE,GAAlEsY,EAAuB,KAAEC,EAA0B,KACW,KAAXvY,mBAAS,GAAE,GAA9DwY,EAAqB,KAAEC,EAAwB,KACmB,KAAXzY,mBAAS,GAAE,GAAlE0Y,EAAuB,KAAEC,EAA0B,KAE1D,EAGIC,aAAsBjpB,EAAYiQ,GAFvBiZ,EAAqB,EAAlCC,YACSC,EAAiB,EAA1BC,QAGIC,EAA0BvY,uBAAY,WAC1C,MAA2B,SAAvB/Q,EAAWupB,QACN,EAEF,KACN,CAACvpB,IA0CJ,GAxCA0Q,qBAAU,WACR,MAAkCpG,IAAKkf,qBAAqBvZ,GAApDwZ,EAAS,EAATA,UAAWC,EAAU,EAAVA,WAGnB,KADsCR,GAAyBE,GAE7D,OAAO,WACL3d,EAASU,IAAQwd,aAAa7kB,IAAa0iB,kCAG/C,IAAMoC,EAAqBV,EAAsBW,EAAIT,EAAkBS,EACjEC,EAAsBZ,EAAsBa,EAAIX,EAAkBW,EAElEC,EAAc7nB,OAAOyK,8BAAgCnG,eAAcwjB,KAAKC,YAAc/nB,OAAOgoB,WAC7FC,EAAkBjoB,OAAOyK,8BAAgCnG,eAAcwjB,KAAKI,UAAY,EAE9F3B,EAA4BvR,EAbC,IAc7BqR,EAA0BZ,EAAiBzf,QAAQI,wBAAwB+hB,IAAMF,GACjF,IAAMG,EAAYP,EAAc7S,EAAiBiS,EAAkBS,EAftC,GAeiEH,EAAaE,EAE3GtB,EADsC,IACViC,GAC5B,IAAMC,EAAiBxqB,EAAWyqB,0BAC5BC,EAAmB1qB,EAAW2qB,QAAUH,EAAeX,EAAKW,EAAeX,EAAIC,EAAsB,EAC3GlB,EAA2B2B,EAAYlC,EAA2BiB,IAA4BoB,GAE9F1B,EAA2B7R,EAtBE,GAsBsCkR,GAEnE,IAAMuC,EAAmB5qB,EAAW2qB,QAAUH,EAAeT,EAAKS,EAAeT,EAAID,EAAsB,EAC3GhB,EAAyBM,EAAkBW,EAAKD,EAAsB,EAAKL,EAAYmB,GAEvF,IAAMC,EAAsB,WAC1Bpf,EAASU,IAAQwd,aAAa7kB,IAAa0iB,kCAK7C,OAFAld,IAAKsG,iBAAiB,oBAAqBia,OAAqB/pB,EAAWmP,GAEpE,WACL3F,IAAKuG,oBAAoB,oBAAqBga,EAAqB5a,MAEpE,CAAC2X,EAAkBzQ,EAAgB+R,EAAuBE,EAAmBjB,EAAwBC,EAAyB3c,EAAUwE,IAEvI+X,IAAeC,GAAmBJ,KAAuBK,EAAgB,CAC3E,IAAM4C,EAAiB9G,KAAK+G,IAAIxC,EAAyBM,GAGnDmC,EAAczC,EAAyBM,EAAwBA,EAFxC,EAEuFN,EAEpH,OACE,kBAAC,GAAmB,KAClB,yBAAKllB,UAAU,iBAAiBuC,MAAO,CAAEqlB,MAAO5C,EAA0B6C,MAAOzC,EAA0B6B,IAAK/B,KAChH,yBAAKllB,UAAU,eAAeuC,MAAO,CAAE4C,OAAQsiB,EAAgBR,IAAKU,EAAaE,MAAOzC,EAA2BJ,KACnH,yBAAKhlB,UAAU,iBAAiBuC,MAAO,CAAEqlB,MAAOtC,EAAyBuC,MAAOnC,EAAyBuB,IAAKzB,IAC5G,yBAAKxlB,UAAU,gBAIvB,OAAO,M,w5CE7HT,gmGAAA7B,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,8YAAAA,GAAA,gEAAAA,GAAA,0JAAAA,EAAA,6FAAAA,GAAA,mIAAAA,IAAA,8SAAAA,IAAA,4OAAAA,EAAA,iBAAAA,EAAA,EAAAA,IAAA,EAAAA,GAAA,EAAAA,GAAA,SAuBA,IAAMqF,GAAY,CAChB7G,WAAY8G,IAAU0K,OAAOvK,WAC7B+M,gBAAiBlN,IAAUwF,KAC3Bd,kBAAmB1E,IAAUwF,KAC7B6e,eAAgBrkB,IAAUwF,KAC1B2H,kBAAmBnN,IAAUE,MAG3BokB,GAAS,EAEPC,GAAO,SAAH,GAQJ,IAPJrrB,EAAU,EAAVA,WACAgU,EAAe,EAAfA,gBACAxI,EAAiB,EAAjBA,kBACA2f,EAAc,EAAdA,eACAlX,EAAiB,EAAjBA,kBACA4T,EAAiB,EAAjBA,kBACAyD,EAAuB,EAAvBA,wBAEA,EASIzjB,qBAAWlI,KARb6T,EAAU,EAAVA,WACA5L,EAAM,EAANA,OACAwK,EAAkB,EAAlBA,mBACAoT,EAAiB,EAAjBA,kBACAN,EAAkB,EAAlBA,mBACAQ,EAAoB,EAApBA,qBAEAvD,EAAa,EAAbA,cAEIoJ,EAAexjB,mBACfyjB,EAAqBzjB,mBACyB,KAAZsI,mBAAS,IAAG,GAA7Cob,EAAY,KAAEC,EAAe,KAC9BjJ,EAAM1a,iBAAO,IACb0D,EAAWC,cACVnH,EAAqB,GAAhBC,cAAgB,GAApB,GACFmnB,EAAmB,IAAIC,IAuB5B,KAZGlnB,aACF,SAACC,GAAK,MAAK,CACTC,IAAUinB,yBAAyBlnB,GACnCC,IAAUknB,+BAA+BnnB,GACzCC,IAAUmnB,yBAAyBpnB,GACnCC,IAAUonB,gCAAgCrnB,GAC1CC,IAAUqnB,mCAAmCtnB,GAC7CC,IAAUsL,2BAA2BvL,GACrCC,IAAUsnB,sBAAsBvnB,GAChCC,IAAUunB,wBAAwBxnB,MAEpC+C,KACD,GApBC0kB,EAAqB,KACrBC,EAA2B,KAC3BC,EAAqB,KACrBC,EAAyB,KACzBN,EAAkC,KAClC/hB,EAAiB,KACjBsiB,EAAkB,KAClBC,EAAoB,KAehBC,EAAU1sB,EACbuV,aACAsO,MAAK,SAACxD,EAAGyD,GAAC,OAAKzD,EAAe,YAAIyD,EAAe,eAEpD4I,EAAQ5c,QAAO,SAAC6c,GAAC,OAAKL,EAAsBM,IAAID,EAAEpb,OAAKlR,SAAQ,SAACssB,GAAC,OAAKhB,EAAiBkB,IAAIF,EAAEpb,OAE7Fb,qBAAU,WACR,IAAM6M,EAA4B,SAACC,EAAaC,GAC/B,WAAXA,GACFD,EAAYnd,SAAQ,SAACqd,GACf4O,EAAsBM,IAAIlP,EAAMnM,KAClC9F,EAASU,IAAQ2gB,uBAAuB,CAAEC,QAAQ,EAAMhf,aAAc2P,EAAMnM,UAOpF,OAFAjH,IAAKsG,iBAAiB,oBAAqB2M,OAA2Bzc,EAAWoJ,GAE1E,WACLI,IAAKuG,oBAAoB,oBAAqB0M,EAA2BrT,MAE1E,CAACoiB,IAEJ5b,qBAAU,WACR,IAAMsc,EAAaxB,EAAmBrjB,QAChC8kB,EAAa1B,EAAapjB,QAAQI,wBAAwBC,OAChEgjB,EAAmBrjB,QAAU8kB,EAKzBD,GAAchJ,KAAKkJ,MAAMF,KAAgBhJ,KAAKkJ,MAAMD,IACtDrlB,OAIJ8I,qBAAU,WACR,GAAI0b,EAAuB,CACzB,IAAMe,EAAoB1mB,eAAc0G,iBAAiB,eAAe,GACxEsV,EAAIta,QAAQ9H,SAAQ,SAACoO,GACnB,IAAM2e,EAAQD,EAAkBzmB,cAAc,kCAAD,OAAmC+H,EAAE,MAC9E2e,GACFA,EAAMC,WAAW5F,YAAY2F,MAIjC3K,EAAIta,QAAU,GAEd,IAAMxD,EAAQ,CACZ3E,aACAwT,cAGF4Y,EAAsBb,EAAapjB,QAASxD,GAAO,WAAe,MAC1DwW,GAAU,EAAAnY,UAASC,cAAa,mBAChCwL,EAAK,kBAAH,OAAqB2c,IAQ7B,OAPAA,KACA3I,EAAIta,QAAQiF,KAAKqB,GACjB0M,EAAQoM,aAAa,gCAAiC9Y,GACtD0M,EAAQvK,iBAAiB,aAAa,SAACpL,GACrCA,EAAEE,qBAGGyV,SAKbzK,qBAAU,WAGY,KADA0B,EAAmBpS,EAAWuR,KACxBiU,IAAsBN,GAC9C/U,GAAa,EAAM,KAEpB,CAAC+U,EAAoBM,EAAmBrV,EAAcnQ,EAAYwL,IAErEmS,cAAa,YACPuH,GAAuBM,GACzBrV,GAAa,EAAO,KAErB,CAAC+U,EAAoBM,EAAmBrV,IAE3C,IAAMmd,EAAe,eArKvB,EAqKuB,GArKvB,EAqKuB,WAAG,WAAO9nB,GAAC,wEAGL,GAAzBA,GAAKA,EAAEE,mBAEH8F,EAAmB,CAAF,eACiB,OAApCyI,GAAmBD,GAAiB,0BAarC,GAVGsY,EAAsBM,IAAI5sB,EAAWuR,KACvC9F,EAASU,IAAQ2gB,uBAAuB,CAAEC,QAAQ,EAAMhf,aAAc/N,EAAWuR,MAGnF8a,GAA+BA,EAA4BrsB,GACtDwT,IACHlJ,IAAKijB,uBAAuBrjB,GAG5BR,YAAW,kBAAM+B,EAASU,IAAQgb,YAAYriB,IAAa0iB,mCAAkC,OAE3F2D,GAAoBqB,GAAsBC,IAAyBe,KAAqBC,QAAQ,iBAMjG,GALDnjB,IAAK2c,iBAAiBjnB,EAAYkK,GAClCiY,EAAcniB,EAAWuR,IACzBjH,IAAK4c,iBAAiBlnB,EAAYkK,GAC7B+hB,GACHxgB,EAASU,IAAQgb,YAAYriB,IAAa4oB,oBAExClB,EAAoB,CAAF,gBAC8D,OAA5E7V,EAAkB3W,EAAW4W,cAAcC,MAAiC,UAC5EvM,IAAKwM,kBAAkB6W,0BAA0BhX,GAAgB,QACvErM,IAAKwM,kBAAkB8W,mBAAmB,2CAnMlD,iLAsMG,gBAjCoB,sCAmCfC,EAAmBlC,EAAiBvqB,KAAO,EAE3C0sB,EAAYxe,IAAW,CAC3B+b,MAAM,EACN0C,SAAUva,EACV,oBAAqBQ,EACrB2M,OAAQ2L,EAAsBM,IAAI5sB,EAAWuR,KAAOsc,EACpD,SAAYrB,GAAsBC,IAAyBe,KAAqBC,UAG5EO,EAAe1e,IAAW,CAC9Bod,SAAS,EACTuB,QAASza,IAGX9C,qBAAU,WAGHlF,GACHkhB,EAAQrsB,SAAQ,SAAC6tB,EAAOnK,GACtB,IAAM/C,EAAc5O,EAAmB8b,EAAM3c,IACxB,KAAhByP,QAA6C,IAAhBA,GAAgCxN,GAChErD,GAAa,EAAM,EAAI4T,QAI5B,CAACvQ,EAAYhI,IAEhBkF,qBAAU,WACJlF,GACF2E,GAAa,EAAO,KAErB,CAAC3E,IAEJ,IAAM2iB,GAAiBzS,OAAO0S,OAAO3C,GAAc4C,MAAK,SAACC,GAAG,OAAKA,KAE3DC,EAAqB,SAACL,GAEtBvC,EAAiBiB,IAAIsB,EAAM3c,MAC7B9F,EAASU,IAAQ2gB,uBAAuB,CAAEC,QAAQ,EAAMhf,aAAcmgB,EAAM3c,MAC5EjH,IAAK2G,qBAAqB/G,GAAmB+c,iBAAiBiH,KAI5DM,EAAqB,WAEzB,GAAI7C,EAAiBvqB,KAAO,EAAG,CAC7B,IAAMqtB,EAAmB/B,EAAQ5c,QAAO,SAAC6c,GAAC,OAAKhB,EAAiBiB,IAAID,EAAEpb,OACtEjH,IAAK2G,qBAAqB/G,GAAmBwkB,kBAAkBD,GAC/DA,EAAiBpuB,SAAQ,SAACssB,GAAC,OAAKlhB,EAASU,IAAQ2gB,uBAAuB,CAAEC,QAAQ,EAAMhf,aAAc4e,EAAEpb,WAItGpB,EAAeY,uBACnB,SAAC6C,EAAWmQ,GACV2H,GAAgB,SAACvmB,GAAG,gBACfA,GAAG,SACL4e,EAAQnQ,SAGb,CAAC8X,IAGGjF,EAAmBnc,IAAKqkB,oBAAoB3uB,EAAYkK,GACxD0kB,GAAUnI,EAAiBnlB,OAAS,EACpC8S,GAAkBoI,aAAmBxc,KAAgBmd,KAAkBC,eAEvEyR,GAAcnC,EAAQprB,OAAS,EAAIorB,EAAQA,EAAQprB,OAAS,GAAGiQ,GAAK,KAE1E,OACE,yBACEvN,IAAKunB,EACLloB,UAAWyqB,EACXrf,GAAE,eAAUzO,EAAWuR,KAEvB,kBAAClL,GAAA,EAAM,CACLhD,UAAU,cACVkC,QAAS,SAACC,GAAC,OAAK8nB,EAAgB9nB,IAChCspB,eAAc,eAAU9uB,EAAWuR,IACnCwd,YAAavb,EACblN,YAAY,qBAEd,kBAAC,GAAW,CACV8J,UAAW,EACXpQ,WAAYA,EACZmQ,aAAcA,EACdyD,UAAW6X,EAAa,GACxBtP,oBAAqBmQ,EAAsBM,IAAI5sB,EAAWuR,IAC1DmC,SAAU4Y,EAAsBM,IAAI5sB,EAAWuR,KAAOsc,EACtD5Z,kBAAmB,SAACzO,GAClB2c,EAAcniB,EAAWuR,IACzB0C,EAAkBzO,IAEpBwO,gBAAiBA,EACjBxI,kBAAmBA,KAEnBgI,GAAckS,GAAwB6G,KAA+BnY,IACrE,oCACGsY,EAAQprB,OAAS,GAChB,yBAAK+B,UAAW2qB,GACbH,GACC,kBAACxnB,GAAA,EAAM,CACLC,YAAY,oBACZjD,UAAU,uBACV8H,MAAO5G,EAAE,sBACTgB,QAASipB,IAGZ9B,EAAQvnB,KAAI,SAAC+oB,EAAO1sB,GAAC,OACpB,yBAAK6B,UAAU,QAAQoL,GAAE,qBAAgByf,EAAM3c,IAAMyJ,IAAG,qBAAgBkT,EAAM3c,KAC5E,kBAAC,GAAW,CACVnB,UAAW5O,EAAI,EACfwZ,IAAKkT,EAAM3c,GACXvR,WAAYkuB,EACZ/d,aAAcA,EACdyD,UAAW6X,EAAajqB,EAAI,GAC5B4a,eAAgBmS,EAChB7a,SAAU4Y,EAAsBM,IAAIsB,EAAM3c,IAC1C0C,kBAAmBA,EACnBD,gBAAiBA,EACjBxI,kBAAmBA,SAM5BojB,IACC,kBAAC,GAAgB,CACfnI,iBAAkBA,EAClBjb,kBAAmBA,IAEtB2iB,IAAkB3iB,GACjB,kBAAC,GAAS,CACRkI,SAAUmb,IAAevC,EAAsBM,IAAIiC,IACnD5J,qBAAsBuJ,EACtBxuB,WAAYA,KAKnBwT,IAAe2X,GAAkBtD,KAAuByD,GACvD,kBAAC,GAA2B,CAC1BtrB,WAAYA,EACZ4nB,iBAAkB2D,EAClB1D,kBAAmBA,MAO7BwD,GAAKxkB,UAAYA,GAEFwkB,UChWAA,Q,qBCFf,IAAI9e,EAAM,EAAQ,IACF3D,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQ4D,WAAa5D,EAAQ6D,QAAU7D,KAG/CA,EAAU,CAAC,CAAC8D,EAAOlL,EAAIoH,EAAS,MAG9C,IAAIrG,EAAU,CAEd,OAAiB,SAAUoK,GAgBX,IAAKxK,OAAOyK,8BAEV,YADA5J,SAAS6J,KAAKvJ,YAAYqJ,GAI5B,IAAIG,EAEJA,EAAgB9J,SAAS+J,qBAAqB,oBAEzCD,EAAcxL,SACjBwL,EAzBF,SAASE,EAAwBC,EAASjH,EAAOhD,UAC/C,MAAMkK,EAAW,GAYjB,OATAlH,EAAKmH,iBAAiBF,GAAS5M,QAAQqI,GAAMwE,EAASE,KAAK1E,IAG3D1C,EAAKmH,iBAAiB,KAAK9M,QAAQqI,IAC7BA,EAAG2E,YACLH,EAASE,QAAQJ,EAAwBC,EAASvE,EAAG2E,eAIlDH,EAYSF,CAAwB,qBAG1C,MAAMM,EAAkB,GACxB,IAAK,IAAI9L,EAAI,EAAGA,EAAIsL,EAAcxL,OAAQE,IAAK,CAC7C,MAAM+L,EAAeT,EAActL,GACnC,GAAU,IAANA,EACF+L,EAAaF,WAAW/J,YAAYqJ,GACpCA,EAASa,OAAS,WACZF,EAAgBhM,OAAS,GAC3BgM,EAAgBjN,QAASoN,IAEvBA,EAAUC,UAAYf,EAASe,iBAIhC,CACL,MAAMD,EAAYd,EAASgB,WAAU,GACrCJ,EAAaF,WAAW/J,YAAYmK,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEPlB,EAAI3D,EAASrG,GAI1BmK,EAAOkB,QAAUhF,EAAQiF,QAAU,I,sBClEnCD,EAAUlB,EAAOkB,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACV,EAAOlL,EAAI,6pTAA8pT,KAGvrToM,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,0lFCOvB,IAAMhH,EAAY,CAChB0b,SAAUzb,IAAUwF,KACpB0iB,cAAeloB,IAAUwF,KACzB2iB,mBAAoBnoB,IAAUwF,KAC9B4iB,SAAUpoB,IAAUqoB,IACpBtK,SAAU/d,IAAU0K,OACpB4d,cAAetoB,IAAUE,KACzBqoB,qBAAsBvoB,IAAU0K,OAChC8d,aAAcxoB,IAAU0K,OACxB+d,wBAAyBzoB,IAAUC,OACnCiH,eAAgBlH,IAAUE,MAGtBwoB,EAAwB,SAAH,GAWrB,IAVJjN,EAAQ,EAARA,SACAyM,EAAa,EAAbA,cACAC,EAAkB,EAAlBA,mBACAC,EAAQ,EAARA,SACArK,EAAQ,EAARA,SACAuK,EAAa,EAAbA,cACAC,EAAoB,EAApBA,qBACAC,EAAY,EAAZA,aACAC,EAAuB,EAAvBA,wBACAvhB,EAAc,EAAdA,eAEOzJ,EAAqB,EAAhBC,cAAgB,GAApB,GACyC,IAAf6L,oBAAS,GAAM,GAA1Cof,EAAU,KAAEC,EAAW,KAExBtb,EAAkBoI,YAAmB6S,KAA0BlS,IAAkBC,eAEjFuS,EACJ,yBACEtsB,UAAWiM,IAAW,CACpBsgB,OAAO,EACPJ,uBAAuB,EACvBK,KAAMZ,EACNa,mBAAoB1b,IAEtBpQ,IAAKkrB,EACLpc,eAAchO,IAAa4c,qBAC3B9b,MAAK,KAAOif,GACZkL,YAAa,SAACvqB,GACZA,EAAEE,mBAEJ4gB,YAAa,SAAC9gB,GACR+c,IACF/c,EAAEE,kBACF0pB,MAGJlrB,UAAW,SAACsB,GACI,WAAVA,EAAEwV,KACJoU,MAIJ,yBACE/rB,UAAWiM,IAAW,CACpB,4BAA4B,EAC5B,SAAYmgB,IAEdnJ,YAAa,SAAC9gB,GACR+c,GACF/c,EAAEE,oBAIL6c,GACC,yBAAKlf,UAAU,yBACb,kBAACgD,EAAA,EAAM,CACLE,IAAKkpB,EAAa,oBAAsB,kBACxCpsB,UAAU,eACViD,YAAaxB,IAAakrB,mCAC1BzqB,QAAS,kBAAMmqB,GAAaD,MAE9B,0BAAMpsB,UAAU,+BAA+BkB,EAAE,mBACjD,kBAAC8B,EAAA,EAAM,CACLE,IAAK,aACLD,YAAaxB,IAAamrB,kCAC1B1qB,QAAS6pB,KAIf,kBAAC,IAAYc,SAAQ,CAACvvB,MAAO2uB,GAC3B,kBAACjE,EAAA,EAAI,CACHrrB,WAAYqvB,EACZrb,iBAAiB,EACjBxI,mBAAmB,EACnByI,kBAAmB,eAErB,kBAACnG,EAAA,EAAqB,CACpBC,aAAcwhB,EACdvhB,eAAgBA,OAO1B,OAAOghB,GAAiB5a,EACtBub,EAEA,kBAAC,IAAS,CAACQ,OAAO,0EAA0ER,IAIhGH,EAAsB3oB,UAAYA,EAEnB2oB,Q,6rFC7Gf,IAAM3oB,EAAY,CAChB7G,WAAY8G,IAAU0K,OACtB4d,cAAetoB,IAAUE,MAGrBopB,EAAiC,SAAH,GAAsC,IAAhCpwB,EAAU,EAAVA,WAAYovB,EAAa,EAAbA,cAsBnD,IAZG1qB,aACF,SAACC,GAAK,MAAK,CACTC,IAAU+c,cAAchd,EAAOG,IAAa8c,aAC5Chd,IAAUyrB,oBAAoB1rB,GAC9BC,IAAU+c,cAAchd,EAAOG,IAAawrB,YAC5C1rB,IAAU2rB,mBAAmB5rB,GAC7BC,IAAU4rB,6BAA6B7rB,GACvCC,IAAU6rB,gBAAgB9rB,GAC1BC,IAAUsgB,mBAAmBvgB,GAC7BC,IAAUsL,2BAA2BvL,MAEvC+C,KACD,GApBCwa,EAAgB,KAChBwO,EAAgB,KAChBC,EAAe,KACfC,EAAe,KACfzc,EAAuB,KACvBL,EAAY,KACZoR,EAAkB,KAClBjV,EAAuB,KAcnBxE,EAAWC,cAC4C,IAA7B2E,mBAAS,CAAEwgB,KAAM,EAAGvG,IAAK,IAAI,GAAtDzF,EAAQ,KAAEiM,EAAW,KACtB5B,EAAWnnB,mBAEXwa,EAAWwO,cACX/B,EAAgBzM,KAAcyO,KAAkBC,IAChDC,EAA2BhP,GAAqBwO,GAAoBC,GAAuC,eAApBC,EAG7FO,YAAkBjC,GAAU,SAAC1pB,GAC3B,IAAM4rB,EAAa3qB,cAAcC,cAAc,+BACzC2qB,EAAsBD,aAAU,EAAVA,EAAYE,SAAS9rB,EAAEyD,QAC7C+C,EAAkBvF,cAAcC,cAAc,kCAAD,OAAmC1G,EAAWuR,GAAE,OAC7FggB,EAA2BvlB,aAAe,EAAfA,EAAiBslB,SAAS9rB,EAAEyD,QACvDuoB,EAAaC,cACbC,EAAeC,cACfC,EAAcC,cAMfR,GAAwBE,GAA6BG,GAAiBE,GAAgBJ,GACzF/lB,EAASU,IAAQwd,aAAa7kB,IAAa4c,0BAI/C,IAAMuN,GAAsBiC,EAEtBY,EAAmB,WACnB7C,GAAsBC,EAAS/mB,UAAYoa,GAC7CuO,EAAYiB,YAAiB/xB,EAAYkvB,EAAUjf,KAIvDhI,2BAAgB,WACd6pB,MACC,CAAC7hB,EAAyBjQ,IAE7B,IAAMgyB,EAAeC,KAAS,WAC5BH,MAjCoC,GAkCJ,CAAE,UAAY,EAAM,SAAW,IAEjE7pB,2BAAgB,WAGd,OAFA9F,OAAOyO,iBAAiB,SAAUohB,GAE3B,WACL7vB,OAAO0O,oBAAoB,SAAUmhB,MAEtC,IAGH,IAAoE,IAAZ3hB,mBAAS,IAAG,GAA7DiC,EAAoB,KAAE4f,EAAuB,KAC9ClkB,EAAiB,SAACmkB,EAAc9U,GACpC6U,GAAwB,SAAC/sB,GAAG,cACvBA,GAAG,QACLgtB,EAAY,YAAQhtB,EAAIgtB,IAAiB,IAAK,EAAG9U,UAI2B,IAAnBhN,wBAASvP,GAAU,GAA1EyuB,EAAuB,KAAE6C,EAA0B,KAEM,IAAZ/hB,mBAAS,IAAG,GAAzD+B,EAAkB,KAAEigB,EAAqB,KAC1CnV,EAAqBnM,uBACzB,SAACiQ,EAAamR,GACZE,GAAsB,SAACltB,GAAG,cACrBA,GAAG,QACLgtB,EAAenR,SAGpB,CAACqR,IAGuD,IAAZhiB,mBAAS,IAAG,GAAnDgC,GAAe,KAAEigB,GAAkB,KACpC7M,GAAkB1U,uBACtB,SAACwhB,EAAcJ,GACbG,IAAmB,SAACntB,GAAG,cAClBA,GAAG,QACLgtB,EAAeI,SAGpB,CAACD,KAsBGhD,GAAe,CACnBtS,YAAa,GACbpV,OAAQ,WAAM,OACU,QAAlB,EAAA0C,IAAKkoB,qBAAa,aAAlB,EAAoBxT,aAAcyT,IAAYC,eAChD5B,EAAYiB,YAAiB/xB,EAAYkvB,EAAUjf,KAGvDuD,YAAY,EACZgS,kBAAmBlb,IAAKkG,kBAAkBxQ,KAAgBA,EAAWG,cACrEiS,qBACA8K,qBACA7K,mBACAoT,mBACAP,qBACAjI,wBAAyB,aACzByI,sBAAsB,EACtBC,uBAAuB,EACvB7R,eACAK,0BACAgO,cAAeiQ,EACf9f,uBACAtE,iBACAqU,iBA1CuB,SAAC8P,GACxBD,GAAwB,SAAC/sB,GAAG,cACvBA,GAAG,QACLgtB,EAAe,SAwClB/P,iBArCuB,SAAC+P,EAAc9jB,GACtC,IAAMskB,EAAiBrgB,EAAqB6f,GAC5C,IAAIQ,aAAc,EAAdA,EAAgBrxB,QAAS,EAAG,CAC9B,IAAMyiB,EAAQ4O,EAAenO,QAAQnW,GACjC0V,GAAS,IACX4O,EAAeC,OAAO7O,EAAO,GAC7BmO,GAAwB,SAAC/sB,GAAG,cACvBA,GAAG,QACLgtB,EAAY,EAAOQ,YAgC5B,OACE,kBAAC,EAAqB,CACpBpQ,SAAUA,EACVyM,cAAeA,EACfC,mBAAoBA,EACpBC,SAAUA,EACVrK,SAAUA,EACVuK,cAAeA,EACfC,qBAAsBrvB,EACtBsvB,aAAcA,GACdC,wBAAyBA,EACzBvhB,eAAgBA,KAKtBoiB,EAA+BvpB,UAAYA,EAE5BupB,QC7LAA", "file": "chunks/chunk.28.js", "sourcesContent": ["import React from 'react';\n\nconst NoteContext = React.createContext();\n\nexport default NoteContext;", "const setAnnotationRichTextStyle = (editor, annotation) => {\n  const richTextStyle = {};\n  const ops = editor.getContents().ops;\n  let breakpoint = 0;\n  ops.forEach((item) => {\n    const attributes = item.attributes;\n    const isMention = item.insert?.mention;\n    let value = item.insert;\n    if (isMention) {\n      const mention = item.insert.mention;\n      value = mention.denotationChar + mention.value;\n    }\n    const cssStyle = {};\n    if (attributes === null || attributes === undefined ? undefined : attributes.bold) {\n      cssStyle['font-weight'] = 'bold';\n    }\n    if (attributes === null || attributes === undefined ? undefined : attributes.italic) {\n      cssStyle['font-style'] = 'italic';\n    }\n    if (attributes === null || attributes === undefined ? undefined : attributes.color) {\n      cssStyle['color'] = attributes.color;\n    }\n    if (attributes === null || attributes === undefined ? undefined : attributes.underline) {\n      cssStyle['text-decoration'] = 'word';\n    }\n    if (attributes === null || attributes === undefined ? undefined : attributes.strike) {\n      if (cssStyle['text-decoration']) {\n        cssStyle['text-decoration'] = `${cssStyle['text-decoration']} line-through`;\n      } else {\n        cssStyle['text-decoration'] = 'line-through';\n      }\n    }\n    if (attributes === null || attributes === undefined ? undefined : attributes.size) {\n      cssStyle['font-size'] = attributes.size;\n    }\n    if (attributes === null || attributes === undefined ? undefined : attributes.font) {\n      cssStyle['font-family'] = attributes.font;\n    }\n\n    richTextStyle[breakpoint] = cssStyle;\n    breakpoint += value.length;\n  });\n  annotation.setRichTextStyle(richTextStyle);\n};\n\nexport default setAnnotationRichTextStyle;\n", "import React from 'react';\nimport ReactQuill, { Quill } from 'react-quill';\nimport 'quill-mention';\nimport mentionsManager from 'helpers/MentionsManager';\nimport Button from 'components/Button';\nimport { useTranslation } from 'react-i18next';\nimport { useSelector } from 'react-redux';\nimport DataElements from 'constants/dataElement';\nimport selectors from 'selectors';\nimport getRootNode from 'helpers/getRootNode';\n\nimport '../../../constants/quill.scss';\nimport './CommentTextarea.scss';\n\nlet globalUserData = [];\n\n// These are the formats that will be accepted by quill\n// removed images and videos\nconst formats = [\n  'background',\n  'bold',\n  'color',\n  'font',\n  'code',\n  'italic',\n  'link',\n  'size',\n  'strike',\n  'script',\n  'underline',\n  'blockquote',\n  'header',\n  'indent',\n  'list',\n  'align',\n  'direction',\n  'code-block',\n  'formula',\n  'mention',\n];\n\n// We override the default keyboard module to disable the list autofill feature\nconst Keyboard = Quill.import('modules/keyboard');\n\nclass CustomKeyboard extends Keyboard {\n  static DEFAULTS = {\n    ...Keyboard.DEFAULTS,\n    bindings: {\n      ...Keyboard.DEFAULTS.bindings,\n      'list autofill': undefined,\n    }\n  };\n}\n\nQuill.register('modules/keyboard', CustomKeyboard, true);\n\n// Overriding clipboard module to fix cursor issue after pasting text\nconst Clipboard = Quill.import('modules/clipboard');\nconst { quillShadowDOMWorkaround } = window.Core;\n\nclass QuillPasteExtra extends Clipboard {\n  constructor(quill, options) {\n    quillShadowDOMWorkaround(quill);\n    super(quill, options);\n  }\n}\nQuill.register('modules/clipboard', QuillPasteExtra, true);\n\n// mentionsModule has to be outside the funtion to be able to access it without it being destroyed and recreated\nconst mentionModule = {\n  mention: {\n    allowedChars: /^[A-Za-z\\sÅÄÖåäö0-9\\-_]*$/,\n    mentionDenotationChars: ['@', '#'],\n    mentionContainerClass: 'mention__element',\n    mentionListClass: 'mention__suggestions__list',\n    listItemClass: 'mention__suggestions__item',\n    renderItem(item) {\n      // quill-mentions does not support jsx being returned\n      const div = document.createElement('div');\n      div.innerText = item.value;\n      if (item.email) {\n        const para = document.createElement('p');\n        para.innerText = item.email;\n        para.className = 'email';\n        div.appendChild(para);\n      }\n      return div;\n    },\n    async source(searchTerm, renderList) {\n      const mentionsSearchFunction = mentionsManager.getMentionLookupCallback();\n      const foundUsers = await mentionsSearchFunction(globalUserData, searchTerm);\n      renderList(foundUsers, searchTerm);\n    }\n  }\n};\n\nconst CommentTextarea = React.forwardRef(\n  (\n    {\n      value = '',\n      onChange,\n      onKeyDown,\n      onBlur,\n      onFocus,\n      userData,\n      isReply,\n    },\n    ref\n  ) => {\n    const [t] = useTranslation();\n\n    const isAddReplyAttachmentDisabled = useSelector((state) => selectors.isElementDisabled(state, DataElements.NotesPanel.ADD_REPLY_ATTACHMENT_BUTTON));\n\n    globalUserData = userData;\n\n    const addAttachment = () => {\n      getRootNode().querySelector('#reply-attachment-picker')?.click();\n    };\n\n    const onClick = (e) => {\n      e.preventDefault();\n      e.stopPropagation();\n    };\n\n    const onScroll = (e) => {\n      e.preventDefault();\n      e.stopPropagation();\n    };\n\n    // Convert text with newline (\"\\n\") to <p>...</p> format so\n    // that editor handles multiline text correctly\n    const containsNewlines = value && value.split('\\n').length > 1;\n    if (containsNewlines) {\n      const contentArray = value.split('\\n');\n      value = contentArray.map((item) => {\n        const paragraph = document.createElement('p');\n        paragraph.innerText = item || '\\n';\n        return paragraph.outerHTML;\n      }).join('');\n    }\n\n    // onBlur and onFocus have to be outside in the div because of quill bug\n    return (\n      <div className='comment-textarea' onBlur={onBlur} onFocus={onFocus} onClick={onClick} onScroll={onScroll}>\n        <ReactQuill\n          className='comment-textarea ql-container ql-editor'\n          style={{ overflowY: 'visible' }}\n          ref={(ele) => {\n            if (ele) {\n              ele.getEditor().root.ariaLabel = `${isReply ? t('action.reply') : t('action.comment')}`;\n            }\n            return ref(ele);\n          }}\n          modules={userData && userData.length > 0 ? mentionModule : {}}\n          theme=\"snow\"\n          value={value}\n          placeholder={`${isReply ? t('action.reply') : t('action.comment')}...`}\n          onChange={onChange}\n          onKeyDown={onKeyDown}\n          formats={formats}\n        />\n        {isReply && !isAddReplyAttachmentDisabled &&\n          <Button\n            className='add-attachment'\n            dataElement={DataElements.NotesPanel.ADD_REPLY_ATTACHMENT_BUTTON}\n            img='ic_fileattachment_24px'\n            title={`${t('action.add')} ${t('option.type.fileattachment')}`}\n            onClick={addAttachment}\n          />\n        }\n      </div>\n    );\n  });\n\nCommentTextarea.displayName = 'CommentTextarea';\n\nexport default CommentTextarea;\n", "import React, { useLayoutEffect, useRef, useContext } from 'react';\nimport PropTypes from 'prop-types';\nimport { useSelector, shallowEqual } from 'react-redux';\nimport throttle from 'lodash/throttle';\nimport NoteContext from 'components/Note/Context';\nimport selectors from 'selectors';\nimport CommentTextarea from './CommentTextarea/CommentTextarea';\nimport mentionsManager from 'helpers/MentionsManager';\n\nconst propTypes = {\n  // same the value attribute of a HTML textarea element\n  value: PropTypes.string,\n  // same the placeholder attribute of a HTML textarea element\n  placeholder: PropTypes.string,\n  // same the onChange attribute of a HTML textarea element\n  onChange: PropTypes.func.isRequired,\n  // same the onBlur attribute of a HTML textarea element\n  onBlur: PropTypes.func,\n  // same the onBlur attribute of a HTML textarea element\n  onFocus: PropTypes.func,\n  // a function that will be invoked when Ctrl + Enter or Cmd + Enter or only Enter are pressed\n  onSubmit: PropTypes.func,\n};\n\nconst NoteTextarea = React.forwardRef((props, forwardedRef) => {\n  const [\n    userData,\n    canSubmitByEnter,\n  ] = useSelector(\n    (state) => [\n      selectors.getUserData(state),\n      selectors.isNoteSubmissionWithEnterEnabled(state),\n      selectors.getAutoFocusNoteOnAnnotationSelection(state),\n      selectors.getIsNoteEditing(state),\n    ],\n    shallowEqual,\n  );\n\n  const { resize } = useContext(NoteContext);\n  const textareaRef = useRef();\n  const prevHeightRef = useRef();\n\n  useLayoutEffect(() => {\n    // when the height of the textarea changes, we also want to call resize\n    // to clear the cell measurer cache and update the note height in the virtualized list\n    const boxDOMElement = textareaRef.current?.editor?.container.firstElementChild;\n    const boundingBox = boxDOMElement?.getBoundingClientRect() || {};\n    if (prevHeightRef.current && prevHeightRef.current !== boundingBox.height) {\n      resize();\n    }\n    prevHeightRef.current = boundingBox.height;\n    // we need value to be in the dependency array because the height will only change when value changes\n    // eslint-disable-next-line\n  }, [props.value, resize]);\n\n\n  const handleKeyDown = (e) => {\n    const enterKey = 13;\n    const enterKeyPressed = e.which === enterKey;\n    if (enterKeyPressed) {\n      const isSubmittingByEnter = canSubmitByEnter;\n      const isSubmittingByCtrlEnter = (e.metaKey || e.ctrlKey);\n\n      if (isSubmittingByEnter || isSubmittingByCtrlEnter) {\n        props.onSubmit(e);\n      }\n    }\n  };\n\n  const handleChange = (content, delta, source, editor) => {\n    // Removes Non-breaking Space and replaces with regular space\n    content = content.replace(/&nbsp;/g, ' ');\n\n    if (textareaRef.current) {\n      /* For the React Quill editor, the text won't ever be empty, at least a '\\n'\n       * will be there, so it is necessary to trim the value to check if it is empty\n       */\n      const isEmpty = editor && editor.getText().trim() === '' && content === '<p><br></p>';\n      let value = '';\n\n      if (!isEmpty) {\n        value = content.target ? content.target.value : content;\n      }\n      props.onChange(value);\n\n      // If the delta contains a mention, then move the cursor to the end of the editor.\n      // This is necessary instead of using debounce when mentioning a user because\n      // the debounce was generating the following bug\n      // https://apryse.atlassian.net/browse/WVR-2380\n      const deltaContainsMention = mentionsManager.doesDeltaContainMention(delta.ops);\n\n      if (deltaContainsMention) {\n        const formattedText = mentionsManager.getFormattedTextFromDeltas(delta.ops);\n        const mentionData = mentionsManager.extractMentionDataFromStr(formattedText);\n        const editortextValue = editor.getText();\n        const totalTextLength = editortextValue.length + mentionData.plainTextValue.length;\n        const textareaEditor = textareaRef.current?.editor;\n        setTimeout(() => textareaEditor?.setSelection(totalTextLength, totalTextLength), 1);\n      }\n    }\n  };\n\n  const textareaProps = {\n    ...props,\n    ref: (el) => {\n      textareaRef.current = el;\n      forwardedRef(el);\n    },\n    onChange: throttle(handleChange, 100),\n    onKeyDown: handleKeyDown,\n    userData,\n  };\n\n  return (\n    <>\n      <CommentTextarea {...textareaProps}/>\n    </>\n  );\n});\n\nNoteTextarea.displayName = 'NoteTextarea';\nNoteTextarea.propTypes = propTypes;\n\nexport default NoteTextarea;\n", "import NoteTextarea from './NoteTextarea';\n\nexport default NoteTextarea;", "import i18next from 'i18next';\nimport core from 'core';\n\nfunction createStateAnnotation(annotation, state, documentViewerKey = 1) {\n  // TODO: the code below is copied from annotManager.updateAnnotationState in WebViewer to work around the issue\n  // in https://github.com/PDFTron/webviewer-ui/issues/620\n  // the implement before wasn't causing any actual issues, but it was confusing and unnecessary to trigger two annotationChanged events when a status is set\n  // A proper fix should be done once https://trello.com/c/zWlkygNb/1023-consider-adding-a-setlocalizationhandler-to-corecontrols is implemented\n  // at that time, we could use the translation handler(t) internally in updateAnnotationState before setting the contents, and use that function instead in this component\n\n  const stateAnnotation = new window.Core.Annotations.StickyAnnotation();\n\n  stateAnnotation['InReplyTo'] = annotation['Id'];\n  stateAnnotation['X'] = annotation['X'];\n  stateAnnotation['Y'] = annotation['Y'];\n  stateAnnotation['PageNumber'] = annotation['PageNumber'];\n  stateAnnotation['Subject'] = 'Sticky Note';\n  stateAnnotation['Author'] = core.getCurrentUser();\n  stateAnnotation['State'] = state;\n  stateAnnotation['StateModel'] = state === 'Marked' || state === 'Unmarked' ? 'Marked' : 'Review';\n  stateAnnotation['Hidden'] = true;\n  stateAnnotation.enableSkipAutoLink();\n\n  const displayAuthor = core.getDisplayAuthor(stateAnnotation['Author'], documentViewerKey);\n  const stateMessage = i18next.t(`option.state.${state.toLowerCase()}`);\n  const contents = `${stateMessage} ${i18next.t('option.state.setBy')} ${displayAuthor}`;\n  stateAnnotation.setContents(contents);\n\n  return stateAnnotation;\n}\n\nexport {\n  createStateAnnotation,\n};\n", "import { useLayoutEffect } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport PropTypes from 'prop-types';\nimport DataElements from 'src/constants/dataElement';\nimport actions from 'actions';\nimport selectors from 'selectors';\n\nconst createFlyoutItem = (option, icon, dataElement) => ({\n  icon,\n  label: `option.state.${option.toLowerCase()}`,\n  title: `option.state.${option.toLowerCase()}`,\n  option,\n  dataElement,\n});\n\nexport const noteStateFlyoutItems = [\n  createFlyoutItem('Accepted', 'icon-annotation-status-accepted', 'noteStateFlyoutAcceptedOption'),\n  createFlyoutItem('Rejected', 'icon-annotation-status-rejected', 'noteStateFlyoutRejectedOption'),\n  createFlyoutItem('Cancelled', 'icon-annotation-status-cancelled', 'noteStateFlyoutCancelledOption'),\n  createFlyoutItem('Completed', 'icon-annotation-status-completed', 'noteStateFlyoutCompletedOption'),\n  createFlyoutItem('None', 'icon-annotation-status-none', 'noteStateFlyoutNoneOption'),\n  createFlyoutItem('Marked', 'icon-annotation-status-marked', 'noteStateFlyoutMarkedOption'),\n  createFlyoutItem('Unmarked', 'icon-annotation-status-unmarked', 'noteStateFlyoutUnmarkedOption'),\n];\n\nconst NoteStateFlyout = (props) => {\n  const {\n    noteId,\n    handleStateChange = () => {},\n    isMultiSelectMode = false,\n  } = props;\n\n  const dispatch = useDispatch();\n\n  const selectorSuffix = isMultiSelectMode ? '' : `-${noteId}`;\n  const flyoutSelector = `${DataElements.NOTE_STATE_FLYOUT}${selectorSuffix}`;\n  const currentFlyout = useSelector((state) => selectors.getFlyout(state, flyoutSelector));\n\n  const handleClick = (noteState) => {\n    handleStateChange(noteState);\n  };\n\n  useLayoutEffect(() => {\n    const noteStateFlyout = {\n      dataElement: flyoutSelector,\n      className: 'NoteStateFlyout',\n      items: noteStateFlyoutItems.map((item) => {\n        return {\n          ...item,\n          onClick: () => handleClick(item.option),\n        };\n      }),\n    };\n\n    if (!currentFlyout) {\n      dispatch(actions.addFlyout(noteStateFlyout));\n    } else {\n      dispatch(actions.updateFlyout(noteStateFlyout.dataElement, noteStateFlyout));\n    }\n  }, [handleStateChange]);\n\n  return null;\n};\n\nNoteStateFlyout.propTypes = {\n  noteId: PropTypes.string,\n  handleStateChange: PropTypes.func,\n  isMultiSelectMode: PropTypes.bool,\n};\n\nexport default NoteStateFlyout;", "import NoteStateFlyout from './NoteStateFlyout';\n\nexport default NoteStateFlyout;", "var api = require(\"!../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../node_modules/css-loader/index.js!../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../node_modules/sass-loader/dist/cjs.js!./quill.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".ql-container{box-sizing:border-box;font-family:Helvetica,Arial,sans-serif;font-size:13px;height:100%;margin:0;position:relative}.ql-container.ql-disabled .ql-tooltip{visibility:hidden}.ql-container.ql-disabled .ql-editor ul[data-checked]>li:before{pointer-events:none}.ql-clipboard{left:-100000px;height:1px;overflow-y:hidden;position:absolute;top:50%}.ql-clipboard p{margin:0;padding:0}.ql-container .ql-editor{box-sizing:border-box;line-height:1.42;height:100%;outline:none;overflow-y:auto;padding:12px 15px;-o-tab-size:4;tab-size:4;-moz-tab-size:4;text-align:left;white-space:pre-wrap;word-wrap:break-word;-webkit-user-select:text;-moz-user-select:text;user-select:text}.ql-editor>*{cursor:text}.ql-editor blockquote,.ql-editor h1,.ql-editor h2,.ql-editor h3,.ql-editor h4,.ql-editor h5,.ql-editor h6,.ql-editor ol,.ql-editor p,.ql-editor pre,.ql-editor ul{counter-reset:list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9}.ql-editor p{margin:0;padding:0}.ql-editor ol,.ql-editor ul{padding-left:1.5em}.ql-editor ol>li,.ql-editor ul>li{list-style-type:none}.ql-editor ul>li:before{content:\\\"\\\\2022\\\"}.ql-editor ul[data-checked=false],.ql-editor ul[data-checked=true]{pointer-events:none}.ql-editor ul[data-checked=false]>li *,.ql-editor ul[data-checked=true]>li *{pointer-events:all}.ql-editor ul[data-checked=false]>li:before,.ql-editor ul[data-checked=true]>li:before{color:#777;cursor:pointer;pointer-events:all}.ql-editor ul[data-checked=true]>li:before{content:\\\"\\\\2611\\\"}.ql-editor ul[data-checked=false]>li:before{content:\\\"\\\\2610\\\"}.ql-editor li:before{display:inline-block;white-space:nowrap;width:1.2em}.ql-editor li:not(.ql-direction-rtl):before{margin-left:-1.5em;margin-right:.3em;text-align:right}.ql-editor li.ql-direction-rtl:before{margin-left:.3em;margin-right:-1.5em}.ql-editor ol li:not(.ql-direction-rtl),.ql-editor ul li:not(.ql-direction-rtl){padding-left:1.5em}.ql-editor ol li.ql-direction-rtl,.ql-editor ul li.ql-direction-rtl{padding-right:1.5em}.ql-editor ol li{counter-reset:list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;counter-increment:list-0}.ql-editor ol li:before{content:counter(list-0,decimal) \\\". \\\"}.ql-editor ol li.ql-indent-1{counter-increment:list-1}.ql-editor ol li.ql-indent-1:before{content:counter(list-1,lower-alpha) \\\". \\\"}.ql-editor ol li.ql-indent-1{counter-reset:list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-2{counter-increment:list-2}.ql-editor ol li.ql-indent-2:before{content:counter(list-2,lower-roman) \\\". \\\"}.ql-editor ol li.ql-indent-2{counter-reset:list-3 list-4 list-5 list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-3{counter-increment:list-3}.ql-editor ol li.ql-indent-3:before{content:counter(list-3,decimal) \\\". \\\"}.ql-editor ol li.ql-indent-3{counter-reset:list-4 list-5 list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-4{counter-increment:list-4}.ql-editor ol li.ql-indent-4:before{content:counter(list-4,lower-alpha) \\\". \\\"}.ql-editor ol li.ql-indent-4{counter-reset:list-5 list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-5{counter-increment:list-5}.ql-editor ol li.ql-indent-5:before{content:counter(list-5,lower-roman) \\\". \\\"}.ql-editor ol li.ql-indent-5{counter-reset:list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-6{counter-increment:list-6}.ql-editor ol li.ql-indent-6:before{content:counter(list-6,decimal) \\\". \\\"}.ql-editor ol li.ql-indent-6{counter-reset:list-7 list-8 list-9}.ql-editor ol li.ql-indent-7{counter-increment:list-7}.ql-editor ol li.ql-indent-7:before{content:counter(list-7,lower-alpha) \\\". \\\"}.ql-editor ol li.ql-indent-7{counter-reset:list-8 list-9}.ql-editor ol li.ql-indent-8{counter-increment:list-8}.ql-editor ol li.ql-indent-8:before{content:counter(list-8,lower-roman) \\\". \\\"}.ql-editor ol li.ql-indent-8{counter-reset:list-9}.ql-editor ol li.ql-indent-9{counter-increment:list-9}.ql-editor ol li.ql-indent-9:before{content:counter(list-9,decimal) \\\". \\\"}.ql-editor .ql-indent-1:not(.ql-direction-rtl){padding-left:3em}.ql-editor li.ql-indent-1:not(.ql-direction-rtl){padding-left:4.5em}.ql-editor .ql-indent-1.ql-direction-rtl.ql-align-right{padding-right:3em}.ql-editor li.ql-indent-1.ql-direction-rtl.ql-align-right{padding-right:4.5em}.ql-editor .ql-indent-2:not(.ql-direction-rtl){padding-left:6em}.ql-editor li.ql-indent-2:not(.ql-direction-rtl){padding-left:7.5em}.ql-editor .ql-indent-2.ql-direction-rtl.ql-align-right{padding-right:6em}.ql-editor li.ql-indent-2.ql-direction-rtl.ql-align-right{padding-right:7.5em}.ql-editor .ql-indent-3:not(.ql-direction-rtl){padding-left:9em}.ql-editor li.ql-indent-3:not(.ql-direction-rtl){padding-left:10.5em}.ql-editor .ql-indent-3.ql-direction-rtl.ql-align-right{padding-right:9em}.ql-editor li.ql-indent-3.ql-direction-rtl.ql-align-right{padding-right:10.5em}.ql-editor .ql-indent-4:not(.ql-direction-rtl){padding-left:12em}.ql-editor li.ql-indent-4:not(.ql-direction-rtl){padding-left:13.5em}.ql-editor .ql-indent-4.ql-direction-rtl.ql-align-right{padding-right:12em}.ql-editor li.ql-indent-4.ql-direction-rtl.ql-align-right{padding-right:13.5em}.ql-editor .ql-indent-5:not(.ql-direction-rtl){padding-left:15em}.ql-editor li.ql-indent-5:not(.ql-direction-rtl){padding-left:16.5em}.ql-editor .ql-indent-5.ql-direction-rtl.ql-align-right{padding-right:15em}.ql-editor li.ql-indent-5.ql-direction-rtl.ql-align-right{padding-right:16.5em}.ql-editor .ql-indent-6:not(.ql-direction-rtl){padding-left:18em}.ql-editor li.ql-indent-6:not(.ql-direction-rtl){padding-left:19.5em}.ql-editor .ql-indent-6.ql-direction-rtl.ql-align-right{padding-right:18em}.ql-editor li.ql-indent-6.ql-direction-rtl.ql-align-right{padding-right:19.5em}.ql-editor .ql-indent-7:not(.ql-direction-rtl){padding-left:21em}.ql-editor li.ql-indent-7:not(.ql-direction-rtl){padding-left:22.5em}.ql-editor .ql-indent-7.ql-direction-rtl.ql-align-right{padding-right:21em}.ql-editor li.ql-indent-7.ql-direction-rtl.ql-align-right{padding-right:22.5em}.ql-editor .ql-indent-8:not(.ql-direction-rtl){padding-left:24em}.ql-editor li.ql-indent-8:not(.ql-direction-rtl){padding-left:25.5em}.ql-editor .ql-indent-8.ql-direction-rtl.ql-align-right{padding-right:24em}.ql-editor li.ql-indent-8.ql-direction-rtl.ql-align-right{padding-right:25.5em}.ql-editor .ql-indent-9:not(.ql-direction-rtl){padding-left:27em}.ql-editor li.ql-indent-9:not(.ql-direction-rtl){padding-left:28.5em}.ql-editor .ql-indent-9.ql-direction-rtl.ql-align-right{padding-right:27em}.ql-editor li.ql-indent-9.ql-direction-rtl.ql-align-right{padding-right:28.5em}.ql-editor .ql-video{display:block;max-width:100%}.ql-editor .ql-video.ql-align-center{margin:0 auto}.ql-editor .ql-video.ql-align-right{margin:0 0 0 auto}.ql-editor .ql-bg-black{background-color:#000}.ql-editor .ql-bg-red{background-color:#e60000}.ql-editor .ql-bg-orange{background-color:#f90}.ql-editor .ql-bg-yellow{background-color:#ff0}.ql-editor .ql-bg-green{background-color:#008a00}.ql-editor .ql-bg-blue{background-color:#06c}.ql-editor .ql-bg-purple{background-color:#93f}.ql-editor .ql-color-white{color:#fff}.ql-editor .ql-color-red{color:#e60000}.ql-editor .ql-color-orange{color:#f90}.ql-editor .ql-color-yellow{color:#ff0}.ql-editor .ql-color-green{color:#008a00}.ql-editor .ql-color-blue{color:#06c}.ql-editor .ql-color-purple{color:#93f}.ql-editor .ql-font-serif{font-family:Georgia,Times New Roman,serif}.ql-editor .ql-font-monospace{font-family:Monaco,Courier New,monospace}.ql-editor .ql-size-small{font-size:.75em}.ql-editor .ql-size-large{font-size:1.5em}.ql-editor .ql-size-huge{font-size:2.5em}.ql-editor .ql-direction-rtl{direction:rtl;text-align:inherit}.ql-editor .ql-align-center{text-align:center}.ql-editor .ql-align-justify{text-align:justify}.ql-editor .ql-align-right{text-align:right}.ql-editor.ql-blank:before{color:rgba(0,0,0,.6);content:attr(data-placeholder);font-style:italic;left:15px;pointer-events:none;position:absolute;right:15px}.ql-snow.ql-toolbar:after,.ql-snow .ql-toolbar:after{clear:both;content:\\\"\\\";display:table}.ql-snow.ql-toolbar button,.ql-snow .ql-toolbar button{background:none;border:none;cursor:pointer;display:inline-block;float:left;height:24px;padding:3px 5px;width:28px}.ql-snow.ql-toolbar button svg,.ql-snow .ql-toolbar button svg{float:left;height:100%}.ql-snow.ql-toolbar button:active:hover,.ql-snow .ql-toolbar button:active:hover{outline:none}.ql-snow.ql-toolbar input.ql-image[type=file],.ql-snow .ql-toolbar input.ql-image[type=file]{display:none}.ql-snow.ql-toolbar .ql-picker-item.ql-selected,.ql-snow .ql-toolbar .ql-picker-item.ql-selected,.ql-snow.ql-toolbar .ql-picker-item:hover,.ql-snow .ql-toolbar .ql-picker-item:hover,.ql-snow.ql-toolbar .ql-picker-label.ql-active,.ql-snow .ql-toolbar .ql-picker-label.ql-active,.ql-snow.ql-toolbar .ql-picker-label:hover,.ql-snow .ql-toolbar .ql-picker-label:hover,.ql-snow.ql-toolbar button.ql-active,.ql-snow .ql-toolbar button.ql-active,.ql-snow.ql-toolbar button:focus,.ql-snow .ql-toolbar button:focus,.ql-snow.ql-toolbar button:hover,.ql-snow .ql-toolbar button:hover{color:#06c}.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-fill,.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-fill,.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill,.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill,.ql-snow.ql-toolbar .ql-picker-item:hover .ql-fill,.ql-snow .ql-toolbar .ql-picker-item:hover .ql-fill,.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-fill,.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-fill,.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,.ql-snow.ql-toolbar .ql-picker-label:hover .ql-fill,.ql-snow .ql-toolbar .ql-picker-label:hover .ql-fill,.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,.ql-snow.ql-toolbar button.ql-active .ql-fill,.ql-snow .ql-toolbar button.ql-active .ql-fill,.ql-snow.ql-toolbar button.ql-active .ql-stroke.ql-fill,.ql-snow .ql-toolbar button.ql-active .ql-stroke.ql-fill,.ql-snow.ql-toolbar button:focus .ql-fill,.ql-snow .ql-toolbar button:focus .ql-fill,.ql-snow.ql-toolbar button:focus .ql-stroke.ql-fill,.ql-snow .ql-toolbar button:focus .ql-stroke.ql-fill,.ql-snow.ql-toolbar button:hover .ql-fill,.ql-snow .ql-toolbar button:hover .ql-fill,.ql-snow.ql-toolbar button:hover .ql-stroke.ql-fill,.ql-snow .ql-toolbar button:hover .ql-stroke.ql-fill{fill:#06c}.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke,.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke,.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter,.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter,.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke,.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke,.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke-miter,.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke-miter,.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke,.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke,.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke,.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke,.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke-miter,.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke-miter,.ql-snow.ql-toolbar button.ql-active .ql-stroke,.ql-snow .ql-toolbar button.ql-active .ql-stroke,.ql-snow.ql-toolbar button.ql-active .ql-stroke-miter,.ql-snow .ql-toolbar button.ql-active .ql-stroke-miter,.ql-snow.ql-toolbar button:focus .ql-stroke,.ql-snow .ql-toolbar button:focus .ql-stroke,.ql-snow.ql-toolbar button:focus .ql-stroke-miter,.ql-snow .ql-toolbar button:focus .ql-stroke-miter,.ql-snow.ql-toolbar button:hover .ql-stroke,.ql-snow .ql-toolbar button:hover .ql-stroke,.ql-snow.ql-toolbar button:hover .ql-stroke-miter,.ql-snow .ql-toolbar button:hover .ql-stroke-miter{stroke:#06c}@media(pointer:coarse){.ql-snow.ql-toolbar button:hover:not(.ql-active),.ql-snow .ql-toolbar button:hover:not(.ql-active){color:#444}.ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-fill,.ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-fill,.ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke.ql-fill,.ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke.ql-fill{fill:#444}.ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke,.ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke,.ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke-miter,.ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke-miter{stroke:#444}}.ql-snow,.ql-snow *{box-sizing:border-box}.ql-snow .ql-hidden{display:none}.ql-snow .ql-out-bottom,.ql-snow .ql-out-top{visibility:hidden}.ql-snow .ql-tooltip{position:absolute;transform:translateY(10px)}.ql-snow .ql-tooltip a{cursor:pointer;text-decoration:none}.ql-snow .ql-tooltip.ql-flip{transform:translateY(-10px)}.ql-snow .ql-formats{display:inline-block;vertical-align:middle}.ql-snow .ql-formats:after{clear:both;content:\\\"\\\";display:table}.ql-snow .ql-stroke{fill:none;stroke:#444;stroke-linecap:round;stroke-linejoin:round;stroke-width:2}.ql-snow .ql-stroke-miter{fill:none;stroke:#444;stroke-miterlimit:10;stroke-width:2}.ql-snow .ql-fill,.ql-snow .ql-stroke.ql-fill{fill:#444}.ql-snow .ql-empty{fill:none}.ql-snow .ql-even{fill-rule:evenodd}.ql-snow .ql-stroke.ql-thin,.ql-snow .ql-thin{stroke-width:1}.ql-snow .ql-transparent{opacity:.4}.ql-snow .ql-direction svg:last-child{display:none}.ql-snow .ql-direction.ql-active svg:last-child{display:inline}.ql-snow .ql-direction.ql-active svg:first-child{display:none}.ql-snow .ql-editor h1{font-size:2em}.ql-snow .ql-editor h2{font-size:1.5em}.ql-snow .ql-editor h3{font-size:1.17em}.ql-snow .ql-editor h4{font-size:1em}.ql-snow .ql-editor h5{font-size:.83em}.ql-snow .ql-editor h6{font-size:.67em}.ql-snow .ql-editor a{text-decoration:underline}.ql-snow .ql-editor blockquote{border-left:4px solid #ccc;margin-bottom:5px;margin-top:5px;padding-left:16px}.ql-snow .ql-editor code,.ql-snow .ql-editor pre{background-color:#f0f0f0;border-radius:3px}.ql-snow .ql-editor pre{white-space:pre-wrap;margin-bottom:5px;margin-top:5px;padding:5px 10px}.ql-snow .ql-editor code{font-size:85%;padding:2px 4px}.ql-snow .ql-editor pre.ql-syntax{background-color:#23241f;color:#f8f8f2;overflow:visible}.ql-snow .ql-editor img{max-width:100%}.ql-snow .ql-picker{color:#444;display:inline-block;float:left;font-size:14px;font-weight:500;height:24px;position:relative;vertical-align:middle}.ql-snow .ql-picker-label{cursor:pointer;display:inline-block;height:100%;padding-left:8px;padding-right:2px;position:relative;width:100%}.ql-snow .ql-picker-label:before{display:inline-block;line-height:22px}.ql-snow .ql-picker-options{background-color:#fff;display:none;min-width:100%;padding:4px 8px;position:absolute;white-space:nowrap}.ql-snow .ql-picker-options .ql-picker-item{cursor:pointer;display:block;padding-bottom:5px;padding-top:5px}.ql-snow .ql-picker.ql-expanded .ql-picker-label{color:#ccc;z-index:2}.ql-snow .ql-picker.ql-expanded .ql-picker-label .ql-fill{fill:#ccc}.ql-snow .ql-picker.ql-expanded .ql-picker-label .ql-stroke{stroke:#ccc}.ql-snow .ql-picker.ql-expanded .ql-picker-options{display:block;margin-top:-1px;top:100%;z-index:1}.ql-snow .ql-color-picker,.ql-snow .ql-icon-picker{width:28px}.ql-snow .ql-color-picker .ql-picker-label,.ql-snow .ql-icon-picker .ql-picker-label{padding:2px 4px}.ql-snow .ql-color-picker .ql-picker-label svg,.ql-snow .ql-icon-picker .ql-picker-label svg{right:4px}.ql-snow .ql-icon-picker .ql-picker-options{padding:4px 0}.ql-snow .ql-icon-picker .ql-picker-item{height:24px;width:24px;padding:2px 4px}.ql-snow .ql-color-picker .ql-picker-options{padding:3px 5px;width:152px}.ql-snow .ql-color-picker .ql-picker-item{border:1px solid transparent;float:left;height:16px;margin:2px;padding:0;width:16px}.ql-snow .ql-picker:not(.ql-color-picker):not(.ql-icon-picker) svg{position:absolute;margin-top:-9px;right:0;top:50%;width:18px}.ql-snow .ql-picker.ql-font .ql-picker-item[data-label]:not([data-label=\\\"\\\"]):before,.ql-snow .ql-picker.ql-font .ql-picker-label[data-label]:not([data-label=\\\"\\\"]):before,.ql-snow .ql-picker.ql-header .ql-picker-item[data-label]:not([data-label=\\\"\\\"]):before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-label]:not([data-label=\\\"\\\"]):before,.ql-snow .ql-picker.ql-size .ql-picker-item[data-label]:not([data-label=\\\"\\\"]):before,.ql-snow .ql-picker.ql-size .ql-picker-label[data-label]:not([data-label=\\\"\\\"]):before{content:attr(data-label)}.ql-snow .ql-picker.ql-header{width:98px}.ql-snow .ql-picker.ql-header .ql-picker-item:before,.ql-snow .ql-picker.ql-header .ql-picker-label:before{content:\\\"Normal\\\"}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\\\"1\\\"]:before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\\\"1\\\"]:before{content:\\\"Heading 1\\\"}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\\\"2\\\"]:before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\\\"2\\\"]:before{content:\\\"Heading 2\\\"}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\\\"3\\\"]:before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\\\"3\\\"]:before{content:\\\"Heading 3\\\"}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\\\"4\\\"]:before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\\\"4\\\"]:before{content:\\\"Heading 4\\\"}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\\\"5\\\"]:before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\\\"5\\\"]:before{content:\\\"Heading 5\\\"}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\\\"6\\\"]:before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\\\"6\\\"]:before{content:\\\"Heading 6\\\"}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\\\"1\\\"]:before{font-size:2em}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\\\"2\\\"]:before{font-size:1.5em}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\\\"3\\\"]:before{font-size:1.17em}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\\\"4\\\"]:before{font-size:1em}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\\\"5\\\"]:before{font-size:.83em}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\\\"6\\\"]:before{font-size:.67em}.ql-snow .ql-picker.ql-font{width:108px}.ql-snow .ql-picker.ql-font .ql-picker-item:before,.ql-snow .ql-picker.ql-font .ql-picker-label:before{content:\\\"Sans Serif\\\"}.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=serif]:before,.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=serif]:before{content:\\\"Serif\\\"}.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=monospace]:before,.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=monospace]:before{content:\\\"Monospace\\\"}.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=serif]:before{font-family:Georgia,Times New Roman,serif}.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=monospace]:before{font-family:Monaco,Courier New,monospace}.ql-snow .ql-picker.ql-size{width:98px}.ql-snow .ql-picker.ql-size .ql-picker-item:before,.ql-snow .ql-picker.ql-size .ql-picker-label:before{content:\\\"Normal\\\"}.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=small]:before,.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=small]:before{content:\\\"Small\\\"}.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=large]:before,.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=large]:before{content:\\\"Large\\\"}.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=huge]:before,.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=huge]:before{content:\\\"Huge\\\"}.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=small]:before{font-size:10px}.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=large]:before{font-size:18px}.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=huge]:before{font-size:32px}.ql-snow .ql-color-picker.ql-background .ql-picker-item{background-color:#fff}.ql-snow .ql-color-picker.ql-color .ql-picker-item{background-color:#000}.ql-toolbar.ql-snow{border:1px solid #ccc;box-sizing:border-box;font-family:Helvetica Neue,Helvetica,Arial,sans-serif;padding:8px}.ql-toolbar.ql-snow .ql-formats{margin-right:15px}.ql-toolbar.ql-snow .ql-picker-label{border:1px solid transparent}.ql-toolbar.ql-snow .ql-picker-options{border:1px solid transparent;box-shadow:0 2px 8px rgba(0,0,0,.2)}.ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label,.ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options{border-color:#ccc}.ql-toolbar.ql-snow .ql-color-picker .ql-picker-item.ql-selected,.ql-toolbar.ql-snow .ql-color-picker .ql-picker-item:hover{border-color:#000}.ql-toolbar.ql-snow+.ql-container.ql-snow{border-top:0}.ql-snow .ql-tooltip{background-color:#fff;border:1px solid #ccc;box-shadow:0 0 5px #ddd;color:#444;padding:5px 12px;white-space:nowrap}.ql-snow .ql-tooltip:before{content:\\\"Visit URL:\\\";line-height:26px;margin-right:8px}.ql-snow .ql-tooltip input[type=text]{display:none;border:1px solid #ccc;font-size:13px;height:26px;margin:0;padding:3px 5px;width:170px}.ql-snow .ql-tooltip a.ql-preview{display:inline-block;max-width:200px;overflow-x:hidden;text-overflow:ellipsis;vertical-align:top}.ql-snow .ql-tooltip a.ql-action:after{border-right:1px solid #ccc;content:\\\"Edit\\\";margin-left:16px;padding-right:8px}.ql-snow .ql-tooltip a.ql-remove:before{content:\\\"Remove\\\";margin-left:8px}.ql-snow .ql-tooltip a{line-height:26px}.ql-snow .ql-tooltip.ql-editing a.ql-preview,.ql-snow .ql-tooltip.ql-editing a.ql-remove{display:none}.ql-snow .ql-tooltip.ql-editing input[type=text]{display:inline-block}.ql-snow .ql-tooltip.ql-editing a.ql-action:after{border-right:0;content:\\\"Save\\\";padding-right:0}.ql-snow .ql-tooltip[data-mode=link]:before{content:\\\"Enter link:\\\"}.ql-snow .ql-tooltip[data-mode=formula]:before{content:\\\"Enter formula:\\\"}.ql-snow .ql-tooltip[data-mode=video]:before{content:\\\"Enter video:\\\"}.ql-snow a{color:#06c}.ql-container.ql-snow{border:1px solid #ccc}\", \"\"]);\n\n// exports\n", "var api = require(\"!../../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../../node_modules/css-loader/index.js!../../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../../node_modules/sass-loader/dist/cjs.js!./CommentTextarea.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.comment-textarea{position:relative}.comment-textarea .ql-toolbar{display:none}.comment-textarea .ql-container{border:none}.comment-textarea .ql-container .ql-editor{width:100%;padding:4px 6px;border:1px solid var(--border);border-radius:4px;color:var(--text-color);resize:none;overflow:hidden;box-sizing:border-box}.comment-textarea .ql-container .ql-editor:focus{outline:none;border:1px solid var(--focus-border)}.comment-textarea .ql-container .ql-editor.ql-blank:before{left:8px;list-style-type:none;font-style:normal;color:var(--placeholder-text)}.comment-textarea .ql-container .ql-editor p{margin:0;word-break:break-word}.comment-textarea .ql-container .ql-editor ul>li:before{content:none!important}.comment-textarea .ql-container.ql-snow{border:none}.comment-textarea .add-attachment{position:absolute;bottom:2px;right:2px;width:24px;height:24px}.comment-textarea .add-attachment:hover{background-color:var(--blue-1)}.comment-textarea .add-attachment .Icon{padding:3px}.ql-editor ul>li:before{content:none!important}.mention__element{width:170px;z-index:9001!important;max-height:200px;overflow-y:auto;overflow-y:overlay;overflow-x:hidden;background-color:var(--component-background);border:1px solid var(--border);border-radius:4px}.mention__suggestions__list{width:100%;font-size:14px;margin-top:0;padding-left:0!important;list-style:none;word-wrap:break-word;border-radius:4px}.mention__suggestions__item{background-color:var(--component-background);white-space:nowrap;padding-left:0;text-overflow:clip;padding:7px 5px 1px!important;margin:0;width:100%;cursor:pointer}.mention__suggestions__item .email{margin-top:2px;font-size:12px;white-space:normal;color:var(--faded-text)}.mention__suggestions__item.selected{background-color:var(--view-header-button-active)!important}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./NotePopup.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.NotePopup .options.modular-ui .option:hover{cursor:pointer;background:var(--primary-button-hover);color:var(--gray-0)}.NotePopup{flex-grow:0;display:flex;justify-content:flex-end;-webkit-user-select:none;-moz-user-select:none;user-select:none;width:28px;height:28px;position:relative}.NotePopup .Button.overflow{width:28px;height:28px;border-radius:4px;display:flex;justify-content:center;align-items:center}.NotePopup .Button.overflow .Icon{width:24px;height:24px}.NotePopup .Button.overflow:hover{background:var(--blue-1)}.NotePopup .Button.overflow.active{background:var(--popup-button-active)}.NotePopup .options{display:flex;flex-direction:column;box-shadow:0 0 3px 0 var(--box-shadow);z-index:80;position:absolute;border-radius:4px;background:var(--component-background);top:40px;width:-moz-max-content;width:max-content}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.NotePopup .options{right:0}}.NotePopup .options .note-popup-option{padding:0;border:none;background-color:transparent;align-items:flex-start}:host(:not([data-tabbing=true])) .NotePopup .options .note-popup-option,html:not([data-tabbing=true]) .NotePopup .options .note-popup-option{outline:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .NotePopup .options .note-popup-option{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .NotePopup .options .note-popup-option{font-size:13px}}.NotePopup .options.options-reply{top:30px}.NotePopup .options .option{display:flex;flex-direction:column;justify-content:center;height:28px;padding-left:8px;padding-right:17px;border-radius:0}.NotePopup .options .option:hover{background-color:var(--popup-button-hover)}.NotePopup .options .option:first-child{border-top-right-radius:4px;border-top-left-radius:4px}.NotePopup .options .option:last-child{border-bottom-right-radius:4px}.NotePopup .Button{height:28px}.NotePopup .Button.active{background:var(--popup-button-active)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .NotePopup .Button.note-popup-toggle-trigger{width:28px;height:28px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .NotePopup .Button.note-popup-toggle-trigger{width:28px;height:28px}}.NotePopupFlyout{min-width:unset!important;max-width:unset!important}.NotePopupFlyout .flyout-item-container{height:unset!important;margin:unset!important}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./NoteHeader.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.NoteHeader{padding-right:12px;position:relative;flex:1;color:var(--text-color);display:flex}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.NoteHeader{flex:none}}.NoteHeader .type-icon{margin:2px;width:24px;height:24px}.NoteHeader .type-icon-container{padding-right:13px}.NoteHeader .type-icon-container .unread-notification{position:absolute;width:13px;height:13px;right:-2px;top:-2px;border-radius:10000px;border:2px solid var(--component-background);background:#00a5e4}.NoteHeader .author-and-date{flex:1;min-width:0;position:relative}.NoteHeader .author-and-date.isReply{padding-left:0;padding-top:0;font-size:10px}.NoteHeader .author-and-date .author-and-overflow{display:flex;justify-content:space-between}.NoteHeader .author-and-date .author-and-overflow .author-and-time{display:flex;flex-direction:column;word-break:break-word}.NoteHeader .author-and-date .author-and-overflow .author-and-time .author{font-weight:700;font-size:13px}.NoteHeader .author-and-date .author-and-overflow .author-and-time .date-and-num-replies{display:flex}.NoteHeader .author-and-date .author-and-overflow .author-and-time .date-and-num-replies .date-and-time{font-size:10px;color:var(--faded-text)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .NoteHeader .author-and-date .author-and-overflow .author-and-time .date-and-num-replies .date-and-time{font-size:10px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .NoteHeader .author-and-date .author-and-overflow .author-and-time .date-and-num-replies .date-and-time{font-size:10px}}.NoteHeader .author-and-date .author-and-overflow .author-and-time .date-and-num-replies .num-replies-container{display:flex;flex-grow:1;padding-left:10px}.NoteHeader .author-and-date .author-and-overflow .author-and-time .date-and-num-replies .num-replies-container .num-reply-icon{height:12px;width:12px}.NoteHeader .author-and-date .author-and-overflow .author-and-time .date-and-num-replies .num-replies-container .num-replies{font-size:10px;color:var(--gray-7)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .NoteHeader .author-and-date .author-and-overflow .author-and-time .date-and-num-replies .num-replies-container .num-replies{font-size:12px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .NoteHeader .author-and-date .author-and-overflow .author-and-time .date-and-num-replies .num-replies-container .num-replies{font-size:12px}}.NoteHeader .author-and-date .author-and-overflow .state-and-overflow{display:flex;flex:1;justify-content:flex-end}.NoteHeader .author-and-date .author-and-overflow .state-and-overflow>*{pointer-events:auto}.NoteHeader .author-and-date .author-and-overflow .state-and-overflow .tracked-change-icon-wrapper .tracked-change-icon{margin:2px;width:24px;height:24px}.NoteHeader .author-and-date .author-and-overflow .state-and-overflow .tracked-change-icon-wrapper:hover.accept{background-color:#d5f5ca}.NoteHeader .author-and-date .author-and-overflow .state-and-overflow .tracked-change-icon-wrapper:hover.reject{background-color:#ffe8e8}.NoteHeader .author-and-date .author-and-overflow .state-and-overflow .ToggleElementButton button{width:28px;height:28px;margin:0 8px 0 13px}.NoteHeader .author-and-date .author-and-overflow .state-and-overflow .ToggleElementButton button .Icon{width:20px;height:20px}.NoteHeader .author-name{font-weight:700}.NoteHeader .note-popup-toggle-trigger{padding:0;margin-right:0!important;margin-left:0!important;min-width:28px!important}.NoteHeader .note-popup-toggle-trigger .Icon{width:24px!important;height:24px!important}.parent{padding-left:12px;padding-top:12px}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./ReplyAttachmentList.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".reply-attachment-list{display:flex;flex-direction:column;cursor:default}.reply-attachment-list .reply-attachment{background-color:var(--gray-1);border-radius:4px;cursor:pointer;pointer-events:auto}.reply-attachment-list .reply-attachment:not(:last-child){margin-bottom:8px}.reply-attachment-list .reply-attachment .reply-attachment-preview{width:100%;max-height:80px;display:flex;justify-content:center}.reply-attachment-list .reply-attachment .reply-attachment-preview.dirty{position:relative;margin-bottom:15px}.reply-attachment-list .reply-attachment .reply-attachment-preview img{max-width:100%;max-height:100%;-o-object-fit:contain;object-fit:contain}.reply-attachment-list .reply-attachment .reply-attachment-preview .reply-attachment-preview-message{font-size:11px;color:var(--error-text-color);position:absolute;bottom:-15px;left:10px}.reply-attachment-list .reply-attachment .reply-attachment-info{display:flex;align-items:center;height:40px;padding:8px}.reply-attachment-list .reply-attachment .reply-attachment-info .reply-attachment-icon{height:24px;min-height:24px;width:24px;min-width:24px}.reply-attachment-list .reply-attachment .reply-attachment-info .reply-file-name{height:16px;width:100%;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;margin-left:8px;margin-right:8px}.reply-attachment-list .reply-attachment .reply-attachment-info .attachment-button{height:24px;min-height:24px;width:24px;min-width:24px}.reply-attachment-list .reply-attachment .reply-attachment-info .attachment-button:hover{background-color:var(--blue-1)}.reply-attachment-list .reply-attachment .reply-attachment-info .attachment-button .Icon{height:16px;width:16px}\", \"\"]);\n\n// exports\n", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./NoteContent.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.NoteContent.modular-ui .edit-content .edit-buttons .save-button.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.NoteContent.modular-ui .edit-content .edit-buttons .save-button.disabled span{color:var(--primary-button-text)}.NoteContent{--note-content-right-padding-width:12px;position:relative;display:flex;flex-direction:column;align-items:left;flex:1;color:var(--text-color);padding-bottom:12px;pointer-events:none}.NoteContent.isReply{padding-bottom:0}.NoteContent.unread.isReply{background:rgba(0,165,228,.08)}.NoteContent.unread.clicked .author-and-time span{font-weight:400}.NoteContent.unread .author-and-time span{font-weight:700}.NoteContent .container{padding-left:52px;padding-right:var(--note-content-right-padding-width)}.NoteContent .container,.NoteContent .container-reply{margin-top:8px;overflow:hidden;white-space:pre-wrap;word-wrap:break-word;-webkit-user-select:text;-moz-user-select:text;user-select:text;cursor:text}.NoteContent .edit-content{margin-top:7px;display:flex;flex-direction:column;position:relative;flex:1;padding-left:52px;padding-right:12px;padding-bottom:12px;pointer-events:auto}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.NoteContent .edit-content{flex:none}}.NoteContent .edit-content textarea{width:100%;padding-left:8px;border:1px solid var(--border);border-radius:4px;color:var(--text-color);padding-top:4px;padding-bottom:4px;resize:none;overflow:hidden;box-sizing:border-box}.NoteContent .edit-content textarea:focus{outline:none;border:1px solid var(--focus-border)}.NoteContent .edit-content textarea::-moz-placeholder{color:var(--placeholder-text)}.NoteContent .edit-content textarea::placeholder{color:var(--placeholder-text)}.NoteContent .edit-content .edit-buttons{display:flex;flex-direction:row;justify-content:flex-end;margin-top:8px}.NoteContent .edit-content .edit-buttons>div{margin-right:4px}.NoteContent .edit-content .edit-buttons .save-button{background-color:transparent;cursor:pointer;flex-shrink:0;background:var(--primary-button);border-radius:4px;width:-moz-fit-content;width:fit-content;border:none;height:28px;display:flex;align-items:center;justify-content:center;position:relative;color:var(--primary-button-text);padding:0 13px}:host(:not([data-tabbing=true])) .NoteContent .edit-content .edit-buttons .save-button,html:not([data-tabbing=true]) .NoteContent .edit-content .edit-buttons .save-button{outline:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .NoteContent .edit-content .edit-buttons .save-button{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .NoteContent .edit-content .edit-buttons .save-button{font-size:13px}}.NoteContent .edit-content .edit-buttons .save-button:hover{background:var(--primary-button-hover);color:var(--primary-button-text)}.NoteContent .edit-content .edit-buttons .save-button.disabled{background:var(--gray-6)!important;border-color:var(--gray-6)!important;cursor:not-allowed}.NoteContent .edit-content .edit-buttons .save-button.disabled span{color:var(--primary-button-text)}.NoteContent .edit-content .edit-buttons .cancel-button{border:none;background-color:transparent;color:var(--secondary-button-text);padding:0 10px;width:-moz-fit-content;width:fit-content;height:28px;display:flex;align-items:center;justify-content:center;cursor:pointer;margin-right:2px}:host(:not([data-tabbing=true])) .NoteContent .edit-content .edit-buttons .cancel-button,html:not([data-tabbing=true]) .NoteContent .edit-content .edit-buttons .cancel-button{outline:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .NoteContent .edit-content .edit-buttons .cancel-button{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .NoteContent .edit-content .edit-buttons .cancel-button{font-size:13px}}.NoteContent .edit-content .edit-buttons .cancel-button:hover{color:var(--secondary-button-hover)}.NoteContent .reply-content{padding-left:0}.NoteContent .contents{white-space:pre-wrap;color:var(--text-color);margin-right:5px;padding:0;word-break:normal;word-wrap:break-word;-webkit-user-select:text;-moz-user-select:text;user-select:text}.NoteContent .contents .highlight{background:#fffc95;color:#333}.NoteContent .highlight{background:#fffc95}.NoteContent .selected-text-preview{padding-left:52px;padding-top:8px}.NoteContent .reply-attachment-list{margin-bottom:8px}.NoteContent.modular-ui .highlight{font-weight:700;color:var(--blue-5);background:none}.NoteContent.modular-ui .edit-content .edit-buttons .save-button.disabled{border:none}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../../node_modules/css-loader/index.js!../../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../../node_modules/sass-loader/dist/cjs.js!./ReplyArea.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.reply-area-container{border-top:1px solid var(--divider);display:flex;flex-direction:column;margin-bottom:0}.reply-area-container .reply-attachment-list{margin:12px 12px 0}.reply-area-container .reply-area-with-button{display:flex}.reply-area-container .reply-area-with-button .reply-area{position:relative;flex:1;margin:12px 17px 12px 12px;border-radius:4px;align-items:center}.reply-area-container .reply-area-with-button .reply-area.unread{background:rgba(0,165,228,.08)}.reply-area-container .reply-area-with-button .reply-area .comment-textarea .ql-container .ql-editor.ql-blank{padding:4px}.reply-area-container .reply-area-with-button .reply-area .comment-textarea .ql-container .ql-editor.ql-blank:before{left:4px}.reply-area-container .reply-area-with-button .reply-button-container{display:flex;flex-direction:column;justify-content:flex-end}.reply-area-container .reply-area-with-button .reply-button-container .reply-button{width:28px;height:28px;padding:0;border:none;background-color:transparent;right:10px;bottom:12px}:host(:not([data-tabbing=true])) .reply-area-container .reply-area-with-button .reply-button-container .reply-button,html:not([data-tabbing=true]) .reply-area-container .reply-area-with-button .reply-button-container .reply-button{outline:none}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.reply-area-container .reply-area-with-button .reply-button-container .reply-button{width:80px}}.reply-area-container .reply-area-with-button .reply-button-container .reply-button:hover{background:var(--blue-1)}.reply-area-container .reply-area-with-button .reply-button-container .reply-button.disabled{cursor:not-allowed}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./AnnotationNoteConnectorLine.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}@keyframes bottom-up{0%{transform:translateY(100%)}to{transform:translateY(0)}}@keyframes up-bottom{0%{transform:translateY(0)}to{transform:translateY(100%)}}@media print{#line-connector-root{opacity:0}}#line-connector-root{position:relative;z-index:69}.horizontalLine{height:2px}.horizontalLine,.verticalLine{background-color:rgba(30,120,235,.5);position:fixed}.verticalLine{width:2px}.arrowHead{position:absolute;top:0;left:0;margin:auto;width:0;height:0;border-top:6px solid transparent;border-bottom:6px solid transparent;border-right:7px solid rgba(30,120,235,.5);transform:translateX(-100%) translateY(-50%) translateY(1px)}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./Note.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.Note{padding:0;border:none;background-color:transparent;display:block;text-align:left;border-radius:4px;box-shadow:0 0 3px 0 var(--note-box-shadow);margin-bottom:8px;margin-left:2px;background:var(--component-background);cursor:pointer;position:relative}:host(:not([data-tabbing=true])) .Note,html:not([data-tabbing=true]) .Note{outline:none}.Note.unread{border:1.2px solid #00a5e4}.Note.expanded{box-shadow:0 4px 16px var(--note-box-shadow-expanded),0 0 4px 0 var(--note-box-shadow)}.Note.is-multi-selected{box-shadow:0 4px 16px rgba(134,142,150,.24),0 0 4px 0 var(--note-box-shadow)}.Note.disabled{opacity:.5;pointer-events:none}.Note .note-button{position:absolute;width:100%;height:100%;top:0;left:0}.Note .mark-all-read-button{background:#00a5e4;text-align:center;color:#fff;height:16px;font-size:12px;width:100%;border-radius:0}.Note .divider{height:1px;width:100%;background:var(--divider)}.Note .reply-divider{background:var(--reply-divider);height:1px;width:100%}.Note .replies{margin-left:52px;padding-bottom:12px}.Note .reply{padding-left:12px;padding-bottom:24px;border-left:1px solid var(--reply-divider)}.Note .reply:last-of-type{padding-bottom:0}.Note .group-section{margin-left:52px;padding-bottom:12px;display:flex;flex-direction:column;grid-row-gap:4px;row-gap:4px;padding-right:12px}.Note .group-section.modular-ui .group-child:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);background:var(--faded-component-background);border-radius:4px}.Note .text-button{color:var(--secondary-button-text);display:flex;position:relative;width:auto;height:auto;flex-direction:row-reverse;justify-content:flex-end}.Note .text-button .Icon{color:var(--secondary-button-text);height:18px;width:18px}.Note .group-child{position:relative;width:auto;height:auto;display:block;text-align:left;padding-top:4px;padding-bottom:4px}.Note .group-child:hover{background:var(--view-header-button-hover)}.Note .group-child .NoteContent{padding-bottom:0}.Note:focus{outline:none}.Note.focus-visible,.Note:focus-visible{outline:var(--focus-visible-outline)}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React from 'react';\nimport { useSelector } from 'react-redux';\nimport selectors from 'selectors';\n\nconst ReplyAttachmentPicker = ({ annotationId, addAttachments }) => {\n  const replyAttachmentHandler = useSelector((state) => selectors.getReplyAttachmentHandler(state));\n\n  const onChange = async (e) => {\n    const file = e.target.files[0];\n    if (file) {\n      let attachment = file;\n      if (replyAttachmentHandler) {\n        const url = await replyAttachmentHandler(file);\n        attachment = {\n          url,\n          name: file.name,\n          size: file.size,\n          type: file.type\n        };\n      }\n      addAttachments(annotationId, [attachment]);\n    }\n  };\n\n  return (\n    <input\n      id=\"reply-attachment-picker\"\n      type=\"file\"\n      style={{ display: 'none' }}\n      onChange={onChange}\n      onClick={(e) => {\n        e.target.value = '';\n      }}\n    />\n  );\n};\n\nexport default ReplyAttachmentPicker;\n", "import React, { useLayoutEffect } from 'react';\nimport PropTypes from 'prop-types';\nimport { useTranslation } from 'react-i18next';\nimport classNames from 'classnames';\nimport selectors from 'selectors';\nimport DataElements from 'src/constants/dataElement';\nimport actions from 'actions';\nimport './NotePopup.scss';\nimport { useDispatch, useSelector } from 'react-redux';\nimport ToggleElementButton from 'components/ModularComponents/ToggleElementButton';\n\nconst createFlyoutItem = (option, icon, dataElement) => ({\n  icon,\n  label: `action.${option.toLowerCase()}`,\n  title: `action.${option.toLowerCase()}`,\n  option,\n  dataElement,\n});\n\nexport const notePopupFlyoutItems = [\n  createFlyoutItem('Edit', '', 'notePopupEdit'),\n  createFlyoutItem('Delete', '', 'notePopupDelete'),\n];\n\nconst propTypes = {\n  handleEdit: PropTypes.func,\n  handleDelete: PropTypes.func,\n  isEditable: PropTypes.bool,\n  isDeletable: PropTypes.bool,\n  noteId: PropTypes.string,\n};\n\nfunction noop() { }\n\nfunction NotePopup(props) {\n  const {\n    handleEdit = noop,\n    handleDelete = noop,\n    isEditable,\n    isDeletable,\n    isReply,\n    noteId,\n  } = props;\n\n  const customizableUI = useSelector((state) => selectors.getFeatureFlags(state)?.customizableUI);\n  const flyoutSelector = `${DataElements.NOTE_POPUP_FLYOUT}-${noteId}`;\n  const [t] = useTranslation();\n\n  const handleClick = (selection) => {\n    if (selection === 'Edit') {\n      handleEdit();\n    } else if (selection === 'Delete') {\n      handleDelete();\n    }\n  };\n\n  if (!isEditable && !isDeletable) {\n    return null;\n  }\n\n  const notePopupButtonClass = classNames('overflow note-popup-toggle-trigger');\n  const optionsClass = classNames('NotePopup options note-popup-options', { 'options-reply': isReply, 'modular-ui': customizableUI });\n  return (\n    <div className={optionsClass}>\n      <ToggleElementButton\n        dataElement={`notePopup-${noteId}`}\n        className={notePopupButtonClass}\n        img=\"icon-tools-more\"\n        title={t('formField.formFieldPopup.options')}\n        toggleElement={flyoutSelector}\n        disabled={false}\n      />\n      <NotePopupFlyout\n        flyoutSelector={flyoutSelector}\n        handleClick={handleClick}\n        isEditable={isEditable}\n        isDeletable={isDeletable}\n      />\n    </div>\n  );\n}\n\n\nconst NotePopupFlyout = ({\n  flyoutSelector,\n  handleClick,\n  isEditable,\n  isDeletable,\n}) => {\n  const dispatch = useDispatch();\n  const currentFlyout = useSelector((state) => selectors.getFlyout(state, flyoutSelector));\n  const [t] = useTranslation();\n\n  useLayoutEffect(() => {\n    let items = notePopupFlyoutItems;\n    if (!isEditable) {\n      items = items.filter((item) => item.option !== 'Edit');\n    } else if (!isDeletable) {\n      items = items.filter((item) => item.option !== 'Delete');\n    }\n\n    const notePopupFlyout = {\n      dataElement: flyoutSelector,\n      className: 'NotePopupFlyout',\n      items: items.map((item) => {\n        return {\n          ...item,\n          label: t(item.label),\n          title: t(item.title),\n          onClick: () => handleClick(item.option),\n        };\n      }),\n    };\n\n    if (!currentFlyout) {\n      dispatch(actions.addFlyout(notePopupFlyout));\n    } else {\n      dispatch(actions.updateFlyout(notePopupFlyout.dataElement, notePopupFlyout));\n    }\n  }, [isEditable, isDeletable]);\n\n  return null;\n};\n\nNotePopupFlyout.propTypes = {\n  flyoutSelector: PropTypes.string,\n  handleClick: PropTypes.func,\n  isEditable: PropTypes.bool,\n  isDeletable: PropTypes.bool,\n};\n\nNotePopup.propTypes = propTypes;\n\nexport default NotePopup;\n", "import React from 'react';\nimport core from 'core';\nimport NotePopup from './NotePopup';\nimport { useSelector } from 'react-redux';\nimport selectors from 'selectors';\n\nfunction NotePopupContainer(props) {\n  const [\n    activeDocumentViewerKey,\n  ] = useSelector((state) => [\n    selectors.getActiveDocumentViewerKey(state),\n  ]);\n  const { annotation, setIsEditing, noteIndex } = props;\n\n  const [canModify, setCanModify] = React.useState(core.canModify(annotation));\n  const [canModifyContents, setCanModifyContents] = React.useState(core.canModifyContents(annotation));\n\n  React.useEffect(() => {\n    function onUpdateAnnotationPermission() {\n      setCanModify(core.canModify(annotation, activeDocumentViewerKey));\n      setCanModifyContents(core.canModifyContents(annotation, activeDocumentViewerKey));\n    }\n\n    onUpdateAnnotationPermission();\n    core.addEventListener('updateAnnotationPermission', onUpdateAnnotationPermission, undefined, activeDocumentViewerKey);\n    return () => core.removeEventListener('updateAnnotationPermission', onUpdateAnnotationPermission, activeDocumentViewerKey);\n  }, [annotation, activeDocumentViewerKey]);\n\n  const handleEdit = React.useCallback(() => {\n    const isFreeText = annotation instanceof window.Core.Annotations.FreeTextAnnotation;\n    if (isFreeText && core.getAnnotationManager(activeDocumentViewerKey).isFreeTextEditingEnabled()) {\n      core.getAnnotationManager(activeDocumentViewerKey).trigger('annotationDoubleClicked', annotation);\n    } else {\n      setIsEditing(true, noteIndex);\n    }\n  }, [annotation, setIsEditing, noteIndex]);\n\n  const handleDelete = React.useCallback(() => {\n    core.deleteAnnotations([annotation, ...annotation.getGroupedChildren()], undefined, activeDocumentViewerKey);\n  }, [annotation]);\n\n  const isEditable = canModifyContents;\n  const isDeletable = canModify && !annotation?.NoDelete;\n  const noteId = (annotation) ? annotation.Id : '';\n\n  const passProps = {\n    handleEdit,\n    handleDelete,\n    isEditable,\n    isDeletable,\n    noteId,\n  };\n\n  return (\n    <NotePopup {...props} {...passProps} />\n  );\n}\n\nexport default NotePopupContainer;\n", "import NotePopupContainer from './NotePopupContainer';\n\nexport default NotePopupContainer;\n", "import React from 'react';\nimport PropTypes from 'prop-types';\nimport { useTranslation } from 'react-i18next';\nimport NoteStateFlyout from 'components/ModularComponents/NoteStateFlyout';\nimport ToggleElementButton from 'components/ModularComponents/ToggleElementButton';\nimport DataElements from 'constants/dataElement';\n\nconst propTypes = {\n  annotation: PropTypes.object.isRequired,\n  handleStateChange: PropTypes.func\n};\n\nfunction NoteState(props) {\n  const {\n    annotation,\n    handleStateChange = () => { },\n  } = props;\n\n  const [t] = useTranslation();\n\n  const annotationState = annotation.getStatus();\n  const icon = `icon-annotation-status-${annotationState === '' ? 'none' : annotationState.toLowerCase()}`;\n  const id = annotation.Id;\n\n  return (\n    <>\n      <ToggleElementButton\n        dataElement={`noteState-${id}`}\n        title={t('option.notesOrder.status')}\n        img={icon}\n        toggleElement={`${DataElements.NOTE_STATE_FLYOUT}-${id}`}\n      />\n      <NoteStateFlyout\n        noteId={id}\n        handleStateChange={handleStateChange}\n      />\n    </>\n  );\n}\n\nNoteState.propTypes = propTypes;\n\nexport default NoteState;", "import React, { useCallback } from 'react';\nimport PropTypes from 'prop-types';\nimport core from 'core';\n\nimport NoteState from './NoteState';\nimport { createStateAnnotation } from 'helpers/NoteStateUtils';\nimport { useSelector } from 'react-redux';\nimport selectors from 'selectors';\nimport useFocusOnClose from 'hooks/useFocusOnClose';\n\nconst propTypes = {\n  annotation: PropTypes.object,\n};\n\nfunction NoteStateContainer(props) {\n  const activeDocumentViewerKey = useSelector((state) => selectors.getActiveDocumentViewerKey(state));\n  const isNoteStateDisabled = useSelector((state) => selectors.isElementDisabled(state, 'noteState'));\n\n  const { annotation } = props;\n\n  const handleStateChange = useFocusOnClose(useCallback(function handleStateChangeCallback(newValue) {\n    const stateAnnotation = createStateAnnotation(annotation, newValue, activeDocumentViewerKey);\n    annotation.addReply(stateAnnotation);\n    const annotationManager = core.getAnnotationManager(activeDocumentViewerKey);\n    annotationManager.addAnnotation(stateAnnotation);\n    annotationManager.trigger('addReply', [stateAnnotation, annotation, annotationManager.getRootAnnotation(annotation)]);\n  }, [annotation, activeDocumentViewerKey]));\n\n  return (!isNoteStateDisabled &&\n    <div>\n      <NoteState handleStateChange={handleStateChange} {...props} />\n    </div>\n  );\n}\n\nNoteStateContainer.propTypes = propTypes;\nexport default NoteStateContainer;\n", "import NoteStateContainer from './NoteStateContainer';\n\nexport default NoteStateContainer;\n", "import React, { useState, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport Tooltip from 'components/Tooltip';\nimport Icon from 'components/Icon';\nimport PropTypes from 'prop-types';\n\nconst propTypes = {\n  annotationId: PropTypes.string,\n  ariaLabel: PropTypes.string,\n  pendingEditTextMap: PropTypes.object,\n  pendingReplyMap: PropTypes.object,\n  pendingAttachmentMap: PropTypes.object,\n};\n\nconst NoteUnpostedCommentIndicator = ({ annotationId, ariaLabel, pendingEditTextMap, pendingReplyMap, pendingAttachmentMap }) => {\n  const { t } = useTranslation();\n  const [hasUnpostedComment, setHasUnpostedComment] = useState(false);\n  const [hasUnpostedReply, setHasUnpostedReply] = useState(false);\n  const [hasUnpostedAttachment, setHasUnpostedAttachment] = useState(false);\n\n  useEffect(() => {\n    setHasUnpostedComment(pendingEditTextMap[annotationId]?.length > 0);\n    setHasUnpostedReply(pendingReplyMap[annotationId]?.length > 0);\n    setHasUnpostedAttachment(pendingAttachmentMap[annotationId]?.length > 0);\n  }, [pendingEditTextMap, pendingReplyMap, pendingAttachmentMap]);\n\n  return (\n    (hasUnpostedComment || hasUnpostedReply || hasUnpostedAttachment) ?\n      <div data-element=\"unpostedCommentIndicator\">\n        <Tooltip content={t('message.unpostedComment')}>\n          <div>\n            <Icon className=\"type-icon\" glyph={'icon-unposted-comment'} ariaLabel={ariaLabel}/>\n          </div>\n        </Tooltip>\n      </div> :\n      null\n  );\n};\n\nNoteUnpostedCommentIndicator.propTypes = propTypes;\n\nexport default NoteUnpostedCommentIndicator;\n", "import React from 'react';\nimport { useSelector } from 'react-redux';\nimport selectors from 'selectors';\nimport NoteContext from 'components/Note/Context';\nimport NoteUnpostedCommentIndicator from './NoteUnpostedCommentIndicator';\nimport PropTypes from 'prop-types';\n\nconst propTypes = {\n  annotationId: PropTypes.string,\n  ariaLabel: PropTypes.string,\n};\n\nconst NoteUnpostedCommentIndicatorContainer = ({ annotationId, ariaLabel }) => {\n  const isDisabled = useSelector((state) => selectors.isElementDisabled(state, 'unpostedCommentIndicator'));\n  const { pendingEditTextMap, pendingReplyMap, pendingAttachmentMap } = React.useContext(NoteContext);\n\n  if (isDisabled) {\n    return null;\n  }\n  return (\n    <NoteUnpostedCommentIndicator\n      annotationId={annotationId}\n      ariaLabel={ariaLabel}\n      pendingEditTextMap={pendingEditTextMap}\n      pendingReplyMap={pendingReplyMap}\n      pendingAttachmentMap={pendingAttachmentMap}\n    />);\n};\n\nNoteUnpostedCommentIndicatorContainer.propTypes = propTypes;\n\nexport default NoteUnpostedCommentIndicatorContainer;", "import NoteUnpostedCommentIndicatorContainer from './NoteUnpostedCommentIndicatorContainer';\n\nexport default NoteUnpostedCommentIndicatorContainer;", "import React from 'react';\nimport PropTypes from 'prop-types';\n\nimport NotePopup from 'components/NotePopup';\nimport NoteState from 'components/NoteState';\nimport Icon from 'components/Icon';\nimport NoteUnpostedCommentIndicator from 'components/NoteUnpostedCommentIndicator';\nimport Choice from 'components/Choice';\nimport Button from 'components/Button';\n\nimport getLatestActivityDate from 'helpers/getLatestActivityDate';\nimport getColor from 'helpers/getColor';\nimport { isDarkColorHex, isLightColorHex } from 'helpers/color';\nimport dayjs from 'dayjs';\nimport classNames from 'classnames';\nimport { useTranslation } from 'react-i18next';\nimport core from 'core';\nimport { NotesPanelSortStrategy } from 'constants/sortStrategies';\nimport Theme from 'constants/theme';\nimport { OFFICE_EDITOR_TRACKED_CHANGE_KEY } from 'constants/officeEditor';\nimport { COMMON_COLORS } from 'constants/commonColors';\n\nimport './NoteHeader.scss';\n\nconst propTypes = {\n  icon: PropTypes.string,\n  iconColor: PropTypes.string,\n  color: PropTypes.string,\n  fillColor: PropTypes.string,\n  annotation: PropTypes.object,\n  language: PropTypes.string,\n  noteDateFormat: PropTypes.string,\n  isSelected: PropTypes.bool,\n  setIsEditing: PropTypes.func,\n  notesShowLastUpdatedDate: PropTypes.bool,\n  isUnread: PropTypes.bool,\n  renderAuthorName: PropTypes.func,\n  isNoteStateDisabled: PropTypes.bool,\n  isEditing: PropTypes.bool,\n  noteIndex: PropTypes.number,\n  sortStrategy: PropTypes.string,\n  activeTheme: PropTypes.string,\n  isMultiSelected: PropTypes.bool,\n  isMultiSelectMode: PropTypes.bool,\n  handleMultiSelect: PropTypes.func,\n  isGroupMember: PropTypes.bool,\n  showAnnotationNumbering: PropTypes.bool,\n  isTrackedChange: PropTypes.bool,\n};\n\nfunction NoteHeader(props) {\n  const {\n    icon,\n    iconColor,\n    annotation,\n    language,\n    noteDateFormat,\n    isSelected,\n    setIsEditing,\n    notesShowLastUpdatedDate,\n    isReply,\n    isUnread,\n    renderAuthorName,\n    isNoteStateDisabled,\n    isEditing,\n    noteIndex,\n    sortStrategy,\n    activeTheme,\n    isMultiSelected,\n    isMultiSelectMode,\n    handleMultiSelect,\n    isGroupMember,\n    showAnnotationNumbering,\n    timezone,\n    isTrackedChange,\n  } = props;\n\n  const [t] = useTranslation();\n\n  let date;\n  const dateCreated = (sortStrategy === NotesPanelSortStrategy.MODIFIED_DATE || (notesShowLastUpdatedDate && sortStrategy !== NotesPanelSortStrategy.CREATED_DATE)) ? getLatestActivityDate(annotation) : annotation.DateCreated;\n  if (timezone && dateCreated) {\n    const datetimeStr = dateCreated.toLocaleString('en-US', { timeZone: timezone });\n    date = new Date(datetimeStr);\n  } else {\n    date = dateCreated;\n  }\n\n  const noteDateAndTime = date ? dayjs(date).locale(language).format(noteDateFormat) : t('option.notesPanel.noteContent.noDate');\n\n  const numberOfReplies = annotation.getReplies().length;\n  let color = annotation[iconColor]?.toHexString?.();\n\n  if (activeTheme === Theme.DARK && color && isDarkColorHex(color)) {\n    color = COMMON_COLORS['white'];\n  } else if (activeTheme === Theme.LIGHT && color && isLightColorHex(color)) {\n    color = COMMON_COLORS['black'];\n  }\n\n  const fillColor = getColor(annotation.FillColor);\n  const annotationAssociatedNumber = annotation.getAssociatedNumber();\n  const annotationDisplayedAssociatedNumber = `#${annotationAssociatedNumber} - `;\n\n  const authorAndDateClass = classNames('author-and-date', { isReply });\n  const noteHeaderClass = classNames('NoteHeader', { parent: !isReply && !isGroupMember });\n\n  const acceptTrackedChange = (trackedChangeAnnot) => {\n    const trackedChangeId = trackedChangeAnnot.getCustomData(OFFICE_EDITOR_TRACKED_CHANGE_KEY);\n    core.getOfficeEditor().acceptTrackedChange(trackedChangeId);\n  };\n  const rejectTrackedChange = (trackedChangeAnnot) => {\n    const trackedChangeId = trackedChangeAnnot.getCustomData(OFFICE_EDITOR_TRACKED_CHANGE_KEY);\n    core.getOfficeEditor().rejectTrackedChange(trackedChangeId);\n  };\n\n  return (\n    <div className={noteHeaderClass}>\n      {!isReply &&\n        <div className=\"type-icon-container\">\n          {isUnread &&\n            <div className=\"unread-notification\"></div>\n          }\n          <Icon className=\"type-icon\" glyph={icon} color={color} fillColor={fillColor} />\n        </div>\n      }\n      <div className={authorAndDateClass}>\n        <div className=\"author-and-overflow\">\n          <div className=\"author-and-time\">\n            <div className=\"author\">\n              {showAnnotationNumbering && annotationAssociatedNumber !== undefined &&\n                <span className=\"annotation-number\">{annotationDisplayedAssociatedNumber}</span>\n              }\n              {renderAuthorName(annotation)}\n            </div>\n            <div className=\"date-and-num-replies\">\n              <div className=\"date-and-time\">\n                {noteDateAndTime}\n                {isGroupMember && ` (Page ${annotation.PageNumber})`}\n              </div>\n              {numberOfReplies > 0 && !isSelected &&\n                <div className=\"num-replies-container\">\n                  <Icon className=\"num-reply-icon\" glyph='icon-chat-bubble' />\n                  <div className=\"num-replies\">{numberOfReplies}</div>\n                </div>}\n            </div>\n          </div>\n          <div className=\"state-and-overflow\">\n            {isMultiSelectMode && !isGroupMember && !isReply &&\n              <Choice\n                id={`note-multi-select-toggle_${annotation.Id}`}\n                aria-label={`${renderAuthorName(annotation)} ${t('option.notesPanel.toggleMultiSelect')}`}\n                checked={isMultiSelected}\n                onClick={(e) => {\n                  e.preventDefault();\n                  e.stopPropagation();\n                  handleMultiSelect(!isMultiSelected);\n                }}\n              />\n            }\n            <NoteUnpostedCommentIndicator\n              annotationId={annotation.Id}\n              ariaLabel={`Unposted Comment, ${renderAuthorName(annotation)}, ${noteDateAndTime}`}\n            />\n            {!isNoteStateDisabled && !isReply && !isMultiSelectMode && !isGroupMember && !isTrackedChange &&\n              <NoteState\n                annotation={annotation}\n                isSelected={isSelected}\n              />\n            }\n            {!isEditing && isSelected && !isMultiSelectMode && !isGroupMember && !isTrackedChange &&\n              <NotePopup\n                noteIndex={noteIndex}\n                annotation={annotation}\n                setIsEditing={setIsEditing}\n                isReply={isReply}\n              />\n            }\n            {isSelected && isTrackedChange && !isMultiSelectMode &&\n              <>\n                <Button\n                  title={t('officeEditor.accept')}\n                  img={'icon-menu-checkmark'}\n                  className=\"tracked-change-icon-wrapper accept\"\n                  onClick={() => acceptTrackedChange(annotation)}\n                  iconClassName=\"tracked-change-icon\"\n                />\n                <Button\n                  title={t('officeEditor.reject')}\n                  img={'icon-close'}\n                  className=\"tracked-change-icon-wrapper reject\"\n                  onClick={() => rejectTrackedChange(annotation)}\n                  iconClassName=\"tracked-change-icon\"\n                />\n              </>\n            }\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nNoteHeader.propTypes = propTypes;\n\nexport default NoteHeader;\n", "import NoteHeader from './NoteHeader';\n\nexport default NoteHeader;", "import React from 'react';\nimport { useSelector, shallowEqual } from 'react-redux';\nimport selectors from 'selectors';\nimport NoteTextPreview from './NoteTextPreview';\n\nfunction NoteTextPreviewContainer(props) {\n  const [notePanelWidth] = useSelector(\n    (state) => [\n      selectors.getNotesPanelWidth(state),\n    ],\n    shallowEqual,\n  );\n\n  return (\n    <NoteTextPreview {...props} panelWidth={notePanelWidth} />\n  );\n}\n\nexport default NoteTextPreviewContainer;\n", "import NoteTextPreviewContainer from './NoteTextPreviewContainer';\n\nexport default NoteTextPreviewContainer;", "const icons = {\n  pdf: 'ic-file-pdf',\n  image: 'ic-file-img',\n  cad: 'ic-file-cad',\n  doc: 'ic-file-doc',\n  ppt: 'ic-file-ppt',\n  xls: 'ic-file-xls',\n  unknown: 'ic-file-etc'\n};\n\nconst FileAttachmentUtils = window.Core.Annotations.FileAttachmentUtils;\n\nexport async function decompressFileContent(file) {\n  return FileAttachmentUtils.decompressWithFlateDecode(file.content, file.type);\n}\n\nexport async function setAnnotationAttachments(annotation, files = []) {\n  await annotation.setAttachments(files);\n}\n\nexport function isImage(file) {\n  if (file.type && file.type.startsWith('image/')) {\n    return true;\n  }\n  return false;\n}\n\nexport function getAttachmentIcon(file) {\n  if (isImage(file)) {\n    return icons.image;\n  }\n  const extension = file.name?.split('.').pop().toLowerCase();\n  switch (extension) {\n    case 'pdf':\n      return icons.pdf;\n    case 'cad':\n      return icons.cad;\n    case 'doc':\n    case 'docx':\n      return icons.doc;\n    case 'ppt':\n    case 'pptx':\n      return icons.ppt;\n    case 'xls':\n    case 'xlsx':\n      return icons.xls;\n    default:\n      return icons.unknown;\n  }\n}\n", "import DOMPurify from 'dompurify';\n\nconst SVG_MIME_TYPE = 'image/svg+xml';\n\nconst hasFileSize = (file) => {\n  return file.size !== undefined;\n};\n\n// Taken from https://github.com/mattkrick/sanitize-svg/blob/master/src/sanitizeSVG.ts#L31\nconst readAsText = (svg) => {\n  return new Promise((resolve) => {\n    if (!hasFileSize(svg)) {\n      resolve(svg.toString('utf-8'));\n    } else {\n      const fileReader = new FileReader();\n      fileReader.onload = () => resolve(fileReader.result);\n      fileReader.readAsText(svg);\n    }\n  });\n};\n\nexport const isSVG = (file) => {\n  return file.type === SVG_MIME_TYPE;\n};\n\nexport const sanitizeSVG = async (file) => {\n  const svgText = await readAsText(file);\n  if (!svgText) {\n    return { svg: file };\n  }\n\n  const forbiddenTags = [];\n  DOMPurify.addHook('uponSanitizeElement', (_, hookEvent) => {\n    const { tagName, allowedTags } = hookEvent;\n    if (!allowedTags[tagName]) {\n      forbiddenTags.push(tagName);\n    }\n  });\n\n  const clean = DOMPurify.sanitize(svgText);\n  const svg = new Blob([clean], { type: SVG_MIME_TYPE });\n  return { svg, isDirty: forbiddenTags.length > 0 };\n};", "import React, { useState, useEffect } from 'react';\nimport classNames from 'classnames';\nimport { useTranslation } from 'react-i18next';\nimport { useSelector } from 'react-redux';\nimport selectors from 'selectors';\nimport { saveAs } from 'file-saver';\nimport Button from 'components/Button';\nimport Icon from 'components/Icon';\nimport Tooltip from 'components/Tooltip';\nimport { getAttachmentIcon, isImage, decompressFileContent } from 'helpers/ReplyAttachmentManager';\nimport { isSVG, sanitizeSVG } from 'helpers/sanitizeSVG';\n\nimport './ReplyAttachmentList.scss';\n\nconst ImagePreview = ({ file }) => {\n  const [t] = useTranslation();\n\n  const [src, setSrc] = useState();\n  const [isDirtySVG, setIsDirtySvg] = useState(false);\n\n  useEffect(() => {\n    const processImagePreview = async () => {\n      setIsDirtySvg(false);\n      let fileToSanitize = file;\n\n      const isImageFromPDF = !(file instanceof File) && !file.url;\n      if (isImageFromPDF) {\n        fileToSanitize = await decompressFileContent(file);\n      }\n\n      if (file instanceof File || isImageFromPDF) {\n        if (isSVG(file)) {\n          const { svg, isDirty } = await sanitizeSVG(fileToSanitize);\n          setSrc(URL.createObjectURL(svg));\n          setIsDirtySvg(isDirty);\n        } else {\n          setSrc(URL.createObjectURL(fileToSanitize));\n        }\n      }\n    };\n    processImagePreview();\n  }, [file]);\n\n  return (\n    <div className={classNames({\n      'reply-attachment-preview': true,\n      'dirty': isDirtySVG,\n    })}>\n      <img src={src} />\n      {isDirtySVG && <span className=\"reply-attachment-preview-message\">{t('message.svgMalicious')}</span>}\n    </div>\n  );\n};\n\nconst ReplyAttachmentList = ({ files, isEditing, fileDeleted }) => {\n  const [tabManager, previewEnabled] = useSelector((state) => [\n    selectors.getTabManager(state),\n    selectors.isReplyAttachmentPreviewEnabled(state)\n  ]);\n  const [t] = useTranslation();\n\n  const onClick = async (e, file) => {\n    e.preventDefault();\n    e.stopPropagation();\n\n    if (!tabManager) {\n      return console.warn('Can\\'t open attachment in non-multi-tab mode');\n    }\n\n    let fileData;\n    if (file instanceof File) {\n      fileData = file;\n    } else if (file.url) {\n      fileData = file.url;\n    } else {\n      fileData = await decompressFileContent(file);\n    }\n\n    fileData && tabManager.addTab(fileData, {\n      filename: file.name,\n      setActive: true,\n      saveCurrentActiveTabState: true\n    });\n  };\n\n  const onDelete = (e, file) => {\n    e.preventDefault();\n    e.stopPropagation();\n\n    fileDeleted(file);\n  };\n\n  const onDownload = async (e, file) => {\n    e.preventDefault();\n    e.stopPropagation();\n\n    const fileData = file.url ? file.url : await decompressFileContent(file);\n    saveAs(fileData, file.name);\n  };\n\n  return (\n    <div className=\"reply-attachment-list\">\n      {files.map((file, i) => (\n        <div\n          className=\"reply-attachment\"\n          key={i}\n          onClick={(e) => onClick(e, file)}\n        >\n          {previewEnabled && isImage(file) && (\n            <ImagePreview file={file} />\n          )}\n          <div className=\"reply-attachment-info\">\n            <Icon\n              className=\"reply-attachment-icon\"\n              glyph={getAttachmentIcon(file)}\n            />\n            <Tooltip content={file.name}>\n              <div className=\"reply-file-name\">{file.name}</div>\n            </Tooltip>\n            {isEditing ? (\n              <Button\n                className=\"attachment-button\"\n                title={`${t('action.delete')} ${t('option.type.fileattachment')}`}\n                img='icon-close'\n                onClick={(e) => onDelete(e, file)}\n              />\n            ) : (\n              <Button\n                className=\"attachment-button\"\n                title={`${t('action.download')} ${t('option.type.fileattachment')}`}\n                img='icon-download'\n                onClick={(e) => onDownload(e, file)}\n              />\n            )}\n          </div>\n        </div>\n      ))}\n    </div>\n  );\n};\n\nexport default ReplyAttachmentList;\n", "import ReplyAttachmentList from './ReplyAttachmentList';\n\nexport default ReplyAttachmentList;", "import mentionsManager from './MentionsManager';\n/*\n * Transforming RichText Style object into the Object acceptable by React Quill component.\n */\n\nconst setReactQuillContent = (annotation, editor) => {\n  const richTextStyle = annotation.getRichTextStyle();\n  const indexes = Object.keys(richTextStyle);\n  const formattedText = mentionsManager.getFormattedTextFromDeltas(editor.getContents());\n  const text = mentionsManager.extractMentionDataFromStr(formattedText).plainTextValue;\n  const ops = [];\n\n  for (let i = 0; i < indexes.length; i++) {\n    const element = richTextStyle[indexes[i]];\n    const attr = getAttributtes(element);\n\n    if (isNaN(indexes[i])) {\n      continue;\n    }\n\n    const lastIndex = isNaN(indexes[i + 1]) ? text.length : indexes[i + 1];\n    const textSlice = text.slice(indexes[i], lastIndex);\n\n    ops.push({ insert: textSlice, attributes: attr });\n  }\n\n  editor.setContents(ops);\n  editor.setSelection(text.length, 0);\n};\n\nconst getAttributtes = (element) => {\n  const attr = {};\n  if (element['font-weight'] && element['font-weight'] !== 'normal') {\n    attr['bold'] = true;\n  }\n  if (element['font-style'] && element['font-style'] !== 'normal') {\n    attr['italic'] = true;\n  }\n  if (element['color']) {\n    attr['color'] = element['color'];\n  }\n  if (element['text-decoration']) {\n    const decoration = element['text-decoration'].split(' ');\n\n    if (decoration.includes('line-through')) {\n      attr['strike'] = true;\n    }\n    if (decoration.includes('word')) {\n      attr['underline'] = true;\n    }\n  }\n\n  return attr;\n};\n\nexport default setReactQuillContent;\n", "import React, { useCallback, useContext, useEffect, useMemo, useRef, useState } from 'react';\nimport PropTypes from 'prop-types';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\nimport Autolinker from 'autolinker';\nimport dayjs from 'dayjs';\nimport classNames from 'classnames';\nimport LocalizedFormat from 'dayjs/plugin/localizedFormat';\nimport isString from 'lodash/isString';\n\nimport NoteTextarea from 'components/NoteTextarea';\nimport NoteContext from 'components/Note/Context';\nimport NoteHeader from 'components/NoteHeader';\nimport NoteTextPreview from 'components/NoteTextPreview';\nimport ReplyAttachmentList from 'components/ReplyAttachmentList';\n\nimport mentionsManager from 'helpers/MentionsManager';\nimport getLatestActivityDate from 'helpers/getLatestActivityDate';\nimport setAnnotationRichTextStyle from 'helpers/setAnnotationRichTextStyle';\nimport setReactQuillContent from 'helpers/setReactQuillContent';\nimport { isDarkColorHex, isLightColorHex } from 'helpers/color';\nimport { setAnnotationAttachments } from 'helpers/ReplyAttachmentManager';\nimport { isMobile } from 'helpers/device';\n\nimport core from 'core';\nimport { getDataWithKey, mapAnnotationToKey, annotationMapKeys } from 'constants/map';\nimport Theme from 'constants/theme';\nimport useDidUpdate from 'hooks/useDidUpdate';\nimport actions from 'actions';\nimport selectors from 'selectors';\nimport DataElements from 'constants/dataElement';\nimport DataElementWrapper from '../DataElementWrapper';\nimport { COMMON_COLORS } from 'constants/commonColors';\nimport Button from 'components/Button';\n\nimport './NoteContent.scss';\n\ndayjs.extend(LocalizedFormat);\n\nconst propTypes = {\n  annotation: PropTypes.object.isRequired,\n  isEditing: PropTypes.bool,\n  setIsEditing: PropTypes.func,\n  noteIndex: PropTypes.number,\n  isUnread: PropTypes.bool,\n  isNonReplyNoteRead: PropTypes.bool,\n  onReplyClicked: PropTypes.func,\n  isMultiSelected: PropTypes.bool,\n  isMultiSelectMode: PropTypes.bool,\n  handleMultiSelect: PropTypes.func,\n  isGroupMember: PropTypes.bool,\n};\n\nconst NoteContent = ({\n  annotation,\n  isEditing,\n  setIsEditing,\n  noteIndex,\n  isUnread,\n  isNonReplyNoteRead,\n  onReplyClicked,\n  isMultiSelected,\n  isMultiSelectMode,\n  handleMultiSelect,\n  isGroupMember,\n}) => {\n\n  const noteDateFormat = useSelector((state) => selectors.getNoteDateFormat(state));\n  const iconColor = useSelector((state) => selectors.getIconColor(state, mapAnnotationToKey(annotation), shallowEqual));\n  const isNoteStateDisabled = useSelector((state) => selectors.isElementDisabled(state, 'noteStateFlyout'));\n  const language = useSelector((state) => selectors.getCurrentLanguage(state));\n  const notesShowLastUpdatedDate = useSelector((state) => selectors.notesShowLastUpdatedDate(state));\n  const canCollapseTextPreview = useSelector((state) => selectors.isNotesPanelTextCollapsingEnabled(state));\n  const canCollapseReplyPreview = useSelector((state) => selectors.isNotesPanelRepliesCollapsingEnabled(state));\n  const activeTheme = useSelector((state) => selectors.getActiveTheme(state));\n  const timezone = useSelector((state) => selectors.getTimezone(state));\n  const customizableUI = useSelector((state) => selectors.getFeatureFlags(state)?.customizableUI);\n\n  const {\n    isSelected,\n    searchInput,\n    resize,\n    pendingEditTextMap,\n    onTopNoteContentClicked,\n    sortStrategy,\n    showAnnotationNumbering,\n    setPendingEditText\n  } = useContext(NoteContext);\n\n  const dispatch = useDispatch();\n  const [t] = useTranslation();\n\n  const isReply = annotation.isReply();\n  const isTrackedChange = mapAnnotationToKey(annotation) === annotationMapKeys.TRACKED_CHANGE;\n\n  const [attachments, setAttachments] = useState([]);\n\n  useEffect(() => {\n    setAttachments(annotation.getAttachments());\n  }, [annotation]);\n\n  useEffect(() => {\n    const annotationChangedListener = (annotations, action) => {\n      if (action === 'modify') {\n        annotations.forEach((annot) => {\n          if (annot.Id === annotation.Id) {\n            setAttachments(annot.getAttachments());\n          }\n        });\n      }\n    };\n    core.addEventListener('annotationChanged', annotationChangedListener);\n\n    return () => {\n      core.removeEventListener('annotationChanged', annotationChangedListener);\n    };\n  }, [annotation]);\n\n  useDidUpdate(() => {\n    if (!isEditing) {\n      dispatch(actions.finishNoteEditing());\n    }\n\n    resize();\n  }, [isEditing]);\n\n  const renderAuthorName = useCallback(\n    (annotation) => {\n      const name = core.getDisplayAuthor(annotation['Author']);\n\n      return name ? (\n        highlightSearchInput(name, searchInput)\n      ) : (\n        t('option.notesPanel.noteContent.noName')\n      );\n    },\n    [searchInput],\n  );\n\n  const skipAutoLink = annotation.getSkipAutoLink && annotation.getSkipAutoLink();\n\n  const renderContents = useCallback(\n    (contents, richTextStyle, fontColor, skipAutoLink) => {\n      const autolinkerContent = [];\n      if (!skipAutoLink) {\n        Autolinker.link(contents, {\n          stripPrefix: false,\n          stripTrailingSlash: false,\n          replaceFn(match) {\n            const href = match.getAnchorHref();\n            const anchorText = match.getAnchorText();\n            const offset = match.getOffset();\n\n            switch (match.getType()) {\n              case 'url':\n              case 'email':\n              case 'phone':\n                autolinkerContent.push({\n                  href,\n                  text: anchorText,\n                  start: offset,\n                  end: offset + match.getMatchedText().length\n                });\n            }\n          }\n        });\n      }\n\n      if (!autolinkerContent.length) {\n        const highlightResult = highlightSearchInput(contents, searchInput, richTextStyle);\n        const shouldCollapseAnnotationText = !isReply && canCollapseTextPreview;\n        const shouldCollapseReply = isReply && canCollapseReplyPreview;\n\n        /*\n         * Case there is no value on Search input, and the collapse of the text is allowed,\n         * just render the value with Text preview component\n         */\n        if (!searchInput && (shouldCollapseAnnotationText || shouldCollapseReply)) {\n          const beforeContent = () => {\n            if (!isTrackedChange) {\n              return null;\n            }\n            const text = annotation['TrackedChangeType'] === 1 ? t('officeEditor.added') : t('officeEditor.deleted');\n            return (\n              <span style={{ color: annotation.FillColor.toString(), fontWeight: 700 }}>{text}</span>\n            );\n          };\n\n          return (\n            <NoteTextPreview\n              linesToBreak={3}\n              comment\n              renderRichText={renderRichText}\n              richTextStyle={richTextStyle}\n              resize={resize}\n              style={fontColor}\n              beforeContent={beforeContent}\n            >\n              {contents}\n            </NoteTextPreview>\n          );\n        }\n        return highlightResult;\n      }\n      const contentToRender = [];\n      let strIdx = 0;\n      // Iterate through each case detected by Autolinker, wrap all content\n      // before the current link in a span tag, and wrap the current link\n      // in our own anchor tag\n      autolinkerContent.forEach((anchorData, forIdx) => {\n        const { start, end, href } = anchorData;\n        if (strIdx < start) {\n          contentToRender.push(\n            <span key={`span_${forIdx}`}>\n              {\n                highlightSearchInput(\n                  contents,\n                  searchInput,\n                  richTextStyle,\n                  strIdx,\n                  start\n                )\n              }\n            </span>\n          );\n        }\n        contentToRender.push(\n          <a\n            href={href}\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            key={`a_${forIdx}`}\n          >\n            {\n              highlightSearchInput(\n                contents,\n                searchInput,\n                richTextStyle,\n                start,\n                end\n              )\n            }\n          </a>\n        );\n        strIdx = end;\n      });\n      // Ensure any content after the last link is accounted for\n      if (strIdx < contents.length - 1) {\n        contentToRender.push(highlightSearchInput(\n          contents,\n          searchInput,\n          richTextStyle,\n          strIdx\n        ));\n      }\n      return contentToRender;\n    },\n    [searchInput]\n  );\n\n  const icon = getDataWithKey(mapAnnotationToKey(annotation)).icon;\n  let customData;\n  try {\n    customData = JSON.parse(annotation.getCustomData('trn-mention'));\n  } catch (e) {\n    customData = annotation.getCustomData('trn-mention');\n  }\n  const contents = customData?.contents || annotation.getContents();\n  const contentsToRender = annotation.getContents();\n  const richTextStyle = annotation.getRichTextStyle();\n  let textColor = annotation['TextColor'];\n\n  if (activeTheme === Theme.DARK) {\n    if (textColor && isDarkColorHex(textColor.toHexString())) {\n      textColor = new window.Core.Annotations.Color(255, 255, 255, 1);\n    }\n\n    if (richTextStyle) {\n      const sections = Object.keys(richTextStyle);\n      sections.forEach((a) => {\n        if (richTextStyle[a]['color'] && isDarkColorHex(richTextStyle[a]['color'])) {\n          richTextStyle[a]['color'] = COMMON_COLORS['white'];\n        }\n      });\n    }\n  } else if (activeTheme === Theme.LIGHT) {\n    if (textColor && isLightColorHex(textColor.toHexString())) {\n      textColor = new window.Core.Annotations.Color(0, 0, 0, 1);\n    }\n\n    if (richTextStyle) {\n      const sections = Object.keys(richTextStyle);\n      sections.forEach((a) => {\n        if (richTextStyle[a]['color'] && isLightColorHex(richTextStyle[a]['color'])) {\n          richTextStyle[a]['color'] = COMMON_COLORS['black'];\n        }\n      });\n    }\n  }\n  // This is the text placeholder passed to the ContentArea\n  // It ensures that if we try and edit, we get the right placeholder\n  // depending on whether the comment has been saved to the annotation or not\n  const thereIsNoUnpostedEdit = typeof pendingEditTextMap[annotation.Id] === 'undefined';\n  let textAreaValue;\n  if (contents && thereIsNoUnpostedEdit) {\n    textAreaValue = contents;\n  } else {\n    textAreaValue = pendingEditTextMap[annotation.Id];\n  }\n\n  const handleNoteContentClicked = () => {\n    if (!isGroupMember) {\n      if (isReply) {\n        onReplyClicked(annotation);\n      } else if (!isEditing) {\n        // collapse expanded note when top noteContent is clicked if it's not being edited\n        onTopNoteContentClicked();\n      }\n    }\n  };\n\n  const handleContentsClicked = (e) => {\n    if (window.getSelection()?.toString()) {\n      e?.stopPropagation();\n    }\n  };\n\n  const noteContentClass = classNames({\n    NoteContent: true,\n    isReply,\n    unread: isUnread, // The note content itself is unread or it has unread replies\n    clicked: isNonReplyNoteRead, // The top note content is read\n    'modular-ui': customizableUI,\n  });\n\n  const content = useMemo(\n    () => {\n      const contentStyle = {};\n      if (textColor) {\n        contentStyle.color = textColor.toHexString();\n      }\n\n      return (\n        <>\n          {(isEditing && isSelected) ? (\n            <ContentArea\n              annotation={annotation}\n              noteIndex={noteIndex}\n              setIsEditing={setIsEditing}\n              textAreaValue={textAreaValue}\n              onTextAreaValueChange={setPendingEditText}\n              pendingText={pendingEditTextMap[annotation.Id]}\n            />\n          ) : (\n            contentsToRender && (\n              <div className={classNames('container', { 'reply-content': isReply })} onClick={handleContentsClicked}>\n                {isReply && (attachments.length > 0) && (\n                  <ReplyAttachmentList\n                    files={attachments}\n                    isEditing={false}\n                  />\n                )}\n                {renderContents(contentsToRender, richTextStyle, contentStyle, skipAutoLink)}\n              </div>\n            )\n          )}\n        </>\n      );\n    },\n    [annotation, isSelected, isEditing, setIsEditing, contents, renderContents, textAreaValue, setPendingEditText, attachments]\n  );\n\n  const text = annotation.getCustomData('trn-annot-preview');\n  const textPreview = useMemo(\n    () => {\n      if (text === '') {\n        return null;\n      }\n\n      const highlightSearchResult = highlightSearchInput(text, searchInput);\n      const shouldCollapseAnnotationText = !isReply && canCollapseTextPreview;\n      // If we have a search result do not use text\n      // preview but instead show the entire text\n      if (isString(highlightSearchResult) && shouldCollapseAnnotationText) {\n        return (\n          <DataElementWrapper\n            className=\"selected-text-preview\"\n            dataElement=\"notesSelectedTextPreview\">\n            <NoteTextPreview linesToBreak={3}>\n              {`\"${highlightSearchResult}\"`}\n            </NoteTextPreview>\n          </DataElementWrapper>\n        );\n      }\n      return (\n        <div className=\"selected-text-preview\" style={{ paddingRight: '12px' }}>\n          {highlightSearchResult}\n        </div>\n      );\n    }, [text, searchInput]);\n\n  const header = useMemo(\n    () => {\n      return (\n        <NoteHeader\n          icon={icon}\n          iconColor={iconColor}\n          annotation={annotation}\n          language={language}\n          noteDateFormat={noteDateFormat}\n          isSelected={isSelected}\n          setIsEditing={setIsEditing}\n          notesShowLastUpdatedDate={notesShowLastUpdatedDate}\n          isReply={isReply}\n          isUnread={isUnread}\n          renderAuthorName={renderAuthorName}\n          isNoteStateDisabled={isNoteStateDisabled}\n          isEditing={isEditing}\n          noteIndex={noteIndex}\n          sortStrategy={sortStrategy}\n          activeTheme={activeTheme}\n          handleMultiSelect={handleMultiSelect}\n          isMultiSelected={isMultiSelected}\n          isMultiSelectMode={isMultiSelectMode}\n          isGroupMember={isGroupMember}\n          showAnnotationNumbering={showAnnotationNumbering}\n          timezone={timezone}\n          isTrackedChange={isTrackedChange}\n        />\n      );\n    }, [icon, iconColor, annotation, language, noteDateFormat, isSelected, setIsEditing, notesShowLastUpdatedDate, isReply, isUnread, renderAuthorName, core.getDisplayAuthor(annotation['Author']), isNoteStateDisabled, isEditing, noteIndex, getLatestActivityDate(annotation), sortStrategy, handleMultiSelect, isMultiSelected, isMultiSelectMode, isGroupMember, timezone, isTrackedChange]\n  );\n\n  return (\n    <div className={noteContentClass} onClick={handleNoteContentClicked}>\n      {header}\n      {textPreview}\n      {content}\n    </div>\n  );\n};\n\nNoteContent.propTypes = propTypes;\n\nexport default NoteContent;\n\n// a component that contains the content textarea, the save button and the cancel button\nconst ContentArea = ({\n  annotation,\n  noteIndex,\n  setIsEditing,\n  textAreaValue,\n  onTextAreaValueChange,\n  pendingText\n}) => {\n  const [\n    autoFocusNoteOnAnnotationSelection,\n    isMentionEnabled,\n    isInlineCommentDisabled,\n    isInlineCommentOpen,\n    isNotesPanelOpen,\n    activeDocumentViewerKey,\n    isAnyCustomPanelOpen,\n  ] = useSelector((state) => [\n    selectors.getAutoFocusNoteOnAnnotationSelection(state),\n    selectors.getIsMentionEnabled(state),\n    selectors.isElementDisabled(state, DataElements.INLINE_COMMENT_POPUP),\n    selectors.isElementOpen(state, DataElements.INLINE_COMMENT_POPUP),\n    selectors.isElementOpen(state, DataElements.NOTES_PANEL),\n    selectors.getActiveDocumentViewerKey(state),\n    selectors.isAnyCustomPanelOpen(state),\n  ]);\n  const [t] = useTranslation();\n  const textareaRef = useRef();\n  const isReply = annotation.isReply();\n  const {\n    setCurAnnotId,\n    pendingAttachmentMap,\n    deleteAttachment,\n    clearAttachments,\n    addAttachments\n  } = useContext(NoteContext);\n\n  const shouldNotFocusOnInput = !isInlineCommentDisabled && isInlineCommentOpen && isMobile();\n\n  useEffect(() => {\n    // on initial mount, focus the last character of the textarea\n    if (isAnyCustomPanelOpen || (isNotesPanelOpen || isInlineCommentOpen) && textareaRef.current) {\n      const editor = textareaRef.current.getEditor();\n      const isFreeTextAnnnotation = annotation && annotation instanceof window.Core.Annotations.FreeTextAnnotation;\n      isFreeTextAnnnotation && editor.setText('');\n\n      /**\n       * If there is a pending text we should update the annotation rich text style\n       * with this pending text style.\n       */\n      if (pendingText) {\n        setAnnotationRichTextStyle(editor, annotation);\n      } else if (editor.getContents()) {\n        setTimeout(() => {\n          // need setTimeout because textarea seem to rerender and unfocus\n          if (isMentionEnabled) {\n            textAreaValue = mentionsManager.getFormattedTextFromDeltas(editor.getContents());\n            const { plainTextValue, ids } = mentionsManager.extractMentionDataFromStr(textAreaValue);\n\n            if (ids.length) {\n              editor.setText(plainTextValue);\n            }\n          }\n\n          if (shouldNotFocusOnInput) {\n            return;\n          }\n\n          if (autoFocusNoteOnAnnotationSelection) {\n            textareaRef.current?.focus();\n            const annotRichTextStyle = annotation.getRichTextStyle();\n            if (annotRichTextStyle) {\n              setReactQuillContent(annotation, editor);\n            }\n          }\n        }, 100);\n      }\n\n      const lastNewLineCharacterLength = 1;\n      const textLength = editor.getLength() - lastNewLineCharacterLength;\n\n      if (shouldNotFocusOnInput) {\n        return;\n      }\n\n      setTimeout(() => {\n        if (textLength) {\n          editor.setSelection(textLength, textLength);\n        }\n      }, 100);\n    }\n  }, [isNotesPanelOpen, isInlineCommentOpen, shouldNotFocusOnInput]);\n\n  useEffect(() => {\n    if (isReply && pendingAttachments.length === 0) {\n      // Load attachments\n      const attachments = annotation.getAttachments();\n      addAttachments(annotation.Id, attachments);\n    }\n  }, []);\n\n  const setContents = async (e) => {\n    // prevent the textarea from blurring out which will unmount these two buttons\n    e.preventDefault();\n\n    const editor = textareaRef.current.getEditor();\n    textAreaValue = mentionsManager.getFormattedTextFromDeltas(editor.getContents());\n    setAnnotationRichTextStyle(editor, annotation);\n\n    const hasTrailingNewlineToRemove = textAreaValue.length > 1 && textAreaValue[textAreaValue.length - 1] === '\\n';\n    if (hasTrailingNewlineToRemove) {\n      textAreaValue = textAreaValue.slice(0, textAreaValue.length - 1);\n    }\n\n    const skipAutoLink = annotation.getSkipAutoLink && annotation.getSkipAutoLink();\n    if (skipAutoLink) {\n      annotation.disableSkipAutoLink();\n    }\n\n    if (isMentionEnabled) {\n      const { plainTextValue, ids } = mentionsManager.extractMentionDataFromStr(textAreaValue);\n\n      // If modified, double check for ids\n      const annotMentionData = mentionsManager.extractMentionDataFromAnnot(annotation);\n      annotMentionData.mentions.forEach((mention) => {\n        if (plainTextValue.includes(mention.value)) {\n          ids.push(mention.id);\n        }\n      });\n\n      annotation.setCustomData('trn-mention', JSON.stringify({\n        contents: textAreaValue,\n        ids,\n      }));\n      annotation.setContents(plainTextValue);\n    } else {\n      annotation.setContents(textAreaValue);\n    }\n\n    await setAnnotationAttachments(annotation, pendingAttachmentMap[annotation.Id]);\n\n    const source = (annotation instanceof window.Core.Annotations.FreeTextAnnotation)\n      ? 'textChanged' : 'noteChanged';\n    core.getAnnotationManager(activeDocumentViewerKey).trigger('annotationChanged', [[annotation], 'modify', { 'source': source }]);\n\n    if (annotation instanceof window.Core.Annotations.FreeTextAnnotation) {\n      core.drawAnnotationsFromList([annotation]);\n    }\n\n    setIsEditing(false, noteIndex);\n    // Only set comment to unposted state if it is not empty\n    if (textAreaValue !== '') {\n      onTextAreaValueChange(undefined, annotation.Id);\n    }\n    clearAttachments(annotation.Id);\n  };\n\n  const onBlur = (e) => {\n    if (e.relatedTarget?.getAttribute('data-element')?.includes('annotationCommentButton')) {\n      e.target.focus();\n      return;\n    }\n    setCurAnnotId(undefined);\n  };\n\n  const onFocus = () => {\n    setCurAnnotId(annotation.Id);\n  };\n\n  const contentClassName = classNames('edit-content', { 'reply-content': isReply });\n  const pendingAttachments = pendingAttachmentMap[annotation.Id] || [];\n\n  return (\n    <div className={contentClassName}>\n      {isReply && pendingAttachments.length > 0 && (\n        <ReplyAttachmentList\n          files={pendingAttachments}\n          isEditing={true}\n          fileDeleted={(file) => deleteAttachment(annotation.Id, file)}\n        />\n      )}\n      <NoteTextarea\n        ref={(el) => {\n          textareaRef.current = el;\n        }}\n        value={textAreaValue}\n        onChange={(value) => onTextAreaValueChange(value, annotation.Id)}\n        onSubmit={setContents}\n        isReply={isReply}\n        onBlur={onBlur}\n        onFocus={onFocus}\n      />\n      <div className=\"edit-buttons\">\n        <Button\n          className=\"cancel-button\"\n          label={t('action.cancel')}\n          onClick={(e) => {\n            e.stopPropagation();\n            setIsEditing(false, noteIndex);\n            // Clear pending text\n            onTextAreaValueChange(undefined, annotation.Id);\n            clearAttachments(annotation.Id);\n          }}\n        />\n        <Button\n          className={`save-button${!textAreaValue ? ' disabled' : ''}`}\n          disabled={!textAreaValue}\n          label={t('action.save')}\n          onClick={(e) => {\n            e.stopPropagation();\n            setContents(e);\n          }}\n        />\n      </div>\n    </div>\n  );\n};\n\nContentArea.propTypes = {\n  noteIndex: PropTypes.number.isRequired,\n  annotation: PropTypes.object.isRequired,\n  setIsEditing: PropTypes.func.isRequired,\n  textAreaValue: PropTypes.string,\n  onTextAreaValueChange: PropTypes.func.isRequired,\n  pendingText: PropTypes.string\n};\n\nconst getRichTextSpan = (text, richTextStyle, key) => {\n  const style = {\n    fontWeight: richTextStyle['font-weight'],\n    fontStyle: richTextStyle['font-style'],\n    textDecoration: richTextStyle['text-decoration'],\n    color: richTextStyle['color']\n  };\n  if (style.textDecoration) {\n    style.textDecoration = style.textDecoration.replace('word', 'underline');\n  }\n  return (\n    <span style={style} key={key}>{text}</span>\n  );\n};\n\nconst renderRichText = (text, richTextStyle, start) => {\n  if (!richTextStyle || !text) {\n    return text;\n  }\n\n  const styles = {};\n  const indices = Object.keys(richTextStyle).map(Number).sort((a, b) => a - b);\n  for (let i = 0; i < indices.length; i++) {\n    let index = indices[i] - start;\n    index = Math.min(Math.max(index, 0), text.length);\n    styles[index] = richTextStyle[indices[i]];\n    if (index === text.length) {\n      break;\n    }\n  }\n\n  const contentToRender = [];\n  const styleIndices = Object.keys(styles).map(Number).sort((a, b) => a - b);\n  for (let i = 1; i < styleIndices.length; i++) {\n    contentToRender.push(getRichTextSpan(\n      text.slice(styleIndices[i - 1], styleIndices[i]),\n      styles[styleIndices[i - 1]],\n      `richtext_span_${i}`\n    ));\n  }\n\n  return contentToRender;\n};\n\nconst highlightSearchInput = (fullText, searchInput, richTextStyle, start = 0, end = fullText.length) => {\n  const text = fullText.slice(start, end);\n  const loweredText = text.toLowerCase();\n  const loweredSearchInput = searchInput.toLowerCase();\n  if (richTextStyle) {\n    richTextStyle['0'] = richTextStyle['0'] || {};\n    richTextStyle[fullText.length] = richTextStyle[fullText.length] || {};\n  }\n  let lastFoundInstance = loweredText.indexOf(loweredSearchInput);\n  if (!loweredSearchInput.trim() || lastFoundInstance === -1) {\n    return renderRichText(text, richTextStyle, start);\n  }\n  const contentToRender = [];\n  const allFoundPositions = [lastFoundInstance];\n  // Escape all RegExp special characters\n  const regexSafeSearchInput = loweredSearchInput.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n  if (new RegExp(`(${regexSafeSearchInput})`, 'gi').test(loweredText)) {\n    while (lastFoundInstance !== -1) {\n      lastFoundInstance = loweredText.indexOf(loweredSearchInput, lastFoundInstance + loweredSearchInput.length);\n      if (lastFoundInstance !== -1) {\n        allFoundPositions.push(lastFoundInstance);\n      }\n    }\n  }\n  allFoundPositions.forEach((position, idx) => {\n    // Account for any content at the beginning of the string before the first\n    // instance of the searchInput\n    if (idx === 0 && position !== 0) {\n      contentToRender.push(renderRichText(text.substring(0, position), richTextStyle, start));\n    }\n    contentToRender.push(\n      <span className=\"highlight\" key={`highlight_span_${idx}`}>\n        {\n          renderRichText(\n            text.substring(position, position + loweredSearchInput.length),\n            richTextStyle,\n            start + position)\n        }\n      </span>\n    );\n    if (\n      // Ensure that we do not try to make an out-of-bounds access\n      position + loweredSearchInput.length < loweredText.length\n      // Ensure that this is the end of the allFoundPositions array\n      && position + loweredSearchInput.length !== allFoundPositions[idx + 1]\n    ) {\n      contentToRender.push(renderRichText(\n        text.substring(position + loweredSearchInput.length, allFoundPositions[idx + 1]),\n        richTextStyle,\n        start + position + loweredSearchInput.length\n      ));\n    }\n  });\n  return contentToRender;\n};\n", "import NoteContent from './NoteContent';\n\nexport default NoteContent;", "import React, { useState, useEffect, useRef, useContext } from 'react';\nimport PropTypes from 'prop-types';\nimport { useSelector, useDispatch, shallowEqual } from 'react-redux';\nimport NoteContext from 'components/Note/Context';\nimport NoteTextarea from 'components/NoteTextarea';\nimport ReplyAttachmentList from 'components/ReplyAttachmentList';\nimport Button from 'components/Button';\nimport classNames from 'classnames';\nimport core from 'core';\nimport mentionsManager from 'helpers/MentionsManager';\nimport setAnnotationRichTextStyle from 'helpers/setAnnotationRichTextStyle';\nimport { setAnnotationAttachments } from 'helpers/ReplyAttachmentManager';\nimport useDidUpdate from 'hooks/useDidUpdate';\nimport actions from 'actions';\nimport selectors from 'selectors';\nimport { isMobile } from 'src/helpers/device';\nimport DataElements from 'src/constants/dataElement';\n\nimport './ReplyArea.scss';\n\nconst propTypes = {\n  annotation: PropTypes.object.isRequired,\n};\n\n// a component that contains the reply textarea, the reply button and the cancel button\nconst ReplyArea = ({ annotation, isUnread, onPendingReplyChange }) => {\n  const [\n    autoFocusNoteOnAnnotationSelection,\n    isReadOnly,\n    isReplyDisabled,\n    isReplyDisabledForAnnotation,\n    isMentionEnabled,\n    isNoteEditingTriggeredByAnnotationPopup,\n    isInlineCommentDisabled,\n    isInlineCommentOpen,\n    activeDocumentViewerKey,\n  ] = useSelector(\n    (state) => [\n      selectors.getAutoFocusNoteOnAnnotationSelection(state),\n      selectors.isDocumentReadOnly(state),\n      selectors.isElementDisabled(state, 'noteReply'),\n      selectors.getIsReplyDisabled(state)?.(annotation),\n      selectors.getIsMentionEnabled(state),\n      selectors.getIsNoteEditing(state),\n      selectors.isElementDisabled(state, DataElements.INLINE_COMMENT_POPUP),\n      selectors.isElementOpen(state, DataElements.INLINE_COMMENT_POPUP),\n      selectors.getActiveDocumentViewerKey(state),\n    ],\n    shallowEqual\n  );\n  const {\n    isContentEditable,\n    isSelected,\n    pendingReplyMap,\n    setPendingReply,\n    isExpandedFromSearch,\n    scrollToSelectedAnnot,\n    setCurAnnotId,\n    pendingAttachmentMap,\n    clearAttachments,\n    deleteAttachment\n  } = useContext(NoteContext);\n  const [isFocused, setIsFocused] = useState(false);\n  const dispatch = useDispatch();\n  const textareaRef = useRef();\n\n  const shouldNotFocusOnInput = !isInlineCommentDisabled && isInlineCommentOpen && isMobile();\n\n  useDidUpdate(() => {\n    if (!isFocused) {\n      dispatch(actions.finishNoteEditing());\n    }\n  }, [isFocused]);\n\n  useEffect(() => {\n    if (shouldNotFocusOnInput) {\n      return;\n    }\n\n    if (\n      isNoteEditingTriggeredByAnnotationPopup &&\n      isSelected &&\n      !isContentEditable &&\n      autoFocusNoteOnAnnotationSelection &&\n      textareaRef &&\n      textareaRef.current\n    ) {\n      textareaRef.current.focus();\n    }\n  }, [isContentEditable, isNoteEditingTriggeredByAnnotationPopup, isSelected, shouldNotFocusOnInput]);\n\n  useEffect(() => {\n    // on initial mount, focus the last character of the textarea\n    // when search item, should disable auto focus\n    if (!isExpandedFromSearch && scrollToSelectedAnnot) {\n      // use \"setTimeout\" to wait for element to be added before focusing to have the blinking text cursor appear\n      setTimeout(() => {\n        // calling focus() cause the \"NotePanel\" to scroll to note that being focused.\n        // we don't want to jump to the selected annotation when scrolling up and down, so only focus once\n        if (textareaRef && textareaRef.current && autoFocusNoteOnAnnotationSelection) {\n          textareaRef.current.focus();\n        }\n      }, 100);\n    }\n    if (textareaRef && textareaRef.current) {\n      if (shouldNotFocusOnInput) {\n        return;\n      }\n\n      const editor = textareaRef.current.getEditor();\n      const lastNewLineCharacterLength = 1;\n      const textLength = editor.getLength() - lastNewLineCharacterLength;\n      setTimeout(() => {\n        if (textareaRef.current) {\n          textareaRef.current.editor.setSelection(textLength, textLength);\n        }\n      }, 100);\n    }\n  }, []);\n\n  const postReply = async (e) => {\n    // prevent the textarea from blurring out\n    e.preventDefault();\n    e.stopPropagation();\n\n    const editor = textareaRef.current.getEditor();\n    const replyText = mentionsManager.getFormattedTextFromDeltas(editor.getContents());\n\n    if (!replyText.trim()) {\n      return;\n    }\n\n    if (isMentionEnabled) {\n      const replyAnnotation = mentionsManager.createMentionReply(annotation, replyText);\n      setAnnotationRichTextStyle(editor, replyAnnotation);\n      await setAnnotationAttachments(replyAnnotation, pendingAttachmentMap[annotation.Id]);\n      core.addAnnotations([replyAnnotation], activeDocumentViewerKey);\n    } else {\n      const replyAnnotation = core.createAnnotationReply(annotation, replyText);\n      setAnnotationRichTextStyle(editor, replyAnnotation);\n      await setAnnotationAttachments(replyAnnotation, pendingAttachmentMap[annotation.Id]);\n      core.getAnnotationManager(activeDocumentViewerKey).trigger('annotationChanged', [[replyAnnotation], 'modify', {}]);\n    }\n\n    setPendingReply('', annotation.Id);\n    clearAttachments(annotation.Id);\n  };\n\n  const ifReplyNotAllowed =\n    isReadOnly ||\n    isReplyDisabled ||\n    isReplyDisabledForAnnotation;\n\n  const replyAreaClass = classNames({\n    'reply-area': true,\n    unread: isUnread,\n  });\n\n  const handleNoteTextareaChange = (value) => {\n    setPendingReply(value, annotation.Id);\n    onPendingReplyChange && onPendingReplyChange();\n  };\n\n  const onBlur = () => {\n    setIsFocused(false);\n    setCurAnnotId(undefined);\n  };\n\n  const onFocus = () => {\n    setIsFocused(true);\n    setCurAnnotId(annotation.Id);\n  };\n\n  const pendingAttachments = pendingAttachmentMap[annotation.Id] || [];\n\n  return (ifReplyNotAllowed || !isSelected) ? null : (\n    <form onSubmit={postReply} className=\"reply-area-container\">\n      {pendingAttachments.length > 0 && (\n        <ReplyAttachmentList\n          files={pendingAttachments}\n          isEditing={true}\n          fileDeleted={(file) => deleteAttachment(annotation.Id, file)}\n        />\n      )}\n      <div className=\"reply-area-with-button\">\n        <div\n          className={replyAreaClass}\n          // stop bubbling up otherwise the note will be closed\n          // due to annotation deselection\n          onMouseDown={(e) => e.stopPropagation()}\n        >\n          <NoteTextarea\n            ref={(el) => {\n              textareaRef.current = el;\n            }}\n            value={pendingReplyMap[annotation.Id]}\n            onChange={(value) => handleNoteTextareaChange(value)}\n            onSubmit={postReply}\n            onBlur={onBlur}\n            onFocus={onFocus}\n            isReply\n          />\n        </div>\n        <div className=\"reply-button-container\">\n          <Button\n            img=\"icon-post-reply\"\n            className='reply-button'\n            title={'action.submit'}\n            disabled={!pendingReplyMap[annotation.Id]}\n            onClick={postReply}\n            isSubmitType\n          />\n        </div>\n      </div>\n    </form>\n  );\n};\n\nReplyArea.propTypes = propTypes;\n\nexport default ReplyArea;\n", "import ReplyArea from './ReplyArea';\n\nexport default ReplyArea;", "import React, { useState } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\n\nimport Button from 'components/Button';\nimport NoteContent from 'components/NoteContent';\nimport PropTypes from 'prop-types';\nimport core from 'core';\nimport actions from 'actions';\nimport selectors from 'selectors';\nimport classNames from 'classnames';\n\nconst propTypes = {\n  groupAnnotations: PropTypes.array.isRequired,\n  isMultiSelectMode: PropTypes.bool.isRequired,\n};\n\nconst NoteGroupSection = ({\n  groupAnnotations,\n  isMultiSelectMode,\n}) => {\n  const [t] = useTranslation();\n  const dispatch = useDispatch();\n  const [isViewingGroupAnnots, setIsViewingGroupAnnots] = useState(false);\n  const customizableUI = useSelector((state) => selectors.getFeatureFlags(state)?.customizableUI);\n\n  const upArrow = 'ic_chevron_up_black_24px';\n  const downArrow = 'ic_chevron_down_black_24px';\n\n  const ViewAllAnnotsButton = (\n    <Button\n      onClick={(e) => {\n        e.preventDefault();\n        e.stopPropagation();\n        setIsViewingGroupAnnots(true);\n      }}\n      className=\"text-button\"\n      ariaLabel={t('component.noteGroupSection.open')}\n      label={t('component.noteGroupSection.open')}\n      img={downArrow}\n    />\n  );\n  const CloseAllAnnotsButton = (\n    <Button\n      onClick={(e) => {\n        e.preventDefault();\n        e.stopPropagation();\n        setIsViewingGroupAnnots(false);\n      }}\n      className=\"text-button\"\n      ariaLabel={t('component.noteGroupSection.close')}\n      label={t('component.noteGroupSection.close')}\n      img={upArrow}\n    />\n  );\n\n  return (\n    <div\n      className={classNames({\n        'group-section': true,\n        'modular-ui': customizableUI,\n      })}\n    >\n      {isViewingGroupAnnots ? CloseAllAnnotsButton : ViewAllAnnotsButton}\n      {isViewingGroupAnnots &&\n        groupAnnotations.map((groupAnnotation, i) => {\n          // Ignore the group primary annotation\n          if (i === 0) {\n            return null;\n          }\n          return (\n            <Button\n              key={groupAnnotation.Id}\n              className=\"group-child\"\n              onClick={(e) => {\n                e.preventDefault();\n                e.stopPropagation();\n                core.selectAnnotation(groupAnnotation);\n                core.jumpToAnnotation(groupAnnotation);\n                dispatch(actions.openElement('annotationPopup'));\n              }}\n            >\n              <NoteContent\n                key={groupAnnotation.Id}\n                annotation={groupAnnotation}\n                isUnread={false}\n                isGroupMember={true}\n                isMultiSelectMode={isMultiSelectMode}\n              />\n            </Button>\n          );\n        })\n      }\n    </div>\n  );\n};\n\nNoteGroupSection.propTypes = propTypes;\n\nexport default NoteGroupSection;", "import React, { useEffect, useState, useCallback } from 'react';\nimport { useSelector, shallowEqual, useDispatch } from 'react-redux';\nimport core from 'core';\nimport selectors from 'selectors';\nimport actions from 'actions';\nimport { createPortal } from 'react-dom';\nimport { getAnnotationPosition } from 'helpers/getPopupPosition';\nimport getRootNode from 'helpers/getRootNode';\nimport DataElements from 'constants/dataElement';\n\nimport './AnnotationNoteConnectorLine.scss';\n\nconst LineConnectorPortal = ({ children }) => {\n  const mount = getRootNode().querySelector('#line-connector-root');\n  const el = document.createElement('div');\n  el.setAttribute('data-element', DataElements.ANNOTATION_NOTE_CONNECTOR_LINE);\n\n  useEffect(() => {\n    mount.appendChild(el);\n    return () => mount.removeChild(el);\n  }, [el, mount]);\n\n  return createPortal(children, el);\n};\n\nconst AnnotationNoteConnectorLine = ({ annotation, noteContainerRef, isCustomPanelOpen }) => {\n  const [\n    notePanelWidth,\n    lineIsOpen,\n    notePanelIsOpen,\n    isLineDisabled,\n    documentContainerWidth,\n    documentContainerHeight,\n    activeDocumentViewerKey,\n  ] = useSelector(\n    (state) => [\n      selectors.getNotesPanelWidth(state),\n      selectors.isElementOpen(state, DataElements.ANNOTATION_NOTE_CONNECTOR_LINE),\n      selectors.isElementOpen(state, DataElements.NOTES_PANEL),\n      selectors.isElementDisabled(state, DataElements.ANNOTATION_NOTE_CONNECTOR_LINE),\n      selectors.getDocumentContainerWidth(state),\n      selectors.getDocumentContainerHeight(state),\n      selectors.getActiveDocumentViewerKey(state),\n    ],\n    shallowEqual,\n  );\n\n  const dispatch = useDispatch();\n\n  // Right Horizontal Line\n  const [rightHorizontalLineWidth, setRightHorizontalLineWidth] = useState(0);\n  const [rightHorizontalLineTop, setRightHorizontalLineTop] = useState(0);\n  const [rightHorizontalLineRight, setRightHorizontalLineRight] = useState(0);\n\n  // Left Horizontal Line\n  const [leftHorizontalLineWidth, setLeftHorizontalLineWidth] = useState(0);\n  const [leftHorizontalLineTop, setLeftHorizontalLineTop] = useState(0);\n  const [leftHorizontalLineRight, setLeftHorizontalLineRight] = useState(0);\n\n  const {\n    bottomRight: annotationBottomRight,\n    topLeft: annotationTopLeft\n  } = getAnnotationPosition(annotation, activeDocumentViewerKey);\n\n  const getAnnotationLineOffset = useCallback(() => {\n    if (annotation.Subject === 'Note') {\n      return 4;\n    }\n    return 15;\n  }, [annotation]);\n\n  useEffect(() => {\n    const { scrollTop, scrollLeft } = core.getScrollViewElement(activeDocumentViewerKey);\n    const notePanelLeftPadding = 16;\n    const isAnnotationPositionInvalid = !(annotationBottomRight && annotationTopLeft);\n    if (isAnnotationPositionInvalid) {\n      return () => {\n        dispatch(actions.closeElement(DataElements.ANNOTATION_NOTE_CONNECTOR_LINE));\n      };\n    }\n    const annotWidthInPixels = annotationBottomRight.x - annotationTopLeft.x;\n    const annotHeightInPixels = annotationBottomRight.y - annotationTopLeft.y;\n\n    const viewerWidth = window.isApryseWebViewerWebComponent ? getRootNode().host.clientWidth : window.innerWidth;\n    const viewerOffsetTop = window.isApryseWebViewerWebComponent ? getRootNode().host.offsetTop : 0;\n\n    setRightHorizontalLineRight(notePanelWidth - notePanelLeftPadding);\n    setRightHorizontalLineTop(noteContainerRef.current.getBoundingClientRect().top - viewerOffsetTop);\n    const lineWidth = viewerWidth - notePanelWidth - annotationTopLeft.x + notePanelLeftPadding + scrollLeft - annotWidthInPixels;\n    const rightHorizontalLineWidthRatio = 0.75;\n    setRightHorizontalLineWidth(lineWidth * rightHorizontalLineWidthRatio);\n    const noZoomRefPoint = annotation.getNoZoomReferencePoint();\n    const noZoomRefShiftX = (annotation.NoZoom && noZoomRefPoint.x) ? noZoomRefPoint.x * annotHeightInPixels : 0;\n    setLeftHorizontalLineWidth(lineWidth - rightHorizontalLineWidth - getAnnotationLineOffset() + noZoomRefShiftX);\n\n    setLeftHorizontalLineRight(notePanelWidth - notePanelLeftPadding + rightHorizontalLineWidth);\n\n    const noZoomRefShiftY = (annotation.NoZoom && noZoomRefPoint.y) ? noZoomRefPoint.y * annotHeightInPixels : 0;\n    setLeftHorizontalLineTop(annotationTopLeft.y + (annotHeightInPixels / 2) - scrollTop - noZoomRefShiftY);\n\n    const onPageNumberUpdated = () => {\n      dispatch(actions.closeElement(DataElements.ANNOTATION_NOTE_CONNECTOR_LINE));\n    };\n\n    core.addEventListener('pageNumberUpdated', onPageNumberUpdated, undefined, activeDocumentViewerKey);\n\n    return () => {\n      core.removeEventListener('pageNumberUpdated', onPageNumberUpdated, activeDocumentViewerKey);\n    };\n  }, [noteContainerRef, notePanelWidth, annotationBottomRight, annotationTopLeft, documentContainerWidth, documentContainerHeight, dispatch, activeDocumentViewerKey]);\n\n  if (lineIsOpen && (notePanelIsOpen || isCustomPanelOpen) && !isLineDisabled) {\n    const verticalHeight = Math.abs(rightHorizontalLineTop - leftHorizontalLineTop);\n    const horizontalLineHeight = 2;\n    // Add HorizontalLineHeight of 2px when annot is above note to prevent little gap between lines\n    const verticalTop = rightHorizontalLineTop > leftHorizontalLineTop ? leftHorizontalLineTop + horizontalLineHeight : rightHorizontalLineTop;\n\n    return (\n      <LineConnectorPortal>\n        <div className=\"horizontalLine\" style={{ width: rightHorizontalLineWidth, right: rightHorizontalLineRight, top: rightHorizontalLineTop }} />\n        <div className=\"verticalLine\" style={{ height: verticalHeight, top: verticalTop, right: rightHorizontalLineRight + rightHorizontalLineWidth }} />\n        <div className=\"horizontalLine\" style={{ width: leftHorizontalLineWidth, right: leftHorizontalLineRight, top: leftHorizontalLineTop }}>\n          <div className=\"arrowHead\" />\n        </div>\n      </LineConnectorPortal>);\n  }\n  return null;\n};\n\nexport default AnnotationNoteConnectorLine;\n", "import AnnotationNoteConnectorLine from './AnnotationNoteConnectorLine';\n\nexport default AnnotationNoteConnectorLine;", "import React, { useEffect, useRef, useContext, useState, useCallback } from 'react';\nimport classNames from 'classnames';\nimport PropTypes from 'prop-types';\nimport { useSelector, shallowEqual, useDispatch } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\n\nimport NoteContext from 'components/Note/Context';\nimport NoteContent from 'components/NoteContent';\nimport ReplyArea from 'components/Note/ReplyArea';\nimport NoteGroupSection from 'components/Note/NoteGroupSection';\nimport Button from 'components/Button';\n\nimport selectors from 'selectors';\nimport actions from 'actions';\nimport core from 'core';\nimport AnnotationNoteConnectorLine from 'components/AnnotationNoteConnectorLine';\nimport useDidUpdate from 'hooks/useDidUpdate';\nimport DataElements from 'constants/dataElement';\nimport getRootNode from 'helpers/getRootNode';\nimport { mapAnnotationToKey, annotationMapKeys } from 'constants/map';\nimport { OfficeEditorEditMode, OFFICE_EDITOR_TRACKED_CHANGE_KEY } from 'constants/officeEditor';\n\nimport './Note.scss';\n\nconst propTypes = {\n  annotation: PropTypes.object.isRequired,\n  isMultiSelected: PropTypes.bool,\n  isMultiSelectMode: PropTypes.bool,\n  isInNotesPanel: PropTypes.bool,\n  handleMultiSelect: PropTypes.func,\n};\n\nlet currId = 0;\n\nconst Note = ({\n  annotation,\n  isMultiSelected,\n  isMultiSelectMode,\n  isInNotesPanel,\n  handleMultiSelect,\n  isCustomPanelOpen,\n  shouldHideConnectorLine,\n}) => {\n  const {\n    isSelected,\n    resize,\n    pendingEditTextMap,\n    isContentEditable,\n    isDocumentReadOnly,\n    isExpandedFromSearch,\n    // documentViewerKey,\n    setCurAnnotId,\n  } = useContext(NoteContext);\n  const containerRef = useRef();\n  const containerHeightRef = useRef();\n  const [isEditingMap, setIsEditingMap] = useState({});\n  const ids = useRef([]);\n  const dispatch = useDispatch();\n  const [t] = useTranslation();\n  const unreadReplyIdSet = new Set();\n\n  const [\n    noteTransformFunction,\n    customNoteSelectionFunction,\n    unreadAnnotationIdSet,\n    shouldExpandCommentThread,\n    isRightClickAnnotationPopupEnabled,\n    documentViewerKey,\n    isOfficeEditorMode,\n    officeEditorEditMode,\n  ] = useSelector(\n    (state) => [\n      selectors.getNoteTransformFunction(state),\n      selectors.getCustomNoteSelectionFunction(state),\n      selectors.getUnreadAnnotationIdSet(state),\n      selectors.isCommentThreadExpansionEnabled(state),\n      selectors.isRightClickAnnotationPopupEnabled(state),\n      selectors.getActiveDocumentViewerKey(state),\n      selectors.getIsOfficeEditorMode(state),\n      selectors.getOfficeEditorEditMode(state),\n    ],\n    shallowEqual,\n  );\n\n  const replies = annotation\n    .getReplies()\n    .sort((a, b) => a['DateCreated'] - b['DateCreated']);\n\n  replies.filter((r) => unreadAnnotationIdSet.has(r.Id)).forEach((r) => unreadReplyIdSet.add(r.Id));\n\n  useEffect(() => {\n    const annotationChangedListener = (annotations, action) => {\n      if (action === 'delete') {\n        annotations.forEach((annot) => {\n          if (unreadAnnotationIdSet.has(annot.Id)) {\n            dispatch(actions.setAnnotationReadState({ isRead: true, annotationId: annot.Id }));\n          }\n        });\n      }\n    };\n    core.addEventListener('annotationChanged', annotationChangedListener, undefined, documentViewerKey);\n\n    return () => {\n      core.removeEventListener('annotationChanged', annotationChangedListener, documentViewerKey);\n    };\n  }, [unreadAnnotationIdSet]);\n\n  useEffect(() => {\n    const prevHeight = containerHeightRef.current;\n    const currHeight = containerRef.current.getBoundingClientRect().height;\n    containerHeightRef.current = currHeight;\n\n    // have a prevHeight check here because we don't want to call resize on mount\n    // use Math.round because in some cases in IE11 these two numbers will differ in just 0.00001\n    // and we don't want call resize in this case\n    if (prevHeight && Math.round(prevHeight) !== Math.round(currHeight)) {\n      resize();\n    }\n  });\n\n  useEffect(() => {\n    if (noteTransformFunction) {\n      const notesPanelElement = getRootNode().querySelectorAll('.NotesPanel')[0];\n      ids.current.forEach((id) => {\n        const child = notesPanelElement.querySelector(`[data-webviewer-custom-element=${id}]`);\n        if (child) {\n          child.parentNode.removeChild(child);\n        }\n      });\n\n      ids.current = [];\n\n      const state = {\n        annotation,\n        isSelected,\n      };\n\n      noteTransformFunction(containerRef.current, state, (...params) => {\n        const element = document.createElement(...params);\n        const id = `custom-element-${currId}`;\n        currId++;\n        ids.current.push(id);\n        element.setAttribute('data-webviewer-custom-element', id);\n        element.addEventListener('mousedown', (e) => {\n          e.stopPropagation();\n        });\n\n        return element;\n      });\n    }\n  });\n\n  useEffect(() => {\n    // If this is not a new one, rebuild the isEditing map\n    const pendingText = pendingEditTextMap[annotation.Id];\n    if (pendingText !== '' && isContentEditable && !isDocumentReadOnly) {\n      setIsEditing(true, 0);\n    }\n  }, [isDocumentReadOnly, isContentEditable, setIsEditing, annotation, isMultiSelectMode]);\n\n  useDidUpdate(() => {\n    if (isDocumentReadOnly || !isContentEditable) {\n      setIsEditing(false, 0);\n    }\n  }, [isDocumentReadOnly, isContentEditable, setIsEditing]);\n\n  const handleNoteClick = async (e) => {\n    // stop bubbling up otherwise the note will be closed\n    // due to annotation deselection\n    e && e.stopPropagation();\n\n    if (isMultiSelectMode) {\n      handleMultiSelect(!isMultiSelected);\n      return;\n    }\n    if (unreadAnnotationIdSet.has(annotation.Id)) {\n      dispatch(actions.setAnnotationReadState({ isRead: true, annotationId: annotation.Id }));\n    }\n\n    customNoteSelectionFunction && customNoteSelectionFunction(annotation);\n    if (!isSelected) {\n      core.deselectAllAnnotations(documentViewerKey);\n\n      // Need this delay to ensure all other event listeners fire before we open the line\n      setTimeout(() => dispatch(actions.openElement(DataElements.ANNOTATION_NOTE_CONNECTOR_LINE)), 300);\n    }\n    if (isInNotesPanel && !(isOfficeEditorMode && officeEditorEditMode === OfficeEditorEditMode.PREVIEW)) {\n      core.selectAnnotation(annotation, documentViewerKey);\n      setCurAnnotId(annotation.Id);\n      core.jumpToAnnotation(annotation, documentViewerKey);\n      if (!isRightClickAnnotationPopupEnabled) {\n        dispatch(actions.openElement(DataElements.ANNOTATION_POPUP));\n      }\n      if (isOfficeEditorMode) {\n        const trackedChangeId = annotation.getCustomData(OFFICE_EDITOR_TRACKED_CHANGE_KEY);\n        await core.getOfficeEditor().moveCursorToTrackedChange(trackedChangeId);\n        core.getOfficeEditor().freezeMainCursor();\n      }\n    }\n  };\n\n  const hasUnreadReplies = unreadReplyIdSet.size > 0;\n\n  const noteClass = classNames({\n    Note: true,\n    expanded: isSelected,\n    'is-multi-selected': isMultiSelected,\n    unread: unreadAnnotationIdSet.has(annotation.Id) || hasUnreadReplies,\n    'disabled': isOfficeEditorMode && officeEditorEditMode === OfficeEditorEditMode.PREVIEW,\n  });\n\n  const repliesClass = classNames({\n    replies: true,\n    hidden: !isSelected,\n  });\n\n  useEffect(() => {\n    // Must also restore the isEdit for  any replies, in case someone was editing a\n    // reply when a comment was placed above\n    if (!isMultiSelectMode) {\n      replies.forEach((reply, index) => {\n        const pendingText = pendingEditTextMap[reply.Id];\n        if ((pendingText !== '' && typeof pendingText !== 'undefined') && isSelected) {\n          setIsEditing(true, 1 + index);\n        }\n      });\n    }\n  }, [isSelected, isMultiSelectMode]);\n\n  useEffect(() => {\n    if (isMultiSelectMode) {\n      setIsEditing(false, 0);\n    }\n  }, [isMultiSelectMode]);\n\n  const showReplyArea = !Object.values(isEditingMap).some((val) => val);\n\n  const handleReplyClicked = (reply) => {\n    // set clicked reply as read\n    if (unreadReplyIdSet.has(reply.Id)) {\n      dispatch(actions.setAnnotationReadState({ isRead: true, annotationId: reply.Id }));\n      core.getAnnotationManager(documentViewerKey).selectAnnotation(reply);\n    }\n  };\n\n  const markAllRepliesRead = () => {\n    // set all replies to read state if user starts to type in reply textarea\n    if (unreadReplyIdSet.size > 0) {\n      const repliesSetToRead = replies.filter((r) => unreadReplyIdSet.has(r.Id));\n      core.getAnnotationManager(documentViewerKey).selectAnnotations(repliesSetToRead);\n      repliesSetToRead.forEach((r) => dispatch(actions.setAnnotationReadState({ isRead: true, annotationId: r.Id })));\n    }\n  };\n\n  const setIsEditing = useCallback(\n    (isEditing, index) => {\n      setIsEditingMap((map) => ({\n        ...map,\n        [index]: isEditing,\n      }));\n    },\n    [setIsEditingMap],\n  );\n\n  const groupAnnotations = core.getGroupAnnotations(annotation, documentViewerKey);\n  const isGroup = groupAnnotations.length > 1;\n  const isTrackedChange = mapAnnotationToKey(annotation) === annotationMapKeys.TRACKED_CHANGE;\n  // apply unread reply style to replyArea if the last reply is unread\n  const lastReplyId = replies.length > 0 ? replies[replies.length - 1].Id : null;\n\n  return (\n    <div\n      ref={containerRef}\n      className={noteClass}\n      id={`note_${annotation.Id}`}\n    >\n      <Button\n        className='note-button'\n        onClick={(e) => handleNoteClick(e)}\n        ariaLabelledby={`note_${annotation.Id}`}\n        ariaCurrent={isSelected}\n        dataElement=\"expandNoteButton\"\n      />\n      <NoteContent\n        noteIndex={0}\n        annotation={annotation}\n        setIsEditing={setIsEditing}\n        isEditing={isEditingMap[0]}\n        isNonReplyNoteRead={!unreadAnnotationIdSet.has(annotation.Id)}\n        isUnread={unreadAnnotationIdSet.has(annotation.Id) || hasUnreadReplies}\n        handleMultiSelect={(e) => {\n          setCurAnnotId(annotation.Id);\n          handleMultiSelect(e);\n        }}\n        isMultiSelected={isMultiSelected}\n        isMultiSelectMode={isMultiSelectMode}\n      />\n      {(isSelected || isExpandedFromSearch || shouldExpandCommentThread) && !isTrackedChange && (\n        <>\n          {replies.length > 0 && (\n            <div className={repliesClass}>\n              {hasUnreadReplies && (\n                <Button\n                  dataElement=\"markAllReadButton\"\n                  className=\"mark-all-read-button\"\n                  label={t('action.markAllRead')}\n                  onClick={markAllRepliesRead}\n                />\n              )}\n              {replies.map((reply, i) => (\n                <div className=\"reply\" id={`note_reply_${reply.Id}`} key={`note_reply_${reply.Id}`}>\n                  <NoteContent\n                    noteIndex={i + 1}\n                    key={reply.Id}\n                    annotation={reply}\n                    setIsEditing={setIsEditing}\n                    isEditing={isEditingMap[i + 1]}\n                    onReplyClicked={handleReplyClicked}\n                    isUnread={unreadAnnotationIdSet.has(reply.Id)}\n                    handleMultiSelect={handleMultiSelect}\n                    isMultiSelected={isMultiSelected}\n                    isMultiSelectMode={isMultiSelectMode}\n                  />\n                </div>\n              ))}\n            </div>\n          )}\n          {isGroup &&\n            <NoteGroupSection\n              groupAnnotations={groupAnnotations}\n              isMultiSelectMode={isMultiSelectMode}\n            />}\n          {showReplyArea && !isMultiSelectMode && (\n            <ReplyArea\n              isUnread={lastReplyId && unreadAnnotationIdSet.has(lastReplyId)}\n              onPendingReplyChange={markAllRepliesRead}\n              annotation={annotation}\n            />\n          )}\n        </>\n      )}\n      {isSelected && (isInNotesPanel || isCustomPanelOpen) && !shouldHideConnectorLine && (\n        <AnnotationNoteConnectorLine\n          annotation={annotation}\n          noteContainerRef={containerRef}\n          isCustomPanelOpen={isCustomPanelOpen}\n        />\n      )}\n    </div>\n  );\n};\n\nNote.propTypes = propTypes;\n\nexport default Note;\n", "import Note from './Note';\n\nexport default Note;\n", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./InlineCommentingPopup.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".open.InlineCommentingPopup{visibility:visible}.closed.InlineCommentingPopup{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.InlineCommentingPopup{position:absolute;z-index:70;display:flex;justify-content:center;align-items:center}.InlineCommentingPopup:empty{padding:0}.InlineCommentingPopup .buttons{display:flex}.InlineCommentingPopup .Button{margin:4px;width:32px;height:32px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .Button{width:42px;height:42px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .Button{width:42px;height:42px}}.InlineCommentingPopup .Button:hover{background:var(--popup-button-hover)}.InlineCommentingPopup .Button:hover:disabled{background:none}.InlineCommentingPopup .Button .Icon{width:18px;height:18px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .Button .Icon{width:24px;height:24px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .Button .Icon{width:24px;height:24px}}.is-vertical.InlineCommentingPopup .Button.main-menu-button{width:100%;border-radius:0;justify-content:flex-start;padding-left:var(--padding-small);padding-right:var(--padding-small);margin:0 0 var(--padding-tiny) 0}.is-vertical.InlineCommentingPopup .Button.main-menu-button:first-child{margin-top:var(--padding-tiny)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .is-vertical.InlineCommentingPopup .Button.main-menu-button{width:100%;height:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .is-vertical.InlineCommentingPopup .Button.main-menu-button{width:100%;height:32px}}.is-vertical.InlineCommentingPopup .Button.main-menu-button .Icon{margin-right:10px}.is-vertical.InlineCommentingPopup .Button.main-menu-button span{white-space:nowrap}@keyframes bottom-up{0%{transform:translateY(100%)}to{transform:translateY(0)}}@keyframes up-bottom{0%{transform:translateY(0)}to{transform:translateY(100%)}}.InlineCommentingPopup{border-radius:4px;box-shadow:0 0 3px 0 var(--document-box-shadow);background:var(--component-background);align-items:flex-start}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup{position:fixed;left:0;bottom:0;z-index:100;flex-direction:column;justify-content:flex-end;width:100%;background:var(--modal-negative-space)}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup{position:fixed;left:0;bottom:0;z-index:100;flex-direction:column;justify-content:flex-end;width:100%;background:var(--modal-negative-space)}}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup{overflow:auto;max-height:calc(100% - 100px)}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup{overflow:auto;max-height:calc(100% - 100px)}}.InlineCommentingPopup .inline-comment-container{display:flex;flex-direction:column}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .inline-comment-container{flex-basis:auto;width:100%;max-height:40%;background:var(--component-background);box-shadow:0 0 3px 0 var(--document-box-shadow);border-radius:4px 4px 0 0}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .inline-comment-container{flex-basis:auto;width:100%;max-height:40%;background:var(--component-background);box-shadow:0 0 3px 0 var(--document-box-shadow);border-radius:4px 4px 0 0}}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .inline-comment-container{width:260px}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .inline-comment-container{width:260px}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .inline-comment-container.expanded{flex-grow:1;max-height:90%}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .inline-comment-container.expanded{flex-grow:1;max-height:90%}}.InlineCommentingPopup .inline-comment-container .inline-comment-header{flex-grow:0;flex-shrink:0;display:flex;flex-direction:row;align-items:center}.InlineCommentingPopup .inline-comment-container .inline-comment-header .inline-comment-header-title{flex-grow:1;font-size:16px}.InlineCommentingPopup .inline-comment-container .inline-comment-header .Button{margin:4px}.InlineCommentingPopup .Note{border-radius:0;background:none;margin:0;cursor:default}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .Note{flex-grow:1;display:flex;flex-direction:column;overflow:auto;box-shadow:0 0 3px 0 var(--document-box-shadow)}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .Note{flex-grow:1;display:flex;flex-direction:column;overflow:auto;box-shadow:0 0 3px 0 var(--document-box-shadow)}}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .Note{box-shadow:none}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .Note{box-shadow:none}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .Note>div:not(:nth-last-child(2)){flex-grow:0}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .Note>div:not(:nth-last-child(2)){flex-grow:0}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .Note>div:nth-last-child(2){flex-grow:1}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .Note>div:nth-last-child(2){flex-grow:1}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .Note>.NoteContent:only-child{flex-grow:1}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .Note>.NoteContent:only-child{flex-grow:1}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .Note>.NoteContent:only-child .edit-content{flex-grow:0}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .Note>.NoteContent:only-child .edit-content{flex-grow:0}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .NoteHeader{flex-grow:0}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .NoteHeader{flex-grow:0}}.InlineCommentingPopup .NoteContent .edit-content{margin-top:16px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .ql-container,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .ql-editor,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .quill{font-size:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .ql-container,.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .ql-editor,.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .quill{font-size:16px}}.InlineCommentingPopup .Button,.InlineCommentingPopup .Button.add-attachment,.InlineCommentingPopup .Button.reply-button{margin:0}.InlineCommentingPopup .Button.add-attachment .Icon,.InlineCommentingPopup .Button.reply-button .Icon{width:22px;height:22px}.InlineCommentingPopup .Button.add-attachment{width:24px;height:24px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .Button.add-attachment{width:24px;height:24px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .Button.add-attachment{width:24px;height:24px}}.InlineCommentingPopup .Button.note-popup-toggle-trigger,.InlineCommentingPopup .Button.reply-button{width:28px;height:28px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .Button.note-popup-toggle-trigger,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .Button.reply-button{width:28px;height:28px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .Button.note-popup-toggle-trigger,.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .Button.reply-button{width:28px;height:28px}}.sb-show-main .InlineCommentingPopup .quill.comment-textarea{padding:0}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React, { useState } from 'react';\nimport Draggable from 'react-draggable';\nimport classNames from 'classnames';\nimport PropTypes from 'prop-types';\nimport { useTranslation } from 'react-i18next';\n\nimport NoteContext from 'components/Note/Context';\nimport Note from 'components/Note';\nimport ReplyAttachmentPicker from 'components/NotesPanel/ReplyAttachmentPicker';\nimport Button from 'components/Button';\n\nimport DataElements from 'src/constants/dataElement';\n\nimport { mapAnnotationToKey, annotationMapKeys } from 'constants/map';\n\nimport './InlineCommentingPopup.scss';\n\nconst propTypes = {\n  isMobile: PropTypes.bool,\n  isUndraggable: PropTypes.bool,\n  isNotesPanelClosed: PropTypes.bool,\n  popupRef: PropTypes.any,\n  position: PropTypes.object,\n  closeAndReset: PropTypes.func,\n  commentingAnnotation: PropTypes.object,\n  contextValue: PropTypes.object,\n  annotationForAttachment: PropTypes.string,\n  addAttachments: PropTypes.func,\n};\n\nconst InlineCommentingPopup = ({\n  isMobile,\n  isUndraggable,\n  isNotesPanelClosed,\n  popupRef,\n  position,\n  closeAndReset,\n  commentingAnnotation,\n  contextValue,\n  annotationForAttachment,\n  addAttachments,\n}) => {\n  const [t] = useTranslation();\n  const [isExpanded, setExpanded] = useState(false);\n\n  const isTrackedChange = mapAnnotationToKey(commentingAnnotation) === annotationMapKeys.TRACKED_CHANGE;\n\n  const inlineCommentPopup = (\n    <div\n      className={classNames({\n        Popup: true,\n        InlineCommentingPopup: true,\n        open: isNotesPanelClosed,\n        trackedChangePopup: isTrackedChange,\n      })}\n      ref={popupRef}\n      data-element={DataElements.INLINE_COMMENT_POPUP}\n      style={{ ...position }}\n      onMouseMove={(e) => {\n        e.stopPropagation();\n      }}\n      onMouseDown={(e) => {\n        if (isMobile) {\n          e.stopPropagation();\n          closeAndReset();\n        }\n      }}\n      onKeyDown={(e) => {\n        if (e.key === 'Escape') {\n          closeAndReset();\n        }\n      }}\n    >\n      <div\n        className={classNames({\n          'inline-comment-container': true,\n          'expanded': isExpanded,\n        })}\n        onMouseDown={(e) => {\n          if (isMobile) {\n            e.stopPropagation();\n          }\n        }}\n      >\n        {isMobile && (\n          <div className='inline-comment-header'>\n            <Button\n              img={isExpanded ? 'icon-chevron-down' : 'icon-chevron-up'}\n              className=\"expand-arrow\"\n              dataElement={DataElements.INLINE_COMMENT_POPUP_EXPAND_BUTTON}\n              onClick={() => setExpanded(!isExpanded)}\n            />\n            <span className='inline-comment-header-title'>{t('action.comment')}</span>\n            <Button\n              img={'icon-close'}\n              dataElement={DataElements.INLINE_COMMENT_POPUP_CLOSE_BUTTON}\n              onClick={closeAndReset}\n            />\n          </div>\n        )}\n        <NoteContext.Provider value={contextValue}>\n          <Note\n            annotation={commentingAnnotation}\n            isMultiSelected={false}\n            isMultiSelectMode={false}\n            handleMultiSelect={() => { }}\n          />\n          <ReplyAttachmentPicker\n            annotationId={annotationForAttachment}\n            addAttachments={addAttachments}\n          />\n        </NoteContext.Provider>\n      </div>\n    </div>\n  );\n\n  return isUndraggable || isTrackedChange ? (\n    inlineCommentPopup\n  ) : (\n    <Draggable cancel=\".Button, .cell, svg, select, button, input, .quill, .note-text-preview\">{inlineCommentPopup}</Draggable>\n  );\n};\n\nInlineCommentingPopup.propTypes = propTypes;\n\nexport default InlineCommentingPopup;\n", "import React, { useState, useRef, useCallback, useLayoutEffect } from 'react';\nimport { useSelector, useDispatch, shallowEqual } from 'react-redux';\nimport InlineCommentingPopup from './InlineCommentingPopup';\nimport core from 'core';\nimport { getAnnotationPopupPositionBasedOn as getPopupPosition } from 'helpers/getPopupPosition';\nimport { getOpenedWarningModal, getOpenedColorPicker, getDatePicker } from 'helpers/getElements';\nimport useOnClickOutside from 'hooks/useOnClickOutside';\nimport actions from 'actions';\nimport selectors from 'selectors';\nimport { isMobile as isPhone, isIE, isMobileDevice } from 'helpers/device';\nimport DataElements from 'constants/dataElement';\nimport getRootNode from 'helpers/getRootNode';\nimport debounce from 'lodash/debounce';\nimport PropTypes from 'prop-types';\nimport { workerTypes } from 'constants/types';\n\nconst propTypes = {\n  annotation: PropTypes.object,\n  closeAndReset: PropTypes.func,\n};\n\nconst InlineCommentingPopupContainer = ({ annotation, closeAndReset }) => {\n  const [\n    isNotesPanelOpen,\n    notesInLeftPanel,\n    isLeftPanelOpen,\n    activeLeftPanel,\n    showAnnotationNumbering,\n    sortStrategy,\n    isDocumentReadOnly,\n    activeDocumentViewerKey,\n  ] = useSelector(\n    (state) => [\n      selectors.isElementOpen(state, DataElements.NOTES_PANEL),\n      selectors.getNotesInLeftPanel(state),\n      selectors.isElementOpen(state, DataElements.LEFT_PANEL),\n      selectors.getActiveLeftPanel(state),\n      selectors.isAnnotationNumberingEnabled(state),\n      selectors.getSortStrategy(state),\n      selectors.isDocumentReadOnly(state),\n      selectors.getActiveDocumentViewerKey(state),\n    ],\n    shallowEqual,\n  );\n  const dispatch = useDispatch();\n  const [position, setPosition] = useState({ left: 0, top: 0 });\n  const popupRef = useRef();\n  // on tablet, the behaviour will be like on desktop, except being draggable\n  const isMobile = isPhone();\n  const isUndraggable = isMobile || !!isMobileDevice || isIE;\n  const isNotesPanelOpenOrActive = isNotesPanelOpen || (notesInLeftPanel && isLeftPanelOpen && activeLeftPanel === 'notesPanel');\n  const sixtyFramesPerSecondIncrement = 16;\n\n  useOnClickOutside(popupRef, (e) => {\n    const notesPanel = getRootNode().querySelector('[data-element=\"notesPanel\"]');\n    const clickedInNotesPanel = notesPanel?.contains(e.target);\n    const noteStateFlyout = getRootNode().querySelector(`[data-element=\"noteStateFlyout-${annotation.Id}\"]`);\n    const clickedInNoteStateFlyout = noteStateFlyout?.contains(e.target);\n    const datePicker = getDatePicker();\n    const warningModal = getOpenedWarningModal();\n    const colorPicker = getOpenedColorPicker();\n\n    // the notes panel has mousedown handlers to handle the opening/closing states of this component\n    // we don't want this handler to run when clicked in the notes panel otherwise the opening/closing states may mess up\n    // for example: click on a note will call core.selectAnnotation which triggers the annotationSelected event\n    // and opens this component. If we don't exclude the notes panel this handler will run and close it after\n    if (!clickedInNotesPanel && !clickedInNoteStateFlyout && !warningModal && !colorPicker && !datePicker) {\n      dispatch(actions.closeElement(DataElements.INLINE_COMMENT_POPUP));\n    }\n  });\n\n  const isNotesPanelClosed = !isNotesPanelOpenOrActive;\n\n  const setPopupPosition = () => {\n    if (isNotesPanelClosed && popupRef.current && !isMobile) {\n      setPosition(getPopupPosition(annotation, popupRef, activeDocumentViewerKey));\n    }\n  };\n\n  useLayoutEffect(() => {\n    setPopupPosition();\n  }, [activeDocumentViewerKey, annotation]);\n\n  const handleResize = debounce(() => {\n    setPopupPosition();\n  }, sixtyFramesPerSecondIncrement, { 'trailing': true, 'leading': false });\n\n  useLayoutEffect(() => {\n    window.addEventListener('resize', handleResize);\n\n    return () => {\n      window.removeEventListener('resize', handleResize);\n    };\n  }, []);\n\n  // TO-DO refactor: Lines 189-239 was copied from NotesPanel 228-275\n  const [pendingAttachmentMap, setPendingAttachmentMap] = useState({});\n  const addAttachments = (annotationID, attachments) => {\n    setPendingAttachmentMap((map) => ({\n      ...map,\n      [annotationID]: [...(map[annotationID] || []), ...attachments]\n    }));\n  };\n\n  const [annotationForAttachment, setAnnotationForAttachment] = useState(undefined);\n\n  const [pendingEditTextMap, setPendingEditTextMap] = useState({});\n  const setPendingEditText = useCallback(\n    (pendingText, annotationID) => {\n      setPendingEditTextMap((map) => ({\n        ...map,\n        [annotationID]: pendingText,\n      }));\n    },\n    [setPendingEditTextMap],\n  );\n\n  const [pendingReplyMap, setPendingReplyMap] = useState({});\n  const setPendingReply = useCallback(\n    (pendingReply, annotationID) => {\n      setPendingReplyMap((map) => ({\n        ...map,\n        [annotationID]: pendingReply,\n      }));\n    },\n    [setPendingReplyMap],\n  );\n  const clearAttachments = (annotationID) => {\n    setPendingAttachmentMap((map) => ({\n      ...map,\n      [annotationID]: []\n    }));\n  };\n  const deleteAttachment = (annotationID, attachment) => {\n    const attachmentList = pendingAttachmentMap[annotationID];\n    if (attachmentList?.length > 0) {\n      const index = attachmentList.indexOf(attachment);\n      if (index > -1) {\n        attachmentList.splice(index, 1);\n        setPendingAttachmentMap((map) => ({\n          ...map,\n          [annotationID]: [...attachmentList]\n        }));\n      }\n    }\n  };\n\n  const contextValue = {\n    searchInput: '',\n    resize: () => {\n      if (core.getDocument()?.getType() === workerTypes.OFFICE_EDITOR) {\n        setPosition(getPopupPosition(annotation, popupRef, activeDocumentViewerKey));\n      }\n    },\n    isSelected: true,\n    isContentEditable: core.canModifyContents(annotation) && !annotation.getContents(),\n    pendingEditTextMap,\n    setPendingEditText,\n    pendingReplyMap,\n    setPendingReply,\n    isDocumentReadOnly,\n    onTopNoteContentClicked: () => { },\n    isExpandedFromSearch: false,\n    scrollToSelectedAnnot: false,\n    sortStrategy,\n    showAnnotationNumbering,\n    setCurAnnotId: setAnnotationForAttachment,\n    pendingAttachmentMap,\n    addAttachments,\n    clearAttachments,\n    deleteAttachment,\n  };\n\n  return (\n    <InlineCommentingPopup\n      isMobile={isMobile}\n      isUndraggable={isUndraggable}\n      isNotesPanelClosed={isNotesPanelClosed}\n      popupRef={popupRef}\n      position={position}\n      closeAndReset={closeAndReset}\n      commentingAnnotation={annotation}\n      contextValue={contextValue}\n      annotationForAttachment={annotationForAttachment}\n      addAttachments={addAttachments}\n    />\n  );\n};\n\nInlineCommentingPopupContainer.propTypes = propTypes;\n\nexport default InlineCommentingPopupContainer;\n", "import InlineCommentingPopupContainer from './InlineCommentingPopupContainer';\n\nexport default InlineCommentingPopupContainer;"], "sourceRoot": ""}