{"version": 3, "sources": ["webpack:///./src/ui/src/components/LinkModal/LinkModal.scss?9ee2", "webpack:///./src/ui/src/components/LinkModal/LinkModal.scss"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,sgTAAugT,KAGhiT0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB", "file": "chunks/chunk.20.js", "sourcesContent": ["var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./LinkModal.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".open.LinkModal{visibility:visible}.closed.LinkModal{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.LinkModal .footer .modal-button.confirm:hover{background:var(--primary-button-hover);border-color:var(--primary-button-hover);color:var(--gray-0)}.LinkModal .footer .modal-button.confirm{background:var(--primary-button);border-color:var(--primary-button);color:var(--primary-button-text)}.LinkModal .footer .modal-button.confirm.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.LinkModal .footer .modal-button.confirm.disabled span{color:var(--primary-button-text)}.LinkModal .footer .modal-button.cancel:hover,.LinkModal .footer .modal-button.secondary-btn-custom:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6)}.LinkModal .footer .modal-button.cancel,.LinkModal .footer .modal-button.secondary-btn-custom{border:none;box-shadow:inset 0 0 0 1px var(--primary-button);color:var(--primary-button)}.LinkModal .footer .modal-button.cancel.disabled,.LinkModal .footer .modal-button.secondary-btn-custom.disabled{cursor:default;border:none;box-shadow:inset 0 0 0 1px rgba(43,115,171,.5);color:rgba(43,115,171,.5)}.LinkModal .footer .modal-button.cancel.disabled span,.LinkModal .footer .modal-button.secondary-btn-custom.disabled span{color:rgba(43,115,171,.5)}.LinkModal{position:fixed;left:0;bottom:0;z-index:100;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background:var(--modal-negative-space)}.LinkModal .modal-container .wrapper .modal-content{padding:10px}.LinkModal .footer{display:flex;flex-direction:row;justify-content:flex-end;width:100%;margin-top:13px}.LinkModal .footer.modal-footer{padding:16px;margin:0;border-top:1px solid var(--divider)}.LinkModal .footer .modal-button{display:flex;justify-content:center;align-items:center;padding:6px 18px;margin:8px 0 0;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;height:30px;cursor:pointer}.LinkModal .footer .modal-button.confirm{margin-left:4px}.LinkModal .footer .modal-button.secondary-btn-custom{border-radius:4px;padding:2px 20px 4px;cursor:pointer}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .LinkModal .footer .modal-button{padding:23px 8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .LinkModal .footer .modal-button{padding:23px 8px}}.LinkModal .swipe-indicator{background:var(--swipe-indicator-bg);border-radius:2px;height:4px;width:38px;position:absolute;top:12px;margin-left:auto;margin-right:auto;left:0;right:0}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .LinkModal .swipe-indicator{display:none}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .LinkModal .swipe-indicator{display:none}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .LinkModal .swipe-indicator{width:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .LinkModal .swipe-indicator{width:32px}}.LinkModal .container .tab-list{width:100%;height:28px;display:flex;border-radius:4px;color:var(--text-color)}.LinkModal .container .tab-list .tab-options-button{background-color:transparent;text-align:center;vertical-align:middle;line-height:24px;text-overflow:ellipsis;overflow:hidden;white-space:nowrap;flex:1;border-radius:0;cursor:pointer}.LinkModal .container .tab-list .tab-options-button:first-child{border-bottom-left-radius:4px;border-top-left-radius:4px}.LinkModal .container .tab-list .tab-options-button:last-child{border-bottom-right-radius:4px;border-top-right-radius:4px}.LinkModal .container .tab-list .tab-options-button:hover{background:var(--popup-button-hover)}.LinkModal .container .tab-list .tab-options-button.selected{cursor:default}.LinkModal .container .tab-list .tab-options-button.focus-visible,.LinkModal .container .tab-list .tab-options-button:focus-visible{outline:var(--focus-visible-outline)}.LinkModal .container .tab-panel{width:100%;display:flex;flex-direction:column;align-items:center}.LinkModal .container .tab-panel.focus-visible,.LinkModal .container .tab-panel:focus-visible{outline:var(--focus-visible-outline)!important}.LinkModal{font-size:.9em}.LinkModal .container{display:flex;flex-direction:column;align-items:center;min-width:400px;width:480px;border-radius:4px;background:var(--component-background)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .LinkModal .container{padding:24px 24px 16px;width:100%;min-width:100px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .LinkModal .container{padding:24px 24px 16px;width:100%;min-width:100px}}.LinkModal .container .StylePopup{border-radius:0;box-shadow:none}.LinkModal .container .divider-horizontal{width:100%;height:1px;margin-bottom:8px;background:var(--divider)}.LinkModal .container .linkInput{display:flex;flex-direction:row;justify-content:space-between}.LinkModal .container input::-webkit-inner-spin-button,.LinkModal .container input::-webkit-outer-spin-button{-webkit-appearance:none;margin:0}.LinkModal .container input[type=number]{-moz-appearance:textfield}.LinkModal .container form{width:100%}.LinkModal .container .tabs-header-container{padding:16px;width:100%}.LinkModal .container .header{margin:0;display:flex;align-items:center;width:100%;height:24px}.LinkModal .container .header label,.LinkModal .container .header p{font-size:16px;font-weight:700;width:89.286%;margin:0 16px 0 0}.LinkModal .container .tab-list{font-size:14px}.LinkModal .container .tab-panel{overflow-y:visible}.LinkModal .container .tab-panel .panel-body{width:100%;height:140px;position:relative;padding:0 16px 16px;font-size:13px;border-radius:4px}.LinkModal .container .tab-panel .panel-body .add-url-link{display:flex;justify-content:center;height:100%;flex-direction:column;background-color:var(--document-background-color);border-radius:4px}.LinkModal .container .tab-panel .panel-body .add-url-link .inputLabel{font-size:13px;line-height:16px;padding-left:16px;padding-bottom:8px;font-weight:700}.LinkModal .container .tab-panel .panel-body .add-url-link label.inputLabel{width:100%;display:inline-block}.LinkModal .container .tab-panel .panel-body .add-url-link .linkInput{padding:0 16px}.LinkModal .container .tab-panel .panel-body .add-url-link .linkInput input{width:100%;height:32px;padding-left:8px;font-size:13px;box-sizing:border-box}.LinkModal .container .footer{display:flex;padding:16px;align-items:center;justify-content:flex-end;width:100%;box-shadow:inset 0 1px 0 var(--modal-stroke-and-border);margin:0}.LinkModal .container .footer .ok-button.Button{display:flex;justify-content:center;align-items:center;background-color:var(--primary-button);border:none;color:var(--gray-0);padding:6px 18px;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;height:28px;cursor:pointer}.LinkModal .container .footer .ok-button.Button:hover:not(:disabled){background:var(--primary-button-hover)}.LinkModal .container .footer .ok-button.Button.disabled{background:var(--disabled-button-color)!important;cursor:not-allowed}.LinkModal .container .footer .ok-button.Button.disabled span{color:var(--gray-0)}.LinkModal .container input.pageInput:focus,.LinkModal .container input.urlInput:focus{border-color:var(--outline-color)}.LinkModal .container input.pageInput:active,.LinkModal .container input.urlInput:active{border-color:var(--focus-border)}.LinkModal .container .tab-list .tab-options-divider+.tab-options-button{border-left:none!important}.LinkModal .container .tab-list .tab-options-button{border-top:1px solid var(--tab-border-color);border-bottom:1px solid var(--tab-border-color)}.LinkModal .container .tab-list .tab-options-button:first-child{border-left:1px solid var(--tab-border-color)}.LinkModal .container .tab-list .tab-options-button:last-child{border-right:1px solid var(--tab-border-color)}.LinkModal .container .tab-list .tab-options-button:hover{background:var(--tab-background-color-hover);border-top:1px solid var(--tab-border-color-hover);border-bottom:1px solid var(--tab-border-color-hover);border-right:1px solid var(--tab-border-color-hover)}.LinkModal .container .tab-list .tab-options-button:hover+button,.LinkModal .container .tab-list .tab-options-button:hover+div{border-left:none}.LinkModal .container .tab-list .tab-options-button.selected{background:var(--tab-color-selected);border:1px solid var(--tab-color-selected);color:var(--tab-text-color-selected)}.LinkModal .container .tab-list .tab-options-button.selected+button,.LinkModal .container .tab-list .tab-options-button.selected+div{border-left:none!important}.LinkModal .container .tab-list .tab-options-button:not(.selected){border-right:1px solid var(--tab-border-color)}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};"], "sourceRoot": ""}