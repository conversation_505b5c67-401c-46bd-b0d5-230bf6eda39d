{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/bn-bd.js"], "names": ["module", "exports", "e", "_", "default", "t", "n", "1", "2", "3", "4", "5", "6", "7", "8", "9", "0", "d", "r", "name", "weekdays", "split", "months", "weekdaysShort", "monthsShort", "weekdaysMin", "weekStart", "preparse", "replace", "postformat", "ordinal", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "meridiem", "relativeTime", "future", "past", "s", "m", "mm", "h", "hh", "dd", "M", "MM", "y", "yy", "locale"], "mappings": "gFAAoEA,EAAOC,QAAgL,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAGI,EAAE,CAACC,EAAE,IAAIC,EAAE,IAAIC,EAAE,IAAIC,EAAE,IAAIC,EAAE,IAAIC,EAAE,IAAIC,EAAE,IAAIC,EAAE,IAAIC,EAAE,IAAIC,EAAE,KAAKC,EAAE,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAKC,EAAE,CAACC,KAAK,QAAQC,SAAS,4DAA4DC,MAAM,KAAKC,OAAO,0FAA0FD,MAAM,KAAKE,cAAc,uCAAuCF,MAAM,KAAKG,YAAY,mEAAmEH,MAAM,KAAKI,YAAY,kCAAkCJ,MAAM,KAAKK,UAAU,EAAEC,SAAS,SAASzB,GAAG,OAAOA,EAAE0B,QAAQ,iBAAgB,SAAU1B,GAAG,OAAOe,EAAEf,OAAO2B,WAAW,SAAS3B,GAAG,OAAOA,EAAE0B,QAAQ,OAAM,SAAU1B,GAAG,OAAOI,EAAEJ,OAAO4B,QAAQ,SAAS5B,GAAG,IAAIG,EAAE,CAAC,IAAI,KAAK,KAAK,KAAK,MAAMF,EAAED,EAAE,IAAI,MAAM,IAAIA,GAAGG,GAAGF,EAAE,IAAI,KAAKE,EAAEF,IAAIE,EAAE,IAAI,KAAK0B,QAAQ,CAACC,GAAG,aAAaC,IAAI,gBAAgBC,EAAE,yBAAyBC,GAAG,0BAA0BC,IAAI,sCAAsCC,KAAK,6CAA6CC,SAAS,SAASpC,GAAG,OAAOA,EAAE,EAAE,MAAMA,EAAE,EAAE,MAAMA,EAAE,GAAG,OAAOA,EAAE,GAAG,QAAQA,EAAE,GAAG,QAAQA,EAAE,GAAG,UAAU,OAAOqC,aAAa,CAACC,OAAO,SAASC,KAAK,SAASC,EAAE,eAAeC,EAAE,WAAWC,GAAG,WAAWC,EAAE,WAAWC,GAAG,WAAW7B,EAAE,SAAS8B,GAAG,SAASC,EAAE,SAASC,GAAG,SAASC,EAAE,SAASC,GAAG,WAAW,OAAOhD,EAAEC,QAAQgD,OAAOlC,EAAE,MAAK,GAAIA,EAApmDb,CAAE,EAAQ", "file": "chunks/chunk.115.js", "sourcesContent": ["!function(e,t){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=t(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],t):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_bn_bd=t(e.dayjs)}(this,(function(e){\"use strict\";function t(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var _=t(e),n={1:\"১\",2:\"২\",3:\"৩\",4:\"৪\",5:\"৫\",6:\"৬\",7:\"৭\",8:\"৮\",9:\"৯\",0:\"০\"},d={\"১\":\"1\",\"২\":\"2\",\"৩\":\"3\",\"৪\":\"4\",\"৫\":\"5\",\"৬\":\"6\",\"৭\":\"7\",\"৮\":\"8\",\"৯\":\"9\",\"০\":\"0\"},r={name:\"bn-bd\",weekdays:\"রবিবার_সোমবার_মঙ্গলবার_বুধবার_বৃহস্পতিবার_শুক্রবার_শনিবার\".split(\"_\"),months:\"জানুয়ারি_ফেব্রুয়ারি_মার্চ_এপ্রিল_মে_জুন_জুলাই_আগস্ট_সেপ্টেম্বর_অক্টোবর_নভেম্বর_ডিসেম্বর\".split(\"_\"),weekdaysShort:\"রবি_সোম_মঙ্গল_বুধ_বৃহস্পতি_শুক্র_শনি\".split(\"_\"),monthsShort:\"জানু_ফেব্রু_মার্চ_এপ্রিল_মে_জুন_জুলাই_আগস্ট_সেপ্ট_অক্টো_নভে_ডিসে\".split(\"_\"),weekdaysMin:\"রবি_সোম_মঙ্গ_বুধ_বৃহঃ_শুক্র_শনি\".split(\"_\"),weekStart:0,preparse:function(e){return e.replace(/[১২৩৪৫৬৭৮৯০]/g,(function(e){return d[e]}))},postformat:function(e){return e.replace(/\\d/g,(function(e){return n[e]}))},ordinal:function(e){var t=[\"ই\",\"লা\",\"রা\",\"ঠা\",\"শে\"],_=e%100;return\"[\"+e+(t[(_-20)%10]||t[_]||t[0])+\"]\"},formats:{LT:\"A h:mm সময়\",LTS:\"A h:mm:ss সময়\",L:\"DD/MM/YYYY খ্রিস্টাব্দ\",LL:\"D MMMM YYYY খ্রিস্টাব্দ\",LLL:\"D MMMM YYYY খ্রিস্টাব্দ, A h:mm সময়\",LLLL:\"dddd, D MMMM YYYY খ্রিস্টাব্দ, A h:mm সময়\"},meridiem:function(e){return e<4?\"রাত\":e<6?\"ভোর\":e<12?\"সকাল\":e<15?\"দুপুর\":e<18?\"বিকাল\":e<20?\"সন্ধ্যা\":\"রাত\"},relativeTime:{future:\"%s পরে\",past:\"%s আগে\",s:\"কয়েক সেকেন্ড\",m:\"এক মিনিট\",mm:\"%d মিনিট\",h:\"এক ঘন্টা\",hh:\"%d ঘন্টা\",d:\"এক দিন\",dd:\"%d দিন\",M:\"এক মাস\",MM:\"%d মাস\",y:\"এক বছর\",yy:\"%d বছর\"}};return _.default.locale(r,null,!0),r}));"], "sourceRoot": ""}