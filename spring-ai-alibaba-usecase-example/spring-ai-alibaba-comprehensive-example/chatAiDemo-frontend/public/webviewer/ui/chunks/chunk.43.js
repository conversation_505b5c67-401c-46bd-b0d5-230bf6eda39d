(window.webpackJsonp=window.webpackJsonp||[]).push([[43],{1524:function(e,n,t){"use strict";var i=t(0),o=t.n(i),a=t(4),r=t.n(a),l=(t(1534),{renderContent:r.a.func,children:r.a.node}),d=function(e){var n=e.renderContent?e.renderContent():e.children;return o.a.createElement("h4",{className:"ListSeparator"},n)};d.propTypes=l;var c=o.a.memo(d);n.a=c},1534:function(e,n,t){var i=t(32),o=t(1535);"string"==typeof(o=o.__esModule?o.default:o)&&(o=[[e.i,o,""]]);var a={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let n;n=document.getElementsByTagName("apryse-webviewer"),n.length||(n=function e(n,t=document){const i=[];return t.querySelectorAll(n).forEach(e=>i.push(e)),t.querySelectorAll("*").forEach(t=>{t.shadowRoot&&i.push(...e(n,t.shadowRoot))}),i}("apryse-webviewer"));const t=[];for(let i=0;i<n.length;i++){const o=n[i];if(0===i)o.shadowRoot.appendChild(e),e.onload=function(){t.length>0&&t.forEach(n=>{n.innerHTML=e.innerHTML})};else{const n=e.cloneNode(!0);o.shadowRoot.appendChild(n),t.push(n)}}},singleton:!1};i(o,a);e.exports=o.locals||{}},1535:function(e,n,t){(n=e.exports=t(33)(!1)).push([e.i,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.ListSeparator{margin-top:16px;margin-bottom:8px;font-family:Lato;font-weight:500;color:var(--list-separator-color);-webkit-user-select:none;-moz-user-select:none;user-select:none}",""]),n.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1732:function(e,n,t){"use strict";n.a=function(e){switch(e.errorType){case"empty":return"formField.formFieldPopup.invalidField.empty";case"duplicate":return"formField.formFieldPopup.invalidField.duplicate"}}},1940:function(e,n,t){var i=t(32),o=t(1941);"string"==typeof(o=o.__esModule?o.default:o)&&(o=[[e.i,o,""]]);var a={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let n;n=document.getElementsByTagName("apryse-webviewer"),n.length||(n=function e(n,t=document){const i=[];return t.querySelectorAll(n).forEach(e=>i.push(e)),t.querySelectorAll("*").forEach(t=>{t.shadowRoot&&i.push(...e(n,t.shadowRoot))}),i}("apryse-webviewer"));const t=[];for(let i=0;i<n.length;i++){const o=n[i];if(0===i)o.shadowRoot.appendChild(e),e.onload=function(){t.length>0&&t.forEach(n=>{n.innerHTML=e.innerHTML})};else{const n=e.cloneNode(!0);o.shadowRoot.appendChild(n),t.push(n)}}},singleton:!1};i(o,a);e.exports=o.locals||{}},1941:function(e,n,t){(n=e.exports=t(33)(!1)).push([e.i,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.index-panel-container{z-index:65;flex:1;display:flex;flex-direction:column;position:relative;overflow:hidden}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .index-panel-container{padding:var(--padding);padding-top:0}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .index-panel-container .close-container{display:flex;align-items:center;justify-content:flex-end;height:28px;margin-bottom:8px}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .index-panel-container .close-container .close-icon-container{padding:0;border:none;background-color:transparent;z-index:3;cursor:pointer}:host(:not([data-tabbing=true])) .App:not(.is-in-desktop-only-mode):not(.is-web-component) .index-panel-container .close-container .close-icon-container,html:not([data-tabbing=true]) .App:not(.is-in-desktop-only-mode):not(.is-web-component) .index-panel-container .close-container .close-icon-container{outline:none}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .index-panel-container .close-container .close-icon-container .close-icon{width:24px;height:24px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .index-panel-container{padding:var(--padding);padding-top:0}.App.is-web-component:not(.is-in-desktop-only-mode) .index-panel-container .close-container{display:flex;align-items:center;justify-content:flex-end;height:28px;margin-bottom:8px}.App.is-web-component:not(.is-in-desktop-only-mode) .index-panel-container .close-container .close-icon-container{padding:0;border:none;background-color:transparent;z-index:3;cursor:pointer}:host(:not([data-tabbing=true])) .App.is-web-component:not(.is-in-desktop-only-mode) .index-panel-container .close-container .close-icon-container,html:not([data-tabbing=true]) .App.is-web-component:not(.is-in-desktop-only-mode) .index-panel-container .close-container .close-icon-container{outline:none}.App.is-web-component:not(.is-in-desktop-only-mode) .index-panel-container .close-container .close-icon-container .close-icon{width:24px;height:24px}}.index-panel-container .index-page-container{padding:0;margin:0}.index-panel-container .index-page-container .ListSeparator{color:var(--gray-9);font-weight:700}.index-panel-container .index-panel-footer{border-top:1.5px solid var(--gray-4);padding-top:var(--padding-medium);padding-bottom:var(--padding-medium);display:flex;justify-content:center;align-items:center}.index-panel-container .title-container{display:flex;flex-flow:row nowrap;justify-content:space-between;align-items:center;padding-bottom:var(--padding-tiny)}.index-panel-container .title-container .field-control-button{width:auto}.index-panel-container .title-container .field-control-button span{color:var(--blue-5)}.index-panel-container .title-container .field-control-button span:hover{color:var(--blue-6)}.index-panel-container .title-container .field-control-button,.index-panel-container .title-container .field-control-button.disabled,.index-panel-container .title-container .field-control-button[disabled]{color:var(--secondary-button-text)}.index-panel-container .title-container .field-control-button.disabled,.index-panel-container .title-container .field-control-button[disabled]{opacity:.5}.index-panel-container .title-container .field-control-button.disabled span,.index-panel-container .title-container .field-control-button[disabled] span{color:inherit}.index-panel-container .title-container .field-control-button:not(.disabled):active,.index-panel-container .title-container .field-control-button:not(.disabled):focus,.index-panel-container .title-container .field-control-button:not(.disabled):hover,.index-panel-container .title-container .field-control-button:not([disabled]):active,.index-panel-container .title-container .field-control-button:not([disabled]):focus,.index-panel-container .title-container .field-control-button:not([disabled]):hover{color:var(--blue-6)}.index-panel-container .fields-counter{font-size:16px}.index-panel-container .fields-counter span{font-weight:700}.index-panel-container .multi-selection-button{width:auto;padding:7px}.index-panel-container .multi-selection-button .Icon{width:18px;height:18px}.index-panel-container .multi-selection-button:not(:first-child){margin-left:var(--padding-tiny)}.index-panel-container .multi-selection-button.disabled:hover,.index-panel-container .multi-selection-button[disabled]:hover{border:none;box-shadow:none}.index-panel-container .multi-selection-button:not(.disabled):hover,.index-panel-container .multi-selection-button:not([disabled]):hover{background-color:var(--gray-2);border:1px solid var(--blue-6)}.index-panel-container .IndexPanel{width:100%;display:flex;flex-direction:column;position:relative;flex-grow:1;overflow:auto;padding:16px 0 0}.index-panel-container .IndexPanel .multi-select-place-holder{height:72px}.index-panel-container .IndexPanel .no-fields{display:flex;flex-direction:column;align-items:center}.index-panel-container .IndexPanel .no-fields .msg{text-align:center}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .index-panel-container .IndexPanel .no-fields .msg{line-height:15px;width:146px}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .index-panel-container .IndexPanel .no-fields .msg{line-height:15px;width:146px}}.index-panel-container .IndexPanel .no-fields .empty-icon,.index-panel-container .IndexPanel .no-fields .empty-icon svg{width:65px;height:83px}.index-panel-container .IndexPanel .no-fields .empty-icon *{fill:var(--gray-5);color:var(--gray-5)}.index-panel-container .IndexPanel .no-results{display:flex;flex-direction:column;align-items:center;padding-right:18px}.index-panel-container .IndexPanel .no-results .msg{text-align:center}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .index-panel-container .IndexPanel .no-results .msg{line-height:15px;width:92px}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .index-panel-container .IndexPanel .no-results .msg{line-height:15px;width:92px}}.index-panel-container .IndexPanel .no-results .empty-icon,.index-panel-container .IndexPanel .no-results .empty-icon svg{width:65px;height:83px}.index-panel-container .IndexPanel .no-results .empty-icon *{fill:var(--border);color:var(--border)}",""]),n.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1942:function(e,n,t){var i=t(32),o=t(1943);"string"==typeof(o=o.__esModule?o.default:o)&&(o=[[e.i,o,""]]);var a={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let n;n=document.getElementsByTagName("apryse-webviewer"),n.length||(n=function e(n,t=document){const i=[];return t.querySelectorAll(n).forEach(e=>i.push(e)),t.querySelectorAll("*").forEach(t=>{t.shadowRoot&&i.push(...e(n,t.shadowRoot))}),i}("apryse-webviewer"));const t=[];for(let i=0;i<n.length;i++){const o=n[i];if(0===i)o.shadowRoot.appendChild(e),e.onload=function(){t.length>0&&t.forEach(n=>{n.innerHTML=e.innerHTML})};else{const n=e.cloneNode(!0);o.shadowRoot.appendChild(n),t.push(n)}}},singleton:!1};i(o,a);e.exports=o.locals||{}},1943:function(e,n,t){(e.exports=t(33)(!1)).push([e.i,".index-drag-container .index-panel-single-container{display:flex;flex-flow:row nowrap;align-items:center;border-radius:4px;margin-top:6px;margin-bottom:6px;padding:var(--padding-small) var(--padding-tiny);min-height:32px}.index-drag-container .index-panel-single-container .index-panel_content-container{display:flex;flex-flow:row nowrap;flex-grow:1;flex-shrink:1;align-items:center}.index-drag-container .index-panel-single-container .index-panel_content-container .ui__input__messageText{margin-left:8px;font-size:13px;font-style:normal;font-weight:400;line-height:normal;color:var(--red)}.index-drag-container .index-panel-single-container .index-panel_content-container .ui__input--message-warning{border-color:var(--red)}.index-drag-container .index-panel-single-container .index-panel-label-row{flex-grow:1;flex-shrink:1;display:flex;flex-flow:row wrap;align-items:flex-start;position:relative;overflow:hidden;padding-left:var(--padding-large)}.index-drag-container .index-panel-single-container .index-panel-label{font-weight:600;flex:1 0 100%;margin-bottom:var(--padding-small)}.index-drag-container .index-panel-single-container .type-icon-container{padding-right:13px;align-self:start;height:30px;display:flex;flex-direction:row;align-items:center}.index-drag-container .index-panel-single-container .index-panel-more-button{display:none;flex-grow:0;flex-shrink:0;width:auto;height:auto;margin-left:var(--padding-tiny);margin-right:0}.index-drag-container .index-panel-single-container .index-panel-more-button .Icon{width:14px;height:14px}.index-drag-container .index-panel-single-container.default{padding:var(--padding-small) 0;border:1px solid transparent}.index-drag-container .index-panel-single-container.default.hover,.index-drag-container .index-panel-single-container.default:hover{cursor:pointer}.index-drag-container .index-panel-single-container.default.hover .index-panel-more-button,.index-drag-container .index-panel-single-container.default:hover .index-panel-more-button{display:flex}.index-drag-container .index-panel-single-container.default:hover{border:1px solid var(--blue-6)}.index-drag-container .index-panel-single-container.default.hover,.index-drag-container .index-panel-single-container.default.selected{background-color:var(--popup-button-active)}.index-drag-container .index-panel-single-container.default.selected{border:1px solid var(--blue-5)}.index-drag-container .index-panel-single-container.editing{padding:var(--padding-medium) 20px var(--padding-medium) 0;background-color:var(--outline-selected)}.index-drag-container .index-panel-single-container .index-panel-checkbox{flex-grow:0;flex-shrink:0;margin-top:1px;margin-bottom:0;margin-right:var(--padding-small)}.index-drag-container .index-panel-single-container .index-panel-text{background-color:inherit;border:none;text-align:left;padding:0;margin:0;cursor:pointer;display:table}.index-drag-container .index-panel-single-container .index-panel-text span{display:table-cell;vertical-align:middle}.index-drag-container .index-panel-single-container .index-panel-input,.index-drag-container .index-panel-single-container .index-panel-text{flex-grow:1;flex-shrink:1;flex-basis:calc(100% - 18px);white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.index-drag-container .index-panel-single-container .index-panel-outline-input{flex-grow:1;flex-shrink:1;flex-basis:calc(100% - 18px);color:var(--text-color);width:calc(100% - var(--padding-large));padding:var(--padding-small)}.index-drag-container .index-panel-single-container .index-panel-outline-input:focus{border-color:var(--outline-color)}.index-drag-container .index-panel-single-container .index-panel-outline-input::-moz-placeholder{color:var(--placeholder-text)}.index-drag-container .index-panel-single-container .index-panel-outline-input::placeholder{color:var(--placeholder-text)}.index-drag-container .index-panel-single-container .index-panel-editing-controls{flex-basis:100%;display:flex;flex-flow:row wrap;justify-content:flex-end;align-items:center;margin-top:var(--padding-medium)}.index-drag-container .index-panel-single-container .index-panel-cancel-button,.index-drag-container .index-panel-single-container .index-panel-save-button{width:auto;padding:6px var(--padding)}.index-drag-container .index-panel-single-container .index-panel-cancel-button{color:var(--secondary-button-text)}.index-drag-container .index-panel-single-container .index-panel-cancel-button:hover{color:var(--secondary-button-hover)}.index-drag-container .index-panel-single-container .index-panel-save-button{color:var(--primary-button-text);background-color:var(--primary-button);margin-left:var(--padding-tiny);border-radius:4px}.index-drag-container .index-panel-single-container .index-panel-save-button:hover{background-color:var(--primary-button-hover)}.index-drag-container .index-panel-single-container .index-panel-save-button.disabled,.index-drag-container .index-panel-single-container .index-panel-save-button:disabled{cursor:not-allowed;background-color:var(--blue-5)!important;opacity:.5}.index-drag-container .index-panel-single-container .index-panel-save-button.disabled span,.index-drag-container .index-panel-single-container .index-panel-save-button:disabled span{color:var(--gray-0)}",""])},1978:function(e,n,t){"use strict";t.r(n);t(28),t(8),t(19),t(11),t(13),t(14),t(10),t(9),t(12),t(16),t(15),t(20),t(18),t(59),t(47);var i=t(0),o=t.n(i),a=t(6),r=(t(1940),t(1)),l=t(5),d=(t(38),t(49),t(53),t(23),t(24),t(117),t(127),t(29),t(35),t(3)),c=t(428),s=t(4),p=t.n(s),u=t(2),f=t(42),m=t(44),x=t(76),g=(t(1942),t(1957)),b=t(1732),h=t(50),y=t(1502),v=t(1487);function w(e,n){return function(e){if(Array.isArray(e))return e}(e)||function(e,n){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var i,o,a,r,l=[],d=!0,c=!1;try{if(a=(t=t.call(e)).next,0===n){if(Object(t)!==t)return;d=!1}else for(;!(d=(i=a.call(t)).done)&&(l.push(i.value),l.length!==n);d=!0);}catch(e){c=!0,o=e}finally{try{if(!d&&null!=t.return&&(r=t.return(),Object(r)!==r))return}finally{if(c)throw o}}return l}}(e,n)||function(e,n){if(!e)return;if("string"==typeof e)return A(e,n);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(e);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return A(e,n)}(e,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function A(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,i=new Array(n);t<n;t++)i[t]=e[t];return i}var E={isMultiSelectionMode:p.a.bool,fieldName:p.a.string,widgetId:p.a.string,icon:p.a.string,setSelected:p.a.func,isActive:p.a.bool,handleDeletion:p.a.func,isSubLevel:p.a.bool,childWidgets:p.a.array,selectingWidgets:p.a.array},k=Object(i.forwardRef)((function(e,n){var t=e.isSubLevel,s=e.isMultiSelectionMode,p=e.fieldName,f=e.widgetId,x=e.icon,A=e.setSelected,E=e.isActive,I=e.handleDeletion,O=e.childWidgets,N=e.selectingWidgets,j=w(Object(c.a)(),1)[0],S=w(Object(i.useState)(!1),2),C=S[0],P=S[1],T=w(Object(i.useState)(!1),2),_=T[0],L=T[1],M=w(Object(i.useState)(p),2),R=M[0],D=M[1],F=w(Object(i.useState)(!0),2),W=F[0],H=F[1],B=w(Object(i.useState)(""),2),z=B[0],q=B[1],V=Object(i.useRef)(),U=Object(a.d)(),X=function(){var e,n=r.a.getFormFieldCreationManager(),t=null;if(f){var i=r.a.getAnnotationById(f);t=n.setFieldName(i,R)}else O.forEach((function(e){t=n.setFieldName(e,R)}));H(t.isValid),e=Object(b.a)(t),q(e),t.isValid&&G()},G=function(){_&&D(p),L(!1),H(!0)};Object(i.useEffect)((function(){R!==p&&D(p)}),[p]),Object(i.useEffect)((function(){_&&(V.current.focus(),V.current.select()),P(!_)}),[_]);var K="".concat(l.a.BOOKMARK_OUTLINE_FLYOUT,"-").concat(f),$=Object(a.e)((function(e){return d.a.getFlyout(e,K)})),J={moreOptionsDataElement:"index-panel-more-button-".concat(f),flyoutToggleElement:K},Y={shouldHideDeleteButton:!1,currentFlyout:$,flyoutSelector:K,type:f?"indexPanel":"indexPanel.folder",handleOnClick:function(e){switch(e){case v.b.OPENFORMFIELDPANEL:U(u.a.openElement(l.a.FORM_FIELD_PANEL)),A(f,!0);break;case v.b.RENAME:L(!0);break;case v.b.DELETE:if(!f)return void O.forEach((function(e){I(e.Id)}));I(f)}}},Q={id:f,checked:E,onChange:function(e){f?A(f,e.target.checked):A(f,e.target.checked,O)},"aria-label":"index panel checkbox","aria-checked":E};return o.a.createElement("div",{className:"index-drag-container",key:f,ref:n},C&&o.a.createElement(y.a,{iconGlyph:x,labelHeader:t?f:p,enableMoreOptionsContextMenuFlyout:!0,onDoubleClick:function(){return L(!0)},onClick:function(){return!s&&A(f)},contextMenuMoreButtonOptions:J,contentMenuFlyoutOptions:Y,checkboxOptions:s&&Q||null,isActive:E},null==O?void 0:O.map((function(e){return function(e){var n=Object(h.e)(Object(h.g)(e)).icon,t=e.fieldName,i=e.Id;return o.a.createElement(k,{key:i,isMultiSelectionMode:s,fieldName:t,icon:n,widgetId:i,setSelected:A,isActive:N.includes(i),handleDeletion:I,isSubLevel:!0})}(e)}))),_&&o.a.createElement("div",{className:"index-panel-single-container"},o.a.createElement("div",{className:"index-panel-label-row"},o.a.createElement("div",{className:"index-panel_content-container"},o.a.createElement(g.a,{type:"text",name:"field-name",ref:V,className:"index-panel-outline-input index-panel-text-input","aria-label":j("action.name"),value:R,onKeyDown:function(e){"Enter"===e.key&&(e.stopPropagation(),_&&X()),"Escape"===e.key&&G()},onChange:function(e){H(!0),D(e.target.value)},fillWidth:"false",onBlur:function(e){e.relatedTarget&&(e.relatedTarget.className.includes("index-panel-save-button")||e.relatedTarget.className.includes("index-panel-cancel-button"))?e.preventDefault():G()},messageText:W?"":j(z),message:W?"default":"warning"})),o.a.createElement("div",{className:"index-panel-editing-controls"},o.a.createElement(m.a,{className:"index-panel-cancel-button",label:j("action.cancel"),onClick:G}),o.a.createElement(m.a,{className:"index-panel-save-button",label:j("action.save"),isSubmitType:!0,onClick:X,disabled:!W||R===p})))))}));k.displayName="IndexPanelContent",k.propTypes=E;var I=k,O=t(60),N=t(1524);function j(e,n){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=P(e))||n&&e&&"number"==typeof e.length){t&&(e=t);var i=0,o=function(){};return{s:o,n:function(){return i>=e.length?{done:!0}:{done:!1,value:e[i++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,r=!0,l=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return r=e.done,e},e:function(e){l=!0,a=e},f:function(){try{r||null==t.return||t.return()}finally{if(l)throw a}}}}function S(e){return function(e){if(Array.isArray(e))return T(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||P(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function C(e,n){return function(e){if(Array.isArray(e))return e}(e)||function(e,n){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var i,o,a,r,l=[],d=!0,c=!1;try{if(a=(t=t.call(e)).next,0===n){if(Object(t)!==t)return;d=!1}else for(;!(d=(i=a.call(t)).done)&&(l.push(i.value),l.length!==n);d=!0);}catch(e){c=!0,o=e}finally{try{if(!d&&null!=t.return&&(r=t.return(),Object(r)!==r))return}finally{if(c)throw o}}return l}}(e,n)||P(e,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function P(e,n){if(e){if("string"==typeof e)return T(e,n);var t=Object.prototype.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?T(e,n):void 0}}function T(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,i=new Array(n);t<n;t++)i[t]=e[t];return i}var _={widgets:p.a.arrayOf(p.a.object).isRequired},L=function(e){var n=e.widgets,t=C(Object(a.e)((function(e){return[d.a.isElementOpen(e,l.a.INDEX_PANEL),d.a.isElementDisabled(e,l.a.INDEX_PANEL),d.a.isInDesktopOnlyMode(e)]})),3),s=t[0],p=t[1],g=t[2],b=C(Object(c.a)(),1)[0],y=C(Object(i.useState)(!1),2),v=y[0],w=y[1],A=C(Object(i.useState)([]),2),E=A[0],k=A[1],P=!p&&s,T=Object(O.b)(),_=Object(a.d)(),L=Object(i.useRef)({}),M=Object(i.useRef)(),R=Object(i.useRef)(-1);Object(i.useEffect)((function(){var e=r.a.getSelectedAnnotations().filter((function(e){return e instanceof window.Core.Annotations.WidgetAnnotation})).map((function(e){return e.Id}));k(e),e.length&&(R.current=e[0])}),[]),Object(i.useEffect)((function(){E.length&&-1!==R.current?F(R.current):R.current=-1}),[E]);var D=function(e,n){if("selected"===n){var t=e.filter((function(e){return e instanceof window.Core.Annotations.WidgetAnnotation})).map((function(e){return e.Id}));k(S(t)),t.length&&(R.current=t[0])}else if("deselected"===n){var i=e.filter((function(e){return e instanceof window.Core.Annotations.WidgetAnnotation})).map((function(e){return e.Id}));k((function(e){return e.filter((function(e){return!i.includes(e)}))}))}};Object(i.useEffect)((function(){return r.a.addEventListener("annotationSelected",D),function(){r.a.removeEventListener("annotationSelected",D)}}),[v]);var F=function(e){var n=L.current[e];n&&(function(e){var n=M.current;if(!n)return!1;var t=n.getBoundingClientRect(),i=e.getBoundingClientRect();return i.top>=t.top&&i.top<=t.top+n.clientHeight}(n)||n.scrollIntoView({behavior:"smooth",block:"nearest",inline:"start"}))},W=n.reduce((function(e,n){e[n.PageNumber]||(e[n.PageNumber]=[]);var t,i=j(e[n.PageNumber]);try{for(i.s();!(t=i.n()).done;){if(t.value.fieldName===n.fieldName)return e}}catch(e){i.e(e)}finally{i.f()}return e[(n=function(e){var n=e.getField(),t=[];return((null==n?void 0:n.widgets)||[]).forEach((function(n){n.PageNumber===e.PageNumber&&t.push(n)})),t.length<=1?e:{fieldName:e.fieldName,Id:null,PageNumber:e.PageNumber,childWidgets:t}}(n)).PageNumber].push(n),e}),{}),H=Object.keys(W).sort((function(e,n){return e-n})),B=function(e){if(e)r.a.deleteAnnotations([r.a.getAnnotationById(e)]);else{var n=E.map((function(e){return r.a.getAnnotationById(e)}));r.a.deleteAnnotations(n)}},z=function(e,n,t){R.current=e,v?function(e,n,t){var i=[];if(e)i=n?[e].concat(S(E)):E.filter((function(n){return n!==e}));else{var o=t.map((function(e){return e.Id}));i=n?[].concat(S(o),S(E)):E.filter((function(e){return!o.includes(e)}))}k(i);var a=i.map((function(e){return r.a.getAnnotationById(e)}));if(r.a.deselectAllAnnotations(),r.a.selectAnnotations(a),n){var l=r.a.getAnnotationById(e||t[0].Id);r.a.jumpToAnnotation(l)}}(e,n,t):function(e){var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],t=E.includes(e),i=r.a.getAnnotationById(e);r.a.deselectAllAnnotations(),!t||n?(k([e]),r.a.selectAnnotation(r.a.getAnnotationById(e)),r.a.jumpToAnnotation(i)):k([])}(e,n)},q=o.a.createElement("div",{className:"no-fields"},o.a.createElement("div",null,o.a.createElement(f.a,{className:"empty-icon",glyph:"illustration - empty state - outlines"})),o.a.createElement("div",{className:"msg"},b("formField.indexPanel.notFields"))),V=function(e){var n=e.fieldName,t=e.childWidgets?"ic-folder-open":Object(h.e)(Object(h.g)(e)).icon,i=e.Id,a=e.childWidgets||null,r=i?E.includes(i):function(e){var n,t=j(e);try{for(t.s();!(n=t.n()).done;){var i=n.value;if(!E.includes(i.Id))return!1}}catch(e){t.e(e)}finally{t.f()}return!0}(a),l=a?a.map((function(e){return e.Id})):[];return o.a.createElement(I,{key:e.Id,ref:function(e){return function(e,n,t){n?L.current[n]=e:t.forEach((function(n){L.current[n]=e}))}(e,i,l)},isMultiSelectionMode:v,fieldName:n,icon:t,widgetId:i,setSelected:z,isActive:r,handleDeletion:B,childWidgets:a,selectingWidgets:E})},U=function(){_(u.a.closeElement(l.a.INDEX_PANEL))};return P?o.a.createElement("div",{className:"index-panel-container"},o.a.createElement(x.a,{className:"index-panel-header",dataElement:"index-panel-header"},!g&&T&&o.a.createElement("div",{className:"close-container"},o.a.createElement("button",{className:"close-icon-container",onClick:U},o.a.createElement(f.a,{glyph:"ic_close_black_24px",className:"close-icon"}))),o.a.createElement(x.a,{className:"title-container",dataElement:"index-panel-title-container"},o.a.createElement(x.a,{className:"fields-counter",dataElement:"index-panel-fields-counter"},o.a.createElement("span",null,b("formField.indexPanel.formFieldList"))," (",n.length,")"),o.a.createElement(x.a,{dataElement:"form-field-multi-select"},!v&&o.a.createElement(m.a,{className:"field-control-button",label:b("action.edit"),disabled:0===n.length,onClick:function(){w(!0),k([]),r.a.deselectAllAnnotations(),R.current=-1}}),v&&o.a.createElement(m.a,{className:"field-control-button",label:b("option.bookmarkOutlineControls.done"),onClick:function(){w(!1),k([]),r.a.deselectAllAnnotations(),R.current=-1}})))),o.a.createElement(x.a,{className:"IndexPanel",dataElement:"index-panel",ref:M},n.length?H.map((function(e){return o.a.createElement("ul",{className:"index-page-container",key:e},o.a.createElement(N.a,{renderContent:function(){return"".concat(b("option.shared.page")," ").concat(e)}}),W[e].map(V))})):q),v&&o.a.createElement(x.a,{className:"index-panel-footer",dataElement:"index-panel-footer"},o.a.createElement(m.a,{className:"multi-selection-button",img:"icon-delete-line",disabled:0===E.length,onClick:function(){return B()}}))):null};L.propTypes=_;var M=L;function R(){return(R=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])}return e}).apply(this,arguments)}function D(e,n){return function(e){if(Array.isArray(e))return e}(e)||function(e,n){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var i,o,a,r,l=[],d=!0,c=!1;try{if(a=(t=t.call(e)).next,0===n){if(Object(t)!==t)return;d=!1}else for(;!(d=(i=a.call(t)).done)&&(l.push(i.value),l.length!==n);d=!0);}catch(e){c=!0,o=e}finally{try{if(!d&&null!=t.return&&(r=t.return(),Object(r)!==r))return}finally{if(c)throw o}}return l}}(e,n)||function(e,n){if(!e)return;if("string"==typeof e)return F(e,n);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(e);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return F(e,n)}(e,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function F(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,i=new Array(n);t<n;t++)i[t]=e[t];return i}var W={parentDataElement:p.a.string,dataElement:p.a.string.isRequired};function H(e){var n=e.parentDataElement,t=void 0===n?void 0:n,c=e.dataElement,s=D(Object(a.e)((function(e){return[d.a.isElementOpen(e,t||c||l.a.INDEX_PANEL)]}),a.c),1)[0],p=D(Object(i.useState)([]),2),u=p[0],f=p[1];Object(i.useEffect)((function(){var e=function(){var e=r.a.getAnnotationsList().filter((function(e){return e instanceof window.Core.Annotations.WidgetAnnotation}));f(e)};return r.a.addEventListener("annotationChanged",e),function(){r.a.removeEventListener("annotationChanged",e)}}),[]),Object(i.useEffect)((function(){if(s){var e=r.a.getAnnotationsList().filter((function(e){return e instanceof window.Core.Annotations.WidgetAnnotation}));f(e)}else f([])}),[s]);var m={widgets:u};return o.a.createElement(M,R({},e,m))}H.propTypes=W;var B=H;n.default=B}}]);
//# sourceMappingURL=chunk.43.js.map