{"version": 3, "sources": ["webpack:///./src/ui/src/helpers/createItemsForBookmarkOutlineFlyout.js", "webpack:///./src/ui/src/components/MoreOptionsContextMenuFlyout/MoreOptionsContextMenuFlyout.js", "webpack:///./src/ui/src/components/MoreOptionsContextMenuFlyout/index.js", "webpack:///./src/ui/src/hooks/useNestingLevel.js", "webpack:///./src/ui/src/components/PanelListItem/PanelListItem.js", "webpack:///./src/ui/src/components/PanelListItem/PanelListItem.scss?23f7", "webpack:///./src/ui/src/components/PanelListItem/PanelListItem.scss"], "names": ["createItemsForBookmarkOutlineFlyout", "menuItems", "type", "shouldHideDeleteButton", "handleOnClick", "menuTypes", "hiddenRules", "DELETE", "DOWNLOAD", "OPENFILE", "SETDEST", "OPENFORMFIELDPANEL", "includes", "MOVE_LEFT", "MOVE_RIGHT", "MOVE_UP", "MOVE_DOWN", "map", "item", "option", "hidden", "dataElement", "onClick", "i", "createFlyoutItem", "icon", "label", "title", "toUpperCase", "slice", "RENAME", "MoreOptionsContextMenuFlyout", "currentFlyout", "flyoutSelector", "dispatch", "useDispatch", "useLayoutEffect", "bookmarkOutlineFlyout", "className", "items", "actions", "updateFlyout", "addFlyout", "runDispatch", "propTypes", "PropTypes", "oneOf", "isRequired", "func", "object", "string", "bool", "useNestingLevel", "elementRef", "nestingSelector", "useState", "level", "setLevel", "useEffect", "currentLevel", "pointer", "current", "parentElement", "closest", "PanelList<PERSON><PERSON><PERSON><PERSON>", "children", "length", "child", "index", "key", "oneOfType", "arrayOf", "node", "PanelItemContent", "React", "memo", "iconGlyph", "labelHeader", "onDoubleClick", "useI18String", "textColor", "isActive", "Icon", "glyph", "<PERSON><PERSON>", "style", "color", "aria<PERSON><PERSON><PERSON>", "classNames", "displayName", "PanelListItem", "checkboxOptions", "contentMenuFlyoutOptions", "contextMenuMoreButtonOptions", "description", "enableMoreOptionsContextMenuFlyout", "expanded", "setIsExpandedHandler", "panelListItemRef", "useRef", "currentNestingLevel", "isExpanded", "setIsExpanded", "t", "useTranslation", "flyoutToggleElement", "moreOptionsDataElement", "showCheckBox", "disabled", "data-element", "ref", "Choice", "role", "id", "aria-label", "aria-checked", "checked", "onChange", "toggled", "visible", "img", "ariaExpanded", "ToggleElementButton", "toggleElement", "shape", "api", "content", "__esModule", "default", "module", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals"], "mappings": "ukDA0BeA,MA1Bf,SAA6CC,EAAWC,EAAMC,EAAwBC,EAAeC,GAAW,MACxGC,GAAW,OACdD,EAAUE,QAAS,kBAAMJ,KAAsB,IAC/CE,EAAUG,UAAW,iBAAe,cAATN,KAAoB,IAC/CG,EAAUI,UAAW,iBAAe,cAATP,KAAoB,IAC/CG,EAAUK,SAAU,iBAAe,YAATR,KAAkB,IAC5CG,EAAUM,oBAAqB,iBAAM,CAAC,YAAa,YAAYC,SAASV,MAAK,IAC7EG,EAAUQ,WAAY,iBAAe,YAATX,KAAkB,IAC9CG,EAAUS,YAAa,iBAAe,YAATZ,KAAkB,IAC/CG,EAAUU,SAAU,iBAAe,YAATb,GAA+B,cAATA,KAAoB,IACpEG,EAAUW,WAAY,iBAAe,YAATd,GAA+B,cAATA,KAAoB,GAGzE,OAAOD,EAAUgB,KAAI,SAACC,GACpB,IAAQC,EAAWD,EAAXC,OACFC,IAASd,EAAYa,IAAUb,EAAYa,KAEjD,OAAO,EAAP,KACKD,GAAI,IACPE,SACAC,YAAa,GAAF,OAAKnB,GAAI,OAAGgB,EAAKG,aAC5BC,QAAS,kBAAMlB,EAAcc,EAAKC,e,sYCpBxC,8lGAAAI,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,kkBAKA,IAAMC,EAAmB,SAACL,EAAQM,EAAMC,GAAK,MAAM,CACjDD,OACAC,QACAC,MAAOD,EACPP,SACAE,YAAa,GAAF,OAAKF,EAAO,GAAGS,cAAiBT,EAAOU,MAAM,GAAE,YAG/CxB,EAAY,CACvBI,SAAU,WACVqB,OAAQ,SACRpB,QAAS,iBACTF,SAAU,WACVD,OAAQ,SACRI,mBAAoB,qBACpBI,QAAS,SACTC,UAAW,WACXH,UAAW,WACXC,WAAY,aAGDb,EAAY,CACvBuB,EAAiBnB,EAAUM,mBAAoB,uBAAwB,eACvEa,EAAiBnB,EAAUI,SAAU,sBAAuB,sBAC5De,EAAiBnB,EAAUyB,OAAQ,oBAAqB,iBACxDN,EAAiBnB,EAAUK,QAAS,iBAAkB,yBACtDc,EAAiBnB,EAAUG,SAAU,gBAAiB,mBACtDgB,EAAiBnB,EAAUE,OAAQ,mBAAoB,iBACvDiB,EAAiBnB,EAAUU,QAAS,oBAAqB,iBACzDS,EAAiBnB,EAAUW,UAAW,sBAAuB,mBAC7DQ,EAAiBnB,EAAUQ,UAAW,sBAAuB,mBAC7DW,EAAiBnB,EAAUS,WAAY,uBAAwB,qBAG3DiB,EAA+B,SAAH,GAM5B,IALJ7B,EAAI,EAAJA,KACAE,EAAa,EAAbA,cACA4B,EAAa,EAAbA,cACAC,EAAc,EAAdA,eACA9B,EAAsB,EAAtBA,uBAEM+B,EAAWC,cAkBjB,OAhBAC,2BAAgB,WACd,IAAMC,EAAwB,CAC5BhB,YAAaY,EACbK,UAAW,+BACXC,MAAOvC,EAAoCC,EAAWC,EAAMC,EAAwBC,EAAeC,IAE3E,aAMzB,OANyB,cAA1B,8EAII6B,EAHGF,EAGMQ,IAAQC,aAAaJ,EAAsBhB,YAAagB,GAFxDG,IAAQE,UAAUL,IAG5B,4CACF,uBAPC,WACwB,wBAO1BM,KACC,IAEI,MAITZ,EAA6Ba,UAAY,CACvC1C,KAAM2C,IAAUC,MAAM,CAAC,WAAY,UAAW,YAAa,aAAc,sBAAsBC,WAC/F3C,cAAeyC,IAAUG,KACzBhB,cAAea,IAAUI,OACzBhB,eAAgBY,IAAUK,OAC1B/C,uBAAwB0C,IAAUM,MAGrBpB,O,4MC3EAA,E,QAA4B,E,+iCCA3C,IAgBeqB,EAhBS,SAACC,EAAYC,GACnC,IAAqC,IAAXC,mBAAS,GAAE,GAA9BC,EAAK,KAAEC,EAAQ,KAYtB,OAVAC,qBAAU,WAGR,IAFA,IAAIC,GAAgB,EAChBC,EAAUP,aAAU,EAAVA,EAAYQ,QACnBD,GACLD,IACAC,EAAUA,EAAQE,cAAcC,QAAQT,GAE1CG,EAASE,KACR,CAACN,IAEGG,G,+hCCHT,IAAMQ,EAAoB,SAAH,GAAqB,IAAfC,EAAQ,EAARA,SAC3B,OAAKA,GAAgC,IAApBA,EAASC,OAIxB,wBAAI5B,UAAU,uBACX2B,EAAShD,KAAI,SAACkD,EAAOC,GAAK,OACzB,wBAAIC,IAAKD,GAAQD,OALd,MAWXH,EAAkBpB,UAAY,CAC5BqB,SAAUpB,IAAUyB,UAAU,CAC5BzB,IAAU0B,QAAQ1B,IAAU2B,MAC5B3B,IAAU2B,QAId,IAAMC,EAAmBC,IAAMC,MAAK,gBAClCC,EAAS,EAATA,UACAC,EAAW,EAAXA,YACAC,EAAa,EAAbA,cACAxD,EAAO,EAAPA,QACAyD,EAAY,EAAZA,aACAC,EAAS,EAATA,UACAC,EAAQ,EAARA,SAAQ,OAER,oCACGL,GACC,yBAAKtC,UAAU,6BACb,kBAAC4C,EAAA,EAAI,CAACC,MAAOP,KAGjB,yBAAKtC,UAAW,6BACd,yBAAKA,UAAU,2BACb,kBAAC8C,EAAA,EAAM,CACLC,MAAO,CAAEC,MAAON,GAAa,WAC7BO,UAAWV,EACXnD,MAAOmD,EACPC,cAAeA,EACfxD,QAASA,EACTgB,UAAWkD,IAAW,CACpB,YAAaP,IAEfF,aAAcA,UAOxBN,EAAiBgB,YAAc,mBAE/BhB,EAAiB7B,UAAY,CAC3BgC,UAAW/B,IAAUK,OACrB2B,YAAahC,IAAUK,OAAOH,WAC9B+B,cAAejC,IAAUG,KACzB1B,QAASuB,IAAUG,KACnB+B,aAAclC,IAAUM,KACxB6B,UAAWnC,IAAUK,OACrB+B,SAAUpC,IAAUM,MAGtB,IAAMuC,EAAgB,SAAH,GAgBb,IAfJC,EAAe,EAAfA,gBACA1B,EAAQ,EAARA,SAAQ,IACR2B,gCAAwB,IAAG,KAAE,MAC7BC,oCAA4B,IAAG,KAAE,EACjCC,EAAW,EAAXA,YACAC,EAAkC,EAAlCA,mCACAnB,EAAS,EAATA,UACAC,EAAW,EAAXA,YAAW,IACXE,oBAAY,IAAG,GAAI,MACnBD,qBAAa,IAAG,eAAQ,MACxBxD,eAAO,IAAG,eAAQ,EAClB0E,EAAQ,EAARA,SACAC,EAAoB,EAApBA,qBACAjB,EAAS,EAATA,UACAC,EAAQ,EAARA,SAEMiB,EAAmBC,mBACnBC,EAAsBhD,EAAgB8C,GACmB,IAA3B3C,mBAASyC,YAAkB,GAAxDK,EAAU,KAAEC,EAAa,KACxBC,EAAMC,cAAND,EACR,EAMIX,EALFzF,8BAAsB,IAAG,GAAK,EAC9B6B,EAIE4D,EAJF5D,cACAC,EAGE2D,EAHF3D,eACA/B,EAEE0F,EAFF1F,KACAE,EACEwF,EADFxF,cAIAqG,EAEEZ,EAFFY,oBACAC,EACEb,EADFa,uBAGIC,EAAehB,IAAoBA,EAAgBiB,WAAY,EAOrE,OACE,yBAAKC,eAAa,gBAAgBvE,UAAU,kBAAkBwE,IAAKZ,GACjE,yBAAK5D,UAAWkD,IAAW,CACzB,mBAAmB,EACnB,mBAAoBM,EACpB,mBAAoBA,EACpB,uBAAwBlB,IAAce,EACtC,sBAAuBf,GAAae,EACpC,sBAAuBf,GAAae,KAEpC,yBAAKrD,UAAWkD,IAAW,iBAAD,OAAkBG,EAAkB,iBAAmB,IAAM,wBACpFgB,GACC,yBACEtB,MAAO,CAAE,kBAAmB,GAAF,QAAM,GAAKe,EAAsB,EAAC,OAC5D9D,UAAU,YAEV,kBAACyE,EAAA,EAAM,CACLC,KAAK,WACLC,GAAItB,aAAe,EAAfA,EAAiBsB,GACrBC,aAAYvB,aAAe,EAAfA,EAAiBJ,UAC7B4B,eAAcxB,aAAe,EAAfA,EAAiByB,QAC/BA,QAASzB,aAAe,EAAfA,EAAiByB,QAC1BC,SAAU1B,aAAe,EAAfA,EAAiB0B,YAKjC,yBACE/F,QAAS,WACPgF,GAAeD,GACXJ,GACFA,GAAsBI,IAG1B/D,UAAWkD,IAAW,CACpB,qBAAqB,EACrB8B,QAASjB,EACTkB,QAAStD,GAAYA,EAASC,OAAS,KAGzC,kBAACkB,EAAA,EAAM,CACLoC,IAAI,qBACJlF,UAAU,oBACVmF,aAAcpB,EACdd,UAAS,UAAkBgB,EAAbF,EAAe,kBAAuB,iBAAgB,YAAIxB,MAI5E,kBAACJ,EAAgB,CACfG,UAAWA,EACXC,YAAaA,EACbC,cAAeA,EACfxD,QAASA,EACTyD,aAAcA,EACdC,UAAWA,EACXC,SAAUA,IAEXc,GACC,yBAAKzD,UAAU,2BACb,kBAACoF,EAAA,EAAmB,CAClBpF,UAAU,qBACVX,MAAK,UAAK4E,EAAE,kCAAiC,YAAI1B,GACjD8C,cAAelB,EACfpF,YAAaqF,EACbc,IAAI,iBACJZ,UAAU,IAEZ,kBAAC,EAA4B,CAC3B1G,KAAMA,EACNC,uBAAwBA,EACxB6B,cAAeA,EACfC,eAAgBA,EAChB7B,cAAeA,MAKtB0F,GACC,yBAAKxD,UAAU,0BAA0BwD,IAG5CO,GAAc,kBAAC,EAAiB,KAAEpC,KAKzCyB,EAAc9C,UAAY,CACxB+C,gBAAiB9C,IAAU+E,MAAM,CAC/BX,GAAIpE,IAAUK,OACdkE,QAASvE,IAAUM,KACnBkE,SAAUxE,IAAUG,KACpBuC,UAAW1C,IAAUK,OACrB0D,SAAU/D,IAAUM,OAEtB4B,aAAclC,IAAUM,KACxByB,UAAW/B,IAAUK,OACrB2B,YAAahC,IAAUK,OAAOH,WAC9B+C,YAAajD,IAAUK,OACvB6C,mCAAoClD,IAAUM,KAC9Cc,SAAUpB,IAAU2B,KACpBM,cAAejC,IAAUG,KACzB1B,QAASuB,IAAUG,KACnBgD,SAAUnD,IAAUM,KACpB6B,UAAWnC,IAAUK,OACrB+C,qBAAsBpD,IAAUG,KAChC4C,yBAA0B/C,IAAU+E,MAAM,CACxCzH,uBAAwB0C,IAAUM,KAClCnB,cAAea,IAAUI,OACzBhB,eAAgBY,IAAUK,OAC1BhD,KAAM2C,IAAUK,OAChB9C,cAAeyC,IAAUG,OAE3B6C,6BAA8BhD,IAAU+E,MAAM,CAC5CnB,oBAAqB5D,IAAUK,OAC/BwD,uBAAwB7D,IAAUK,SAEpC+B,SAAUpC,IAAUM,MAGPuC,O,qBC7Of,IAAImC,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAO1G,EAAIuG,EAAS,MAG9C,IAAII,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcvE,SACjBuE,EAzBF,SAASE,EAAwBC,EAASC,EAAOP,UAC/C,MAAMQ,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAI7H,EAAI,EAAGA,EAAIkH,EAAcvE,OAAQ3C,IAAK,CAC7C,MAAM8H,EAAeZ,EAAclH,GACnC,GAAU,IAANA,EACF8H,EAAaF,WAAWX,YAAYL,GACpCA,EAASmB,OAAS,WACZF,EAAgBlF,OAAS,GAC3BkF,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYrB,EAASqB,iBAIhC,CACL,MAAMD,EAAYpB,EAASsB,WAAU,GACrCJ,EAAaF,WAAWX,YAAYe,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP1B,EAAIC,EAASI,GAI1BD,EAAOyB,QAAU5B,EAAQ6B,QAAU,I,sBClEzB1B,EAAOyB,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACjB,EAAO1G,EAAI,guGAAmuG", "file": "chunks/chunk.4.js", "sourcesContent": ["function createItemsForBookmarkOutlineFlyout(menuItems, type, shouldHideDeleteButton, handleOnClick, menuTypes) {\n  const hiddenRules = {\n    [menuTypes.DELETE]: () => shouldHideDeleteButton,\n    [menuTypes.DOWNLOAD]: () => type !== 'portfolio',\n    [menuTypes.OPENFILE]: () => type !== 'portfolio',\n    [menuTypes.SETDEST]: () => type !== 'outline',\n    [menuTypes.OPENFORMFIELDPANEL]: () => ['portfolio', 'bookmark'].includes(type),\n    [menuTypes.MOVE_LEFT]: () => type !== 'outline',\n    [menuTypes.MOVE_RIGHT]: () => type !== 'outline',\n    [menuTypes.MOVE_UP]: () => type !== 'outline' && type !== 'portfolio',\n    [menuTypes.MOVE_DOWN]: () => type !== 'outline' && type !== 'portfolio',\n  };\n\n  return menuItems.map((item) => {\n    const { option } = item;\n    const hidden = hiddenRules[option] ? hiddenRules[option]() : false;\n\n    return {\n      ...item,\n      hidden,\n      dataElement: `${type}${item.dataElement}`,\n      onClick: () => handleOnClick(item.option),\n    };\n  });\n}\n\nexport default createItemsForBookmarkOutlineFlyout;", "import { useLayoutEffect } from 'react';\nimport { useDispatch } from 'react-redux';\nimport actions from 'actions';\nimport PropTypes from 'prop-types';\nimport createItemsForBookmarkOutlineFlyout from 'src/helpers/createItemsForBookmarkOutlineFlyout';\n\nconst createFlyoutItem = (option, icon, label) => ({\n  icon,\n  label,\n  title: label,\n  option,\n  dataElement: `${option[0].toUpperCase() +  option.slice(1)}Button`,\n});\n\nexport const menuTypes = {\n  OPENFILE: 'openFile',\n  RENAME: 'rename',\n  SETDEST: 'setDestination',\n  DOWNLOAD: 'download',\n  DELETE: 'delete',\n  OPENFORMFIELDPANEL: 'openFormFieldPanel',\n  MOVE_UP: 'moveUp',\n  MOVE_DOWN: 'moveDown',\n  MOVE_LEFT: 'moveLeft',\n  MOVE_RIGHT: 'moveRight',\n};\n\nexport const menuItems = [\n  createFlyoutItem(menuTypes.OPENFORMFIELDPANEL, 'icon-edit-form-field', 'action.edit'),\n  createFlyoutItem(menuTypes.OPENFILE, 'icon-portfolio-file', 'portfolio.openFile'),\n  createFlyoutItem(menuTypes.RENAME, 'ic_edit_page_24px', 'action.rename'),\n  createFlyoutItem(menuTypes.SETDEST, 'icon-thumbtack', 'action.setDestination'),\n  createFlyoutItem(menuTypes.DOWNLOAD, 'icon-download', 'action.download'),\n  createFlyoutItem(menuTypes.DELETE, 'icon-delete-line', 'action.delete'),\n  createFlyoutItem(menuTypes.MOVE_UP, 'icon-page-move-up', 'action.moveUp'),\n  createFlyoutItem(menuTypes.MOVE_DOWN, 'icon-page-move-down', 'action.moveDown'),\n  createFlyoutItem(menuTypes.MOVE_LEFT, 'icon-page-move-left', 'action.moveLeft'),\n  createFlyoutItem(menuTypes.MOVE_RIGHT, 'icon-page-move-right', 'action.moveRight'),\n];\n\nconst MoreOptionsContextMenuFlyout = ({\n  type,\n  handleOnClick,\n  currentFlyout,\n  flyoutSelector,\n  shouldHideDeleteButton,\n}) => {\n  const dispatch = useDispatch();\n\n  useLayoutEffect(() => {\n    const bookmarkOutlineFlyout = {\n      dataElement: flyoutSelector,\n      className: 'MoreOptionsContextMenuFlyout',\n      items: createItemsForBookmarkOutlineFlyout(menuItems, type, shouldHideDeleteButton, handleOnClick, menuTypes),\n    };\n    async function runDispatch() {\n      if (!currentFlyout) {\n        dispatch(actions.addFlyout(bookmarkOutlineFlyout));\n      } else {\n        dispatch(actions.updateFlyout(bookmarkOutlineFlyout.dataElement, bookmarkOutlineFlyout));\n      }\n    }\n    runDispatch();\n  }, []);\n\n  return null;\n};\n\n\nMoreOptionsContextMenuFlyout.propTypes = {\n  type: PropTypes.oneOf(['bookmark', 'outline', 'portfolio', 'indexPanel', 'indexPanel.folder']).isRequired,\n  handleOnClick: PropTypes.func,\n  currentFlyout: PropTypes.object,\n  flyoutSelector: PropTypes.string,\n  shouldHideDeleteButton: PropTypes.bool,\n};\n\nexport default MoreOptionsContextMenuFlyout;", "import MoreOptionsContextMenuFlyout from './MoreOptionsContextMenuFlyout';\n\nexport default MoreOptionsContextMenuFlyout;\n", "import { useState, useEffect } from 'react';\n\nconst useNestingLevel = (elementRef, nestingSelector) => {\n  const [level, setLevel] = useState(1);\n\n  useEffect(() => {\n    let currentLevel = -1;\n    let pointer = elementRef?.current;\n    while (pointer) {\n      currentLevel++;\n      pointer = pointer.parentElement.closest(nestingSelector);\n    }\n    setLevel(currentLevel);\n  }, [elementRef]);\n\n  return level;\n};\n\nexport default useNestingLevel;", "import React, { useRef, useState } from 'react';\nimport PropTypes from 'prop-types';\nimport classNames from 'classnames';\nimport { useTranslation } from 'react-i18next';\nimport ToggleElementButton from 'components/ModularComponents/ToggleElementButton';\nimport Button from '../Button';\nimport { Choice } from '@pdftron/webviewer-react-toolkit';\nimport MoreOptionsContextMenuFlyout from '../MoreOptionsContextMenuFlyout';\nimport Icon from 'components/Icon';\nimport './PanelListItem.scss';\nimport useNestingLevel from 'src/hooks/useNestingLevel';\n\nconst PanelListChildren = ({ children }) => {\n  if (!children || children.length === 0) {\n    return null;\n  }\n  return (\n    <ul className=\"panel-list-children\">\n      {children.map((child, index) => (\n        <li key={index}>{child}</li>\n      ))}\n    </ul>\n  );\n};\n\nPanelListChildren.propTypes = {\n  children: PropTypes.oneOfType([\n    PropTypes.arrayOf(PropTypes.node),\n    PropTypes.node\n  ])\n};\n\nconst PanelItemContent = React.memo(({\n  iconGlyph,\n  labelHeader,\n  onDoubleClick,\n  onClick,\n  useI18String,\n  textColor,\n  isActive,\n}) => (\n  <>\n    {iconGlyph && (\n      <div className=\"panel-list-icon-container\">\n        <Icon glyph={iconGlyph} />\n      </div>\n    )}\n    <div className={'panel-list-text-container'}>\n      <div className=\"panel-list-label-header\">\n        <Button\n          style={{ color: textColor || 'inherit' }}\n          ariaLabel={labelHeader}\n          label={labelHeader}\n          onDoubleClick={onDoubleClick}\n          onClick={onClick}\n          className={classNames({\n            'set-focus': isActive,\n          })}\n          useI18String={useI18String}\n        />\n      </div>\n    </div>\n  </>\n));\n\nPanelItemContent.displayName = 'PanelItemContent';\n\nPanelItemContent.propTypes = {\n  iconGlyph: PropTypes.string,\n  labelHeader: PropTypes.string.isRequired,\n  onDoubleClick: PropTypes.func,\n  onClick: PropTypes.func,\n  useI18String: PropTypes.bool,\n  textColor: PropTypes.string,\n  isActive: PropTypes.bool,\n};\n\nconst PanelListItem = ({\n  checkboxOptions,\n  children,\n  contentMenuFlyoutOptions = {},\n  contextMenuMoreButtonOptions = {},\n  description,\n  enableMoreOptionsContextMenuFlyout,\n  iconGlyph,\n  labelHeader,\n  useI18String = true,\n  onDoubleClick = () => {},\n  onClick = () => {},\n  expanded,\n  setIsExpandedHandler,\n  textColor,\n  isActive,\n}) => {\n  const panelListItemRef = useRef();\n  const currentNestingLevel = useNestingLevel(panelListItemRef);\n  const [isExpanded, setIsExpanded] = useState(expanded ?? false);\n  const { t } = useTranslation();\n  const {\n    shouldHideDeleteButton = false,\n    currentFlyout,\n    flyoutSelector,\n    type,\n    handleOnClick,\n  } = contentMenuFlyoutOptions;\n\n  const {\n    flyoutToggleElement,\n    moreOptionsDataElement,\n  } = contextMenuMoreButtonOptions;\n\n  const showCheckBox = checkboxOptions && !checkboxOptions.disabled || false;\n\n  /* component layout when all options are enabled:\n  checkbox|chevron| icon |    label header   |menu\n    empty | empty | empty| label description |empty\n  */\n\n  return (\n    <div data-element=\"panelListItem\" className=\"panel-list-item\" ref={panelListItemRef}>\n      <div className={classNames({\n        'panel-list-grid': true,\n        'grid-with-2-rows': description,\n        'grid-with-1-row': !description,\n        'grid-with-3-columns': !iconGlyph && !checkboxOptions,\n        'grid-with-4-columns': iconGlyph || checkboxOptions,\n        'grid-with-5-columns': iconGlyph && checkboxOptions,\n      })} >\n        <div className={classNames(`panel-list-row${checkboxOptions ? ' with-checkbox' : ''}`, 'focusable-container')}>\n          {showCheckBox && (\n            <div\n              style={{ '--checkbox-left': `${-32 * currentNestingLevel + 4}px` }}\n              className=\"checkbox\"\n            >\n              <Choice\n                role=\"checkbox\"\n                id={checkboxOptions?.id}\n                aria-label={checkboxOptions?.ariaLabel}\n                aria-checked={checkboxOptions?.checked}\n                checked={checkboxOptions?.checked}\n                onChange={checkboxOptions?.onChange}\n              />\n            </div>\n          )}\n\n          <div\n            onClick={() => {\n              setIsExpanded(!isExpanded);\n              if (setIsExpandedHandler) {\n                setIsExpandedHandler(!isExpanded);\n              }\n            }}\n            className={classNames({\n              'chevron-container': true,\n              toggled: isExpanded,\n              visible: children && children.length > 0,\n            })}\n          >\n            <Button\n              img=\"icon-chevron-right\"\n              className=\"panel-list-button\"\n              ariaExpanded={isExpanded}\n              ariaLabel={`${isExpanded ? t('action.collapse') : t('action.expand')} ${labelHeader}`}\n            />\n          </div>\n\n          <PanelItemContent\n            iconGlyph={iconGlyph}\n            labelHeader={labelHeader}\n            onDoubleClick={onDoubleClick}\n            onClick={onClick}\n            useI18String={useI18String}\n            textColor={textColor}\n            isActive={isActive}\n          />\n          {enableMoreOptionsContextMenuFlyout && (\n            <div className=\"panel-list-more-options\">\n              <ToggleElementButton\n                className=\"toggle-more-button\"\n                title={`${t('option.searchPanel.moreOptions')} ${labelHeader}`}\n                toggleElement={flyoutToggleElement}\n                dataElement={moreOptionsDataElement}\n                img=\"icon-tool-more\"\n                disabled={false}\n              />\n              <MoreOptionsContextMenuFlyout\n                type={type}\n                shouldHideDeleteButton={shouldHideDeleteButton}\n                currentFlyout={currentFlyout}\n                flyoutSelector={flyoutSelector}\n                handleOnClick={handleOnClick}\n              />\n            </div>\n          )}\n        </div>\n        {description && (\n          <div className=\"panel-list-description\">{description}</div>\n        )}\n      </div>\n      {isExpanded && <PanelListChildren>{children}</PanelListChildren>}\n    </div>\n  );\n};\n\nPanelListItem.propTypes = {\n  checkboxOptions: PropTypes.shape({\n    id: PropTypes.string,\n    checked: PropTypes.bool,\n    onChange: PropTypes.func,\n    ariaLabel: PropTypes.string,\n    disabled: PropTypes.bool\n  }),\n  useI18String: PropTypes.bool,\n  iconGlyph: PropTypes.string,\n  labelHeader: PropTypes.string.isRequired,\n  description: PropTypes.string,\n  enableMoreOptionsContextMenuFlyout: PropTypes.bool,\n  children: PropTypes.node,\n  onDoubleClick: PropTypes.func,\n  onClick: PropTypes.func,\n  expanded: PropTypes.bool,\n  textColor: PropTypes.string,\n  setIsExpandedHandler: PropTypes.func,\n  contentMenuFlyoutOptions: PropTypes.shape({\n    shouldHideDeleteButton: PropTypes.bool,\n    currentFlyout: PropTypes.object,\n    flyoutSelector: PropTypes.string,\n    type: PropTypes.string,\n    handleOnClick: PropTypes.func,\n  }),\n  contextMenuMoreButtonOptions: PropTypes.shape({\n    flyoutToggleElement: PropTypes.string,\n    moreOptionsDataElement: PropTypes.string,\n  }),\n  isActive: PropTypes.bool,\n};\n\nexport default PanelListItem;", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./PanelListItem.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".panel-list-item{width:100%;display:flex;flex-direction:column;position:relative;box-sizing:border-box;list-style-type:none}.panel-list-item ul{list-style-type:none;margin:0}.panel-list-item li::marker{content:\\\"\\\";margin:0}.panel-list-grid{display:grid;align-items:center;grid-column-gap:8px;-moz-column-gap:8px;column-gap:8px;margin-top:8px;margin-bottom:8px}.panel-list-grid.grid-with-1-row{grid-template-rows:auto}.panel-list-grid.grid-with-2-rows{grid-template-rows:auto auto}.panel-list-grid.grid-with-3-columns{grid-template-columns:auto minmax(0,1fr) auto}.panel-list-grid.grid-with-4-columns{grid-template-columns:auto auto minmax(0,1fr) auto}.panel-list-grid.grid-with-5-columns{grid-template-columns:auto auto auto minmax(0,1fr) auto}.panel-list-grid:hover .panel-list-more-options,.panel-list-grid[focus-within] .panel-list-more-options{visibility:visible}.panel-list-grid:focus-within .panel-list-more-options,.panel-list-grid:hover .panel-list-more-options{visibility:visible}.panel-list-row{display:contents}.panel-list-row.with-checkbox{padding-left:32px}.panel-list-row .checkbox{margin:0;position:relative;left:0;left:var(--checkbox-left,0)}.panel-list-row .chevron-container{min-width:24px;transition:transform .1s ease;visibility:hidden}.panel-list-row .chevron-container.toggled{transform:rotate(90deg)}.panel-list-row .chevron-container.visible{visibility:visible}.panel-list-row .chevron-container:hover{cursor:pointer;border:none;border-radius:4px;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6);background-color:var(--faded-component-background)}.panel-list-row .chevron-container .Button{width:24px;height:24px}.panel-list-row .chevron-container .Button .Icon{width:12px;height:12px}.panel-list-row .panel-list-icon-container .Icon{width:24px;height:24px}.panel-list-row .panel-list-text-container{grid-area:1/-3/auto/-2;display:flex;flex-direction:row;height:24px}.panel-list-row .panel-list-text-container .panel-list-label-header{align-content:center;margin:0;width:100%}.panel-list-row .panel-list-text-container .panel-list-label-header .set-focus{color:#2c73ab}.panel-list-row .panel-list-text-container .panel-list-label-header .Button{display:flex;width:auto;max-width:100%;height:100%;padding:2px 0 2px 4px;justify-content:start}.panel-list-row .panel-list-text-container .panel-list-label-header .Button:focus{color:#2c73ab}.panel-list-row .panel-list-text-container .panel-list-label-header .Button:hover{cursor:pointer;border:none;border-radius:4px;box-shadow:unset;color:var(--blue-6)}.panel-list-row .panel-list-text-container .panel-list-label-header .Button span{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;display:inline-block;flex-grow:1}.panel-list-row .panel-list-more-options{grid-area:1/-2/auto/-1;display:flex;justify-content:flex-end;margin-left:2px;visibility:hidden}.panel-list-row .panel-list-more-options .Button{width:24px;height:24px;min-width:24px}.panel-list-row .panel-list-more-options .Button:focus{color:var(--blue-6)}.panel-list-row .panel-list-more-options .Button .Icon{width:12px;height:12px}.panel-list-description{grid-area:2/-3/auto/-2;display:flex;align-items:center;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;padding:2px 0 2px 4px;height:24px}.panel-list-children{padding-left:32px}\", \"\"]);\n\n// exports\n"], "sourceRoot": ""}