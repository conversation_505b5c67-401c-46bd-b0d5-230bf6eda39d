{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/bg.js"], "names": ["module", "exports", "e", "t", "default", "_", "d", "name", "weekdays", "split", "weekdaysShort", "weekdaysMin", "months", "monthsShort", "weekStart", "ordinal", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "relativeTime", "future", "past", "s", "m", "mm", "h", "hh", "dd", "M", "MM", "y", "yy", "locale"], "mappings": "gFAAoEA,EAAOC,QAA6K,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAGI,EAAE,CAACC,KAAK,KAAKC,SAAS,yDAAyDC,MAAM,KAAKC,cAAc,8BAA8BD,MAAM,KAAKE,YAAY,uBAAuBF,MAAM,KAAKG,OAAO,oFAAoFH,MAAM,KAAKI,YAAY,kDAAkDJ,MAAM,KAAKK,UAAU,EAAEC,QAAQ,SAASb,GAAG,IAAIG,EAAEH,EAAE,IAAI,GAAGG,EAAE,IAAIA,EAAE,GAAG,OAAOH,EAAE,MAAM,IAAIC,EAAED,EAAE,GAAG,OAAO,IAAIC,EAAED,EAAE,MAAM,IAAIC,EAAED,EAAE,MAAM,IAAIC,GAAG,IAAIA,EAAED,EAAE,MAAMA,EAAE,OAAOc,QAAQ,CAACC,GAAG,OAAOC,IAAI,UAAUC,EAAE,YAAYC,GAAG,cAAcC,IAAI,mBAAmBC,KAAK,0BAA0BC,aAAa,CAACC,OAAO,UAAUC,KAAK,WAAWC,EAAE,kBAAkBC,EAAE,SAASC,GAAG,YAAYC,EAAE,MAAMC,GAAG,UAAUxB,EAAE,MAAMyB,GAAG,UAAUC,EAAE,QAAQC,GAAG,YAAYC,EAAE,SAASC,GAAG,cAAc,OAAOhC,EAAEC,QAAQgC,OAAO9B,EAAE,MAAK,GAAIA,EAA1mCD,CAAE,EAAQ", "file": "chunks/chunk.112.js", "sourcesContent": ["!function(e,_){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=_(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],_):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_bg=_(e.dayjs)}(this,(function(e){\"use strict\";function _(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var t=_(e),d={name:\"bg\",weekdays:\"неделя_понеделник_вторник_сряда_четвъртък_петък_събота\".split(\"_\"),weekdaysShort:\"нед_пон_вто_сря_чет_пет_съб\".split(\"_\"),weekdaysMin:\"нд_пн_вт_ср_чт_пт_сб\".split(\"_\"),months:\"януари_февруари_март_април_май_юни_юли_август_септември_октомври_ноември_декември\".split(\"_\"),monthsShort:\"янр_фев_мар_апр_май_юни_юли_авг_сеп_окт_ное_дек\".split(\"_\"),weekStart:1,ordinal:function(e){var _=e%100;if(_>10&&_<20)return e+\"-ти\";var t=e%10;return 1===t?e+\"-ви\":2===t?e+\"-ри\":7===t||8===t?e+\"-ми\":e+\"-ти\"},formats:{LT:\"H:mm\",LTS:\"H:mm:ss\",L:\"D.MM.YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY H:mm\",LLLL:\"dddd, D MMMM YYYY H:mm\"},relativeTime:{future:\"след %s\",past:\"преди %s\",s:\"няколко секунди\",m:\"минута\",mm:\"%d минути\",h:\"час\",hh:\"%d часа\",d:\"ден\",dd:\"%d дена\",M:\"месец\",MM:\"%d месеца\",y:\"година\",yy:\"%d години\"}};return t.default.locale(d,null,!0),d}));"], "sourceRoot": ""}