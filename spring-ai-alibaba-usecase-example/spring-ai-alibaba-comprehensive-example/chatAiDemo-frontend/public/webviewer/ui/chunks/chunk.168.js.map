{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/it-ch.js"], "names": ["module", "exports", "e", "n", "default", "o", "t", "name", "weekdays", "split", "months", "weekStart", "weekdaysShort", "monthsShort", "weekdaysMin", "ordinal", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "relativeTime", "future", "past", "s", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "locale"], "mappings": "gFAAoEA,EAAOC,QAAgL,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAGI,EAAE,CAACC,KAAK,QAAQC,SAAS,2DAA2DC,MAAM,KAAKC,OAAO,gGAAgGD,MAAM,KAAKE,UAAU,EAAEC,cAAc,8BAA8BH,MAAM,KAAKI,YAAY,kDAAkDJ,MAAM,KAAKK,YAAY,uBAAuBL,MAAM,KAAKM,QAAQ,SAASb,GAAG,OAAOA,GAAGc,QAAQ,CAACC,GAAG,QAAQC,IAAI,WAAWC,EAAE,aAAaC,GAAG,cAAcC,IAAI,oBAAoBC,KAAK,0BAA0BC,aAAa,CAACC,OAAO,SAASC,KAAK,QAAQC,EAAE,iBAAiBC,EAAE,YAAYC,GAAG,YAAYC,EAAE,SAASC,GAAG,SAASC,EAAE,YAAYC,GAAG,YAAYC,EAAE,UAAUC,GAAG,UAAUC,EAAE,UAAUC,GAAG,YAAY,OAAOjC,EAAEC,QAAQiC,OAAO/B,EAAE,MAAK,GAAIA,EAA9hCD,CAAE,EAAQ", "file": "chunks/chunk.168.js", "sourcesContent": ["!function(e,o){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=o(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],o):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_it_ch=o(e.dayjs)}(this,(function(e){\"use strict\";function o(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var n=o(e),t={name:\"it-ch\",weekdays:\"domenica_lunedì_martedì_mercoledì_giovedì_venerdì_sabato\".split(\"_\"),months:\"gennaio_febbraio_marzo_aprile_maggio_giugno_luglio_agosto_settembre_ottobre_novembre_dicembre\".split(\"_\"),weekStart:1,weekdaysShort:\"dom_lun_mar_mer_gio_ven_sab\".split(\"_\"),monthsShort:\"gen_feb_mar_apr_mag_giu_lug_ago_set_ott_nov_dic\".split(\"_\"),weekdaysMin:\"do_lu_ma_me_gi_ve_sa\".split(\"_\"),ordinal:function(e){return e},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD.MM.YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY HH:mm\",LLLL:\"dddd D MMMM YYYY HH:mm\"},relativeTime:{future:\"tra %s\",past:\"%s fa\",s:\"alcuni secondi\",m:\"un minuto\",mm:\"%d minuti\",h:\"un'ora\",hh:\"%d ore\",d:\"un giorno\",dd:\"%d giorni\",M:\"un mese\",MM:\"%d mesi\",y:\"un anno\",yy:\"%d anni\"}};return n.default.locale(t,null,!0),t}));"], "sourceRoot": ""}