(window.webpackJsonp=window.webpackJsonp||[]).push([[72],{1730:function(e,t,n){var r=n(32),a=n(1731);"string"==typeof(a=a.__esModule?a.default:a)&&(a=[[e.i,a,""]]);var o={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const r=[];return n.querySelectorAll(t).forEach(e=>r.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&r.push(...e(t,n.shadowRoot))}),r}("apryse-webviewer"));const n=[];for(let r=0;r<t.length;r++){const a=t[r];if(0===r)a.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);a.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};r(a,o);e.exports=a.locals||{}},1731:function(e,t,n){(t=e.exports=n(33)(!1)).push([e.i,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.RubberStampPanel{height:100%;display:flex;flex-direction:column}.RubberStampPanel .rubber-stamp-panel-header{font-size:16px;font-weight:700}.RubberStampPanel .CreateRubberStampButton{width:100%;padding:8px;color:var(--blue-5);border:1px solid var(--blue-5);border-radius:4px;margin:16px 0}.RubberStampPanel .CreateRubberStampButton:hover{color:var(--primary-button-hover);border-color:var(--primary-button-hover)}.RubberStampPanel .rubber-stamps-container{overflow:auto;padding:0 4px}.RubberStampPanel .rubber-stamps-container .collapsible-page-group-header button{font-size:14px;font-weight:700;margin:16px 0}.RubberStampPanel .rubber-stamps-container .rubber-stamps-list{display:flex;flex-direction:column;grid-gap:4px;gap:4px;padding-top:2px;padding-bottom:16px}.RubberStampPanel .rubber-stamps-container .rubber-stamps-list .rubber-stamp{border:none;background-color:transparent;height:48px;padding:0 8px;border:1px solid var(--border);border-radius:4px;display:flex;align-items:center;justify-content:center;cursor:pointer;background-color:var(--gray-0);flex-grow:1}:host(:not([data-tabbing=true])) .RubberStampPanel .rubber-stamps-container .rubber-stamps-list .rubber-stamp,html:not([data-tabbing=true]) .RubberStampPanel .rubber-stamps-container .rubber-stamps-list .rubber-stamp{outline:none}.RubberStampPanel .rubber-stamps-container .rubber-stamps-list .rubber-stamp img{max-width:100%;max-height:100%}.RubberStampPanel .rubber-stamps-container .rubber-stamps-list .rubber-stamp:hover{border:1px solid var(--blue-6)}.RubberStampPanel .rubber-stamps-container .rubber-stamps-list .rubber-stamp.active{border:1px solid var(--blue-5);background:var(--gray-2)}.RubberStampPanel .custom-rubber-stamp-row{display:flex;align-items:center;grid-gap:8px;gap:8px}.RubberStampPanel .custom-rubber-stamp-row .icon-button{padding:0;border:none;background-color:transparent;display:flex;align-items:center;justify-content:center;width:34px;height:34px;border-radius:4px;cursor:pointer}:host(:not([data-tabbing=true])) .RubberStampPanel .custom-rubber-stamp-row .icon-button,html:not([data-tabbing=true]) .RubberStampPanel .custom-rubber-stamp-row .icon-button{outline:none}.RubberStampPanel .custom-rubber-stamp-row .icon-button:hover{border:1px solid var(--blue-6);background:var(--gray-2)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RubberStampPanel{width:100%}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RubberStampPanel .rubber-stamp-panel-header{margin:0 16px 16px}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RubberStampPanel [data-element=createRubberStampButtonWrap]{padding:0 16px}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RubberStampPanel [data-element=createRubberStampButtonWrap] .CreateRubberStampButton{margin-top:0}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RubberStampPanel.small-size .collapsible-page-group-header,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RubberStampPanel.small-size .CreateRubberStampButton,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RubberStampPanel.small-size .Divider,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RubberStampPanel.small-size .rubber-stamps-list-header{display:none}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RubberStampPanel .rubber-stamps-container{position:relative;padding:0 14px 16px;overflow-y:scroll}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RubberStampPanel .rubber-stamps-container.small-size{overflow:hidden}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RubberStampPanel .rubber-stamps-container .rubber-stamps-list .icon-button:hover,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RubberStampPanel .rubber-stamps-container .rubber-stamps-list .rubber-stamp:hover{background-color:var(--gray-0)}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RubberStampPanel .rubber-stamps-container .rubber-stamps-list .icon-button.active,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RubberStampPanel .rubber-stamps-container .rubber-stamps-list .rubber-stamp.active{background:var(--tools-overlay-button-active)}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RubberStampPanel .rubber-stamps-container .Divider{margin:0}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RubberStampPanel{width:100%}.App.is-web-component:not(.is-in-desktop-only-mode) .RubberStampPanel .rubber-stamp-panel-header{margin:0 16px 16px}.App.is-web-component:not(.is-in-desktop-only-mode) .RubberStampPanel [data-element=createRubberStampButtonWrap]{padding:0 16px}.App.is-web-component:not(.is-in-desktop-only-mode) .RubberStampPanel [data-element=createRubberStampButtonWrap] .CreateRubberStampButton{margin-top:0}.App.is-web-component:not(.is-in-desktop-only-mode) .RubberStampPanel.small-size .collapsible-page-group-header,.App.is-web-component:not(.is-in-desktop-only-mode) .RubberStampPanel.small-size .CreateRubberStampButton,.App.is-web-component:not(.is-in-desktop-only-mode) .RubberStampPanel.small-size .Divider,.App.is-web-component:not(.is-in-desktop-only-mode) .RubberStampPanel.small-size .rubber-stamps-list-header{display:none}.App.is-web-component:not(.is-in-desktop-only-mode) .RubberStampPanel .rubber-stamps-container{position:relative;padding:0 14px 16px;overflow-y:scroll}.App.is-web-component:not(.is-in-desktop-only-mode) .RubberStampPanel .rubber-stamps-container.small-size{overflow:hidden}.App.is-web-component:not(.is-in-desktop-only-mode) .RubberStampPanel .rubber-stamps-container .rubber-stamps-list .icon-button:hover,.App.is-web-component:not(.is-in-desktop-only-mode) .RubberStampPanel .rubber-stamps-container .rubber-stamps-list .rubber-stamp:hover{background-color:var(--gray-0)}.App.is-web-component:not(.is-in-desktop-only-mode) .RubberStampPanel .rubber-stamps-container .rubber-stamps-list .icon-button.active,.App.is-web-component:not(.is-in-desktop-only-mode) .RubberStampPanel .rubber-stamps-container .rubber-stamps-list .rubber-stamp.active{background:var(--tools-overlay-button-active)}.App.is-web-component:not(.is-in-desktop-only-mode) .RubberStampPanel .rubber-stamps-container .Divider{margin:0}}.RubberStampPanel.modular-ui-panel .rubber-stamps-list .rubber-stamp{border-color:var(--lighter-border)}.RubberStampPanel.modular-ui-panel .rubber-stamps-list .rubber-stamp.focus-visible,.RubberStampPanel.modular-ui-panel .rubber-stamps-list .rubber-stamp:focus-visible{outline:var(--focus-visible-outline)!important}",""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1762:function(e,t,n){"use strict";n.r(t);n(15),n(19),n(11),n(13),n(8),n(14),n(10),n(9),n(12),n(16),n(20),n(18),n(57),n(22),n(63),n(64),n(65),n(66),n(36),n(39),n(23),n(24),n(40),n(62),n(26),n(27),n(25);var r=n(0),a=n.n(r),o=n(428),i=n(6),l=n(3),u=n(2),s=n(1),c=n(17),p=n.n(c),b=n(5),m=n(31),d=n(72),f=n(54),h=n(76),y=n(44),v=n(100);function S(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,i,l=[],u=!0,s=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);u=!0);}catch(e){s=!0,a=e}finally{try{if(!u&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw a}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return g(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return g(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function g(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var w=function(){var e=Object(i.d)(),t=S(Object(o.a)(),1)[0],n=Object(v.a)((function(){e(u.a.openElement(b.a.CUSTOM_STAMP_MODAL))}));return a.a.createElement(h.a,{dataElement:b.a.CREATE_RUBBER_STAMP_BUTTON_WRAP},a.a.createElement(y.a,{className:"CreateRubberStampButton",dataElement:b.a.CREATE_RUBBER_STAMP_BUTTON,label:t("component.createStampButton"),onClick:n}))},x=n(444),A=(n(1730),n(38),n(192)),R=n(60),E=n(1642),P=n(4),k=n.n(P);function j(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,i,l=[],u=!0,s=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);u=!0);}catch(e){s=!0,a=e}finally{try{if(!u&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw a}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return O(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return O(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function O(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var L=a.a.memo((function(e){var t=e.imgSrc,n=e.annotation,r=e.index,i=e.onClick,l=e.isActive,u=j(Object(o.a)(),1)[0],s="rubberStamp.".concat(n.Icon);return a.a.createElement("button",{tabIndex:0,key:r,className:p()("rubber-stamp",{active:l}),"aria-label":u(s),onClick:function(){return i(n,r)},"aria-current":l},a.a.createElement("img",{src:t,alt:""}))}));L.displayName="RubberStamp",L.propTypes={imgSrc:k.a.string,annotation:k.a.object,index:k.a.number,onClick:k.a.func,isActive:k.a.bool};var I=function(e){var t=e.standardStamps,n=e.selectedStampIndex,u=e.setSelectedRubberStamp,s=j(Object(o.a)(),1)[0],c=Object(R.b)(),p=j(Object(i.e)((function(e){return[l.a.getMobilePanelSize(e),l.a.getLastSelectedStampIndex(e)]})),2),b=p[0],d=p[1],f=t.map((function(e,t){var r=e.imgSrc,o=e.annotation,i=n===t,l=t===(d||0)&&Object(E.isNull)(n);return!c||c&&b!==m.a.SMALL_SIZE||c&&b===m.a.SMALL_SIZE&&(i||l)?a.a.createElement(L,{key:t,index:t,imgSrc:r,annotation:o,onClick:u,isActive:i}):null})),h=Object(r.useCallback)((function(){return s("rubberStampPanel.standard")}),[s]);return a.a.createElement(A.a,{header:h,headingLevel:2,ariaControls:"rubber-stamps-list",expansionDescription:s("rubberStampPanel.standard")},a.a.createElement("div",{className:"rubber-stamps-list",id:"rubber-stamps-list"},f))};I.displayName="StandardRubberStamps";var C=a.a.memo(I);n(35);function T(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,i,l=[],u=!0,s=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);u=!0);}catch(e){s=!0,a=e}finally{try{if(!u&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw a}}return l}}(e,t)||_(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _(e,t){if(e){if("string"==typeof e)return N(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?N(e,t):void 0}}function N(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var B=a.a.memo((function(e){var t,n=e.imgSrc,r=e.annotation,i=e.index,l=e.onClick,u=e.deleteHandler,s=e.standardStampsOffset,c=e.isActive,b=T(Object(o.a)(),1)[0],m=r.getCustomData("trn-custom-stamp");try{t=JSON.parse(m)}catch(e){t={title:"".concat(b("annotation.defaultCustomStampTitle"))}}return a.a.createElement("div",{className:"custom-rubber-stamp-row",tabIndex:"-1"},a.a.createElement("button",{key:i,className:p()("rubber-stamp",{active:c}),"aria-label":"".concat(b("annotation.stamp")," ").concat(t.title," ").concat(t.author," ").concat(r.DateCreated),onClick:function(){return l(r,i+s)},"aria-current":c},a.a.createElement("img",{src:n,alt:""})),a.a.createElement(y.a,{"data-element":"defaultSignatureDeleteButton",onClick:function(){u(i)},img:"icon-delete-line",ariaLabel:"".concat(b("action.delete")," ").concat(b("annotation.stamp")," ").concat(i+1)}))}));B.displayName="CustomRubberStamp",B.propTypes={imgSrc:k.a.string,annotation:k.a.object,index:k.a.number,onClick:k.a.func,deleteHandler:k.a.func,standardStampsOffset:k.a.number,isActive:k.a.bool};var M=function(e){var t=e.customStamps,n=e.selectedStampIndex,o=e.setSelectedRubberStamp,c=e.standardStampsOffset,p=s.a.getToolsFromAllDocumentViewers("AnnotationCreateRubberStamp"),b=Object(R.b)(),d=Object(i.d)(),f=T(Object(i.e)((function(e){return[l.a.getMobilePanelSize(e),l.a.getLastSelectedStampIndex(e)]})),2),h=f[0],y=f[1],v=Object(r.useCallback)((function(e){var t,n,r=function(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=_(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,a=function(){};return{s:a,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,i=!0,l=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return i=e.done,e},e:function(e){l=!0,o=e},f:function(){try{i||null==n.return||n.return()}finally{if(l)throw o}}}}(p);try{for(r.s();!(t=r.n()).done;){var a=t.value,o=a.getCustomStamps();a.deleteCustomStamps([o[e]]);var i=(n=e,0===a.getCustomStamps().length?null:0===n?c:n-1>=0?n-1+c:n+c);d(u.a.setSelectedStampIndex(null)),d(u.a.setLastSelectedStampIndex(i))}}catch(e){r.e(e)}finally{r.f()}}),[]),S=b&&h!==m.a.SMALL_SIZE,g=b&&h===m.a.SMALL_SIZE,w=t.map((function(e,r){var i=e.imgSrc,l=e.annotation,u=r+c,s=n===u,p=g&&(Object(E.isNull)(n)&&t.length&&u===y||s);return!b||S||p||g&&s?a.a.createElement(B,{key:r,index:r,imgSrc:i,annotation:l,onClick:o,standardStampsOffset:c,deleteHandler:v,isActive:s}):null}));return 0===t.length?null:a.a.createElement("div",{className:"rubber-stamps-list"},w)};M.displayName="CustomRubberStamps",M.propTypes={customStamps:k.a.array,selectedStampIndex:k.a.number,setSelectedRubberStamp:k.a.func,standardStampsOffset:k.a.number};var z=a.a.memo(M),D=n(103);function H(e){return(H="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function U(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==H(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==H(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===H(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function G(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */G=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,r=Object.defineProperty||function(e,t,n){e[t]=n.value},a="function"==typeof Symbol?Symbol:{},o=a.iterator||"@@iterator",i=a.asyncIterator||"@@asyncIterator",l=a.toStringTag||"@@toStringTag";function u(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,n){return e[t]=n}}function s(e,t,n,a){var o=t&&t.prototype instanceof b?t:b,i=Object.create(o.prototype),l=new E(a||[]);return r(i,"_invoke",{value:w(e,n,l)}),i}function c(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=s;var p={};function b(){}function m(){}function d(){}var f={};u(f,o,(function(){return this}));var h=Object.getPrototypeOf,y=h&&h(h(P([])));y&&y!==t&&n.call(y,o)&&(f=y);var v=d.prototype=b.prototype=Object.create(f);function S(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function g(e,t){var a;r(this,"_invoke",{value:function(r,o){function i(){return new t((function(a,i){!function r(a,o,i,l){var u=c(e[a],e,o);if("throw"!==u.type){var s=u.arg,p=s.value;return p&&"object"==H(p)&&n.call(p,"__await")?t.resolve(p.__await).then((function(e){r("next",e,i,l)}),(function(e){r("throw",e,i,l)})):t.resolve(p).then((function(e){s.value=e,i(s)}),(function(e){return r("throw",e,i,l)}))}l(u.arg)}(r,o,a,i)}))}return a=a?a.then(i,i):i()}})}function w(e,t,n){var r="suspendedStart";return function(a,o){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===a)throw o;return k()}for(n.method=a,n.arg=o;;){var i=n.delegate;if(i){var l=x(i,n);if(l){if(l===p)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var u=c(e,t,n);if("normal"===u.type){if(r=n.done?"completed":"suspendedYield",u.arg===p)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(r="completed",n.method="throw",n.arg=u.arg)}}}function x(e,t){var n=t.method,r=e.iterator[n];if(void 0===r)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,x(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),p;var a=c(r,e.iterator,t.arg);if("throw"===a.type)return t.method="throw",t.arg=a.arg,t.delegate=null,p;var o=a.arg;return o?o.done?(t[e.resultName]=o.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,p):o:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,p)}function A(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function R(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function E(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(A,this),this.reset(!0)}function P(e){if(e){var t=e[o];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,a=function t(){for(;++r<e.length;)if(n.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return a.next=a}}return{next:k}}function k(){return{value:void 0,done:!0}}return m.prototype=d,r(v,"constructor",{value:d,configurable:!0}),r(d,"constructor",{value:m,configurable:!0}),m.displayName=u(d,l,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===m||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,d):(e.__proto__=d,u(e,l,"GeneratorFunction")),e.prototype=Object.create(v),e},e.awrap=function(e){return{__await:e}},S(g.prototype),u(g.prototype,i,(function(){return this})),e.AsyncIterator=g,e.async=function(t,n,r,a,o){void 0===o&&(o=Promise);var i=new g(s(t,n,r,a),o);return e.isGeneratorFunction(n)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},S(v),u(v,l,"Generator"),u(v,o,(function(){return this})),u(v,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},e.values=P,E.prototype={constructor:E,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(R),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(n,r){return i.type="throw",i.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var a=this.tryEntries.length-1;a>=0;--a){var o=this.tryEntries[a],i=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var l=n.call(o,"catchLoc"),u=n.call(o,"finallyLoc");if(l&&u){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r];if(a.tryLoc<=this.prev&&n.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var o=a;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=e,i.arg=t,o?(this.method="next",this.next=o.finallyLoc,p):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),p},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),R(n),p}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var a=r.arg;R(n)}return a}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:P(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),p}},e}function F(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=Z(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,a=function(){};return{s:a,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,i=!0,l=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return i=e.done,e},e:function(e){l=!0,o=e},f:function(){try{i||null==n.return||n.return()}finally{if(l)throw o}}}}function W(e,t,n,r,a,o,i){try{var l=e[o](i),u=l.value}catch(e){return void n(e)}l.done?t(u):Promise.resolve(u).then(r,a)}function V(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,i,l=[],u=!0,s=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);u=!0);}catch(e){s=!0,a=e}finally{try{if(!u&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw a}}return l}}(e,t)||Z(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Z(e,t){if(e){if("string"==typeof e)return Y(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Y(e,t):void 0}}function Y(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var $=function(){var e,t=V(Object(o.a)(),1)[0],n=Object(i.d)(),c=s.a.getToolsFromAllDocumentViewers("AnnotationCreateRubberStamp"),y=Object(R.b)(),v=Object(i.e)(l.a.getStandardStamps,i.c),S=Object(i.e)(l.a.getCustomStamps,i.c),g=Object(i.e)(l.a.getSelectedStampIndex),A=Object(i.e)(l.a.getMobilePanelSize),E=Object(i.e)(l.a.getFeatureFlags,i.c),P=Object(i.f)(),k=E.customizableUI,j=Object(r.useCallback)(function(){var e,r=(e=G().mark((function e(r,a){var o,i,l,p;return G().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:s.a.setToolMode("AnnotationCreateRubberStamp"),o=F(c),e.prev=2,o.s();case 4:if((i=o.n()).done){e.next=12;break}return l=i.value,p=t("rubberStamp.".concat(r.Icon)),e.next=9,l.setRubberStamp(r,p);case 9:l.showPreview();case 10:e.next=4;break;case 12:e.next=17;break;case 14:e.prev=14,e.t0=e.catch(2),o.e(e.t0);case 17:return e.prev=17,o.f(),e.finish(17);case 20:n(u.a.setSelectedStampIndex(a)),y&&A!==m.a.SMALL_SIZE&&n(u.a.setMobilePanelSize(m.a.SMALL_SIZE));case 22:case"end":return e.stop()}}),e,null,[[2,14,17,20]])})),function(){var t=this,n=arguments;return new Promise((function(r,a){var o=e.apply(t,n);function i(e){W(o,r,a,i,l,"next",e)}function l(e){W(o,r,a,i,l,"throw",e)}i(void 0)}))});return function(e,t){return r.apply(this,arguments)}}(),[]);return Object(r.useEffect)((function(){n(u.a.setSelectedStampIndex(null))}),[]),Object(r.useEffect)((function(){var e=function(e){var t=s.a.getToolMode(),n=null==t?void 0:t.name,r=e.detail,a=r.element,o=r.isVisible;a!==m.e.RUBBER_STAMP||o||"AnnotationCreateRubberStamp"!==n&&n!==d.a||Object(D.a)(P,d.a)};return window.addEventListener(f.a.VISIBILITY_CHANGED,e),function(){window.removeEventListener(f.a.VISIBILITY_CHANGED,e)}}),[]),a.a.createElement(h.a,{dataElement:b.a.RUBBER_STAMP_PANEL,className:p()((e={Panel:!0,RubberStampPanel:!0},U(e,A,y),U(e,"modular-ui-panel",k),e))},a.a.createElement("h1",{className:"rubber-stamp-panel-header"},t("rubberStampPanel.header")),a.a.createElement(w,null),a.a.createElement("div",{className:p()(U({"rubber-stamps-container":!0},A,y))},a.a.createElement(z,{selectedStampIndex:g,standardStampsOffset:v.length,setSelectedRubberStamp:j,customStamps:S}),a.a.createElement(x.a,null),a.a.createElement(C,{setSelectedRubberStamp:j,standardStamps:v,selectedStampIndex:g})))};t.default=$}}]);
//# sourceMappingURL=chunk.72.js.map