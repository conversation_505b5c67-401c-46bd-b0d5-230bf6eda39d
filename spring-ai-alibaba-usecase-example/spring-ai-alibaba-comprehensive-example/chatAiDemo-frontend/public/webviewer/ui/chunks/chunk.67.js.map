{"version": 3, "sources": ["webpack:///./src/ui/src/components/MeasurementOverlay/MeasurementOverlay.scss?3e37", "webpack:///./src/ui/src/components/MeasurementOverlay/MeasurementOverlay.scss", "webpack:///./src/ui/src/components/MeasurementOverlay/CustomMeasurementOverlay.js", "webpack:///./src/ui/src/components/MeasurementOverlay/CountMeasurementOverlay.js", "webpack:///./src/ui/src/components/MeasurementOverlay/MeasurementOverlay.js", "webpack:///./src/ui/src/components/MeasurementOverlay/index.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "CustomMeasurementOverlay", "props", "type", "mapAnnotationToKey", "annotation", "CustomEllipseMeasurementOverlay", "console", "error", "renderAppropriateOverlay", "factor", "annotationKey", "icon", "getDataWithKey", "t", "distanceMeasurementTool", "core", "getTool", "precision", "defaults", "Precision", "scale", "Scale", "measure", "Measure", "unit", "className", "Icon", "glyph", "title", "label", "min", "value", "axis", "toFixed", "onChange", "event", "propTypes", "PropTypes", "object", "isRequired", "func", "string", "withTranslation", "CountMeasurementOverlay", "useTranslation", "annotationCount", "getAnnotationsList", "filter", "getCustomData", "MeasurementOverlay", "setValue", "isDisabled", "useSelector", "state", "selectors", "isElementDisabled", "DataElements", "MEASUREMENT_OVERLAY", "forceUpdate", "useState", "dispatch", "useDispatch", "x", "y", "position", "setPosition", "transparentBackground", "setTransparentBackground", "isCreatingAnnotation", "setIsCreatingAnnotation", "isOpen", "isElementOpen", "activeToolName", "getActiveToolName", "customMeasurementOverlay", "getCustomMeasurementOverlay", "overlayRef", "useRef", "key", "shouldShowCustomOverlay", "annot", "includes", "isCountMeasurementAnnotation", "some", "overlay", "validate", "onMouseMove", "e", "tool", "useTransparentBackground", "insideRect", "overlayElement", "overlayRect", "getBoundingClientRect", "touches", "TouchEvent", "clientX", "clientY", "left", "right", "top", "bottom", "isMouseInsideRect", "current", "drawMode", "getToolMode", "getDrawMode", "annotUnderMouse", "getAnnotationByMouseEvent", "actions", "openElement", "onAnnotationChanged", "annotations", "action", "closeElement", "useEffect", "addEventListener", "removeEventListener", "cancel", "onDrag", "onStop", "classNames", "Overlay", "open", "closed", "transparent", "ref", "data-element", "customOverlay", "renderOverlay"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,mkEAAokE,KAG7lE0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,yRCHvB,SAASC,EAAyBC,GAUhC,OATiC,SAACC,GAEhC,OADuBC,YAAmBF,EAAMG,aAE9C,IAAK,UACH,OAAO,kBAACC,EAAoCJ,GAC9C,QACEK,QAAQC,MAAM,uCAAD,OAAwCL,EAAI,oBAGvDM,CAAyBP,EAAMC,MAYzC,SAASG,EAAgCJ,GACvC,IAkBQQ,EAlBFC,EAAgBP,YAAmBF,EAAMG,YACvCO,EAASC,YAAeF,GAAxBC,KACAE,EAAMZ,EAANY,EAKFC,EAA0BC,IAAKC,QAAQ,uCACvCC,EAAYH,EAAwBI,SAASC,UAC7CC,EAAQN,EAAwBI,SAASG,MACzCC,EAAUR,EAAwBS,QAClCC,EAAOJ,EAAM,GAAG,GAYtB,OACE,oCACE,yBAAKK,UAAU,sBACZd,GAAQ,kBAACe,EAAA,EAAI,CAACD,UAAU,oBAAoBE,MAAOhB,IACnDV,EAAM2B,OAET,yBAAKH,UAAU,sBACZZ,EAAE,mCAAmC,KAhBtB,UAAYO,EAAM,GAAG,GAAE,YAAIA,EAAM,GAAG,GAAE,cAAMA,EAAM,GAAG,GAAE,YAAIA,EAAM,GAAG,KAkBtF,yBAAKK,UAAU,0BACZZ,EAAE,2BAA2B,KAAGI,GAEnC,yBAAKQ,UAAU,sBACZxB,EAAM4B,MAAM,KAAE,2BAAOJ,UAAU,uBAAuBvB,KAAK,SAAS4B,IAAI,IAAIC,OAlB3EtB,EAASa,EAAQU,KAAK,GAAGvB,QACXR,EAAM8B,MAAM9B,EAAMG,YAAY6B,QAF5B,GAGAxB,GAAQwB,QAHR,IAmBmFC,SAAU,SAACC,GAAK,OAAKlC,EAAMiC,SAASC,EAAOlC,EAAMG,eAAc,IAAEoB,IA9C9KxB,EAAyBoC,UAAY,CACnChC,WAAYiC,IAAUC,OAAOC,WAC7BR,MAAOM,IAAUG,KAAKD,WACtBL,SAAUG,IAAUG,KAAKD,WACzBV,MAAOQ,IAAUI,OAAOF,WACxB1B,EAAGwB,IAAUG,KAAKD,WAClBX,MAAOS,IAAUI,OAAOF,YA8C1BlC,EAAgC+B,UAAYpC,EAAyBoC,UAEtDM,oBAAkB1C,G,SC/DjC,SAAS2C,EAAwB1C,GAC/B,IAAMS,EAAgBP,YAAmBF,EAAMG,YACvCO,EAASC,YAAeF,GAAxBC,KACAE,EAAM+B,cAAN/B,EAMFgC,EALiB9B,IAAK+B,qBAEsBC,QAAO,SAAC3C,GACxD,OAAOA,EAAW4C,cAAc,mBAEiBlE,OAEnD,OACE,oCACE,yBAAK2C,UAAU,sBACZd,GAAQ,kBAACe,EAAA,EAAI,CAACD,UAAU,oBAAoBE,MAAOhB,IACnDE,EAAE,+CAEL,yBAAKY,UAAU,sBACZZ,EAAE,mCAAmC,KAAGgC,IAtBjDF,EAAwBP,UAAY,CAClChC,WAAYiC,IAAUC,OAAOC,YA2BhBI,Q,ixCCUf,IAAMP,EAAY,CAChBhC,WAAYiC,IAAUC,QAGlBW,EAAqB,SAAChD,GAC1B,IATSiD,EASD9C,EAAeH,EAAfG,WACF+C,EAAaC,aAAY,SAACC,GAAK,OAAKC,IAAUC,kBAAkBF,EAAOG,IAAaC,wBAEpFC,GAZGR,EAAuB,EAAXS,mBAAS,GAAE,GAAf,GACV,kBAAMT,GAAS,SAACnB,GAAK,OAAKA,EAAQ,OAYnC6B,EAAWC,cACuC,IAAxBF,mBAAS,CAAEG,EAAG,EAAGC,EAAG,IAAI,GAAjDC,EAAQ,KAAEC,EAAW,KAC6C,IAAfN,oBAAS,GAAM,GAAlEO,EAAqB,KAAEC,EAAwB,KACiB,IAAfR,oBAAS,GAAM,GAAhES,EAAoB,KAAEC,EAAuB,KAC9CC,EAASlB,aAAY,SAACC,GAAK,OAAKC,IAAUiB,cAAclB,EAAOG,IAAaC,wBAC5Ee,EAAiBpB,aAAY,SAACC,GAAK,OAAKC,IAAUmB,kBAAkBpB,MACpEqB,EAA2BtB,aAAY,SAACC,GAAK,OAAKC,IAAUqB,4BAA4BtB,MACxFuB,EAAaC,mBACbC,EAAM3E,YAAmBC,GAEzB2E,EAA0B,SAACC,GAAK,OA3BH,SAACA,GAAK,MAAK,CAAC,oBAAoBC,SAAS9E,YAAmB6E,IA2BlDE,CAA6BF,IAAUN,EAAyBS,MAAK,SAACC,GAAO,OAAKA,EAAQC,SAASL,OAiB1IM,EAAc,SAACC,GACnB,IAAMC,EAAOzE,IAAKC,QAAQwD,GAE1B,GAAIpE,EAAY,CACd,IACIqF,EADEC,EAtEc,SAACH,EAAGI,GAC5B,GAAuB,OAAnBA,EACF,OAAO,EAGT,IACI7B,EAAOC,EADL6B,EAAcD,EAAeE,wBASnC,OAPIN,EAAEO,SAAWP,aAAaQ,YAC5BjC,EAAIyB,EAAEO,QAAQ,GAAGE,QACjBjC,EAAIwB,EAAEO,QAAQ,GAAGG,UAEjBnC,EAAIyB,EAAES,QACNjC,EAAIwB,EAAEU,SAGNnC,GAAK8B,EAAYM,MACjBpC,GAAK8B,EAAYO,OACjBpC,GAAK6B,EAAYQ,KACjBrC,GAAK6B,EAAYS,OAoDIC,CAAkBf,EAAGX,EAAW2B,SAGnD,GAAInC,EAAsB,SAClBoC,EAAyC,QAAjC,GAAG,EAAAzF,IAAK0F,eAAcC,mBAAW,aAA9B,UACjBjB,EAA2BC,GAA2B,cAAbc,MACpC,CACL,IAAMG,EAAkB5F,IAAK6F,0BAA0BrB,GACvDE,EAA2BC,GAAciB,IAAoBvG,EAE/D+D,EAAyBsB,GACzB/B,SACSqB,EAAwBS,EAAKpF,cACtCwD,EAASiD,IAAQC,YAAYtD,IAAaC,sBAG1CY,GAAwB,KAItB0C,EAAsB,SAACC,EAAaC,GAK3B,QAAXA,GACuB,IAAvBD,EAAYlI,QACZkI,EAAY,KAAO5G,GAEnBwD,EAASiD,IAAQK,aAAa1D,IAAaC,sBAIhC,WAAXwD,GACuB,IAAvBD,EAAYlI,QACZkI,EAAY,KAAO5G,GAEnBsD,KAaJ,OATAyD,qBAAU,WAGR,OAFApG,IAAKqG,iBAAiB,YAAa9B,GACnCvE,IAAKqG,iBAAiB,oBAAqBL,GACpC,WACLhG,IAAKsG,oBAAoB,YAAa/B,GACtCvE,IAAKsG,oBAAoB,oBAAqBN,MAE/C,IAEC5D,IAAe/C,EACV,KAIP,kBAAC,IAAS,CACRkH,OAAO,QACPtD,SAAUA,EACVuD,OAAQ,SAAChC,EAAG,GAAF,IAAIzB,EAAC,EAADA,EAAGC,EAAC,EAADA,EAAC,OAAOE,EAAY,CAAEH,IAAGC,OAC1CyD,OAAQ,SAACjC,EAAG,GAAF,IAAIzB,EAAC,EAADA,EAAGC,EAAC,EAADA,EAAC,OAAOE,EAAY,CAAEH,IAAGC,QAE1C,yBACEtC,UAAWgG,IAAW,CACpBC,SAAS,EACTzE,oBAAoB,EACpB0E,KAAMrD,EACNsD,QAAStD,EACTuD,YAAa3D,IAEf4D,IAAKlD,EACLmD,eAAcvE,IAAaC,qBAzFX,SAACuB,EAAOF,GAC5B,OAAIC,EAAwBC,GAExB,kBAAC,EAAwB,GACvB5E,WAAY4E,GACRN,EAAyB3B,QAAO,SAACiF,GAAa,OAAKA,EAAc3C,SAASL,MAC5E,KAII,qBAARF,EACK,kBAAC,EAAuB,CAAC1E,WAAY4E,SAD9C,EAiFKiD,CAAc7H,EAAY0E,MAMnC7B,EAAmBb,UAAYA,EAEhBa,QCnKAA", "file": "chunks/chunk.67.js", "sourcesContent": ["var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./MeasurementOverlay.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".open.MeasurementOverlay{visibility:visible}.closed.MeasurementOverlay{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.MeasurementOverlay{position:absolute;z-index:95;border-radius:4px;box-shadow:0 0 3px 0 var(--document-box-shadow);background:var(--component-background);flex-direction:column;bottom:60px;right:14px;font-size:16px;min-width:215px;padding:6px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .MeasurementOverlay{bottom:8px;right:8px;padding:8px;font-size:14px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .MeasurementOverlay{bottom:8px;right:8px;padding:8px;font-size:14px}}.MeasurementOverlay>*{margin:6px}.MeasurementOverlay.transparent{opacity:.5;pointer-events:none}.measurement__value{display:flex;flex-direction:row}.measurement__value .distance-show{margin-left:3px;cursor:pointer}.measurement__title{display:flex;justify-content:center;align-items:center}.measurement__title .measurement__icon{margin-right:5px}.measurement__deltas{display:flex;justify-content:space-between}.lineMeasurementInput{width:50px;margin-left:4px}.length_input{margin-bottom:3px}.angle_input{margin-top:3px}.angle_input .lineMeasurementInput{width:65px}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React from 'react';\nimport PropTypes from 'prop-types';\nimport { withTranslation } from 'react-i18next';\nimport Icon from 'components/Icon';\nimport core from 'core';\nimport { mapAnnotationToKey, getDataWithKey } from '../../constants/map';\n\nfunction CustomMeasurementOverlay(props) {\n  const renderAppropriateOverlay = (type) => {\n    const annotationType = mapAnnotationToKey(props.annotation);\n    switch (annotationType) {\n      case 'ellipse':\n        return <CustomEllipseMeasurementOverlay {...props}/>;\n      default:\n        console.error(`Custom overlay for annotation type: ${type} not supported`);\n    }\n  };\n  return (renderAppropriateOverlay(props.type));\n}\n\nCustomMeasurementOverlay.propTypes = {\n  annotation: PropTypes.object.isRequired,\n  value: PropTypes.func.isRequired,\n  onChange: PropTypes.func.isRequired,\n  label: PropTypes.string.isRequired,\n  t: PropTypes.func.isRequired,\n  title: PropTypes.string.isRequired,\n};\n\nfunction CustomEllipseMeasurementOverlay(props) {\n  const annotationKey = mapAnnotationToKey(props.annotation);\n  const { icon } = getDataWithKey(annotationKey);\n  const { t } = props;\n\n  // Get the Scale, Precision and Units from\n  // the AnnotationCreateDistanceMeasurement tool as these do not exist\n  // in the Ellipse annotation\n  const distanceMeasurementTool = core.getTool('AnnotationCreateDistanceMeasurement');\n  const precision = distanceMeasurementTool.defaults.Precision;\n  const scale = distanceMeasurementTool.defaults.Scale;\n  const measure = distanceMeasurementTool.Measure;\n  const unit = scale[1][1];\n\n\n  const renderScaleRatio = () => `${scale[0][0]} ${scale[0][1]} = ${scale[1][0]} ${scale[1][1]}`;\n\n  const computeRadius = () => {\n    const decimalPlaces = 2;\n    const factor = measure.axis[0].factor;\n    const radiusInPts = props.value(props.annotation).toFixed(decimalPlaces);\n    return (radiusInPts * factor).toFixed(decimalPlaces);\n  };\n\n  return (\n    <>\n      <div className=\"measurement__title\">\n        {icon && <Icon className=\"measurement__icon\" glyph={icon} />}\n        {props.title}\n      </div>\n      <div className=\"measurement__scale\">\n        {t('option.measurementOverlay.scale')}: {renderScaleRatio()}\n      </div>\n      <div className=\"measurement__precision\">\n        {t('option.shared.precision')}: {precision}\n      </div>\n      <div className=\"measurement__value\">\n        {props.label}: <input className=\"lineMeasurementInput\" type=\"number\" min=\"0\" value={computeRadius()} onChange={(event) => props.onChange(event, props.annotation)}/> {unit}\n      </div>\n    </>\n  );\n}\n\nCustomEllipseMeasurementOverlay.propTypes = CustomMeasurementOverlay.propTypes;\n\nexport default withTranslation()(CustomMeasurementOverlay);", "import React from 'react';\nimport PropTypes from 'prop-types';\nimport { useTranslation } from 'react-i18next';\nimport Icon from 'components/Icon';\nimport core from 'core';\nimport { mapAnnotationToKey, getDataWithKey } from '../../constants/map';\n\nCountMeasurementOverlay.propTypes = {\n  annotation: PropTypes.object.isRequired\n};\n\nfunction CountMeasurementOverlay(props) {\n  const annotationKey = mapAnnotationToKey(props.annotation);\n  const { icon } = getDataWithKey(annotationKey);\n  const { t } = useTranslation();\n  const annotationList = core.getAnnotationsList();\n\n  const measurementAnnotationsList = annotationList.filter((annotation) => {\n    return annotation.getCustomData('trn-is-count');\n  });\n  const annotationCount = measurementAnnotationsList.length;\n\n  return (\n    <>\n      <div className=\"measurement__title\">\n        {icon && <Icon className=\"measurement__icon\" glyph={icon} />}\n        {t('option.measurementOverlay.countMeasurement')}\n      </div>\n      <div className=\"measurement__count\">\n        {t('option.measurementOverlay.count')}: {annotationCount}\n      </div>\n    </>\n  );\n}\n\nexport default CountMeasurementOverlay;", "import React, { useState, useRef, useEffect } from 'react';\nimport classNames from 'classnames';\nimport Draggable from 'react-draggable';\nimport PropTypes from 'prop-types';\nimport core from 'core';\nimport { mapAnnotationToKey } from 'constants/map';\nimport actions from 'actions';\nimport selectors from 'selectors';\nimport { useSelector, useDispatch } from 'react-redux';\nimport CustomMeasurementOverlay from './CustomMeasurementOverlay';\nimport CountMeasurementOverlay from './CountMeasurementOverlay';\nimport DataElements from 'constants/dataElement';\n\nimport './MeasurementOverlay.scss';\n\nconst isMouseInsideRect = (e, overlayElement) => {\n  if (overlayElement === null) {\n    return false;\n  }\n\n  const overlayRect = overlayElement.getBoundingClientRect();\n  let x; let y;\n  if (e.touches && e instanceof TouchEvent) {\n    x = e.touches[0].clientX;\n    y = e.touches[0].clientY;\n  } else {\n    x = e.clientX;\n    y = e.clientY;\n  }\n  return (\n    x >= overlayRect.left &&\n    x <= overlayRect.right &&\n    y >= overlayRect.top &&\n    y <= overlayRect.bottom\n  );\n};\n\nconst isCountMeasurementAnnotation = (annot) => ['countMeasurement'].includes(mapAnnotationToKey(annot));\n\n// create your forceUpdate hook\nfunction useForceUpdate() {\n  const [, setValue] = useState(0); // integer state\n  return () => setValue((value) => value + 1); // update state to force render\n}\n\nconst propTypes = {\n  annotation: PropTypes.object,\n};\n\nconst MeasurementOverlay = (props) => {\n  const { annotation } = props;\n  const isDisabled = useSelector((state) => selectors.isElementDisabled(state, DataElements.MEASUREMENT_OVERLAY));\n\n  const forceUpdate = useForceUpdate();\n  const dispatch = useDispatch();\n  const [position, setPosition] = useState({ x: 0, y: 0 });\n  const [transparentBackground, setTransparentBackground] = useState(false);\n  const [isCreatingAnnotation, setIsCreatingAnnotation] = useState(false);\n  const isOpen = useSelector((state) => selectors.isElementOpen(state, DataElements.MEASUREMENT_OVERLAY));\n  const activeToolName = useSelector((state) => selectors.getActiveToolName(state));\n  const customMeasurementOverlay = useSelector((state) => selectors.getCustomMeasurementOverlay(state));\n  const overlayRef = useRef();\n  const key = mapAnnotationToKey(annotation);\n\n  const shouldShowCustomOverlay = (annot) => (!isCountMeasurementAnnotation(annot) && customMeasurementOverlay.some((overlay) => overlay.validate(annot)));\n\n  const renderOverlay = (annot, key) => {\n    if (shouldShowCustomOverlay(annot)) {\n      return (\n        <CustomMeasurementOverlay\n          annotation={annot}\n          {...customMeasurementOverlay.filter((customOverlay) => customOverlay.validate(annot)\n          )[0]}\n        />\n      );\n    }\n    if (key === 'countMeasurement') {\n      return <CountMeasurementOverlay annotation={annot} />;\n    }\n  };\n\n  const onMouseMove = (e) => {\n    const tool = core.getTool(activeToolName);\n\n    if (annotation) {\n      const insideRect = isMouseInsideRect(e, overlayRef.current);\n      let useTransparentBackground;\n\n      if (isCreatingAnnotation) {\n        const drawMode = core.getToolMode().getDrawMode?.();\n        useTransparentBackground = insideRect && drawMode !== 'twoClicks';\n      } else {\n        const annotUnderMouse = core.getAnnotationByMouseEvent(e);\n        useTransparentBackground = insideRect && annotUnderMouse === annotation;\n      }\n      setTransparentBackground(useTransparentBackground);\n      forceUpdate();\n    } else if (shouldShowCustomOverlay(tool.annotation)) {\n      dispatch(actions.openElement(DataElements.MEASUREMENT_OVERLAY));\n      // this.setState({ annotation: tool.annotation });\n      // we know we are creating an annotation at this point because tool.annotation is truthy\n      setIsCreatingAnnotation(true);\n    }\n  };\n\n  const onAnnotationChanged = (annotations, action) => {\n    // measurement overlay will open and show the annotation information when we are creating an annotation using measurement tools\n    // since by default we don't auto select an annotation after it's created, we close the overlay here to avoid the confusion\n    // where no annotation is selected but measurement overlay shows the information about the annotation we were creating\n    if (\n      action === 'add' &&\n      annotations.length === 1 &&\n      annotations[0] === annotation\n    ) {\n      dispatch(actions.closeElement(DataElements.MEASUREMENT_OVERLAY));\n    }\n\n    if (\n      action === 'modify' &&\n      annotations.length === 1 &&\n      annotations[0] === annotation\n    ) {\n      forceUpdate();\n    }\n  };\n\n  useEffect(() => {\n    core.addEventListener('mouseMove', onMouseMove);\n    core.addEventListener('annotationChanged', onAnnotationChanged);\n    return () => {\n      core.removeEventListener('mouseMove', onMouseMove);\n      core.removeEventListener('annotationChanged', onAnnotationChanged);\n    };\n  }, []);\n\n  if (isDisabled || !annotation) {\n    return null;\n  }\n\n  return (\n    <Draggable\n      cancel=\"input\"\n      position={position}\n      onDrag={(e, { x, y }) => setPosition({ x, y })}\n      onStop={(e, { x, y }) => setPosition({ x, y })}\n    >\n      <div\n        className={classNames({\n          Overlay: true,\n          MeasurementOverlay: true,\n          open: isOpen,\n          closed: !isOpen,\n          transparent: transparentBackground,\n        })}\n        ref={overlayRef}\n        data-element={DataElements.MEASUREMENT_OVERLAY}\n      >\n        {renderOverlay(annotation, key)}\n      </div>\n    </Draggable>\n  );\n};\n\nMeasurementOverlay.propTypes = propTypes;\n\nexport default MeasurementOverlay;\n", "import MeasurementOverlay from './MeasurementOverlay';\n\nexport default MeasurementOverlay;"], "sourceRoot": ""}