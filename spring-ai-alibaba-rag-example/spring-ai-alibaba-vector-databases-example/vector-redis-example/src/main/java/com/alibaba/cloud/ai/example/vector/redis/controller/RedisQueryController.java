package com.alibaba.cloud.ai.example.vector.redis.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.document.Document;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.ai.vectorstore.redis.RedisVectorStore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/demo")
public class RedisQueryController {

    private static final Logger logger = LoggerFactory.getLogger(RedisQueryController.class);

    private final RedisVectorStore redisVectorStore;

    @Autowired
    public DemoQueryController(@Qualifier("redisVectorStoreCustom") RedisVectorStore redisVectorStore) {
        this.redisVectorStore = redisVectorStore;
    }

    /**
     * 1. 导入示例文档 (GET 请求，用于初始化数据)
     * 再次强调，Document 的 Map 就是其元数据，用于附加额外信息。
     * 这里的元数据非常重要，后续查询会用到。
     *
     * GET http://localhost:8080/api/demo/import
     */
    @GetMapping("/import")
    public String importData() {
        logger.info("========== 开始导入示例文档 ==========");

        // 定义一些用于元数据的HashMap
        // Map 1: 侧重技术、特定年份
        HashMap<String, Object> tech2024meta = new HashMap<>();
        tech2024meta.put("topic", "Technology");
        tech2024meta.put("year", 2024); // Integer 类型
        tech2024meta.put("author", "AI Team");
        tech2024meta.put("featured", true); // Boolean 类型

        // Map 2: 侧重编程、稍早年份
        HashMap<String, Object> programming2022meta = new HashMap<>();
        programming2022meta.put("topic", "Programming");
        programming2022meta.put("year", 2022);
        programming2022meta.put("author", "Dev Guru");
        programming2022meta.put("language", "Java"); // String 类型

        // Map 3: 侧重金融，包含地域信息
        HashMap<String, Object> financeMeta = new HashMap<>();
        financeMeta.put("topic", "Finance");
        financeMeta.put("year", 2023);
        financeMeta.put("region", "Global");
        financeMeta.put("sentiment", "Neutral");

        // Map 4: 另一个关于Spring AI的，不同年份和作者
        HashMap<String, Object> tech2023meta = new HashMap<>();
        tech2023meta.put("topic", "Technology");
        tech2023meta.put("year", 2023);
        tech2023meta.put("author", "Spring Expert");
        tech2023meta.put("version", "0.8"); // 可以是字符串版本号

        // Map 5: redis相关
        HashMap<String, Object> redisMeta = new HashMap<>();
        redisMeta.put("topic", "Database");
        redisMeta.put("type", "NoSQL");
        redisMeta.put("author", "Redis Labs");
        redisMeta.put("year", 2024);


        List<Document> documents = List.of(
                // 文档 1: 关于 Spring AI 的最新发展 (带有丰富的元数据)
                new Document("Spring AI reaches new milestones with enhanced capabilities for RAG and agentic workflows.", tech2024meta),

                // 文档 2: Java Spring Boot 最佳实践 (带有编程相关元数据)
                new Document("Dive deep into Spring Boot for building robust and scalable microservices.", programming2022meta),

                // 文档 3: 全球经济展望与金融市场投资策略 (带有金融元数据)
                new Document("Analyzing the global economic trends and their impact on investment opportunities.", financeMeta),

                // 文档 4: Spring AI 在实际项目中的应用 (不同年份的 Spring AI 文档)
                new Document("Practical applications of Spring AI in enterprise solutions.", tech2023meta),

                // 文档 5: Redis 的高级特性和性能优化
                new Document("Explore advanced features of Redis for high-performance data caching and real-time analytics.", redisMeta)
        );

        redisVectorStore.add(documents);
        logger.info("=========== 示例文档导入完成，共 {} 篇。===========", documents.size());
        return "示例文档导入成功！请尝试各种查询方法。";
    }

    /**
     * 2. 纯语义查询（最基础的查询用法）：
     * 查找与 "Spring AI" 语义最相似的 Top 2 文档。
     * 元数据在这里不会被用作过滤条件，但会在返回结果中显示。
     *
     * GET http://localhost:8080/api/demo/search-semantically
     */
    @GetMapping("/search-semantically")
    public List<Document> searchSemantically() {
        logger.info("=========== 执行纯语义查询：查找与 'Spring AI' 最相关的Top 2 文档 ===========");
        return redisVectorStore.similaritySearch(SearchRequest
                .builder()
                .query("Spring AI")
                .topK(2)
                .build());
    }

    /**
     * 3. 语义查询 + 简单元数据过滤（equal 表达式）：
     * 查找与 "Spring AI" 语义相似，且 topic 为 "Technology" 的 Top 2 文档。
     * 演示如何使用 `metadata.propertyName == 'value'` 进行精确匹配。
     *
     * GET http://localhost:8080/api/demo/search-by-topic
     */
    @GetMapping("/search-by-topic")
    public List<Document> searchByTopic() {
        logger.info("=========== 执行语义查询 + 元数据过滤：查找 'Technology' 相关的 Top 2 AI 文档 ===========");
        return redisVectorStore.similaritySearch(SearchRequest
                .builder()
                .query("AI technology advancements") // 语义查询
                .withFilter(SearchRequest.Filter.builder()
                        .withExpression("metadata.topic == 'Technology'") // 元数据过滤: topic 必须是 'Technology'
                        .build()
                )
                .withTopK(2)
                .build());
    }

    /**
     * 4. 语义查询 + 多个 AND 元数据过滤：
     * 查找与 "编程" 语义相似，且 topic 为 "Programming" 并且 year 为 2022 的 Top 2 文档。
     * 演示多个过滤条件通过 AND 关联。
     *
     * GET http://localhost:8080/api/demo/search-by-programming-and-year
     */
    @GetMapping("/search-by-programming-and-year")
    public List<Document> searchByProgrammingAndYear() {
        logger.info("=========== 执行语义查询 + 多个 AND 元数据过滤：查找 'Programming' 且 '2022年' 的 Top 2 文档 ===========");
        return redisVectorStore.similaritySearch(SearchRequest
                .builder()
                .query("Software development best practices") // 语义查询
                .withFilter(SearchRequest.Filter.builder()
                        .withExpression("metadata.topic == 'Programming' && metadata.year == 2022") // 多个 AND 条件
                        .build()
                )
                .withTopK(2)
                .build());
    }

    /**
     * 5. 语义查询 + 元数据过滤（OR 表达式）：
     * 查找与 "数据库" 相关的，且 topic 是 "Database" **或者** year 是 "2023" 的 Top 2 文档。
     * 演示 `||` (OR) 运算符的使用。
     *
     * GET http://localhost:8080/api/demo/search-by-database-or-year
     */
    @GetMapping("/search-by-database-or-year")
    public List<Document> searchByDatabaseOrYear() {
        logger.info("=========== 执行语义查询 + OR 元数据过滤：查找 'Database' 或 '2023年' 的 Top 2 文档 ===========");
        return redisVectorStore.similaritySearch(SearchRequest
                .builder()
                .query("Efficient data storage solutions") // 语义查询
                .withFilter(SearchRequest.Filter.builder()
                        .withExpression("metadata.topic == 'Database' || metadata.year == 2023") // OR 条件
                        .build()
                )
                .withTopK(2)
                .build());
    }

    /**
     * 6. 语义查询 + 数值范围过滤：
     * 查找与 "AI" 相关的，且 year 大于等于 2023 的 Top 2 文档。
     * 演示 `>` 和 `>=` 等数值比较运算符。
     *
     * GET http://localhost:8080/api/demo/search-by-year-range
     */
    @GetMapping("/search-by-year-range")
    public List<Document> searchByYearRange() {
        logger.info("=========== 执行语义查询 + 数值范围过滤：查找 'AI' 相关且 >= 2023 年份的 Top 2 文档 ===========");
        return redisVectorStore.similaritySearch(SearchRequest
                .builder()
                .query("New developments in artificial intelligence")
                .withFilter(SearchRequest.Filter.builder()
                        .withExpression("metadata.year >= 2023") // 数值范围过滤
                        .build()
                )
                .withTopK(2)
                .build());
    }

    /**
     * 7. 语义查询 + 布尔值过滤：
     * 查找与 "Spring AI" 相关的，且 `featured` 为 `true` 的 Top 1 文档。
     * 演示布尔元数据的过滤。
     *
     * GET http://localhost:8080/api/demo/search-by-featured
     */
    @GetMapping("/search-by-featured")
    public List<Document> searchByFeatured() {
        logger.info("=========== 执行语义查询 + 布尔值过滤：查找 'Spring AI' 且 '特色推荐' 的 Top 1 文档 ===========");
        return redisVectorStore.similaritySearch(SearchRequest
                .builder()
                .query("Future of generative AI with Spring")
                .withFilter(SearchRequest.Filter.builder()
                        .withExpression("metadata.featured == true") // 布尔值过滤
                        .build()
                )
                .withTopK(1)
                .build());
    }

    /**
     * 8. 结合 Filter 和 SimilarityThreshold:
     * 查找与 "Redis" 相关的，topic 为 "Database" 且相似度必须高于 0.8 的文档。
     * `withSimilarityThreshold` 用于设置相似度阈值，只有高于该阈值的文档才会返回。
     *
     * GET http://localhost:8080/api/demo/search-with-threshold
     */
    @GetMapping("/search-with-threshold")
    public List<Document> searchWithThreshold() {
        logger.info("=========== 执行语义查询 + 元数据过滤 + 相似度阈值：查找 'Redis' 相关且 `topic` 为 'Database'，相似度高于 0.8 的文档 ===========");
        return redisVectorStore.similaritySearch(SearchRequest
                .builder()
                .query("Database solutions for caching")
                .withFilter(SearchRequest.Filter.builder()
                        .withExpression("metadata.topic == 'Database'")
                        .build()
                )
                .withSimilarityThreshold(0.8) // 设置相似度阈值
                .withTopK(3)
                .build());
    }

    /**
     * 9. 删除文档：
     * 删除指定 ID 的文档。在导入文档时，Spring AI 通常会为没有指定 ID 的文档生成一个 ID。
     * 你可以通过之前的搜索结果查看文档的 ID。
     * 例如，如果你想删除 Document 2 （Spring Boot 相关），它的 id 可能会是 "spring-boot-is-excellent..." 这样的哈希值。
     * 如果在导入时我们给它设置了 id，那就可以用我们设置的 id。
     *
     * 注意：由于 importData 中并没有显式给所有 Document 设置一个固定的 ID，
     *   这里需要你从前几次查询的返回结果中复制一个想要删除的 Document ID。
     *   为了演示，我假设你知道某个文档的 ID。
     *
     * POST http://localhost:8080/api/demo/delete-document
     * Body:
     * {
     *   "documentId": "paste-your-document-id-here"
     * }
     */
    @PostMapping("/delete-document")
    public String deleteDocument(@RequestBody Map<String, String> payload) {
        String documentId = payload.get("documentId");
        if (documentId == null || documentId.isEmpty()) {
            return "请提供要删除的 documentId 参数。";
        }
        logger.info("=========== 尝试删除文档，ID: {} ===========", documentId);
        redisVectorStore.delete(List.of(documentId));
        logger.info("=========== 文档删除操作完成 ===========");
        return "文档删除操作已尝试，ID: " + documentId + "。请重新查询确认。";
    }

    /**
     * POST 请求体用于删除文档
     */
    public record DeleteRequest(String documentId) {}
}
