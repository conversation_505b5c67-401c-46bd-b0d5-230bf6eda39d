package com.alibaba.cloud.ai.example.vector.redis.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.document.Document;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.filter.Filter;
import org.springframework.ai.vectorstore.filter.FilterExpressionBuilder;
import org.springframework.ai.vectorstore.redis.RedisVectorStore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * Redis向量数据库控制器 - 全面演示Spring AI向量数据库API和Document元数据的使用
 *
 * 本控制器包含以下学习目标的完整示例：
 * 1. Spring AI向量数据库API覆盖：基础相似性搜索、过滤搜索、阈值搜索、Top-K搜索等
 * 2. Document元数据理解：元数据的用途、优势以及在过滤和检索中的实际应用
 */
@RestController
@RequestMapping("/redis")
public class RedisController {

    private static final Logger logger = LoggerFactory.getLogger(RedisController.class);
    private final RedisVectorStore redisVectorStore;

    @Autowired
    public RedisController(@Qualifier("redisVectorStoreCustom") RedisVectorStore redisVectorStore) {
        this.redisVectorStore = redisVectorStore;
    }

    /**
     * 【核心方法】导入示例数据 - 演示Document元数据的重要性和用途
     *
     * Document元数据的作用和优势：
     * 1. 分类标识：通过topic、category等字段对文档进行分类
     * 2. 时间维度：通过year、date等字段进行时间范围过滤
     * 3. 来源追踪：通过author、source等字段追踪文档来源
     * 4. 业务属性：通过priority、status等字段标记业务状态
     * 5. 技术属性：通过language、version等字段标记技术特征
     *
     * 元数据在向量搜索中的实际价值：
     * - 精确过滤：在语义搜索基础上进行精确的属性过滤
     * - 业务隔离：不同业务线的数据可以通过元数据进行隔离
     * - 权限控制：可以基于元数据实现细粒度的访问控制
     * - 结果排序：可以结合元数据进行更智能的结果排序
     */
    @GetMapping("/import")
    public String importData() {
        logger.info("========== 开始导入丰富元数据的示例文档 ==========");

        // 技术文档元数据 - 演示多种数据类型的元数据
        Map<String, Object> techMetadata = new HashMap<>();
        techMetadata.put("topic", "Technology");           // 字符串类型：主题分类
        techMetadata.put("year", 2024);                   // 数值类型：年份
        techMetadata.put("author", "Spring Team");        // 字符串类型：作者
        techMetadata.put("priority", 5);                  // 数值类型：优先级(1-5)
        techMetadata.put("featured", true);               // 布尔类型：是否推荐
        techMetadata.put("tags", Arrays.asList("AI", "Spring", "Framework")); // 列表类型：标签
        techMetadata.put("language", "Java");             // 字符串类型：编程语言
        techMetadata.put("difficulty", "Advanced");       // 字符串类型：难度级别

        // 业务文档元数据 - 演示业务场景的元数据设计
        Map<String, Object> businessMetadata = new HashMap<>();
        businessMetadata.put("topic", "Business");
        businessMetadata.put("year", 2023);
        businessMetadata.put("author", "Business Analyst");
        businessMetadata.put("department", "Sales");      // 部门信息
        businessMetadata.put("region", "Asia");          // 地区信息
        businessMetadata.put("confidential", false);     // 是否机密
        businessMetadata.put("revenue_impact", 1000000); // 收入影响（数值）
        businessMetadata.put("status", "Active");        // 状态

        // 学术文档元数据 - 演示学术场景的元数据
        Map<String, Object> academicMetadata = new HashMap<>();
        academicMetadata.put("topic", "Research");
        academicMetadata.put("year", 2024);
        academicMetadata.put("author", "Dr. AI Researcher");
        academicMetadata.put("journal", "AI Quarterly");
        academicMetadata.put("citation_count", 150);     // 引用次数
        academicMetadata.put("peer_reviewed", true);     // 是否同行评议
        academicMetadata.put("open_access", true);       // 是否开放获取
        academicMetadata.put("field", "Machine Learning");

        // 产品文档元数据 - 演示产品管理场景
        Map<String, Object> productMetadata = new HashMap<>();
        productMetadata.put("topic", "Product");
        productMetadata.put("year", 2024);
        productMetadata.put("author", "Product Manager");
        productMetadata.put("product_line", "AI Platform");
        productMetadata.put("version", "2.1.0");
        productMetadata.put("release_stage", "GA");      // General Availability
        productMetadata.put("customer_facing", true);
        productMetadata.put("support_level", "Enterprise");

        List<Document> documents = List.of(
                // 技术文档：Spring AI的最新发展
                new Document("Spring AI 2024年重大更新：引入了革命性的RAG（检索增强生成）功能，" +
                           "支持多种向量数据库，包括Redis、Milvus、PostgreSQL等。新版本大幅提升了" +
                           "AI应用的开发效率，为企业级AI解决方案提供了强大的基础框架。", techMetadata),

                // 业务文档：市场分析报告
                new Document("2023年亚洲AI市场分析报告显示，企业对AI解决方案的需求持续增长，" +
                           "预计未来三年市场规模将达到500亿美元。销售部门建议加大在AI平台产品的投入，" +
                           "以抢占市场先机。", businessMetadata),

                // 学术文档：机器学习研究
                new Document("最新的机器学习研究表明，向量数据库在大规模语义搜索中的性能优势显著。" +
                           "通过对比实验发现，Redis向量存储在处理百万级文档时，查询响应时间可控制在" +
                           "100毫秒以内，为实时AI应用提供了可靠保障。", academicMetadata),

                // 产品文档：AI平台功能介绍
                new Document("AI平台2.1.0版本正式发布，新增智能文档检索、多模态内容理解、" +
                           "自动化工作流等企业级功能。该版本已通过全面测试，可为企业客户提供" +
                           "7x24小时技术支持服务。", productMetadata),

                // 简单文档（无元数据）- 用于对比演示
                new Document("这是一个没有元数据的简单文档，用于演示元数据的重要性。" +
                           "在实际应用中，缺少元数据的文档很难进行精确的分类和过滤。")
        );

        redisVectorStore.add(documents);
        logger.info("========== 文档导入完成，共导入 {} 个文档 ==========", documents.size());

        return String.format("成功导入 %d 个带有丰富元数据的示例文档！现在可以测试各种查询方法了。", documents.size());
    }

    // ==================== 基础查询方法 ====================

    /**
     * 1. 基础语义相似性搜索 - 最简单的向量搜索
     *
     * 功能说明：
     * - 基于文档内容的语义相似性进行搜索
     * - 不使用任何元数据过滤条件
     * - 返回与查询最相似的Top-K个文档
     *
     * 实际应用场景：
     * - 通用文档检索
     * - 内容推荐系统
     * - 知识库搜索
     */
    @GetMapping("/search-basic")
    public List<Document> searchBasic() {
        logger.info("========== 执行基础语义搜索：查找与'Spring AI'最相关的文档 ==========");

        List<Document> results = redisVectorStore.similaritySearch(SearchRequest
                .builder()
                .query("Spring AI artificial intelligence framework")
                .topK(3)  // 返回最相似的3个文档
                .build());

        logger.info("找到 {} 个相关文档", results.size());
        logSearchResults("基础语义搜索", results);
        return results;
    }

    /**
     * 2. 带相似度阈值的搜索 - 控制结果质量
     *
     * 功能说明：
     * - 设置相似度阈值，只返回相似度高于阈值的文档
     * - 有效过滤低质量的搜索结果
     * - 阈值范围通常在0.0-1.0之间，值越高要求越严格
     *
     * 实际应用场景：
     * - 高精度文档检索
     * - 质量控制要求严格的应用
     * - 避免返回不相关的结果
     */
    @GetMapping("/search-with-threshold")
    public List<Document> searchWithThreshold() {
        logger.info("========== 执行带阈值的语义搜索：相似度必须高于0.7 ==========");

        List<Document> results = redisVectorStore.similaritySearch(SearchRequest
                .builder()
                .query("machine learning research artificial intelligence")
                .topK(5)
                .similarityThreshold(0.7)  // 设置相似度阈值为0.7
                .build());

        logger.info("找到 {} 个高质量相关文档（相似度>0.7）", results.size());
        logSearchResults("带阈值搜索", results);
        return results;
    }

    // ==================== 元数据过滤查询方法 ====================

    /**
     * 3. 基于主题的元数据过滤搜索
     *
     * 功能说明：
     * - 结合语义搜索和元数据过滤
     * - 只返回特定主题的文档
     * - 演示字符串类型元数据的精确匹配
     *
     * 元数据的优势：
     * - 精确分类：可以准确区分不同类型的文档
     * - 业务隔离：不同业务线的内容不会相互干扰
     * - 提高精度：在语义搜索基础上进一步缩小范围
     */
    @GetMapping("/search-by-topic")
    public List<Document> searchByTopic(@RequestParam(defaultValue = "Technology") String topic) {
        logger.info("========== 执行主题过滤搜索：查找'{}'主题的AI相关文档 ==========", topic);

        List<Document> results = redisVectorStore.similaritySearch(SearchRequest
                .builder()
                .query("AI technology development framework")
                .topK(3)
                .filterExpression("topic == '" + topic + "'")  // 元数据过滤：只查找指定主题
                .build());

        logger.info("在'{}'主题下找到 {} 个相关文档", topic, results.size());
        logSearchResults("主题过滤搜索", results);
        return results;
    }

    /**
     * 4. 基于年份范围的数值过滤搜索
     *
     * 功能说明：
     * - 演示数值类型元数据的范围查询
     * - 支持 >、>=、<、<=、== 等比较操作符
     * - 适用于时间范围、数量范围等场景
     *
     * 实际应用场景：
     * - 查找最新的文档（年份>=2024）
     * - 历史数据分析（特定年份范围）
     * - 基于数值属性的筛选（价格、评分等）
     */
    @GetMapping("/search-by-year-range")
    public List<Document> searchByYearRange(@RequestParam(defaultValue = "2024") int minYear) {
        logger.info("========== 执行年份范围搜索：查找{}年及以后的文档 ==========", minYear);

        List<Document> results = redisVectorStore.similaritySearch(SearchRequest
                .builder()
                .query("latest developments updates new features")
                .topK(4)
                .filterExpression("year >= " + minYear)  // 数值范围过滤
                .build());

        logger.info("找到 {} 个{}年及以后的文档", results.size(), minYear);
        logSearchResults("年份范围搜索", results);
        return results;
    }

    /**
     * 5. 复合条件过滤搜索（AND逻辑）
     *
     * 功能说明：
     * - 使用AND逻辑组合多个过滤条件
     * - 演示多种数据类型的组合过滤
     * - 实现精确的多维度筛选
     *
     * 实际应用场景：
     * - 精确的业务查询（特定部门+特定年份+特定状态）
     * - 权限控制（用户角色+访问级别+内容类型）
     * - 复杂的数据分析需求
     */
    @GetMapping("/search-with-multiple-filters")
    public List<Document> searchWithMultipleFilters() {
        logger.info("========== 执行复合条件搜索：Technology主题 AND 2024年 AND 推荐文档 ==========");

        List<Document> results = redisVectorStore.similaritySearch(SearchRequest
                .builder()
                .query("Spring framework AI technology")
                .topK(3)
                // 多个AND条件：主题为Technology，年份为2024，且为推荐文档
                .filterExpression("topic == 'Technology' && year == 2024 && featured == true")
                .build());

        logger.info("找到 {} 个符合所有条件的文档", results.size());
        logSearchResults("复合条件搜索", results);
        return results;
    }

    /**
     * 6. OR逻辑过滤搜索
     *
     * 功能说明：
     * - 使用OR逻辑组合多个过滤条件
     * - 满足任一条件的文档都会被返回
     * - 扩大搜索范围，增加结果多样性
     *
     * 实际应用场景：
     * - 多类别内容检索（技术文档或产品文档）
     * - 多时间段数据查询（2023年或2024年）
     * - 灵活的业务查询需求
     */
    @GetMapping("/search-with-or-filters")
    public List<Document> searchWithOrFilters() {
        logger.info("========== 执行OR条件搜索：Technology主题 OR Product主题 ==========");

        List<Document> results = redisVectorStore.similaritySearch(SearchRequest
                .builder()
                .query("platform development framework")
                .topK(4)
                // OR条件：主题为Technology或Product
                .filterExpression("topic == 'Technology' || topic == 'Product'")
                .build());

        logger.info("找到 {} 个Technology或Product主题的文档", results.size());
        logSearchResults("OR条件搜索", results);
        return results;
    }

    /**
     * 7. 布尔类型元数据过滤
     *
     * 功能说明：
     * - 演示布尔类型元数据的过滤
     * - 适用于开关状态、标记属性等场景
     * - 简化业务逻辑的表达
     *
     * 实际应用场景：
     * - 查找推荐内容（featured == true）
     * - 过滤机密文档（confidential == false）
     * - 筛选已发布内容（published == true）
     */
    @GetMapping("/search-featured-content")
    public List<Document> searchFeaturedContent() {
        logger.info("========== 执行推荐内容搜索：查找所有推荐文档 ==========");

        List<Document> results = redisVectorStore.similaritySearch(SearchRequest
                .builder()
                .query("important featured content recommendations")
                .topK(3)
                .filterExpression("featured == true")  // 布尔值过滤：只查找推荐内容
                .build());

        logger.info("找到 {} 个推荐文档", results.size());
        logSearchResults("推荐内容搜索", results);
        return results;
    }

    /**
     * 8. 基于作者的字符串过滤
     *
     * 功能说明：
     * - 演示基于作者信息的精确过滤
     * - 展示字符串类型元数据的实际应用
     * - 支持内容来源的追踪和管理
     *
     * 实际应用场景：
     * - 查找特定作者的文档
     * - 内容来源管理
     * - 专家知识检索
     */
    @GetMapping("/search-by-author")
    public List<Document> searchByAuthor(@RequestParam(defaultValue = "Spring Team") String author) {
        logger.info("========== 执行作者过滤搜索：查找'{}'的文档 ==========", author);

        List<Document> results = redisVectorStore.similaritySearch(SearchRequest
                .builder()
                .query("development framework technology")
                .topK(3)
                .filterExpression("author == '" + author + "'")  // 作者过滤
                .build());

        logger.info("找到 {} 个'{}'创建的文档", results.size(), author);
        logSearchResults("作者过滤搜索", results);
        return results;
    }

    // ==================== 高级查询方法 ====================

    /**
     * 9. 复杂的混合条件搜索
     *
     * 功能说明：
     * - 结合AND、OR、括号等复杂逻辑
     * - 演示高级过滤表达式的构建
     * - 满足复杂业务场景的查询需求
     *
     * 查询逻辑：(Technology OR Research) AND year >= 2024 AND featured == true
     */
    @GetMapping("/search-complex-conditions")
    public List<Document> searchComplexConditions() {
        logger.info("========== 执行复杂条件搜索：(Technology OR Research) AND year>=2024 AND featured=true ==========");

        List<Document> results = redisVectorStore.similaritySearch(SearchRequest
                .builder()
                .query("advanced AI research technology")
                .topK(5)
                // 复杂条件：(主题为Technology或Research) 且 年份>=2024 且 为推荐内容
                .filterExpression("(topic == 'Technology' || topic == 'Research') && year >= 2024 && featured == true")
                .build());

        logger.info("找到 {} 个符合复杂条件的文档", results.size());
        logSearchResults("复杂条件搜索", results);
        return results;
    }

    /**
     * 10. 动态Top-K搜索
     *
     * 功能说明：
     * - 支持动态调整返回结果数量
     * - 演示Top-K参数的灵活使用
     * - 适应不同场景的结果数量需求
     *
     * 实际应用场景：
     * - 分页查询
     * - 根据用户偏好调整结果数量
     * - 性能优化（减少不必要的结果）
     */
    @GetMapping("/search-dynamic-topk")
    public List<Document> searchDynamicTopK(@RequestParam(defaultValue = "3") int topK,
                                           @RequestParam(defaultValue = "0.5") double threshold) {
        logger.info("========== 执行动态Top-K搜索：topK={}, threshold={} ==========", topK, threshold);

        List<Document> results = redisVectorStore.similaritySearch(SearchRequest
                .builder()
                .query("business analysis market research")
                .topK(topK)  // 动态Top-K
                .similarityThreshold(threshold)  // 动态阈值
                .build());

        logger.info("找到 {} 个文档（topK={}, threshold={}）", results.size(), topK, threshold);
        logSearchResults("动态Top-K搜索", results);
        return results;
    }
